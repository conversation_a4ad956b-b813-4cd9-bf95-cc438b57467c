# 06 - <PERSON><PERSON><PERSON><PERSON>ste módulo organiza la programación de servicios en planta, gestiona proyectos autorizados, recepciones de equipos y proporciona reportes de desempeño. Está diseñado para dar visibilidad y trazabilidad a todas las etapas logísticas del servicio.

---

## 1. <PERSON><PERSON> de Servicios (Pestaña Principal)

**Funcionalidad**:
- Visualización diaria de servicios programados con detalle por técnico y cliente
- Acceso directo a hoja de servicio (formato COM-FT-SP)

**Campos**:
- Datos del Cliente: Dirección, contacto, teléfono, correo
- Datos del Servicio: Atención, fechas, domicilio del servicio
- Datos del Personal: Técnico responsable, recepción de papelería
- Datos del Equipo: Folio, instrumento, marca, modelo, serie, ubicación, vigencia, observaciones

**Acciones**:
- <PERSON><PERSON><PERSON>, editar servic<PERSON> (Logística)
- Visualizar (todas las áreas)

---

## 2. Proyectos (Subpestaña)

**Funcionalidad**:
- Visualizar historial de proyectos subidos por Ventas y autorizados por Administración

**Campos**:
- Estatus: En proceso, Finalizado, Pendiente de Almacén, Mantenimiento
- Descarga de cotizaciones y órdenes de compra
- Comentarios por estatus

**Acciones**:
- Modificar estatus, agregar comentarios, notificar cambios

**Accesos**: Logística

---

## 3. Historial de Ingresos (Subpestaña)

**Funcionalidad**:
- Consulta de ingresos de equipos para servicio (laboratorio, planta, etc.)

**Campos**:
- Proyecto: Cotización, OC
- Cliente: Nombre, domicilio, contacto, correo
- Equipo: Instrumento, folio, marca, modelo, serie, accesorios, observaciones
- Seguimiento: Entregó, Recibió, fechas de ingreso y finalización

**Acciones**:
- Crear, editar, consultar, exportar

**Accesos**:
- Crear/Editar: Logística
- Consultar: Metrología, Ventas

---

## 4. Estadísticos (Subpestaña)

**Funcionalidad**:
- Base de datos con análisis de cumplimiento de objetivos y tiempos

**Campos**:
- Empresa, equipo, fechas de cotización/OC, programación y servicio
- Personal asignado

**Acciones**:
- Consultar estadísticas, editar fechas cuando aplique
- Exportar reportes

**Alimentación de datos**:
- Automática desde acciones realizadas en otros módulos

**Accesos**: Logística

---

## 5. Solicitud de Viáticos y Paquetería (Submenús compartidos)

**Funcionalidades**:
- Generar solicitudes desde Logística
- Envío de notificación a Administración cuando se registra una nueva

**Acciones**:
- Llenar formulario de solicitud
- Adjuntar archivos si aplica

**Accesos**: Logística, Administración

Este módulo permite a COMINTEC coordinar servicios en planta con precisión, controlar el flujo de ingresos de equipos y obtener métricas logísticas confiables para toma de decisiones.

