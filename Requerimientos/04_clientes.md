# 04 - <PERSON><PERSON><PERSON><PERSON>ste módulo permite la gestión completa de clientes, tanto nuevos como existentes, en dos modalidades: ingreso completo y rápido. También proporciona filtros detallados, seguimiento y análisis de comportamiento comercial.

---

## 1. <PERSON><PERSON><PERSON><PERSON> (Modo <PERSON>to)

**Formulario**:
### Datos Generales:
- Razón_Social, Nombre_Comercial, RFC, Régimen_Fiscal

### Dirección Fiscal:
- Calle, Colonia, Ciudad, Estado, Código_Postal, País (default: México)

### Contacto Principal:
- Nombre_Contacto, Puesto, Teléfonos, Emails

### Datos Comerciales:
- Giro, Área_Empresa, Tamaño_Empresa, Vendedor_Asignado
- Tipo_Cliente (Prospecto, Cliente, Frecuente), Límite y Días de Crédito, Forma_Pago_Preferida

### Información Adicional:
- Página_Web, Observaciones, <PERSON><PERSON>_Regis<PERSON>, <PERSON><PERSON><PERSON>_Registro, Estado_Cliente

**Validaciones**:
- RFC válido SAT
- Emails válidos

---

## 2. <PERSON><PERSON><PERSON><PERSON> (Modo Rápido)

**Formulario**:
- Nombre_Comercial_O_RFC (requerido)
- Vendedor_Asignado (por sesión actual)
- Tipo_Registro: Rápido
- Estado: Temporal (requiere completar datos en 7 días)

**Automatización**:
- Validación y autocompletado de datos desde SAT si RFC válido
- Generación de ID temporal
- Notificación para completar información faltante

---

## 3. Mis Clientes - Vista con Filtros

**Filtros**:
- Ubicación (Estado/Ciudad), Nombre parcial
- Estado_Cliente (Activo, Inactivo, Suspendido, Temporal)
- Área_Empresa, Tipo_Cliente
- Límite_Crédito (rango)
- Última_Compra (rango de fechas)

**Columnas de Vista**:
- Nombre_Comercial, RFC, Ciudad/Estado
- Contacto_Principal, Teléfono, Email
- Última_Cotización, Última_Compra, Monto_Última_Compra
- Estado_Cuenta (Al día, Moroso)
- Acciones: Ver, Editar, Historial

**Extras**:
- Historial de compras frecuentes
- Productos más comprados
- Estacionalidad de compra
- Notas de seguimiento

---

**Accesos**:
- Ventas: edición y seguimiento
- Administración y Sistemas: consulta y validaciones
- Otros módulos: vista parcial cuando se relaciona un cliente

Este módulo permite un control ordenado y flexible de la cartera de clientes, adaptable a operaciones recurrentes o nuevos prospectos.

