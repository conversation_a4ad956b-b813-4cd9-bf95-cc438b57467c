# 10 - <PERSON><PERSON><PERSON><PERSON>ste módulo organiza la programación, ejecución y seguimiento de servicios prestados, asegurando coordinación efectiva entre áreas y trazabilidad de la atención brindada.

---

## 1. Calendario de Servicios

**Funcionalidad**:
- Visualización y gestión de servicios programados

**Formulario**:
- Fecha, Cliente, Tipo_Servicio, Técnico_Asignado, Hora, Dirección

**Acciones**:
- Logística: crear, editar
- Otras áreas: consulta

**Accesos**:
- Todas las áreas (solo edición: logística)

---

## 2. Hoja de Servicio en Planta

**Funcionalidad**:
- Registro detallado del servicio realizado

**Campos**:
- Datos cliente, datos servicio, personal asignado, datos del equipo

**Acciones**:
- Logística: crear, editar
- Otras áreas: consultar

---

## 3. Seguimiento de Proyectos

**Funcionalidad**:
- Visualización del estatus de los proyectos de servicio

**Interfaz**:
- Estatus: En proceso, Finalizado, Pendiente

**Acciones**:
- Logística: modificar estatus, agregar comentarios
- Otras áreas: consultar

Este módulo permite una programación clara, con trazabilidad completa desde la solicitud hasta la ejecución, mejorando la atención al cliente.
