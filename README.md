# 🏢 AppComintec 2.0

Sistema integral de gestión empresarial para COMINTEC - Una aplicación web moderna construida con Next.js 15, Express.js y Supabase.

## 🚀 Características Principales

- **🔐 Sistema de Autenticación Completo** - Login seguro con JWT y roles
- **👥 Gestión de Usuarios y Roles** - Sistema RBAC (Role-Based Access Control)
- **📊 Dashboard Interactivo** - Métricas en tiempo real y visualizaciones
- **🏪 Módulo de Almacén** - Control de inventario y stock
- **🛒 Módulo de Ventas** - Cotizaciones, clientes y CRM
- **📋 Gestión de Calidad** - Control de procesos y certificaciones
- **📈 Reportes y Estadísticas** - Análisis de datos empresariales
- **🔧 Módulo de Sistemas** - Administración y configuración
- **🌙 Modo Oscuro/Claro** - Interfaz adaptable
- **📱 Diseño Responsivo** - Compatible con dispositivos móviles

## 🛠️ Stack Tecnológico

### Frontend
- **Next.js 15** - Framework React con App Router
- **React 19** - Biblioteca de interfaz de usuario
- **TypeScript** - Tipado estático
- **Tailwind CSS 3.4** - Framework de estilos utilitarios
- **shadcn/ui** - Sistema de componentes moderno
- **Radix UI** - Componentes primitivos accesibles
  - `@radix-ui/react-dialog` - Modales y diálogos
  - `@radix-ui/react-dropdown-menu` - Menús desplegables
  - `@radix-ui/react-tabs` - Componentes de pestañas
  - `@radix-ui/react-select` - Selectores avanzados
  - `@radix-ui/react-checkbox` - Checkboxes personalizados
  - `@radix-ui/react-toast` - Notificaciones toast
- **React Hook Form** - Manejo eficiente de formularios
- **Zod** - Validación de esquemas TypeScript-first
- **Recharts** - Gráficos y visualizaciones de datos
- **Lucide React** - Iconografía moderna y consistente
- **Class Variance Authority** - Gestión de variantes de componentes
- **Tailwind Merge** - Fusión inteligente de clases CSS
- **Next Themes** - Soporte para modo oscuro/claro
- **Date-fns** - Manipulación de fechas
- **Sonner** - Sistema de notificaciones elegante

### Backend
- **Express.js** - Framework web para Node.js
- **TypeScript** - Tipado estático
- **Supabase** - Base de datos PostgreSQL
- **TypeORM** - ORM para TypeScript
- **JWT** - Autenticación con tokens
- **bcrypt** - Encriptación de contraseñas
- **Socket.io** - Comunicación en tiempo real
- **Helmet** - Seguridad HTTP
- **CORS** - Control de acceso entre dominios

### Testing & DevOps
- **Jest** - Testing unitario
- **Playwright** - Testing E2E
- **Storybook** - Desarrollo de componentes
- **Docker** - Containerización
- **GitHub Actions** - CI/CD

## 📋 Requisitos del Sistema

- **Node.js** 18.0.0 o superior
- **npm** 9.0.0 o superior
- **PostgreSQL** 14+ (via Supabase)
- **Git** para control de versiones

## ⚡ Instalación Rápida

1. **Clonar el repositorio:**
```bash
git clone https://github.com/gaboLectric/AppComintec2.0.git
cd AppComintec2.0
```

2. **Instalar dependencias:**
```bash
npm install
```

3. **Configurar variables de entorno:**
```bash
# Copiar archivos de ejemplo
cp .env.example .env
cp frontend/.env.local.example frontend/.env.local
cp backend/.env.example backend/.env
```

4. **Iniciar en modo desarrollo:**
```bash
npm run dev
```

La aplicación estará disponible en:
- **Frontend:** http://localhost:3001
- **Backend:** http://localhost:3000

## 🎨 Sistema de Diseño y Estilos

### Componentes UI Instalados

El proyecto utiliza **shadcn/ui** como sistema de componentes base, construido sobre **Radix UI** y **Tailwind CSS**:

```bash
# Componentes principales instalados
npx shadcn-ui@latest add button
npx shadcn-ui@latest add card
npx shadcn-ui@latest add dialog
npx shadcn-ui@latest add dropdown-menu
npx shadcn-ui@latest add form
npx shadcn-ui@latest add input
npx shadcn-ui@latest add label
npx shadcn-ui@latest add select
npx shadcn-ui@latest add tabs
npx shadcn-ui@latest add toast
npx shadcn-ui@latest add checkbox
npx shadcn-ui@latest add switch
npx shadcn-ui@latest add progress
npx shadcn-ui@latest add avatar
npx shadcn-ui@latest add badge
npx shadcn-ui@latest add calendar
npx shadcn-ui@latest add popover
npx shadcn-ui@latest add tooltip
```

### Dependencias de Estilos

```json
{
  "dependencies": {
    "tailwindcss": "^3.4.17",
    "autoprefixer": "^10.4.20",
    "postcss": "^8.5",
    "tailwindcss-animate": "^1.0.7",
    "tailwind-merge": "^2.5.5",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "next-themes": "latest"
  }
}
```

### Configuración de Tailwind

El proyecto incluye configuración personalizada para:
- **Colores del tema** - Paleta consistente para modo claro/oscuro
- **Animaciones** - Transiciones suaves y micro-interacciones
- **Tipografía** - Escalas de texto responsivas
- **Espaciado** - Sistema de espaciado coherente
- **Componentes** - Clases utilitarias personalizadas

### Iconografía

- **Lucide React** - +1000 iconos SVG optimizados
- **Tamaños consistentes** - 16px, 20px, 24px, 32px
- **Variantes** - Outline, filled, duotone

## 🔧 Configuración Detallada

### Variables de Entorno

#### Backend (.env)
```bash
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h

# Server Configuration
PORT=3000
NODE_ENV=development

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/comintec

# File Upload (Optional)
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-bucket-name
```

#### Frontend (.env.local)
```bash
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_WS_URL=ws://localhost:3000

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# App Configuration
NEXT_PUBLIC_APP_NAME=AppComintec 2.0
NEXT_PUBLIC_APP_VERSION=2.0.0
```

## 🧪 Testing

### Tests Unitarios
```bash
# Backend tests
npm run test:backend

# Frontend tests
npm run test:frontend

# Tests con cobertura
npm run test:cov
```

### Tests E2E con Playwright
```bash
# Instalar dependencias de Playwright
npx playwright install --with-deps

# Ejecutar tests E2E
npm run test:e2e

# Ejecutar con interfaz gráfica
npm run test:e2e:ui

# Ejecutar tests específicos
npm run test:systems
npm run test:crud
```

### Ejecutar todos los tests
```bash
npm test
```

## 👤 Usuarios de Prueba

> **Nota:** Todos los usuarios tienen la contraseña: `password`

### 🔑 Administradores
| Rol | Email | Descripción |
|-----|-------|-------------|
| Admin General | <EMAIL> | Acceso completo al sistema |
| Admin Manager | <EMAIL> | Gestión administrativa |
| Admin | <EMAIL> | Administrador principal |
| Sistemas | <EMAIL> | Gestión de sistemas |

### 📦 Almacén
| Rol | Email | Descripción |
|-----|-------|-------------|
| Manager de Almacén | <EMAIL> | Gestión completa de inventario |
| Usuario de Almacén | <EMAIL> | Operaciones de almacén |

### 💼 Ventas
| Rol | Email | Descripción |
|-----|-------|-------------|
| Manager de Ventas | <EMAIL> | Gestión completa de ventas |
| Usuario de Ventas | <EMAIL> | Cotizaciones y clientes |

### 🚚 Logística
| Rol | Email | Descripción |
|-----|-------|-------------|
| Manager de Logística | <EMAIL> | Gestión de envíos y distribución |
| Usuario de Logística | <EMAIL> | Operaciones logísticas |

### ✅ Calidad
| Rol | Email | Descripción |
|-----|-------|-------------|
| Manager de Calidad | <EMAIL> | Gestión de procesos de calidad |
| Usuario de Calidad | <EMAIL> | Inspecciones y certificaciones |

### 📏 Metrología
| Rol | Email | Descripción |
|-----|-------|-------------|
| Manager de Metrología | <EMAIL> | Gestión de calibraciones |
| Usuario de Metrología | <EMAIL> | Operaciones técnicas |

### 📊 Informes
| Rol | Email | Descripción |
|-----|-------|-------------|
| Manager de Informes | <EMAIL> | Gestión de reportes |
| Usuario de Informes | <EMAIL> | Consulta de informes |

### 👥 Recursos Humanos
| Rol | Email | Descripción |
|-----|-------|-------------|
| Manager de RH | <EMAIL> | Gestión de personal |
| Usuario de RH | <EMAIL> | Operaciones de RH |

### 🛒 Compras
| Rol | Email | Descripción |
|-----|-------|-------------|
| Manager de Compras | <EMAIL> | Gestión de proveedores |
| Usuario de Compras | <EMAIL> | Procesamiento de órdenes |

### 🗑️ Usuarios Especiales
| Rol | Email | Descripción |
|-----|-------|-------------|
| Delete Manager | <EMAIL> | Usuario para eliminaciones |

## 📁 Estructura del Proyecto

```
AppComintec2.0/
├── 🗂️ backend/                    # API REST con Express.js + TypeORM
│   ├── src/
│   │   ├── 🎮 controllers/        # Controladores de rutas API
│   │   ├── 🛡️ middlewares/        # Auth, CORS, error handling
│   │   ├── 🛣️ routes/             # Definición de endpoints
│   │   ├── 🔧 services/           # Lógica de negocio
│   │   ├── 📊 entities/           # Modelos de base de datos
│   │   ├── 🔍 dtos/               # Data Transfer Objects
│   │   └── 🧪 tests/              # Tests unitarios
│   ├── 📋 scripts/                # Scripts de BD y utilidades
│   └── 🏗️ migrations/             # Migraciones de base de datos
│
├── 🎨 frontend/                   # Frontend con Next.js 15
│   ├── 📄 app/                    # App Router (Next.js 15)
│   │   ├── 🏠 dashboard/          # Páginas del dashboard
│   │   ├── 🔐 login/              # Página de autenticación
│   │   └── 🌐 globals.css         # Estilos globales
│   ├── 🧩 components/             # Componentes React reutilizables
│   │   ├── 🎛️ ui/                 # Componentes base (shadcn/ui)
│   │   ├── 📊 dashboard/          # Componentes del dashboard
│   │   ├── 🔐 auth/               # Componentes de autenticación
│   │   ├── 📦 almacen/            # Módulo de almacén
│   │   ├── 💼 ventas/             # Módulo de ventas
│   │   ├── ✅ calidad/            # Módulo de calidad
│   │   └── ⚙️ sistemas/           # Módulo de sistemas
│   ├── 🪝 hooks/                  # Custom React hooks
│   ├── 📚 lib/                    # Utilidades y servicios
│   │   ├── 🔌 services/           # Servicios API
│   │   └── 🛠️ utils/              # Funciones utilitarias
│   └── 🎨 styles/                 # Archivos de estilos
│
├── 🧪 e2e/                        # Tests End-to-End (Playwright)
│   ├── 🔐 auth.spec.ts            # Tests de autenticación
│   ├── 📦 almacen.spec.ts         # Tests del módulo almacén
│   ├── 💼 ventas.spec.ts          # Tests del módulo ventas
│   └── ⚙️ sistemas.spec.ts        # Tests del módulo sistemas
│
├── 📜 scripts/                    # Scripts de automatización
│   ├── 🗄️ update-db-schema.sh    # Actualización de esquema BD
│   ├── 🧹 cleanup-*.sh           # Scripts de limpieza
│   └── 🔧 *.ts                   # Scripts TypeScript
│
├── 🐳 docker-compose.yml          # Configuración Docker
├── 🎭 playwright.config.ts        # Configuración Playwright
├── 📦 package.json                # Dependencias del proyecto
└── 📖 README.md                   # Documentación
```

## 🗄️ Configuración de Base de Datos

### Supabase Setup

1. **Crear proyecto en Supabase:**
   - Visita [Supabase](https://supabase.com)
   - Crea un nuevo proyecto
   - Copia las credenciales (URL, Anon Key, Service Role Key)

2. **Configurar variables de entorno:**
   ```bash
   # Actualizar .env con las credenciales de Supabase
   SUPABASE_URL=https://tu-proyecto.supabase.co
   SUPABASE_ANON_KEY=tu-anon-key
   SUPABASE_SERVICE_ROLE_KEY=tu-service-role-key
   ```

3. **Ejecutar migraciones:**
   ```bash
   # Aplicar esquema de base de datos
   npm run db:migrate

   # O manualmente con Supabase CLI
   npx supabase db push
   ```

4. **Cargar datos iniciales:**
   ```bash
   # Ejecutar seeds
   npm run db:seed

   # O cargar datos específicos
   npm run seed:permissions
   npm run seed:roles
   npm run seed:users
   ```

## 🚀 Scripts Disponibles

### Desarrollo
```bash
npm run dev              # Iniciar frontend y backend
npm run dev:frontend     # Solo frontend (puerto 3001)
npm run dev:backend      # Solo backend (puerto 3000)
npm run dev:full         # Modo completo con todos los servicios
```

### Build y Producción
```bash
npm run build            # Build completo
npm run build:frontend   # Build solo frontend
npm run build:backend    # Build solo backend
npm start               # Iniciar en producción
```

### Testing
```bash
npm test                # Todos los tests
npm run test:unit       # Tests unitarios
npm run test:e2e        # Tests E2E
npm run test:e2e:ui     # Tests E2E con interfaz
npm run test:systems    # Tests específicos de sistemas
```

### Utilidades
```bash
npm run lint            # Linting
npm run cleanup         # Limpiar archivos temporales
npm run storybook       # Iniciar Storybook
npm run extract:schema  # Extraer esquema de BD
```

## 🐳 Docker

### Desarrollo con Docker
```bash
# Construir y ejecutar
docker-compose up --build

# Solo base de datos
docker-compose up postgres

# Modo desarrollo
docker-compose -f docker-compose.dev.yml up
```

## 📚 Documentación Adicional

- [🔧 Configuración de Desarrollo](./docs/development.md)
- [🏗️ Arquitectura del Sistema](./docs/architecture.md)
- [🔐 Sistema de Autenticación](./docs/authentication.md)
- [📊 API Documentation](./docs/api.md)
- [🧪 Guía de Testing](./docs/testing.md)

## 🤝 Contribución

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📝 Changelog

Ver [CHANGELOG.md](./CHANGELOG.md) para los cambios detallados.

## 📄 Licencia

Este proyecto está bajo la Licencia ISC - ver el archivo [LICENSE](./LICENSE) para más detalles.

## 👨‍💻 Equipo de Desarrollo

- **Gabriel Isaí Alcaraz Suarez** - *Desarrollador Principal* - [@gaboLectric](https://github.com/gaboLectric)

## 🆘 Soporte

Si tienes problemas o preguntas:

1. Revisa la [documentación](./docs/)
2. Busca en [Issues existentes](https://github.com/gaboLectric/AppComintec2.0/issues)
3. Crea un [nuevo Issue](https://github.com/gaboLectric/AppComintec2.0/issues/new)

---

<div align="center">
  <strong>🏢 AppComintec 2.0</strong><br>
  Sistema de Gestión Empresarial Moderno<br>
  <em>Desarrollado con ❤️ para COMINTEC</em>
</div>
