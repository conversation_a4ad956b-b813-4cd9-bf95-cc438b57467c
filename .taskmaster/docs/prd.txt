<context>
# Documento de Requerimientos del Producto (PRD) - Sistema Web COMINTEC

## 1. Visión General y Objetivo Principal

- **Propósito**: Desarrollar una plataforma web integral para automatizar y digitalizar todos los procesos operativos de COMINTEC. El sistema busca centralizar la información, mejorar la eficiencia y asegurar la trazabilidad completa en las áreas de ventas, administración, almacén, logística, calidad, informes, metrología, recursos humanos y sistemas.
- **Alcance**: El proyecto abarca el diseño, desarrollo, testeo e implementación de una aplicación web modular, con acceso basado en roles, dashboards analíticos, notificaciones automáticas e integraciones con sistemas externos clave.

## 2. Stack Tecnológico

### Frontend:
- **Framework**: Next.js 15.2.4 con TypeScript
- **UI Library**: Radix UI components con Tailwind CSS
- **State Management**: TanStack React Query v5.81.2
- **Form Handling**: React Hook Form con Zod validation
- **Table Components**: TanStack React Table v8.21.3
- **Charts**: Recharts 2.15.0
- **Icons**: Lucide React
- **Notifications**: Sonner
- **Theme**: Next Themes con Tailwind CSS Animate

### Backend:
- **Runtime**: Node.js (>=14.0.0)
- **Framework**: Express.js 4.19.2 con TypeScript
- **Database**: PostgreSQL con TypeORM 0.3.25
- **Authentication**: JWT (jsonwebtoken 9.0.2)
- **Security**: 
  - bcrypt/bcryptjs para hashing de passwords
  - Helmet 8.1.0 para security headers
  - CORS 2.8.5
- **File Upload**: Multer 2.0.1
- **Validation**: class-validator 0.14.0 con class-transformer
- **Development**: ts-node-dev para hot reload

### Testing & Quality:
- **Testing Framework**: Jest 29.7.0 con ts-jest
- **API Testing**: Supertest 6.3.4
- **Linting**: ESLint
- **Build Verification**: `npm run build && npm test` debe pasar sin errores

## 3. Arquitectura de Usuarios y Roles

El sistema soportará tres niveles de usuarios con permisos jerárquicos:

| Tipo de Usuario | Cantidad Aprox. | Permisos Clave | Descripción |
|---|---|---|---|
| **Administrador** | 2 | Acceso total y sin restricciones. | Responsable de la configuración global del sistema, gestión de todos los usuarios, asignación de roles y permisos, y mantenimiento de la base de datos (backups/restauración). |
| **Responsables de Área** | 15 | Control total sobre su módulo específico. | Pueden crear, editar, aprobar y consultar toda la información y flujos de trabajo de su área. |
| **Usuarios Generales** | 30 | Acceso limitado a funcionalidades específicas. | Principalmente visualizan información y alimentan formularios relevantes para sus tareas diarias. |

---

## 4. Jerarquía y Prioridad de Módulos

Para un desarrollo estructurado, los módulos se implementarán siguiendo el siguiente orden de prioridad, basado en sus dependencias lógicas y criticidad para el negocio:

1.  **Sistemas**: **Prioridad Máxima**. Es la base del sistema. La gestión de usuarios, roles y permisos debe existir antes de que cualquier otro módulo pueda ser utilizado de forma segura.
2.  **Clientes (CRM)**: **Prioridad Alta**. La gestión de clientes por parte de los usuarios es fundamental y un prerrequisito para los módulos de Ventas, Administración y Logística.
3.  **Administración**: **Prioridad Alta**. Maneja los flujos financieros, contractuales y de aprobación que son centrales para la operación del negocio.
4.  **Ventas**: **Prioridad Alta**. Genera las cotizaciones y proyectos que alimentan a los demás módulos operativos. Depende directamente del módulo de Clientes.
5.  **Almacén**: **Prioridad Media-Alta**. El control de inventario es crucial para las ventas y la ejecución de servicios.
6.  **Logística**: **Prioridad Media-Alta**. Coordina la ejecución de servicios y proyectos generados por Ventas.
7.  **Informes de Calibración**: **Prioridad Media**. Representa uno de los servicios principales de la empresa. Depende de la logística y los clientes.
8.  **Metrología**: **Prioridad Media**. Gestiona al personal y los equipos técnicos, operando en conjunto con Logística e Informes.
9.  **Recursos Humanos (RH)**: **Prioridad Media-Baja**. Importante para la gestión del personal, pero puede desarrollarse en paralelo a la optimización de los módulos operativos.
10. **Calidad**: **Prioridad Media-Baja**. Supervisa y estandariza los procesos de todos los demás módulos. Su implementación es más efectiva una vez que los otros flujos están definidos.

---
</context>
<PRD>
## 5. Módulos del Sistema

### 5.1. Módulo de Administración

Gestiona solicitudes internas, finanzas, contratos y facturación.

- **Solicitudes de Viáticos, Tiempo Extra y Paquetería**: Formularios de solicitud con flujos de aprobación y notificaciones automáticas a Administración.
- **Asignación de Número Fiscal**: Generación de un folio fiscal único y secuencial (`COMINTEC-YYYY-####`) para documentos.
- **Gestión de Contratos REPSE**: Control de contratos con clientes, con alertas automáticas de vencimiento (30, 15, 7 días).
- **Gestión de Facturas**: Carga y validación de facturas (PDF/XML) contra datos del SAT.
- **Solicitudes de Crédito**: Flujo para solicitar, analizar y aprobar líneas de crédito para clientes.
- **Control de Morosos**: Identificación y seguimiento automático de facturas vencidas, con gestión de acciones de cobranza.
- **Dashboard Administrativo**: Gráficos y reportes sobre facturación, cartera vencida, ventas y rendimiento por vendedor.

### 5.2. Módulo de Almacén

Control de inventario en tiempo real con categorización dual.

#### 5.2.1. Stock Actual con Categorización
- **Almacén General**: Inventario permanente de la empresa
- **Almacén Rotativo**: Equipos que se prestan temporalmente
- **Selector desplegable**: Radio buttons o dropdown para alternar entre ambos tipos
- **Indicadores visuales**:
  - Verde: stock > 10
  - Amarillo: entre 1 y 10  
  - Rojo: sin stock
  - Azul: comprometido

#### 5.2.2. Entradas y Salidas de Stock
- **Escaneo QR**: Integración con cámara web/móvil para lectura de códigos
- **Vale de salida**: Generación automática con PDF descargable
- **Campos requeridos**: Código_ITEM, Descripción, Cantidad, Responsable, Área_Solicitante

#### 5.2.3. Préstamo de Equipo
- **Flujo automatizado**: 
  1. Solicitud desde Ventas → Notificación automática a Almacén
  2. Generación de folio y vale descargable
  3. Seguimiento automático de devolución
- **Alertas**: Notificación de préstamos no devueltos en tiempo

#### 5.2.4. Reporte de Inventario
- **Gráficos**: Ventas por marcas/modelos con comparativo mensual
- **Top 10**: Productos más vendidos
- **Exportación**: Excel compatible con sistemas externos

### 5.3. Módulo de Calidad

Gestión documental y seguimiento de la mejora continua.

#### 5.3.1. Gestión de Procedimientos
- **Repositorio centralizado**: PDFs de procedimientos técnicos y generales
- **Control de versiones**: Historial de cambios
- **Permisos**: Solo Calidad y Sistemas pueden subir/eliminar

#### 5.3.2. Encuestas (Integración QuestionPro)
- **Método de integración**: Enlaces directos embebidos (iframes o botones de acceso)
- **Configuración**: Panel para actualizar URLs de encuestas
- **Tracking**: Notificación automática a Calidad cuando se accede a un enlace
- **Implementación sugerida**:
  ```typescript
  // Componente para enlace de encuesta
  const EncuestaLink = ({ url, titulo }) => {
    const handleClick = () => {
      // Registrar acceso en la base de datos
      fetch('/api/encuestas/log-access', {
        method: 'POST',
        body: JSON.stringify({ url, timestamp: new Date() })
      });
      // Abrir enlace en nueva pestaña
      window.open(url, '_blank');
    };
    return <button onClick={handleClick}>{titulo}</button>;
  };
  ```

#### 5.3.3. Seguimiento de Acciones Correctivas
- **Tipos**: Minutas, Quejas, Trabajos No Conformes, Llamados de Atención, Acciones Correctivas
- **Estados**: En Proceso, Cerrado (con barra de progreso visual)
- **Recordatorios**: Notificaciones automáticas para revisión de pendientes

### 5.4. Módulo de Clientes (CRM)

Gestión de la cartera de clientes.

- **Alta de Clientes**: Formularios para registro rápido (RFC y vendedor) y completo de clientes, con validación de RFC contra el SAT.
- **Vista "Mis Clientes"**: Panel con filtros avanzados para segmentar la cartera de clientes.
- **Historial del Cliente**: Acceso al historial de compras, cotizaciones, y notas de seguimiento.

### 5.5. Módulo de Informes de Calibración

Gestión del ciclo de vida de los servicios de calibración.

- **Asignación de Folios**: Generación de folios únicos y secuenciales para servicios en Planta (`P-YYYY-####`) y Laboratorio (`L-YYYY-####`).
- **Status de Informes**: Vista de calendario para seguimiento del estado de los informes (Enviado, En Proceso, Moroso, etc.) con cálculo automático de días de entrega.
- **Historial de Folios**: Base de datos consultable de todos los servicios realizados.
- **Dashboard de Estadísticos**: Gráficas sobre folios asignados, rendimiento por técnico y eficiencia del proceso.

### 5.6. Módulo de Logística

Coordinación de servicios en planta y gestión de proyectos.

- **Calendario de Servicios**: Agenda visual para programar servicios, asignando técnicos y recursos.
- **Gestión de Proyectos**: Seguimiento del estado de proyectos autorizados (En proceso, Finalizado, etc.).
- **Historial de Ingresos**: Registro de equipos recibidos para servicio.
- **Dashboard de Estadísticos**: Análisis de cumplimiento de objetivos y tiempos de servicio.

### 5.7. Módulo de Metrología

Gestión de la operación técnica y del personal.

- **Asignación de Tareas**: Creación y asignación de tareas de calibración o verificación a técnicos.
- **Métricas de Rendimiento**: Dashboard para visualizar el rendimiento por técnico (tiempos de entrega, servicios completados).
- **Calendario de Calibración de Patrones**: Programación anual de la calibración de equipos patrón internos con alertas de vencimiento.
- **Gestión de Retroalimentación y Autorizaciones**: Registro de feedback técnico y autorizaciones de servicio.

### 5.8. Módulo de Recursos Humanos (RH)

Gestión del capital humano y desarrollo profesional.

- **Capacitación (RFP)**: Automatización del plan de formación por perfil de puesto, con evaluaciones y seguimiento de calificaciones.
- **Agenda de Capacitación**: Calendario para agendar y gestionar las capacitaciones del personal.
- **Seguimiento Personal (RSP)**: Registro de reuniones de seguimiento y objetivos profesionales.
- **Expedientes Digitales**: Almacenamiento seguro de expedientes contractuales y laborales de los empleados.

### 5.9. Módulo de Ventas

Herramientas para el equipo comercial.

- **Gestión de Clientes Asignados**: CRUD de clientes para cada vendedor.
- **Cotizador**: Herramienta para crear y gestionar cotizaciones, arrastrando ítems desde el inventario.
- **CRM de Proyectos**: Seguimiento del progreso de oportunidades de venta.
- **Dashboard de Ventas**: Estadísticas de ventas por mes, vendedor y producto.

### 5.10. Módulo de Sistemas (Requiere permisos de Administrador)

Herramientas para la administración y mantenimiento del sistema.

- **Gestión de Usuarios (CRUD)**: Interfaz completa para la administración de usuarios del sistema.
    - **Crear usuarios**: Registro de nuevos usuarios con validación de email único y contraseñas seguras
    - **Listar usuarios**: Vista tabular con filtros por estado, rol y área
    - **Editar usuarios**: Modalidad de edición para modificar datos de usuarios existentes incluyendo:
        - Información personal: nombre, email, teléfono, área de trabajo
        - Cambio de contraseña: validación de contraseñas seguras con hash bcrypt
        - Estado del usuario: activo/inactivo/bloqueado
        - Asignación de roles: múltiples roles por usuario con selección visual
        - Campos de auditoría: created_by, updated_by para trazabilidad
    - **Eliminar usuarios**: Baja lógica o física con confirmación de seguridad
    - **Activar/Desactivar usuarios**: Control de acceso sin eliminar el registro

- **Gestión de Roles y Permisos**: Asignación de roles a usuarios y configuración detallada de los permisos para cada rol, definiendo el acceso a módulos y acciones específicas (crear, leer, editar, eliminar, aprobar).

- **Gestión de Backups**:
    - **Creación de Backups**: Funcionalidad para generar respaldos de la base de datos de forma manual e inmediata.
    - **Programación de Backups**: Configuración de respaldos automáticos (diarios, semanales, mensuales).
    - **Restauración de Backups**: Interfaz para restaurar la base de datos a partir de un archivo de respaldo existente.

- **Auditoría del Sistema (Logs)**: Visualizador de logs de auditoría para rastrear acciones críticas realizadas en el sistema (ej. cambios en permisos, eliminación de datos, modificaciones de usuarios, etc.).

---

## 6. Requerimientos No Funcionales Detallados

### 6.1. Seguridad
- **Autenticación**: JWT con refresh tokens
- **Autorización**: RBAC (Role-Based Access Control)
- **Hashing**: bcrypt para passwords
- **Headers de seguridad**: Helmet.js
- **Protección CORS**: Configuración restrictiva
- **Auditoría**: Log de todas las acciones críticas
- **Validación**: Input sanitization con class-validator

### 6.2. Rendimiento
- **Frontend**: 
  - React Query para cache inteligente
  - Lazy loading de componentes
  - Optimización de imágenes con Next.js
- **Backend**:
  - Queries optimizadas con TypeORM
  - Paginación en listados extensos
  - Indexación de base de datos

#### ⚡ Tips de Velocidad para Desarrollo:
- **Nodemon en desarrollo**: Recarga automática del servidor para desarrollo eficiente
- **Cache con Redis**: Implementar para consultas frecuentes y sesiones de usuario
- **Async/await consistente**: Evitar callback hell, usar patrones asíncronos modernos
- **Paginación desde el inicio**: Implementar en todas las listas para evitar problemas de performance
- **Índices en BD**: Crear índices apropiados para consultas frecuentes (email, foreign keys, etc.)
- **Connection pooling**: Configurar pool de conexiones de base de datos optimizado
- **Compresión gzip**: Activar en Express.js para respuestas más rápidas
- **Lazy loading**: Cargar componentes y datos bajo demanda

### 6.3. Usabilidad y Accesibilidad
- **Responsive design**: Tailwind CSS mobile-first
- **Componentes**: Radix UI para accesibilidad WAI-ARIA
- **Navegadores soportados**: Chrome, Firefox, Safari, Edge (últimas 2 versiones)
- **Móviles**: iOS Safari, Chrome Android

### 6.4. Integraciones Detalladas

#### 6.4.1. Contpaq
- **Método preferido**: Enlaces directos embebidos (iframe o ventana emergente)
- **Alternativa**: API REST si está disponible
- **Funcionalidad**: Sincronización de datos contables y facturación

#### 6.4.2. Seguimiento de Paquetería
- **Implementación**: Campo para número de guía + enlace de tracking
- **Integración**: URLs dinámicas según paquetería (DHL, FedEx, etc.)
- **Ejemplo**: 
  ```typescript
  const trackingUrl = `https://www.fedex.com/fedextrack/?trknbr=${guideNumber}`;
  ```

#### 6.4.3. Outlook/SMTP
- **Notificaciones**: Nodemailer con configuración SMTP
- **Templates**: HTML templates para diferentes tipos de notificaciones
- **Configuración**: Variables de entorno para credenciales

#### 6.4.4. Validación SAT (RFC)
- **API**: Integración con servicios de validación de RFC
- **Implementación**: Verificación automática en alta de clientes
- **Fallback**: Validación manual si API no está disponible

---

## 7. Estrategia de Testing Completa

### 7.1. Testing por Capas

#### Frontend Testing:
- **Unit Tests**: Componentes individuales con Jest/React Testing Library
- **Integration Tests**: Flujos entre componentes
- **E2E Tests**: Cypress para flujos críticos de usuario
- **Visual Regression**: Storybook para componentes UI

#### Backend Testing:
- **Unit Tests**: Servicios y utilidades individuales
- **Integration Tests**: Endpoints de API con Supertest
- **Database Tests**: Operaciones de TypeORM
- **Authentication Tests**: Verificación de JWT y permisos

### 7.2. Testing del Módulo de Sistemas (Crítico)

#### 7.2.1. CRUD de Usuarios
```typescript
// Ejemplo de test unitario
describe('User CRUD Operations', () => {
  test('should create user with valid data', async () => {
    const userData = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'SecurePass123!',
      phone: '3333333333',
      area: 'Sistemas',
      status: 'activo'
    };
    const result = await userService.createUser(userData);
    expect(result.email).toBe(userData.email);
    expect(result.name).toBe(userData.name);
    expect(result.password).toBeUndefined(); // No debe devolver password
  });

  test('should update user data successfully', async () => {
    const updateData = {
      name: 'Updated Name',
      phone: '4444444444',
      area: 'Ventas',
      status: 'activo'
    };
    const result = await userService.updateUser(userId, updateData);
    expect(result.name).toBe(updateData.name);
    expect(result.phone).toBe(updateData.phone);
    expect(result.area).toBe(updateData.area);
  });

  test('should update user password with proper hashing', async () => {
    const newPassword = 'NewSecurePass456!';
    await userService.updateUser(userId, { password: newPassword });
    const user = await userService.findById(userId);
    expect(await bcrypt.compare(newPassword, user.password)).toBe(true);
  });

  test('should not update user with duplicate email', async () => {
    const updateData = { email: '<EMAIL>' };
    await expect(userService.updateUser(userId, updateData))
      .rejects.toThrow('El correo electrónico ya está en uso');
  });

  test('should validate password complexity on update', async () => {
    const weakPassword = '123';
    await expect(userService.updateUser(userId, { password: weakPassword }))
      .rejects.toThrow('La contraseña debe tener al menos 6 caracteres');
  });

  test('should track user modifications with audit fields', async () => {
    const updateData = { name: 'Audited Update', updated_by: adminUserId };
    const result = await userService.updateUser(userId, updateData);
    expect(result.updated_by).toBe(adminUserId);
    expect(result.updated_at).toBeDefined();
  });
});
```

#### 7.2.2. Testing de Roles y Permisos
```typescript
// Ejemplo de test de autorización
describe('Authorization Middleware', () => {
  test('should allow admin to access all routes', async () => {
    const adminToken = generateToken({ role: 'ADMIN' });
    const response = await request(app)
      .get('/api/admin/users')
      .set('Authorization', `Bearer ${adminToken}`);
    expect(response.status).toBe(200);
  });

  test('should deny user access to admin routes', async () => {
    const userToken = generateToken({ role: 'USER' });
    const response = await request(app)
      .get('/api/admin/users')
      .set('Authorization', `Bearer ${userToken}`);
    expect(response.status).toBe(403);
  });
});
```

#### 7.2.3. Testing de Backups (CRÍTICO)
```typescript
// Test crítico de backup y restauración
describe('Backup System', () => {
  test('should create backup successfully', async () => {
    const backup = await backupService.createBackup();
    expect(backup.filename).toBeDefined();
    expect(backup.size).toBeGreaterThan(0);
  });

  test('should restore from backup successfully', async () => {
    // Crear datos de prueba
    await createTestData();
    
    // Crear backup
    const backup = await backupService.createBackup();
    
    // Modificar datos
    await modifyTestData();
    
    // Restaurar backup
    await backupService.restoreBackup(backup.filename);
    
    // Verificar que los datos originales fueron restaurados
    const restoredData = await getTestData();
    expect(restoredData).toMatchOriginalData();
  });
});
```

### 7.3. Criterios de Aceptación

#### Build Verification:
- `npm run build` debe completarse sin errores
- `npm run test` debe tener 100% de tests pasando
- Coverage mínimo: 80% para código crítico (autenticación, permisos, backups)
- Coverage mínimo: 60% para código general

#### Testing Obligatorio:
- **Módulo de Sistemas**: 95% de coverage
- **Autenticación y Autorización**: 100% de coverage
- **APIs críticas**: 90% de coverage
- **Flujos de backup/restore**: 100% de coverage

#### Métricas de Calidad:
- Tiempo de respuesta API: < 500ms para 95% de requests
- Tiempo de carga inicial: < 3 segundos
- Accesibilidad: Lighthouse score > 90
- Performance: Lighthouse score > 80

---

## 8. Entidades de Base de Datos Principales

### 8.1. Usuarios y Seguridad
```typescript
// User Entity (Estructura actual en base de datos)
{
  id: number; // BIGSERIAL PRIMARY KEY
  name: string; // VARCHAR(100) NOT NULL
  email: string; // VARCHAR(100) NOT NULL UNIQUE
  phone?: string; // VARCHAR(20) NULLABLE
  password: string; // VARCHAR(255) NOT NULL - bcrypt hashed
  status: string; // VARCHAR(20) DEFAULT 'activo' - valores: activo, inactivo, bloqueado
  area?: string; // VARCHAR(100) NULLABLE - área de trabajo del usuario
  refresh_token?: string; // TEXT NULLABLE - para JWT refresh tokens
  active: boolean; // BOOLEAN DEFAULT true - campo adicional para compatibilidad
  created_at: Date; // TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
  updated_at: Date; // TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
  created_by?: number; // BIGINT REFERENCES users(id) - usuario que lo creó
  updated_by?: number; // BIGINT REFERENCES users(id) - último usuario que lo modificó
  roles: Role[]; // Relación many-to-many con la tabla roles
}

// Role Entity
{
  id: number;
  name: string;
  permissions: Permission[];
  description: string;
}

// Permission Entity
{
  id: number;
  resource: string; // 'users', 'clients', 'contracts', etc.
  action: string; // 'create', 'read', 'update', 'delete', 'approve'
  conditions: JSON; // Condiciones adicionales
}
```

### 8.2. Almacén
```typescript
// Product Entity
{
  id: number;
  codigo_item: string;
  nombre: string;
  marca: string;
  modelo: string;
  categoria: string;
  tipo_almacen: 'GENERAL' | 'ROTATIVO';
  stock_disponible: number;
  stock_comprometido: number;
  stock_minimo: number;
  estado: 'DISPONIBLE' | 'AGOTADO' | 'DESCONTINUADO';
}

// StockMovement Entity
{
  id: number;
  product: Product;
  tipo: 'ENTRADA' | 'SALIDA' | 'PRESTAMO' | 'DEVOLUCION';
  cantidad: number;
  responsable: User;
  area_solicitante: string;
  observaciones: string;
  fecha: Date;
}
```

### 8.3. Administración
```typescript
// Contract Entity
{
  id: number;
  numero_contrato: string;
  cliente: Client;
  tipo_servicio: string;
  responsable_ventas: User;
  monto: number;
  fecha_inicio: Date;
  fecha_vencimiento: Date;
  estado: 'ACTIVO' | 'VENCIDO' | 'RENOVADO' | 'CANCELADO';
  archivo_pdf: string;
}

// Request Entity (Viáticos, Tiempo Extra, Paquetería)
{
  id: number;
  tipo: 'VIATICOS' | 'TIEMPO_EXTRA' | 'PAQUETERIA';
  solicitante: User;
  area: string;
  estado: 'PENDIENTE' | 'APROBADO' | 'RECHAZADO';
  datos_especificos: JSON; // Campos específicos por tipo
  aprobado_por: User;
  fecha_aprobacion: Date;
  comentarios: string;
}
```

---

## 9. Sistema de Notificaciones Push en Tiempo Real

### Funcionalidades Principales:
- **Notificaciones en Navegador**: Notificaciones push nativas del navegador para alertas críticas
- **Centro de Notificaciones**: Panel interno en el sistema con historial de notificaciones
- **Notificaciones por Email**: Integración SMTP para envío automático de correos
- **Configuración Personalizable**: Cada usuario puede configurar qué notificaciones desea recibir

### Tipos de Notificaciones por Módulo:

#### Administración:
- Nuevas solicitudes de viáticos/tiempo extra pendientes de aprobación
- Contratos REPSE próximos a vencer (30, 15, 7 días)
- Facturas vencidas (cartera morosa)
- Solicitudes de crédito pendientes de revisión

#### Almacén:
- Stock bajo mínimos establecidos
- Equipos prestados próximos a fecha de devolución
- Nuevas solicitudes de préstamo de equipo

#### Logística:
- Servicios programados para el día siguiente
- Proyectos con retrasos en cronograma
- Equipos recibidos pendientes de asignación

#### Metrología:
- Calibración de patrones próxima a vencer
- Tareas asignadas con fecha límite próxima
- Equipos pendientes de autorización de servicio

#### Recursos Humanos:
- Capacitaciones programadas próximas
- Evaluaciones pendientes de realizar
- Reuniones de seguimiento personal agendadas

#### Ventas:
- Cotizaciones próximas a expirar
- Seguimientos comerciales programados
- Nuevas oportunidades asignadas

---

## 10. Sistema de Exportación de Datos

### Formatos Soportados:
- **Excel (.xlsx)**: Para análisis de datos y reportes detallados
- **PDF**: Para documentos oficiales y presentaciones
- **CSV**: Para intercambio de datos con sistemas externos

### Reportes Exportables por Módulo:

#### Administración:
- Reporte de facturación mensual/anual
- Estado de cartera de clientes
- Análisis de ventas por vendedor
- Resumen de gastos operativos
- Estado de contratos REPSE

#### Almacén:
- Inventario actual con valorización
- Movimientos de stock por período
- Rotación de inventario por producto
- Reporte de equipos prestados

#### Logística:
- Calendario de servicios programados
- Reporte de cumplimiento de tiempos
- Estado de proyectos en curso
- Historial de servicios por cliente

#### Metrología:
- Rendimiento por técnico
- Calendario de calibración de patrones
- Estadísticas de tiempos de entrega
- Reporte de tareas completadas

#### Informes de Calibración:
- Listado de folios por período
- Estadísticas de entrega
- Reporte de servicios morosos
- Análisis de productividad

#### Recursos Humanos:
- Reporte de capacitaciones por empleado
- Estado de expedientes digitales
- Evaluaciones de desempeño
- Calendario de actividades RH

#### Ventas:
- Reporte de ventas por período
- Estado de cotizaciones
- Pipeline de oportunidades
- Análisis de conversión por vendedor

### Configuración de Exportación:
- **Filtros Avanzados**: Por fechas, usuarios, estados, clientes, etc.
- **Plantillas Personalizables**: Logos, colores corporativos, formato de empresa
- **Programación Automática**: Envío automático de reportes por email
- **Permisos de Exportación**: Control de acceso por rol de usuario

---

## 11. Configuración de Empresa

### Datos Fiscales:
- **Información Legal**:
  - Razón social completa
  - RFC de la empresa
  - Régimen fiscal
  - Domicilio fiscal completo
  - Representante legal

- **Datos Comerciales**:
  - Nombre comercial
  - Giro de la empresa
  - Teléfonos de contacto
  - Correos electrónicos corporativos
  - Página web y redes sociales

### Identidad Corporativa:
- **Logos y Marca**:
  - Logo principal (formato PNG/SVG)
  - Logo secundario/simplificado
  - Colores corporativos (códigos hex)
  - Tipografías oficiales

- **Plantillas de Documentos**:
  - Plantilla de cotizaciones
  - Plantilla de contratos
  - Plantilla de informes de calibración
  - Plantilla de facturas
  - Membrete para documentos oficiales

### Configuraciones Operativas:
- **Numeración de Folios**:
  - Formato de folios fiscales
  - Formato de folios de servicio
  - Contadores actuales y rangos

- **Parámetros del Sistema**:
  - Días de vigencia de cotizaciones
  - Alertas de vencimiento (contratos, calibraciones)
  - Configuración de stock mínimo por defecto
  - Horarios laborales y días hábiles

---

## 12. Gestión Documental Centralizada

### Repositorio Central:
- **Organización Jerárquica**: Estructura de carpetas por tipo de documento y área
- **Control de Versiones**: Historial de cambios y versiones de documentos
- **Metadatos**: Etiquetado automático con fecha, autor, tipo, área relacionada
- **Búsqueda Avanzada**: Por nombre, contenido, fecha, área, tipo de documento

### Tipos de Documentos por Área:

#### Contratos y Legal:
- Contratos REPSE con clientes
- Convenios con proveedores
- Contratos laborales (RH)
- Documentos fiscales y legales

#### Comercial:
- Cotizaciones enviadas
- Propuestas técnicas
- Catálogos de productos/servicios
- Presentaciones comerciales

#### Técnico:
- Informes de calibración generados
- Certificados de patrones
- Procedimientos técnicos
- Manuales de equipo

#### Administrativo:
- Facturas recibidas y emitidas
- Comprobantes de gastos
- Documentos de nómina
- Reportes financieros

#### Calidad:
- Procedimientos del SGC
- Registros de auditorías
- Acciones correctivas
- Encuestas de satisfacción

### Funcionalidades del Repositorio:
- **Carga Masiva**: Subida múltiple de archivos con drag & drop
- **Previsualización**: Visualización de PDFs, imágenes y documentos de Office
- **Compartir Documentos**: Enlaces seguros para compartir con externos
- **Notificaciones**: Alertas cuando se suben nuevos documentos relevantes
- **Permisos Granulares**: Control de acceso por documento, carpeta o tipo
- **Integración**: Vinculación automática con registros de otros módulos
- **Backup Automático**: Respaldo seguro de todos los documentos

### Seguridad y Compliance:
- **Cifrado**: Documentos sensibles cifrados en reposo
- **Auditoría**: Log de accesos y modificaciones
- **Retención**: Políticas de retención según tipo de documento
- **Eliminación Segura**: Proceso controlado para eliminación de documentos

---

## 13. Comandos Útiles para Desarrollo

### 🔧 Comandos de Desarrollo Principal:

#### Frontend (Next.js):
```bash
# Desarrollo con hot reload
npm run dev

# Build para producción
npm run build

# Iniciar en producción
npm start

# Linting
npm run lint

# Testing
npm run test
npm run test:watch
```

#### Backend (Express + TypeScript):
```bash
# Desarrollo con nodemon
npm run dev

# Build TypeScript
npm run build

# Producción
npm run production
npm run start:prod

# Testing
npm test
npm run test:watch
npm run test:coverage

# Migraciones de base de datos
npm run migration:generate
npm run migration:run
npm run migration:revert
```

#### Comandos de Base de Datos:
```bash
# Sincronizar esquema
npm run db:sync

# Seedear datos iniciales
npm run db:seed

# Backup manual
npm run db:backup

# Restaurar backup
npm run db:restore
```

#### Comandos de Monitoreo y Producción:
```bash
# PM2 - Proceso manager
pm2 start ecosystem.config.js
pm2 monit
pm2 logs
pm2 reload all
pm2 stop all

# Docker (si se usa)
docker-compose up -d
docker-compose logs -f
docker-compose down

# Verificación de salud
curl http://localhost:3001/health
curl http://localhost:3000/api/health
```

#### Comandos de Utilidad:
```bash
# Verificar puertos ocupados
lsof -i :3000
lsof -i :3001

# Limpiar caché de npm
npm cache clean --force

# Reinstalar dependencias
rm -rf node_modules package-lock.json
npm install

# Verificar vulnerabilidades
npm audit
npm audit fix

# Análisis de bundle size
npm run analyze
```

### 📋 Scripts de Package.json Recomendados:

#### Frontend:
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "test": "jest",
    "test:watch": "jest --watch",
    "type-check": "tsc --noEmit"
  }
}
```

#### Backend:
```json
{
  "scripts": {
    "dev": "nodemon --exec ts-node src/server.ts",
    "build": "tsc",
    "start": "node dist/server.js",
    "start:prod": "NODE_ENV=production node dist/server.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "migration:generate": "typeorm migration:generate",
    "migration:run": "typeorm migration:run",
    "db:seed": "ts-node scripts/seed.ts"
  }
}
```

</PRD>