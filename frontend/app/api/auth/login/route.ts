import { NextResponse } from "next/server"

export async function POST(request: Request) {
  const { credential, password } = await request.json()

  // Proxy to backend
  const backendRes = await fetch(`${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000"}/api/auth/login`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ credential, password }),
  })

  const data = await backendRes.json()
  return NextResponse.json(data, { status: backendRes.status })
}
