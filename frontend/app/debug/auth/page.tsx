"use client"

import { useEffect, useState } from "react"
import { useAuth } from "@/components/providers/auth-provider"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Shield, User, Key, Database, RefreshCw, CheckCircle, XCircle } from "lucide-react"

export default function AuthDebugPage() {
  const { user, isAuthenticated, hasPermission, hasRole } = useAuth()
  const [localStorageData, setLocalStorageData] = useState<any>({})
  const [apiTest, setApiTest] = useState<{ status: string; message: string } | null>(null)

  const refreshLocalStorageData = () => {
    // Leer datos del localStorage
    const data = {
      auth_token: localStorage.getItem('auth_token'),
      refresh_token: localStorage.getItem('refresh_token'),
      user_data: localStorage.getItem('user_data'),
      access_token: localStorage.getItem('access_token'),
    }
    setLocalStorageData(data)
  }

  useEffect(() => {
    refreshLocalStorageData()
  }, [])

  const testApiConnection = async () => {
    try {
      // Actualizar datos primero
      refreshLocalStorageData()

      const token = localStorage.getItem('auth_token')
      console.log('[DEBUG] Token encontrado:', token ? 'SÍ' : 'NO')

      if (!token) {
        setApiTest({ status: 'error', message: 'No se encontró auth_token en localStorage' })
        return
      }

      console.log('[DEBUG] Probando conexión a: http://localhost:3000/api/systems/users')
      const response = await fetch('http://localhost:3000/api/systems/users', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      console.log('[DEBUG] Response status:', response.status)
      console.log('[DEBUG] Response ok:', response.ok)

      if (response.ok) {
        const data = await response.json()
        console.log('[DEBUG] Response data:', data)
        setApiTest({
          status: 'success',
          message: `✅ API funcionando correctamente - Encontrados ${data.total || data.data?.length || 0} usuarios`
        })
      } else {
        const errorText = await response.text()
        console.log('[DEBUG] Error response:', errorText)
        setApiTest({
          status: 'error',
          message: `❌ Error API: ${response.status} ${response.statusText} - ${errorText}`
        })
      }
    } catch (error) {
      console.error('[DEBUG] Connection error:', error)
      setApiTest({
        status: 'error',
        message: `❌ Error de conexión: ${error instanceof Error ? error.message : 'Error desconocido'}`
      })
    }
  }

  const clearAuth = () => {
    localStorage.removeItem('auth_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('user_data')
    localStorage.removeItem('access_token')
    window.location.reload()
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-2 mb-6">
        <Shield className="h-6 w-6" />
        <h1 className="text-2xl font-bold">Panel de Debug - Autenticación</h1>
      </div>

      {/* Estado de Autenticación */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Estado de Autenticación
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-2">
            <span>Estado:</span>
            {isAuthenticated ? (
              <Badge variant="default" className="bg-green-100 text-green-800">
                <CheckCircle className="h-3 w-3 mr-1" />
                Autenticado
              </Badge>
            ) : (
              <Badge variant="destructive">
                <XCircle className="h-3 w-3 mr-1" />
                No Autenticado
              </Badge>
            )}
          </div>
          
          {user && (
            <div className="space-y-2">
              <p><strong>Usuario:</strong> {user.name}</p>
              <p><strong>Email:</strong> {user.email}</p>
              <p><strong>Área:</strong> {user.area}</p>
              <div className="flex flex-wrap gap-1">
                <strong>Roles:</strong>
                {user.roles?.map((role: string) => (
                  <Badge key={role} variant="outline">{role}</Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* LocalStorage Data */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Datos en LocalStorage
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 font-mono text-sm">
            {Object.entries(localStorageData).map(([key, value]) => (
              <div key={key} className="flex items-start gap-2">
                <span className="font-semibold min-w-[120px]">{key}:</span>
                <span className={value ? "text-green-600" : "text-red-500"}>
                  {value ? "✓ Present" : "✗ Missing"}
                </span>
                {value && (
                  <span className="text-xs text-gray-500 break-all">
                    ({typeof value === 'string' && value.length > 50 
                      ? `${value.substring(0, 50)}...` 
                      : value})
                  </span>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* API Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Test de Conexión API
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={testApiConnection} className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Probar Conexión
            </Button>
            <Button onClick={refreshLocalStorageData} variant="outline" className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Actualizar Datos
            </Button>
          </div>
          
          {apiTest && (
            <div className={`p-3 rounded-md ${
              apiTest.status === 'success' 
                ? 'bg-green-50 text-green-800 border border-green-200' 
                : 'bg-red-50 text-red-800 border border-red-200'
            }`}>
              <strong>{apiTest.status === 'success' ? 'Éxito:' : 'Error:'}</strong> {apiTest.message}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Permisos de Sistemas */}
      <Card>
        <CardHeader>
          <CardTitle>Permisos de Sistemas</CardTitle>
          <CardDescription>Verificación de permisos específicos del módulo sistemas</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2 text-sm">
            {[
              'sistemas:users:read',
              'sistemas:users:create',
              'sistemas:users:update',
              'sistemas:roles:read',
              'sistemas:roles:create',
              'sistemas:permissions:read'
            ].map(permission => (
              <div key={permission} className="flex items-center gap-2">
                {hasPermission(permission) ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                <span className="font-mono text-xs">{permission}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Acciones */}
      <Card>
        <CardHeader>
          <CardTitle>Acciones de Debug</CardTitle>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={clearAuth} 
            variant="destructive"
            className="flex items-center gap-2"
          >
            <XCircle className="h-4 w-4" />
            Limpiar Autenticación
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
