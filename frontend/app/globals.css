@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import custom form components styles */
@import '../styles/form-components.css';

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Inter", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  font-weight: 300;
  letter-spacing: -0.01em;
  margin: 0;
  padding: 0;
  min-width: 0;
  max-width: 100vw;
  box-sizing: border-box;
  overflow-x: hidden;
  @apply text-foreground;
}

html, body, #__next {
  min-height: 100vh !important;
  height: 100%;
  width: 100%;
}



body::before, .dark body::before {
  content: '';
  position: fixed;
  top: 0; left: 0; width: 100vw; height: 100vh;
  pointer-events: none;
  z-index: 0;
  opacity: 0.18;
  background: radial-gradient(circle at 60% 40%, #fffbe6 0%, transparent 70%);
}

.dark body::before {
  background: radial-gradient(circle at 40% 60%, #3a1c71 0%, transparent 70%);
  opacity: 0.22;
}

body, .dark body {
  z-index: 1;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Clases para eliminar bordes molestos */
  .no-border {
    border: none !important;
  }

  .subtle-border {
    border: 1px solid hsl(var(--border) / 0.1) !important;
  }

  .invisible-border {
    border: 1px solid transparent !important;
  }

  /* Forzar eliminación de bordes en tarjetas */
  .force-no-border,
  .force-no-border * {
    border: none !important;
    outline: none !important;
  }

  /* Eliminar bordes específicamente de elementos comunes */
  .card-no-border {
    border: none !important;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1) !important;
  }

  .card-no-border * {
    border: none !important;
  }

  /* Estilos específicos solo para checkboxes de permisos */
  .permission-checkbox {
    border: 2px solid #374151 !important;
    background-color: #f9fafb !important;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1) !important;
    min-width: 20px !important;
    min-height: 20px !important;
    width: 20px !important;
    height: 20px !important;
  }

  .dark .permission-checkbox {
    border: 2px solid rgba(255, 255, 255, 0.8) !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
  }

  .dark .permission-checkbox[data-state="checked"] {
    border: 2px solid #3b82f6 !important;
    background-color: #3b82f6 !important;
  }

  .permission-checkbox:hover {
    border-color: #1f2937 !important;
    background-color: rgba(31, 41, 55, 0.05) !important;
    transform: scale(1.02) !important;
  }

  .dark .permission-checkbox:hover {
    border-color: #60a5fa !important;
    background-color: rgba(96, 165, 250, 0.15) !important;
    transform: scale(1.02) !important;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 1 61% 27%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 96%;
    --input: 0 0% 94%;
    --ring: 1 61% 27%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 15 23% 4%;
    --foreground: 0 0% 98%;
    --card: 15 23% 4%;
    --card-foreground: 0 0% 98%;
    --popover: 15 23% 4%;
    --popover-foreground: 0 0% 98%;
    --primary: 1 61% 27%;
    --primary-foreground: 0 0% 98%;
    --secondary: 15 20% 8%;
    --secondary-foreground: 0 0% 98%;
    --muted: 15 20% 8%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 15 20% 8%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 15 20% 8%;
    --input: 15 20% 8%;
    --ring: 1 61% 27%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 15 23% 4%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 15 20% 8%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 15 20% 8%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  body {
    @apply text-foreground;
  }
}

/* Animaciones y utilidades extra (no modificar) */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
  }
  66% {
    transform: translateY(10px) rotate(240deg);
  }
}

@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-gradient {
  background-size: 400% 400%;
  animation: gradient-shift 8s ease infinite;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Glass effect utilities for playground components */
.bg-glass-light {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.bg-glass-dark {
  background: rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}