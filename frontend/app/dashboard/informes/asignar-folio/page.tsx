// filepath: /frontend/app/dashboard/informes/asignar-folio/page.tsx
"use client";

import { PageGuard } from "@/components/auth/page-guard";
import { AsignarFolioView } from "@/components/informes/AsignarFolioView";

export default function AsignarFolioPage() {
  return (
    <PageGuard
      requiredPermissions={["informes:folios:assign", "informes:folios:create"]}
      unauthorizedTitle="Acceso Denegado - Asignación de Folios"
      unauthorizedMessage="No tienes los permisos necesarios para asignar folios."
    >
      <AsignarFolioView />
    </PageGuard>
  );
}
