// filepath: /frontend/app/dashboard/informes/estadisticos/page.tsx
"use client";

import { PageGuard } from "@/components/auth/page-guard";
import { EstadisticosInformesView } from "@/components/informes/EstadisticosInformesView";

export default function EstadisticosPage() {
  return (
    <PageGuard
      requiredPermissions={["informes:estadisticos:read", "informes:estadisticos:generate"]}
      unauthorizedTitle="Acceso Denegado - Estadísticos de Informes"
      unauthorizedMessage="No tienes los permisos necesarios para acceder a los estadísticos de informes."
    >
      <EstadisticosInformesView />
    </PageGuard>
  );
}
