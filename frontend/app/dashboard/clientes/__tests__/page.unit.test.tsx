import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import ClientesPage from '../page';
import NuevoClientePage from '../nuevo/page';
import DetalleClientePage from '../[id]/page';
import EditarClientePage from '../[id]/editar/page';

// Mock de PageGuard para que siempre deje pasar
jest.mock('@/components/auth/page-guard', () => ({
  PageGuard: ({ children }: any) => <>{children}</>
}));
// Mock de usePagePermissions para simular acceso
jest.mock('@/hooks/usePagePermissions', () => ({
  usePagePermissions: () => ({ hasAccess: true })
}));
// Mock de UnauthorizedPage para evitar errores
jest.mock('@/components/auth/unauthorized-page', () => ({
  UnauthorizedPage: () => <div>Acceso Restringido</div>
}));

jest.mock('@/hooks/use-client', () => ({
  useClients: () => ({ data: { clients: [], page: 1, totalPages: 1 }, isLoading: false, error: null }),
  useClient: () => ({ data: { commercialName: 'Test', legalName: 'Test', rfc: 'ABC123456T12' }, isLoading: false, error: null }),
  useCreateClient: () => ({ mutate: jest.fn(), status: 'idle', isPending: false }),
  useUpdateClient: () => ({ mutate: jest.fn(), status: 'idle', isPending: false })
}));

jest.mock('next/navigation', () => ({
  useRouter: () => ({ push: jest.fn() }),
  useParams: () => ({ id: '1' })
}));

describe('Clientes - Páginas protegidas', () => {
  it('renderiza la página de listado', () => {
    render(<ClientesPage />);
    expect(screen.getByText(/Mis Clientes/i)).toBeInTheDocument();
  });
  it('renderiza la página de nuevo cliente', () => {
    render(<NuevoClientePage />);
    expect(screen.getByText(/Registro de Cliente/i)).toBeInTheDocument();
  });
  it('renderiza la página de detalle', () => {
    render(<DetalleClientePage />);
    expect(screen.getByText(/Detalle de Cliente/i)).toBeInTheDocument();
  });
  it('renderiza la página de edición', () => {
    render(<EditarClientePage />);
    expect(screen.getByText(/Editar Cliente/i)).toBeInTheDocument();
  });
});
