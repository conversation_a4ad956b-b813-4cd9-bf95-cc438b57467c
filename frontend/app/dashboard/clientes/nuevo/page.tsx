"use client"

import { ClientForm, ClientPayload } from '@/components/clientes/ClientForm';
import { useCreateClient } from '@/hooks/use-client';
import { useRouter } from 'next/navigation';
import { PageGuard } from "@/components/auth/page-guard";

export default function NuevoClientePage() {
  const router = useRouter();
  const createClient = useCreateClient();

  const handleSubmit = async (data: ClientPayload) => {
    console.log('[DEBUG][NuevoClientePage] Payload enviado:', data);
    createClient.mutate(data, {
      onSuccess: (created: any) => {
        console.log('[DEBUG][NuevoClientePage] Cliente creado:', created);
        router.push(`/dashboard/clientes/${created.id}`);
      },
      onError: (err: any) => {
        console.error('[DEBUG][NuevoClientePage] Error al crear cliente:', err);
      },
    });
  };

  return (
    <PageGuard 
      requiredPermissions={["clientes:create"]}
      unauthorizedTitle="Registro de Cliente - Acceso Restringido"
      unauthorizedMessage="Necesitas permisos de alta de clientes para acceder a esta sección."
    >
      <div className="max-w-3xl mx-auto py-8">
        <h1 className="text-2xl font-bold mb-6">Alta de cliente</h1>
        <ClientForm onSubmit={handleSubmit} loading={createClient.status === 'pending' || createClient.isPending} />
      </div>
    </PageGuard>
  );
}