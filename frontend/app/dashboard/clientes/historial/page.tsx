"use client";
import { useClientPurchases, useClientTopProducts, useClientQuotations } from "@/hooks/use-client";
import { Loader2, AlertCircle } from "lucide-react";
import { useState } from "react";

// Demo: id de cliente fijo (en real, obtén de router o props)
const CLIENT_ID = 1;

export default function HistorialClientePage() {
  const { data: compras, isLoading: loadingCompras, error: errorCompras } = useClientPurchases(CLIENT_ID);
  const { data: topProducts, isLoading: loadingTop, error: errorTop } = useClientTopProducts(CLIENT_ID);
  const { data: cotizacionesResponse, isLoading: loadingCot, error: errorCot } = useClientQuotations(CLIENT_ID);
  const cotizaciones = Array.isArray(cotizacionesResponse)
    ? cotizacionesResponse
    : cotizacionesResponse?.data ?? [];

  return (
    <div className="p-8 space-y-8">
      <h2 className="text-2xl font-bold mb-4">Historial de Cliente</h2>

      {/* Tabla de compras */}
      <section>
        <h3 className="text-xl font-semibold mb-2">Compras</h3>
        {loadingCompras ? (
          <div className="flex items-center gap-2"><Loader2 className="animate-spin" />Cargando compras...</div>
        ) : errorCompras ? (
          <div className="flex items-center gap-2 text-red-600"><AlertCircle />Error al cargar compras</div>
        ) : compras?.length === 0 ? (
          <div className="text-muted-foreground">Sin compras registradas</div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full border text-sm">
              <thead>
                <tr className="bg-muted">
                  <th className="px-2 py-1">Fecha</th>
                  <th className="px-2 py-1">Total</th>
                  <th className="px-2 py-1">Estatus</th>
                </tr>
              </thead>
              <tbody>
                {compras.map((c: any) => (
                  <tr key={c.id} className="border-b">
                    <td className="px-2 py-1">{c.date}</td>
                    <td className="px-2 py-1">${c.total}</td>
                    <td className="px-2 py-1">{c.status}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </section>

      {/* Productos más comprados */}
      <section>
        <h3 className="text-xl font-semibold mb-2">Productos más comprados</h3>
        {loadingTop ? (
          <div className="flex items-center gap-2"><Loader2 className="animate-spin" />Cargando productos...</div>
        ) : errorTop ? (
          <div className="flex items-center gap-2 text-red-600"><AlertCircle />Error al cargar productos</div>
        ) : topProducts?.length === 0 ? (
          <div className="text-muted-foreground">Sin productos registrados</div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full border text-sm">
              <thead>
                <tr className="bg-muted">
                  <th className="px-2 py-1">Producto</th>
                  <th className="px-2 py-1">Cantidad</th>
                  <th className="px-2 py-1">Total</th>
                </tr>
              </thead>
              <tbody>
                {topProducts.map((p: any) => (
                  <tr key={p.productId} className="border-b">
                    <td className="px-2 py-1">{p.name}</td>
                    <td className="px-2 py-1">{p.quantity}</td>
                    <td className="px-2 py-1">${p.total}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </section>

      {/* Cotizaciones */}
      <section>
        <h3 className="text-xl font-semibold mb-2">Cotizaciones</h3>
        {loadingCot ? (
          <div className="flex items-center gap-2"><Loader2 className="animate-spin" />Cargando cotizaciones...</div>
        ) : errorCot ? (
          <div className="flex items-center gap-2 text-red-600"><AlertCircle />Error al cargar cotizaciones</div>
        ) : cotizaciones?.length === 0 ? (
          <div className="text-muted-foreground">Sin cotizaciones registradas</div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full border text-sm">
              <thead>
                <tr className="bg-muted">
                  <th className="px-2 py-1">Folio</th>
                  <th className="px-2 py-1">Fecha</th>
                  <th className="px-2 py-1">Total</th>
                  <th className="px-2 py-1">Estatus</th>
                </tr>
              </thead>
              <tbody>
                {cotizaciones.map((q: any) => (
                  <tr key={q.id} className="border-b">
                    <td className="px-2 py-1">{q.folio}</td>
                    <td className="px-2 py-1">{q.date}</td>
                    <td className="px-2 py-1">${q.total}</td>
                    <td className="px-2 py-1">{q.status}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </section>
    </div>
  );
} 