"use client";
import { QuickClientForm } from "@/components/clientes/QuickClientForm";
import { useCreateClient } from "@/hooks/use-client";
import { toast } from "@/components/ui/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { CheckCircle2 } from "lucide-react";
import { useRouter } from "next/navigation";
import React from "react";

export default function AltaRapidaClientePage() {
  const createClient = useCreateClient();
  const router = useRouter();
  const [showSuccess, setShowSuccess] = React.useState(false);

  return (
    <div className="p-8 max-w-xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">Alta Rápida de Cliente</h2>
      <QuickClientForm
        onSubmit={async (data) => {
          try {
            await createClient.mutateAsync(data);
            setShowSuccess(true);
          } catch (error: any) {
            toast({ title: "Error al registrar cliente", description: error?.message || "Error desconocido", variant: "destructive" });
          }
        }}
        loading={createClient.status === 'pending'}
      />
      <Dialog open={showSuccess} onOpenChange={(open) => {
        setShowSuccess(open);
        if (!open) router.push("/dashboard/clientes");
      }}>
        <DialogContent className="max-w-sm text-center">
          <DialogHeader>
            <DialogTitle className="flex flex-col items-center gap-2">
              <CheckCircle2 className="w-12 h-12 text-green-600 mx-auto animate-bounce" />
              ¡Cliente registrado!
            </DialogTitle>
            <DialogDescription>
              El cliente fue creado exitosamente.<br />
              <button className="mt-4 btn btn-primary" onClick={() => router.push("/dashboard/clientes")}>Ver clientes</button>
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </div>
  );
} 