"use client";
import { ClientForm } from "@/components/clientes/ClientForm";
import { useCreateClient } from "@/hooks/use-client";
import { toast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { CheckCircle2 } from "lucide-react";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import React from "react";

export default function AltaCompletaClientePage() {
  const createClient = useCreateClient();
  const router = useRouter();
  const [showSuccess, setShowSuccess] = React.useState(false);
  const [createdId, setCreatedId] = React.useState<string | number | null>(null);

  return (
    <div className="p-8 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">Alta Completa de Cliente</h2>
      <ClientForm
        onSubmit={async (data: any) => {
          try {
            const result = await createClient.mutateAsync(data);
            setCreatedId(result?.id);
            setShowSuccess(true);
          } catch (error: any) {
            let message = "Error al registrar cliente";
            if (error?.response?.data?.message) {
              message = error.response.data.message;
            } else if (error?.message) {
              message = error.message;
            }
            toast({
              title: "Error",
              description: message,
              variant: "destructive"
            });
          }
        }}
        loading={createClient.isPending}
      />
      <Dialog open={showSuccess} onOpenChange={(open) => {
        setShowSuccess(open);
        if (!open && createdId) {
          router.push(`/dashboard/clientes/${createdId}`);
        }
      }}>
        <DialogContent className="flex flex-col items-center gap-4">
          <DialogHeader>
            <DialogTitle className="flex flex-col items-center gap-2">
              <CheckCircle2 className="w-16 h-16 text-green-500 animate-bounce" />
              ¡Cliente registrado exitosamente!
            </DialogTitle>
            <DialogDescription className="text-center">
              El cliente fue creado y guardado en el sistema.
            </DialogDescription>
          </DialogHeader>
          <Button onClick={() => {
            setShowSuccess(false);
            if (createdId) router.push(`/dashboard/clientes/${createdId}`);
          }} className="mt-2">Ver cliente</Button>
        </DialogContent>
      </Dialog>
    </div>
  );
} 