"use client"

import { useRouter, useParams } from "next/navigation";
import { useClient } from "../../../../hooks/use-client";
import { useEffect } from "react";
import { PageGuard } from "@/components/auth/page-guard";
import { ClientQuotationsTable } from "@/components/clientes/ClientQuotationsTable";

export default function DetalleClientePage() {
  const router = useRouter();
  const params = useParams();
  const clientId = typeof params?.id === 'string' || typeof params?.id === 'number' ? params.id : '';
  const { data, isLoading, error } = useClient(clientId);

  useEffect(() => {
    if (error) {
      // eslint-disable-next-line no-console
      console.error("[CLIENTES][DETAIL] Error al cargar cliente:", error);
    }
    if (data) {
      // eslint-disable-next-line no-console
      console.log("[CLIENTES][DETAIL] Datos cliente:", data);
    }
  }, [data, error]);

  if (isLoading) return <div className="py-10 text-center">Cargando...</div>;
  if (error) return <div className="py-10 text-center text-red-500">Error al cargar cliente</div>;
  if (!data) return <div className="py-10 text-center">Cliente no encontrado</div>;

  return (
    <PageGuard 
      requiredPermissions={["clientes:read"]}
      unauthorizedTitle="Detalle de Cliente - Acceso Restringido"
      unauthorizedMessage="Necesitas permisos de consulta de clientes para acceder a esta sección."
    >
      <div className="max-w-2xl mx-auto p-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold">Detalle de Cliente</h1>
          <button
            className="btn btn-outline"
            onClick={() => router.push(`/dashboard/clientes/${clientId}/editar`)}
          >Editar</button>
        </div>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <span className="font-semibold">Nombre comercial:</span> {data.commercialName}
          </div>
          <div>
            <span className="font-semibold">Razón social:</span> {data.legalName}
          </div>
          <div>
            <span className="font-semibold">RFC:</span> {data.rfc}
          </div>
          <div>
            <span className="font-semibold">Régimen fiscal:</span> {data.taxRegime}
          </div>
          <div>
            <span className="font-semibold">Dirección:</span> {data.address}
          </div>
          <div>
            <span className="font-semibold">Colonia:</span> {data.neighborhood}
          </div>
          <div>
            <span className="font-semibold">Ciudad:</span> {data.city}
          </div>
          <div>
            <span className="font-semibold">Estado:</span> {data.state}
          </div>
          <div>
            <span className="font-semibold">Código postal:</span> {data.zipCode}
          </div>
          <div>
            <span className="font-semibold">País:</span> {data.country}
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <span className="font-semibold">Contacto principal:</span> {data.contactName}
          </div>
          <div>
            <span className="font-semibold">Puesto:</span> {data.contactPosition}
          </div>
          <div>
            <span className="font-semibold">Teléfono:</span> {data.contactPhone}
          </div>
          <div>
            <span className="font-semibold">Email:</span> {data.contactEmail}
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <span className="font-semibold">Giro:</span> {data.industry}
          </div>
          <div>
            <span className="font-semibold">Área:</span> {data.companyArea}
          </div>
          <div>
            <span className="font-semibold">Tamaño:</span> {data.companySize}
          </div>
          <div>
            <span className="font-semibold">Tipo de cliente:</span> {data.clientType}
          </div>
          <div>
            <span className="font-semibold">Límite de crédito:</span> {data.creditLimit}
          </div>
          <div>
            <span className="font-semibold">Días de crédito:</span> {data.creditDays}
          </div>
          <div>
            <span className="font-semibold">Forma de pago:</span> {data.preferredPayment}
          </div>
        </div>
        <div className="mb-4">
          <span className="font-semibold">Sitio web:</span> {data.website}
        </div>
        <div className="mb-4">
          <span className="font-semibold">Observaciones:</span> {data.observations}
        </div>
        <div className="mb-4">
          <span className="font-semibold">Vendedor asignado:</span> {data.assignedSalespersonId}
        </div>
        <div className="mb-4">
          <span className="font-semibold">Estatus:</span> {data.status === "active" ? "Activo" : "Inactivo"}
        </div>
        {/* Historial de cotizaciones */}
        <ClientQuotationsTable clientId={clientId} />
      </div>
    </PageGuard>
  );
}

export function ClienteResumenPage() {
  return (
    <div className="p-8 text-center text-gray-600">
      <h2 className="text-2xl font-bold mb-4">Resumen del Cliente</h2>
      <p>Selecciona una pestaña para ver la información detallada del cliente.</p>
    </div>
  );
}