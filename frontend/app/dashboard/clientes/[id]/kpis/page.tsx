"use client";
import { useParams } from "next/navigation";
import { useClientKpis } from "@/hooks/use-client";
import { Loader2, AlertCircle, BarChart3 } from "lucide-react";
import { Card } from "@/components/ui/card";

export default function KPIsClientePage() {
  const params = useParams();
  // El id puede venir como string o array (Next.js 13+)
  const clientIdRaw = params?.id;
  const clientId = Array.isArray(clientIdRaw) ? clientIdRaw[0] : clientIdRaw;
  if (!clientId) {
    return <div className="p-8 text-center text-red-600">ID de cliente no especificado</div>;
  }
  const { data: kpis, isLoading, error } = useClientKpis(clientId);

  return (
    <div className="p-8 max-w-3xl mx-auto space-y-8">
      <h2 className="text-2xl font-bold mb-4 flex items-center gap-2"><BarChart3 className="w-6 h-6" />Indicadores y KPIs de Cliente</h2>
      {isLoading ? (
        <div className="flex items-center gap-2"><Loader2 className="animate-spin" />Cargando KPIs...</div>
      ) : error ? (
        <div className="flex items-center gap-2 text-red-600"><AlertCircle />Error al cargar KPIs</div>
      ) : !kpis ? (
        <div className="text-muted-foreground">Sin datos de KPIs</div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="p-4 flex flex-col items-center">
            <span className="text-3xl font-bold">${kpis.totalSpent}</span>
            <span className="text-muted-foreground mt-1">Total gastado</span>
          </Card>
          <Card className="p-4 flex flex-col items-center">
            <span className="text-3xl font-bold">{kpis.purchasesThisYear}</span>
            <span className="text-muted-foreground mt-1">Compras este año</span>
          </Card>
          <Card className="p-4 flex flex-col items-center">
            <span className="text-3xl font-bold">${kpis.averagePurchase}</span>
            <span className="text-muted-foreground mt-1">Ticket promedio</span>
          </Card>
        </div>
      )}
      {/* Gráfico de barras simple (placeholder) */}
      <div className="mt-8">
        <h3 className="text-lg font-semibold mb-2">Histórico de compras (demo)</h3>
        <div className="bg-muted rounded h-32 flex items-end gap-2 p-4">
          {/* Placeholder de barras */}
          {[1200, 800, 1500, 900, 2000, 1100].map((val, i) => (
            <div key={i} className="bg-primary/70 w-8 rounded-t" style={{ height: `${val / 25}px` }}>
              <span className="block text-xs text-center mt-1">{val}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export function ClienteKPIsPlaceholder() {
  return (
    <div className="p-8 text-center text-gray-600">
      <h2 className="text-2xl font-bold mb-4">KPIs del Cliente</h2>
      <p>Aquí se mostrarán los indicadores clave de desempeño de este cliente.</p>
    </div>
  );
} 