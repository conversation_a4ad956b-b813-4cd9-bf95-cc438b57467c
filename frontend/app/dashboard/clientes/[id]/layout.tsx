"use client";
import { ReactNode } from "react";
import Link from "next/link";
import { useParams, usePathname } from "next/navigation";

const tabs = [
  { label: "Resumen", path: "" },
  { label: "Documentos", path: "documentos" },
  { label: "KPIs", path: "kpis" },
  { label: "Categorización", path: "categorizacion" },
  { label: "Exportar", path: "exportar" },
];

export default function ClienteLayout({ children }: { children: ReactNode }) {
  const params = useParams();
  const pathname = usePathname();
  const clientId = Array.isArray(params.id) ? params.id[0] : params.id;

  const shouldShowTabs = !pathname.endsWith("/documentos");

  return (
    <div className="p-4">
      {shouldShowTabs && (
        <div className="flex gap-2 border-b mb-6">
          {tabs.map((tab) => {
            const href = `/dashboard/clientes/${clientId}${tab.path ? "/" + tab.path : ""}`;
            const isActive = pathname === href || (tab.path === "" && pathname === `/dashboard/clientes/${clientId}`);
            return (
              <Link
                key={tab.path}
                href={href}
                className={`px-4 py-2 border-b-2 ${isActive ? "border-blue-600 font-bold" : "border-transparent text-gray-500"}`}
              >
                {tab.label}
              </Link>
            );
          })}
        </div>
      )}
      <div>{children}</div>
    </div>
  );
} 