"use client"

import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { useClient, useUpdateClient } from "@/hooks/use-client";
import { PageGuard } from "@/components/auth/page-guard";
import { ClientForm, ClientPayload } from '@/components/clientes/ClientForm';

const initialForm = {
  commercialName: "",
  legalName: "",
  rfc: "",
  taxRegime: "",
  address: "",
  neighborhood: "",
  city: "",
  state: "",
  zipCode: "",
  country: "México",
  contactName: "",
  contactPosition: "",
  contactEmail: "",
  contactPhone: "",
  industry: "",
  companyArea: "",
  companySize: "",
  clientType: "Prospecto",
  creditLimit: 0,
  creditDays: 0,
  preferredPayment: "",
  website: "",
  observations: "",
  assignedSalespersonId: "",
  status: "active",
};

const clientTypes = ["Prospecto", "Cliente", "Frecuente"];
const statuses = ["Activo", "Inactivo", "Suspendido", "Temporal"];

export default function EditarClientePage() {
  const router = useRouter();
  const params = useParams();
  const clientId = typeof params?.id === 'string' || typeof params?.id === 'number' ? params.id : '';
  const { data, isLoading, error } = useClient(clientId);
  const updateClient = useUpdateClient();

  const handleSubmit = async (payload: ClientPayload) => {
    console.log('[DEBUG][EditarClientePage] Payload enviado:', payload);
    updateClient.mutate({ id: clientId, data: payload }, {
      onSuccess: (updated: any) => {
        console.log('[DEBUG][EditarClientePage] Cliente actualizado:', updated);
        router.push(`/dashboard/clientes/${clientId}`);
      },
      onError: (err: any) => {
        console.error('[DEBUG][EditarClientePage] Error al actualizar cliente:', err);
      },
    });
  };

  if (isLoading) return <div className="py-10 text-center">Cargando...</div>;
  if (error) return <div className="py-10 text-center text-red-500">Error al cargar cliente</div>;

  return (
    <PageGuard 
      requiredPermissions={["clientes:update"]}
      unauthorizedTitle="Editar Cliente - Acceso Restringido"
      unauthorizedMessage="Necesitas permisos de edición de clientes para acceder a esta sección."
    >
      <div className="max-w-3xl mx-auto py-8">
        <h1 className="text-2xl font-bold mb-6">Editar cliente</h1>
        <ClientForm onSubmit={handleSubmit} defaultValues={data} loading={updateClient.status === 'pending' || updateClient.isPending} />
      </div>
    </PageGuard>
  );
}