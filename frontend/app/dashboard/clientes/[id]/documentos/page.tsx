"use client";
import { useParams, useRouter } from "next/navigation";
import { useClientDocuments, useUploadClientDocument, useDeleteClientDocument } from "@/hooks/use-client";
import { Loader2, AlertCircle, FileText, Upload, Trash2 } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useRef, useEffect } from "react";

export default function DocumentosClientePage() {
  const params = useParams();
  const router = useRouter();
  const clientIdRaw = params?.id;
  const clientId = typeof clientIdRaw === 'string' ? clientIdRaw : (Array.isArray(clientIdRaw) ? clientIdRaw[0] : undefined);
  console.log('[DEBUG][DocumentosClientePage] clientId:', clientId, 'params:', params);
  if (!clientId || clientId === 'undefined' || clientId === '' || clientId === '__invalid__') {
    useEffect(() => { router.push('/dashboard/clientes'); }, [router]);
    return null;
  }
  const { data: documentos = [], isLoading, error, refetch } = useClientDocuments(clientId);
  const uploadDoc = useUploadClientDocument(clientId);
  const deleteDoc = useDeleteClientDocument(clientId);
  const fileInputRef = useRef<HTMLInputElement>(null);

  if (isLoading) {
    return <div className="flex items-center justify-center p-8"><Loader2 className="animate-spin mr-2" />Cargando documentos...</div>;
  }

  if (error) {
    return <div className="flex flex-col items-center justify-center p-8"><AlertCircle className="w-8 h-8 text-red-500 mb-2" /><p className="text-red-600">Error al cargar documentos</p></div>;
  }

  if (!documentos || !Array.isArray(documentos) || documentos.length === 0) {
    return <div className="p-8 text-center text-gray-500">No hay documentos para este cliente.</div>;
  }

  const handleUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const formData = new FormData();
    formData.append("file", file);
    try {
      await uploadDoc.mutateAsync(formData);
      refetch();
    } catch {}
  };

  const handleDelete = async (docId: string) => {
    try {
      await deleteDoc.mutateAsync(docId);
      refetch();
    } catch {}
  };

  return (
    <div className="p-8 max-w-3xl mx-auto space-y-8">
      <h2 className="text-2xl font-bold mb-4 flex items-center gap-2"><FileText className="w-6 h-6" />Documentos del Cliente</h2>
      <div className="flex items-center gap-4 mb-4">
        <Button
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
          disabled={uploadDoc.status === 'pending'}
        >
          <Upload className="w-4 h-4 mr-2" />Subir documento
        </Button>
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          onChange={handleUpload}
          accept="application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,image/*"
        />
        {uploadDoc.status === 'pending' && <Loader2 className="animate-spin w-4 h-4 ml-2" />}
      </div>
      <Card className="p-0 overflow-x-auto">
        <table className="min-w-full border text-sm">
          <thead>
            <tr className="bg-muted">
              <th className="px-2 py-1">Nombre</th>
              <th className="px-2 py-1">Tipo</th>
              <th className="px-2 py-1">Fecha</th>
              <th className="px-2 py-1">Acciones</th>
            </tr>
          </thead>
          <tbody>
            {Array.isArray(documentos) && documentos.map((doc: any) => (
              <tr key={doc.id} className="border-b">
                <td className="px-2 py-1">{doc.filename || doc.name}</td>
                <td className="px-2 py-1">{doc.file_type || doc.type}</td>
                <td className="px-2 py-1">{doc.created_at ? new Date(doc.created_at).toLocaleDateString() : (doc.createdAt ? new Date(doc.createdAt).toLocaleDateString() : "")}</td>
                <td className="px-2 py-1 flex gap-2">
                  {doc.url && (
                    <a href={doc.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 underline">Ver</a>
                  )}
                  <Button size="sm" variant="destructive" onClick={() => handleDelete(doc.id)} disabled={deleteDoc.status === 'pending'}>
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </Card>
      {deleteDoc.status === 'pending' && <div className="flex items-center gap-2 text-muted-foreground"><Loader2 className="animate-spin w-4 h-4" />Eliminando documento...</div>}
      {uploadDoc.status === 'error' && <div className="flex items-center gap-2 text-red-600 mt-2"><AlertCircle />Error al subir documento</div>}
      {deleteDoc.status === 'error' && <div className="flex items-center gap-2 text-red-600 mt-2"><AlertCircle />Error al eliminar documento</div>}
    </div>
  );
}

export function ClienteDocumentosPlaceholder() {
  return (
    <div className="p-8 text-center text-gray-600">
      <h2 className="text-2xl font-bold mb-4">Documentos del Cliente</h2>
      <p>Aquí podrás ver y gestionar los documentos asociados a este cliente.</p>
    </div>
  );
} 