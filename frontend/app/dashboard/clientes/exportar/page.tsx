"use client";
import { useClients } from "@/hooks/use-client";
import { Loader2, AlertCircle, Download } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/button";

export default function ExportarClientesPage() {
  const { data, isLoading, error } = useClients();
  const [selectedFields, setSelectedFields] = useState<string[]>(["id", "commercial_name", "rfc", "status"]);

  const allFields = [
    { key: "id", label: "ID" },
    { key: "commercial_name", label: "Nombre Comercial" },
    { key: "legal_name", label: "Razón Social" },
    { key: "rfc", label: "RFC" },
    { key: "status", label: "Estatus" },
    { key: "city", label: "Ciudad" },
    { key: "state", label: "Estado" },
    { key: "created_at", label: "Fecha Alta" },
  ];

  const handleFieldToggle = (key: string) => {
    setSelectedFields((prev) =>
      prev.includes(key) ? prev.filter((f) => f !== key) : [...prev, key]
    );
  };

  const exportToCSV = () => {
    if (!data?.clients?.length) return;
    const rows = data.clients.map((c: any) =>
      selectedFields.map((f) => c[f])
    );
    const csv = [
      selectedFields.map((f) => allFields.find((af) => af.key === f)?.label || f).join(","),
      ...rows.map((row) => row.map((cell) => `"${cell ?? ""}"`).join(",")),
    ].join("\n");
    const blob = new Blob([csv], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "clientes.csv";
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="p-8 max-w-4xl mx-auto space-y-8">
      <h2 className="text-2xl font-bold mb-4">Exportar Clientes</h2>
      {/* Selección de campos */}
      <div className="mb-4 flex flex-wrap gap-2">
        {allFields.map((field) => (
          <label key={field.key} className="flex items-center gap-1 text-sm border rounded px-2 py-1 cursor-pointer">
            <input
              type="checkbox"
              checked={selectedFields.includes(field.key)}
              onChange={() => handleFieldToggle(field.key)}
            />
            {field.label}
          </label>
        ))}
      </div>
      <Button onClick={exportToCSV} disabled={!data?.clients?.length}>
        <Download className="w-4 h-4 mr-2" />Exportar a CSV
      </Button>
      {/* Tabla de clientes */}
      {isLoading ? (
        <div className="flex items-center gap-2"><Loader2 className="animate-spin" />Cargando clientes...</div>
      ) : error ? (
        <div className="flex items-center gap-2 text-red-600"><AlertCircle />Error al cargar clientes</div>
      ) : data?.clients?.length === 0 ? (
        <div className="text-muted-foreground">Sin clientes registrados</div>
      ) : (
        <div className="overflow-x-auto mt-4">
          <table className="min-w-full border text-sm">
            <thead>
              <tr className="bg-muted">
                {selectedFields.map((f) => (
                  <th key={f} className="px-2 py-1">{allFields.find((af) => af.key === f)?.label || f}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              {(data?.clients ?? []).map((c: any) => (
                <tr key={c.id} className="border-b">
                  {selectedFields.map((f) => (
                    <td key={f} className="px-2 py-1">{c[f]}</td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
} 