"use client";
import { useClient, useUpdateClient } from "@/hooks/use-client";
import { Loader2, <PERSON>ertCircle, CheckCircle2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";

// Demo: id de cliente fijo (en real, obtén de router o props)
const CLIENT_ID = 1;

const campos: { name: "segment" | "type" | "area" | "size" | "industry"; label: string }[] = [
  { name: "segment", label: "Segmento" },
  { name: "type", label: "Tipo de cliente" },
  { name: "area", label: "Área" },
  { name: "size", label: "Tamaño" },
  { name: "industry", label: "Giro" },
];

export default function CategorizacionClientePage() {
  const { data: cliente, isLoading, error } = useClient(CLIENT_ID);
  const updateClient = useUpdateClient();
  const [success, setSuccess] = useState(false);

  const form = useForm({
    defaultValues: {
      segment: "",
      type: "",
      area: "",
      size: "",
      industry: "",
    },
  });

  useEffect(() => {
    if (cliente) {
      form.reset({
        segment: cliente.segment || "",
        type: cliente.type || "",
        area: cliente.area || "",
        size: cliente.size || "",
        industry: cliente.industry || "",
      });
    }
  }, [cliente]);

  const onSubmit = async (data: any) => {
    try {
      await updateClient.mutateAsync({ id: CLIENT_ID, data });
      setSuccess(true);
      setTimeout(() => setSuccess(false), 2000);
    } catch {}
  };

  return (
    <div className="p-8 max-w-xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">Categorización y Segmentos</h2>
      {isLoading ? (
        <div className="flex items-center gap-2"><Loader2 className="animate-spin" />Cargando datos...</div>
      ) : error ? (
        <div className="flex items-center gap-2 text-red-600"><AlertCircle />Error al cargar datos</div>
      ) : (
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          {campos.map((campo) => (
            <div key={campo.name} className="flex flex-col">
              <label className="font-medium mb-1">{campo.label}</label>
              <input
                className="border rounded px-2 py-1"
                {...form.register(campo.name)}
                placeholder={`Selecciona o escribe el ${campo.label.toLowerCase()}`}
              />
            </div>
          ))}
          <Button type="submit" disabled={updateClient.status === 'pending'}>
            {updateClient.status === 'pending' ? <Loader2 className="animate-spin w-4 h-4" /> : "Guardar cambios"}
          </Button>
          {success && (
            <div className="flex items-center gap-2 text-green-600 mt-2"><CheckCircle2 />Guardado correctamente</div>
          )}
          {updateClient.status === 'error' && (
            <div className="flex items-center gap-2 text-red-600 mt-2"><AlertCircle />Error al guardar cambios</div>
          )}
        </form>
      )}
    </div>
  );
} 