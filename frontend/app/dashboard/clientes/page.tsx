"use client";
import { PageGuard } from "@/components/auth/page-guard";
import { useClients } from "@/hooks/use-client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

export default function ClientesPage() {
  const router = useRouter();
  const [filters, setFilters] = useState({ search: "", rfc: "", status: "" });
  const [page, setPage] = useState(1);
  const { data, isLoading, error } = useClients({ ...filters, page });

  // Reset page to 1 on filter change
  const handleFilterChange = (field: string, value: string) => {
    setFilters(f => ({ ...f, [field]: value }));
    setPage(1);
  };

  return (
    <div className="space-y-6">
      <section className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
        <div>
          <h1 className="text-2xl font-bold mb-1">Mis Clientes</h1>
          <p className="text-sm text-muted-foreground">Gestión de clientes, historial y categorización.</p>
        </div>
      </section>
      <Card className="shadow-sm">
        <CardHeader className="pb-2">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
            <div className="flex gap-2 w-full md:w-auto">
              <Input
                placeholder="Buscar por nombre..."
                value={filters.search}
                onChange={e => handleFilterChange("search", e.target.value)}
                className="max-w-xs"
              />
              <Input
                placeholder="RFC..."
                value={filters.rfc}
                onChange={e => handleFilterChange("rfc", e.target.value)}
                className="max-w-xs"
              />
              <select
                className="input input-bordered"
                value={filters.status}
                onChange={e => handleFilterChange("status", e.target.value)}
              >
                <option value="">Todos</option>
                <option value="active">Activo</option>
                <option value="inactive">Inactivo</option>
              </select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="py-10 text-center text-muted-foreground">Cargando...</div>
          ) : error ? (
            <div className="py-10 text-center text-destructive">
              Error al cargar clientes. Por favor intente de nuevo.<br />
              <span className="text-xs">{error.message || String(error)}</span>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nombre comercial</TableHead>
                    <TableHead>Razón social</TableHead>
                    <TableHead>RFC</TableHead>
                    <TableHead>Contacto</TableHead>
                    <TableHead>Estatus</TableHead>
                    <TableHead className="text-right">Acciones</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data?.clients?.map((c: any) => (
                    <TableRow key={c.id} className="hover:bg-muted/50">
                      <TableCell>{c.commercialName}</TableCell>
                      <TableCell>{c.legalName}</TableCell>
                      <TableCell>{c.rfc}</TableCell>
                      <TableCell>{c.contactName}</TableCell>
                      <TableCell>
                        <Badge variant={c.status === "active" ? "outline" : "secondary"}>
                          {c.status === "active" ? "Activo" : "Inactivo"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="icon" onClick={() => router.push(`/dashboard/clientes/${c.id}`)}>
                          Ver
                        </Button>
                        <Button variant="ghost" size="icon" onClick={() => router.push(`/dashboard/clientes/${c.id}/editar`)}>
                          Editar
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              <div className="flex justify-end mt-4 gap-2">
                <Button size="sm" variant="outline" disabled={page <= 1} onClick={() => setPage(p => Math.max(1, p - 1))}>Anterior</Button>
                <span className="px-2">Página {data?.page} de {data?.totalPages}</span>
                <Button size="sm" variant="outline" disabled={page >= (data?.totalPages ?? 1)} onClick={() => setPage(p => p + 1)}>Siguiente</Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}