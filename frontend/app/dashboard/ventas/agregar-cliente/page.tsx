import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export default function AgregarClientePage() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Agregar Nuevo Cliente</CardTitle>
      </CardHeader>
      <CardContent>
        <form className="space-y-4">
          <div>
            <Label htmlFor="nombre-comercial">Nombre Comercial</Label>
            <Input id="nombre-comercial" placeholder="Nombre Comercial" />
          </div>
          <div>
            <Label htmlFor="rfc">RFC</Label>
            <Input id="rfc" placeholder="RFC" />
          </div>
          <Button type="submit">Guardar Cliente</Button>
        </form>
      </CardContent>
    </Card>
  );
}