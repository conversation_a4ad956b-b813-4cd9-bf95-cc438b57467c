import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

const fichas = [
  {
    id: 1,
    nombre: 'Ficha Técnica Manómetro Digital MD-001',
    fecha: '2023-10-26',
  },
  {
    id: 2,
    nombre: 'Ficha Técnica Termómetro TI-002',
    fecha: '2023-10-25',
  },
];

export default function FichasTecnicasPage() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Fichas Técnicas</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-center mb-4">
          <Input type="file" className="max-w-sm" />
          <Button>Subir Ficha</Button>
        </div>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>Nombre</TableHead>
              <TableHead>Fecha de Subida</TableHead>
              <TableHead>Acciones</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {fichas.map((ficha) => (
              <TableRow key={ficha.id}>
                <TableCell>{ficha.id}</TableCell>
                <TableCell>{ficha.nombre}</TableCell>
                <TableCell>{ficha.fecha}</TableCell>
                <TableCell>
                  <Button variant="outline" size="sm">
                    Descargar
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}