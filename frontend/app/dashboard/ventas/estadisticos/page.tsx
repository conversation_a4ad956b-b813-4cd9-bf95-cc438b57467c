'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON>ontainer, <PERSON><PERSON><PERSON><PERSON>, YAxis } from 'recharts';

const data = [
  { name: '<PERSON><PERSON>', total: Math.floor(Math.random() * 5000) + 1000 },
  { name: '<PERSON><PERSON><PERSON>', total: Math.floor(Math.random() * 5000) + 1000 },
  { name: '<PERSON><PERSON>', total: Math.floor(Math.random() * 5000) + 1000 },
  { name: 'Abril', total: Math.floor(Math.random() * 5000) + 1000 },
  { name: '<PERSON>', total: Math.floor(Math.random() * 5000) + 1000 },
  { name: '<PERSON><PERSON>', total: Math.floor(Math.random() * 5000) + 1000 },
];

export default function EstadisticosVentasPage() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Estadísticos de Ventas</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={350}>
          <BarChart data={data}>
            <XAxis
              dataKey="name"
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />
            <YAxis
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => `$${value}`}
            />
            <Bar dataKey="total" fill="#3b82f6" radius={[4, 4, 0, 0]} />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}