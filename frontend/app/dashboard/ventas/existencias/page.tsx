import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

const productos = [
  {
    id: 1,
    nombre: 'Manómetro Digital',
    marca: 'Marca A',
    codigo: 'MD-001',
    stock: 15,
  },
  {
    id: 2,
    nombre: 'Termómetro de Infrarrojos',
    marca: 'Marca B',
    codigo: 'TI-002',
    stock: 8,
  },
  {
    id: 3,
    nombre: 'Balanza de Precisión',
    marca: 'Marca C',
    codigo: 'BP-003',
    stock: 2,
  },
];

export default function ExistenciasPage() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Existencias de Productos</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-center mb-4">
          <Input placeholder="Filtrar por marca, código, nombre..." className="max-w-sm" />
        </div>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>Nombre</TableHead>
              <TableHead>Marca</TableHead>
              <TableHead>Código</TableHead>
              <TableHead>Stock</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {productos.map((producto) => (
              <TableRow key={producto.id}>
                <TableCell>{producto.id}</TableCell>
                <TableCell>{producto.nombre}</TableCell>
                <TableCell>{producto.marca}</TableCell>
                <TableCell>{producto.codigo}</TableCell>
                <TableCell>{producto.stock}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}