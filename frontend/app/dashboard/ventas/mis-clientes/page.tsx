import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

const clientes = [
  {
    id: 1,
    nombre: 'Cliente A',
    ubicacion: 'Guadalajara',
    estado: 'Activo',
  },
  {
    id: 2,
    nombre: 'Cliente B',
    ubicacion: 'Monterrey',
    estado: 'Activo',
  },
  {
    id: 3,
    nombre: 'Cliente C',
    ubicacion: 'Ciudad de México',
    estado: 'Inactivo',
  },
];

export default function MisClientesPage() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Mis Clientes</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-center mb-4">
          <Input placeholder="Filtrar por nombre, ubicación, estado..." className="max-w-sm" />
        </div>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>Nombre</TableHead>
              <TableHead>Ubicación</TableHead>
              <TableHead>Estado</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {clientes.map((cliente) => (
              <TableRow key={cliente.id}>
                <TableCell>{cliente.id}</TableCell>
                <TableCell>{cliente.nombre}</TableCell>
                <TableCell>{cliente.ubicacion}</TableCell>
                <TableCell>{cliente.estado}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}