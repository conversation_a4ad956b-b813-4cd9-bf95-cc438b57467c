import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

export default function BorradorCotizacionPage() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Borrador de Cotización</CardTitle>
      </CardHeader>
      <CardContent>
        <form className="space-y-4">
          <div>
            <Label htmlFor="cliente">Cliente</Label>
            <Input id="cliente" placeholder="Seleccionar cliente" />
          </div>
          <div>
            <Label htmlFor="items">Items</Label>
            <Textarea id="items" placeholder="Agregar items a la cotización" />
          </div>
          <Button type="submit">Guardar Borrador</Button>
        </form>
      </CardContent>
    </Card>
  );
}