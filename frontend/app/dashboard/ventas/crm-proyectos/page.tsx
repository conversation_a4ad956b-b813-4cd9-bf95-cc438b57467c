import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const proyectos = [
  { id: 1, nombre: 'Proyecto Alpha', etapa: 'Cotización', progreso: 25 },
  { id: 2, nombre: 'Proyecto Beta', etapa: 'Negociación', progreso: 50 },
  { id: 3, nombre: 'Proyecto Gamma', etapa: 'Cerrado Ganado', progreso: 100 },
  { id: 4, nombre: 'Proyecto Delta', etapa: 'Cerrado Perdido', progreso: 100 },
];

export default function CrmProyectosPage() {
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Proceso de Proyectos (CRM)</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {proyectos.map((proyecto) => (
          <Card key={proyecto.id}>
            <CardHeader>
              <CardTitle>{proyecto.nombre}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">{proyecto.etapa}</p>
              <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mt-2">
                <div
                  className="bg-blue-600 h-2.5 rounded-full"
                  style={{ width: `${proyecto.progreso}%` }}
                ></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}