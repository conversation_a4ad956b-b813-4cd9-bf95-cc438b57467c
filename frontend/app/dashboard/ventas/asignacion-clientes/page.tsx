import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export default function AsignacionClientesPage() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Asignación de Clientes</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <label htmlFor="cliente" className="block text-sm font-medium text-gray-700">
              Cliente
            </label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Seleccionar cliente" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">Cliente A</SelectItem>
                <SelectItem value="2">Cliente B</SelectItem>
                <SelectItem value="3">Cliente C</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <label htmlFor="vendedor" className="block text-sm font-medium text-gray-700">
              Vendedor
            </label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Seleccionar vendedor" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">Vendedor 1</SelectItem>
                <SelectItem value="2">Vendedor 2</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button>Asignar Cliente</Button>
        </div>
      </CardContent>
    </Card>
  );
}