import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

export default function CotizarPage() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Crear Nueva Cotización</CardTitle>
      </CardHeader>
      <CardContent>
        <form className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="cliente">Cliente</Label>
              <Input id="cliente" placeholder="Buscar cliente por nombre o RFC" />
            </div>
            <div>
              <Label htmlFor="prospecto">O agregar nuevo prospecto</Label>
              <Input id="prospecto" placeholder="Nombre del prospecto" />
            </div>
          </div>
          <div>
            <Label>Items de la Cotización</Label>
            {/* Aquí iría un componente más complejo para agregar items */}
            <div className="border rounded-lg p-4 space-y-2">
              <p className="text-sm text-muted-foreground">
                Arrastra productos aquí o búscalos para agregarlos.
              </p>
            </div>
          </div>
          <div>
            <Label htmlFor="comentarios">Comentarios Adicionales</Label>
            <Textarea id="comentarios" placeholder="Añadir comentarios o notas" />
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline">Guardar Borrador</Button>
            <Button type="submit">Generar Cotización PDF</Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}