"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { VisibleCheckbox } from "@/components/ui/visible-checkbox"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Calendar } from "@/components/ui/calendar"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, Search, Plus, Edit, Trash2, Save, X } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { DashboardLayout } from "@/app/dashboard/layout"

const sampleData = [
  { id: 1, name: "Juan Pérez", email: "<EMAIL>", role: "Admin", status: "Active" },
  { id: 2, name: "María García", email: "<EMAIL>", role: "User", status: "Inactive" },
  { id: 3, name: "Carlos López", email: "<EMAIL>", role: "Editor", status: "Active" },
  { id: 4, name: "Ana Martínez", email: "<EMAIL>", role: "User", status: "Active" },
]

export default function FormTemplate() {
  const [date, setDate] = useState<Date>()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedRole, setSelectedRole] = useState("All Roles")
  const [notifications, setNotifications] = useState(true)
  const [marketing, setMarketing] = useState(false)
  const [priority, setPriority] = useState("")
  const [selectedItems, setSelectedItems] = useState<number[]>([])

  const filteredData = sampleData.filter(
    (item) =>
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
      (selectedRole === "All Roles" || item.role === selectedRole),
  )

  const handleSelectAll = () => {
    if (selectedItems.length === filteredData.length) {
      setSelectedItems([])
    } else {
      setSelectedItems(filteredData.map((item) => item.id))
    }
  }

  const handleSelectItem = (id: number) => {
    setSelectedItems((prev) => (prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]))
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-slate-100">Form Template</h1>
            <p className="text-gray-600 dark:text-slate-400 mt-1">
              Comprehensive form components and data table example
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button size="sm" className="bg-amber-500 hover:bg-amber-600 dark:bg-slate-600 dark:hover:bg-slate-700">
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
          </div>
        </div>

        <Tabs defaultValue="form" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-amber-100 dark:bg-slate-800">
            <TabsTrigger
              value="form"
              className="data-[state=active]:bg-amber-200 dark:data-[state=active]:bg-slate-700"
            >
              Form Components
            </TabsTrigger>
            <TabsTrigger
              value="data"
              className="data-[state=active]:bg-amber-200 dark:data-[state=active]:bg-slate-700"
            >
              Data Table
            </TabsTrigger>
            <TabsTrigger
              value="advanced"
              className="data-[state=active]:bg-amber-200 dark:data-[state=active]:bg-slate-700"
            >
              Advanced
            </TabsTrigger>
          </TabsList>

          <TabsContent value="form" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Basic Information */}
              <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-slate-100">Basic Information</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-slate-400">
                    Enter your personal details
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="firstName" className="text-gray-700 dark:text-slate-300">
                        First Name
                      </Label>
                      <Input
                        id="firstName"
                        placeholder="Enter first name"
                        className="bg-white/50 dark:bg-slate-700/50 border-gray-200 dark:border-slate-600"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName" className="text-gray-700 dark:text-slate-300">
                        Last Name
                      </Label>
                      <Input
                        id="lastName"
                        placeholder="Enter last name"
                        className="bg-white/50 dark:bg-slate-700/50 border-gray-200 dark:border-slate-600"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-gray-700 dark:text-slate-300">
                      Email Address
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter email address"
                      className="bg-white/50 dark:bg-slate-700/50 border-gray-200 dark:border-slate-600"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone" className="text-gray-700 dark:text-slate-300">
                      Phone Number
                    </Label>
                    <Input
                      id="phone"
                      type="tel"
                      placeholder="Enter phone number"
                      className="bg-white/50 dark:bg-slate-700/50 border-gray-200 dark:border-slate-600"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Preferences */}
              <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-slate-100">Preferences</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-slate-400">
                    Configure your account settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label className="text-gray-700 dark:text-slate-300">Department</Label>
                    <Select>
                      <SelectTrigger className="bg-white/50 dark:bg-slate-700/50 border-gray-200 dark:border-slate-600">
                        <SelectValue placeholder="Select department" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="hr">Human Resources</SelectItem>
                        <SelectItem value="it">Information Technology</SelectItem>
                        <SelectItem value="finance">Finance</SelectItem>
                        <SelectItem value="marketing">Marketing</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-gray-700 dark:text-slate-300">Priority Level</Label>
                    <RadioGroup value={priority} onValueChange={setPriority}>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="low" id="low" />
                        <Label htmlFor="low" className="text-gray-600 dark:text-slate-400">
                          Low
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="medium" id="medium" />
                        <Label htmlFor="medium" className="text-gray-600 dark:text-slate-400">
                          Medium
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="high" id="high" />
                        <Label htmlFor="high" className="text-gray-600 dark:text-slate-400">
                          High
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label className="text-gray-700 dark:text-slate-300">Email Notifications</Label>
                        <p className="text-sm text-gray-500 dark:text-slate-400">Receive notifications via email</p>
                      </div>
                      <Switch checked={notifications} onCheckedChange={setNotifications} />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label className="text-gray-700 dark:text-slate-300">Marketing Emails</Label>
                        <p className="text-sm text-gray-500 dark:text-slate-400">Receive marketing communications</p>
                      </div>
                      <Switch checked={marketing} onCheckedChange={setMarketing} />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Additional Information */}
              <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20 lg:col-span-2">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-slate-100">Additional Information</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-slate-400">
                    Provide additional details and preferences
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="startDate" className="text-gray-700 dark:text-slate-300">
                        Start Date
                      </Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal bg-white/50 dark:bg-slate-700/50 border-gray-200 dark:border-slate-600",
                              !date && "text-muted-foreground",
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {date ? format(date, "PPP") : <span>Pick a date</span>}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                        </PopoverContent>
                      </Popover>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="salary" className="text-gray-700 dark:text-slate-300">
                        Expected Salary
                      </Label>
                      <Input
                        id="salary"
                        type="number"
                        placeholder="Enter expected salary"
                        className="bg-white/50 dark:bg-slate-700/50 border-gray-200 dark:border-slate-600"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bio" className="text-gray-700 dark:text-slate-300">
                      Biography
                    </Label>
                    <Textarea
                      id="bio"
                      placeholder="Tell us about yourself..."
                      className="min-h-[100px] bg-white/50 dark:bg-slate-700/50 border-gray-200 dark:border-slate-600"
                    />
                  </div>
                  <div className="space-y-3">
                    <Label className="text-gray-700 dark:text-slate-300">Skills & Interests</Label>
                    <div className="grid grid-cols-2 gap-2">
                      {["JavaScript", "React", "Node.js", "Python", "Design", "Management"].map((skill) => (
                        <div key={skill} className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 dark:border-slate-700 bg-white/30 dark:bg-slate-800/30 hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors">
                          <Checkbox id={skill} />
                          <Label htmlFor={skill} className="text-sm font-medium text-gray-700 dark:text-slate-300 cursor-pointer">
                            {skill}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="data" className="space-y-6">
            <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
              <CardHeader>
                <CardTitle className="text-gray-900 dark:text-slate-100">User Management</CardTitle>
                <CardDescription className="text-gray-600 dark:text-slate-400">
                  Manage users and their permissions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col sm:flex-row gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search users..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 bg-white/50 dark:bg-slate-700/50 border-gray-200 dark:border-slate-600"
                    />
                  </div>
                  <Select value={selectedRole} onValueChange={setSelectedRole}>
                    <SelectTrigger className="w-full sm:w-[180px] bg-white/50 dark:bg-slate-700/50 border-gray-200 dark:border-slate-600">
                      <SelectValue placeholder="Filter by role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="All Roles">All Roles</SelectItem>
                      <SelectItem value="Admin">Admin</SelectItem>
                      <SelectItem value="User">User</SelectItem>
                      <SelectItem value="Editor">Editor</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button className="bg-amber-500 hover:bg-amber-600 dark:bg-slate-600 dark:hover:bg-slate-700">
                    <Plus className="h-4 w-4 mr-2" />
                    Add User
                  </Button>
                </div>

                <div className="rounded-md border border-gray-200 dark:border-slate-700 bg-white/30 dark:bg-slate-800/30">
                  <Table>
                    <TableHeader>
                      <TableRow className="border-gray-200 dark:border-slate-700">
                        <TableHead className="w-12">
                          <VisibleCheckbox
                            checked={selectedItems.length === filteredData.length && filteredData.length > 0}
                            onCheckedChange={handleSelectAll}
                          />
                        </TableHead>
                        <TableHead className="text-gray-700 dark:text-slate-300">Name</TableHead>
                        <TableHead className="text-gray-700 dark:text-slate-300">Email</TableHead>
                        <TableHead className="text-gray-700 dark:text-slate-300">Role</TableHead>
                        <TableHead className="text-gray-700 dark:text-slate-300">Status</TableHead>
                        <TableHead className="text-gray-700 dark:text-slate-300">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredData.map((user) => (
                        <TableRow key={user.id} className="border-gray-200 dark:border-slate-700">
                          <TableCell>
                            <VisibleCheckbox
                              checked={selectedItems.includes(user.id)}
                              onCheckedChange={() => handleSelectItem(user.id)}
                            />
                          </TableCell>
                          <TableCell className="font-medium text-gray-900 dark:text-slate-100">{user.name}</TableCell>
                          <TableCell className="text-gray-600 dark:text-slate-400">{user.email}</TableCell>
                          <TableCell>
                            <Badge
                              variant={user.role === "Admin" ? "default" : "secondary"}
                              className={
                                user.role === "Admin"
                                  ? "bg-amber-100 text-amber-800 dark:bg-slate-700 dark:text-slate-300"
                                  : "bg-gray-100 text-gray-800 dark:bg-slate-600 dark:text-slate-300"
                              }
                            >
                              {user.role}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={user.status === "Active" ? "default" : "secondary"}
                              className={
                                user.status === "Active"
                                  ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                                  : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                              }
                            >
                              {user.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              <Button variant="ghost" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {selectedItems.length > 0 && (
                  <div className="mt-4 p-3 bg-amber-50 dark:bg-slate-700 rounded-lg border border-amber-200 dark:border-slate-600">
                    <p className="text-sm text-amber-800 dark:text-slate-300">
                      {selectedItems.length} item(s) selected
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-6">
            <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
              <CardHeader>
                <CardTitle className="text-gray-900 dark:text-slate-100">Advanced Settings</CardTitle>
                <CardDescription className="text-gray-600 dark:text-slate-400">
                  Configure advanced system settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="text-center py-12">
                  <p className="text-gray-500 dark:text-slate-400">Advanced settings panel coming soon...</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
