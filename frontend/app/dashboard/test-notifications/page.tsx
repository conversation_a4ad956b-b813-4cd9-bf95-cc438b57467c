"use client"

import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useNotifications, useUserNotifications, useRoleNotifications } from '@/components/ui/notification-provider'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useAuth } from '@/components/providers/auth-provider'

export default function TestNotificationsPage() {
  const {
    addNotification,
    clearAll,
    notifySuccess,
    notifyError,
    notifyWarning,
    notifyInfo,
    notifyLogout
  } = useNotifications()

  const { user } = useAuth()
  
  const {
    notifyUserCreated,
    notifyUserUpdated,
    notifyUserActivated,
    notifyUserDeactivated,
    notifyUserBlocked,
    notifyUserDeleted,
    notifyPasswordChanged,
    notifyUserError
  } = useUserNotifications()

  const {
    notifyRoleCreated,
    notify<PERSON><PERSON>Updated,
    notifyRoleDeleted,
    notifyPermissionsAssigned,
    notify<PERSON>ole<PERSON>rror
  } = useRoleNotifications()

  const testUsers = [
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>'
  ]

  const testRoles = [
    'Administrador',
    'Editor',
    'Supervisor',
    'Operador',
    'Auditor'
  ]

  const getRandomUser = () => testUsers[Math.floor(Math.random() * testUsers.length)]
  const getRandomRole = () => testRoles[Math.floor(Math.random() * testRoles.length)]

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold tracking-tight mb-2">
          🎉 Sistema de Notificaciones Animadas
        </h1>
        <p className="text-muted-foreground">
          Prueba las notificaciones con efectos glassmorphism y animaciones modernas
        </p>
      </div>

      {/* Notificaciones Básicas */}
      <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🎨 Notificaciones Básicas
            <Badge variant="outline">Tipos Generales</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <Button 
              onClick={() => notifySuccess('¡Éxito!', 'Operación completada correctamente')}
              className="bg-green-500 hover:bg-green-600"
            >
              ✅ Éxito
            </Button>
            <Button 
              onClick={() => notifyError('Error', 'Algo salió mal, intenta nuevamente')}
              variant="destructive"
            >
              ❌ Error
            </Button>
            <Button 
              onClick={() => notifyWarning('Advertencia', 'Revisa esta información importante')}
              className="bg-orange-500 hover:bg-orange-600"
            >
              ⚠️ Advertencia
            </Button>
            <Button 
              onClick={() => notifyInfo('Información', 'Aquí tienes algunos datos útiles')}
              className="bg-blue-500 hover:bg-blue-600"
            >
              ℹ️ Info
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Notificaciones de Usuario */}
      <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            👥 Notificaciones de Usuario
            <Badge variant="outline">Acciones Específicas</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <Button
              onClick={() => notifyUserCreated(getRandomUser())}
              className="bg-green-500 hover:bg-green-600"
            >
              ➕ Usuario Creado
            </Button>
            <Button
              onClick={() => notifyUserUpdated(getRandomUser())}
              className="bg-blue-500 hover:bg-blue-600"
            >
              ✏️ Usuario Actualizado
            </Button>
            <Button
              onClick={() => notifyUserActivated(getRandomUser())}
              className="bg-green-600 hover:bg-green-700"
            >
              ✅ Usuario Activado
            </Button>
            <Button
              onClick={() => notifyUserDeactivated(getRandomUser())}
              className="bg-orange-500 hover:bg-orange-600"
            >
              ⏸️ Usuario Desactivado
            </Button>
            <Button
              onClick={() => notifyUserBlocked(getRandomUser())}
              className="bg-red-500 hover:bg-red-600"
            >
              🚫 Usuario Bloqueado
            </Button>
            <Button
              onClick={() => notifyUserDeleted(getRandomUser())}
              variant="destructive"
            >
              🗑️ Usuario Eliminado
            </Button>
            <Button
              onClick={() => notifyPasswordChanged(getRandomUser())}
              className="bg-purple-500 hover:bg-purple-600"
            >
              🔑 Contraseña Cambiada
            </Button>
            <Button
              onClick={() => notifyUserError(getRandomUser(), 'Error en la operación')}
              variant="destructive"
            >
              ⚠️ Error de Usuario
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Notificaciones de Roles */}
      <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🛡️ Notificaciones de Roles
            <Badge variant="outline">Gestión de Roles</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <Button
              onClick={() => notifyRoleCreated(getRandomRole())}
              className="bg-green-500 hover:bg-green-600"
            >
              ➕ Rol Creado
            </Button>
            <Button
              onClick={() => notifyRoleUpdated(getRandomRole())}
              className="bg-blue-500 hover:bg-blue-600"
            >
              ✏️ Rol Actualizado
            </Button>
            <Button
              onClick={() => notifyRoleDeleted(getRandomRole())}
              variant="destructive"
            >
              🗑️ Rol Eliminado
            </Button>
            <Button
              onClick={() => notifyPermissionsAssigned(getRandomRole())}
              className="bg-purple-500 hover:bg-purple-600"
            >
              🔐 Permisos Asignados
            </Button>
            <Button
              onClick={() => notifyRoleError(getRandomRole(), 'Error en la operación del rol')}
              variant="destructive"
            >
              ⚠️ Error de Rol
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Notificaciones con Posiciones */}
      <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            📍 Posiciones de Notificación
            <Badge variant="outline">Responsive</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            <Button 
              onClick={() => addNotification({
                type: 'info',
                title: 'Arriba Derecha',
                message: 'Posición por defecto en desktop',
                position: 'top-right'
              })}
              variant="outline"
            >
              ↗️ Top Right
            </Button>
            <Button 
              onClick={() => addNotification({
                type: 'info',
                title: 'Arriba Izquierda',
                message: 'Ideal para menús laterales',
                position: 'top-left'
              })}
              variant="outline"
            >
              ↖️ Top Left
            </Button>
            <Button 
              onClick={() => addNotification({
                type: 'info',
                title: 'Arriba Centro',
                message: 'Perfecto para móviles',
                position: 'top-center'
              })}
              variant="outline"
            >
              ⬆️ Top Center
            </Button>
            <Button 
              onClick={() => addNotification({
                type: 'success',
                title: 'Abajo Derecha',
                message: 'Clásica posición de confirmación',
                position: 'bottom-right'
              })}
              variant="outline"
            >
              ↘️ Bottom Right
            </Button>
            <Button 
              onClick={() => addNotification({
                type: 'success',
                title: 'Abajo Izquierda',
                message: 'Alternativa para confirmaciones',
                position: 'bottom-left'
              })}
              variant="outline"
            >
              ↙️ Bottom Left
            </Button>
            <Button 
              onClick={() => addNotification({
                type: 'success',
                title: 'Abajo Centro',
                message: 'Centrado en la parte inferior',
                position: 'bottom-center'
              })}
              variant="outline"
            >
              ⬇️ Bottom Center
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Controles */}
      <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
        <CardHeader>
          <CardTitle>🎛️ Controles</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button
              onClick={() => {
                // Crear múltiples notificaciones para probar el límite
                for (let i = 0; i < 8; i++) {
                  setTimeout(() => {
                    notifyInfo(`Notificación ${i + 1}`, `Esta es la notificación número ${i + 1}`)
                  }, i * 200)
                }
              }}
              variant="outline"
            >
              🔥 Spam Test (8 notificaciones)
            </Button>
            <Button
              onClick={clearAll}
              variant="destructive"
            >
              🧹 Limpiar Todas
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Prueba de Logout */}
      <Card className="bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-950/30 dark:to-orange-950/30 border border-red-200 dark:border-red-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🚪 Prueba de Logout
            <Badge variant="outline" className="text-red-600 border-red-300">Solo Notificación</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Prueba la notificación de logout sin cerrar realmente la sesión:
            </p>
            <Button
              onClick={() => notifyLogout(user?.name)}
              className="bg-red-500 hover:bg-red-600"
            >
              🔓 Probar Notificación de Logout
            </Button>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Nota: Esto solo muestra la notificación. Para probar el logout real, usa el botón del header.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Información */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 border border-blue-200 dark:border-blue-800">
        <CardContent className="pt-6">
          <div className="text-center space-y-2">
            <h3 className="font-semibold text-blue-900 dark:text-blue-100">
              ✨ Características del Sistema
            </h3>
            <div className="flex flex-wrap justify-center gap-2 text-sm">
              <Badge variant="secondary">🎨 Glassmorphism</Badge>
              <Badge variant="secondary">🎭 Animaciones Framer Motion</Badge>
              <Badge variant="secondary">📱 Responsive Design</Badge>
              <Badge variant="secondary">🎯 Auto-posicionamiento</Badge>
              <Badge variant="secondary">⏱️ Duración Personalizable</Badge>
              <Badge variant="secondary">🔄 Límite de Notificaciones</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
