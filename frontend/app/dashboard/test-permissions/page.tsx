import { PageGuard } from "@/components/auth/page-guard"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Shield, CheckCircle } from "lucide-react"

export default function TestPermissionsPage() {
  return (
    <PageGuard 
      requiredPermissions={["ADMIN_WRITE", "SUPER_SECRET_PERMISSION"]}
      unauthorizedTitle="Página de Prueba - Acceso Denegado"
      unauthorizedMessage="Esta es una página de prueba que requiere permisos especiales que probablemente no tienes."
    >
      <div className="p-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              ¡Acceso Concedido!
            </CardTitle>
            <CardDescription>
              Si puedes ver esta página, significa que tienes los permisos necesarios.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Shield className="w-4 h-4" />
                <span>Permisos requeridos: ADMIN_WRITE, SUPER_SECRET_PERMISSION</span>
              </div>
              <p className="text-gray-700">
                Esta página está protegida por el sistema de permisos. Solo usuarios con los permisos 
                específicos pueden acceder a ella. Si no tienes permisos, deberías haber visto la 
                página de "Acceso Denegado" con redirección automática.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </PageGuard>
  )
} 