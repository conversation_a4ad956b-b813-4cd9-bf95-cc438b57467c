import { AuditoriaView } from "@/components/sistemas/AuditoriaView";
import { PageGuard } from "@/components/auth/page-guard";

export default function AuditoriaPage() {
  return (
    <PageGuard 
      requiredPermissions={["sistemas:audit_logs:read"]}
      unauthorizedTitle="Auditoría del Sistema - Acceso Restringido"
      unauthorizedMessage="Necesitas permisos de auditoría del sistema para acceder a esta sección."
    >
      <AuditoriaView />
    </PageGuard>
  );
}