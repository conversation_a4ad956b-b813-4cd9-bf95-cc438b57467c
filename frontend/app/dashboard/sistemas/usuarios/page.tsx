import { GestionUsuariosView } from "@/components/sistemas/GestionUsuariosView";
import { PageGuard } from "@/components/auth/page-guard";

export default function UsuariosPage() {
  return (
    <PageGuard 
      requiredPermissions={["sistemas:users:read", "sistemas:users:create", "sistemas:users:update"]}
      unauthorizedTitle="Gestión de Usuarios - Acceso Restringido"
      unauthorizedMessage="Necesitas permisos de gestión de usuarios para acceder a esta sección."
    >
      <GestionUsuariosView />
    </PageGuard>
  );
}
