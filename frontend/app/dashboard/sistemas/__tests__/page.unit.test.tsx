import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AuditoriaPage from '../auditoria/page';
import BackupPage from '../backup/page';
import IntegracionesPage from '../integraciones/page';
import RolesPage from '../roles/page';
import UsuariosPage from '../usuarios/page';

jest.mock('@/components/auth/page-guard', () => ({
  PageGuard: ({ children }: any) => <>{children}</>
}));
jest.mock('@/hooks/usePagePermissions', () => ({
  usePagePermissions: () => ({ hasAccess: true })
}));
jest.mock('@/components/auth/unauthorized-page', () => ({
  UnauthorizedPage: () => <div>Acceso Restringido</div>
}));

// Mock ResizeObserver for recharts or similar libraries
beforeAll(() => {
  global.ResizeObserver =
    global.ResizeObserver ||
    class {
      observe() {}
      unobserve() {}
      disconnect() {}
    };
});

const queryClient = new QueryClient();
const renderWithQueryClient = (ui: React.ReactElement) =>
  render(<QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>);

describe('Sistemas - Páginas protegidas', () => {
  it('renderiza la página de auditoría', () => {
    renderWithQueryClient(<AuditoriaPage />);
    expect(screen.getByRole('heading', { name: /Auditoría del Sistema/i })).toBeInTheDocument();
  });
  it('renderiza la página de backup', () => {
    renderWithQueryClient(<BackupPage />);
    expect(screen.getByRole('heading', { name: /Backup y Restauración/i })).toBeInTheDocument();
  });
  it('renderiza la página de integraciones', () => {
    renderWithQueryClient(<IntegracionesPage />);
    expect(screen.getByRole('heading', { name: /Integraciones/i })).toBeInTheDocument();
  });
  it('renderiza la página de roles', () => {
    renderWithQueryClient(<RolesPage />);
    // When no roles, loading indicator is shown
    expect(screen.getByText(/Cargando roles/i)).toBeInTheDocument();
  });
  it('renderiza la página de usuarios', () => {
    renderWithQueryClient(<UsuariosPage />);
    expect(screen.getByRole('heading', { name: /Gestión de Usuarios/i })).toBeInTheDocument();
  });
});
