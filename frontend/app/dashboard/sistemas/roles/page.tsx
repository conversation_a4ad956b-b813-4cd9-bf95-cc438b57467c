import { RolesView } from "@/components/sistemas/RolesView";
import { PageGuard } from "@/components/auth/page-guard";

export default function RolesPage() {
  return (
    <PageGuard 
      requiredPermissions={["sistemas:roles:read", "sistemas:roles:create", "sistemas:roles:update"]}
      unauthorizedTitle="Gestión de Roles - Acceso Restringido"
      unauthorizedMessage="Necesitas permisos de gestión de roles para acceder a esta sección."
    >
      <RolesView />
    </PageGuard>
  );
}