"use client"

import { useState } from "react"
import { add, format, startOfMonth, getDay, eachDayOfInterval, endOfMonth, sub } from "date-fns"
import { es } from "date-fns/locale"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogClose } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { CalendarDays, PlusCircle, ChevronLeft, ChevronRight, Truck } from "lucide-react"

interface ServiceEvent {
  id: string
  date: Date
  client: string
  serviceType: string
  personnel: string
  status: "Programado" | "En Ruta" | "Completado"
}

// Mock data for service events
const initialServiceEvents: ServiceEvent[] = [
  {
    id: "1",
    date: new Date("2024-07-16T09:00:00"),
    client: "Industrias Acme S.A.",
    serviceType: "Calibración en Planta",
    personnel: "Carlos Rivas, Ana Solís",
    status: "Programado",
  },
  {
    id: "2",
    date: new Date("2024-07-18T11:00:00"),
    client: "Laboratorios Beta",
    serviceType: "Recolección de Equipos",
    personnel: "Luis Torres",
    status: "Programado",
  },
  {
    id: "3",
    date: new Date("2024-07-18T15:00:00"),
    client: "Constructora Delta",
    serviceType: "Mantenimiento Preventivo",
    personnel: "Miguel Hernández",
    status: "En Ruta",
  },
]

export default function LogisticaCalendarPage() {
  const [events, setEvents] = useState<ServiceEvent[]>(initialServiceEvents)
  const [currentDate, setCurrentDate] = useState(new Date())
  const [isFormOpen, setIsFormOpen] = useState(false)

  const firstDayOfMonth = startOfMonth(currentDate)
  const daysInMonth = eachDayOfInterval({ start: firstDayOfMonth, end: endOfMonth(currentDate) })
  const startingDayIndex = getDay(firstDayOfMonth) === 0 ? 6 : getDay(firstDayOfMonth) - 1

  const handlePrevMonth = () => setCurrentDate(sub(currentDate, { months: 1 }))
  const handleNextMonth = () => setCurrentDate(add(currentDate, { months: 1 }))

  const handleAddEvent = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    const newEvent: ServiceEvent = {
      id: `SRV-${Math.floor(Math.random() * 1000)}`,
      date: new Date(`${formData.get("date")}T${formData.get("time")}`),
      client: formData.get("client") as string,
      serviceType: formData.get("serviceType") as string,
      personnel: formData.get("personnel") as string,
      status: "Programado",
    }
    setEvents([...events, newEvent])
    setIsFormOpen(false)
    e.currentTarget.reset()
  }

  const getStatusClass = (status: ServiceEvent["status"]) => {
    switch (status) {
      case "Programado": return "bg-blue-100 text-blue-800"
      case "En Ruta": return "bg-yellow-100 text-yellow-800"
      case "Completado": return "bg-green-100 text-green-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center">
            <Truck className="h-6 w-6 mr-2 text-blue-600" />
            <div>
              <CardTitle>Calendario de Programación de Servicios</CardTitle>
              <CardDescription>Planificación y seguimiento de los servicios en campo y recolecciones.</CardDescription>
            </div>
          </div>
          <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
            <DialogTrigger asChild>
              <Button>
                <PlusCircle className="h-4 w-4 mr-2" />
                Programar Servicio
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Programar Nuevo Servicio</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleAddEvent} className="space-y-4">
                <div>
                  <Label htmlFor="client">Cliente</Label>
                  <Input id="client" name="client" placeholder="Nombre del cliente" required />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="date">Fecha</Label>
                    <Input id="date" name="date" type="date" required />
                  </div>
                  <div>
                    <Label htmlFor="time">Hora</Label>
                    <Input id="time" name="time" type="time" required />
                  </div>
                </div>
                <div>
                  <Label htmlFor="serviceType">Tipo de Servicio</Label>
                  <Input id="serviceType" name="serviceType" placeholder="Ej: Calibración, Mantenimiento..." required />
                </div>
                <div>
                  <Label htmlFor="personnel">Personal Asignado</Label>
                  <Textarea id="personnel" name="personnel" placeholder="Nombres del personal técnico" required />
                </div>
                <DialogFooter>
                  <DialogClose asChild><Button type="button" variant="secondary">Cancelar</Button></DialogClose>
                  <Button type="submit">Programar</Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <Button variant="outline" size="icon" onClick={handlePrevMonth}><ChevronLeft className="h-4 w-4" /></Button>
            <h2 className="text-xl font-semibold capitalize">{format(currentDate, "MMMM yyyy", { locale: es })}</h2>
            <Button variant="outline" size="icon" onClick={handleNextMonth}><ChevronRight className="h-4 w-4" /></Button>
          </div>
          <div className="grid grid-cols-7 text-center font-semibold text-sm text-gray-600 border-b">
            {["Lun", "Mar", "Mié", "Jue", "Vie", "Sáb", "Dom"].map(day => <div key={day} className="py-2">{day}</div>)}
          </div>
          <div className="grid grid-cols-7 grid-rows-5">
            {Array.from({ length: startingDayIndex }).map((_, i) => <div key={`empty-${i}`} className="border-r border-b h-32"></div>)}
            {daysInMonth.map((day) => (
              <div key={day.toString()} className="border-r border-b h-32 p-1 relative overflow-hidden">
                <div className={`text-sm ${format(day, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd') ? 'bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center' : ''}`}>
                  {format(day, "d")}
                </div>
                <div className="mt-1 space-y-1 overflow-y-auto max-h-24">
                  {events
                    .filter(event => format(event.date, "yyyy-MM-dd") === format(day, "yyyy-MM-dd"))
                    .map(event => (
                      <div key={event.id} className={`p-1.5 rounded-md text-xs ${getStatusClass(event.status)}`}>
                        <p className="font-bold truncate">{event.client}</p>
                        <p className="truncate">{event.serviceType}</p>
                        <p className="italic truncate">{event.personnel}</p>
                      </div>
                    ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}