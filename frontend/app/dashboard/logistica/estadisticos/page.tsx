"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DatePickerWithRange } from "@/components/ui/date-range-picker" // Assuming this component exists
import { AreaChart, Download } from "lucide-react"

// Mock data for logistics statistics
const statsData = [
  {
    id: "PROJ-001",
    client: "Industrias Acme S.A.",
    equipment: "Manómetro Digital XYZ",
    quoteDate: "2024-06-10",
    ocDate: "2024-06-15",
    programmingTime: "2 días",
    serviceTime: "4 horas",
    personnel: "<PERSON>",
    objectiveMet: true,
  },
  {
    id: "PROJ-002",
    client: "Laboratorios Beta",
    equipment: "Termómetro de Precisión ABC",
    quoteDate: "2024-06-12",
    ocDate: "2024-06-18",
    programmingTime: "3 días",
    serviceTime: "2 horas",
    personnel: "<PERSON>",
    objectiveMet: true,
  },
  {
    id: "PROJ-003",
    client: "Constructora Delta",
    equipment: "Nivel Láser 123",
    quoteDate: "2024-06-20",
    ocDate: "2024-06-25",
    programmingTime: "5 días",
    serviceTime: "8 horas",
    personnel: "Miguel Hernández",
    objectiveMet: false,
  },
]

import { DateRange } from "react-day-picker"

export default function LogisticaEstadisticosPage() {
  const [dateRange, setDateRange] = useState<DateRange | undefined>();

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center">
            <AreaChart className="h-6 w-6 mr-2 text-indigo-600" />
            <div>
              <CardTitle>Estadísticos de Logística</CardTitle>
              <CardDescription>
                Base de datos de proyectos para análisis y descarga de información.
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-4 gap-4">
            <div className="flex gap-4">
              <DatePickerWithRange onSelect={setDateRange} />
              <Input placeholder="Filtrar por cliente..." className="max-w-xs" />
            </div>
            <Button>
              <Download className="h-4 w-4 mr-2" />
              Descargar Base de Datos (Excel)
            </Button>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Cliente</TableHead>
                <TableHead>Equipo</TableHead>
                <TableHead>Fecha Cotización</TableHead>
                <TableHead>Tiempo Programación</TableHead>
                <TableHead>Tiempo Servicio</TableHead>
                <TableHead>Personal</TableHead>
                <TableHead>Cumplimiento</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {statsData.map((stat) => (
                <TableRow key={stat.id}>
                  <TableCell className="font-medium">{stat.client}</TableCell>
                  <TableCell>{stat.equipment}</TableCell>
                  <TableCell>{stat.quoteDate}</TableCell>
                  <TableCell>{stat.programmingTime}</TableCell>
                  <TableCell>{stat.serviceTime}</TableCell>
                  <TableCell>{stat.personnel}</TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        stat.objectiveMet
                          ? "bg-green-100 text-green-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {stat.objectiveMet ? "Cumplido" : "No Cumplido"}
                    </span>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}