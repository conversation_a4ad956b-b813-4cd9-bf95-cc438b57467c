"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogClose } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Mail, PlusCircle } from "lucide-react"
import { toast } from "sonner"

// Mock data for travel expense requests
const viaticosHistory = [
  {
    id: "VIAT-001",
    destination: "Monterrey, NL",
    requestDate: "2024-07-10",
    amount: 5000,
    status: "Aprobado",
  },
  {
    id: "VIAT-002",
    destination: "Querétaro, Qro.",
    requestDate: "2024-07-15",
    amount: 3500,
    status: "Pendiente",
  },
    {
    id: "VIAT-003",
    destination: "Guadalajara, Jal.",
    requestDate: "2024-07-05",
    amount: 4200,
    status: "Rechazado",
  },
]

const getStatusBadge = (status: string) => {
  switch (status) {
    case "Aprobado": return <Badge className="bg-green-100 text-green-800">{status}</Badge>
    case "Pendiente": return <Badge className="bg-yellow-100 text-yellow-800">{status}</Badge>
    case "Rechazado": return <Badge variant="destructive">{status}</Badge>
    default: return <Badge variant="secondary">{status}</Badge>
  }
}

export default function LogisticaViaticosPage() {
  const [history, setHistory] = useState(viaticosHistory)
  const [isFormOpen, setIsFormOpen] = useState(false)

  const handleAddRequest = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    const newRequest = {
      id: `VIAT-${String(history.length + 1).padStart(3, '0')}`,
      destination: formData.get("destination") as string,
      requestDate: new Date().toISOString().split('T')[0],
      amount: parseFloat(formData.get("amount") as string),
      status: "Pendiente",
    }
    setHistory([newRequest, ...history])
    setIsFormOpen(false)
    e.currentTarget.reset()
    toast.success("Solicitud de viáticos creada exitosamente.")
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center">
            <Mail className="h-6 w-6 mr-2 text-cyan-600" />
            <div>
              <CardTitle>Solicitud de Viáticos (Logística)</CardTitle>
              <CardDescription>Crear y consultar solicitudes de viáticos para viajes de trabajo.</CardDescription>
            </div>
          </div>
          <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
            <DialogTrigger asChild>
              <Button>
                <PlusCircle className="h-4 w-4 mr-2" />
                Nueva Solicitud
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[480px]">
              <DialogHeader><DialogTitle>Crear Solicitud de Viáticos</DialogTitle></DialogHeader>
              <form onSubmit={handleAddRequest} className="space-y-4">
                <div><Label htmlFor="destination">Destino</Label><Input id="destination" name="destination" placeholder="Ciudad, Estado" required /></div>
                <div className="grid grid-cols-2 gap-4">
                  <div><Label htmlFor="startDate">Fecha de Salida</Label><Input id="startDate" name="startDate" type="date" required /></div>
                  <div><Label htmlFor="endDate">Fecha de Regreso</Label><Input id="endDate" name="endDate" type="date" required /></div>
                </div>
                <div><Label htmlFor="reason">Motivo del Viaje</Label><Textarea id="reason" name="reason" placeholder="Descripción del propósito del viaje" required /></div>
                <div><Label htmlFor="amount">Monto Estimado (MXN)</Label><Input id="amount" name="amount" type="number" step="0.01" placeholder="5000.00" required /></div>
                <DialogFooter>
                  <DialogClose asChild><Button type="button" variant="secondary">Cancelar</Button></DialogClose>
                  <Button type="submit">Enviar Solicitud</Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Destino</TableHead>
                <TableHead>Fecha Solicitud</TableHead>
                <TableHead>Monto (MXN)</TableHead>
                <TableHead>Estatus</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {history.map((request) => (
                <TableRow key={request.id}>
                  <TableCell className="font-semibold">{request.id}</TableCell>
                  <TableCell>{request.destination}</TableCell>
                  <TableCell>{request.requestDate}</TableCell>
                  <TableCell>{request.amount.toFixed(2)}</TableCell>
                  <TableCell>{getStatusBadge(request.status)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}