"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogClose } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Truck, PlusCircle } from "lucide-react"
import { toast } from "sonner"

// Mock data for shipping requests
const paqueteriaHistory = [
  {
    id: "PAQ-001",
    requestDate: "2024-07-12",
    destination: "Blvd. Kukulcan, Cancún",
    trackingNumber: "1Z9999W99999999999",
    status: "Enviado",
  },
  {
    id: "PAQ-002",
    requestDate: "2024-07-16",
    destination: "Av. Reforma, CDMX",
    trackingNumber: null,
    status: "Procesando",
  },
    {
    id: "PAQ-003",
    requestDate: "2024-07-08",
    destination: "Av. Lázaro Cárdenas, Monterrey",
    trackingNumber: "777123456789",
    status: "Entregado",
  },
]

const getStatusBadge = (status: string) => {
  switch (status) {
    case "Enviado": return <Badge className="bg-blue-100 text-blue-800">{status}</Badge>
    case "Procesando": return <Badge className="bg-yellow-100 text-yellow-800">{status}</Badge>
    case "Entregado": return <Badge className="bg-green-100 text-green-800">{status}</Badge>
    default: return <Badge variant="secondary">{status}</Badge>
  }
}

export default function LogisticaPaqueteriaPage() {
  const [history, setHistory] = useState(paqueteriaHistory)
  const [isFormOpen, setIsFormOpen] = useState(false)

  const handleAddRequest = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    const newRequest = {
      id: `PAQ-${String(history.length + 1).padStart(3, '0')}`,
      requestDate: new Date().toISOString().split('T')[0],
      destination: formData.get("destination") as string,
      trackingNumber: null,
      status: "Procesando",
    }
    setHistory([newRequest, ...history])
    setIsFormOpen(false)
    e.currentTarget.reset()
    toast.success("Solicitud de paquetería creada exitosamente.")
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center">
            <Truck className="h-6 w-6 mr-2 text-orange-500" />
            <div>
              <CardTitle>Solicitud de Paquetería (Logística)</CardTitle>
              <CardDescription>Crear y consultar solicitudes de envío de paquetería.</CardDescription>
            </div>
          </div>
          <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
            <DialogTrigger asChild>
              <Button>
                <PlusCircle className="h-4 w-4 mr-2" />
                Nueva Solicitud
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[480px]">
              <DialogHeader><DialogTitle>Crear Solicitud de Paquetería</DialogTitle></DialogHeader>
              <form onSubmit={handleAddRequest} className="space-y-4">
                <div><Label htmlFor="destination">Dirección de Destino Completa</Label><Textarea id="destination" name="destination" placeholder="Calle, Número, Colonia, Ciudad, Estado, C.P." required /></div>
                <div><Label htmlFor="contentDescription">Descripción del Contenido</Label><Textarea id="contentDescription" name="contentDescription" placeholder="Ej: 2 manómetros, 1 termómetro, documentos..." required /></div>
                <div className="grid grid-cols-2 gap-4">
                    <div><Label htmlFor="weight">Peso (kg)</Label><Input id="weight" name="weight" type="number" step="0.1" placeholder="5.5" required /></div>
                    <div><Label htmlFor="serviceType">Tipo de Servicio</Label><Input id="serviceType" name="serviceType" placeholder="Ej: Día Siguiente, Terrestre" required /></div>
                </div>
                <DialogFooter>
                  <DialogClose asChild><Button type="button" variant="secondary">Cancelar</Button></DialogClose>
                  <Button type="submit">Enviar Solicitud</Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Fecha Solicitud</TableHead>
                <TableHead>Destino</TableHead>
                <TableHead>No. de Guía</TableHead>
                <TableHead>Estatus</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {history.map((request) => (
                <TableRow key={request.id}>
                  <TableCell className="font-semibold">{request.id}</TableCell>
                  <TableCell>{request.requestDate}</TableCell>
                  <TableCell>{request.destination}</TableCell>
                  <TableCell>{request.trackingNumber || <span className="text-muted-foreground">N/A</span>}</TableCell>
                  <TableCell>{getStatusBadge(request.status)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}