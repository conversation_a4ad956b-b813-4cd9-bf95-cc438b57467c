"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogClose } from "@/components/ui/dialog"
import { File<PERSON>lock, PlusCircle, Printer } from "lucide-react"

// Mock data for equipment entries
const entryHistory = [
  {
    id: "ING-001",
    receptionDate: "2024-07-15",
    client: "Industrias Acme S.A.",
    equipment: "Manómetro Digital XYZ",
    quote: "COT-2024-150",
    receivedBy: "<PERSON>",
  },
  {
    id: "ING-002",
    receptionDate: "2024-07-18",
    client: "Laboratorios Beta",
    equipment: "Termómetro de Precisión ABC",
    quote: "COT-2024-152",
    receivedBy: "<PERSON>",
  },
]

export default function LogisticaIngresosPage() {
  const [history, setHistory] = useState(entryHistory)
  const [isFormOpen, setIsFormOpen] = useState(false)

  const handleAddEntry = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    const newEntry = {
      id: `ING-${String(history.length + 1).padStart(3, '0')}`,
      receptionDate: format(new Date(), "yyyy-MM-dd"),
      client: formData.get("client") as string,
      equipment: `${formData.get("instrument")} ${formData.get("brand")} ${formData.get("model")}`,
      quote: formData.get("quote") as string,
      receivedBy: "Usuario Actual", // Placeholder
    }
    setHistory([...history, newEntry])
    setIsFormOpen(false)
    e.currentTarget.reset()
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center">
            <FileClock className="h-6 w-6 mr-2 text-teal-600" />
            <div>
              <CardTitle>Historial de Ingresos de Equipos</CardTitle>
              <CardDescription>Registro y consulta de la recepción de equipos para servicio.</CardDescription>
            </div>
          </div>
          <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
            <DialogTrigger asChild>
              <Button>
                <PlusCircle className="h-4 w-4 mr-2" />
                Registrar Ingreso
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-2xl">
              <DialogHeader><DialogTitle>Hoja de Recepción de Equipos</DialogTitle></DialogHeader>
              <form onSubmit={handleAddEntry} className="space-y-4">
                <Card>
                  <CardHeader><CardTitle className="text-lg">Datos del Proyecto y Cliente</CardTitle></CardHeader>
                  <CardContent className="grid grid-cols-2 gap-4">
                    <div><Label htmlFor="quote">No. de Cotización</Label><Input id="quote" name="quote" required /></div>
                    <div><Label htmlFor="oc">Orden de Compra</Label><Input id="oc" name="oc" /></div>
                    <div><Label htmlFor="client">Nombre de la Empresa</Label><Input id="client" name="client" required /></div>
                    <div><Label htmlFor="contact">Contacto</Label><Input id="contact" name="contact" /></div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader><CardTitle className="text-lg">Datos del Equipo</CardTitle></CardHeader>
                  <CardContent className="grid grid-cols-2 gap-4">
                    <div><Label htmlFor="instrument">Instrumento</Label><Input id="instrument" name="instrument" required /></div>
                    <div><Label htmlFor="brand">Marca</Label><Input id="brand" name="brand" required /></div>
                    <div><Label htmlFor="model">Modelo</Label><Input id="model" name="model" required /></div>
                    <div><Label htmlFor="serial">Serie</Label><Input id="serial" name="serial" /></div>
                    <div><Label htmlFor="accessories">Accesorios</Label><Input id="accessories" name="accessories" /></div>
                    <div className="col-span-2"><Label htmlFor="observations">Observaciones</Label><Textarea id="observations" name="observations" /></div>
                  </CardContent>
                </Card>
                <DialogFooter>
                  <DialogClose asChild><Button type="button" variant="secondary">Cancelar</Button></DialogClose>
                  <Button type="submit">Registrar y Generar Hoja</Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Fecha Recepción</TableHead>
                <TableHead>Cliente</TableHead>
                <TableHead>Equipo</TableHead>
                <TableHead>Cotización</TableHead>
                <TableHead>Recibido por</TableHead>
                <TableHead className="text-right">Acciones</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {history.map((entry) => (
                <TableRow key={entry.id}>
                  <TableCell className="font-semibold">{entry.id}</TableCell>
                  <TableCell>{entry.receptionDate}</TableCell>
                  <TableCell>{entry.client}</TableCell>
                  <TableCell>{entry.equipment}</TableCell>
                  <TableCell>{entry.quote}</TableCell>
                  <TableCell>{entry.receivedBy}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="outline" size="sm">
                      <Printer className="h-4 w-4 mr-2" />
                      Imprimir Hoja
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}

// Helper function to format date, assuming it's not available globally
function format(date: Date, formatStr: string) {
    // Basic formatter, replace with a robust one if needed
    if (formatStr === 'yyyy-MM-dd') {
        return date.toISOString().split('T')[0];
    }
    return date.toLocaleString();
}