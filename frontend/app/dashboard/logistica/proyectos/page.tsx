"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { FolderOpen, Search, Download, MessageSquare } from "lucide-react"

// Mock data for logistics projects
const projectData = [
  {
    id: "PROJ-001",
    quote: "COT-2024-150",
    oc: "OC-CLIENT-A-01",
    client: "Industrias Acme S.A.",
    status: "En Proceso",
    lastUpdate: "2024-07-15",
  },
  {
    id: "PROJ-002",
    quote: "COT-2024-152",
    oc: "OC-CLIENT-B-05",
    client: "Laboratorios Beta",
    status: "Finalizado",
    lastUpdate: "2024-07-10",
  },
  {
    id: "PROJ-003",
    quote: "COT-2024-155",
    oc: "OC-CLIENT-C-02",
    client: "Constructora Delta",
    status: "Pendiente de Almacén",
    lastUpdate: "2024-07-18",
  },
  {
    id: "PROJ-004",
    quote: "COT-2024-158",
    oc: "OC-CLIENT-D-09",
    client: "Manufacturas Zeta",
    status: "Mantenimiento",
    lastUpdate: "2024-07-17",
  },
]

const getStatusBadge = (status: string) => {
  switch (status) {
    case "En Proceso":
      return <Badge className="bg-blue-100 text-blue-800">{status}</Badge>
    case "Finalizado":
      return <Badge className="bg-green-100 text-green-800">{status}</Badge>
    case "Pendiente de Almacén":
      return <Badge className="bg-yellow-100 text-yellow-800">{status}</Badge>
    case "Mantenimiento":
      return <Badge className="bg-purple-100 text-purple-800">{status}</Badge>
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

export default function LogisticaProyectosPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const filteredProjects = projectData.filter(
    (p) =>
      p.client.toLowerCase().includes(searchTerm.toLowerCase()) ||
      p.quote.toLowerCase().includes(searchTerm.toLowerCase()) ||
      p.oc.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center">
            <FolderOpen className="h-6 w-6 mr-2 text-yellow-600" />
            <div>
              <CardTitle>Gestión de Proyectos (Logística)</CardTitle>
              <CardDescription>
                Visualización y seguimiento del estatus de los proyectos autorizados.
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar por cliente, cotización, OC..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Cliente</TableHead>
                <TableHead>Cotización</TableHead>
                <TableHead>Orden de Compra</TableHead>
                <TableHead>Estatus</TableHead>
                <TableHead>Última Actualización</TableHead>
                <TableHead className="text-right">Acciones</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProjects.map((project) => (
                <TableRow key={project.id}>
                  <TableCell className="font-medium">{project.client}</TableCell>
                  <TableCell>{project.quote}</TableCell>
                  <TableCell>{project.oc}</TableCell>
                  <TableCell>{getStatusBadge(project.status)}</TableCell>
                  <TableCell>{project.lastUpdate}</TableCell>
                  <TableCell className="text-right space-x-2">
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Docs
                    </Button>
                    <Button variant="outline" size="sm">
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Comentar
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}