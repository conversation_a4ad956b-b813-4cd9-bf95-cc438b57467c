import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, DialogContent, <PERSON>alogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { ArrowRight } from "lucide-react";

// Tipos
interface Project {
  id: number;
  customer: string;
  project: string;
  value: number;
  status: string;
  department: string;
  date: string;
  progress: number;
  responsible: string;
  contact: { name: string; email: string; phone: string };
  address: string;
  description: string;
  timeline: any[];
  documents: any[];
}

interface DashboardCardsProps {
  projects: Project[];
  departmentNames: Record<string, string>;
  departmentColors: Record<string, string>;
  statusNames: Record<string, string>;
  statusColors: Record<string, string>;
  cn: (...args: any[]) => string;
}

export default function DashboardCards({
  projects,
  departmentNames,
  departmentColors,
  statusNames,
  statusColors,
  cn,
}: DashboardCardsProps) {
  const [statusFilter, setStatusFilter] = useState("all");
  const [departmentFilter, setDepartmentFilter] = useState("all");
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);

  const filteredProjects = projects.filter((project) => {
    const matchesStatus = statusFilter === "all" || project.status === statusFilter;
    const matchesDepartment = departmentFilter === "all" || project.department === departmentFilter;
    return matchesStatus && matchesDepartment;
  });

  // Modal de detalle de proyecto
  function ProjectDetailModal({ project }: { project: Project }) {
    return (
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto backdrop-blur-2xl bg-moka-peach/90 dark:bg-slate-800/40 border-moka-brown/30 dark:border-slate-700/30 text-moka-bistre dark:text-slate-100">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3 font-light text-2xl text-moka-bistre dark:text-slate-200">
            <div className={cn("w-4 h-4 rounded-full", departmentColors[project.department])} />
            {project.customer}
            <span className="text-lg font-extralight text-moka-brown dark:text-slate-400">— {project.project}</span>
          </DialogTitle>
        </DialogHeader>
        {/* ...aquí puedes pegar el resto del contenido del modal de tu código original... */}
        <div className="p-4">(Detalles del proyecto...)</div>
      </DialogContent>
    );
  }

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-3xl font-light tracking-wide text-gray-900 dark:text-gray-100">Proyectos Activos</h2>
        <div className="flex items-center gap-4">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-48 bg-white text-gray-900 dark:bg-gray-800 dark:text-gray-100 invisible-border rounded-full font-light">
              <SelectValue placeholder="Filtrar por estado" />
            </SelectTrigger>
            <SelectContent className="bg-white text-gray-900 dark:bg-gray-800 dark:text-gray-100 invisible-border">
              <SelectItem value="all" className="font-light">Todos los estados</SelectItem>
              <SelectItem value="pending" className="font-light">Pendiente</SelectItem>
              <SelectItem value="in_progress" className="font-light">En Progreso</SelectItem>
              <SelectItem value="completed" className="font-light">Completado</SelectItem>
              <SelectItem value="cancelled" className="font-light">Cancelado</SelectItem>
            </SelectContent>
          </Select>
          <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
            <SelectTrigger className="w-48 bg-white text-gray-900 dark:bg-gray-800 dark:text-gray-100 invisible-border rounded-full font-light">
              <SelectValue placeholder="Filtrar departamento" />
            </SelectTrigger>
            <SelectContent className="bg-white text-gray-900 dark:bg-gray-800 dark:text-gray-100 invisible-border">
              <SelectItem value="all" className="font-light">Todos los departamentos</SelectItem>
              {Object.entries(departmentNames).map(([key, name]) => (
                <SelectItem key={key} value={key} className="font-light">
                  <div className="flex items-center gap-2">
                    <div className={cn("w-3 h-3 rounded-full", departmentColors[key])} />
                    {name}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="flex gap-6 overflow-x-auto pb-4 scrollbar-hide">
        {filteredProjects.map((project) => (
          <Dialog key={project.id} open={selectedProject?.id === project.id} onOpenChange={() => setSelectedProject(null)}>
            <DialogTrigger asChild>
              <Card
                className="backdrop-blur-xl border rounded-3xl min-w-80 flex-shrink-0 cursor-pointer transition-all duration-300 shadow-lg hover:shadow-xl group bg-gray-200/30 dark:bg-slate-800/30 border-gray-300/30 dark:border-slate-700/30 hover:bg-gray-300/40 dark:hover:bg-slate-700/40"
                onClick={() => setSelectedProject(project)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={cn("w-3 h-3 rounded-full", departmentColors[project.department])} />
                      <Badge className={cn("text-xs rounded-full font-light", statusColors[project.status])}>{statusNames[project.status]}</Badge>
                    </div>
                    <ArrowRight className="w-4 h-4 transition-transform duration-200 group-hover:translate-x-1 text-gray-500 dark:text-slate-400" />
                  </div>
                  <CardTitle className="text-xl font-light tracking-wide text-gray-900 dark:text-slate-100">{project.customer}</CardTitle>
                  <p className="text-sm font-light text-gray-600 dark:text-slate-400">{project.project}</p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-light text-gray-600 dark:text-slate-400">Valor</span>
                      <span className="text-xl font-light text-green-600 dark:text-green-400">${project.value.toLocaleString()}</span>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-light text-gray-600 dark:text-slate-400">Progreso</span>
                        <span className="text-sm font-light text-gray-800 dark:text-slate-200">{project.progress}%</span>
                      </div>
                      <div className="w-full rounded-full h-2 bg-gray-300/50 dark:bg-slate-700/50">
                        <div className="bg-gradient-to-r from-moka-falu to-red-600 h-2 rounded-full transition-all duration-300" style={{ width: `${project.progress}%` }} />
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="font-light text-gray-600 dark:text-slate-400">Responsable</span>
                      <span className="font-light text-gray-800 dark:text-slate-200">{project.responsible}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </DialogTrigger>
            {selectedProject?.id === project.id && <ProjectDetailModal project={project} />}
          </Dialog>
        ))}
      </div>
    </div>
  );
}
