import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import EntradasPage from '../entradas/page';
import PrestamosPage from '../prestamos/page';
import ReportePage from '../reporte/page';
import SalidasPage from '../salidas/page';
import StockPage from '../stock/page';
import TiempoPage from '../tiempo/page';
import ViaticosPage from '../viaticos/page';

jest.mock('@/components/auth/page-guard', () => ({
  PageGuard: ({ children }: any) => <>{children}</>
}));
jest.mock('@/hooks/usePagePermissions', () => ({
  usePagePermissions: () => ({ hasAccess: true })
}));
jest.mock('@/components/auth/unauthorized-page', () => ({
  UnauthorizedPage: () => <div>Acceso Restringido</div>
}));

// Mock ResizeObserver for recharts or similar libraries
beforeAll(() => {
  global.ResizeObserver =
    global.ResizeObserver ||
    class {
      observe() {}
      unobserve() {}
      disconnect() {}
    };
});

// Mock almacen hooks (example for useProducts, useInventoryStats, etc.)
jest.mock('@/hooks/useProducts', () => ({
  useProducts: () => ({ data: { products: [], total: 0 }, isLoading: false, error: null, isError: false }),
  useInventoryStats: () => ({ data: { totalProducts: 0, lowStockProducts: 0, outOfStockProducts: 0 }, isLoading: false }),
  useCreateProduct: () => ({ mutateAsync: jest.fn(), isPending: false }),
  useUpdateProduct: () => ({ mutateAsync: jest.fn(), isPending: false }),
  useDeleteProduct: () => ({ mutateAsync: jest.fn(), isPending: false })
}));

const queryClient = new QueryClient();
const renderWithQueryClient = (ui: React.ReactElement) =>
  render(<QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>);

describe('Almacén - Páginas protegidas', () => {
  it('renderiza la página de entradas', () => {
    renderWithQueryClient(<EntradasPage />);
    expect(screen.getByRole('heading', { name: /Entradas de Stock/i })).toBeInTheDocument();
  });
  it('renderiza la página de préstamos', () => {
    renderWithQueryClient(<PrestamosPage />);
    expect(screen.getByRole('heading', { name: /Préstamo de Equipo/i })).toBeInTheDocument();
  });
  it('renderiza la página de reporte', () => {
    renderWithQueryClient(<ReportePage />);
    expect(screen.getByRole('heading', { name: /Reporte de Inventario/i })).toBeInTheDocument();
  });
  it('renderiza la página de salidas', () => {
    renderWithQueryClient(<SalidasPage />);
    expect(screen.getByRole('heading', { name: /Vale de Salida de Stock/i })).toBeInTheDocument();
  });
  it('renderiza la página de stock', () => {
    renderWithQueryClient(<StockPage />);
    expect(screen.getByRole('heading', { name: /Stock Actual/i })).toBeInTheDocument();
  });
  it('renderiza la página de tiempo', () => {
    renderWithQueryClient(<TiempoPage />);
    expect(screen.getByRole('heading', { name: /Solicitud de Tiempo/i })).toBeInTheDocument();
  });
  it('renderiza la página de viáticos', () => {
    renderWithQueryClient(<ViaticosPage />);
    expect(screen.getByRole('heading', { name: /Solicitud de Viáticos/i })).toBeInTheDocument();
  });
});
