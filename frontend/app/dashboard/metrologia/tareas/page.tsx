"use client"

import { PageGuard } from "@/components/auth/page-guard"
import { TareasTable } from '@/components/metrologia/TareasTable'

export default function TareasPage() {
  return (
    <PageGuard
      requiredPermissions={["metrologia:maintenance:read", "metrologia:maintenance:schedule"]}
      unauthorizedTitle="Acceso Denegado - Tareas de Metrología"
      unauthorizedMessage="No tienes los permisos necesarios para acceder a las tareas de metrología."
    >
      <TareasTable />
    </PageGuard>
  )
}