"use client"

import { PageGuard } from "@/components/auth/page-guard"
import { CapacitacionesCalendar } from '@/components/metrologia/CapacitacionesCalendar'

export default function CapacitacionesPage() {
  return (
    <PageGuard
      requiredPermissions={["metrologia:capacitaciones:read", "metrologia:capacitaciones:schedule"]}
      unauthorizedTitle="Acceso Denegado - Calendario de Capacitaciones"
      unauthorizedMessage="No tienes los permisos necesarios para acceder al calendario de capacitaciones."
    >
      <CapacitacionesCalendar />
    </PageGuard>
  )
}