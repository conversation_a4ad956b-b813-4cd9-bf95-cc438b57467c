"use client"

import { PageGuard } from "@/components/auth/page-guard"
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function PatronesPage() {
  return (
    <PageGuard
      requiredPermissions={["metrologia:standards:read", "metrologia:standards:validate"]}
      unauthorizedTitle="Acceso Denegado - Patrones de Metrología"
      unauthorizedMessage="No tienes los permisos necesarios para acceder a la sección de patrones."
    >
      <Card>
        <CardHeader>
          <CardTitle>Patrones de Metrología</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Aquí se mostrará la información sobre los patrones de metrología.</p>
        </CardContent>
      </Card>
    </PageGuard>
  )
}