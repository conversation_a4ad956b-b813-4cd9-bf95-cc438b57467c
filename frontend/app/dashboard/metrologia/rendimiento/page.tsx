"use client"

import { PageGuard } from "@/components/auth/page-guard"
import { MetrologiaDashboard } from '@/components/metrologia/MetrologiaDashboard'

export default function RendimientoPage() {
  return (
    <PageGuard
      requiredPermissions={["metrologia:reports:read", "metrologia:reports:generate"]}
      unauthorizedTitle="Acceso Denegado - Rendimiento de Metrología"
      unauthorizedMessage="No tienes los permisos necesarios para acceder a los reportes de rendimiento."
    >
      <MetrologiaDashboard />
    </PageGuard>
  )
}