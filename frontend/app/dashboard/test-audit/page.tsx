"use client"

import { useQuery } from "@tanstack/react-query"
import { auditService } from "@/lib/services/audit.service"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"

export default function TestAuditPage() {
  const { data: auditLogs, isLoading, error, refetch } = useQuery({
    queryKey: ['testAuditLogs'],
    queryFn: async () => {
      console.log('[TestAudit] Starting API call...')
      
      try {
        const response = await auditService.getAuditLogs({})
        console.log('[TestAudit] Raw response:', response)
        console.log('[TestAudit] Response data:', response.data)
        console.log('[TestAudit] Audit logs:', response.data.data)
        console.log('[TestAudit] Number of logs:', response.data.data?.length || 0)
        
        return response.data.data
      } catch (error) {
        console.error('[TestAudit] Error:', error)
        throw error
      }
    },
  })

  console.log('[TestAudit] Component render:')
  console.log('- auditLogs:', auditLogs)
  console.log('- isLoading:', isLoading)
  console.log('- error:', error)

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold tracking-tight mb-2">
          🔍 Test de Auditoría
        </h1>
        <p className="text-muted-foreground">
          Prueba directa del endpoint de auditoría
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Estado de la Consulta
            <Button onClick={() => refetch()} variant="outline" size="sm">
              🔄 Refrescar
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Badge variant={isLoading ? "default" : "secondary"}>
                {isLoading ? "Cargando..." : "Completado"}
              </Badge>
              {error && (
                <Badge variant="destructive">
                  Error: {error.message}
                </Badge>
              )}
            </div>
            
            <div className="text-sm text-muted-foreground">
              <p>Registros encontrados: <strong>{auditLogs?.length || 0}</strong></p>
              <p>Estado de carga: <strong>{isLoading ? "Sí" : "No"}</strong></p>
              <p>Hay error: <strong>{error ? "Sí" : "No"}</strong></p>
            </div>
          </div>
        </CardContent>
      </Card>

      {error && (
        <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/30">
          <CardHeader>
            <CardTitle className="text-red-800 dark:text-red-200">
              ❌ Error Detectado
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p className="text-red-700 dark:text-red-300">
                <strong>Mensaje:</strong> {error.message}
              </p>
              {error.response && (
                <div className="text-sm">
                  <p><strong>Status:</strong> {error.response.status}</p>
                  <p><strong>Data:</strong> {JSON.stringify(error.response.data, null, 2)}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {auditLogs && auditLogs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>
              ✅ Datos Recibidos ({auditLogs.length} registros)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {auditLogs.slice(0, 5).map((log, index) => (
                <div key={log.id} className="p-4 border rounded-lg bg-gray-50 dark:bg-slate-800">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                    <div><strong>ID:</strong> {log.id}</div>
                    <div><strong>Acción:</strong> {log.action}</div>
                    <div><strong>Usuario:</strong> {log.user?.name || log.user?.email || 'N/A'}</div>
                    <div><strong>Entidad:</strong> {log.target_entity || 'N/A'}</div>
                    <div><strong>Fecha:</strong> {new Date(log.created_at).toLocaleString()}</div>
                    <div><strong>IP:</strong> {log.ip_address || 'N/A'}</div>
                  </div>
                  {log.details && (
                    <div className="mt-2 p-2 bg-gray-100 dark:bg-slate-700 rounded text-xs">
                      <strong>Detalles:</strong>
                      <pre className="mt-1 whitespace-pre-wrap">
                        {typeof log.details === 'string' ? log.details : JSON.stringify(log.details, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              ))}
              
              {auditLogs.length > 5 && (
                <div className="text-center text-muted-foreground">
                  ... y {auditLogs.length - 5} registros más
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {!isLoading && (!auditLogs || auditLogs.length === 0) && !error && (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-muted-foreground">
              ❌ No se encontraron registros de auditoría
            </p>
          </CardContent>
        </Card>
      )}

      <Card className="bg-blue-50 dark:bg-blue-950/30">
        <CardHeader>
          <CardTitle className="text-blue-800 dark:text-blue-200">
            🔧 Información de Debug
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm space-y-2">
            <p><strong>API URL:</strong> {process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api'}</p>
            <p><strong>Endpoint:</strong> /systems/audit</p>
            <p><strong>Método:</strong> GET</p>
            <p><strong>Headers:</strong> Authorization Bearer token</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
