import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, Clock, UserCheck, Building } from "lucide-react";

// Tipos
interface CalendarEvent {
  id: number;
  title: string;
  date: string;
  time: string;
  department: string;
  type: string;
  relatedProject?: number;
  responsible: string;
  status: string;
  description?: string;
}
interface Project {
  id: number;
  project: string;
}
interface DashboardCalendarProps {
  events: CalendarEvent[];
  departmentNames: Record<string, string>;
  departmentColors: Record<string, string>;
  eventTypeIcons: Record<string, any>;
  eventStatusColors: Record<string, string>;
  eventStatusNames: Record<string, string>;
  cn: (...args: any[]) => string;
  projects: Project[];
}

const MONTHS = [
  "Enero", "Febrero", "Mar<PERSON>", "Abril", "Mayo", "Junio", "Julio", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre"
];
const WEEKDAYS = ["Dom", "Lun", "Mar", "Mié", "Jue", "Vie", "Sáb"];

export default function DashboardCalendar({
  events,
  departmentNames,
  departmentColors,
  eventTypeIcons,
  eventStatusColors,
  eventStatusNames,
  cn,
  projects,
}: DashboardCalendarProps) {
  const [calendarDepartmentFilter, setCalendarDepartmentFilter] = useState("all");
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    const days = [];
    const current = new Date(startDate);
    for (let i = 0; i < 42; i++) {
      days.push(new Date(current));
      current.setDate(current.getDate() + 1);
    }
    return days;
  };

  const getEventsForDate = (date: Date) => {
    const dateString = date.toISOString().split("T")[0];
    return events.filter((event) => {
      const eventDate = event.date;
      const matchesDate = eventDate === dateString;
      const matchesDepartment = calendarDepartmentFilter === "all" || event.department === calendarDepartmentFilter;
      return matchesDate && matchesDepartment;
    });
  };

  const navigateMonth = (direction: "prev" | "next") => {
    const newDate = new Date(currentDate);
    if (direction === "prev") {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentDate.getMonth();
  };

  const selectedDateEvents = selectedDate ? getEventsForDate(selectedDate) : [];

  function EventDetailModal({ event }: { event: CalendarEvent }) {
    const relatedProject = event.relatedProject ? projects.find((s) => s.id === event.relatedProject) : null;
    const IconComponent = eventTypeIcons[event.type];
    return (
      <DialogContent className="max-w-2xl backdrop-blur-2xl border shadow-2xl bg-white/95 dark:bg-gray-800/95 border-gray-300/50 dark:border-gray-600/50 text-gray-900 dark:text-gray-100">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 font-light text-gray-800 dark:text-gray-200">
            <div className={cn("p-2 rounded-full text-white", departmentColors[event.department])}>
              <IconComponent className="w-4 h-4" />
            </div>
            {event.title}
          </DialogTitle>
        </DialogHeader>
        {/* ...contenido del modal de evento... */}
        <div className="p-4">(Detalles del evento...)</div>
      </DialogContent>
    );
  }

  return (
    <section>
      {/* Renderiza aquí el calendario y los filtros usando los props */}
      <Card className="backdrop-blur-xl invisible-border rounded-3xl shadow-lg bg-white/30 dark:bg-gray-700/30">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-3xl font-light tracking-wide text-gray-900 dark:text-gray-100">Calendario de Actividades</CardTitle>
            <div className="flex items-center gap-4">
              <Select value={calendarDepartmentFilter} onValueChange={setCalendarDepartmentFilter}>
                <SelectTrigger className="w-48 backdrop-blur-xl invisible-border rounded-full shadow-lg font-light bg-white/30 dark:bg-gray-700/30 text-gray-900 dark:text-gray-100">
                  <SelectValue placeholder="Filtrar departamento" />
                </SelectTrigger>
                <SelectContent className="backdrop-blur-xl border bg-white/95 dark:bg-gray-800/95 border-gray-300/30 dark:border-gray-600/30">
                  <SelectItem value="all" className="font-light text-gray-900 dark:text-gray-100">Todos los departamentos</SelectItem>
                  {Object.entries(departmentNames).map(([key, name]) => (
                    <SelectItem key={key} value={key} className="font-light text-gray-900 dark:text-gray-100">
                      <div className="flex items-center gap-2">
                        <div className={cn("w-3 h-3 rounded-full", departmentColors[key])} />
                        {name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="sm" onClick={() => navigateMonth("prev")} className="rounded-full backdrop-blur-md border font-light bg-white/30 dark:bg-gray-700/30 border-gray-300/20 dark:border-gray-600/20 hover:bg-white/40 dark:hover:bg-gray-600/40">
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <h3 className="font-light text-xl text-gray-800 dark:text-gray-200">{MONTHS[currentDate.getMonth()]} {currentDate.getFullYear()}</h3>
                <Button variant="ghost" size="sm" onClick={() => navigateMonth("next")} className="rounded-full backdrop-blur-md border font-light bg-white/30 dark:bg-gray-700/30 border-gray-300/20 dark:border-gray-600/20 hover:bg-white/40 dark:hover:bg-gray-600/40">
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-7 gap-1 mb-4">
            {WEEKDAYS.map((day) => (
              <div key={day} className="p-3 text-center text-sm font-light text-gray-600 dark:text-gray-400">{day}</div>
            ))}
            {getDaysInMonth(currentDate).map((date, index) => {
              const dayEvents = getEventsForDate(date);
              const isCurrentMonthDate = isCurrentMonth(date);
              const isTodayDate = isToday(date);
              return (
                <div
                  key={index}
                  className={cn(
                    "p-2 min-h-20 invisible-border rounded-xl cursor-pointer transition-colors backdrop-blur-sm hover:bg-white/40 dark:hover:bg-gray-600/40",
                    !isCurrentMonthDate && "bg-gray-200/10 dark:bg-gray-700/10 text-gray-400 dark:text-gray-500",
                    isTodayDate && "bg-blue-100/60 dark:bg-blue-900/60 subtle-border",
                    selectedDate?.toDateString() === date.toDateString() && "bg-blue-200/60 dark:bg-blue-800/60 subtle-border",
                  )}
                  onClick={() => setSelectedDate(date)}
                >
                  <div className="text-sm font-light mb-1">{date.getDate()}</div>
                  <div className="space-y-1">
                    {dayEvents.slice(0, 2).map((event) => {
                      const IconComponent = eventTypeIcons[event.type];
                      return (
                        <div
                          key={event.id}
                          className={cn(
                            "text-xs p-1 rounded-full text-white truncate cursor-pointer hover:opacity-80",
                            departmentColors[event.department],
                          )}
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedEvent(event);
                          }}
                          title={event.title}
                        >
                          <div className="flex items-center gap-1">
                            <IconComponent className="w-3 h-3" />
                            <span className="truncate font-light">{event.title}</span>
                          </div>
                        </div>
                      );
                    })}
                    {dayEvents.length > 2 && (
                      <div className="text-xs text-center font-light text-gray-500 dark:text-gray-400">+{dayEvents.length - 2} más</div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
          {/* Eventos del día seleccionado */}
          {selectedDate && (
            <Card className="backdrop-blur-xl invisible-border rounded-2xl mt-4 shadow-lg bg-white/20 dark:bg-gray-700/20">
              <CardHeader>
                <CardTitle className="text-lg font-light text-gray-800 dark:text-gray-200">
                  Eventos - {selectedDate.toLocaleDateString("es-ES", { weekday: "long", year: "numeric", month: "long", day: "numeric" })}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {selectedDateEvents.length === 0 ? (
                  <p className="text-center py-4 font-light text-gray-500 dark:text-gray-400">No hay eventos para esta fecha</p>
                ) : (
                  <div className="space-y-3">
                    {selectedDateEvents.map((event) => {
                      const IconComponent = eventTypeIcons[event.type];
                      return (
                        <div
                          key={event.id}
                          className="flex items-center gap-3 p-3 border rounded-xl cursor-pointer transition-colors backdrop-blur-sm border-gray-300/30 dark:border-gray-600/30 hover:bg-white/30 dark:hover:bg-gray-600/40"
                          onClick={() => setSelectedEvent(event)}
                        >
                          <div className={cn("p-2 rounded-full text-white", departmentColors[event.department])}>
                            <IconComponent className="w-4 h-4" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h4 className="font-light text-sm text-gray-800 dark:text-gray-200">{event.title}</h4>
                              <Badge className={cn("text-xs font-light", eventStatusColors[event.status])}>{eventStatusNames[event.status]}</Badge>
                            </div>
                            <p className="text-xs font-light mb-1 text-gray-600 dark:text-gray-400">{event.description}</p>
                            <div className="flex items-center gap-4 text-xs font-light text-gray-500 dark:text-gray-400">
                              <span className="flex items-center gap-1"><Clock className="w-3 h-3" />{event.time}</span>
                              <span className="flex items-center gap-1"><UserCheck className="w-3 h-3" />{event.responsible}</span>
                              <span className="flex items-center gap-1"><Building className="w-3 h-3" />{departmentNames[event.department]}</span>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
      <Dialog open={!!selectedEvent} onOpenChange={() => setSelectedEvent(null)}>
        {selectedEvent && <EventDetailModal event={selectedEvent} />}
      </Dialog>
    </section>
  );
}
