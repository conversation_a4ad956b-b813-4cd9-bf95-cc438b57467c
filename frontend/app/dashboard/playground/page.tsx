"use client"

import React from "react"
import { FormCard } from "@/components/ui/form-styles"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"

// Importa aquí tus componentes modernos de prueba
import {
  ModernComponentTemplate,
  ExampleUsage,
  ComponentShowcase,
  ShowcaseExample,
  FileDrop,
  InputField,
  Toggle,
  Segment,
  ProjectForm,
  PlaygroundDemo
} from "@/components/playground"
import { useState } from "react"

export default function PlaygroundPage() {
  return (
    <div className="max-w-7xl mx-auto p-6">
      <FormCard>
        <header className="mb-6">
          <h1 className="text-3xl font-bold tracking-tight">🎨 Playground de Componentes</h1>
          <p className="text-muted-foreground">
            Espacio para probar y experimentar con componentes modernos antes de integrarlos al sistema.
          </p>
        </header>

        <Tabs defaultValue="demo" className="w-full">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="demo">Demo Completa</TabsTrigger>
            <TabsTrigger value="forms">Formularios</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="showcase">Showcase</TabsTrigger>
            <TabsTrigger value="project">Proyecto</TabsTrigger>
            <TabsTrigger value="examples">Ejemplos</TabsTrigger>
          </TabsList>

          <TabsContent value="demo" className="space-y-4">
            <PlaygroundDemo />
          </TabsContent>

          <TabsContent value="forms" className="space-y-4">
            <FormsDemo />
          </TabsContent>

          <TabsContent value="templates" className="space-y-4">
            <ExampleUsage />
          </TabsContent>

          <TabsContent value="showcase" className="space-y-4">
            <ShowcaseExample />
          </TabsContent>

          <TabsContent value="project" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Formulario de Proyecto Completo</CardTitle>
                <CardDescription>Ejemplo de formulario usando todos los componentes del playground</CardDescription>
              </CardHeader>
              <CardContent>
                <ProjectForm />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="examples" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Ejemplos y Referencias</CardTitle>
                <CardDescription>Componentes de ejemplo usando la paleta moka</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Button className="bg-moka-falu hover:bg-moka-falu/90">Moka Falu</Button>
                  <Button className="bg-moka-lion hover:bg-moka-lion/90 text-moka-bistre">Moka Lion</Button>
                  <Button className="bg-moka-brown hover:bg-moka-brown/90">Moka Brown</Button>
                  <Button className="bg-moka-peach hover:bg-moka-peach/90 text-moka-bistre">Moka Peach</Button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                  <div className="p-4 bg-moka-peach/20 border border-moka-brown/40 rounded-lg">
                    <h3 className="font-medium text-moka-bistre">Card Ejemplo</h3>
                    <p className="text-sm text-moka-brown">Usando colores de la paleta moka</p>
                  </div>
                  <div className="p-4 bg-moka-lion/20 border border-moka-brown/40 rounded-lg">
                    <h3 className="font-medium text-moka-bistre">Otra Card</h3>
                    <p className="text-sm text-moka-brown">Con diferentes tonos moka</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </FormCard>
    </div>
  )
}

// Componente de demostración de formularios
function FormsDemo() {
  const [toggleValue, setToggleValue] = useState(false)
  const [segmentValue, setSegmentValue] = useState<"option1" | "option2" | "option3">("option1")

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Componentes de Formulario</CardTitle>
          <CardDescription>Prueba los componentes individuales del playground</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* InputField Demo */}
          <div>
            <h3 className="text-lg font-medium mb-3">Campo de Entrada (InputField)</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <InputField label="Nombre completo" placeholder="Ingresa tu nombre" />
              <InputField label="Email" type="email" placeholder="<EMAIL>" />
            </div>
          </div>

          {/* Toggle Demo */}
          <div>
            <h3 className="text-lg font-medium mb-3">Interruptor (Toggle)</h3>
            <div className="flex items-center gap-4">
              <span>Notificaciones:</span>
              <Toggle checked={toggleValue} onChange={setToggleValue} />
              <span className="text-sm text-muted-foreground">
                {toggleValue ? "Activadas" : "Desactivadas"}
              </span>
            </div>
          </div>

          {/* Segment Demo */}
          <div>
            <h3 className="text-lg font-medium mb-3">Selector de Segmentos (Segment)</h3>
            <Segment
              options={[
                { value: "option1", label: "Opción 1" },
                { value: "option2", label: "Opción 2" },
                { value: "option3", label: "Opción 3" },
              ]}
              value={segmentValue}
              onChange={setSegmentValue}
            />
            <p className="text-sm text-muted-foreground mt-2">
              Seleccionado: {segmentValue}
            </p>
          </div>

          {/* FileDrop Demo */}
          <div>
            <h3 className="text-lg font-medium mb-3">Arrastrar y Soltar Archivos (FileDrop)</h3>
            <FileDrop onFiles={(files) => {
              console.log("Archivos seleccionados:", files)
              alert(`${files.length} archivo(s) seleccionado(s)`)
            }} />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
