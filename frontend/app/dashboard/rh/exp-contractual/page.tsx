"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Briefcase, UploadCloud, FileText, Trash2, Search } from "lucide-react"

// Mock data for employee contractual files
const contractualFilesData = [
  {
    id: "DOC-001",
    employee: "<PERSON>",
    documentName: "Contrato de Trabajo Indefinido.pdf",
    uploadDate: "2023-01-15",
    uploader: "<PERSON>",
  },
  {
    id: "DOC-002",
    employee: "<PERSON>",
    documentName: "Acuerdo de Confidencialidad.pdf",
    uploadDate: "2023-01-15",
    uploader: "<PERSON> García",
  },
  {
    id: "DOC-003",
    employee: "<PERSON>",
    documentName: "Contrato de Trabajo Temporal.pdf",
    uploadDate: "2023-03-10",
    uploader: "<PERSON>",
  },
]

export default function ExpContractualPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const filteredFiles = contractualFilesData.filter(
    (file) =>
      file.employee.toLowerCase().includes(searchTerm.toLowerCase()) ||
      file.documentName.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center">
            <Briefcase className="h-6 w-6 mr-2 text-orange-600" />
            <div>
              <CardTitle>Expediente Contractual</CardTitle>
              <CardDescription>
                Almacenamiento y consulta de documentos contractuales de los empleados.
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar por empleado o documento..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            {/* Esta acción sería solo para RH */}
            <Button>
              <UploadCloud className="h-4 w-4 mr-2" />
              Subir Documento
            </Button>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Empleado</TableHead>
                <TableHead>Nombre del Documento</TableHead>
                <TableHead>Fecha de Carga</TableHead>
                <TableHead>Subido por</TableHead>
                <TableHead className="text-right">Acciones</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredFiles.map((file) => (
                <TableRow key={file.id}>
                  <TableCell className="font-medium">{file.employee}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-gray-500" />
                      {file.documentName}
                    </div>
                  </TableCell>
                  <TableCell>{file.uploadDate}</TableCell>
                  <TableCell>{file.uploader}</TableCell>
                  <TableCell className="text-right space-x-2">
                    <Button variant="outline" size="sm">
                      Ver
                    </Button>
                    {/* Esta acción sería solo para RH */}
                    <Button variant="destructive" size="sm">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}