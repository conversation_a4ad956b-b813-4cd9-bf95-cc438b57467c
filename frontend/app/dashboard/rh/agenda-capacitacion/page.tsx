"use client"

import { useState } from "react"
import { PageGuard } from "@/components/auth/page-guard"
import { add, format, startOfMonth, getDay, eachDayOfInterval, endOfMonth, sub } from "date-fns"
import { es } from "date-fns/locale"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogClose } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { CalendarDays, PlusCircle, ChevronLeft, ChevronRight } from "lucide-react"

interface TrainingEvent {
  id: string
  date: Date
  title: string
  time: string
  assignedTo: string // '<PERSON>', 'Todos', etc.
  type: "Capacitación" | "Evaluación" | "Revisión"
}

// Mock data for calendar events
const initialEvents: TrainingEvent[] = [
  {
    id: "1",
    date: new Date("2024-07-15T10:00:00"),
    title: "Capacitación: Norma ISO 9001",
    time: "10:00 AM",
    assignedTo: "Todos",
    type: "Capacitación",
  },
  {
    id: "2",
    date: new Date("2024-07-17T14:00:00"),
    title: "Evaluación: Seguridad en Laboratorio",
    time: "02:00 PM",
    assignedTo: "Técnicos Lab",
    type: "Evaluación",
  },
  {
    id: "3",
    date: new Date("2024-08-05T09:00:00"),
    title: "Revisión de Protocolos",
    time: "09:00 AM",
    assignedTo: "Calidad",
    type: "Revisión",
  },
]

export default function AgendaCapacitacionPage() {
  const [events, setEvents] = useState<TrainingEvent[]>(initialEvents)
  const [currentDate, setCurrentDate] = useState(new Date())
  const [isFormOpen, setIsFormOpen] = useState(false)

  const firstDayOfMonth = startOfMonth(currentDate)
  const lastDayOfMonth = endOfMonth(currentDate)

  const daysInMonth = eachDayOfInterval({
    start: firstDayOfMonth,
    end: lastDayOfMonth,
  })

  // Adjust to start week on Sunday (0) for getDay() compatibility
  const startingDayIndex = getDay(firstDayOfMonth) === 0 ? 6 : getDay(firstDayOfMonth) - 1


  const handlePrevMonth = () => {
    setCurrentDate(sub(currentDate, { months: 1 }))
  }

  const handleNextMonth = () => {
    setCurrentDate(add(currentDate, { months: 1 }))
  }

  const handleAddEvent = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    const newEvent: TrainingEvent = {
      id: (Math.random() * 1000).toString(),
      date: new Date(formData.get("date") as string + "T12:00:00"),
      title: formData.get("title") as string,
      time: formData.get("time") as string,
      assignedTo: formData.get("assignedTo") as string,
      type: formData.get("type") as "Capacitación" | "Evaluación" | "Revisión",
    }
    setEvents([...events, newEvent])
    setIsFormOpen(false)
  }

  const getEventTypeClass = (type: TrainingEvent["type"]) => {
    switch (type) {
      case "Capacitación": return "bg-blue-100 text-blue-800"
      case "Evaluación": return "bg-yellow-100 text-yellow-800"
      case "Revisión": return "bg-purple-100 text-purple-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <PageGuard 
      requiredPermissions={["hr:training:read", "hr:training:create", "hr:training:update"]}
      unauthorizedTitle="Agenda de Capacitación - Acceso Restringido"
      unauthorizedMessage="Necesitas permisos de recursos humanos para gestionar la agenda de capacitación."
    >
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center">
            <CalendarDays className="h-6 w-6 mr-2 text-green-600" />
            <div>
              <CardTitle>Agenda de Capacitación</CardTitle>
              <CardDescription>
                Gestión del calendario de capacitaciones para el personal.
              </CardDescription>
            </div>
          </div>
          <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
            <DialogTrigger asChild>
              <Button>
                <PlusCircle className="h-4 w-4 mr-2" />
                Agendar Capacitación
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Agendar Nueva Capacitación</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleAddEvent} className="space-y-4">
                <div>
                  <Label htmlFor="title">Título del Evento</Label>
                  <Input id="title" name="title" required />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="date">Fecha</Label>
                    <Input id="date" name="date" type="date" required />
                  </div>
                  <div>
                    <Label htmlFor="time">Hora</Label>
                    <Input id="time" name="time" type="time" required />
                  </div>
                </div>
                <div>
                  <Label htmlFor="type">Tipo de Evento</Label>
                  <Select name="type" required>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecciona un tipo" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Capacitación">Capacitación</SelectItem>
                      <SelectItem value="Evaluación">Evaluación</SelectItem>
                      <SelectItem value="Revisión">Revisión</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="assignedTo">Asignado a</Label>
                  <Input id="assignedTo" name="assignedTo" placeholder="Ej: Todos, Técnicos Lab..." required />
                </div>
                <DialogFooter>
                  <DialogClose asChild>
                    <Button type="button" variant="secondary">Cancelar</Button>
                  </DialogClose>
                  <Button type="submit">Agendar</Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <Button variant="outline" size="icon" onClick={handlePrevMonth}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <h2 className="text-xl font-semibold capitalize">
              {format(currentDate, "MMMM yyyy", { locale: es })}
            </h2>
            <Button variant="outline" size="icon" onClick={handleNextMonth}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          <div className="grid grid-cols-7 text-center font-semibold text-sm text-gray-600 border-b">
            {["Lun", "Mar", "Mié", "Jue", "Vie", "Sáb", "Dom"].map(day => (
              <div key={day} className="py-2">{day}</div>
            ))}
          </div>
          <div className="grid grid-cols-7 grid-rows-5">
            {Array.from({ length: startingDayIndex }).map((_, i) => (
              <div key={`empty-${i}`} className="border-r border-b h-28"></div>
            ))}
            {daysInMonth.map((day) => (
              <div key={day.toString()} className="border-r border-b h-28 p-1 relative">
                <div className={`text-sm ${format(day, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd') ? 'bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center' : ''}`}>
                  {format(day, "d")}
                </div>
                <div className="mt-1 space-y-1 overflow-y-auto max-h-20">
                  {events
                    .filter(event => format(event.date, "yyyy-MM-dd") === format(day, "yyyy-MM-dd"))
                    .map(event => (
                      <div key={event.id} className={`p-1 rounded-md text-xs ${getEventTypeClass(event.type)}`}>
                        <p className="font-semibold truncate">{event.title}</p>
                        <p>{event.time}</p>
                      </div>
                    ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
    </PageGuard>
  )
}