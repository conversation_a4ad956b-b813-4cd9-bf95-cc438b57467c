"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { BookUser, CheckCircle, XCircle, Edit } from "lucide-react"

// Mock data simulating user's training plan
const trainingData = [
  {
    id: "C001",
    course: "Norma ISO 9001:2015",
    status: "Aprobado",
    progress: 100,
    score: 95,
    evaluationDate: "2024-05-20",
  },
  {
    id: "C002",
    course: "Seguridad en Laboratorio",
    status: "En Progreso",
    progress: 60,
    score: null,
    evaluationDate: null,
  },
  {
    id: "C003",
    course: "Manejo de Residuos Peligrosos",
    status: "Reprobado",
    progress: 100,
    score: 72,
    evaluationDate: "2024-06-10",
  },
  {
    id: "C004",
    course: "Uso de Software CRM",
    status: "Pendiente",
    progress: 0,
    score: null,
    evaluationDate: null,
  },
]

const getStatusBadge = (status: string) => {
  switch (status) {
    case "Aprobado":
      return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Aprobado</Badge>
    case "En Progreso":
      return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">En Progreso</Badge>
    case "Reprobado":
      return <Badge variant="destructive">Reprobado</Badge>
    case "Pendiente":
      return <Badge variant="secondary">Pendiente</Badge>
    default:
      return <Badge>{status}</Badge>
  }
}

export default function CapacitacionRfpPage() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center">
            <BookUser className="h-6 w-6 mr-2 text-blue-600" />
            <div>
              <CardTitle>Capacitación (RFP)</CardTitle>
              <CardDescription>
                Registro de Formación Personal. Consulta tu progreso y gestiona tus evaluaciones.
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[40%]">Curso</TableHead>
                <TableHead>Estado</TableHead>
                <TableHead>Progreso</TableHead>
                <TableHead>Calificación</TableHead>
                <TableHead className="text-right">Acciones</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {trainingData.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">{item.course}</TableCell>
                  <TableCell>{getStatusBadge(item.status)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Progress value={item.progress} className="w-[80%]" />
                      <span className="text-xs text-muted-foreground">{item.progress}%</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {item.score !== null ? (
                      <span className={`font-semibold ${item.score >= 80 ? 'text-green-600' : 'text-red-600'}`}>
                        {item.score}
                      </span>
                    ) : (
                      <span className="text-muted-foreground">N/A</span>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    {item.status === "En Progreso" && (
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4 mr-2" />
                        Continuar
                      </Button>
                    )}
                    {item.status === "Pendiente" && (
                      <Button size="sm">Iniciar Curso</Button>
                    )}
                     {item.status === "Reprobado" && (
                      <Button variant="secondary" size="sm">Reiniciar</Button>
                    )}
                     {item.status === "Aprobado" && (
                      <Button variant="ghost" size="sm" disabled>Completado</Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Mi Perfil de Puesto</CardTitle>
          <CardDescription>Capacitaciones requeridas para tu rol.</CardDescription>
        </CardHeader>
        <CardContent className="flex gap-4">
            <div className="p-4 border rounded-lg flex-1">
                <h4 className="font-semibold">Técnico de Laboratorio A</h4>
                <ul className="list-disc pl-5 mt-2 text-sm text-muted-foreground">
                    <li>Norma ISO 9001:2015 <CheckCircle className="inline h-4 w-4 text-green-500"/></li>
                    <li>Seguridad en Laboratorio <XCircle className="inline h-4 w-4 text-blue-500"/></li>
                    <li>Manejo de Residuos Peligrosos <XCircle className="inline h-4 w-4 text-red-500"/></li>
                </ul>
            </div>
        </CardContent>
      </Card>
    </div>
  )
}