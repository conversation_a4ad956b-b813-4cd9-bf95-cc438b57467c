"use client"

import { <PERSON>, CardContent, CardDes<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { UsersRound, PlusCircle, CalendarPlus, Eye } from "lucide-react"

// Mock data for follow-up meetings
const followUpData = [
  {
    id: "RSP-001",
    employee: "<PERSON>",
    responsible: "<PERSON> (RH)",
    meetingDate: "2024-06-20",
    status: "Completada",
    nextMeeting: null,
  },
  {
    id: "RSP-002",
    employee: "<PERSON>",
    responsible: "<PERSON> (RH)",
    meetingDate: "2024-07-05",
    status: "Completada",
    nextMeeting: "2024-09-05",
  },
  {
    id: "RSP-003",
    employee: "<PERSON>",
    responsible: "<PERSON> (Manager)",
    meetingDate: "2024-07-18",
    status: "Programada",
    nextMeeting: null,
  },
  {
    id: "RSP-004",
    employee: "<PERSON>",
    responsible: "<PERSON> (RH)",
    meetingDate: "2024-08-01",
    status: "Programada",
    nextMeeting: null,
  },
]

export default function RspPage() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center">
            <UsersRound className="h-6 w-6 mr-2 text-purple-600" />
            <div>
              <CardTitle>Seguimiento Personal (RSP)</CardTitle>
              <CardDescription>
                Registro de reuniones de seguimiento para el crecimiento profesional.
              </CardDescription>
            </div>
          </div>
          {/* Esta acción sería solo para RH o responsables */}
          <Button>
            <PlusCircle className="h-4 w-4 mr-2" />
            Registrar Reunión
          </Button>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Empleado</TableHead>
                <TableHead>Responsable</TableHead>
                <TableHead>Fecha de Reunión</TableHead>
                <TableHead>Estado</TableHead>
                <TableHead>Próxima Reunión</TableHead>
                <TableHead className="text-right">Acciones</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {followUpData.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">{item.employee}</TableCell>
                  <TableCell>{item.responsible}</TableCell>
                  <TableCell>{item.meetingDate}</TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        item.status === "Completada"
                          ? "bg-green-100 text-green-800"
                          : "bg-blue-100 text-blue-800"
                      }`}
                    >
                      {item.status}
                    </span>
                  </TableCell>
                  <TableCell>
                    {item.nextMeeting || <span className="text-muted-foreground">N/A</span>}
                  </TableCell>
                  <TableCell className="text-right space-x-2">
                    <Button variant="outline" size="icon">
                      <Eye className="h-4 w-4" />
                    </Button>
                    {item.status === "Completada" && (
                       <Button variant="outline" size="icon">
                        <CalendarPlus className="h-4 w-4" />
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}