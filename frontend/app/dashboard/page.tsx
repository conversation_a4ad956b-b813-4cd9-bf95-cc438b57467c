"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  ChevronLeft,
  ChevronRight,
  Clock,
  MapPin,
  Phone,
  Mail,
  Building,
  FileText,
  Truck,
  Wrench,
  FlaskConical,
  GraduationCap,
  UserCheck,
  ClipboardCheck,
  Quote,
  Package,
  Bell,
  User,
  LogOut,
  Check,
  Trash2,
  Download,
  ExternalLink,
  File,
  FileSpreadsheet,
  Calendar,
  ArrowRight,
} from "lucide-react"
import { cn } from "@/lib/utils"
import AnimatedList from "@/components/ui/animated-list"

interface Sale {
  id: number
  customer: string
  project: string
  value: number
  status: "pending" | "in_progress" | "completed" | "cancelled"
  department: string
  date: string
  progress: number
  responsible: string
  contact: {
    name: string
    email: string
    phone: string
  }
  address: string
  description: string
  timeline: {
    id: number
    date: string
    title: string
    description: string
    status: "completed" | "current" | "pending"
  }[]
  documents: {
    id: number
    name: string
    type: string
    date: string
    size: string
    fileType: "pdf" | "docx" | "xlsx"
    url: string
  }[]
}

interface CalendarEvent {
  id: number
  title: string
  date: string
  time: string
  department: string
  type: "delivery" | "installation" | "calibration" | "training" | "meeting" | "inspection" | "quotation" | "inventory"
  relatedSale?: number
  responsible: string
  status: "scheduled" | "confirmed" | "pending" | "completed" | "cancelled" | "draft"
  description?: string
}

interface Notification {
  id: number
  title: string
  message: string
  time: string
  type: "info" | "warning" | "success" | "error"
  read: boolean
}

const salesData: Sale[] = [
  {
    id: 1,
    customer: "Metro de Lima",
    project: "Sistema de Medición Línea 5",
    value: 850000,
    status: "in_progress",
    department: "logistics",
    date: "2024-03-15",
    progress: 75,
    responsible: "Carlos Ruiz",
    contact: {
      name: "Juan Pérez",
      email: "<EMAIL>",
      phone: "+51 1 234-5678",
    },
    address: "Av. Paseo de la República 123, Lima",
    description:
      "Implementación completa del sistema de medición automatizado para la nueva línea 5 del metro, incluyendo sensores de última generación, software de monitoreo en tiempo real y capacitación especializada del personal técnico.",
    timeline: [
      {
        id: 1,
        date: "2024-03-15",
        title: "Propuesta enviada",
        description: "Envío de propuesta técnica y comercial detallada",
        status: "completed",
      },
      {
        id: 2,
        date: "2024-03-20",
        title: "Aprobación cliente",
        description: "Cliente aprueba propuesta y firma contrato",
        status: "completed",
      },
      {
        id: 3,
        date: "2024-04-01",
        title: "Inicio proyecto",
        description: "Reunión de inicio y planificación detallada",
        status: "completed",
      },
      {
        id: 4,
        date: "2024-05-15",
        title: "Entrega equipos",
        description: "Entrega e instalación de equipos principales",
        status: "current",
      },
      {
        id: 5,
        date: "2024-06-01",
        title: "Capacitación",
        description: "Capacitación del personal técnico",
        status: "pending",
      },
      {
        id: 6,
        date: "2024-06-15",
        title: "Cierre proyecto",
        description: "Entrega final y cierre del proyecto",
        status: "pending",
      },
    ],
    documents: [
      {
        id: 1,
        name: "Contrato_Metro_Lima.pdf",
        type: "Contrato",
        date: "2024-03-20",
        size: "2.5 MB",
        fileType: "pdf",
        url: "/documents/contrato_metro_lima.pdf",
      },
      {
        id: 2,
        name: "Especificaciones_Tecnicas.pdf",
        type: "Especificaciones",
        date: "2024-03-15",
        size: "1.8 MB",
        fileType: "pdf",
        url: "/documents/especificaciones_tecnicas.pdf",
      },
      {
        id: 3,
        name: "Cronograma_Proyecto.xlsx",
        type: "Cronograma",
        date: "2024-04-01",
        size: "456 KB",
        fileType: "xlsx",
        url: "/documents/cronograma_proyecto.xlsx",
      },
      {
        id: 4,
        name: "Manual_Usuario.pdf",
        type: "Manual",
        date: "2024-05-10",
        size: "3.2 MB",
        fileType: "pdf",
        url: "/documents/manual_usuario.pdf",
      },
      {
        id: 5,
        name: "Propuesta_Tecnica.docx",
        type: "Propuesta",
        date: "2024-03-10",
        size: "1.2 MB",
        fileType: "docx",
        url: "/documents/propuesta_tecnica.docx",
      },
    ],
  },
  {
    id: 2,
    customer: "Hospital Nacional",
    project: "Calibración Equipos Médicos",
    value: 125000,
    status: "pending",
    department: "metrology",
    date: "2024-04-08",
    progress: 30,
    responsible: "Diana Torres",
    contact: {
      name: "María González",
      email: "<EMAIL>",
      phone: "+51 1 987-6543",
    },
    address: "Av. Grau 456, Lima",
    description:
      "Servicio especializado de calibración para equipos médicos críticos incluyendo monitores de signos vitales, ventiladores mecánicos y equipos de imagen diagnóstica.",
    timeline: [
      {
        id: 1,
        date: "2024-04-08",
        title: "Solicitud recibida",
        description: "Recepción de solicitud de calibración",
        status: "completed",
      },
      {
        id: 2,
        date: "2024-04-10",
        title: "Evaluación técnica",
        description: "Evaluación inicial de equipos",
        status: "current",
      },
      {
        id: 3,
        date: "2024-04-20",
        title: "Cronograma definido",
        description: "Definición de cronograma de calibración",
        status: "pending",
      },
      {
        id: 4,
        date: "2024-05-01",
        title: "Inicio calibración",
        description: "Inicio del proceso de calibración",
        status: "pending",
      },
    ],
    documents: [
      {
        id: 1,
        name: "Solicitud_Calibracion.pdf",
        type: "Solicitud",
        date: "2024-04-08",
        size: "1.2 MB",
        fileType: "pdf",
        url: "/documents/solicitud_calibracion.pdf",
      },
      {
        id: 2,
        name: "Inventario_Equipos.xlsx",
        type: "Inventario",
        date: "2024-04-10",
        size: "789 KB",
        fileType: "xlsx",
        url: "/documents/inventario_equipos.xlsx",
      },
    ],
  },
  {
    id: 3,
    customer: "AutoTech Peru",
    project: "Sistema de Control de Calidad",
    value: 320000,
    status: "completed",
    department: "quality",
    date: "2024-01-20",
    progress: 100,
    responsible: "Roberto Silva",
    contact: {
      name: "Carlos Mendoza",
      email: "<EMAIL>",
      phone: "+51 1 555-0123",
    },
    address: "Av. Industrial 789, Callao",
    description:
      "Implementación de sistema automatizado de control de calidad para línea de producción automotriz con tecnología de vanguardia.",
    timeline: [
      {
        id: 1,
        date: "2024-01-20",
        title: "Propuesta aprobada",
        description: "Aprobación de propuesta técnica",
        status: "completed",
      },
      {
        id: 2,
        date: "2024-02-01",
        title: "Instalación iniciada",
        description: "Inicio de instalación de equipos",
        status: "completed",
      },
      {
        id: 3,
        date: "2024-02-15",
        title: "Configuración software",
        description: "Configuración del software de control",
        status: "completed",
      },
      {
        id: 4,
        date: "2024-03-01",
        title: "Pruebas finales",
        description: "Pruebas y validación del sistema",
        status: "completed",
      },
      {
        id: 5,
        date: "2024-03-10",
        title: "Entrega final",
        description: "Entrega y cierre del proyecto",
        status: "completed",
      },
    ],
    documents: [
      {
        id: 1,
        name: "Proyecto_AutoTech.pdf",
        type: "Proyecto",
        date: "2024-01-20",
        size: "4.1 MB",
        fileType: "pdf",
        url: "/documents/proyecto_autotech.pdf",
      },
      {
        id: 2,
        name: "Manual_Sistema.pdf",
        type: "Manual",
        date: "2024-03-01",
        size: "2.8 MB",
        fileType: "pdf",
        url: "/documents/manual_sistema.pdf",
      },
      {
        id: 3,
        name: "Certificado_Calidad.pdf",
        type: "Certificado",
        date: "2024-03-10",
        size: "1.5 MB",
        fileType: "pdf",
        url: "/documents/certificado_calidad.pdf",
      },
    ],
  },
  {
    id: 4,
    customer: "Minera del Norte",
    project: "Mantenimiento Predictivo",
    value: 580000,
    status: "in_progress",
    department: "service",
    date: "2024-02-10",
    progress: 45,
    responsible: "Elena Vega",
    contact: {
      name: "Alejandro Ríos",
      email: "<EMAIL>",
      phone: "+51 1 444-5678",
    },
    address: "Carretera Norte Km 45, Trujillo",
    description:
      "Implementación de sistema de mantenimiento predictivo para equipos mineros críticos utilizando IoT y análisis de datos avanzado.",
    timeline: [
      {
        id: 1,
        date: "2024-02-10",
        title: "Contrato firmado",
        description: "Firma de contrato de servicios",
        status: "completed",
      },
      {
        id: 2,
        date: "2024-02-20",
        title: "Análisis inicial",
        description: "Análisis de equipos y condiciones",
        status: "completed",
      },
      {
        id: 3,
        date: "2024-03-15",
        title: "Instalación sensores",
        description: "Instalación de sensores de monitoreo",
        status: "current",
      },
      {
        id: 4,
        date: "2024-04-30",
        title: "Configuración sistema",
        description: "Configuración del sistema de monitoreo",
        status: "pending",
      },
    ],
    documents: [
      {
        id: 1,
        name: "Contrato_Minera.pdf",
        type: "Contrato",
        date: "2024-02-10",
        size: "3.7 MB",
        fileType: "pdf",
        url: "/documents/contrato_minera.pdf",
      },
      {
        id: 2,
        name: "Analisis_Equipos.pdf",
        type: "Análisis",
        date: "2024-02-20",
        size: "5.2 MB",
        fileType: "pdf",
        url: "/documents/analisis_equipos.pdf",
      },
      {
        id: 3,
        name: "Reporte_Sensores.xlsx",
        type: "Reporte",
        date: "2024-03-15",
        size: "892 KB",
        fileType: "xlsx",
        url: "/documents/reporte_sensores.xlsx",
      },
    ],
  },
  {
    id: 5,
    customer: "Textil Paracas",
    project: "Automatización Planta",
    value: 275000,
    status: "pending",
    department: "admin",
    date: "2024-04-15",
    progress: 15,
    responsible: "Sandra Torres",
    contact: {
      name: "Luis Herrera",
      email: "<EMAIL>",
      phone: "+51 1 333-9876",
    },
    address: "Av. Textil 321, Ica",
    description:
      "Proyecto de automatización integral para planta textil incluyendo control de procesos, calidad y gestión de inventarios.",
    timeline: [
      {
        id: 1,
        date: "2024-04-15",
        title: "Propuesta presentada",
        description: "Presentación de propuesta técnica",
        status: "completed",
      },
      {
        id: 2,
        date: "2024-04-25",
        title: "Negociación",
        description: "Negociación de términos y condiciones",
        status: "current",
      },
      {
        id: 3,
        date: "2024-05-10",
        title: "Aprobación esperada",
        description: "Esperando aprobación del cliente",
        status: "pending",
      },
    ],
    documents: [
      {
        id: 1,
        name: "Propuesta_Textil.pdf",
        type: "Propuesta",
        date: "2024-04-15",
        size: "2.9 MB",
        fileType: "pdf",
        url: "/documents/propuesta_textil.pdf",
      },
      {
        id: 2,
        name: "Cotizacion_Detallada.docx",
        type: "Cotización",
        date: "2024-04-16",
        size: "1.1 MB",
        fileType: "docx",
        url: "/documents/cotizacion_detallada.docx",
      },
    ],
  },
  {
    id: 6,
    customer: "Farmacéutica Central",
    project: "Validación Equipos",
    value: 95000,
    status: "cancelled",
    department: "quality",
    date: "2024-03-01",
    progress: 0,
    responsible: "Miguel Herrera",
    contact: {
      name: "Andrea Silva",
      email: "<EMAIL>",
      phone: "+51 1 222-3456",
    },
    address: "Av. Farmacia 654, Lima",
    description: "Servicio de validación de equipos farmacéuticos según normativas internacionales GMP y FDA.",
    timeline: [
      {
        id: 1,
        date: "2024-03-01",
        title: "Propuesta enviada",
        description: "Envío de propuesta técnica",
        status: "completed",
      },
      {
        id: 2,
        date: "2024-03-15",
        title: "Proyecto cancelado",
        description: "Cliente cancela proyecto por cambios internos",
        status: "completed",
      },
    ],
    documents: [
      {
        id: 1,
        name: "Propuesta_Farmaceutica.pdf",
        type: "Propuesta",
        date: "2024-03-01",
        size: "1.8 MB",
        fileType: "pdf",
        url: "/documents/propuesta_farmaceutica.pdf",
      },
    ],
  },
]

const calendarEvents: CalendarEvent[] = [
  {
    id: 1,
    title: "Entrega Proyecto Metro Line 5",
    date: "2024-07-15",
    time: "09:00",
    department: "logistics",
    type: "delivery",
    relatedSale: 1,
    responsible: "Carlos Ruiz",
    status: "scheduled",
    description: "Entrega e instalación de equipos principales del sistema de medición",
  },
  {
    id: 2,
    title: "Instalación Hospital Central",
    date: "2024-07-18",
    time: "14:00",
    department: "service",
    type: "installation",
    relatedSale: 2,
    responsible: "Diego Morales",
    status: "confirmed",
    description: "Instalación de equipos médicos calibrados",
  },
  {
    id: 3,
    title: "Calibración equipos AutoTech",
    date: "2024-07-20",
    time: "10:30",
    department: "metrology",
    type: "calibration",
    relatedSale: 3,
    responsible: "Roberto Silva",
    status: "pending",
    description: "Calibración final de equipos de control de calidad",
  },
  {
    id: 4,
    title: "Training personal Metro",
    date: "2024-07-22",
    time: "08:00",
    department: "hr",
    type: "training",
    relatedSale: 1,
    responsible: "Sandra Torres",
    status: "scheduled",
    description: "Capacitación del personal técnico en el uso del sistema",
  },
  {
    id: 5,
    title: "Reunión seguimiento Hospital",
    date: "2024-07-25",
    time: "11:00",
    department: "admin",
    type: "meeting",
    relatedSale: 2,
    responsible: "Laura Díaz",
    status: "confirmed",
    description: "Reunión de seguimiento del proyecto de calibración",
  },
  {
    id: 6,
    title: "Inspección final AutoTech",
    date: "2024-07-28",
    time: "15:00",
    department: "quality",
    type: "inspection",
    relatedSale: 3,
    responsible: "Elena Vega",
    status: "scheduled",
    description: "Inspección final del sistema de control de calidad",
  },
  {
    id: 7,
    title: "Cotización nueva oportunidad",
    date: "2024-07-30",
    time: "16:00",
    department: "sales",
    type: "quotation",
    relatedSale: undefined,
    responsible: "Ana García",
    status: "draft",
    description: "Preparación de cotización para nuevo cliente potencial",
  },
  {
    id: 8,
    title: "Verificación inventario",
    date: "2024-07-12",
    time: "09:30",
    department: "warehouse",
    type: "inventory",
    relatedSale: 1,
    responsible: "Maria López",
    status: "completed",
    description: "Verificación de inventario para proyecto Metro",
  },
  {
    id: 9,
    title: "Entrega equipos Minera",
    date: "2024-07-08",
    time: "13:00",
    department: "logistics",
    type: "delivery",
    relatedSale: 4,
    responsible: "Carlos Ruiz",
    status: "completed",
    description: "Entrega de sensores para mantenimiento predictivo",
  },
  {
    id: 10,
    title: "Reunión Textil Paracas",
    date: "2024-07-10",
    time: "10:00",
    department: "admin",
    type: "meeting",
    relatedSale: 5,
    responsible: "Sandra Torres",
    status: "completed",
    description: "Reunión de negociación con cliente textil",
  },
]

const initialNotifications: Notification[] = [
  {
    id: 1,
    title: "Nuevo proyecto asignado",
    message: "Se te ha asignado el proyecto Metro de Lima - Sistema de Medición",
    time: "Hace 5 min",
    type: "info",
    read: false,
  },
  {
    id: 2,
    title: "Entrega programada",
    message: "La entrega del proyecto AutoTech está programada para mañana",
    time: "Hace 1 hora",
    type: "warning",
    read: false,
  },
  {
    id: 3,
    title: "Proyecto completado",
    message: "El proyecto de calibración del Hospital Nacional ha sido completado",
    time: "Hace 2 horas",
    type: "success",
    read: true,
  },
  {
    id: 4,
    title: "Reunión cancelada",
    message: "La reunión con Textil Paracas ha sido cancelada",
    time: "Hace 3 horas",
    type: "error",
    read: false,
  },
  {
    id: 5,
    title: "Documento actualizado",
    message: "Se ha actualizado el contrato del proyecto Minera del Norte",
    time: "Ayer",
    type: "info",
    read: true,
  },
]

const departmentColors = {
  sales: "bg-blue-500",
  admin: "bg-green-500",
  warehouse: "bg-orange-500",
  logistics: "bg-purple-500",
  quality: "bg-red-500",
  metrology: "bg-cyan-500",
  service: "bg-yellow-500",
  hr: "bg-pink-500",
}

const departmentNames = {
  sales: "Ventas",
  admin: "Administración",
  warehouse: "Almacén",
  logistics: "Logística",
  quality: "Calidad",
  metrology: "Metrología",
  service: "Servicio",
  hr: "RRHH",
}

const statusColors = {
  pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
  in_progress: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
  completed: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
  cancelled: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
}

const statusNames = {
  pending: "Pendiente",
  in_progress: "En Progreso",
  completed: "Completado",
  cancelled: "Cancelado",
}

const eventTypeIcons = {
  delivery: Truck,
  installation: Wrench,
  calibration: FlaskConical,
  training: GraduationCap,
  meeting: UserCheck,
  inspection: ClipboardCheck,
  quotation: Quote,
  inventory: Package,
}

const eventTypeNames = {
  delivery: "Entrega",
  installation: "Instalación",
  calibration: "Calibración",
  training: "Capacitación",
  meeting: "Reunión",
  inspection: "Inspección",
  quotation: "Cotización",
  inventory: "Inventario",
}

const eventStatusColors = {
  scheduled: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
  confirmed: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
  pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
  completed: "bg-gray-100 text-gray-800 dark:bg-gray-800/30 dark:text-gray-300",
  cancelled: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
  draft: "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
}

const eventStatusNames = {
  scheduled: "Programado",
  confirmed: "Confirmado",
  pending: "Pendiente",
  completed: "Completado",
  cancelled: "Cancelado",
  draft: "Borrador",
}

const notificationTypeColors = {
  info: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
  warning: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
  success: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
  error: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
}

const fileTypeColors = {
  pdf: "text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-900/20",
  docx: "text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/20",
  xlsx: "text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/20",
}

const fileTypeIcons = {
  pdf: FileText,
  docx: File,
  xlsx: FileSpreadsheet,
}

const MONTHS = [
  "Enero",
  "Febrero",
  "Marzo",
  "Abril",
  "Mayo",
  "Junio",
  "Julio",
  "Agosto",
  "Septiembre",
  "Octubre",
  "Noviembre",
  "Diciembre",
]

const WEEKDAYS = ["Dom", "Lun", "Mar", "Mié", "Jue", "Vie", "Sáb"]

export default function SalesDashboard() {
  const [selectedSale, setSelectedSale] = useState<Sale | null>(null)
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [departmentFilter, setDepartmentFilter] = useState("all")
  const [currentDate, setCurrentDate] = useState(new Date())
  const [calendarDepartmentFilter, setCalendarDepartmentFilter] = useState("all")
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [notifications, setNotifications] = useState<Notification[]>(initialNotifications)
  const [notificationsOpen, setNotificationsOpen] = useState(false)

  const filteredSales = salesData.filter((sale) => {
    const matchesSearch =
      sale.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      sale.project.toLowerCase().includes(searchTerm.toLowerCase()) ||
      sale.responsible.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || sale.status === statusFilter
    const matchesDepartment = departmentFilter === "all" || sale.department === departmentFilter

    return matchesSearch && matchesStatus && matchesDepartment
  })

  const unreadNotifications = notifications.filter((n) => !n.read).length

  const markAsRead = (id: number) => {
    setNotifications((prev) => prev.map((n) => (n.id === id ? { ...n, read: true } : n)))
  }

  const deleteNotification = (id: number) => {
    setNotifications((prev) => prev.filter((n) => n.id !== id))
  }

  const handleDocumentAction = (doc: Sale["documents"][0], action: "open" | "download") => {
    if (action === "open") {
      window.open(doc.url, "_blank")
    } else {
      // Create a temporary link to download the file
      const link = document.createElement("a")
      link.href = doc.url
      link.download = doc.name
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const startDate = new Date(firstDay)
    startDate.setDate(startDate.getDate() - firstDay.getDay())

    const days = []
    const current = new Date(startDate)

    for (let i = 0; i < 42; i++) {
      days.push(new Date(current))
      current.setDate(current.getDate() + 1)
    }

    return days
  }

  const getEventsForDate = (date: Date) => {
    const dateString = date.toISOString().split("T")[0]
    return calendarEvents.filter((event) => {
      const eventDate = event.date
      const matchesDate = eventDate === dateString
      const matchesDepartment = calendarDepartmentFilter === "all" || event.department === calendarDepartmentFilter
      return matchesDate && matchesDepartment
    })
  }

  const navigateMonth = (direction: "prev" | "next") => {
    const newDate = new Date(currentDate)
    if (direction === "prev") {
      newDate.setMonth(newDate.getMonth() - 1)
    } else {
      newDate.setMonth(newDate.getMonth() + 1)
    }
    setCurrentDate(newDate)
  }

  const isToday = (date: Date) => {
    const today = new Date()
    return date.toDateString() === today.toDateString()
  }

  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentDate.getMonth()
  }

  const selectedDateEvents = selectedDate ? getEventsForDate(selectedDate) : []

  const SaleDetailModal = ({ sale }: { sale: Sale }) => (
    <DialogContent
      className={cn(
        "max-w-6xl max-h-[90vh] overflow-y-auto backdrop-blur-xl border shadow-2xl",
      )}
    >
      <DialogHeader>
        <DialogTitle
          className={cn("flex items-center gap-3 font-light text-2xl")}
        >
          <div
            className={cn("w-4 h-4 rounded-full", departmentColors[sale.department as keyof typeof departmentColors])}
          />
          {sale.customer}
          <span className={cn("text-lg font-extralight")}>
            — {sale.project}
          </span>
        </DialogTitle>
      </DialogHeader>

      <Tabs defaultValue="details" className="w-full">
        <TabsList className={cn("grid w-full grid-cols-4")}>
          <TabsTrigger value="details" className="font-light">
            Detalles
          </TabsTrigger>
          <TabsTrigger value="timeline" className="font-light">
            Timeline
          </TabsTrigger>
          <TabsTrigger value="documents" className="font-light">
            Documentos
          </TabsTrigger>
          <TabsTrigger value="contact" className="font-light">
            Contacto
          </TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6 mt-6">
          <div
            className="gap-6"
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(min(100%, 300px), 1fr))'
            }}
          >
            <Card
              className={cn(
                "backdrop-blur-md border shadow-lg bg-gray-200/40 dark:bg-slate-800/40 border-gray-300/30 dark:border-slate-700/30",
              )}
            >
              <CardHeader className="pb-4">
                <CardTitle
                  className={cn(
                    "text-lg font-light flex items-center gap-2",
                  )}
                >
                  <Building className="w-5 h-5" />
                  Información General
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <label className={cn("text-sm font-medium")}>
                      Cliente
                    </label>
                    <p className={cn("text-base font-light mt-1")}>
                      {sale.customer}
                    </p>
                  </div>
                  <div>
                    <label className={cn("text-sm font-medium")}>
                      Proyecto
                    </label>
                    <p className={cn("text-base font-light mt-1")}>
                      {sale.project}
                    </p>
                  </div>
                  <div>
                    <label className={cn("text-sm font-medium")}>
                      Valor del Proyecto
                    </label>
                    <p className="text-2xl font-light text-green-600 mt-1">${sale.value.toLocaleString()}</p>
                  </div>
                  <div>
                    <label className={cn("text-sm font-medium")}>
                      Estado
                    </label>
                    <div className="mt-1">
                      <Badge className={cn("text-sm font-light px-3 py-1", statusColors[sale.status])}>
                        {statusNames[sale.status]}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card
              className={cn(
                "backdrop-blur-md border shadow-lg bg-gray-200/40 dark:bg-slate-800/40 border-gray-300/30 dark:border-slate-700/30",
              )}
            >
              <CardHeader className="pb-4">
                <CardTitle
                  className={cn(
                    "text-lg font-light flex items-center gap-2",
                  )}
                >
                  <UserCheck className="w-5 h-5" />
                  Gestión del Proyecto
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className={cn("text-sm font-medium")}>
                    Departamento
                  </label>
                  <div className="flex items-center gap-2 mt-1">
                    <div
                      className={cn(
                        "w-3 h-3 rounded-full",
                        departmentColors[sale.department as keyof typeof departmentColors],
                      )}
                    />
                    <span className={cn("text-base font-light")}>
                      {departmentNames[sale.department as keyof typeof departmentNames]}
                    </span>
                  </div>
                </div>
                <div>
                  <label className={cn("text-sm font-medium")}>
                    Responsable
                  </label>
                  <p className={cn("text-base font-light mt-1")}>
                    {sale.responsible}
                  </p>
                </div>
                <div>
                  <label className={cn("text-sm font-medium")}>
                    Fecha de Inicio
                  </label>
                  <p className={cn("text-base font-light mt-1")}>
                    {sale.date}
                  </p>
                </div>
                <div>
                  <label className={cn("text-sm font-medium")}>
                    Progreso del Proyecto
                  </label>
                  <div className="mt-2 space-y-2">
                    <div className="flex items-center justify-between">
                      <span className={cn("text-2xl font-light")}>
                        {sale.progress}%
                      </span>
                      <span className={cn("text-sm font-light")}>
                        Completado
                      </span>
                    </div>
                    <div className={cn("w-full rounded-full h-3")}>
                      <div
                        className="bg-gradient-to-r from-moka-falu to-red-600 h-3 rounded-full transition-all duration-500 ease-out"
                        style={{ width: `${sale.progress}%` }}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card
              className={cn(
                "backdrop-blur-md border shadow-lg bg-gray-200/40 dark:bg-slate-800/40 border-gray-300/30 dark:border-slate-700/30",
              )}
            >
              <CardHeader className="pb-4">
                <CardTitle
                  className={cn(
                    "text-lg font-light flex items-center gap-2",
                  )}
                >
                  <FileText className="w-5 h-5" />
                  Resumen Rápido
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className={cn("p-3 rounded-lg")}>
                    <p className={cn("text-xs font-medium")}>
                      DOCUMENTOS
                    </p>
                    <p className={cn("text-xl font-light")}>
                      {sale.documents.length}
                    </p>
                  </div>
                  <div className={cn("p-3 rounded-lg")}>
                    <p className={cn("text-xs font-medium")}>HITOS</p>
                    <p className={cn("text-xl font-light")}>
                      {sale.timeline.length}
                    </p>
                  </div>
                </div>
                <div
                  className={cn(
                    "p-4 rounded-lg border-l-4 border-blue-500",
                  )}
                >
                  <p className={cn("text-sm font-medium mb-1")}>
                    Próximo Hito
                  </p>
                  <p className={cn("text-sm font-light")}>
                    {sale.timeline.find((t) => t.status === "current")?.title || "No definido"}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card
            className={cn(
              "backdrop-blur-md border shadow-lg bg-gray-200/40 dark:bg-slate-800/40 border-gray-300/30 dark:border-slate-700/30",
            )}
          >
            <CardHeader className="pb-4">
              <CardTitle
                className={cn(
                  "text-lg font-light flex items-center gap-2",
                )}
              >
                <FileText className="w-5 h-5" />
                Descripción del Proyecto
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className={cn("text-base font-light leading-relaxed")}>
                {sale.description}
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-6 mt-6">
          <Card
            className={cn(
              "backdrop-blur-md border shadow-lg bg-gray-200/40 dark:bg-slate-800/40 border-gray-300/30 dark:border-slate-700/30",
            )}
          >
            <CardHeader className="pb-6">
              <CardTitle
                className={cn(
                  "text-xl font-light flex items-center gap-2",
                )}
              >
                <Calendar className="w-6 h-6" />
                Timeline del Proyecto
              </CardTitle>
            </CardHeader>
            <CardContent>
              {/* Horizontal Timeline */}
              <div className="relative">
                <div className="flex items-center justify-between mb-8">
                  {sale.timeline.map((event, index) => (
                    <div key={event.id} className="flex flex-col items-center relative group">
                      {/* Timeline Line */}
                      {index < sale.timeline.length - 1 && (
                        <div
                          className={cn(
                            "absolute top-6 left-6 h-0.5 transition-all duration-500",
                            event.status === "completed"
                              ? "bg-green-500"
                              : event.status === "current"
                                ? "bg-blue-500"
                                : "bg-gray-300",
                          )}
                          style={{ width: `calc(${100 / (sale.timeline.length - 1)}vw - 3rem)` }}
                        />
                      )}

                      {/* Timeline Node */}
                      <div
                        className={cn(
                          "w-12 h-12 rounded-full border-4 flex items-center justify-center transition-all duration-300 hover:scale-110 z-10",
                          event.status === "completed" && "bg-green-500 border-green-500 shadow-lg shadow-green-500/30",
                          event.status === "current" &&
                            "bg-blue-500 border-blue-500 shadow-lg shadow-blue-500/30 animate-pulse",
                          event.status === "pending" &&
                            "bg-muted border-border",
                        )}
                      >
                        {event.status === "completed" && <Check className="w-6 h-6 text-white" />}
                        {event.status === "current" && <Clock className="w-6 h-6 text-white" />}
                        {event.status === "pending" && (
                          <div className={cn("w-3 h-3 rounded-full", "bg-gray-400")} />
                        )}
                      </div>

                      {/* Event Info */}
                      <div className="mt-4 text-center max-w-32">
                        <h4 className={cn("font-medium text-sm mb-1")}>
                          {event.title}
                        </h4>
                        <p className={cn("text-xs font-light mb-2")}>
                          {event.date}
                        </p>
                        <p
                          className={cn(
                            "text-xs font-light leading-tight",
                          )}
                        >
                          {event.description}
                        </p>
                      </div>

                      {/* Hover Card */}
                      <div
                        className={cn(
                          "absolute top-16 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-20 p-3 rounded-lg shadow-xl border",
                          "bg-card border-border",
                        )}
                      >
                        <div className="text-center">
                          <h5 className={cn("font-medium text-sm")}>
                            {event.title}
                          </h5>
                          <p className={cn("text-xs mt-1")}>
                            {event.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Progress Bar */}
                <div className="mt-8">
                  <div className="flex items-center justify-between mb-2">
                    <span className={cn("text-sm font-medium")}>
                      Progreso General
                    </span>
                    <span className={cn("text-sm font-light")}>
                      {sale.progress}%
                    </span>
                  </div>
                  <div className={cn("w-full rounded-full h-2")}>
                    <div
                      className="bg-gradient-to-r from-moka-falu to-red-600 h-2 rounded-full transition-all duration-1000 ease-out"
                      style={{ width: `${sale.progress}%` }}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-6 mt-6">
          <Card
            className={cn(
              "backdrop-blur-md border shadow-lg bg-gray-200/40 dark:bg-slate-800/40 border-gray-300/30 dark:border-slate-700/30",
            )}
          >
            <CardHeader className="pb-6">
              <CardTitle
                className={cn(
                  "text-xl font-light flex items-center gap-2",
                )}
              >
                <FileText className="w-6 h-6" />
                Documentos del Proyecto
                <Badge variant="outline" className="ml-2">
                  {sale.documents.length} archivos
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div
                className="gap-4"
                style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(min(100%, 250px), 1fr))'
                }}
              >
                {sale.documents.map((doc) => {
                  const IconComponent = fileTypeIcons[doc.fileType]
                  return (
                    <div
                      key={doc.id}
                      className={cn(
                        "group flex items-center gap-4 p-4 border rounded-xl transition-all duration-200 hover:shadow-lg",
                        "border-border hover:bg-muted/20",
                      )}
                    >
                      <div className={cn("p-3 rounded-lg", fileTypeColors[doc.fileType])}>
                        <IconComponent className="w-6 h-6" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4
                          className={cn("font-medium text-sm truncate")}
                        >
                          {doc.name}
                        </h4>
                        <div
                          className={cn(
                            "flex items-center gap-4 text-xs font-light mt-1",
                          )}
                        >
                          <span className="flex items-center gap-1">
                            <Badge variant="outline" className="text-xs">
                              {doc.type}
                            </Badge>
                          </span>
                          <span>{doc.date}</span>
                          <span>{doc.size}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDocumentAction(doc, "open")}
                          className="h-8 w-8 p-0"
                          title="Abrir documento"
                        >
                          <ExternalLink className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDocumentAction(doc, "download")}
                          className="h-8 w-8 p-0"
                          title="Descargar documento"
                        >
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="contact" className="space-y-6 mt-6">
          <div
            className="gap-6"
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(min(100%, 350px), 1fr))'
            }}
          >
            <Card
              className={cn(
                "backdrop-blur-md border shadow-lg bg-gray-200/40 dark:bg-slate-800/40 border-gray-300/30 dark:border-slate-700/30",
              )}
            >
              <CardHeader className="pb-4">
                <CardTitle
                  className={cn(
                    "text-lg font-light flex items-center gap-2",
                  )}
                >
                  <User className="w-5 h-5" />
                  Información de Contacto
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className={cn("text-sm font-medium")}>
                    Contacto Principal
                  </label>
                  <p className={cn("text-lg font-light mt-1")}>
                    {sale.contact.name}
                  </p>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className={cn("p-2 rounded-lg")}>
                      <Mail className={cn("w-4 h-4")} />
                    </div>
                    <div>
                      <p className={cn("text-xs font-medium")}>EMAIL</p>
                      <a
                        href={`mailto:${sale.contact.email}`}
                        className="text-sm font-light text-blue-600 hover:underline"
                      >
                        {sale.contact.email}
                      </a>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className={cn("p-2 rounded-lg")}>
                      <Phone className={cn("w-4 h-4")} />
                    </div>
                    <div>
                      <p className={cn("text-xs font-medium")}>
                        TELÉFONO
                      </p>
                      <a
                        href={`tel:${sale.contact.phone}`}
                        className="text-sm font-light text-blue-600 hover:underline"
                      >
                        {sale.contact.phone}
                      </a>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className={cn("p-2 rounded-lg")}>
                      <MapPin className={cn("w-4 h-4")} />
                    </div>
                    <div>
                      <p className={cn("text-xs font-medium")}>
                        DIRECCIÓN
                      </p>
                      <p className={cn("text-sm font-light")}>
                        {sale.address}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card
              className={cn(
                "backdrop-blur-md border shadow-lg bg-gray-200/40 dark:bg-slate-800/40 border-gray-300/30 dark:border-slate-700/30",
              )}
            >
              <CardHeader className="pb-4">
                <CardTitle
                  className={cn(
                    "text-lg font-light flex items-center gap-2",
                  )}
                >
                  <Building className="w-5 h-5" />
                  Acciones Rápidas
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full justify-start gap-3 font-light bg-transparent" variant="outline">
                  <Mail className="w-4 h-4" />
                  Enviar Email
                </Button>
                <Button className="w-full justify-start gap-3 font-light bg-transparent" variant="outline">
                  <Phone className="w-4 h-4" />
                  Llamar Cliente
                </Button>
                <Button className="w-full justify-start gap-3 font-light bg-transparent" variant="outline">
                  <Calendar className="w-4 h-4" />
                  Programar Reunión
                </Button>
                <Button className="w-full justify-start gap-3 font-light bg-transparent" variant="outline">
                  <FileText className="w-4 h-4" />
                  Generar Reporte
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </DialogContent>
  )

  const EventDetailModal = ({ event }: { event: CalendarEvent }) => {
    const relatedSale = event.relatedSale ? salesData.find((s) => s.id === event.relatedSale) : null
    const IconComponent = eventTypeIcons[event.type]

    return (
      <DialogContent
        className={cn(
          "max-w-2xl backdrop-blur-xl border shadow-2xl",
        )}
      >
        <DialogHeader>
          <DialogTitle
            className={cn("flex items-center gap-2 font-light")}
          >
            <div
              className={cn(
                "p-2 rounded-full text-white",
                departmentColors[event.department as keyof typeof departmentColors],
              )}
            >
              <IconComponent className="w-4 h-4" />
            </div>
            {event.title}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className={cn("text-sm font-light")}>Fecha</label>
              <p className={cn("text-sm font-light")}>
                {new Date(event.date).toLocaleDateString("es-ES", {
                  weekday: "long",
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </p>
            </div>
            <div>
              <label className={cn("text-sm font-light")}>Hora</label>
              <p className={cn("text-sm font-light")}>{event.time}</p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className={cn("text-sm font-light")}>
                Departamento
              </label>
              <div className="flex items-center gap-2">
                <div
                  className={cn(
                    "w-3 h-3 rounded-full",
                    departmentColors[event.department as keyof typeof departmentColors],
                  )}
                />
                <span className={cn("text-sm font-light")}>
                  {departmentNames[event.department as keyof typeof departmentNames]}
                </span>
              </div>
            </div>
            <div>
              <label className={cn("text-sm font-light")}>Tipo</label>
              <p className={cn("text-sm font-light")}>
                {eventTypeNames[event.type]}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className={cn("text-sm font-light")}>
                Responsable
              </label>
              <p className={cn("text-sm font-light")}>
                {event.responsible}
              </p>
            </div>
            <div>
              <label className={cn("text-sm font-light")}>Estado</label>
              <Badge className={cn("text-xs font-light", eventStatusColors[event.status])}>
                {eventStatusNames[event.status]}
              </Badge>
            </div>
          </div>

          {event.description && (
            <div>
              <label className={cn("text-sm font-light")}>
                Descripción
              </label>
              <p className={cn("text-sm font-light")}>
                {event.description}
              </p>
            </div>
          )}

          {relatedSale && (
            <div>
              <label className={cn("text-sm font-light")}>
                Venta Relacionada
              </label>
              <div className="flex items-center gap-2 mt-1">
                <Button
                  variant="outline"
                  size="sm"
                  className="font-light bg-transparent"
                  onClick={() => {
                    setSelectedEvent(null)
                    setSelectedSale(relatedSale)
                  }}
                >
                  <Building className="w-4 h-4 mr-2" />
                  {relatedSale.customer} - {relatedSale.project}
                </Button>
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" className="font-light bg-transparent" onClick={() => setSelectedEvent(null)}>
            Cerrar
          </Button>
          <Button className="font-light">Editar Evento</Button>
        </div>
      </DialogContent>
    )
  }

  return (
    <div
      className={cn(
        "flex flex-col h-screen w-full transition-colors duration-500 overflow-hidden"
      )}
      style={{ height: '100vh', maxHeight: '100vh' }}
    >
      <style jsx global>{`
        * {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Inter', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
          font-weight: 300;
          letter-spacing: -0.01em;
        }
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
        @keyframes slideInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        @keyframes slideInLeft {
          from {
            opacity: 0;
            transform: translateX(-20px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }
        @keyframes fadeInScale {
          from {
            opacity: 0;
            transform: scale(0.95);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }
        .animate-slide-up {
          animation: slideInUp 0.6s ease-out;
        }
        .animate-slide-left {
          animation: slideInLeft 0.6s ease-out;
        }
        .animate-fade-scale {
          animation: fadeInScale 0.5s ease-out;
        }
        html, body {
          overflow: hidden;
          height: 100vh;
          max-height: 100vh;
        }
      `}</style>
      <div
        className="flex-1 flex flex-col px-3 md:px-6 pt-4 pb-2 overflow-hidden"
        style={{ height: 'calc(100vh - 64px)', maxHeight: 'calc(100vh - 64px)' }}
      >
        {/* Sales Cards - Horizontal Scroll */}
        <div className="mb-4 animate-slide-up flex-shrink-0">
          <div className="flex items-center justify-between mb-3">
            <h2 className={cn("text-xl font-light tracking-wide transition-colors duration-300")}>Proyectos Activos</h2>
            <div className="flex items-center gap-4">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger
                  className={cn(
                    "w-48 backdrop-blur-xl border rounded-full shadow-lg font-light transition-colors duration-300",
                    "bg-background/30 border-border text-foreground",
                  )}
                >
                  <SelectValue placeholder="Filtrar por estado" />
                </SelectTrigger>
                <SelectContent
                  className={cn(
                    "backdrop-blur-xl border",
                    "bg-background/95 border-border",
                  )}
                >
                  <SelectItem value="all" className="font-light">
                    Todos los estados
                  </SelectItem>
                  <SelectItem value="pending" className="font-light">
                    Pendiente
                  </SelectItem>
                  <SelectItem value="in_progress" className="font-light">
                    En Progreso
                  </SelectItem>
                  <SelectItem value="completed" className="font-light">
                    Completado
                  </SelectItem>
                  <SelectItem value="cancelled" className="font-light">
                    Cancelado
                  </SelectItem>
                </SelectContent>
              </Select>
              <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                <SelectTrigger
                  className={cn(
                    "w-48 backdrop-blur-xl border rounded-full shadow-lg font-light transition-colors duration-300",
                    "bg-background/30 border-border text-foreground",
                  )}
                >
                  <SelectValue placeholder="Filtrar departamento" />
                </SelectTrigger>
                <SelectContent
                  className={cn(
                    "backdrop-blur-xl border",
                    "bg-background/95 border-border",
                  )}
                >
                  <SelectItem value="all" className="font-light">
                    Todos los departamentos
                  </SelectItem>
                  {Object.entries(departmentNames).map(([key, name]) => (
                    <SelectItem key={key} value={key} className="font-light">
                      {name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="overflow-x-auto scrollbar-hide pb-2 -mx-2">
            <div className="flex gap-4 px-2">
              {filteredSales.map((sale) => (
                <Dialog key={sale.id}>
                  <DialogTrigger asChild>
                    <div
                      className="group min-w-[220px] sm:min-w-[250px] max-w-[220px] sm:max-w-[250px] bg-gray-200/30 dark:bg-slate-800/30 backdrop-blur-xl rounded-2xl p-2 sm:p-3 flex flex-col gap-2 cursor-pointer transition-all duration-300 hover:bg-gray-300/40 dark:hover:bg-slate-700/40 hover:scale-[1.02] hover:shadow-xl shadow-lg border border-gray-300/30 dark:border-slate-700/30 animate-fade-scale"
                      onClick={() => setSelectedSale(sale)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className={`w-3 h-3 rounded-full ${departmentColors[sale.department as keyof typeof departmentColors]}`}></div>
                          <span className={`px-2 py-1 rounded-full text-xs font-light ${statusColors[sale.status]}`}>{statusNames[sale.status]}</span>
                        </div>
                        <ArrowRight className="w-4 h-4 transition-transform duration-200 group-hover:translate-x-1 text-gray-500 dark:text-slate-400" />
                      </div>
                      <h3 className="text-lg sm:text-xl font-light tracking-wide text-gray-900 dark:text-slate-100 line-clamp-2">{sale.customer}</h3>
                      <p className="text-xs sm:text-sm font-light text-gray-600 dark:text-slate-400 line-clamp-2">{sale.project}</p>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-light text-gray-600 dark:text-slate-400">Valor</span>
                          <span className="text-xl font-light text-green-600 dark:text-green-400">${sale.value.toLocaleString()}</span>
                        </div>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-light text-gray-600 dark:text-slate-400">Progreso</span>
                            <span className="text-sm font-light text-gray-800 dark:text-slate-200">{sale.progress}%</span>
                          </div>
                          <div className="w-full rounded-full h-2 bg-gray-300/50 dark:bg-slate-700/50 overflow-hidden">
                            <div
                              className="bg-gradient-to-r from-moka-falu to-red-600 h-2 rounded-full transition-all duration-1000 ease-out group-hover:animate-pulse-glow"
                              style={{ width: `${sale.progress}%` }}
                            />
                          </div>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="font-light text-gray-600 dark:text-slate-400">Responsable</span>
                          <span className="font-light text-gray-800 dark:text-slate-200">{sale.responsible}</span>
                        </div>
                      </div>
                    </div>
                  </DialogTrigger>
                  {selectedSale && selectedSale.id === sale.id && <SaleDetailModal sale={selectedSale} />}
                </Dialog>
              ))}
            </div>
          </div>
        </div>
        {/* Calendar Section - Responsive Height */}
        <div className="gap-4 lg:gap-6 animate-slide-left">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 lg:gap-6">
            {/* En móvil: calendario más pequeño, en desktop: 3/4 del espacio */}
            {/* Calendar - Left Side */}
            <div className="lg:col-span-3 h-64 lg:h-80">
            <Card className={cn("backdrop-blur-xl border rounded-2xl shadow-lg transition-colors duration-300 flex flex-col h-full", "bg-gray-200/30 dark:bg-slate-800/30 border-gray-300/30 dark:border-slate-700/30")}>
              <CardHeader className="pb-2 flex-shrink-0">
                <div className="flex items-center justify-between">
                  <CardTitle
                    className={cn(
                      "text-2xl font-light tracking-wide transition-colors duration-300",
                    )}
                  >
                    Calendario Empresarial
                  </CardTitle>
                  <div className="flex items-center gap-3">
                    <Select value={calendarDepartmentFilter} onValueChange={setCalendarDepartmentFilter}>
                      <SelectTrigger
                        className={cn(
                          "w-40 backdrop-blur-xl border rounded-full shadow-lg font-light transition-colors duration-300",
                          "bg-background/30 border-border text-foreground",
                        )}
                      >
                        <SelectValue placeholder="Filtrar departamento" />
                      </SelectTrigger>
                      <SelectContent
                        className={cn(
                          "backdrop-blur-xl border",
                          "bg-background/95 border-border",
                        )}
                      >
                        <SelectItem value="all" className="font-light">
                          Todos los departamentos
                        </SelectItem>
                        {Object.entries(departmentNames).map(([key, name]) => (
                          <SelectItem key={key} value={key} className="font-light">
                            {name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => navigateMonth("prev")}
                        className={cn(
                          "rounded-full backdrop-blur-md border font-light transition-colors duration-300",
                          "bg-background/30 border-border hover:bg-background/50",
                        )}
                      >
                        <ChevronLeft className="w-4 h-4" />
                      </Button>
                      <h3
                        className={cn(
                          "font-light text-lg transition-colors duration-300 min-w-[140px] text-center",
                        )}
                      >
                        {MONTHS[currentDate.getMonth()]} {currentDate.getFullYear()}
                      </h3>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => navigateMonth("next")}
                        className={cn(
                          "rounded-full backdrop-blur-md border font-light transition-colors duration-300",
                          "bg-background/30 border-border hover:bg-background/50",
                        )}
                      >
                        <ChevronRight className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pb-2 flex-1 flex flex-col min-h-0">
                {/* Calendar Grid */}
                <div className="grid grid-cols-7 gap-1 mb-1 flex-shrink-0">
                  {WEEKDAYS.map((day) => (
                    <div key={day} className={cn("p-1.5 text-center text-sm font-medium transition-colors duration-300")}>{day}</div>
                  ))}
                  {getDaysInMonth(currentDate).map((date, index) => {
                    const dayEvents = getEventsForDate(date)
                    const isCurrentMonthDate = isCurrentMonth(date)
                    const isTodayDate = isToday(date)

                    return (
                      <div
                        key={index}
                        className={cn(
                          "p-1 min-h-[32px] border rounded-lg cursor-pointer transition-all duration-200 backdrop-blur-sm hover:bg-background/50 hover:scale-105",
                          !isCurrentMonthDate &&
                            "bg-gray-200/10 text-gray-400",
                          isTodayDate && "bg-blue-100/60 border-blue-200 dark:bg-blue-900/30",
                          selectedDate?.toDateString() === date.toDateString() && "bg-blue-200/60 border-blue-300 dark:bg-blue-800/40",
                          "border-border/30"
                        )}
                        onClick={() => setSelectedDate(date)}
                      >
                        <div className="flex flex-col items-center gap-1">
                          <span className="text-sm font-light">
                            {date.getDate()}
                          </span>
                          <div className="flex flex-wrap gap-0.5 justify-center">
                            {dayEvents.slice(0, 2).map((event) => {
                              const Icon = eventTypeIcons[event.type]
                              return (
                                <span
                                  key={event.id}
                                  className={cn(
                                    "w-3 h-3 rounded-full flex items-center justify-center text-xs",
                                    eventStatusColors[event.status]
                                  )}
                                  title={`${event.title} - ${eventTypeNames[event.type]}`}
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    setSelectedEvent(event)
                                  }}
                                >
                                  <Icon className="w-2 h-2" />
                                </span>
                              )
                            })}
                            {dayEvents.length > 2 && (
                              <span className="text-xs text-gray-400 font-light">+{dayEvents.length - 2}</span>
                            )}
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

            {/* Events Panel - Right Side (1/4 del espacio) */}
            <div className="lg:col-span-1 h-64 lg:h-80">
            {selectedDate ? (
              <Card className={cn("backdrop-blur-xl border rounded-2xl shadow-lg transition-colors duration-300 flex flex-col h-full", "bg-gray-200/30 dark:bg-slate-800/30 border-gray-300/30 dark:border-slate-700/30")}>
                <CardHeader className="pb-2 flex-shrink-0">
                  <CardTitle className={cn("text-sm font-light tracking-wide transition-colors duration-300")}>
                    Eventos del {selectedDate.toLocaleDateString('es-ES', { day: 'numeric', month: 'long' })}
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex-1 min-h-0 p-2">
                  {selectedDateEvents.length === 0 ? (
                    <div className={cn("text-center py-2 text-muted-foreground")}>
                      <Calendar className="w-6 h-6 mx-auto mb-1 opacity-50" />
                      <p className="text-xs font-light">No hay eventos</p>
                    </div>
                  ) : (
                    <AnimatedList
                      items={selectedDateEvents}
                      onItemSelect={(event) => setSelectedEvent(event)}
                      showGradients={true}
                      enableArrowNavigation={false}
                      displayScrollbar={false}
                      className="h-full"
                      renderItem={(event, index, isSelected) => {
                        const Icon = eventTypeIcons[event.type]
                        return (
                          <div
                            className={cn(
                              "flex items-center gap-2 p-1.5 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-sm",
                              isSelected
                                ? "bg-moka-falu/20 border-moka-falu/30 text-moka-falu dark:bg-red-500/20 dark:border-red-500/30 dark:text-red-400"
                                : "bg-white/30 dark:bg-white/10 border-white/40 dark:border-white/20 hover:bg-white/40 dark:hover:bg-white/15"
                            )}
                          >
                            <span className={cn("w-5 h-5 flex items-center justify-center rounded-full text-xs", eventStatusColors[event.status])}>
                              <Icon className="w-3 h-3" />
                            </span>
                            <div className="flex-1 min-w-0">
                              <div className={cn("font-medium text-xs truncate")}>{event.title}</div>
                              <div className={cn("text-xs font-light opacity-70")}>
                                {event.time}
                              </div>
                            </div>
                          </div>
                        )
                      }}
                    />
                  )}
                </CardContent>
              </Card>
            ) : (
              <Card className={cn("backdrop-blur-xl border rounded-2xl shadow-lg transition-colors duration-300 flex flex-col h-80", "bg-gray-200/30 dark:bg-slate-800/30 border-gray-300/30 dark:border-slate-700/30")}>
                <CardContent className="flex flex-col items-center justify-center py-4 flex-1">
                  <Calendar className="w-8 h-8 mb-2 opacity-30 animate-float" />
                  <p className={cn("text-center text-muted-foreground font-light text-xs")}>
                    Selecciona una fecha para ver los eventos
                  </p>
                </CardContent>
              </Card>
            )}
            </div>
          </div>
        </div>
      </div>
      {/* Event Detail Modal */}
      <Dialog open={!!selectedEvent} onOpenChange={() => setSelectedEvent(null)}>
        {selectedEvent && <EventDetailModal event={selectedEvent} />}
      </Dialog>
    </div>
  )
}
