"use client"

import React from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { toast } from "@/components/ui/use-toast"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Search, Edit3 } from "lucide-react"
import { cn } from "@/lib/utils"
import { PermissionGuard } from "@/components/auth/permission-guard"
import { PageGuard } from "@/components/auth/page-guard"
import {
  FormCard,
  FormSection,
  FormGroup,
  CleanInput,
} from "@/components/ui/form-styles"

// Esquema de validación
const asignaNumeroFiscalSchema = z.object({
  proyectoId: z.string().min(1, "El ID o nombre del proyecto/cliente es requerido."),
  numeroFiscal: z.string().min(1, "El número fiscal es requerido.").max(50, "Máximo 50 caracteres."),
})

type AsignaNumeroFiscalValues = z.infer<typeof asignaNumeroFiscalSchema>

// Datos simulados para proyectos/clientes
interface ProyectoCliente {
  id: string
  nombre: string
  rfc?: string
  numeroFiscal?: string
  estado: "Pendiente" | "Asignado"
}

const proyectosClientesSimulados: ProyectoCliente[] = [
  { id: "PROJ001", nombre: "Desarrollo Web Corporativo - Acme Corp", rfc: "ACM123456XYZ", numeroFiscal: "NF-2023-001", estado: "Asignado" },
  { id: "PROJ002", nombre: "Consultoría SEO - Tech Solutions", rfc: "TSO098765ABC", estado: "Pendiente" },
  { id: "CLI003", nombre: "Servicios Contables - Finanzas Seguras SA", rfc: "FSS112233DEF", numeroFiscal: "NF-2023-002", estado: "Asignado" },
  { id: "PROJ004", nombre: "Diseño de Campaña Marketing - Ideas Brillantes", rfc: "IBR445566GHI", estado: "Pendiente" },
  { id: "CLI005", nombre: "Mantenimiento Industrial - Maquinaria Pesada Inc.", rfc: "MPI778899JKL", estado: "Pendiente" },
]

function AsignaNumeroFiscalContent() {
  const [listaFiltrada, setListaFiltrada] = React.useState<ProyectoCliente[]>(proyectosClientesSimulados)
  const [searchTerm, setSearchTerm] = React.useState("")
  const [selectedItem, setSelectedItem] = React.useState<ProyectoCliente | null>(null)

  const form = useForm<AsignaNumeroFiscalValues>({
    resolver: zodResolver(asignaNumeroFiscalSchema),
    defaultValues: {
      proyectoId: "",
      numeroFiscal: "",
    },
  })

  React.useEffect(() => {
    if (selectedItem) {
      form.setValue("proyectoId", selectedItem.nombre) // Mostrar nombre en el input
      form.setValue("numeroFiscal", selectedItem.numeroFiscal || "")
    } else {
      form.reset({ proyectoId: "", numeroFiscal: "" })
    }
  }, [selectedItem, form])

  React.useEffect(() => {
    const filtered = proyectosClientesSimulados.filter(item =>
      item.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.rfc && item.rfc.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    setListaFiltrada(filtered)
  }, [searchTerm])

  function onSubmit(data: AsignaNumeroFiscalValues) {
    // Encontrar el item real para obtener su ID si es necesario
    const itemReal = proyectosClientesSimulados.find(p => p.nombre === data.proyectoId || p.id === data.proyectoId)

    console.log({ ...data, internalId: itemReal?.id || data.proyectoId }) // Aquí se usaría el ID real
    toast({
      title: "Número Fiscal Asignado (Simulación)",
      description: (
        <pre className="mt-2 w-[340px] rounded-md bg-slate-950 p-4">
          <code className="text-white">{JSON.stringify({ ...data, internalId: itemReal?.id }, null, 2)}</code>
        </pre>
      ),
    })
    // Actualizar la lista simulada (esto sería una llamada al backend)
    if (itemReal) {
      const updatedList = proyectosClientesSimulados.map(p =>
        p.id === itemReal.id ? { ...p, numeroFiscal: data.numeroFiscal, estado: "Asignado" as "Asignado" } : p
      )
      // No podemos hacer setProyectosClientesSimulados directamente porque es const.
      // En una app real, esta data vendría de un estado o store que sí se puede actualizar.
      // Por ahora, actualizamos la lista filtrada para reflejar el cambio visualmente si el item estaba allí.
      setListaFiltrada(prev => prev.map(p => p.id === itemReal.id ? { ...p, numeroFiscal: data.numeroFiscal, estado: "Asignado" as "Asignado" } : p))
    }
    setSelectedItem(null) // Limpiar selección
    form.reset()
  }

  const handleSelectItem = (item: ProyectoCliente) => {
    setSelectedItem(item)
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <FormCard>
        <FormSection
          title="Asignar Número Fiscal a Proyecto/Cliente"
          description="Busca un proyecto o cliente y asigna o actualiza su número fiscal."
        >
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <FormSection title="Información del Proyecto/Cliente">
                <FormGroup>
                  <FormField
                    control={form.control}
                    name="proyectoId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Proyecto / Cliente</FormLabel>
                        <FormControl>
                          <CleanInput
                            placeholder="Nombre o ID del proyecto/cliente seleccionado"
                            {...field}
                            disabled // El campo se llena al seleccionar de la tabla
                          />
                        </FormControl>
                        <FormDescription>
                          Selecciona un ítem de la tabla de abajo para editar.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="numeroFiscal"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Número Fiscal</FormLabel>
                        <FormControl>
                          <CleanInput placeholder="Ej: NF-2023-00X" {...field} disabled={!selectedItem} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>
              </FormSection>

              <div className="flex justify-end gap-2 pt-6">
                <Button
                  type="submit"
                  disabled={!selectedItem}
                  className="bg-red-600 hover:bg-red-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 dark:bg-red-600 dark:hover:bg-red-700"
                >
                  {selectedItem?.numeroFiscal ? "Actualizar Número Fiscal" : "Asignar Número Fiscal"}
                </Button>
                {selectedItem && (
                  <Button variant="outline" onClick={() => setSelectedItem(null)}>
                    Cancelar Edición
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </FormSection>
      </FormCard>

      <FormCard>
        <FormSection
          title="Lista de Proyectos y Clientes"
          description="Busca y selecciona un proyecto o cliente para asignar o actualizar su número fiscal."
        >
          <div className="mb-6">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <CleanInput
                type="search"
                placeholder="Buscar por nombre, ID, RFC..."
                className="pl-8 w-full"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Nombre</TableHead>
                  <TableHead>RFC</TableHead>
                  <TableHead>Número Fiscal</TableHead>
                  <TableHead>Estado</TableHead>
                  <TableHead className="text-right">Acción</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {listaFiltrada.length > 0 ? (
                  listaFiltrada.map((item) => (
                    <TableRow
                      key={item.id}
                      className={cn(selectedItem?.id === item.id && "bg-muted/50", "cursor-pointer hover:bg-muted/30")}
                      onClick={() => handleSelectItem(item)}
                    >
                      <TableCell className="font-medium">{item.id}</TableCell>
                      <TableCell>{item.nombre}</TableCell>
                      <TableCell>{item.rfc || "N/A"}</TableCell>
                      <TableCell>{item.numeroFiscal || "No asignado"}</TableCell>
                      <TableCell>
                        <Badge variant={item.estado === "Asignado" ? "default" : "secondary"}
                               className={item.estado === "Asignado" ? "bg-green-100 text-green-700 dark:bg-green-700/30 dark:text-green-300"
                                                                  : "bg-yellow-100 text-yellow-700 dark:bg-yellow-600/30 dark:text-yellow-300"}>
                          {item.estado}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="icon" onClick={(e) => { e.stopPropagation(); handleSelectItem(item); }}>
                          <Edit3 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      No se encontraron resultados.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          {listaFiltrada.length === 0 && searchTerm && (
             <p className="text-sm text-muted-foreground text-center mt-4">
                Intenta con otros términos de búsqueda o limpia el filtro.
            </p>
          )}
        </FormSection>
      </FormCard>
    </div>
  )
}

export default function NumeroFiscalPage() {
  return (
    <PageGuard
      requiredPermissions={["admin:num_fiscal:read", "admin:num_fiscal:create", "admin:num_fiscal:validate"]}
      unauthorizedTitle="Acceso Denegado - Asignación de Número Fiscal"
      unauthorizedMessage="No tienes los permisos necesarios para acceder a la sección de Asignación de Número Fiscal."
    >
      <AsignaNumeroFiscalContent />
    </PageGuard>
  );
}
