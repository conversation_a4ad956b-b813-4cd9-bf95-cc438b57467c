"use client"

import * as React from "react"
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, ChevronDown, MoreHorizontal, Eye, Edit, Trash2, FileText, PlusCircle, UploadCloud } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Dialog<PERSON>rigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { CalendarIcon } from "lucide-react"
import { format, differenceInDays, addMonths } from "date-fns"
import { es } from "date-fns/locale"
import { cn } from "@/lib/utils"
import { toast } from "@/components/ui/use-toast"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuCheckboxItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { PermissionGuard } from "@/components/auth/permission-guard"
import { PageGuard } from "@/components/auth/page-guard"
import { FormCard } from "@/components/ui/form-styles"


export type ContratoRepse = {
  id: string
  empresa: string
  numeroContrato: string
  fechaInicio: Date
  fechaVencimiento: Date
  documentoURL?: string // Simulación de URL a un archivo
  estado?: "Vigente" | "Por Vencer" | "Vencido" // Se calculará dinámicamente
  // Campos adicionales si fueran necesarios
  // contactoEmpresa?: string;
  // tipoServicio?: string;
}

// Esquema de validación para el formulario de Contrato
const contratoRepseFormSchema = z.object({
  empresa: z.string().min(1, "El nombre de la empresa es requerido."),
  numeroContrato: z.string().min(1, "El número de contrato es requerido."),
  fechaInicio: z.date({ required_error: "La fecha de inicio es requerida." }),
  fechaVencimiento: z.date({ required_error: "La fecha de vencimiento es requerida." }),
  documento: z.any().optional(), // Para simular la carga de archivo
}).refine(data => data.fechaVencimiento > data.fechaInicio, {
  message: "La fecha de vencimiento debe ser posterior a la fecha de inicio.",
  path: ["fechaVencimiento"],
});

type ContratoRepseFormValues = z.infer<typeof contratoRepseFormSchema>

// Datos simulados
const initialContratos: ContratoRepse[] = [
  { id: "REPSE001", empresa: "Servicios Integrales del Norte S.A. de C.V.", numeroContrato: "SIN-2023-08-001", fechaInicio: new Date("2023-08-15"), fechaVencimiento: new Date("2024-08-14"), documentoURL: "/docs/repse001.pdf" },
  { id: "REPSE002", empresa: "Mantenimiento Especializado del Bajío S.C.", numeroContrato: "MEB-2022-12-015", fechaInicio: new Date("2022-12-01"), fechaVencimiento: addMonths(new Date(), 1) /* Por Vencer */, documentoURL: "/docs/repse002.pdf" },
  { id: "REPSE003", empresa: "Soluciones Logísticas Avanzadas S. de R.L.", numeroContrato: "SLA-2023-01-103", fechaInicio: new Date("2023-01-20"), fechaVencimiento: new Date("2023-07-19") /* Vencido */, documentoURL: "/docs/repse003.pdf" },
  { id: "REPSE004", empresa: "Constructora y Edificadora Moderna Ltda.", numeroContrato: "CEM-2024-02-050", fechaInicio: new Date("2024-02-01"), fechaVencimiento: new Date("2025-01-31"), documentoURL: "/docs/repse004.pdf" },
  { id: "REPSE005", empresa: "Tecnologías Aplicadas del Sureste S.A.P.I.", numeroContrato: "TAS-2023-11-077", fechaInicio: new Date("2023-11-10"), fechaVencimiento: addMonths(new Date(), 0) /* Por Vencer (en el mes actual) */, documentoURL: "/docs/repse005.pdf" },
];

const calcularEstadoContrato = (fechaVencimiento: Date): ContratoRepse["estado"] => {
  const hoy = new Date();
  const diasDiferencia = differenceInDays(fechaVencimiento, hoy);

  if (diasDiferencia < 0) return "Vencido";
  // Considerar "Por Vencer" si falta 1 mes o menos (aprox 30 días)
  if (diasDiferencia <= 30) return "Por Vencer";
  return "Vigente";
};

const getEstadoBadgeClass = (estado?: ContratoRepse["estado"]): string => {
 switch (estado) {
    case "Vigente":
      return "bg-moka-lion/30 text-moka-bistre border border-moka-brown/40 dark:bg-green-700/30 dark:text-green-300"
    case "Por Vencer":
      return "bg-moka-peach/80 text-moka-bistre border border-moka-brown/40 dark:bg-yellow-600/30 dark:text-yellow-300"
    case "Vencido":
      return "bg-moka-falu/20 text-moka-falu border border-moka-falu/40 dark:bg-red-700/30 dark:text-red-300"
    default:
      return "bg-moka-peach/40 text-moka-bistre border border-moka-brown/40"
  }
}

function ContratosRepseContent() {
  const [contratos, setContratos] = React.useState<ContratoRepse[]>(
    initialContratos.map(c => ({...c, estado: calcularEstadoContrato(c.fechaVencimiento)}))
  );
  const [sorting, setSorting] = React.useState<SortingState>([{ id: "fechaVencimiento", desc: false }])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [isFormOpen, setIsFormOpen] = React.useState(false)
  const [editingContrato, setEditingContrato] = React.useState<ContratoRepse | null>(null)

  const form = useForm<ContratoRepseFormValues>({
    resolver: zodResolver(contratoRepseFormSchema),
    defaultValues: {
      empresa: "",
      numeroContrato: "",
    },
  })

  React.useEffect(() => {
    if (editingContrato) {
      form.reset({
        empresa: editingContrato.empresa,
        numeroContrato: editingContrato.numeroContrato,
        fechaInicio: editingContrato.fechaInicio,
        fechaVencimiento: editingContrato.fechaVencimiento,
        documento: undefined, // El input de archivo no se puede pre-llenar por seguridad
      });
    } else {
      form.reset();
    }
  }, [editingContrato, form, isFormOpen]);


  const onSubmitForm = (values: ContratoRepseFormValues) => {
    const newOrUpdatedContrato: ContratoRepse = {
      id: editingContrato ? editingContrato.id : `REPSE${String(Date.now()).slice(-3)}${String(Math.floor(Math.random()*100)).padStart(2,'0')}`,
      ...values,
      documentoURL: editingContrato?.documentoURL || (values.documento ? `/docs/${values.documento.name}` : undefined),
      estado: calcularEstadoContrato(values.fechaVencimiento),
    };

    if (editingContrato) {
      setContratos(prev => prev.map(c => c.id === editingContrato.id ? newOrUpdatedContrato : c));
      toast({ title: "Contrato Actualizado", description: `El contrato ${newOrUpdatedContrato.numeroContrato} ha sido actualizado.` });
    } else {
      setContratos(prev => [newOrUpdatedContrato, ...prev]);
      toast({ title: "Contrato Añadido", description: `El contrato ${newOrUpdatedContrato.numeroContrato} ha sido añadido.` });
    }

    setIsFormOpen(false);
    setEditingContrato(null);
    form.reset();
  };

  const handleDeleteContrato = (id: string) => {
    setContratos(prev => prev.filter(c => c.id !== id));
    toast({ title: "Contrato Eliminado", description: "El contrato ha sido eliminado.", variant: "destructive" });
  };

  const columns: ColumnDef<ContratoRepse>[] = [
    { accessorKey: "numeroContrato", header: "Nº Contrato" },
    { accessorKey: "empresa", header: "Empresa", cell: ({row}) => <div className="min-w-[200px]">{row.original.empresa}</div> },
    {
      accessorKey: "fechaInicio",
      header: ({ column }) => (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
          Fecha Inicio <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => format(row.original.fechaInicio, "dd/MM/yyyy", { locale: es }),
    },
    {
      accessorKey: "fechaVencimiento",
      header: ({ column }) => (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
          Fecha Vencimiento <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => format(row.original.fechaVencimiento, "dd/MM/yyyy", { locale: es }),
    },
    {
      accessorKey: "estado",
      header: "Estado",
      cell: ({ row }) => (
        <Badge className={cn("capitalize", getEstadoBadgeClass(row.original.estado))}>
          {row.original.estado}
        </Badge>
      ),
      filterFn: (row, id, value) => value.includes(row.getValue(id))
    },
    {
      accessorKey: "documentoURL",
      header: "Documento",
      cell: ({ row }) => row.original.documentoURL ? (
        <Button variant="link" size="sm" asChild>
          <a href={row.original.documentoURL} target="_blank" rel="noopener noreferrer" className="flex items-center">
            <FileText className="mr-1 h-4 w-4" /> Ver PDF
          </a>
        </Button>
      ) : <span className="text-xs text-muted-foreground">No adjunto</span>,
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const contrato = row.original
        return (
          <div className="flex items-center space-x-1">
            <Button variant="ghost" size="icon" onClick={() => { setEditingContrato(contrato); setIsFormOpen(true); }}>
              <Edit className="h-4 w-4" />
            </Button>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="ghost" size="icon" className="text-red-500 hover:text-red-700">
                  <Trash2 className="h-4 w-4" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>¿Estás seguro de eliminar este contrato?</AlertDialogTitle>
                  <AlertDialogDescription>
                    Esta acción no se puede deshacer. Esto eliminará permanentemente el contrato REPSE: <strong>{contrato.numeroContrato}</strong> de la empresa <strong>{contrato.empresa}</strong>.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancelar</AlertDialogCancel>
                  <AlertDialogAction onClick={() => handleDeleteContrato(contrato.id)} className="bg-red-600 hover:bg-red-700">
                    Sí, eliminar
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        )
      },
    },
  ]

  const table = useReactTable({
    data: contratos,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: { sorting, columnFilters, columnVisibility },
    initialState: { pagination: { pageSize: 5 } }
  })

  const estadosUnicos = Array.from(new Set(contratos.map(c => c.estado).filter((e): e is ContratoRepse["estado"] => Boolean(e))));


  return (
    <div className="max-w-7xl mx-auto p-6">
      <FormCard>
        <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Gestión de Contratos REPSE</h1>
            <p className="text-muted-foreground">
              Administra y visualiza los contratos REPSE. Se enviará notificación a Ventas por contratos próximos a vencer.
            </p>
          </div>
        <Dialog open={isFormOpen} onOpenChange={(open) => { setIsFormOpen(open); if (!open) setEditingContrato(null); }}>
          <DialogTrigger asChild>
            <Button onClick={() => { setEditingContrato(null); form.reset(); setIsFormOpen(true); }}>
              <PlusCircle className="mr-2 h-4 w-4" /> Añadir Contrato
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-2xl">
            <DialogHeader>
              <DialogTitle>{editingContrato ? "Editar" : "Añadir Nuevo"} Contrato REPSE</DialogTitle>
              <DialogDescription>
                {editingContrato ? "Modifica los detalles del contrato." : "Completa los campos para registrar un nuevo contrato."}
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmitForm)} className="space-y-4 py-4">
                <FormField control={form.control} name="empresa" render={({ field }) => (
                  <FormItem><FormLabel>Nombre de la Empresa</FormLabel><FormControl><Input placeholder="Ej: Servicios Integrales S.A." {...field} /></FormControl><FormMessage /></FormItem>
                )} />
                <FormField control={form.control} name="numeroContrato" render={({ field }) => (
                  <FormItem><FormLabel>Número de Contrato</FormLabel><FormControl><Input placeholder="Ej: REPSE-2023-001" {...field} /></FormControl><FormMessage /></FormItem>
                )} />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField control={form.control} name="fechaInicio" render={({ field }) => (
                    <FormItem className="flex flex-col"><FormLabel>Fecha de Inicio</FormLabel>
                      <Popover><PopoverTrigger asChild><FormControl>
                        <Button variant={"outline"} className={cn("w-full pl-3 text-left font-normal", !field.value && "text-muted-foreground")}>
                          {field.value ? format(field.value, "PPP", { locale: es }) : <span>Selecciona fecha</span>}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button></FormControl></PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar mode="single" selected={field.value} onSelect={field.onChange} initialFocus locale={es} />
                        </PopoverContent></Popover><FormMessage />
                    </FormItem>
                  )} />
                  <FormField control={form.control} name="fechaVencimiento" render={({ field }) => (
                    <FormItem className="flex flex-col"><FormLabel>Fecha de Vencimiento</FormLabel>
                      <Popover><PopoverTrigger asChild><FormControl>
                        <Button variant={"outline"} className={cn("w-full pl-3 text-left font-normal", !field.value && "text-muted-foreground")}>
                          {field.value ? format(field.value, "PPP", { locale: es }) : <span>Selecciona fecha</span>}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button></FormControl></PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar mode="single" selected={field.value} onSelect={field.onChange}
                                    disabled={(date) => date < (form.getValues("fechaInicio") || new Date(0)) } // No antes de fecha inicio
                                    initialFocus locale={es} />
                        </PopoverContent></Popover><FormMessage />
                    </FormItem>
                  )} />
                </div>
                <FormField control={form.control} name="documento" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Documento del Contrato (PDF)</FormLabel>
                    <FormControl>
                      <Input type="file" accept=".pdf" onChange={(e) => field.onChange(e.target.files ? e.target.files[0] : null)} />
                    </FormControl>
                    {editingContrato?.documentoURL && !field.value && (
                        <FormDescription>
                            Actual: <a href={editingContrato.documentoURL} target="_blank" rel="noopener noreferrer" className="underline">{editingContrato.documentoURL.split('/').pop()}</a>. Sube un nuevo archivo para reemplazarlo.
                        </FormDescription>
                    )}
                    <FormMessage />
                  </FormItem>
                )} />
                <DialogFooter className="pt-4">
                  <DialogClose asChild><Button type="button" variant="outline">Cancelar</Button></DialogClose>
                  <Button type="submit">{editingContrato ? "Guardar Cambios" : "Añadir Contrato"}</Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
        </header>
        <div className="flex items-center py-4 gap-2 flex-wrap">
          <Input
            placeholder="Filtrar por empresa o nº contrato..."
            value={(table.getColumn("empresa")?.getFilterValue() as string) ?? ""}
            onChange={(event) => {
                table.getColumn("empresa")?.setFilterValue(event.target.value)
                // También podríamos querer filtrar por numeroContrato con el mismo input
                // table.getColumn("numeroContrato")?.setFilterValue(event.target.value)
            }}
            className="max-w-sm"
          />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="ml-auto">
                Estado <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {estadosUnicos.map((estado) => (
                <DropdownMenuCheckboxItem
                  key={estado}
                  className="capitalize"
                  checked={(table.getColumn("estado")?.getFilterValue() as string[])?.includes(estado)}
                  onCheckedChange={(checked) => {
                    const currentFilter = (table.getColumn("estado")?.getFilterValue() as string[]) || []
                    if (checked) table.getColumn("estado")?.setFilterValue([...currentFilter, estado])
                    else table.getColumn("estado")?.setFilterValue(currentFilter.filter(val => val !== estado))
                  }}
                >
                  {estado}
                </DropdownMenuCheckboxItem>
              ))}
              <DropdownMenuSeparator />
              <DropdownMenuItem onSelect={() => table.getColumn("estado")?.setFilterValue(undefined)}>Limpiar filtro</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <div className="rounded-md border">
          <Table>
            <TableHeader>{table.getHeaderGroups().map(hg => (<TableRow key={hg.id}>{hg.headers.map(h => <TableHead key={h.id}>{h.isPlaceholder ? null : flexRender(h.column.columnDef.header, h.getContext())}</TableHead>)}</TableRow>))}</TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map(row => (
                  <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                    {row.getVisibleCells().map(cell => <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>)}
                  </TableRow>
                ))
              ) : (
                <TableRow><TableCell colSpan={columns.length} className="h-24 text-center">No hay contratos.</TableCell></TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        <div className="flex items-center justify-end space-x-2 py-4">
          <Button variant="outline" size="sm" onClick={() => table.previousPage()} disabled={!table.getCanPreviousPage()}>Anterior</Button>
          <Button variant="outline" size="sm" onClick={() => table.nextPage()} disabled={!table.getCanNextPage()}>Siguiente</Button>
        </div>
      </FormCard>
    </div>
  )
}

export default function ContratosRepsePage() {
  return (
    <PageGuard
      requiredPermissions={["admin:contratos:read", "admin:contratos:list"]}
      unauthorizedTitle="Acceso Denegado - Contratos REPSE"
      unauthorizedMessage="No tienes los permisos necesarios para acceder a la sección de Contratos REPSE."
    >
      <ContratosRepseContent />
    </PageGuard>
  )
}
