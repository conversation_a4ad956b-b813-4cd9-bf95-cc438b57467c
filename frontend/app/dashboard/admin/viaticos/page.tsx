"use client"

import React from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon } from "lucide-react"
import { Calendar } from "@/components/ui/calendar"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import { toast } from "@/components/ui/use-toast"
import { Combobox } from "@/components/ui/combobox"
import { PageGuard } from "@/components/auth/page-guard"
import {
  FormCard,
  FormSection,
  FormGroup,
  CleanInput,
  CleanTextarea,
  CleanSelect,
  CleanSelectTrigger,
  CleanSelectContent,
  CleanSelectItem,
  CleanButton,
  CleanPopoverContent,
  CleanCalendar
} from "@/components/ui/form-styles"

// Esquema de validación con Zod
const viaticosFormSchema = z.object({
  solicitanteId: z.string({ required_error: "Por favor, selecciona un solicitante." }),
  departamento: z.string().min(1, "El departamento es requerido."),
  destino: z.string().min(1, "El destino es requerido."),
  fechaInicio: z.date({ required_error: "La fecha de inicio es requerida." }),
  fechaFin: z.date({ required_error: "La fecha de fin es requerida." }),
  motivo: z.string().min(10, "El motivo debe tener al menos 10 caracteres."),
  transporte: z.string({ required_error: "Selecciona un tipo de transporte." }),
  hospedaje: z.enum(["si", "no"], { required_error: "Indica si necesitas hospedaje." }),
  anticipoSolicitado: z.preprocess(
    (a) => parseFloat(z.string().parse(a)),
    z.number().positive("El anticipo debe ser un número positivo.").optional()
  ),
  comentarios: z.string().optional(),
})

type ViaticosFormValues = z.infer<typeof viaticosFormSchema>

// Datos simulados para el Combobox de solicitantes
const solicitantes = [
  { value: "user1", label: "Juan Pérez (Ventas)" },
  { value: "user2", label: "Ana Gómez (Logística)" },
  { value: "user3", label: "Carlos Ruiz (Servicio)" },
  { value: "user4", label: "Laura Méndez (Administración)" },
]

// Datos simulados para departamentos
const departamentos = [
  { value: "ventas", label: "Ventas" },
  { value: "logistica", label: "Logística" },
  { value: "servicio", label: "Servicio Técnico" },
  { value: "administracion", label: "Administración" },
  { value: "recursos_humanos", label: "Recursos Humanos" },
  { value: "calidad", label: "Calidad" },
]

function SolicitudViaticosContent() {
  const form = useForm<ViaticosFormValues>({
    resolver: zodResolver(viaticosFormSchema),
    defaultValues: {
      departamento: "",
      destino: "",
      motivo: "",
      anticipoSolicitado: 0,
      comentarios: "",
    },
  })

  function onSubmit(data: ViaticosFormValues) {
    console.log(data)
    toast({
      title: "Solicitud Enviada (Simulación)",
      description: (
        <pre className="mt-2 w-[340px] rounded-md bg-slate-950 p-4">
          <code className="text-white">{JSON.stringify(data, null, 2)}</code>
        </pre>
      ),
    })
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <FormCard>
        <FormSection
          title="Solicitud de Viáticos"
          description="Completa el formulario para solicitar viáticos para viajes de trabajo."
        >
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <FormSection title="Información General">
                <FormGroup>
                  <FormField
                    control={form.control}
                    name="solicitanteId"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Solicitante</FormLabel>
                        <Combobox
                          options={solicitantes}
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Selecciona un solicitante..."
                          searchPlaceholder="Buscar solicitante..."
                          notFoundText="Solicitante no encontrado."
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="departamento"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Departamento</FormLabel>
                        <CleanSelect onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <CleanSelectTrigger>
                              <SelectValue placeholder="Selecciona un departamento" />
                            </CleanSelectTrigger>
                          </FormControl>
                          <CleanSelectContent>
                            {departamentos.map(dep => (
                              <CleanSelectItem key={dep.value} value={dep.value}>{dep.label}</CleanSelectItem>
                            ))}
                          </CleanSelectContent>
                        </CleanSelect>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>

                <FormGroup>
                  <FormField
                    control={form.control}
                    name="destino"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Destino del Viaje</FormLabel>
                        <FormControl>
                          <CleanInput
                            placeholder="Ej: Ciudad de México, Cliente XYZ"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>
              </FormSection>

              <FormSection title="Fechas del Viaje">
                <FormGroup>
                  <FormField
                    control={form.control}
                    name="fechaInicio"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Fecha de Inicio</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <CleanButton
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-gray-500 dark:text-slate-400"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP", { locale: es })
                                ) : (
                                  <span>Selecciona una fecha</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </CleanButton>
                            </FormControl>
                          </PopoverTrigger>
                          <CleanPopoverContent className="w-auto p-0" align="start">
                            <CleanCalendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date < new Date(new Date().setHours(0,0,0,0))
                              }
                              initialFocus
                              locale={es}
                            />
                          </CleanPopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="fechaFin"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Fecha de Fin</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <CleanButton
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-gray-500 dark:text-slate-400"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP", { locale: es })
                                ) : (
                                  <span>Selecciona una fecha</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </CleanButton>
                            </FormControl>
                          </PopoverTrigger>
                          <CleanPopoverContent className="w-auto p-0" align="start">
                            <CleanCalendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date < (form.getValues("fechaInicio") || new Date(new Date().setHours(0,0,0,0)))
                              }
                              initialFocus
                              locale={es}
                            />
                          </CleanPopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>
              </FormSection>

              <FormSection title="Detalles del Viaje">
                <FormGroup>
                  <FormField
                    control={form.control}
                    name="motivo"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Motivo del Viaje</FormLabel>
                        <FormControl>
                          <CleanTextarea
                            placeholder="Describe detalladamente el propósito del viaje (ej: Visita a cliente, Instalación de equipo, Capacitación)"
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Proporciona suficiente detalle para la aprobación de la solicitud.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>
              </FormSection>

              <FormSection title="Transporte y Hospedaje">
                <FormGroup>
                  <FormField
                    control={form.control}
                    name="transporte"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Medio de Transporte Principal</FormLabel>
                        <CleanSelect onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <CleanSelectTrigger>
                              <SelectValue placeholder="Selecciona un medio de transporte" />
                            </CleanSelectTrigger>
                          </FormControl>
                          <CleanSelectContent>
                            <CleanSelectItem value="avion">Avión</CleanSelectItem>
                            <CleanSelectItem value="autobus">Autobús</CleanSelectItem>
                            <CleanSelectItem value="auto_empresa">Auto de la Empresa</CleanSelectItem>
                            <CleanSelectItem value="auto_propio">Auto Propio (con reembolso km)</CleanSelectItem>
                            <CleanSelectItem value="otro">Otro (especificar en comentarios)</CleanSelectItem>
                          </CleanSelectContent>
                        </CleanSelect>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="hospedaje"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>¿Requiere Hospedaje?</FormLabel>
                        <CleanSelect onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <CleanSelectTrigger>
                              <SelectValue placeholder="Selecciona una opción" />
                            </CleanSelectTrigger>
                          </FormControl>
                          <CleanSelectContent>
                            <CleanSelectItem value="si">Sí</CleanSelectItem>
                            <CleanSelectItem value="no">No</CleanSelectItem>
                          </CleanSelectContent>
                        </CleanSelect>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>
              </FormSection>

              <FormSection title="Información Financiera">
                <FormGroup>
                  <FormField
                    control={form.control}
                    name="anticipoSolicitado"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Anticipo Solicitado (MXN)</FormLabel>
                        <FormControl>
                          <CleanInput
                            type="number"
                            placeholder="Ej: 5000"
                            {...field}
                            onChange={event => field.onChange(event.target.valueAsNumber)}
                          />
                        </FormControl>
                        <FormDescription>
                          Si no requieres anticipo, deja en 0 o vacío.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>
              </FormSection>

              <FormSection title="Comentarios Adicionales">
                <FormGroup>
                  <FormField
                    control={form.control}
                    name="comentarios"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Comentarios Adicionales</FormLabel>
                        <FormControl>
                          <CleanTextarea
                            placeholder="Cualquier información adicional relevante para la solicitud."
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>
              </FormSection>

              <div className="flex justify-end pt-6">
                <Button
                  type="submit"
                  className="w-full md:w-auto bg-red-600 hover:bg-red-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 dark:bg-red-600 dark:hover:bg-red-700"
                >
                  Enviar Solicitud de Viáticos
                </Button>
              </div>
            </form>
          </Form>
        </FormSection>
      </FormCard>
    </div>
  )
}

export default function SolicitudViaticosPage() {
  return (
    <PageGuard
      requiredPermissions={["admin:viaticos:read", "admin:viaticos:list"]}
      unauthorizedTitle="Acceso Denegado - Solicitud de Viáticos"
      unauthorizedMessage="No tienes los permisos necesarios para acceder a la sección de Solicitud de Viáticos."
    >
      <SolicitudViaticosContent />
    </PageGuard>
  )
}
