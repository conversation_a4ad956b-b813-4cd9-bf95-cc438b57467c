"use client"

import React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, BarChart3, Calendar as CalendarIcon, Filter } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { es } from "date-fns/locale"
import { format, subMonths } from "date-fns"
import { DateRange } from "react-day-picker"
import { cn } from "@/lib/utils"
import { toast } from "@/components/ui/use-toast"
import { PageGuard } from "@/components/auth/page-guard"
import {
  FormCard,
  FormSection,
  FormGroup,
  CleanButton,
  CleanPopoverContent,
  CleanCalendar,
  CleanSelect,
  CleanSelectTrigger,
  CleanSelectContent,
  CleanSelectItem,
} from "@/components/ui/form-styles"

// Importar Recharts (asegúrate de haberlo instalado: npm install recharts)
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  LineChart,
  Line,
} from "recharts"

// Simulación de datos que podrían venir del backend
const simularDatosFinancieros = (dateRange?: DateRange, tipo?: string) => {
  const meses = ["Ene", "Feb", "Mar", "Abr", "May", "Jun", "Jul", "Ago", "Sep", "Oct", "Nov", "Dic"];
  let data = [];

  const hoy = new Date();
  const mesActual = hoy.getMonth();
  const anioActual = hoy.getFullYear();

  // Determinar el rango de meses a generar basado en dateRange o default a los últimos 6 meses
  let inicioMes = dateRange?.from ? dateRange.from.getMonth() : (mesActual - 5 + 12) % 12;
  let inicioAnio = dateRange?.from ? dateRange.from.getFullYear() : ((mesActual - 5 < 0) ? anioActual -1 : anioActual) ;

  let finMes = dateRange?.to ? dateRange.to.getMonth() : mesActual;
  let finAnio = dateRange?.to ? dateRange.to.getFullYear() : anioActual;


  for (let anio = inicioAnio; anio <= finAnio; anio++) {
    const mesInicioIter = (anio === inicioAnio) ? inicioMes : 0;
    const mesFinIter = (anio === finAnio) ? finMes : 11;

    for (let i = mesInicioIter; i <= mesFinIter; i++) {
        const nombreMes = `${meses[i]} ${anio.toString().slice(-2)}`;
        let cotizado = 0;
        let facturado = 0;
        let pagado = 0;

        if (!tipo || tipo === "todos" || tipo === "cotizado") {
            cotizado = Math.floor(Math.random() * (80000 - 20000 + 1)) + 20000; // entre 20k y 80k
        }
        if (!tipo || tipo === "todos" || tipo === "facturado") {
            facturado = Math.floor(Math.random() * (cotizado * 0.9 - cotizado * 0.5 + 1)) + cotizado * 0.5; // entre 50% y 90% de lo cotizado
        }
        if (!tipo || tipo === "todos" || tipo === "pagado") {
            pagado = Math.floor(Math.random() * (facturado * 0.8 - facturado * 0.4 + 1)) + facturado * 0.4; // entre 40% y 80% de lo facturado
        }

        data.push({ name: nombreMes, Cotizado: cotizado, Facturado: facturado, Pagado: pagado });
    }
  }
  return data;
};

function GraficosAdminContent() {
  const [dateRange, setDateRange] = React.useState<DateRange | undefined>({
    from: subMonths(new Date(), 5), // Default últimos 6 meses
    to: new Date(),
  });
  const [tipoInformacion, setTipoInformacion] = React.useState<string>("todos"); // todos, cotizado, facturado, pagado
  const [chartData, setChartData] = React.useState(simularDatosFinancieros(dateRange, tipoInformacion));

  React.useEffect(() => {
    setChartData(simularDatosFinancieros(dateRange, tipoInformacion));
  }, [dateRange, tipoInformacion]);

  const handleDownloadExcel = () => {
    // Simulación de descarga de Excel
    const dataToExport = chartData.map(d => ({
        "Mes": d.name,
        "Cotizado MXN": d.Cotizado,
        "Facturado MXN": d.Facturado,
        "Pagado MXN": d.Pagado,
    }));
    const csvContent = "data:text/csv;charset=utf-8,"
        + Object.keys(dataToExport[0]).join(",") + "\n"
        + dataToExport.map(e => Object.values(e).join(",")).join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `reporte_financiero_${format(new Date(), "yyyyMMdd")}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({ title: "Excel Descargado (Simulación)", description: "El reporte se ha generado y descargado como CSV." });
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <FormCard>
        <FormSection
          title="Gráficos y Reportes Financieros"
          description="Visualiza el rendimiento financiero y descarga reportes detallados en formato Excel."
        >
          <FormSection title="Filtros de Reporte">
            <FormGroup>
              <div className="grid gap-2 w-full sm:w-auto">
                <Label htmlFor="date-range">Rango de Fechas</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <CleanButton
                      id="date-range"
                      className={cn(
                        "w-full sm:w-[280px] justify-start text-left font-normal",
                        !dateRange && "text-gray-500 dark:text-slate-400"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange?.from ? (
                        dateRange.to ? (
                          <>
                            {format(dateRange.from, "LLL dd, y", { locale: es })} -{" "}
                            {format(dateRange.to, "LLL dd, y", { locale: es })}
                          </>
                        ) : (
                          format(dateRange.from, "LLL dd, y", { locale: es })
                        )
                      ) : (
                        <span>Selecciona un rango</span>
                      )}
                    </CleanButton>
                  </PopoverTrigger>
                  <CleanPopoverContent className="w-auto p-0" align="start">
                    <CleanCalendar
                      initialFocus
                      mode="range"
                      defaultMonth={dateRange?.from}
                      selected={dateRange}
                      onSelect={setDateRange}
                      numberOfMonths={2}
                      locale={es}
                    />
                  </CleanPopoverContent>
                </Popover>
              </div>

              <div className="grid gap-2 w-full sm:w-[200px]">
                <Label htmlFor="tipo-info">Tipo de Información</Label>
                <CleanSelect value={tipoInformacion} onValueChange={setTipoInformacion}>
                  <CleanSelectTrigger id="tipo-info">
                    <SelectValue placeholder="Seleccionar tipo" />
                  </CleanSelectTrigger>
                  <CleanSelectContent>
                    <CleanSelectItem value="todos">Todos (Cotizado, Facturado, Pagado)</CleanSelectItem>
                    <CleanSelectItem value="cotizado">Solo Cotizado</CleanSelectItem>
                    <CleanSelectItem value="facturado">Solo Facturado</CleanSelectItem>
                    <CleanSelectItem value="pagado">Solo Pagado</CleanSelectItem>
                  </CleanSelectContent>
                </CleanSelect>
              </div>

              <Button
                onClick={handleDownloadExcel}
                className="w-full sm:w-auto bg-red-600 hover:bg-red-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 dark:bg-red-600 dark:hover:bg-red-700"
              >
                <Download className="mr-2 h-4 w-4" /> Descargar Excel
            </Button>
            </FormGroup>
          </FormSection>

          <FormSection title="Gráficos de Rendimiento">
            {/* Gráfico de Barras: Comparativa Mensual */}
            <FormCard>
              <FormSection
                title="Comparativa Mensual"
                description="Cotizado vs Facturado vs Pagado por mes."
              >
                <div className="h-[350px] sm:h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={chartData} margin={{ top: 5, right: 20, left: 10, bottom: 5 }}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" fontSize={12} />
                      <YAxis tickFormatter={(value) => `$${(value/1000)}k`} fontSize={12}/>
                      <Tooltip formatter={(value:number) => new Intl.NumberFormat('es-MX', { style: 'currency', currency: 'MXN' }).format(value)} />
                      <Legend />
                      {(tipoInformacion === "todos" || tipoInformacion === "cotizado") && <Bar dataKey="Cotizado" fill="#8884d8" radius={[4, 4, 0, 0]} />}
                      {(tipoInformacion === "todos" || tipoInformacion === "facturado") && <Bar dataKey="Facturado" fill="#82ca9d" radius={[4, 4, 0, 0]} />}
                      {(tipoInformacion === "todos" || tipoInformacion === "pagado") && <Bar dataKey="Pagado" fill="#ffc658" radius={[4, 4, 0, 0]} />}
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </FormSection>
            </FormCard>

            {/* Gráfico de Líneas: Tendencia de Pagos vs Facturación */}
            {(tipoInformacion === "todos" || tipoInformacion === "facturado" || tipoInformacion === "pagado") && (
              <FormCard>
                <FormSection
                  title="Tendencia de Facturación y Pagos"
                  description="Evolución mensual de montos facturados y pagados."
                >
                  <div className="h-[350px] sm:h-[400px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={chartData} margin={{ top: 5, right: 20, left: 10, bottom: 5 }}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" fontSize={12} />
                        <YAxis tickFormatter={(value) => `$${(value/1000)}k`} fontSize={12}/>
                        <Tooltip formatter={(value:number) => new Intl.NumberFormat('es-MX', { style: 'currency', currency: 'MXN' }).format(value)} />
                        <Legend />
                        {(tipoInformacion === "todos" || tipoInformacion === "facturado") && <Line type="monotone" dataKey="Facturado" stroke="#82ca9d" strokeWidth={2} activeDot={{ r: 6 }} />}
                        {(tipoInformacion === "todos" || tipoInformacion === "pagado") && <Line type="monotone" dataKey="Pagado" stroke="#ffc658" strokeWidth={2} activeDot={{ r: 6 }} />}
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </FormSection>
              </FormCard>
            )}
          </FormSection>
        </FormSection>
      </FormCard>
    </div>
  )
}

export default function GraficosAdminPage() {
  return (
    <PageGuard
      requiredPermissions={["admin:graficos:read"]}
      unauthorizedTitle="Acceso Denegado - Gráficos y Reportes"
      unauthorizedMessage="No tienes los permisos necesarios para acceder a la sección de Gráficos y Reportes."
    >
      <GraficosAdminContent />
    </PageGuard>
  )
}
