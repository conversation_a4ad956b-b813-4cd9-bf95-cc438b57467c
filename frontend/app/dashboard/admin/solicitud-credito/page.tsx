"use client"

import * as React from "react"
import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod"
import { use<PERSON><PERSON>, Controller } from "react-hook-form"
import * as z from "zod"
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, CheckCircle, XCircle, Clock, FileText, PlusCircle, Edit, Eye, FileSignature, Award } from "lucide-react"
import { format } from "date-fns"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  <PERSON>alog<PERSON><PERSON><PERSON>,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { toast } from "@/components/ui/use-toast"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { cn } from "@/lib/utils"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { PermissionGuard } from '@/components/auth/permission-guard'
import { PageGuard } from "@/components/auth/page-guard"
import { FormCard } from "@/components/ui/form-styles"


// Tipos y Esquemas
export type DocumentoSolicitudCredito = {
  id: string
  nombre: string
  obligatorio: boolean
  archivo?: File // Para el formulario
  url?: string // Para mostrar si ya existe
  estado?: "Pendiente" | "Cargado" | "Revisado"
}

export type SolicitudCredito = {
  id: string
  socioEmpresa: string
  rfc: string
  domicilio: string
  contactoNombre: string
  contactoEmail: string
  contactoTelefono: string
  lineaSolicitada: number
  lineaOtorgada?: number
  fechaSolicitud: Date
  estado: "En Revisión" | "Aprobada" | "Rechazada" | "Requiere Documentación"
  documentos: DocumentoSolicitudCredito[]
  motivoRechazo?: string
  observaciones?: string
}

const documentoSchema = z.object({
  id: z.string(),
  nombre: z.string(),
  obligatorio: z.boolean(),
  archivo: z.any().optional(),
  url: z.string().optional(),
  estado: z.string().optional(),
});

const solicitudCreditoFormSchema = z.object({
  socioEmpresa: z.string().min(1, "Nombre del socio/empresa es requerido."),
  rfc: z.string().min(12, "RFC debe tener al menos 12 caracteres.").max(13, "RFC no debe exceder 13 caracteres."),
  domicilio: z.string().min(1, "Domicilio es requerido."),
  contactoNombre: z.string().min(1, "Nombre de contacto es requerido."),
  contactoEmail: z.string().email("Email de contacto inválido."),
  contactoTelefono: z.string().min(10, "Teléfono de contacto debe tener al menos 10 dígitos."),
  lineaSolicitada: z.preprocess(a => parseFloat(z.string().parse(a)), z.number().positive("Línea solicitada debe ser positiva.")),
  documentos: z.array(documentoSchema).refine(docs =>
    docs.filter(d => d.obligatorio).every(d => d.archivo || d.url),
    { message: "Todos los documentos obligatorios deben ser cargados." }
  ),
  observaciones: z.string().optional(),
});

type SolicitudCreditoFormValues = z.infer<typeof solicitudCreditoFormSchema>

// Datos / Estado
const documentosBase: Omit<DocumentoSolicitudCredito, "archivo" | "url" | "estado">[] = [
  { id: "acta", nombre: "Acta Constitutiva", obligatorio: true },
  { id: "id_rl", nombre: "Identificación Representante Legal", obligatorio: true },
  { id: "comp_dom", nombre: "Comprobante de Domicilio (Reciente)", obligatorio: true },
  { id: "edocta_fin", nombre: "Estados Financieros (Últimos 2 años)", obligatorio: true },
  { id: "ref_com", nombre: "Referencias Comerciales (Opcional)", obligatorio: false },
];

const initialSolicitudes: SolicitudCredito[] = [
  { id: "SC001", socioEmpresa: "Innovatech Global", rfc: "IGL010203XYZ", domicilio: "Av. Siempre Viva 123, Col. Centro, CDMX", contactoNombre: "Ana López", contactoEmail: "<EMAIL>", contactoTelefono: "5511223344", lineaSolicitada: 250000, fechaSolicitud: new Date("2023-10-10"), estado: "Aprobada", lineaOtorgada: 200000, documentos: documentosBase.map(d => ({...d, url: `/docs/sc001/${d.id}.pdf`, estado: "Revisado"})) },
  { id: "SC002", socioEmpresa: "Servicios Constructivos Beta", rfc: "SCB040506ABC", domicilio: "Calle Falsa 456, Guadalajara, JAL", contactoNombre: "Luis Torres", contactoEmail: "<EMAIL>", contactoTelefono: "3399887766", lineaSolicitada: 100000, fechaSolicitud: new Date("2023-11-01"), estado: "En Revisión", documentos: documentosBase.map(d => d.id === "acta" ? {...d, url: `/docs/sc002/${d.id}.pdf`, estado: "Cargado"} : {...d, estado: "Pendiente"})},
  { id: "SC003", socioEmpresa: "Comercializadora Delta Uno", rfc: "CDU070809DEF", domicilio: "Blvd. Principal 789, Monterrey, NL", contactoNombre: "Sofia Herrera", contactoEmail: "<EMAIL>", contactoTelefono: "8177665544", lineaSolicitada: 50000, fechaSolicitud: new Date("2023-09-15"), estado: "Rechazada", motivoRechazo: "Información financiera insuficiente.", documentos: documentosBase.map(d => ({...d, estado: "Cargado"}))},
];

// Funciones de Utilidad y Componentes
const getEstadoBadgeClass = (estado: SolicitudCredito["estado"]): string => {
  switch (estado) {
    case "Aprobada": return "bg-moka-lion/30 text-moka-bistre border border-moka-brown/40 dark:bg-green-700/30 dark:text-green-300";
    case "En Revisión": return "bg-moka-peach/60 text-moka-bistre border border-moka-brown/40 dark:bg-blue-700/30 dark:text-blue-300";
    case "Rechazada": return "bg-moka-falu/20 text-moka-falu border border-moka-falu/40 dark:bg-red-700/30 dark:text-red-300";
    case "Requiere Documentación": return "bg-moka-peach/80 text-moka-bistre border border-moka-brown/40 dark:bg-yellow-600/30 dark:text-yellow-300";
    default: return "bg-moka-peach/40 text-moka-bistre border border-moka-brown/40";
  }
}

function SolicitudCreditoContent() {
  const [solicitudes, setSolicitudes] = React.useState<SolicitudCredito[]>(initialSolicitudes);
  const [isFormOpen, setIsFormOpen] = React.useState(false);
  const [editingSolicitud, setEditingSolicitud] = React.useState<SolicitudCredito | null>(null);
  const [isDetailOpen, setIsDetailOpen] = React.useState(false);
  const [isActionDialogOpen, setIsActionDialogOpen] = React.useState(false);
  const [actionType, setActionType] = React.useState<"aprobar" | "rechazar" | null>(null);
  const [currentSolicitudForAction, setCurrentSolicitudForAction] = React.useState<SolicitudCredito | null>(null);
  const [lineaOtorgadaInput, setLineaOtorgadaInput] = React.useState<string>("");
  const [motivoRechazoInput, setMotivoRechazoInput] = React.useState<string>("");

  const form = useForm<SolicitudCreditoFormValues>({
    resolver: zodResolver(solicitudCreditoFormSchema),
    defaultValues: {
      documentos: documentosBase.map(d => ({...d, archivo: undefined, url: undefined}))
    }
  });

  React.useEffect(() => {
    if (editingSolicitud) {
      form.reset({
        socioEmpresa: editingSolicitud.socioEmpresa,
        rfc: editingSolicitud.rfc,
        domicilio: editingSolicitud.domicilio,
        contactoNombre: editingSolicitud.contactoNombre,
        contactoEmail: editingSolicitud.contactoEmail,
        contactoTelefono: editingSolicitud.contactoTelefono,
        lineaSolicitada: editingSolicitud.lineaSolicitada,
        observaciones: editingSolicitud.observaciones || "",
        documentos: documentosBase.map(db => {
          const existingDoc = editingSolicitud.documentos.find(ed => ed.id === db.id);
          return { ...db, url: existingDoc?.url, archivo: undefined }; // No pre-llenar File input
        })
      });
    } else {
      form.reset({
        socioEmpresa: "", rfc: "", domicilio: "", contactoNombre: "", contactoEmail: "", contactoTelefono: "",
        lineaSolicitada: 0, observaciones: "",
        documentos: documentosBase.map(d => ({...d, archivo: undefined, url: undefined}))
      });
    }
  }, [editingSolicitud, form, isFormOpen]);


  const onSubmitForm = (values: SolicitudCreditoFormValues) => {
    const now = new Date();
    const newOrUpdated: SolicitudCredito = {
      id: editingSolicitud?.id || `SC${Date.now().toString().slice(-5)}`,
      ...values,
      fechaSolicitud: editingSolicitud?.fechaSolicitud || now,
      estado: editingSolicitud?.estado || "En Revisión", // Nuevas solicitudes inician "En Revisión"
      lineaOtorgada: editingSolicitud?.lineaOtorgada,
      documentos: values.documentos.map(d => ({
        ...d,
        // Simular URL si se carga un archivo nuevo
        url: d.archivo ? `/docs/credito/${d.archivo.name}` : (editingSolicitud?.documentos.find(ed => ed.id === d.id)?.url),
        estado: d.archivo ? "Cargado" : (editingSolicitud?.documentos.find(ed => ed.id === d.id)?.estado || "Pendiente"),
      })),
    };

    if (editingSolicitud) {
      setSolicitudes(prev => prev.map(s => s.id === editingSolicitud.id ? newOrUpdated : s));
      toast({ title: "Solicitud Actualizada", description: `Solicitud ${newOrUpdated.id} actualizada.` });
    } else {
      setSolicitudes(prev => [newOrUpdated, ...prev]);
      toast({ title: "Solicitud de Crédito Enviada", description: `Tu solicitud ${newOrUpdated.id} está en revisión.` });
    }
    setIsFormOpen(false);
    setEditingSolicitud(null);
  };

  const handleActionSubmit = () => {
    if (!currentSolicitudForAction || !actionType) return;

    let updatedSolicitud: SolicitudCredito = { ...currentSolicitudForAction };
    if (actionType === "aprobar") {
      updatedSolicitud.estado = "Aprobada";
      updatedSolicitud.lineaOtorgada = parseFloat(lineaOtorgadaInput) || 0;
      toast({ title: "Solicitud Aprobada", description: `Crédito para ${updatedSolicitud.socioEmpresa} aprobado.` });
      // Simular notificación a Ventas y Facturación
      toast({ title: "Notificación (Simulada)", description: `Ventas y Facturación notificados sobre crédito aprobado.`});
    } else if (actionType === "rechazar") {
      updatedSolicitud.estado = "Rechazada";
      updatedSolicitud.motivoRechazo = motivoRechazoInput;
      toast({ title: "Solicitud Rechazada", description: `Crédito para ${updatedSolicitud.socioEmpresa} rechazado.` });
    }

    setSolicitudes(prev => prev.map(s => s.id === updatedSolicitud.id ? updatedSolicitud : s));
    setIsActionDialogOpen(false);
    setCurrentSolicitudForAction(null);
    setLineaOtorgadaInput("");
    setMotivoRechazoInput("");
  };

  const handleGenerateConstancia = (solicitud: SolicitudCredito) => {
    if (solicitud.estado !== "Aprobada" || !solicitud.lineaOtorgada) {
        toast({title: "Acción no permitida", description: "Solo se pueden generar constancias para solicitudes aprobadas con línea otorgada.", variant: "destructive"});
        return;
    }
    // Simulación de generación de constancia
    const constanciaContent = `CONSTANCIA DE LÍNEA DE CRÉDITO\n\nEmpresa: ${solicitud.socioEmpresa}\nRFC: ${solicitud.rfc}\nLínea de Crédito Otorgada: ${new Intl.NumberFormat("es-MX", {style: 'currency', currency: 'MXN'}).format(solicitud.lineaOtorgada)}\nFecha de Emisión: ${format(new Date(), "dd/MM/yyyy")}\n\nCOMINTEC S.A. de C.V.`;

    // Crear un blob con el contenido y simular descarga
    const blob = new Blob([constanciaContent], { type: 'text/plain;charset=utf-8' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `Constancia_Credito_${solicitud.socioEmpresa.replace(/\s+/g, '_')}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);

    toast({ title: "Constancia Generada", description: "Se ha descargado la constancia de crédito (simulación)." });
  }


  const columns: ColumnDef<SolicitudCredito>[] = [
    { accessorKey: "id", header: "ID" },
    { accessorKey: "socioEmpresa", header: "Socio/Empresa" },
    { accessorKey: "fechaSolicitud", header: "Fecha Sol.", cell: ({row}) => format(row.original.fechaSolicitud, "dd/MM/yyyy") },
    { accessorKey: "lineaSolicitada", header: "Línea Solicitada", cell: ({row}) => new Intl.NumberFormat("es-MX", {style:'currency', currency:'MXN'}).format(row.original.lineaSolicitada), meta:{className:"text-right"}},
    { accessorKey: "lineaOtorgada", header: "Línea Otorgada", cell: ({row}) => row.original.lineaOtorgada ? new Intl.NumberFormat("es-MX", {style:'currency', currency:'MXN'}).format(row.original.lineaOtorgada) : "N/A", meta:{className:"text-right"}},
    { accessorKey: "estado", header: "Estado", cell: ({row}) => <Badge className={cn("capitalize", getEstadoBadgeClass(row.original.estado))}>{row.original.estado}</Badge>},
    { id: "actions", cell: ({row}) => {
        const s = row.original;
        return (
          <div className="flex items-center space-x-1">
            <Button variant="ghost" size="icon" onClick={() => { setEditingSolicitud(s); setIsDetailOpen(true); }}><Eye className="h-4 w-4"/></Button>
            {s.estado === "En Revisión" && (
                <>
                <Button variant="ghost" size="icon" className="text-moka-lion" onClick={() => {setCurrentSolicitudForAction(s); setActionType("aprobar"); setIsActionDialogOpen(true);}}><CheckCircle className="h-4 w-4"/></Button>
                <Button variant="ghost" size="icon" className="text-moka-falu" onClick={() => {setCurrentSolicitudForAction(s); setActionType("rechazar"); setIsActionDialogOpen(true);}}><XCircle className="h-4 w-4"/></Button>
                </>
            )}
            {s.estado === "Aprobada" && (
                <Button variant="ghost" size="icon" title="Generar Constancia" onClick={() => handleGenerateConstancia(s)}><FileSignature className="h-4 w-4 text-moka-lion"/></Button>
            )}
            <Button variant="ghost" size="icon" onClick={() => { setEditingSolicitud(s); setIsFormOpen(true); }}><Edit className="h-4 w-4"/></Button>
          </div>
        )
    }}
  ];

  const table = useReactTable({ data: solicitudes, columns, getCoreRowModel: getCoreRowModel(), getPaginationRowModel: getPaginationRowModel(), initialState: { pagination: { pageSize:5 }} });

  return (
    <div className="max-w-7xl mx-auto p-6">
      <FormCard>
        <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Solicitudes de Crédito</h1>
            <p className="text-muted-foreground">Gestiona las solicitudes de crédito de socios y empresas.</p>
          </div>
          <Dialog open={isFormOpen} onOpenChange={(open) => { setIsFormOpen(open); if(!open) setEditingSolicitud(null);}}>
            <DialogTrigger asChild><Button onClick={() => {setEditingSolicitud(null); setIsFormOpen(true);}}><PlusCircle className="mr-2 h-4 w-4"/>Nueva Solicitud</Button></DialogTrigger>
            <DialogContent className="sm:max-w-3xl">
              <DialogHeader><DialogTitle>{editingSolicitud ? "Editar" : "Nueva"} Solicitud de Crédito</DialogTitle></DialogHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmitForm)} className="space-y-4 py-4 max-h-[80vh] overflow-y-auto pr-3">
                  {/* Form Fields */}
                  <FormField name="socioEmpresa" control={form.control} render={({field})=>(<FormItem><FormLabel>Socio/Empresa</FormLabel><FormControl><Input {...field}/></FormControl><FormMessage/></FormItem>)}/>
                  <div className="grid md:grid-cols-2 gap-4">
                      <FormField name="rfc" control={form.control} render={({field})=>(<FormItem><FormLabel>RFC</FormLabel><FormControl><Input {...field}/></FormControl><FormMessage/></FormItem>)}/>
                      <FormField name="lineaSolicitada" control={form.control} render={({field})=>(<FormItem><FormLabel>Línea de Crédito Solicitada (MXN)</FormLabel><FormControl><Input type="number" {...field} onChange={e => field.onChange(parseFloat(e.target.value))}/></FormControl><FormMessage/></FormItem>)}/>
                  </div>
                  <FormField name="domicilio" control={form.control} render={({field})=>(<FormItem><FormLabel>Domicilio Fiscal Completo</FormLabel><FormControl><Textarea {...field}/></FormControl><FormMessage/></FormItem>)}/>
                  <h4 className="font-medium pt-2">Información de Contacto</h4>
                   <div className="grid md:grid-cols-3 gap-4">
                      <FormField name="contactoNombre" control={form.control} render={({field})=>(<FormItem><FormLabel>Nombre Contacto</FormLabel><FormControl><Input {...field}/></FormControl><FormMessage/></FormItem>)}/>
                      <FormField name="contactoEmail" control={form.control} render={({field})=>(<FormItem><FormLabel>Email Contacto</FormLabel><FormControl><Input type="email" {...field}/></FormControl><FormMessage/></FormItem>)}/>
                      <FormField name="contactoTelefono" control={form.control} render={({field})=>(<FormItem><FormLabel>Teléfono Contacto</FormLabel><FormControl><Input type="tel" {...field}/></FormControl><FormMessage/></FormItem>)}/>
                  </div>
                  <h4 className="font-medium pt-2">Documentación Requerida</h4>
                  {(form.getValues("documentos") as DocumentoSolicitudCredito[]).map((doc, index) => (
                      <FormField
                          key={doc.id}
                          control={form.control}
                          name={`documentos.${index}.archivo`}
                          render={({ field }) => (
                          <FormItem className="p-3 border rounded-md">
                              <div className="flex items-center justify-between">
                                  <div>
                                      <FormLabel className={cn(doc.obligatorio && "font-semibold")}>
                                      {doc.nombre} {doc.obligatorio && <span className="text-red-500">*</span>}
                                      </FormLabel>
                                      {doc.url && !field.value && <FormDescription className="text-xs"><a href={doc.url} target="_blank" className="text-blue-500 underline">Ver archivo cargado: {doc.url.split('/').pop()}</a></FormDescription>}
                                  </div>
                                  <FormControl><Input type="file" accept=".pdf,.jpg,.png,.doc,.docx" className="max-w-xs" onChange={(e) => field.onChange(e.target.files ? e.target.files[0] : null)} /></FormControl>
                              </div>
                              <FormMessage />
                          </FormItem>
                          )}
                      />
                  ))}
                   <FormField name="observaciones" control={form.control} render={({field})=>(<FormItem><FormLabel>Observaciones Adicionales</FormLabel><FormControl><Textarea {...field}/></FormControl><FormMessage/></FormItem>)}/>

                  <DialogFooter className="pt-4"><DialogClose asChild><Button type="button" variant="outline">Cancelar</Button></DialogClose><Button type="submit">{editingSolicitud ? "Guardar Cambios" : "Enviar Solicitud"}</Button></DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </header>

        {/* Input de búsqueda (opcional) */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>{table.getHeaderGroups().map(hg=>(<TableRow key={hg.id}>{hg.headers.map(h=><TableHead key={h.id} className={h.column.columnDef.meta?.className}>{flexRender(h.column.columnDef.header,h.getContext())}</TableHead>)}</TableRow>))}</TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? table.getRowModel().rows.map(row=>(<TableRow key={row.id}>{row.getVisibleCells().map(cell=><TableCell key={cell.id} className={cell.column.columnDef.meta?.className}>{flexRender(cell.column.columnDef.cell,cell.getContext())}</TableCell>)}</TableRow>)) : <TableRow><TableCell colSpan={columns.length} className="h-24 text-center">No hay solicitudes.</TableCell></TableRow>}
              </TableBody>
            </Table>
          </div>
          <div className="flex items-center justify-end space-x-2 py-4"><Button variant="outline" size="sm" onClick={()=>table.previousPage()} disabled={!table.getCanPreviousPage()}>Anterior</Button><Button variant="outline" size="sm" onClick={()=>table.nextPage()} disabled={!table.getCanNextPage()}>Siguiente</Button></div>
      </FormCard>

      {/* Dialog para ver detalle */}
      <Dialog open={isDetailOpen && !!editingSolicitud} onOpenChange={(open) => { setIsDetailOpen(open); if(!open) setEditingSolicitud(null);}}>
          <DialogContent className="sm:max-w-2xl">
              <DialogHeader><DialogTitle>Detalle Solicitud de Crédito: {editingSolicitud?.id}</DialogTitle></DialogHeader>
              {editingSolicitud && (
                  <div className="py-4 space-y-3 max-h-[70vh] overflow-y-auto pr-2 text-sm">
                      <p><strong>Socio/Empresa:</strong> {editingSolicitud.socioEmpresa}</p>
                      <p><strong>RFC:</strong> {editingSolicitud.rfc}</p>
                      <p><strong>Domicilio:</strong> {editingSolicitud.domicilio}</p>
                      <p><strong>Contacto:</strong> {editingSolicitud.contactoNombre} ({editingSolicitud.contactoEmail}, {editingSolicitud.contactoTelefono})</p>
                      <p><strong>Línea Solicitada:</strong> {new Intl.NumberFormat("es-MX", {style:'currency', currency:'MXN'}).format(editingSolicitud.lineaSolicitada)}</p>
                      {editingSolicitud.lineaOtorgada && <p><strong>Línea Otorgada:</strong> {new Intl.NumberFormat("es-MX", {style:'currency', currency:'MXN'}).format(editingSolicitud.lineaOtorgada)}</p>}
                      <p><strong>Fecha Solicitud:</strong> {format(editingSolicitud.fechaSolicitud, "dd/MM/yyyy")}</p>
                      <p><strong>Estado:</strong> <Badge className={cn("capitalize", getEstadoBadgeClass(editingSolicitud.estado))}>{editingSolicitud.estado}</Badge></p>
                      {editingSolicitud.motivoRechazo && <p><strong>Motivo Rechazo:</strong> {editingSolicitud.motivoRechazo}</p>}
                      {editingSolicitud.observaciones && <p><strong>Observaciones:</strong> {editingSolicitud.observaciones}</p>}
                      <h4 className="font-medium pt-2">Documentos:</h4>
                      <ul className="list-disc pl-5">
                          {editingSolicitud.documentos.map(d => (
                              <li key={d.id}>{d.nombre}: {d.url ? <a href={d.url} target="_blank" className="text-blue-500 underline">Ver Archivo</a> : <span className="text-muted-foreground">No cargado</span>} ({d.estado})</li>
                          ))}
                      </ul>
                  </div>
              )}
              <DialogFooter><DialogClose asChild><Button type="button" variant="outline">Cerrar</Button></DialogClose></DialogFooter>
          </DialogContent>
      </Dialog>

      {/* Dialog para Aprobar/Rechazar */}
      <Dialog open={isActionDialogOpen && !!currentSolicitudForAction} onOpenChange={(open) => { if(!open) setCurrentSolicitudForAction(null); setIsActionDialogOpen(open);}}>
          <DialogContent>
              <DialogHeader>
                  <DialogTitle>{actionType === "aprobar" ? "Aprobar" : "Rechazar"} Solicitud de Crédito: {currentSolicitudForAction?.id}</DialogTitle>
                  <DialogDescription>Para: {currentSolicitudForAction?.socioEmpresa}</DialogDescription>
              </DialogHeader>
              <div className="py-4 space-y-4">
                  {actionType === "aprobar" && (
                      <div>
                          <Label htmlFor="lineaOtorgada">Línea de Crédito Otorgada (MXN)</Label>
                          <Input id="lineaOtorgada" type="number" value={lineaOtorgadaInput} onChange={e => setLineaOtorgadaInput(e.target.value)} placeholder={currentSolicitudForAction?.lineaSolicitada.toString()} />
                          {parseFloat(lineaOtorgadaInput) > (currentSolicitudForAction?.lineaSolicitada || 0) &&
                              <p className="text-xs text-yellow-600 mt-1">La línea otorgada es mayor a la solicitada.</p>}
                      </div>
                  )}
                  {actionType === "rechazar" && (
                      <div>
                          <Label htmlFor="motivoRechazo">Motivo del Rechazo</Label>
                          <Textarea id="motivoRechazo" value={motivoRechazoInput} onChange={e => setMotivoRechazoInput(e.target.value)} placeholder="Describe brevemente el motivo..." />
                      </div>
                  )}
              </div>
              <DialogFooter>
                  <DialogClose asChild><Button type="button" variant="outline">Cancelar</Button></DialogClose>
                  <Button
                      onClick={handleActionSubmit}
                      disabled={(actionType === "aprobar" && !lineaOtorgadaInput) || (actionType === "rechazar" && !motivoRechazoInput)}
                      className={cn(actionType === "aprobar" ? "bg-green-600 hover:bg-green-700" : "bg-red-600 hover:bg-red-700")}
                  >
                      Confirmar {actionType === "aprobar" ? "Aprobación" : "Rechazo"}
                  </Button>
              </DialogFooter>
          </DialogContent>
      </Dialog>
    </div>
  )
}

export default function SolicitudCreditoPage() {
  return (
    <PageGuard
      requiredPermissions={["admin:solicitud_credito:read", "admin:solicitud_credito:create"]}
      unauthorizedTitle="Acceso Denegado - Solicitud de Crédito"
      unauthorizedMessage="No tienes los permisos necesarios para acceder a la sección de Solicitud de Crédito."
    >
      <SolicitudCreditoContent />
    </PageGuard>
  );
}
