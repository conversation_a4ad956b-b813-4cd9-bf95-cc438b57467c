"use client"

import * as React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, ChevronDown, MoreHorizontal, Edit, TrendingUp, DollarSign, Send, AlertTriangle } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { toast } from "@/components/ui/use-toast"
import { format, differenceInDays, addDays } from "date-fns"
import { cn } from "@/lib/utils"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { PageGuard } from "@/components/auth/page-guard"
import { FormCard } from "@/components/ui/form-styles"


export type FacturaMorosa = {
  id: string // ID de la factura original
  numeroFactura: string
  clienteNombre: string
  fechaFactura: Date
  fechaVencimiento: Date
  montoTotal: number
  montoPagado: number
  diasVencidos?: number // Se calculará
  tipoFacturacion: "Contado" | "Crédito" | "Anticipo"
  estadoPago: "No Pagado" | "Abonado" | "Pagado" // Aunque aquí deberían ser no pagadas o abonadas
  // Nuevos campos para la gestión de morosidad
  ultimoSeguimiento?: Date
  notasSeguimiento?: string
  responsableVendedor?: string // Para notificar
}

// Datos simulados
const initialFacturasMorosas: FacturaMorosa[] = [
  { id: "FACT002", numeroFactura: "B-5678", clienteNombre: "Tech Solutions", fechaFactura: new Date("2023-10-15"), fechaVencimiento: new Date("2023-11-14"), montoTotal: 8500, montoPagado: 0, tipoFacturacion: "Crédito", estadoPago: "No Pagado", responsableVendedor: "Ana Gómez" },
  { id: "FACT003", numeroFactura: "C-9101", clienteNombre: "Maquinaria Pesada Inc.", fechaFactura: new Date("2023-11-05"), fechaVencimiento: new Date("2023-12-05"), montoTotal: 22000, montoPagado: 10000, tipoFacturacion: "Crédito", estadoPago: "Abonado", responsableVendedor: "Carlos Ruiz" },
  { id: "FACT005", numeroFactura: "E-2023", clienteNombre: "Innovatech Global", fechaFactura: new Date("2023-09-01"), fechaVencimiento: new Date("2023-09-16"), montoTotal: 12500, montoPagado: 0, tipoFacturacion: "Crédito", estadoPago: "No Pagado", responsableVendedor: "Juan Pérez", ultimoSeguimiento: new Date("2023-11-10"), notasSeguimiento: "Cliente indica pago la próxima semana." },
  { id: "FACT006", numeroFactura: "F-0070", clienteNombre: "Consultores Alpha", fechaFactura: new Date("2023-11-20"), fechaVencimiento: new Date("2023-12-20"), montoTotal: 5000, montoPagado: 0, tipoFacturacion: "Contado", estadoPago: "No Pagado", responsableVendedor: "Laura Méndez" },
];

const calcularDiasVencidos = (fechaVencimiento: Date): number => {
  const hoy = new Date();
  // Solo calcular días vencidos si la fecha de vencimiento ya pasó
  return differenceInDays(hoy, fechaVencimiento) > 0 ? differenceInDays(hoy, fechaVencimiento) : 0;
};

const getDiasVencidosBadgeClass = (dias: number = 0): string => {
  if (dias === 0) return "bg-moka-lion/30 text-moka-bistre border border-moka-brown/40 dark:bg-green-700/30 dark:text-green-300"; // No vencido o pagado
  if (dias <= 7) return "bg-moka-peach/80 text-moka-bistre border border-moka-brown/40 dark:bg-yellow-600/30 dark:text-yellow-300"; // Poco vencido
  if (dias <= 30) return "bg-moka-peach/60 text-moka-bistre border border-moka-brown/40 dark:bg-orange-600/30 dark:text-orange-300"; // Vencido
  return "bg-moka-falu/20 text-moka-falu border border-moka-falu/40 dark:bg-red-700/30 dark:text-red-300"; // Muy vencido
}


export default function MorososPage() {
  return (
    <PageGuard
      requiredPermissions={["admin:morosos:read", "admin:morosos:list"]}
      unauthorizedTitle="Acceso Denegado - Morosos"
      unauthorizedMessage="No tienes los permisos necesarios para acceder a la sección de Morosos."
    >
      <MorososContent />
    </PageGuard>
  );
}

function MorososContent() {
  const [facturas, setFacturas] = React.useState<FacturaMorosa[]>(
    initialFacturasMorosas.map(f => ({...f, diasVencidos: calcularDiasVencidos(f.fechaVencimiento)}))
        .filter(f => f.estadoPago !== "Pagado") // Solo mostrar no pagadas o abonadas
  );
  const [sorting, setSorting] = React.useState<SortingState>([{ id: "diasVencidos", desc: true }]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);

  const [isPagoDialogOpen, setIsPagoDialogOpen] = React.useState(false);
  const [isSeguimientoDialogOpen, setIsSeguimientoDialogOpen] = React.useState(false);
  const [currentFactura, setCurrentFactura] = React.useState<FacturaMorosa | null>(null);
  const [montoAbono, setMontoAbono] = React.useState<string>("");
  const [notasInput, setNotasInput] = React.useState<string>("");


  const handleRegistrarPago = () => {
    if (!currentFactura) return;
    const abono = parseFloat(montoAbono);
    if (isNaN(abono) || abono <= 0) {
      toast({ title: "Error", description: "El monto del abono debe ser un número positivo.", variant: "destructive" });
      return;
    }
    if (abono > (currentFactura.montoTotal - currentFactura.montoPagado)) {
      toast({ title: "Error", description: "El abono no puede exceder el saldo pendiente.", variant: "destructive" });
      return;
    }

    const updatedFacturas = facturas.map(f => {
      if (f.id === currentFactura.id) {
        const nuevoMontoPagado = f.montoPagado + abono;
        const nuevoEstadoPago = nuevoMontoPagado >= f.montoTotal ? "Pagado" : "Abonado";
        return { ...f, montoPagado: nuevoMontoPagado, estadoPago: nuevoEstadoPago as FacturaMorosa["estadoPago"], diasVencidos: nuevoEstadoPago === "Pagado" ? 0 : f.diasVencidos };
      }
      return f;
    }).filter(f => f.estadoPago !== "Pagado"); // Quitarla de morosos si se pagó completamente

    setFacturas(updatedFacturas);
    toast({ title: "Pago Registrado", description: `Abono de ${abono} registrado para factura ${currentFactura.numeroFactura}.` });
    // Simular notificación a Informes para liberación
    toast({ title: "Notificación (Simulada)", description: `Informes notificado para liberación de factura ${currentFactura.numeroFactura}.`});
    if (currentFactura.responsableVendedor) {
        toast({ title: "Notificación (Simulada)", description: `Vendedor ${currentFactura.responsableVendedor} notificado del pago.`});
    }

    setIsPagoDialogOpen(false);
    setMontoAbono("");
    setCurrentFactura(null);
  };

  const handleGuardarSeguimiento = () => {
    if(!currentFactura) return;
    const updatedFacturas = facturas.map(f =>
        f.id === currentFactura.id ? {...f, ultimoSeguimiento: new Date(), notasSeguimiento: notasInput} : f
    );
    setFacturas(updatedFacturas);
    toast({title: "Seguimiento Guardado", description: `Notas actualizadas para factura ${currentFactura.numeroFactura}.`});
    setIsSeguimientoDialogOpen(false);
    setNotasInput("");
    setCurrentFactura(null);
  }

  const handleMarcarTipoFacturacion = (facturaId: string, tipo: FacturaMorosa["tipoFacturacion"]) => {
    setFacturas(prev => prev.map(f => f.id === facturaId ? {...f, tipoFacturacion: tipo} : f));
    toast({title: "Tipo de Facturación Actualizado"});
  }


  const columns: ColumnDef<FacturaMorosa>[] = [
    { accessorKey: "numeroFactura", header: "Nº Factura" },
    { accessorKey: "clienteNombre", header: "Cliente" },
    { accessorKey: "fechaVencimiento", header: "F. Vencimiento", cell: ({row}) => format(row.original.fechaVencimiento, "dd/MM/yyyy")},
    {
      accessorKey: "diasVencidos",
      header: ({ column }) => <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>Días Vencidos <ArrowUpDown className="ml-2 h-4 w-4" /></Button>,
      cell: ({row}) => <Badge className={cn("text-xs", getDiasVencidosBadgeClass(row.original.diasVencidos))}>{row.original.diasVencidos} días</Badge>,
      meta: {className: "text-center"}
    },
    { accessorKey: "montoTotal", header: "Monto Total", cell: ({row}) => new Intl.NumberFormat("es-MX",{style:'currency',currency:'MXN'}).format(row.original.montoTotal), meta:{className:"text-right"}},
    { accessorKey: "montoPagado", header: "Monto Pagado", cell: ({row}) => new Intl.NumberFormat("es-MX",{style:'currency',currency:'MXN'}).format(row.original.montoPagado), meta:{className:"text-right"}},
    {
      accessorKey: "saldoPendiente",
      header: "Saldo Pendiente",
      cell: ({row}) => new Intl.NumberFormat("es-MX",{style:'currency',currency:'MXN'}).format(row.original.montoTotal - row.original.montoPagado),
      meta: {className: "text-right font-semibold"}
    },
    {
      accessorKey: "tipoFacturacion",
      header: "Tipo Fact.",
      cell: ({row}) => (
        <Select value={row.original.tipoFacturacion} onValueChange={(value: any) => handleMarcarTipoFacturacion(row.original.id, value as FacturaMorosa["tipoFacturacion"])}>
            <SelectTrigger className="h-8 text-xs w-[100px]"><SelectValue /></SelectTrigger>
            <SelectContent>
                <SelectItem value="Contado">Contado</SelectItem>
                <SelectItem value="Crédito">Crédito</SelectItem>
                <SelectItem value="Anticipo">Anticipo</SelectItem>
            </SelectContent>
        </Select>
      )
    },
    { accessorKey: "estadoPago", header: "Estado Pago", cell: ({row}) => <Badge variant={row.original.estadoPago === "Abonado" ? "default" : "outline"} className={cn(row.original.estadoPago === "Abonado" ? "bg-blue-100 text-blue-700" : "border-yellow-500 text-yellow-600")}>{row.original.estadoPago}</Badge>},
    {
      id: "actions",
      cell: ({ row }) => {
        const factura = row.original;
        return (
          <div className="flex items-center space-x-1">
            <Button variant="outline" size="sm" className="text-xs" onClick={() => {setCurrentFactura(factura); setMontoAbono(""); setIsPagoDialogOpen(true);}}>
              <DollarSign className="mr-1 h-3 w-3"/> Registrar Pago
            </Button>
            <Button variant="outline" size="sm" className="text-xs" onClick={() => {setCurrentFactura(factura); setNotasInput(factura.notasSeguimiento || ""); setIsSeguimientoDialogOpen(true);}}>
              <Edit className="mr-1 h-3 w-3"/> Seguimiento
            </Button>
             <Button variant="ghost" size="sm" className="text-xs text-blue-600" onClick={() => {
                 if(factura.responsableVendedor) toast({title: "Notificación (Simulada)", description: `Recordatorio enviado a ${factura.responsableVendedor} sobre factura ${factura.numeroFactura}.`});
                 else toast({title: "Info", description: "No hay vendedor asignado para esta factura.", variant:"default"});
             }}>
              <Send className="mr-1 h-3 w-3"/> Recordar
            </Button>
          </div>
        )
      }
    }
  ];

  const table = useReactTable({
    data: facturas, columns, getCoreRowModel: getCoreRowModel(), getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting, getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters, getFilteredRowModel: getFilteredRowModel(),
    state: { sorting, columnFilters },
    initialState: { pagination: { pageSize: 10 }}
  });

  return (
    <div className="max-w-7xl mx-auto p-6">
      <FormCard>
        <header className="mb-6">
          <h1 className="text-3xl font-bold tracking-tight">Gestión de Cartera Vencida (Morosos)</h1>
          <p className="text-muted-foreground">Seguimiento de facturas con pagos pendientes o vencidos. Se notificará a Informes para liberación de pagos y a vendedores sobre saldos de sus clientes.</p>
        </header>
          <div className="flex items-center py-4 gap-2">
            <Input placeholder="Filtrar por Nº Factura o Cliente..." value={(table.getColumn("clienteNombre")?.getFilterValue() as string) ?? ""} onChange={(e) => table.getColumn("clienteNombre")?.setFilterValue(e.target.value)} className="max-w-sm" />
          </div>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableHead key={header.id}>
                        {flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                  {table.getRowModel().rows?.length ? table.getRowModel().rows.map(row=>(<TableRow key={row.id} className={cn((row.original.diasVencidos || 0) > 30 && "bg-red-50 dark:bg-red-900/10")}>{row.getVisibleCells().map(cell=><TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell,cell.getContext())}</TableCell>)}</TableRow>)) : <TableRow><TableCell colSpan={columns.length} className="h-24 text-center">No hay facturas en cartera vencida.</TableCell></TableRow>}
                </TableBody>
              </Table>
            </div>
            <div className="flex items-center justify-end space-x-2 py-4"><Button variant="outline" size="sm" onClick={()=>table.previousPage()} disabled={!table.getCanPreviousPage()}>Anterior</Button><Button variant="outline" size="sm" onClick={()=>table.nextPage()} disabled={!table.getCanNextPage()}>Siguiente</Button></div>

        {/* Dialog para registrar pago */}
        <Dialog open={isPagoDialogOpen} onOpenChange={(open: any) => { if (!open) setCurrentFactura(null); setIsPagoDialogOpen(open); }}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Registrar Pago/Abono para Factura: {currentFactura?.numeroFactura}</DialogTitle>
            </DialogHeader>
            <div className="py-4 space-y-2">
              <p className="text-sm">Cliente: <strong>{currentFactura?.clienteNombre}</strong></p>
              <p className="text-sm">Saldo Actual: <strong>{new Intl.NumberFormat("es-MX", { style: 'currency', currency: 'MXN' }).format((currentFactura?.montoTotal || 0) - (currentFactura?.montoPagado || 0))}</strong></p>
              <div>
                <Label htmlFor="montoAbono">Monto del Pago/Abono (MXN)</Label>
                <Input id="montoAbono" type="number" value={montoAbono} onChange={e => setMontoAbono(e.target.value)} placeholder="0.00" />
              </div>
            </div>
            <DialogFooter>
              <DialogClose asChild><Button variant="outline">Cancelar</Button></DialogClose>
              <Button onClick={handleRegistrarPago} disabled={!montoAbono || parseFloat(montoAbono) <= 0}>Confirmar Pago</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Dialog para seguimiento */}
        <Dialog open={isSeguimientoDialogOpen} onOpenChange={(open: any) => { if (!open) setCurrentFactura(null); setIsSeguimientoDialogOpen(open); }}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Notas de Seguimiento - Factura: {currentFactura?.numeroFactura}</DialogTitle>
            </DialogHeader>
            <div className="py-4 space-y-2">
              <p className="text-sm">Cliente: <strong>{currentFactura?.clienteNombre}</strong></p>
              {currentFactura?.ultimoSeguimiento && <p className="text-xs text-muted-foreground">Último seguimiento: {format(currentFactura.ultimoSeguimiento, "dd/MM/yyyy HH:mm")}</p>}
              <div>
                <Label htmlFor="notasInput">Nuevas Notas / Actualizar Notas</Label>
                <Textarea id="notasInput" value={notasInput} onChange={e => setNotasInput(e.target.value)} placeholder="Añadir comentarios sobre el seguimiento, acuerdos de pago, etc." rows={4} />
              </div>
            </div>
            <DialogFooter>
              <DialogClose asChild><Button variant="outline">Cancelar</Button></DialogClose>
              <Button onClick={handleGuardarSeguimiento} disabled={!notasInput.trim()}>Guardar Notas</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </FormCard>
    </div>
  );
}
