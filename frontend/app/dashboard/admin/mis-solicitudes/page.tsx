"use client"

import * as React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, ChevronDown, MoreHorizontal, Eye } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { PermissionGuard } from '@/components/auth/permission-guard'
import { PageGuard } from "@/components/auth/page-guard"
import { FormCard } from "@/components/ui/form-styles"


export type Solicitud = {
  id: string
  tipo: "Viático" | "Paquetería" | "Tiempo" | "Crédito"
  fechaSolicitud: string // Formato YYYY-MM-DD para facilitar ordenamiento
  estado: "Pendiente" | "Aprobado" | "Rechazado" | "En Proceso"
  descripcion: string // Ej: Destino del viático, contenido paquetería, etc.
  monto?: number // Solo para viáticos o créditos
  detalleCompleto: Record<string, any> // Objeto con todos los datos de la solicitud original
}

// Datos simulados - Asumimos que estas son las solicitudes del usuario actual o todas si es admin con capacidad de verlas
const data: Solicitud[] = [
  {
    id: "SOL001",
    tipo: "Viático",
    fechaSolicitud: "2023-10-15",
    estado: "Aprobado",
    descripcion: "Visita cliente Tech Solutions en CDMX",
    monto: 3500,
    detalleCompleto: { solicitante: "Juan Pérez", destino: "CDMX", motivo: "Visita Cliente", fechas: "2023-10-20 a 2023-10-22", anticipo: 3500 },
  },
  {
    id: "SOL002",
    tipo: "Paquetería",
    fechaSolicitud: "2023-10-18",
    estado: "Pendiente",
    descripcion: "Envío de documentos a proveedor Alpha",
    detalleCompleto: { remitente: "Ana Gómez", destinatario: "Proveedor Alpha", contenido: "Documentos contractuales", servicio: "Express" },
  },
  {
    id: "SOL003",
    tipo: "Tiempo",
    fechaSolicitud: "2023-10-20",
    estado: "Rechazado",
    descripcion: "Solicitud de vacaciones (2 días)",
    detalleCompleto: { empleado: "Carlos Ruiz", tipoAusencia: "Vacaciones", fechas: "2023-11-01 a 2023-11-02", motivoRechazo: "Periodo de alta demanda" },
  },
  {
    id: "SOL004",
    tipo: "Viático",
    fechaSolicitud: "2023-10-22",
    estado: "En Proceso",
    descripcion: "Asistencia a feria industrial en Monterrey",
    monto: 6000,
    detalleCompleto: { solicitante: "Laura Méndez", destino: "Monterrey", motivo: "Feria Industrial", fechas: "2023-11-10 a 2023-11-13", anticipo: 6000 },
  },
  {
    id: "SOL005",
    tipo: "Crédito",
    fechaSolicitud: "2023-10-25",
    estado: "Pendiente",
    descripcion: "Solicitud de línea de crédito para Compumayor S.A.",
    monto: 150000,
    detalleCompleto: { socio: "Compumayor S.A.", rfc: "CSA123456XYZ", linea_solicitada: 150000, documentos_adjuntos: 3 },
  },
   {
    id: "SOL006",
    tipo: "Paquetería",
    fechaSolicitud: "2023-11-01",
    estado: "Aprobado",
    descripcion: "Muestras a cliente Beta Inc.",
    detalleCompleto: { remitente: "Ventas Dept.", destinatario: "Beta Inc.", contenido: "Muestras de producto X", servicio: "Estándar" },
  },
  {
    id: "SOL007",
    tipo: "Viático",
    fechaSolicitud: "2023-11-05",
    estado: "Pendiente",
    descripcion: "Capacitación técnica en Guadalajara",
    monto: 2800,
    detalleCompleto: { solicitante: "Pedro Nave", destino: "Guadalajara", motivo: "Capacitación XYZ", fechas: "2023-11-20 a 2023-11-21", anticipo: 2800 },
  },
]

const getEstadoBadgeVariant = (estado: Solicitud["estado"]): "default" | "secondary" | "destructive" | "outline" => {
  switch (estado) {
    case "Aprobado":
      return "default" // Verde
    case "Pendiente":
      return "secondary" // Amarillo/Naranja
    case "Rechazado":
      return "destructive" // Rojo
    case "En Proceso":
      return "outline" // Azul/Gris
    default:
      return "secondary"
  }
}
const getEstadoBadgeClass = (estado: Solicitud["estado"]): string => {
 switch (estado) {
    case "Aprobado":
      return "bg-green-100 text-green-700 dark:bg-green-700/30 dark:text-green-300 border-green-400"
    case "Pendiente":
      return "bg-yellow-100 text-yellow-700 dark:bg-yellow-600/30 dark:text-yellow-300 border-yellow-400"
    case "Rechazado":
      return "bg-red-100 text-red-700 dark:bg-red-700/30 dark:text-red-300 border-red-400"
    case "En Proceso":
      return "bg-blue-100 text-blue-700 dark:bg-blue-700/30 dark:text-blue-300 border-blue-400"
    default:
      return ""
  }
}

function MisSolicitudesContent() {
  const [sorting, setSorting] = React.useState<SortingState>([{ id: "fechaSolicitud", desc: true }])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState({})
  const [selectedSolicitud, setSelectedSolicitud] = React.useState<Solicitud | null>(null)

  const columns: ColumnDef<Solicitud>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "id",
      header: "ID Solicitud",
      cell: ({ row }) => <div className="capitalize">{row.getValue("id")}</div>,
    },
    {
      accessorKey: "tipo",
      header: "Tipo",
      cell: ({ row }) => <div className="capitalize">{row.getValue("tipo")}</div>,
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id))
      }
    },
    {
      accessorKey: "fechaSolicitud",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Fecha Solicitud
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        const date = new Date(row.getValue("fechaSolicitud"))
        // Ajustar la fecha para que no se vea afectada por la zona horaria al formatear
        const userTimezoneOffset = date.getTimezoneOffset() * 60000;
        const adjustedDate = new Date(date.getTime() + userTimezoneOffset);
        return <div className="lowercase">{adjustedDate.toLocaleDateString('es-MX')}</div>
      }
    },
    {
      accessorKey: "descripcion",
      header: "Descripción",
      cell: ({ row }) => <div className="truncate max-w-xs">{row.getValue("descripcion")}</div>,
    },
    {
      accessorKey: "monto",
      header: () => <div className="text-right">Monto</div>,
      cell: ({ row }) => {
        const monto = parseFloat(row.getValue("monto") || "0")
        const formatted = monto > 0 ? new Intl.NumberFormat("es-MX", {
          style: "currency",
          currency: "MXN",
        }).format(monto) : "N/A"
        return <div className="text-right font-medium">{formatted}</div>
      },
    },
    {
      accessorKey: "estado",
      header: "Estado",
      cell: ({ row }) => (
        <Badge variant={getEstadoBadgeVariant(row.getValue("estado"))} className={getEstadoBadgeClass(row.getValue("estado"))}>
          {row.getValue("estado")}
        </Badge>
      ),
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id))
      }
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const solicitud = row.original
        return (
          <Button variant="ghost" className="h-8 w-8 p-0" onClick={() => setSelectedSolicitud(solicitud)}>
            <span className="sr-only">Ver detalle</span>
            <Eye className="h-4 w-4" />
          </Button>
        )
      },
    },
  ]


  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
    initialState: {
        pagination: {
            pageSize: 7, // Mostrar 7 filas por página
        }
    }
  })

  const tiposDeSolicitud = Array.from(new Set(data.map(item => item.tipo)))
  const estadosDeSolicitud = Array.from(new Set(data.map(item => item.estado)))

  return (
    <div className="max-w-7xl mx-auto p-6">
      <FormCard>
        <header className="mb-6">
          <h1 className="text-3xl font-bold tracking-tight">Mis Solicitudes</h1>
          <p className="text-muted-foreground">
            Gestiona y revisa todas tus solicitudes enviadas al sistema.
          </p>
        </header>

        <div className="flex items-center py-4 gap-2 flex-wrap">
        <Input
          placeholder="Filtrar por descripción..."
          value={(table.getColumn("descripcion")?.getFilterValue() as string) ?? ""}
          onChange={(event) =>
            table.getColumn("descripcion")?.setFilterValue(event.target.value)
          }
          className="max-w-sm"
        />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="ml-auto">
              Tipo <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {tiposDeSolicitud.map((tipo) => {
              return (
                <DropdownMenuCheckboxItem
                  key={tipo}
                  className="capitalize"
                  checked={(table.getColumn("tipo")?.getFilterValue() as string[])?.includes(tipo)}
                  onCheckedChange={(checked) => {
                    const currentFilter = (table.getColumn("tipo")?.getFilterValue() as string[]) || []
                    if (checked) {
                      table.getColumn("tipo")?.setFilterValue([...currentFilter, tipo])
                    } else {
                      table.getColumn("tipo")?.setFilterValue(currentFilter.filter(val => val !== tipo))
                    }
                  }}
                >
                  {tipo}
                </DropdownMenuCheckboxItem>
              )
            })}
             <DropdownMenuSeparator />
            <DropdownMenuItem onSelect={() => table.getColumn("tipo")?.setFilterValue(undefined)}>
              Limpiar filtro
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline">
              Estado <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {estadosDeSolicitud.map((estado) => {
              return (
                <DropdownMenuCheckboxItem
                  key={estado}
                  className="capitalize"
                  checked={(table.getColumn("estado")?.getFilterValue() as string[])?.includes(estado)}
                   onCheckedChange={(checked) => {
                    const currentFilter = (table.getColumn("estado")?.getFilterValue() as string[]) || []
                    if (checked) {
                      table.getColumn("estado")?.setFilterValue([...currentFilter, estado])
                    } else {
                      table.getColumn("estado")?.setFilterValue(currentFilter.filter(val => val !== estado))
                    }
                  }}
                >
                  {estado}
                </DropdownMenuCheckboxItem>
              )
            })}
            <DropdownMenuSeparator />
            <DropdownMenuItem onSelect={() => table.getColumn("estado")?.setFilterValue(undefined)}>
              Limpiar filtro
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline">
              Columnas <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id === "fechaSolicitud" ? "Fecha Solicitud" : column.id}
                  </DropdownMenuCheckboxItem>
                )
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No hay resultados.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} de{" "}
          {table.getFilteredRowModel().rows.length} fila(s) seleccionadas.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Anterior
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Siguiente
          </Button>
        </div>
      </div>
      </FormCard>

      {/* Dialog para ver detalles de solicitud */}
      <Dialog open={!!selectedSolicitud} onOpenChange={(open) => !open && setSelectedSolicitud(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Detalle de Solicitud</DialogTitle>
            <DialogDescription>
              Información completa de la solicitud seleccionada
            </DialogDescription>
          </DialogHeader>
          {selectedSolicitud && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Tipo:</label>
                  <p className="text-sm text-muted-foreground">{selectedSolicitud.tipo}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Estado:</label>
                  <p className="text-sm text-muted-foreground">{selectedSolicitud.estado}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Fecha:</label>
                  <p className="text-sm text-muted-foreground">{selectedSolicitud.fecha}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Prioridad:</label>
                  <p className="text-sm text-muted-foreground">{selectedSolicitud.prioridad}</p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium">Descripción:</label>
                <p className="text-sm text-muted-foreground mt-1">{selectedSolicitud.descripcion}</p>
              </div>
            </div>
          )}
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cerrar</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default function MisSolicitudesPage() {
  return (
    <PageGuard
      requiredPermissions={["admin:solicitudes:read", "admin:solicitudes:list"]}
      unauthorizedTitle="Acceso Denegado - Mis Solicitudes"
      unauthorizedMessage="No tienes los permisos necesarios para acceder a la sección de Mis Solicitudes."
    >
      <MisSolicitudesContent />
    </PageGuard>
  )
}
