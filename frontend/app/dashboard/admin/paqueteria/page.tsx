"use client"

import React from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { toast } from "@/components/ui/use-toast"
import { Combobox } from "@/components/ui/combobox"
import { PageGuard } from "@/components/auth/page-guard"
import {
  FormCard,
  FormSection,
  FormGroup,
  CleanInput,
  CleanTextarea,
  CleanSelect,
  CleanSelectTrigger,
  CleanSelectContent,
  CleanSelectItem,
} from "@/components/ui/form-styles"

// Esquema de validación con Zod
const paqueteriaFormSchema = z.object({
  areaSolicitante: z.string().min(1, "El área solicitante es requerida."),
  // Remitente
  remitenteNombre: z.string().min(1, "Nombre del remitente es requerido."),
  remitenteTelefono: z.string().min(10, "Teléfono del remitente debe tener al menos 10 dígitos.").max(15),
  remitenteCalle: z.string().min(1, "Calle del remitente es requerida."),
  remitenteNumeroExt: z.string().min(1, "Número exterior del remitente es requerido."),
  remitenteNumeroInt: z.string().optional(),
  remitenteColonia: z.string().min(1, "Colonia del remitente es requerida."),
  remitenteCiudad: z.string().min(1, "Ciudad del remitente es requerida."),
  remitenteEstado: z.string().min(1, "Estado del remitente es requerido."),
  remitenteCP: z.string().min(5, "CP del remitente debe tener 5 dígitos.").max(5),
  remitenteReferencia: z.string().optional(),

  // Destinatario
  destinatarioNombre: z.string().min(1, "Nombre del destinatario es requerido."),
  destinatarioTelefono: z.string().min(10, "Teléfono del destinatario debe tener al menos 10 dígitos.").max(15),
  destinatarioCalle: z.string().min(1, "Calle del destinatario es requerida."),
  destinatarioNumeroExt: z.string().min(1, "Número exterior del destinatario es requerido."),
  destinatarioNumeroInt: z.string().optional(),
  destinatarioColonia: z.string().min(1, "Colonia del destinatario es requerida."),
  destinatarioCiudad: z.string().min(1, "Ciudad del destinatario es requerida."),
  destinatarioEstado: z.string().min(1, "Estado del destinatario es requerido."),
  destinatarioCP: z.string().min(5, "CP del destinatario debe tener 5 dígitos.").max(5),
  destinatarioReferencia: z.string().optional(),

  // Paquete
  contenidoPaquete: z.string().min(1, "El contenido del paquete es requerido."),
  valorDeclarado: z.preprocess(
    (a) => parseFloat(z.string().parse(a)),
    z.number().nonnegative("El valor declarado no puede ser negativo.")
  ),
  largoCm: z.preprocess((a) => parseFloat(z.string().parse(a)), z.number().positive("Largo debe ser positivo.")),
  anchoCm: z.preprocess((a) => parseFloat(z.string().parse(a)), z.number().positive("Ancho debe ser positivo.")),
  altoCm: z.preprocess((a) => parseFloat(z.string().parse(a)), z.number().positive("Alto debe ser positivo.")),
  pesoKg: z.preprocess((a) => parseFloat(z.string().parse(a)), z.number().positive("Peso debe ser positivo.")),
  tipoServicio: z.string({ required_error: "Selecciona un tipo de servicio." }),
  comentarios: z.string().optional(),
})

type PaqueteriaFormValues = z.infer<typeof paqueteriaFormSchema>

const areasEmpresa = [
  { value: "ventas", label: "Ventas" },
  { value: "logistica", label: "Logística" },
  { value: "servicio_tecnico", label: "Servicio Técnico" },
  { value: "administracion", label: "Administración" },
  { value: "metrologia", label: "Metrología" },
  { value: "calidad", label: "Calidad" },
  { value: "almacen", label: "Almacén" },
  { value: "otro", label: "Otro" },
]

function SolicitudPaqueteriaContent() {
  const form = useForm<PaqueteriaFormValues>({
    resolver: zodResolver(paqueteriaFormSchema),
    defaultValues: {
      valorDeclarado: 0,
      // ... otros valores por defecto si son necesarios
    },
  })

  function onSubmit(data: PaqueteriaFormValues) {
    console.log(data)
    toast({
      title: "Solicitud de Paquetería Enviada (Simulación)",
      description: (
        <pre className="mt-2 w-full max-w-[400px] overflow-x-auto rounded-md bg-slate-950 p-4">
          <code className="text-white">{JSON.stringify(data, null, 2)}</code>
        </pre>
      ),
    })
    // form.reset(); // Opcional
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <FormCard>
        <FormSection
          title="Solicitud de Paquetería"
          description="Completa todos los campos para generar una nueva solicitud de envío de paquetería."
        >
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <FormSection title="Información General">
                <FormGroup>
                  <FormField
                    control={form.control}
                    name="areaSolicitante"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Área/Departamento Solicitante</FormLabel>
                        <Combobox
                          options={areasEmpresa}
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Selecciona un área..."
                          searchPlaceholder="Buscar área..."
                          notFoundText="Área no encontrada."
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>
              </FormSection>

              <FormSection title="Datos del Remitente">
                <FormGroup>
                  <FormField
                    control={form.control}
                    name="remitenteNombre"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nombre Completo</FormLabel>
                        <FormControl>
                          <CleanInput placeholder="Nombre del remitente" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="remitenteTelefono"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Teléfono</FormLabel>
                        <FormControl>
                          <CleanInput type="tel" placeholder="Ej: 3312345678" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>

                <FormGroup>
                  <FormField
                    control={form.control}
                    name="remitenteCalle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Calle</FormLabel>
                        <FormControl>
                          <CleanInput placeholder="Calle" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="remitenteNumeroExt"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Número Exterior</FormLabel>
                        <FormControl>
                          <CleanInput placeholder="Num. Ext." {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="remitenteNumeroInt"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Número Interior (Opcional)</FormLabel>
                        <FormControl>
                          <CleanInput placeholder="Num. Int." {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>

                <FormGroup>
                  <FormField
                    control={form.control}
                    name="remitenteColonia"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Colonia</FormLabel>
                        <FormControl>
                          <CleanInput placeholder="Colonia" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="remitenteCiudad"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Ciudad</FormLabel>
                        <FormControl>
                          <CleanInput placeholder="Ciudad" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="remitenteEstado"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Estado</FormLabel>
                        <FormControl>
                          <CleanInput placeholder="Estado" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="remitenteCP"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Código Postal</FormLabel>
                        <FormControl>
                          <CleanInput placeholder="CP (5 dígitos)" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>

                <FormGroup>
                  <FormField
                    control={form.control}
                    name="remitenteReferencia"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Referencia Adicional Domicilio (Opcional)</FormLabel>
                        <FormControl>
                          <CleanTextarea
                            placeholder="Ej: Entre calles, color de fachada, etc."
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>
              </FormSection>

              <FormSection title="Datos del Destinatario">
                <FormGroup>
                  <FormField
                    control={form.control}
                    name="destinatarioNombre"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nombre Completo</FormLabel>
                        <FormControl>
                          <CleanInput placeholder="Nombre del destinatario" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="destinatarioTelefono"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Teléfono</FormLabel>
                        <FormControl>
                          <CleanInput type="tel" placeholder="Ej: 5512345678" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>

                <FormGroup>
                  <FormField
                    control={form.control}
                    name="destinatarioCalle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Calle</FormLabel>
                        <FormControl>
                          <CleanInput placeholder="Calle" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="destinatarioNumeroExt"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Número Exterior</FormLabel>
                        <FormControl>
                          <CleanInput placeholder="Num. Ext." {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="destinatarioNumeroInt"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Número Interior (Opcional)</FormLabel>
                        <FormControl>
                          <CleanInput placeholder="Num. Int." {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>

                <FormGroup>
                  <FormField
                    control={form.control}
                    name="destinatarioColonia"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Colonia</FormLabel>
                        <FormControl>
                          <CleanInput placeholder="Colonia" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="destinatarioCiudad"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Ciudad</FormLabel>
                        <FormControl>
                          <CleanInput placeholder="Ciudad" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="destinatarioEstado"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Estado</FormLabel>
                        <FormControl>
                          <CleanInput placeholder="Estado" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="destinatarioCP"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Código Postal</FormLabel>
                        <FormControl>
                          <CleanInput placeholder="CP (5 dígitos)" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>

                <FormGroup>
                  <FormField
                    control={form.control}
                    name="destinatarioReferencia"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Referencia Adicional Domicilio (Opcional)</FormLabel>
                        <FormControl>
                          <CleanTextarea
                            placeholder="Ej: Entre calles, color de fachada, etc."
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>
              </FormSection>

              <FormSection title="Detalles del Paquete">
                <FormGroup>
                  <FormField
                    control={form.control}
                    name="contenidoPaquete"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Contenido del Paquete</FormLabel>
                        <FormControl>
                          <CleanTextarea
                            placeholder="Descripción breve pero clara del contenido."
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="valorDeclarado"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Valor Declarado (MXN)</FormLabel>
                        <FormControl>
                          <CleanInput
                            type="number"
                            placeholder="0.00"
                            {...field}
                            onChange={event => field.onChange(event.target.valueAsNumber)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>

                <FormGroup>
                  <FormField
                    control={form.control}
                    name="largoCm"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Largo (cm)</FormLabel>
                        <FormControl>
                          <CleanInput
                            type="number"
                            placeholder="cm"
                            {...field}
                            onChange={event => field.onChange(event.target.valueAsNumber)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="anchoCm"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Ancho (cm)</FormLabel>
                        <FormControl>
                          <CleanInput
                            type="number"
                            placeholder="cm"
                            {...field}
                            onChange={event => field.onChange(event.target.valueAsNumber)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="altoCm"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Alto (cm)</FormLabel>
                        <FormControl>
                          <CleanInput
                            type="number"
                            placeholder="cm"
                            {...field}
                            onChange={event => field.onChange(event.target.valueAsNumber)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="pesoKg"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Peso Estimado (kg)</FormLabel>
                        <FormControl>
                          <CleanInput
                            type="number"
                            placeholder="kg"
                            {...field}
                            onChange={event => field.onChange(event.target.valueAsNumber)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>

                <FormGroup>
                  <FormField
                    control={form.control}
                    name="tipoServicio"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tipo de Servicio</FormLabel>
                        <CleanSelect onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <CleanSelectTrigger>
                              <SelectValue placeholder="Selecciona tipo de servicio" />
                            </CleanSelectTrigger>
                          </FormControl>
                          <CleanSelectContent>
                            <CleanSelectItem value="estandar">Estándar (3-5 días hábiles)</CleanSelectItem>
                            <CleanSelectItem value="express">Express (Día Siguiente/2 días)</CleanSelectItem>
                            <CleanSelectItem value="terrestre_eco">Terrestre Económico (5-7 días)</CleanSelectItem>
                            <CleanSelectItem value="recoleccion_local">Recolección Local (Programada)</CleanSelectItem>
                          </CleanSelectContent>
                        </CleanSelect>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>
              </FormSection>

              <FormSection title="Comentarios Adicionales">
                <FormGroup>
                  <FormField
                    control={form.control}
                    name="comentarios"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Comentarios Adicionales (Opcional)</FormLabel>
                        <FormControl>
                          <CleanTextarea
                            placeholder="Instrucciones especiales, fragilidad, etc."
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormGroup>
              </FormSection>

              <div className="flex justify-end pt-6">
                <Button
                  type="submit"
                  className="w-full md:w-auto bg-red-600 hover:bg-red-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 dark:bg-red-600 dark:hover:bg-red-700"
                >
                  Enviar Solicitud de Paquetería
                </Button>
              </div>
            </form>
          </Form>
        </FormSection>
      </FormCard>
    </div>
  )
}

export default function SolicitudPaqueteriaPage() {
  return (
    <PageGuard
      requiredPermissions={["admin:paqueteria:read", "admin:paqueteria:list"]}
      unauthorizedTitle="Acceso Denegado - Solicitud de Paquetería"
      unauthorizedMessage="No tienes los permisos necesarios para acceder a la sección de Solicitud de Paquetería."
    >
      <SolicitudPaqueteriaContent />
    </PageGuard>
  )
}
