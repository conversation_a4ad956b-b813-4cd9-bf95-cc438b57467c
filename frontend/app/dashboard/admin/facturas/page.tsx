"use client"

import * as React from "react"
import { PageGuard } from "@/components/auth/page-guard"
import { FormCard } from "@/components/ui/form-styles"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, ChevronDown, Eye, Edit, Trash2, FileText, PlusCircle, UploadCloud, Link2, CheckCircle2, XCircle, Clock, MoreHorizontal } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { CalendarIcon } from "lucide-react"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import { cn } from "@/lib/utils"
import { toast } from "@/components/ui/use-toast"
import { Combobox } from "@/components/ui/combobox" // Reutilizar Combobox
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { PermissionGuard } from '@/components/auth/permission-guard'


export type Factura = {
  id: string
  numeroFactura: string
  fechaFactura: Date
  clienteId: string
  clienteNombre?: string // Para mostrar en la tabla
  proyectoId?: string
  proyectoNombre?: string // Para mostrar en la tabla
  montoTotal: number
  estadoPago: "Pendiente de Pago" | "Pagada" | "Parcialmente Pagada" | "Cancelada"
  archivoXML_URL?: string // Simulación
  archivoPDF_URL?: string // Simulación
}

// Esquema de validación Zod para el formulario de Factura
const facturaFormSchema = z.object({
  numeroFactura: z.string().min(1, "El número de factura es requerido."),
  fechaFactura: z.date({ required_error: "La fecha de la factura es requerida." }),
  clienteId: z.string({ required_error: "Debes seleccionar un cliente." }),
  proyectoId: z.string().optional(),
  montoTotal: z.preprocess(
    (a) => parseFloat(z.string().parse(a)),
    z.number().positive("El monto total debe ser un número positivo.")
  ),
  archivoXML: z.any().refine(file => file?.name, "El archivo XML es requerido.").optional(), // opcional si se edita sin cambiar archivo
  archivoPDF: z.any().refine(file => file?.name, "El archivo PDF es requerido.").optional(), // opcional si se edita sin cambiar archivo
  // estadoPago no se edita directamente aquí, se maneja con acciones separadas
});

type FacturaFormValues = z.infer<typeof facturaFormSchema>

// Datos simulados
const clientesSimulados = [
  { value: "CLI001", label: "Acme Corp" },
  { value: "CLI002", label: "Tech Solutions" },
  { value: "CLI003", label: "Finanzas Seguras SA" },
  { value: "CLI004", label: "Maquinaria Pesada Inc." },
];

const proyectosSimulados = [
  { value: "PROJ001", label: "Desarrollo Web Corporativo", clienteId: "CLI001" },
  { value: "PROJ002", label: "Consultoría SEO", clienteId: "CLI002" },
  { value: "PROJ003", label: "Mantenimiento Maquinaria Excavadora", clienteId: "CLI004" },
  { value: "PROJ004", label: "Auditoría Contable Anual", clienteId: "CLI003" },
  { value: "PROJ005", label: "Instalación Red Industrial", clienteId: "CLI001" },
];

const initialFacturas: Factura[] = [
  { id: "FACT001", numeroFactura: "A-1234", fechaFactura: new Date("2023-10-01"), clienteId: "CLI001", clienteNombre: "Acme Corp", proyectoId: "PROJ001", proyectoNombre: "Desarrollo Web", montoTotal: 15000, estadoPago: "Pagada", archivoXML_URL: "/docs/fact001.xml", archivoPDF_URL: "/docs/fact001.pdf" },
  { id: "FACT002", numeroFactura: "B-5678", fechaFactura: new Date("2023-10-15"), clienteId: "CLI002", clienteNombre: "Tech Solutions", proyectoId: "PROJ002", proyectoNombre: "Consultoría SEO", montoTotal: 8500, estadoPago: "Pendiente de Pago", archivoXML_URL: "/docs/fact002.xml", archivoPDF_URL: "/docs/fact002.pdf" },
  { id: "FACT003", numeroFactura: "C-9101", fechaFactura: new Date("2023-11-05"), clienteId: "CLI004", clienteNombre: "Maquinaria Pesada Inc.", montoTotal: 22000, estadoPago: "Parcialmente Pagada", archivoXML_URL: "/docs/fact003.xml", archivoPDF_URL: "/docs/fact003.pdf" },
  { id: "FACT004", numeroFactura: "D-1121", fechaFactura: new Date("2023-09-20"), clienteId: "CLI003", clienteNombre: "Finanzas Seguras SA", proyectoId: "PROJ004", proyectoNombre: "Auditoría Anual", montoTotal: 12000, estadoPago: "Cancelada", archivoXML_URL: "/docs/fact004.xml", archivoPDF_URL: "/docs/fact004.pdf" },
];

const getEstadoPagoBadgeClass = (estado: Factura["estadoPago"]): string => {
 switch (estado) {
    case "Pagada": return "bg-moka-lion/30 text-moka-bistre border border-moka-brown/40 dark:bg-green-700/30 dark:text-green-300";
    case "Pendiente de Pago": return "bg-moka-peach/80 text-moka-bistre border border-moka-brown/40 dark:bg-yellow-600/30 dark:text-yellow-300";
    case "Parcialmente Pagada": return "bg-moka-peach/60 text-moka-bistre border border-moka-brown/40 dark:bg-blue-700/30 dark:text-blue-300";
    case "Cancelada": return "bg-moka-falu/20 text-moka-falu border border-moka-falu/40 dark:bg-red-700/30 dark:text-red-300";
    default: return "bg-moka-peach/40 text-moka-bistre border border-moka-brown/40";
  }
}

function FacturasContent() {
  const [facturas, setFacturas] = React.useState<Factura[]>(initialFacturas);
  const [sorting, setSorting] = React.useState<SortingState>([{ id: "fechaFactura", desc: true }]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [isFormOpen, setIsFormOpen] = React.useState(false);
  const [editingFactura, setEditingFactura] = React.useState<Factura | null>(null);
  const [proyectosFiltrados, setProyectosFiltrados] = React.useState(proyectosSimulados);

  const form = useForm<FacturaFormValues>({
    resolver: zodResolver(facturaFormSchema),
  });

  const clienteSeleccionadoId = form.watch("clienteId");

  React.useEffect(() => {
    if (clienteSeleccionadoId) {
      setProyectosFiltrados(proyectosSimulados.filter(p => p.clienteId === clienteSeleccionadoId));
    } else {
      setProyectosFiltrados([]); // O todos los proyectos si se permite factura sin cliente específico primero
    }
    // Si el proyectoId actual no pertenece al nuevo clienteId, resetearlo
    const currentProyectoId = form.getValues("proyectoId");
    if (currentProyectoId && !proyectosSimulados.find(p => p.value === currentProyectoId && p.clienteId === clienteSeleccionadoId)) {
        form.setValue("proyectoId", undefined, { shouldValidate: true });
    }
  }, [clienteSeleccionadoId, form]);

  React.useEffect(() => {
    if (editingFactura) {
      form.reset({
        numeroFactura: editingFactura.numeroFactura,
        fechaFactura: editingFactura.fechaFactura,
        clienteId: editingFactura.clienteId,
        proyectoId: editingFactura.proyectoId,
        montoTotal: editingFactura.montoTotal,
        archivoXML: undefined, // No se puede pre-llenar
        archivoPDF: undefined, // No se puede pre-llenar
      });
      // Asegurar que los proyectos se filtren correctamente al editar
      if (editingFactura.clienteId) {
        setProyectosFiltrados(proyectosSimulados.filter(p => p.clienteId === editingFactura.clienteId));
      }
    } else {
      form.reset({
        numeroFactura: "",
        // fechaFactura: new Date(), // Opcional: default a hoy
        montoTotal: 0,
        clienteId: undefined,
        proyectoId: undefined,
        archivoXML: undefined,
        archivoPDF: undefined,
      });
      setProyectosFiltrados(proyectosSimulados); // Mostrar todos o ninguno al inicio
    }
  }, [editingFactura, form, isFormOpen]);

  const onSubmitForm = (values: FacturaFormValues) => {
    const cliente = clientesSimulados.find(c => c.value === values.clienteId);
    const proyecto = proyectosSimulados.find(p => p.value === values.proyectoId);

    const newOrUpdatedFactura: Factura = {
      id: editingFactura ? editingFactura.id : `FACT${String(Date.now()).slice(-3)}${String(Math.floor(Math.random()*100)).padStart(2,'0')}`,
      numeroFactura: values.numeroFactura,
      fechaFactura: values.fechaFactura,
      clienteId: values.clienteId,
      clienteNombre: cliente?.label,
      proyectoId: values.proyectoId,
      proyectoNombre: proyecto?.label,
      montoTotal: values.montoTotal,
      estadoPago: editingFactura ? editingFactura.estadoPago : "Pendiente de Pago", // Default para nuevas
      archivoXML_URL: editingFactura?.archivoXML_URL || (values.archivoXML ? `/docs/${values.archivoXML.name}` : undefined),
      archivoPDF_URL: editingFactura?.archivoPDF_URL || (values.archivoPDF ? `/docs/${values.archivoPDF.name}` : undefined),
    };

    if (editingFactura) {
      setFacturas(prev => prev.map(f => f.id === editingFactura.id ? newOrUpdatedFactura : f));
      toast({ title: "Factura Actualizada", description: `La factura ${newOrUpdatedFactura.numeroFactura} ha sido actualizada.` });
    } else {
      setFacturas(prev => [newOrUpdatedFactura, ...prev]);
      toast({ title: "Factura Añadida", description: `La factura ${newOrUpdatedFactura.numeroFactura} ha sido añadida.` });
      // Simular notificación a Ventas
      if (newOrUpdatedFactura.proyectoId) {
        toast({ title: "Notificación (Simulada)", description: `Ventas ha sido notificado que el proyecto ${newOrUpdatedFactura.proyectoNombre} ha sido facturado.`});
      }
    }
    setIsFormOpen(false);
    setEditingFactura(null);
  };

  const handleChangeEstadoPago = (facturaId: string, nuevoEstado: Factura["estadoPago"]) => {
    setFacturas(prev => prev.map(f => f.id === facturaId ? {...f, estadoPago: nuevoEstado} : f));
    toast({ title: "Estado de Pago Actualizado", description: `Factura ${facturaId} ahora está ${nuevoEstado}.`});
  };

  const columns: ColumnDef<Factura>[] = [
    { accessorKey: "numeroFactura", header: "Nº Factura" },
    { accessorKey: "fechaFactura", header: "Fecha", cell: ({row}) => format(row.original.fechaFactura, "dd/MM/yyyy")},
    { accessorKey: "clienteNombre", header: "Cliente" },
    { accessorKey: "proyectoNombre", header: "Proyecto", cell: ({row}) => row.original.proyectoNombre || <span className="text-xs text-muted-foreground">N/A</span> },
    { accessorKey: "montoTotal", header: "Monto Total", cell: ({row}) => new Intl.NumberFormat("es-MX", {style: "currency", currency: "MXN"}).format(row.original.montoTotal), meta: { className: "text-right" } },
    { accessorKey: "estadoPago", header: "Estado Pago", cell: ({row}) => <Badge className={cn("capitalize", getEstadoPagoBadgeClass(row.original.estadoPago))}>{row.original.estadoPago}</Badge> },
    {
      id: "archivos", header: "Archivos",
      cell: ({ row }) => (
        <div className="flex space-x-2">
          {row.original.archivoXML_URL && <Button variant="link" size="sm" asChild><a href={row.original.archivoXML_URL} target="_blank"><FileText className="mr-1 h-4 w-4"/>XML</a></Button>}
          {row.original.archivoPDF_URL && <Button variant="link" size="sm" asChild><a href={row.original.archivoPDF_URL} target="_blank"><FileText className="mr-1 h-4 w-4"/>PDF</a></Button>}
        </div>
      )
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const factura = row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild><Button variant="ghost" className="h-8 w-8 p-0"><MoreHorizontal className="h-4 w-4" /></Button></DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Acciones</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => { setEditingFactura(factura); setIsFormOpen(true); }}>
                <Edit className="mr-2 h-4 w-4" /> Editar Factura
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {factura.estadoPago !== "Pagada" && <DropdownMenuItem onClick={() => handleChangeEstadoPago(factura.id, "Pagada")}><CheckCircle2 className="mr-2 h-4 w-4 text-moka-lion"/>Marcar como Pagada</DropdownMenuItem>}
              {factura.estadoPago !== "Pendiente de Pago" && <DropdownMenuItem onClick={() => handleChangeEstadoPago(factura.id, "Pendiente de Pago")}><Clock className="mr-2 h-4 w-4 text-moka-brown"/>Marcar Pendiente</DropdownMenuItem>}
              {factura.estadoPago !== "Cancelada" && <DropdownMenuItem onClick={() => handleChangeEstadoPago(factura.id, "Cancelada")}><XCircle className="mr-2 h-4 w-4 text-moka-falu"/>Marcar Cancelada</DropdownMenuItem>}
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ];

  const table = useReactTable({
    data: facturas, columns, getCoreRowModel: getCoreRowModel(), getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting, getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters, getFilteredRowModel: getFilteredRowModel(),
    state: { sorting, columnFilters },
    initialState: { pagination: { pageSize: 5 }}
  });

  return (
    <div className="max-w-7xl mx-auto p-6">
      <FormCard>
        <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Gestión de Facturas</h1>
            <p className="text-muted-foreground">Sube, visualiza y administra las facturas de la empresa.</p>
          </div>
          <Dialog open={isFormOpen} onOpenChange={(open) => { setIsFormOpen(open); if (!open) setEditingFactura(null); }}>
            <DialogTrigger asChild>
              <Button onClick={() => { setEditingFactura(null); setIsFormOpen(true); }}>
                <PlusCircle className="mr-2 h-4 w-4" /> Subir Nueva Factura
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-2xl">
              <DialogHeader>
                <DialogTitle>{editingFactura ? "Editar" : "Subir Nueva"} Factura</DialogTitle>
              </DialogHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmitForm)} className="space-y-4 py-4 max-h-[70vh] overflow-y-auto pr-2">
                  <FormField control={form.control} name="numeroFactura" render={({ field }) => (<FormItem><FormLabel>Número de Factura</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>)} />
                  <FormField control={form.control} name="fechaFactura" render={({ field }) => (
                    <FormItem className="flex flex-col"><FormLabel>Fecha de Factura</FormLabel>
                      <Popover><PopoverTrigger asChild><FormControl>
                        <Button variant={"outline"} className={cn("w-full pl-3 text-left font-normal", !field.value && "text-muted-foreground")}>
                          {field.value ? format(field.value, "PPP", { locale: es }) : <span>Selecciona fecha</span>} <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button></FormControl></PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start"><Calendar mode="single" selected={field.value} onSelect={field.onChange} initialFocus locale={es} /></PopoverContent>
                      </Popover><FormMessage />
                    </FormItem>)} />
                  <FormField control={form.control} name="clienteId" render={({ field }) => (
                    <FormItem><FormLabel>Cliente</FormLabel>
                      <Combobox options={clientesSimulados} value={field.value} onChange={field.onChange} placeholder="Selecciona un cliente..." searchPlaceholder="Buscar cliente..." />
                    <FormMessage /></FormItem>)} />
                  <FormField control={form.control} name="proyectoId" render={({ field }) => (
                    <FormItem><FormLabel>Proyecto Asociado (Opcional)</FormLabel>
                      <Combobox options={proyectosFiltrados} value={field.value} onChange={field.onChange} placeholder="Selecciona un proyecto..." searchPlaceholder="Buscar proyecto..." notFoundText="No hay proyectos para este cliente o ninguno seleccionado." disabled={!clienteSeleccionadoId || proyectosFiltrados.length === 0} />
                    <FormDescription>{!clienteSeleccionadoId ? "Selecciona un cliente para ver sus proyectos." : proyectosFiltrados.length === 0 ? "Este cliente no tiene proyectos." : ""}</FormDescription>
                    <FormMessage /></FormItem>)} />
                  <FormField control={form.control} name="montoTotal" render={({ field }) => (<FormItem><FormLabel>Monto Total (MXN)</FormLabel><FormControl><Input type="number" step="0.01" {...field} onChange={e => field.onChange(parseFloat(e.target.value))} /></FormControl><FormMessage /></FormItem>)} />
                  <FormField control={form.control} name="archivoXML" render={({ field }) => (
                    <FormItem><FormLabel>Archivo XML</FormLabel><FormControl><Input type="file" accept=".xml" onChange={(e) => field.onChange(e.target.files ? e.target.files[0] : null)} /></FormControl>
                    {editingFactura?.archivoXML_URL && !field.value && <FormDescription>Actual: {editingFactura.archivoXML_URL.split('/').pop()}</FormDescription>}
                    <FormMessage /></FormItem>)} />
                  <FormField control={form.control} name="archivoPDF" render={({ field }) => (
                    <FormItem><FormLabel>Archivo PDF</FormLabel><FormControl><Input type="file" accept=".pdf" onChange={(e) => field.onChange(e.target.files ? e.target.files[0] : null)} /></FormControl>
                    {editingFactura?.archivoPDF_URL && !field.value && <FormDescription>Actual: {editingFactura.archivoPDF_URL.split('/').pop()}</FormDescription>}
                    <FormMessage /></FormItem>)} />
                  <DialogFooter className="pt-4">
                    <DialogClose asChild><Button type="button" variant="outline">Cancelar</Button></DialogClose>
                    <Button type="submit">{editingFactura ? "Guardar Cambios" : "Subir Factura"}</Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </header>
          <div className="flex items-center py-4 gap-2">
            <Input placeholder="Filtrar por Nº Factura o Cliente..." value={(table.getColumn("numeroFactura")?.getFilterValue() as string) ?? ""} onChange={(e) => table.getColumn("numeroFactura")?.setFilterValue(e.target.value)} className="max-w-sm" />
            {/* Aquí podrían ir más filtros como por estado de pago */}
          </div>
          <div className="rounded-md border">
            <Table>
              <TableHeader>{table.getHeaderGroups().map(hg => (<TableRow key={hg.id}>{hg.headers.map(h => <TableHead key={h.id} className={h.column.columnDef.meta?.className}>{h.isPlaceholder ? null : flexRender(h.column.columnDef.header, h.getContext())}</TableHead>)}</TableRow>))}</TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? table.getRowModel().rows.map(row => (
                  <TableRow key={row.id}>{row.getVisibleCells().map(cell => <TableCell key={cell.id} className={cell.column.columnDef.meta?.className}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>)}</TableRow>
                )) : <TableRow><TableCell colSpan={columns.length} className="h-24 text-center">No hay facturas.</TableCell></TableRow>}
              </TableBody>
            </Table>
          </div>
          <div className="flex items-center justify-end space-x-2 py-4">
            <Button variant="outline" size="sm" onClick={() => table.previousPage()} disabled={!table.getCanPreviousPage()}>Anterior</Button>
            <Button variant="outline" size="sm" onClick={() => table.nextPage()} disabled={!table.getCanNextPage()}>Siguiente</Button>
          </div>
      </FormCard>
    </div>
  )
}

export default function FacturasPage() {
  return (
    <PageGuard
      requiredPermissions={["admin:facturas:read", "admin:facturas:list"]}
      unauthorizedTitle="Acceso Denegado - Facturas"
      unauthorizedMessage="No tienes los permisos necesarios para acceder a la sección de Facturas."
    >
      <FacturasContent />
    </PageGuard>
  )
}
