"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { DashboardLayout } from "../app/dashboard/dashboard-layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ChevronLeft,
  ChevronRight,
  Clock,
  MapPin,
  Phone,
  Mail,
  Building,
  FileText,
  Truck,
  Wrench,
  FlaskConical,
  GraduationCap,
  UserCheck,
  ClipboardCheck,
  Quote,
  Package,
  Check,
  Download,
  ExternalLink,
  File,
  FileSpreadsheet,
  Calendar,
  ArrowRight,
  Use<PERSON>,
} from "lucide-react"
import { cn } from "@/lib/utils"

// Datos específicos de COMINTEC
interface Project {
  id: number
  customer: string
  project: string
  value: number
  status: "pending" | "in_progress" | "completed" | "cancelled"
  department: string
  date: string
  progress: number
  responsible: string
  contact: {
    name: string
    email: string
    phone: string
  }
  address: string
  description: string
  timeline: {
    id: number
    date: string
    title: string
    description: string
    status: "completed" | "current" | "pending"
  }[]
  documents: {
    id: number
    name: string
    type: string
    date: string
    size: string
    fileType: "pdf" | "docx" | "xlsx"
    url: string
  }[]
}

interface CalendarEvent {
  id: number
  title: string
  date: string
  time: string
  department: string
  type: "delivery" | "installation" | "calibration" | "training" | "meeting" | "inspection" | "quotation" | "inventory"
  relatedProject?: number
  responsible: string
  status: "scheduled" | "confirmed" | "pending" | "completed" | "cancelled" | "draft"
  description?: string
}

// Datos expandidos de proyectos de COMINTEC
const projectsData: Project[] = [
  {
    id: 1,
    customer: "Metro de Lima",
    project: "Sistema de Medición Línea 5",
    value: 850000,
    status: "in_progress",
    department: "logistics",
    date: "2024-03-15",
    progress: 75,
    responsible: "Carlos Ruiz",
    contact: {
      name: "Juan Pérez",
      email: "<EMAIL>",
      phone: "+51 1 234-5678",
    },
    address: "Av. Paseo de la República 123, Lima",
    description:
      "Implementación completa del sistema de medición automatizado para la nueva línea 5 del metro, incluyendo sensores de última generación, software de monitoreo en tiempo real y capacitación especializada del personal técnico.",
    timeline: [
      {
        id: 1,
        date: "2024-03-15",
        title: "Propuesta enviada",
        description: "Envío de propuesta técnica y comercial detallada",
        status: "completed",
      },
      {
        id: 2,
        date: "2024-03-20",
        title: "Aprobación cliente",
        description: "Cliente aprueba propuesta y firma contrato",
        status: "completed",
      },
      {
        id: 3,
        date: "2024-04-01",
        title: "Inicio proyecto",
        description: "Reunión de inicio y planificación detallada",
        status: "completed",
      },
      {
        id: 4,
        date: "2024-05-15",
        title: "Entrega equipos",
        description: "Entrega e instalación de equipos principales",
        status: "current",
      },
      {
        id: 5,
        date: "2024-06-01",
        title: "Capacitación",
        description: "Capacitación del personal técnico",
        status: "pending",
      },
      {
        id: 6,
        date: "2024-06-15",
        title: "Cierre proyecto",
        description: "Entrega final y cierre del proyecto",
        status: "pending",
      },
    ],
    documents: [
      {
        id: 1,
        name: "Contrato_Metro_Lima.pdf",
        type: "Contrato",
        date: "2024-03-20",
        size: "2.5 MB",
        fileType: "pdf",
        url: "/documents/contrato_metro_lima.pdf",
      },
      {
        id: 2,
        name: "Especificaciones_Tecnicas.pdf",
        type: "Especificaciones",
        date: "2024-03-15",
        size: "1.8 MB",
        fileType: "pdf",
        url: "/documents/especificaciones_tecnicas.pdf",
      },
      {
        id: 3,
        name: "Cronograma_Proyecto.xlsx",
        type: "Cronograma",
        date: "2024-04-01",
        size: "456 KB",
        fileType: "xlsx",
        url: "/documents/cronograma_proyecto.xlsx",
      },
    ],
  },
  {
    id: 2,
    customer: "Hospital Nacional",
    project: "Calibración Equipos Médicos",
    value: 125000,
    status: "pending",
    department: "metrology",
    date: "2024-04-08",
    progress: 30,
    responsible: "Diana Torres",
    contact: {
      name: "María González",
      email: "<EMAIL>",
      phone: "+51 1 987-6543",
    },
    address: "Av. Grau 456, Lima",
    description:
      "Servicio especializado de calibración para equipos médicos críticos incluyendo monitores de signos vitales, ventiladores mecánicos y equipos de imagen diagnóstica.",
    timeline: [
      {
        id: 1,
        date: "2024-04-08",
        title: "Solicitud recibida",
        description: "Recepción de solicitud de calibración",
        status: "completed",
      },
      {
        id: 2,
        date: "2024-04-10",
        title: "Evaluación técnica",
        description: "Evaluación inicial de equipos",
        status: "current",
      },
      {
        id: 3,
        date: "2024-04-20",
        title: "Cronograma definido",
        description: "Definición de cronograma de calibración",
        status: "pending",
      },
    ],
    documents: [
      {
        id: 1,
        name: "Solicitud_Calibracion.pdf",
        type: "Solicitud",
        date: "2024-04-08",
        size: "1.2 MB",
        fileType: "pdf",
        url: "/documents/solicitud_calibracion.pdf",
      },
    ],
  },
  {
    id: 3,
    customer: "AutoTech Peru",
    project: "Sistema de Control de Calidad",
    value: 320000,
    status: "completed",
    department: "quality",
    date: "2024-01-20",
    progress: 100,
    responsible: "Roberto Silva",
    contact: {
      name: "Carlos Mendoza",
      email: "<EMAIL>",
      phone: "+51 1 555-0123",
    },
    address: "Av. Industrial 789, Callao",
    description:
      "Implementación de sistema automatizado de control de calidad para línea de producción automotriz con tecnología de vanguardia.",
    timeline: [
      {
        id: 1,
        date: "2024-01-20",
        title: "Propuesta aprobada",
        description: "Aprobación de propuesta técnica",
        status: "completed",
      },
      {
        id: 2,
        date: "2024-02-01",
        title: "Instalación iniciada",
        description: "Inicio de instalación de equipos",
        status: "completed",
      },
      {
        id: 3,
        date: "2024-02-15",
        title: "Configuración software",
        description: "Configuración del software de control",
        status: "completed",
      },
      {
        id: 4,
        date: "2024-03-01",
        title: "Pruebas finales",
        description: "Pruebas y validación del sistema",
        status: "completed",
      },
      {
        id: 5,
        date: "2024-03-10",
        title: "Entrega final",
        description: "Entrega y cierre del proyecto",
        status: "completed",
      },
    ],
    documents: [
      {
        id: 1,
        name: "Proyecto_AutoTech.pdf",
        type: "Proyecto",
        date: "2024-01-20",
        size: "4.1 MB",
        fileType: "pdf",
        url: "/documents/proyecto_autotech.pdf",
      },
      {
        id: 2,
        name: "Manual_Sistema.pdf",
        type: "Manual",
        date: "2024-03-01",
        size: "2.8 MB",
        fileType: "pdf",
        url: "/documents/manual_sistema.pdf",
      },
    ],
  },
  {
    id: 4,
    customer: "Minera del Norte",
    project: "Mantenimiento Predictivo",
    value: 580000,
    status: "in_progress",
    department: "service",
    date: "2024-02-10",
    progress: 45,
    responsible: "Elena Vega",
    contact: {
      name: "Alejandro Ríos",
      email: "<EMAIL>",
      phone: "+51 1 444-5678",
    },
    address: "Carretera Norte Km 45, Trujillo",
    description:
      "Implementación de sistema de mantenimiento predictivo para equipos mineros críticos utilizando IoT y análisis de datos avanzado.",
    timeline: [
      {
        id: 1,
        date: "2024-02-10",
        title: "Contrato firmado",
        description: "Firma de contrato de servicios",
        status: "completed",
      },
      {
        id: 2,
        date: "2024-02-20",
        title: "Análisis inicial",
        description: "Análisis de equipos y condiciones",
        status: "completed",
      },
      {
        id: 3,
        date: "2024-03-15",
        title: "Instalación sensores",
        description: "Instalación de sensores de monitoreo",
        status: "current",
      },
      {
        id: 4,
        date: "2024-04-30",
        title: "Configuración sistema",
        description: "Configuración del sistema de monitoreo",
        status: "pending",
      },
    ],
    documents: [
      {
        id: 1,
        name: "Contrato_Minera.pdf",
        type: "Contrato",
        date: "2024-02-10",
        size: "3.7 MB",
        fileType: "pdf",
        url: "/documents/contrato_minera.pdf",
      },
    ],
  },
  {
    id: 5,
    customer: "Petroperú",
    project: "Modernización Refinería",
    value: 1200000,
    status: "in_progress",
    department: "systems",
    date: "2024-01-05",
    progress: 60,
    responsible: "Miguel Herrera",
    contact: {
      name: "Ana Castillo",
      email: "<EMAIL>",
      phone: "+51 1 333-4567",
    },
    address: "Refinería La Pampilla, Callao",
    description:
      "Modernización integral de sistemas de control y monitoreo de refinería con tecnología de última generación para optimizar procesos y mejorar seguridad.",
    timeline: [
      {
        id: 1,
        date: "2024-01-05",
        title: "Inicio del proyecto",
        description: "Reunión de inicio y planificación",
        status: "completed",
      },
      {
        id: 2,
        date: "2024-02-01",
        title: "Análisis de sistemas",
        description: "Evaluación de sistemas existentes",
        status: "completed",
      },
      {
        id: 3,
        date: "2024-03-01",
        title: "Instalación equipos",
        description: "Instalación de nuevos equipos de control",
        status: "current",
      },
      {
        id: 4,
        date: "2024-05-01",
        title: "Pruebas de integración",
        description: "Pruebas de integración con sistemas existentes",
        status: "pending",
      },
    ],
    documents: [
      {
        id: 1,
        name: "Contrato_Petroperu.pdf",
        type: "Contrato",
        date: "2024-01-05",
        size: "5.2 MB",
        fileType: "pdf",
        url: "/documents/contrato_petroperu.pdf",
      },
    ],
  },
  {
    id: 6,
    customer: "Universidad Nacional",
    project: "Laboratorio de Investigación",
    value: 280000,
    status: "pending",
    department: "quality",
    date: "2024-04-15",
    progress: 15,
    responsible: "Patricia Morales",
    contact: {
      name: "Dr. Luis Vargas",
      email: "<EMAIL>",
      phone: "+51 1 222-3456",
    },
    address: "Av. Túpac Amaru 210, Lima",
    description:
      "Implementación de laboratorio de investigación con equipos de alta precisión para análisis químico y físico de materiales.",
    timeline: [
      {
        id: 1,
        date: "2024-04-15",
        title: "Propuesta presentada",
        description: "Presentación de propuesta técnica",
        status: "completed",
      },
      {
        id: 2,
        date: "2024-04-25",
        title: "Evaluación técnica",
        description: "Evaluación de requerimientos técnicos",
        status: "current",
      },
      {
        id: 3,
        date: "2024-05-10",
        title: "Aprobación presupuesto",
        description: "Aprobación de presupuesto por universidad",
        status: "pending",
      },
    ],
    documents: [
      {
        id: 1,
        name: "Propuesta_Universidad.pdf",
        type: "Propuesta",
        date: "2024-04-15",
        size: "3.1 MB",
        fileType: "pdf",
        url: "/documents/propuesta_universidad.pdf",
      },
    ],
  },
]

const calendarEvents: CalendarEvent[] = [
  {
    id: 1,
    title: "Entrega Proyecto Metro Línea 5",
    date: "2024-07-15",
    time: "09:00",
    department: "logistics",
    type: "delivery",
    relatedProject: 1,
    responsible: "Carlos Ruiz",
    status: "scheduled",
    description: "Entrega e instalación de equipos principales del sistema de medición",
  },
  {
    id: 2,
    title: "Calibración Hospital Nacional",
    date: "2024-07-18",
    time: "14:00",
    department: "metrology",
    type: "calibration",
    relatedProject: 2,
    responsible: "Diana Torres",
    status: "confirmed",
    description: "Calibración de equipos médicos críticos",
  },
  {
    id: 3,
    title: "Reunión Petroperú",
    date: "2024-07-20",
    time: "10:30",
    department: "systems",
    type: "meeting",
    relatedProject: 5,
    responsible: "Miguel Herrera",
    status: "scheduled",
    description: "Revisión de avances del proyecto de modernización",
  },
]

// Configuración de departamentos COMINTEC
const departmentColors = {
  sales: "bg-blue-500",
  admin: "bg-green-500",
  warehouse: "bg-orange-500",
  logistics: "bg-purple-500",
  quality: "bg-red-500",
  metrology: "bg-cyan-500",
  service: "bg-yellow-500",
  hr: "bg-pink-500",
  systems: "bg-indigo-500",
}

const departmentNames = {
  sales: "Ventas",
  admin: "Administración",
  warehouse: "Almacén",
  logistics: "Logística",
  quality: "Calidad",
  metrology: "Metrología",
  service: "Servicio",
  hr: "Recursos Humanos",
  systems: "Sistemas",
}

const statusColors = {
  pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/40 dark:text-yellow-200",
  in_progress: "bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-200",
  completed: "bg-green-100 text-green-800 dark:bg-green-900/40 dark:text-green-200",
  cancelled: "bg-red-100 text-red-800 dark:bg-red-900/40 dark:text-red-200",
}

const statusNames = {
  pending: "Pendiente",
  in_progress: "En Progreso",
  completed: "Completado",
  cancelled: "Cancelado",
}

const eventTypeIcons = {
  delivery: Truck,
  installation: Wrench,
  calibration: FlaskConical,
  training: GraduationCap,
  meeting: UserCheck,
  inspection: ClipboardCheck,
  quotation: Quote,
  inventory: Package,
}

const eventTypeNames = {
  delivery: "Entrega",
  installation: "Instalación",
  calibration: "Calibración",
  training: "Capacitación",
  meeting: "Reunión",
  inspection: "Inspección",
  quotation: "Cotización",
  inventory: "Inventario",
}

const eventStatusColors = {
  scheduled: "bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-200",
  confirmed: "bg-green-100 text-green-800 dark:bg-green-900/40 dark:text-green-200",
  pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/40 dark:text-yellow-200",
  completed: "bg-gray-100 text-gray-800 dark:bg-gray-800/40 dark:text-gray-200",
  cancelled: "bg-red-100 text-red-800 dark:bg-red-900/40 dark:text-red-200",
  draft: "bg-orange-100 text-orange-800 dark:bg-orange-900/40 dark:text-orange-200",
}

const eventStatusNames = {
  scheduled: "Programado",
  confirmed: "Confirmado",
  pending: "Pendiente",
  completed: "Completado",
  cancelled: "Cancelado",
  draft: "Borrador",
}

const fileTypeColors = {
  pdf: "text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-900/30",
  docx: "text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/30",
  xlsx: "text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/30",
}

const fileTypeIcons = {
  pdf: FileText,
  docx: File,
  xlsx: FileSpreadsheet,
}

const MONTHS = [
  "Enero",
  "Febrero",
  "Marzo",
  "Abril",
  "Mayo",
  "Junio",
  "Julio",
  "Agosto",
  "Septiembre",
  "Octubre",
  "Noviembre",
  "Diciembre",
]

const WEEKDAYS = ["Dom", "Lun", "Mar", "Mié", "Jue", "Vie", "Sáb"]

function ComintecDashboardComponent() {
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [departmentFilter, setDepartmentFilter] = useState("all")
  const [currentDate, setCurrentDate] = useState(new Date())
  const [calendarDepartmentFilter, setCalendarDepartmentFilter] = useState("all")
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)

  const filteredProjects = projectsData.filter((project) => {
    const matchesSearch =
      project.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.project.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.responsible.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || project.status === statusFilter
    const matchesDepartment = departmentFilter === "all" || project.department === departmentFilter

    return matchesSearch && matchesStatus && matchesDepartment
  })

  const handleDocumentAction = (doc: Project["documents"][0], action: "open" | "download") => {
    if (action === "open") {
      window.open(doc.url, "_blank")
    } else {
      const link = document.createElement("a")
      link.href = doc.url
      link.download = doc.name
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const startDate = new Date(firstDay)
    startDate.setDate(startDate.getDate() - firstDay.getDay())

    const days = []
    const current = new Date(startDate)

    for (let i = 0; i < 42; i++) {
      days.push(new Date(current))
      current.setDate(current.getDate() + 1)
    }

    return days
  }

  const getEventsForDate = (date: Date) => {
    const dateString = date.toISOString().split("T")[0]
    return calendarEvents.filter((event) => {
      const eventDate = event.date
      const matchesDate = eventDate === dateString
      const matchesDepartment = calendarDepartmentFilter === "all" || event.department === calendarDepartmentFilter
      return matchesDate && matchesDepartment
    })
  }

  const navigateMonth = (direction: "prev" | "next") => {
    const newDate = new Date(currentDate)
    if (direction === "prev") {
      newDate.setMonth(newDate.getMonth() - 1)
    } else {
      newDate.setMonth(newDate.getMonth() + 1)
    }
    setCurrentDate(newDate)
  }

  const isToday = (date: Date) => {
    const today = new Date()
    return date.toDateString() === today.toDateString()
  }

  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentDate.getMonth()
  }

  const selectedDateEvents = selectedDate ? getEventsForDate(selectedDate) : []

  const ProjectDetailModal = ({ project }: { project: Project }) => (
    <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto backdrop-blur-2xl border shadow-2xl bg-white/95 dark:bg-gray-800/95 border-gray-300/50 dark:border-gray-600/50 text-gray-900 dark:text-gray-100">
      <DialogHeader>
        <DialogTitle className="flex items-center gap-3 font-light text-2xl text-gray-800 dark:text-gray-200">
          <div
            className={cn(
              "w-4 h-4 rounded-full",
              departmentColors[project.department as keyof typeof departmentColors],
            )}
          />
          {project.customer}
          <span className="text-lg font-extralight text-gray-500 dark:text-gray-400">— {project.project}</span>
        </DialogTitle>
      </DialogHeader>

      <Tabs defaultValue="details" className="w-full">
        <TabsList className="grid w-full grid-cols-4 bg-gray-200/50 dark:bg-gray-700/50">
          <TabsTrigger value="details" className="font-light">
            Detalles
          </TabsTrigger>
          <TabsTrigger value="timeline" className="font-light">
            Timeline
          </TabsTrigger>
          <TabsTrigger value="documents" className="font-light">
            Documentos
          </TabsTrigger>
          <TabsTrigger value="contact" className="font-light">
            Contacto
          </TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6 mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="backdrop-blur-md border shadow-lg bg-gray-200/40 dark:bg-gray-700/40 border-gray-300/30 dark:border-gray-600/30">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light flex items-center gap-2 text-gray-800 dark:text-gray-200">
                  <Building className="w-5 h-5" />
                  Información General
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Cliente</label>
                    <p className="text-base font-light mt-1 text-gray-800 dark:text-gray-200">{project.customer}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Proyecto</label>
                    <p className="text-base font-light mt-1 text-gray-800 dark:text-gray-200">{project.project}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Valor del Proyecto</label>
                    <p className="text-2xl font-light text-green-600 dark:text-green-400 mt-1">
                      ${project.value.toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Estado</label>
                    <div className="mt-1">
                      <Badge className={cn("text-sm font-light px-3 py-1", statusColors[project.status])}>
                        {statusNames[project.status]}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="backdrop-blur-md border shadow-lg bg-gray-200/40 dark:bg-gray-700/40 border-gray-300/30 dark:border-gray-600/30">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light flex items-center gap-2 text-gray-800 dark:text-gray-200">
                  <UserCheck className="w-5 h-5" />
                  Gestión del Proyecto
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Departamento</label>
                  <div className="flex items-center gap-2 mt-1">
                    <div
                      className={cn(
                        "w-3 h-3 rounded-full",
                        departmentColors[project.department as keyof typeof departmentColors],
                      )}
                    />
                    <span className="text-base font-light text-gray-800 dark:text-gray-200">
                      {departmentNames[project.department as keyof typeof departmentNames]}
                    </span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Responsable</label>
                  <p className="text-base font-light mt-1 text-gray-800 dark:text-gray-200">{project.responsible}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Fecha de Inicio</label>
                  <p className="text-base font-light mt-1 text-gray-800 dark:text-gray-200">{project.date}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Progreso del Proyecto</label>
                  <div className="mt-2 space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-2xl font-light text-gray-800 dark:text-gray-200">{project.progress}%</span>
                      <span className="text-sm font-light text-gray-500 dark:text-gray-400">Completado</span>
                    </div>
                    <div className="w-full rounded-full h-3 bg-gray-300 dark:bg-gray-600">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500 ease-out"
                        style={{ width: `${project.progress}%` }}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="backdrop-blur-md border shadow-lg bg-gray-200/40 dark:bg-gray-700/40 border-gray-300/30 dark:border-gray-600/30">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light flex items-center gap-2 text-gray-800 dark:text-gray-200">
                  <FileText className="w-5 h-5" />
                  Resumen Rápido
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-3 rounded-lg bg-gray-200/30 dark:bg-gray-600/30">
                    <p className="text-xs font-medium text-gray-500 dark:text-gray-400">DOCUMENTOS</p>
                    <p className="text-xl font-light text-gray-800 dark:text-gray-200">{project.documents.length}</p>
                  </div>
                  <div className="p-3 rounded-lg bg-gray-200/30 dark:bg-gray-600/30">
                    <p className="text-xs font-medium text-gray-500 dark:text-gray-400">HITOS</p>
                    <p className="text-xl font-light text-gray-800 dark:text-gray-200">{project.timeline.length}</p>
                  </div>
                </div>
                <div className="p-4 rounded-lg border-l-4 border-blue-500 bg-blue-50/50 dark:bg-blue-900/20">
                  <p className="text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Próximo Hito</p>
                  <p className="text-sm font-light text-gray-600 dark:text-gray-400">
                    {project.timeline.find((t) => t.status === "current")?.title || "No definido"}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="backdrop-blur-md border shadow-lg bg-gray-200/40 dark:bg-gray-700/40 border-gray-300/30 dark:border-gray-600/30">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-light flex items-center gap-2 text-gray-800 dark:text-gray-200">
                <FileText className="w-5 h-5" />
                Descripción del Proyecto
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-base font-light leading-relaxed text-gray-700 dark:text-gray-300">
                {project.description}
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-6 mt-6">
          <Card className="backdrop-blur-md border shadow-lg bg-gray-200/40 dark:bg-gray-700/40 border-gray-300/30 dark:border-gray-600/30">
            <CardHeader className="pb-6">
              <CardTitle className="text-xl font-light flex items-center gap-2 text-gray-800 dark:text-gray-200">
                <Calendar className="w-6 h-6" />
                Timeline del Proyecto
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="relative">
                <div className="flex items-center justify-between mb-8">
                  {project.timeline.map((event, index) => (
                    <div key={event.id} className="flex flex-col items-center relative group">
                      {index < project.timeline.length - 1 && (
                        <div
                          className={cn(
                            "absolute top-6 left-6 h-0.5 transition-all duration-500",
                            event.status === "completed"
                              ? "bg-green-500"
                              : event.status === "current"
                                ? "bg-blue-500"
                                : "bg-gray-300 dark:bg-gray-600",
                          )}
                          style={{ width: `calc(${100 / (project.timeline.length - 1)}vw - 3rem)` }}
                        />
                      )}

                      <div
                        className={cn(
                          "w-12 h-12 rounded-full border-4 flex items-center justify-center transition-all duration-300 hover:scale-110 z-10",
                          event.status === "completed" && "bg-green-500 border-green-500 shadow-lg shadow-green-500/30",
                          event.status === "current" &&
                            "bg-blue-500 border-blue-500 shadow-lg shadow-blue-500/30 animate-pulse",
                          event.status === "pending" &&
                            "bg-gray-200 dark:bg-gray-600 border-gray-300 dark:border-gray-500",
                        )}
                      >
                        {event.status === "completed" && <Check className="w-6 h-6 text-white" />}
                        {event.status === "current" && <Clock className="w-6 h-6 text-white" />}
                        {event.status === "pending" && (
                          <div className="w-3 h-3 rounded-full bg-gray-400 dark:bg-gray-500" />
                        )}
                      </div>

                      <div className="mt-4 text-center max-w-32">
                        <h4 className="font-medium text-sm mb-1 text-gray-800 dark:text-gray-200">{event.title}</h4>
                        <p className="text-xs font-light mb-2 text-gray-600 dark:text-gray-400">{event.date}</p>
                        <p className="text-xs font-light leading-tight text-gray-500 dark:text-gray-400">
                          {event.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-8">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Progreso General</span>
                    <span className="text-sm font-light text-gray-800 dark:text-gray-200">{project.progress}%</span>
                  </div>
                  <div className="w-full rounded-full h-2 bg-gray-300 dark:bg-gray-600">
                    <div
                      className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full transition-all duration-1000 ease-out"
                      style={{ width: `${project.progress}%` }}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-6 mt-6">
          <Card className="backdrop-blur-md border shadow-lg bg-gray-200/40 dark:bg-gray-700/40 border-gray-300/30 dark:border-gray-600/30">
            <CardHeader className="pb-6">
              <CardTitle className="text-xl font-light flex items-center gap-2 text-gray-800 dark:text-gray-200">
                <FileText className="w-6 h-6" />
                Documentos del Proyecto
                <Badge variant="outline" className="ml-2">
                  {project.documents.length} archivos
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {project.documents.map((doc) => {
                  const IconComponent = fileTypeIcons[doc.fileType]
                  return (
                    <div
                      key={doc.id}
                      className="group flex items-center gap-4 p-4 border rounded-xl transition-all duration-200 hover:shadow-lg border-gray-300/30 dark:border-gray-600/30 hover:bg-gray-200/20 dark:hover:bg-gray-700/20"
                    >
                      <div className={cn("p-3 rounded-lg", fileTypeColors[doc.fileType])}>
                        <IconComponent className="w-6 h-6" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm truncate text-gray-800 dark:text-gray-200">{doc.name}</h4>
                        <div className="flex items-center gap-4 text-xs font-light mt-1 text-gray-500 dark:text-gray-400">
                          <span className="flex items-center gap-1">
                            <Badge variant="outline" className="text-xs">
                              {doc.type}
                            </Badge>
                          </span>
                          <span>{doc.date}</span>
                          <span>{doc.size}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDocumentAction(doc, "open")}
                          className="h-8 w-8 p-0"
                          title="Abrir documento"
                        >
                          <ExternalLink className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDocumentAction(doc, "download")}
                          className="h-8 w-8 p-0"
                          title="Descargar documento"
                        >
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="contact" className="space-y-6 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="backdrop-blur-md border shadow-lg bg-gray-200/40 dark:bg-gray-700/40 border-gray-300/30 dark:border-gray-600/30">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light flex items-center gap-2 text-gray-800 dark:text-gray-200">
                  <User className="w-5 h-5" />
                  Información de Contacto
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Contacto Principal</label>
                  <p className="text-lg font-light mt-1 text-gray-800 dark:text-gray-200">{project.contact.name}</p>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-gray-200/30 dark:bg-gray-600/30">
                      <Mail className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                    </div>
                    <div>
                      <p className="text-xs font-medium text-gray-500 dark:text-gray-400">EMAIL</p>
                      <a
                        href={`mailto:${project.contact.email}`}
                        className="text-sm font-light text-blue-600 dark:text-blue-400 hover:underline"
                      >
                        {project.contact.email}
                      </a>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-gray-200/30 dark:bg-gray-600/30">
                      <Phone className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                    </div>
                    <div>
                      <p className="text-xs font-medium text-gray-500 dark:text-gray-400">TELÉFONO</p>
                      <a
                        href={`tel:${project.contact.phone}`}
                        className="text-sm font-light text-blue-600 dark:text-blue-400 hover:underline"
                      >
                        {project.contact.phone}
                      </a>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-gray-200/30 dark:bg-gray-600/30">
                      <MapPin className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                    </div>
                    <div>
                      <p className="text-xs font-medium text-gray-500 dark:text-gray-400">DIRECCIÓN</p>
                      <p className="text-sm font-light text-gray-800 dark:text-gray-200">{project.address}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="backdrop-blur-md border shadow-lg bg-gray-200/40 dark:bg-gray-700/40 border-gray-300/30 dark:border-gray-600/30">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light flex items-center gap-2 text-gray-800 dark:text-gray-200">
                  <Building className="w-5 h-5" />
                  Acciones Rápidas
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  className="w-full justify-start gap-3 font-light bg-transparent dark:bg-transparent"
                  variant="outline"
                >
                  <Mail className="w-4 h-4" />
                  Enviar Email
                </Button>
                <Button
                  className="w-full justify-start gap-3 font-light bg-transparent dark:bg-transparent"
                  variant="outline"
                >
                  <Phone className="w-4 h-4" />
                  Llamar Cliente
                </Button>
                <Button
                  className="w-full justify-start gap-3 font-light bg-transparent dark:bg-transparent"
                  variant="outline"
                >
                  <Calendar className="w-4 h-4" />
                  Programar Reunión
                </Button>
                <Button
                  className="w-full justify-start gap-3 font-light bg-transparent dark:bg-transparent"
                  variant="outline"
                >
                  <FileText className="w-4 h-4" />
                  Generar Reporte
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </DialogContent>
  )

  const EventDetailModal = ({ event }: { event: CalendarEvent }) => {
    const relatedProject = event.relatedProject ? projectsData.find((s) => s.id === event.relatedProject) : null
    const IconComponent = eventTypeIcons[event.type]

    return (
      <DialogContent className="max-w-2xl backdrop-blur-2xl border shadow-2xl bg-white/95 dark:bg-gray-800/95 border-gray-300/50 dark:border-gray-600/50 text-gray-900 dark:text-gray-100">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 font-light text-gray-800 dark:text-gray-200">
            <div
              className={cn(
                "p-2 rounded-full text-white",
                departmentColors[event.department as keyof typeof departmentColors],
              )}
            >
              <IconComponent className="w-4 h-4" />
            </div>
            {event.title}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-light text-gray-600 dark:text-gray-400">Fecha</label>
              <p className="text-sm font-light text-gray-800 dark:text-gray-200">
                {new Date(event.date).toLocaleDateString("es-ES", {
                  weekday: "long",
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </p>
            </div>
            <div>
              <label className="text-sm font-light text-gray-600 dark:text-gray-400">Hora</label>
              <p className="text-sm font-light text-gray-800 dark:text-gray-200">{event.time}</p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-light text-gray-600 dark:text-gray-400">Departamento</label>
              <div className="flex items-center gap-2">
                <div
                  className={cn(
                    "w-3 h-3 rounded-full",
                    departmentColors[event.department as keyof typeof departmentColors],
                  )}
                />
                <span className="text-sm font-light text-gray-800 dark:text-gray-200">
                  {departmentNames[event.department as keyof typeof departmentNames]}
                </span>
              </div>
            </div>
            <div>
              <label className="text-sm font-light text-gray-600 dark:text-gray-400">Tipo</label>
              <p className="text-sm font-light text-gray-800 dark:text-gray-200">{eventTypeNames[event.type]}</p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-light text-gray-600 dark:text-gray-400">Responsable</label>
              <p className="text-sm font-light text-gray-800 dark:text-gray-200">{event.responsible}</p>
            </div>
            <div>
              <label className="text-sm font-light text-gray-600 dark:text-gray-400">Estado</label>
              <Badge className={cn("text-xs font-light", eventStatusColors[event.status])}>
                {eventStatusNames[event.status]}
              </Badge>
            </div>
          </div>

          {event.description && (
            <div>
              <label className="text-sm font-light text-gray-600 dark:text-gray-400">Descripción</label>
              <p className="text-sm font-light text-gray-700 dark:text-gray-300">{event.description}</p>
            </div>
          )}

          {relatedProject && (
            <div>
              <label className="text-sm font-light text-gray-600 dark:text-gray-400">Proyecto Relacionado</label>
              <div className="flex items-center gap-2 mt-1">
                <Button
                  variant="outline"
                  size="sm"
                  className="font-light bg-transparent dark:bg-transparent"
                  onClick={() => {
                    setSelectedEvent(null)
                    setSelectedProject(relatedProject)
                  }}
                >
                  <Building className="w-4 h-4 mr-2" />
                  {relatedProject.customer} - {relatedProject.project}
                </Button>
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button
            variant="outline"
            className="font-light bg-transparent dark:bg-transparent"
            onClick={() => setSelectedEvent(null)}
          >
            Cerrar
          </Button>
          <Button className="font-light">Editar Evento</Button>
        </div>
      </DialogContent>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-100 via-gray-200 to-amber-100 dark:from-gray-900 dark:via-blue-900 dark:to-red-900">
      <style jsx global>{`
        * {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Inter', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
          font-weight: 300;
          letter-spacing: -0.01em;
        }
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>

      <div className="p-6">
        {/* Projects Section - Horizontal Carousel */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-light tracking-wide text-gray-900 dark:text-gray-100">Proyectos Activos</h2>
            <div className="flex items-center gap-4">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-48 backdrop-blur-xl border rounded-full shadow-lg font-light bg-white/30 dark:bg-gray-700/30 border-gray-300/30 dark:border-gray-600/30 text-gray-900 dark:text-gray-100">
                  <SelectValue placeholder="Filtrar por estado" />
                </SelectTrigger>
                <SelectContent className="backdrop-blur-xl border bg-white/95 dark:bg-gray-800/95 border-gray-300/30 dark:border-gray-600/30">
                  <SelectItem value="all" className="font-light text-gray-900 dark:text-gray-100">
                    Todos los estados
                  </SelectItem>
                  <SelectItem value="pending" className="font-light text-gray-900 dark:text-gray-100">
                    Pendiente
                  </SelectItem>
                  <SelectItem value="in_progress" className="font-light text-gray-900 dark:text-gray-100">
                    En Progreso
                  </SelectItem>
                  <SelectItem value="completed" className="font-light text-gray-900 dark:text-gray-100">
                    Completado
                  </SelectItem>
                  <SelectItem value="cancelled" className="font-light text-gray-900 dark:text-gray-100">
                    Cancelado
                  </SelectItem>
                </SelectContent>
              </Select>
              <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                <SelectTrigger className="w-48 backdrop-blur-xl border rounded-full shadow-lg font-light bg-white/30 dark:bg-gray-700/30 border-gray-300/30 dark:border-gray-600/30 text-gray-900 dark:text-gray-100">
                  <SelectValue placeholder="Filtrar departamento" />
                </SelectTrigger>
                <SelectContent className="backdrop-blur-xl border bg-white/95 dark:bg-gray-800/95 border-gray-300/30 dark:border-gray-600/30">
                  <SelectItem value="all" className="font-light text-gray-900 dark:text-gray-100">
                    Todos los departamentos
                  </SelectItem>
                  {Object.entries(departmentNames).map(([key, name]) => (
                    <SelectItem key={key} value={key} className="font-light text-gray-900 dark:text-gray-100">
                      <div className="flex items-center gap-2">
                        <div
                          className={cn("w-3 h-3 rounded-full", departmentColors[key as keyof typeof departmentColors])}
                        />
                        {name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Horizontal Carousel */}
          <div className="flex gap-6 overflow-x-auto pb-4 scrollbar-hide">
            {filteredProjects.map((project) => (
              <Dialog key={project.id}>
                <DialogTrigger asChild>
                  <Card
                    className="backdrop-blur-xl border rounded-3xl min-w-80 flex-shrink-0 cursor-pointer transition-all duration-300 shadow-lg hover:shadow-xl group bg-gray-200/40 dark:bg-slate-800/40 border-gray-300/30 dark:border-slate-700/30 hover:bg-gray-100/60 dark:hover:bg-slate-700/60"
                    onClick={() => setSelectedProject(project)}
                  >
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg font-light text-gray-900 dark:text-slate-100">
                        {project.customer}
                      </CardTitle>
                      <div className="text-sm font-normal text-gray-500 dark:text-gray-400 mt-1">
                        {project.project}
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0 space-y-2">
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs text-gray-500 dark:text-gray-400">Valor</span>
                        <span className="text-lg font-light text-green-600 dark:text-green-400">
                          ${project.value.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex items-center justify-between mt-1">
                        <span className="text-xs text-gray-500 dark:text-gray-400">Estado</span>
                        <Badge className={cn("text-xs font-light px-2 py-0.5", statusColors[project.status])}>
                          {statusNames[project.status]}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between mt-1">
                        <span className="text-xs text-gray-500 dark:text-gray-400">Progreso</span>
                        <span className="text-xs text-gray-800 dark:text-slate-200">{project.progress}%</span>
                      </div>
                      <div className="w-full rounded-full h-2 bg-gray-300/50 dark:bg-gray-600/50 mt-1">
                        <div
                          className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${project.progress}%` }}
                        />
                      </div>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs text-gray-500 dark:text-gray-400">Responsable</span>
                        <span className="text-xs text-gray-800 dark:text-slate-200">{project.responsible}</span>
                      </div>
                    </CardContent>
                  </Card>
                </DialogTrigger>
                {selectedProject && <ProjectDetailModal project={selectedProject} />}
              </Dialog>
            ))}
          </div>
        </div>

        {/* Calendar Section */}
        <Card className="backdrop-blur-xl border rounded-3xl shadow-lg bg-white/30 dark:bg-gray-700/30 border-gray-300/30 dark:border-gray-600/30">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-2xl font-light tracking-wide text-gray-900 dark:text-gray-100">
                Calendario de Actividades
              </CardTitle>
              <div className="flex items-center gap-4">
                <Select value={calendarDepartmentFilter} onValueChange={setCalendarDepartmentFilter}>
                  <SelectTrigger className="w-48 backdrop-blur-xl border rounded-full shadow-lg font-light bg-white/30 dark:bg-gray-700/30 border-gray-300/30 dark:border-gray-600/30 text-gray-900 dark:text-gray-100">
                    <SelectValue placeholder="Filtrar departamento" />
                  </SelectTrigger>
                  <SelectContent className="backdrop-blur-xl border bg-white/95 dark:bg-gray-800/95 border-gray-300/30 dark:border-gray-600/30">
                    <SelectItem value="all" className="font-light text-gray-900 dark:text-gray-100">
                      Todos los departamentos
                    </SelectItem>
                    {Object.entries(departmentNames).map(([key, name]) => (
                      <SelectItem key={key} value={key} className="font-light text-gray-900 dark:text-gray-100">
                        <div className="flex items-center gap-2">
                          <div
                            className={cn(
                              "w-3 h-3 rounded-full",
                              departmentColors[key as keyof typeof departmentColors],
                            )}
                          />
                          {name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => navigateMonth("prev")}
                    className="rounded-full backdrop-blur-md border font-light bg-white/30 dark:bg-gray-700/30 border-gray-300/20 dark:border-gray-600/20 hover:bg-white/40 dark:hover:bg-gray-600/40"
                  >
                    <ChevronLeft className="w-4 h-4" />
                  </Button>
                  <h3 className="font-light text-xl text-gray-800 dark:text-gray-200">
                    {MONTHS[currentDate.getMonth()]} {currentDate.getFullYear()}
                  </h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => navigateMonth("next")}
                    className="rounded-full backdrop-blur-md border font-light bg-white/30 dark:bg-gray-700/30 border-gray-300/20 dark:border-gray-600/20 hover:bg-white/40 dark:hover:bg-gray-600/40"
                  >
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-7 gap-1 mb-4">
              {WEEKDAYS.map((day) => (
                <div key={day} className="p-3 text-center text-sm font-light text-gray-600 dark:text-gray-400">
                  {day}
                </div>
              ))}
              {getDaysInMonth(currentDate).map((date, index) => {
                const dayEvents = getEventsForDate(date)
                const isCurrentMonthDate = isCurrentMonth(date)
                const isTodayDate = isToday(date)

                return (
                  <div
                    key={index}
                    className={cn(
                      "p-2 min-h-20 border rounded-xl cursor-pointer transition-colors backdrop-blur-sm border-gray-300/30 dark:border-gray-600/30 hover:bg-white/40 dark:hover:bg-gray-600/40",
                      !isCurrentMonthDate && "bg-gray-200/10 dark:bg-gray-700/10 text-gray-400 dark:text-gray-500",
                      isTodayDate && "bg-blue-100/60 dark:bg-blue-900/60 border-blue-200 dark:border-blue-700",
                      selectedDate?.toDateString() === date.toDateString() &&
                        "bg-blue-200/60 dark:bg-blue-800/60 border-blue-300 dark:border-blue-600",
                    )}
                    onClick={() => setSelectedDate(date)}
                  >
                    <div className="text-sm font-light mb-1">{date.getDate()}</div>
                    <div className="space-y-1">
                      {dayEvents.slice(0, 2).map((event) => {
                        const IconComponent = eventTypeIcons[event.type]
                        return (
                          <div
                            key={event.id}
                            className={cn(
                              "text-xs p-1 rounded-full text-white truncate cursor-pointer hover:opacity-80",
                              departmentColors[event.department as keyof typeof departmentColors],
                            )}
                            onClick={(e) => {
                              e.stopPropagation()
                              setSelectedEvent(event)
                            }}
                            title={event.title}
                          >
                            <div className="flex items-center gap-1">
                              <IconComponent className="w-3 h-3" />
                              <span className="truncate font-light">{event.title}</span>
                            </div>
                          </div>
                        )
                      })}
                      {dayEvents.length > 2 && (
                        <div className="text-xs text-center font-light text-gray-500 dark:text-gray-400">
                          +{dayEvents.length - 2} más
                        </div>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>

            {/* Selected Date Events */}
            {selectedDate && (
              <Card className="backdrop-blur-xl border rounded-2xl mt-4 shadow-lg bg-white/20 dark:bg-gray-700/20 border-gray-300/30 dark:border-gray-600/30">
                <CardHeader>
                  <CardTitle className="text-lg font-light text-gray-800 dark:text-gray-200">
                    Eventos -{" "}
                    {selectedDate.toLocaleDateString("es-ES", {
                      weekday: "long",
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {selectedDateEvents.length === 0 ? (
                    <p className="text-center py-4 font-light text-gray-500 dark:text-gray-400">
                      No hay eventos para esta fecha
                    </p>
                  ) : (
                    <div className="space-y-3">
                      {selectedDateEvents.map((event) => {
                        const IconComponent = eventTypeIcons[event.type]
                        return (
                          <div
                            key={event.id}
                            className="flex items-center gap-3 p-3 border rounded-xl cursor-pointer transition-colors backdrop-blur-sm border-gray-300/30 dark:border-gray-600/30 hover:bg-white/30 dark:hover:bg-gray-600/40"
                            onClick={() => setSelectedEvent(event)}
                          >
                            <div
                              className={cn(
                                "p-2 rounded-full text-white",
                                departmentColors[event.department as keyof typeof departmentColors],
                              )}
                            >
                              <IconComponent className="w-4 h-4" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-light text-sm text-gray-800 dark:text-gray-200">{event.title}</h4>
                                <Badge className={cn("text-xs font-light", eventStatusColors[event.status])}>
                                  {eventStatusNames[event.status]}
                                </Badge>
                              </div>
                              <p className="text-xs font-light mb-1 text-gray-600 dark:text-gray-400">
                                {event.description}
                              </p>
                              <div className="flex items-center gap-4 text-xs font-light text-gray-500 dark:text-gray-400">
                                <span className="flex items-center gap-1">
                                  <Clock className="w-3 h-3" />
                                  {event.time}
                                </span>
                                <span className="flex items-center gap-1">
                                  <UserCheck className="w-3 h-3" />
                                  {event.responsible}
                                </span>
                                <span className="flex items-center gap-1">
                                  <Building className="w-3 h-3" />
                                  {departmentNames[event.department as keyof typeof departmentNames]}
                                </span>
                              </div>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </CardContent>
        </Card>

        {/* Event Detail Modal */}
        <Dialog open={!!selectedEvent} onOpenChange={() => setSelectedEvent(null)}>
          {selectedEvent && <EventDetailModal event={selectedEvent} />}
        </Dialog>
      </div>
    </div>
  )
}

export default function Page() {
  const router = useRouter()

  useEffect(() => {
    // Redirigir a la página de login real
    router.push('/login')
  }, [router])

  // Mostrar loading mientras redirige
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-100 via-gray-200 to-amber-100">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirigiendo al login...</p>
      </div>
    </div>
  )
}
