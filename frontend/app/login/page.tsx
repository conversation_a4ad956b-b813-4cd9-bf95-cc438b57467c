"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/components/providers/auth-provider"
import { useRouter } from "next/navigation"
import { useTheme } from "next-themes"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

import { Eye, EyeOff, Lock, User, Building2, Sparkles, AlertCircle, Sun, Moon } from "lucide-react"
import { cn } from "@/lib/utils"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function LoginPage() {
  // Limpiar storage al cargar la página de login
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_data');
      // Si usas otros keys relacionados a sesión, agrégalos aquí
    }
  }, []);

  const [credential, setCredential] = useState("")
  const [password, setPassword] = useState("")

  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const { theme, resolvedTheme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  const { login } = useAuth()
  const router = useRouter()

  // Evitar hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  // Evitar hydration mismatch
  if (!mounted) {
    return null
  }

  const isDark = resolvedTheme === 'dark'

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")
    try {
      await login({ credential, password })
      // El provider maneja la redirección
    } catch (err: any) {
      setError(err?.message || "Credenciales incorrectas o error de conexión. Por favor, intenta nuevamente.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div
      className={cn(
        "min-h-screen flex items-center justify-center relative overflow-hidden transition-all duration-1000",
        isDark
          ? "bg-gradient-to-br from-slate-900 via-blue-950 to-indigo-950 animate-gradient"
          : "bg-gradient-to-br from-gray-50 via-slate-100 to-gray-200 animate-gradient",
      )}
    >
      {/* Theme Toggle - Esquina superior derecha */}
      <div className="absolute top-6 right-6 z-50">
        <div
          className={cn(
            "flex items-center gap-3 p-3 rounded-xl backdrop-blur-xl border transition-all duration-500 shadow-lg",
            isDark
              ? "bg-slate-900/40 border-slate-700/50 text-slate-200"
              : "bg-white/20 border-moka-bistre/20 text-moka-bistre",
          )}
        >
          <Sun className={cn("h-4 w-4 transition-colors", isDark ? "text-slate-400" : "text-moka-falu")} />
          <Switch
            checked={isDark}
            onCheckedChange={(checked) => setTheme(checked ? "dark" : "light")}
            className={cn(
              "data-[state=checked]:bg-slate-600 transition-colors duration-300",
              isDark ? "data-[state=unchecked]:bg-slate-700" : "data-[state=unchecked]:bg-moka-lion",
            )}
          />
          <Moon className={cn("h-4 w-4 transition-colors", isDark ? "text-blue-400" : "text-slate-500")} />
        </div>
      </div>
      {/* Elementos de fondo animados */}
      <div className="absolute inset-0 overflow-hidden">
        <div
          className={cn(
            "absolute -top-40 -right-40 w-96 h-96 rounded-full blur-3xl animate-pulse opacity-80",
            isDark
              ? "bg-gradient-to-br from-blue-500/40 to-purple-600/40"
              : "bg-gradient-to-br from-orange-300/50 to-amber-400/50",
          )}
          style={{ animationDelay: "0s", animationDuration: "2.5s" }}
        />

        <div
          className={cn(
            "absolute -bottom-40 -left-40 w-96 h-96 rounded-full blur-3xl animate-pulse opacity-80",
            isDark
              ? "bg-gradient-to-br from-amber-500/40 to-orange-600/40"
              : "bg-gradient-to-br from-rose-300/50 to-pink-400/50",
          )}
          style={{ animationDelay: "0.8s", animationDuration: "3s" }}
        />

        {/* Elementos adicionales para más movimiento */}
        <div
          className={cn(
            "absolute top-1/4 left-1/4 w-32 h-32 rounded-full blur-2xl opacity-95 animate-bounce",
            isDark
              ? "bg-gradient-to-br from-purple-400/30 to-pink-500/30"
              : "bg-gradient-to-br from-yellow-300/60 to-orange-300/60",
          )}
          style={{ animationDelay: "0.3s", animationDuration: "2s" }}
        />

        <div
          className={cn(
            "absolute top-3/4 right-1/4 w-24 h-24 rounded-full blur-xl opacity-80 animate-ping",
            isDark
              ? "bg-gradient-to-br from-cyan-400/25 to-blue-500/25"
              : "bg-gradient-to-br from-amber-300/60 to-yellow-400/60",
          )}
          style={{ animationDelay: "0.2s", animationDuration: "2.5s" }}
        />

        <div
          className={cn(
            "absolute top-1/2 left-1/2 w-16 h-16 rounded-full blur-lg opacity-55 animate-spin",
            isDark
              ? "bg-gradient-to-br from-indigo-400/40 to-purple-500/40"
              : "bg-gradient-to-br from-orange-400/70 to-red-400/70",
          )}
          style={{ animationDuration: "5s" }}
        />

        <div
          className={cn(
            "absolute top-1/3 right-1/3 w-20 h-20 rounded-full blur-xl opacity-40 animate-pulse",
            isDark
              ? "bg-gradient-to-br from-teal-400/20 to-green-500/20"
              : "bg-gradient-to-br from-peach-400/60 to-orange-400/60",
          )}
          style={{ animationDelay: "1s", animationDuration: "3.5s" }}
        />

        {/* Más elementos para mayor dinamismo */}
        <div
          className={cn(
            "absolute top-10 right-10 w-12 h-12 rounded-full blur-md opacity-60 animate-bounce",
            isDark
              ? "bg-gradient-to-br from-blue-300/40 to-indigo-400/40"
              : "bg-gradient-to-br from-yellow-400/80 to-amber-400/80",
          )}
          style={{ animationDelay: "0.5s", animationDuration: "1.5s" }}
        />

        <div
          className={cn(
            "absolute bottom-20 left-10 w-8 h-8 rounded-full blur-sm opacity-70 animate-pulse",
            isDark
              ? "bg-gradient-to-br from-purple-300/50 to-pink-400/50"
              : "bg-gradient-to-br from-rose-400/80 to-pink-400/80",
          )}
          style={{ animationDelay: "0.4s", animationDuration: "2s" }}
        />

        <div
          className={cn(
            "absolute top-2/3 left-3/4 w-6 h-6 rounded-full blur-sm opacity-65 animate-ping",
            isDark
              ? "bg-gradient-to-br from-cyan-300/35 to-blue-400/35"
              : "bg-gradient-to-br from-orange-300/80 to-red-300/80",
          )}
          style={{ animationDelay: "0.6s", animationDuration: "3s" }}
        />

        <div
          className={cn(
            "absolute top-1/4 right-2/3 w-10 h-10 rounded-full blur-lg opacity-50 animate-spin",
            isDark
              ? "bg-gradient-to-br from-emerald-300/25 to-teal-400/25"
              : "bg-gradient-to-br from-amber-300/70 to-orange-300/70",
          )}
          style={{ animationDuration: "7s" }}
        />

        {/* Elementos específicos para el área de los campos de entrada */}
        <div
          className={cn(
            "absolute top-1/2 left-1/3 w-14 h-14 rounded-full blur-lg opacity-50 animate-pulse",
            isDark
              ? "bg-gradient-to-br from-blue-400/50 to-purple-500/50"
              : "bg-gradient-to-br from-yellow-400/70 to-orange-500/70",
          )}
          style={{ animationDelay: "0.7s", animationDuration: "2.5s" }}
        />

        <div
          className={cn(
            "absolute top-2/5 right-1/3 w-18 h-18 rounded-full blur-xl opacity-45 animate-bounce",
            isDark
              ? "bg-gradient-to-br from-indigo-400/45 to-blue-500/45"
              : "bg-gradient-to-br from-rose-400/65 to-pink-500/65",
          )}
          style={{ animationDelay: "0.4s", animationDuration: "2s" }}
        />

        <div
          className={cn(
            "absolute top-3/5 left-2/5 w-10 h-10 rounded-full blur-md opacity-60 animate-ping",
            isDark
              ? "bg-gradient-to-br from-purple-400/60 to-pink-500/60"
              : "bg-gradient-to-br from-amber-400/80 to-red-400/80",
          )}
          style={{ animationDelay: "0.9s", animationDuration: "2.8s" }}
        />

        {/* Elementos adicionales para efecto cristal más intenso */}
        <div
          className={cn(
            "absolute top-1/6 left-1/2 w-4 h-4 rounded-full blur-sm opacity-90 animate-pulse",
            isDark
              ? "bg-gradient-to-br from-blue-400/90 to-indigo-500/90"
              : "bg-gradient-to-br from-yellow-500/90 to-orange-500/90",
          )}
          style={{ animationDelay: "0.1s", animationDuration: "1.8s" }}
        />

        <div
          className={cn(
            "absolute top-5/6 right-1/2 w-6 h-6 rounded-full blur-md opacity-85 animate-bounce",
            isDark
              ? "bg-gradient-to-br from-purple-400/85 to-pink-500/85"
              : "bg-gradient-to-br from-red-400/85 to-pink-500/85",
          )}
          style={{ animationDelay: "0.3s", animationDuration: "1.2s" }}
        />

        <div
          className={cn(
            "absolute top-1/3 left-1/6 w-3 h-3 rounded-full blur-sm opacity-95 animate-ping",
            isDark
              ? "bg-gradient-to-br from-cyan-400/95 to-blue-500/95"
              : "bg-gradient-to-br from-amber-500/95 to-yellow-500/95",
          )}
          style={{ animationDelay: "0.8s", animationDuration: "2.2s" }}
        />

        <div
          className={cn(
            "absolute top-2/3 right-1/6 w-5 h-5 rounded-full blur-sm opacity-80 animate-spin",
            isDark
              ? "bg-gradient-to-br from-emerald-400/80 to-teal-500/80"
              : "bg-gradient-to-br from-orange-500/80 to-red-500/80",
          )}
          style={{ animationDuration: "4s" }}
        />
      </div>

      {/* Tarjeta de Login */}
      <Card
        className={cn(
          "w-full max-w-md mx-4 backdrop-blur-2xl shadow-2xl relative z-10 transition-all duration-500 force-no-border card-no-border",
          isDark ? "bg-slate-900/40" : "bg-moka-peach/20 backdrop-blur-2xl border-moka-falu/40 shadow-2xl",
          isLoading && "animate-pulse-glow",
        )}
      >
        <CardHeader className="space-y-6 text-center">
          {/* Logo COMINTEC */}
          <div className="flex justify-center">
            <div
              className={cn(
                "p-4 rounded-full shadow-2xl transition-all duration-500 animate-pulse-glow",
                isDark
                  ? "bg-gradient-to-br from-blue-600 to-indigo-700"
                  : "bg-gradient-to-br from-moka-falu to-moka-falu",
              )}
            >
              <Building2 className="w-10 h-10 text-white" />
            </div>
          </div>

          <div className="space-y-3">
            <CardTitle
              className={cn(
                "text-4xl font-extralight tracking-wider transition-colors duration-500",
                isDark ? "text-white" : "text-moka-bistre",
              )}
            >
              COMINTEC
            </CardTitle>
            <CardDescription
              className={cn(
                "text-base font-light transition-colors duration-500",
                isDark ? "text-slate-300" : "text-moka-falu",
              )}
            >
              Sistema Integral de Gestión Empresarial
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4 mr-2" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            {/* Campo Usuario/Email */}
            <div className="space-y-2">
              <Label
                htmlFor="credential"
                className={cn(
                  "text-sm font-light transition-colors duration-500",
                  isDark ? "text-slate-200" : "text-moka-bistre",
                )}
              >
                Usuario o Email
              </Label>
              <div className="relative">
                <User
                  className={cn(
                    "absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 transition-colors duration-500",
                    isDark ? "text-slate-400" : "text-moka-falu",
                  )}
                />
                <Input
                  id="credential"
                  type="text"
                  placeholder="Ingresa tu usuario o email"
                  value={credential}
                  onChange={(e) => setCredential(e.target.value)}
                  className={cn(
                    "pl-10 h-12 backdrop-blur-md border font-light transition-all duration-500 focus:ring-2 focus:ring-blue-500/50",
                    isDark
                      ? "bg-slate-800/50 border-slate-600/50 text-white placeholder:text-slate-400 focus:border-blue-400"
                      : "bg-white/5 backdrop-blur-lg border-moka-bistre/30 text-moka-bistre placeholder:text-moka-falu/80 focus:border-moka-falu focus:ring-2 focus:ring-moka-falu/30 shadow-lg",
                  )}
                  required
                  disabled={isLoading}
                />
              </div>
            </div>

            {/* Campo Contraseña */}
            <div className="space-y-2">
              <Label
                htmlFor="password"
                className={cn(
                  "text-sm font-light transition-colors duration-500",
                  isDark ? "text-slate-200" : "text-moka-bistre",
                )}
              >
                Contraseña
              </Label>
              <div className="relative">
                <Lock
                  className={cn(
                    "absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 transition-colors duration-500",
                    isDark ? "text-slate-400" : "text-moka-falu",
                  )}
                />
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Ingresa tu contraseña"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className={cn(
                    "pl-10 pr-10 h-12 backdrop-blur-md border font-light transition-all duration-500 focus:ring-2 focus:ring-blue-500/50",
                    isDark
                      ? "bg-slate-800/50 border-slate-600/50 text-white placeholder:text-slate-400 focus:border-blue-400"
                      : "bg-white/15 backdrop-blur-lg border-moka-bistre/30 text-moka-bistre placeholder:text-moka-falu/80 focus:border-moka-falu focus:ring-2 focus:ring-moka-falu/30 shadow-lg",
                  )}
                  required
                  disabled={isLoading}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className={cn(
                    "absolute right-3 top-1/2 transform -translate-y-1/2 transition-colors duration-500 hover:scale-110",
                    isDark ? "text-slate-400 hover:text-slate-200" : "text-moka-falu hover:text-moka-bistre",
                  )}
                  tabIndex={-1}
                  disabled={isLoading}
                >
                  {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>



            {/* Botón de Login */}
            <Button
              type="submit"
              className={cn(
                "w-full font-light text-base py-6 rounded-xl transition-all duration-500 shadow-2xl transform hover:scale-[1.02]",
                isDark
                  ? "bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800"
                  : "bg-gradient-to-r from-moka-falu to-moka-falu hover:from-moka-falu/90 hover:to-moka-falu/90",
                "text-white hover:shadow-blue-500/25",
                isLoading && "opacity-70 cursor-not-allowed animate-pulse",
              )}
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center gap-3">
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  <span>Iniciando sesión...</span>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <span>Iniciar Sesión</span>
                  <Sparkles className="w-4 h-4" />
                </div>
              )}
            </Button>


          </form>


        </CardContent>
      </Card>

      {/* Footer */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 text-center">
        <p
          className={cn(
            "text-xs font-light transition-colors duration-500",
            isDark ? "text-slate-400" : "text-moka-falu",
          )}
        >
          © 2024 COMINTEC. Sistema de Gestión Empresarial
        </p>
      </div>
    </div>
  )
}
