'use client';

import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';

export default function SupabaseTestPage() {
  const [connectionStatus, setConnectionStatus] = useState<string>('Conectando...');
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState<boolean>(false);

  useEffect(() => {
    const testConnection = async () => {
      try {
        setConnectionStatus('Probando conexión con Supabase...');
        
        // 1. Primero probamos una consulta simple a la tabla auth.users
        const { data, error: queryError } = await supabase
          .from('users')
          .select('*')
          .limit(1);

        if (queryError) {
          // Si falla, puede ser porque la tabla no existe o no tenemos permisos
          // Lo cual es normal, solo verificamos que la conexión funcione
          console.log('No se pudo acceder a la tabla users:', queryError.message);
        }
        
        // Si llegamos aquí sin errores, la conexión es exitosa
        setIsConnected(true);
        setConnectionStatus('Conexión exitosa con Supabase');
        
      } catch (err: unknown) {
        const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
        console.error('Error al conectar con Supabase:', err);
        setError(`Error de conexión: ${errorMessage}`);
        setConnectionStatus('Error de conexión');
      }
    };

    testConnection();
  }, []);

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold mb-6">Prueba de conexión con Supabase</h1>
        
        <div className={`mb-6 p-4 border-l-4 ${
          error ? 'bg-red-50 border-red-500' : 
          isConnected ? 'bg-green-50 border-green-500' : 
          'bg-blue-50 border-blue-500'
        }`}>
          <div className="flex items-center">
            {error ? (
              <svg className="h-5 w-5 text-red-500 mr-3 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            ) : isConnected ? (
              <svg className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            ) : (
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500 mr-3"></div>
            )}
            <div>
              <p className={`font-medium ${
                error ? 'text-red-800' : 
                isConnected ? 'text-green-800' : 
                'text-blue-800'
              }`}>
                {connectionStatus}
              </p>
              {error && (
                <p className="mt-1 text-sm text-red-700">{error}</p>
              )}
            </div>
          </div>
        </div>

        {isConnected && (
          <div className="space-y-6">
            <div>
              <h2 className="text-lg font-medium text-gray-900 mb-2">Variables de entorno:</h2>
              <div className="bg-gray-50 p-4 rounded-md overflow-x-auto">
                <pre className="text-sm text-gray-700">
                  {`NEXT_PUBLIC_SUPABASE_URL: ${process.env.NEXT_PUBLIC_SUPABASE_URL ? '✅ Configurada' : '❌ No configurada'}\n`}
                  {`NEXT_PUBLIC_SUPABASE_ANON_KEY: ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✅ Configurada' : '❌ No configurada'}`}
                </pre>
              </div>
            </div>

            <div className="pt-4 border-t border-gray-200">
              <h3 className="text-md font-medium text-gray-900 mb-2">Siguientes pasos:</h3>
              <ul className="list-disc pl-5 space-y-2 text-gray-700">
                <li>Configura las políticas RLS en el panel de Supabase</li>
                <li>Crea tus tablas y relaciones</li>
                <li>Implementa la autenticación</li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
