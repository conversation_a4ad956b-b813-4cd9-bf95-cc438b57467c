export const ROLES = {
  ADMIN: 'ROLE_ADMIN',
  MANAGER: 'ROLE_MANAGER',
  SALES: 'ROLE_SALES',
  FINANCE: 'ROLE_FINANCE',
  WAREHOUSE: 'ROLE_WAREHOUSE',
  USER: 'ROLE_USER',
  CALIDAD: 'ROLE_CALIDAD',
  LOGISTICA: 'ROLE_LOGISTICA',
  METROLOGIA: 'ROLE_METROLOGIA',
  RH: 'ROLE_RH',
  SISTEMAS: 'ROLE_SISTEMAS',
  INFORMES: 'ROLE_INFORMES',
};

// Mapeo para mostrar nombres amigables en la UI
export const ROLE_LABELS = {
  'ROLE_ADMIN': 'Administrador',
  'ROLE_MANAGER': 'Gerente',
  'ROLE_USER': 'Usuario General',
  'ROLE_SISTEMAS': 'Sistemas',
  'ROLE_SALES': 'Ventas',
  'R<PERSON><PERSON>_FINANCE': 'Finanzas',
  'ROLE_WAREHOUSE': 'Almacén',
  'ROLE_CALIDAD': 'Calidad',
  'ROLE_LOGISTICA': 'Logística',
  'ROLE_METROLOGIA': 'Metrología',
  'ROLE_RH': 'Recursos Humanos',
  'ROLE_INFORMES': 'Informes',
  'ROLE_ENGINEER': 'Ingeniero',
};

// Función helper para obtener el nombre amigable de un rol
export const getRoleLabel = (roleName: string): string => {
  return ROLE_LABELS[roleName as keyof typeof ROLE_LABELS] || roleName;
}