'use client';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';

const tareas = [
  {
    id: 1,
    descripcion: 'Calibración de manómetro',
    asignadoA: '<PERSON>',
    estado: 'En progreso',
  },
  {
    id: 2,
    descripcion: 'Verificación de termómetro',
    asignadoA: '<PERSON>',
    estado: 'Pendiente',
  },
  {
    id: 3,
    descripcion: 'Mantenimiento de balanza',
    asignadoA: '<PERSON>',
    estado: 'Completada',
  },
];

export function TareasTable() {
  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Asignación de Tareas</h2>
        <Button>Crear Tarea</Button>
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>ID</TableHead>
            <TableHead>Descripción</TableHead>
            <TableHead>Asignado a</TableHead>
            <TableHead>Estado</TableHead>
            <TableHead>Acciones</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tareas.map((tarea) => (
            <TableRow key={tarea.id}>
              <TableCell>{tarea.id}</TableCell>
              <TableCell>{tarea.descripcion}</TableCell>
              <TableCell>{tarea.asignadoA}</TableCell>
              <TableCell>{tarea.estado}</TableCell>
              <TableCell>
                <Button variant="outline" size="sm" className="mr-2">
                  Editar
                </Button>
                <Button variant="destructive" size="sm">
                  Eliminar
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}