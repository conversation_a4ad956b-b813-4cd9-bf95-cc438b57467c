'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export function ReportesMetrologia() {
  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Reportes de Metrología</h2>
        <Button>Generar Reporte</Button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Reporte de Tiempos de Entrega</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Visualiza los tiempos de entrega de informes de calibración.</p>
            <Button variant="outline" className="mt-4">
              Ver Reporte
            </Button>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Reporte de Rendimiento de Personal</CardTitle>
          </CardHeader>
          <CardContent>
            <p><PERSON>liza el rendimiento y la carga de trabajo del personal.</p>
            <Button variant="outline" className="mt-4">
              Ver Reporte
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}