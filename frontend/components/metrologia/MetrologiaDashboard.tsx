'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';

export function MetrologiaDashboard() {
  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Dashboard de Metrología</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Informes Entregados</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">125</p>
            <p className="text-sm text-muted-foreground">+10% vs mes anterior</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Tareas Pendientes</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">15</p>
            <p className="text-sm text-muted-foreground">3 urgentes</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Próximas Capacitaciones</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">3</p>
            <p className="text-sm text-muted-foreground">En los próximos 30 días</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Rendimiento del Personal</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">95%</p>
            <p className="text-sm text-muted-foreground">Cumplimiento de tareas</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}