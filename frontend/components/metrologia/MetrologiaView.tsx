'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { MetrologiaDashboard } from './MetrologiaDashboard.tsx';
import { TareasTable } from './TareasTable.tsx';
import { CapacitacionesCalendar } from './CapacitacionesCalendar.tsx';
import { ReportesMetrologia } from './ReportesMetrologia.tsx';

export function MetrologiaView() {
  const [activeTab, setActiveTab] = useState('dashboard');

  return (
    <div className="space-y-6 p-6">
      {/* Header Section */}
      <header className="space-y-2 mb-8">
        <h1 className="text-3xl font-medium tracking-tight text-gray-900 dark:text-slate-100">
          Metrología
        </h1>
        <p className="text-gray-600 dark:text-slate-400">
          Gestión de calibraciones, asignación de tareas y capacitaciones del área de metrología.
        </p>
      </header>

      <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
        <CardContent className="p-6">
          <Tabs defaultValue="dashboard" onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
              <TabsTrigger value="tareas">Asignación de Tareas</TabsTrigger>
              <TabsTrigger value="capacitaciones">Capacitaciones</TabsTrigger>
              <TabsTrigger value="reportes">Reportes</TabsTrigger>
            </TabsList>
            <TabsContent value="dashboard" className="mt-6">
              <MetrologiaDashboard />
            </TabsContent>
            <TabsContent value="tareas" className="mt-6">
              <TareasTable />
            </TabsContent>
            <TabsContent value="capacitaciones" className="mt-6">
              <CapacitacionesCalendar />
            </TabsContent>
            <TabsContent value="reportes" className="mt-6">
              <ReportesMetrologia />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}