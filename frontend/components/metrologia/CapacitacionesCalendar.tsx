'use client';

import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export function CapacitacionesCalendar() {
  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Calendario de Capacitaciones</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="md:col-span-2">
          <Card>
            <CardContent className="p-0">
              <Calendar
                mode="range"
                className="p-3"
              />
            </CardContent>
          </Card>
        </div>
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Próximas Capacitaciones</CardTitle>
            </CardHeader>
            <CardContent>
              <ul>
                <li className="mb-2">
                  <p className="font-semibold">Introducción a la Metrología</p>
                  <p className="text-sm text-muted-foreground">Fecha: 2024-08-15</p>
                </li>
                <li className="mb-2">
                  <p className="font-semibold">Calibración de Equipos de Presión</p>
                  <p className="text-sm text-muted-foreground">Fecha: 2024-09-01</p>
                </li>
                <li>
                  <p className="font-semibold">Uso de Software de Gestión</p>
                  <p className="text-sm text-muted-foreground">Fecha: 2024-09-20</p>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}