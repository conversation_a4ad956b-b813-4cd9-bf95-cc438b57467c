"use client"

import { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Key,
  UserCheck,
  UserX,
  Shield,
  RefreshCw,
  Users
} from 'lucide-react'
import { toast } from 'sonner'
import { useUserNotifications } from '@/components/ui/notification-provider'
import {
  useUsers,
  useDeleteUser,
  useUpdateUserStatus
} from '@/hooks/useUsers'
import { User, UserFilters } from '@/lib/services/user'
import { UserFormModal } from './UserFormModal'
import { PasswordChangeModal } from './PasswordChangeModal'

// Interfaces para modales
interface ModalState {
  open: boolean
  user: User | null
}

interface DeleteModalState extends ModalState {}
interface EditModalState extends ModalState {}
interface PasswordModalState extends ModalState {}

export function GestionUsuariosView() {
  console.log('[GestionUsuariosView] Component rendering...')

  // Detectar si la altura de la ventana es reducida (como cuando está abierta la consola)
  const [isReducedHeight, setIsReducedHeight] = useState(false)

  useEffect(() => {
    const checkHeight = () => {
      setIsReducedHeight(window.innerHeight < 600)
    }

    checkHeight()
    window.addEventListener('resize', checkHeight)
    return () => window.removeEventListener('resize', checkHeight)
  }, [])

  // Estados de filtros
  const [filters, setFilters] = useState<UserFilters>({
    page: 1,
    limit: 10,
    search: '',
    status: '',
    area: '',
    role: ''
  })

  // Estados de modales
  const [editModal, setEditModal] = useState<EditModalState>({ open: false, user: null })
  const [deleteModal, setDeleteModal] = useState<DeleteModalState>({ open: false, user: null })
  const [passwordModal, setPasswordModal] = useState<PasswordModalState>({ open: false, user: null })

  // Queries y mutations
  const { data: usersData, isLoading, error, refetch } = useUsers(filters)
  const deleteUserMutation = useDeleteUser()
  const updateStatusMutation = useUpdateUserStatus()

  // Notificaciones de usuario
  const {
    notifyUserCreated,
    notifyUserUpdated,
    notifyUserActivated,
    notifyUserDeactivated,
    notifyUserBlocked,
    notifyUserDeleted,
    notifyPasswordChanged,
    notifyUserError
  } = useUserNotifications()

  console.log('[GestionUsuariosView] Users data:', usersData)
  console.log('[GestionUsuariosView] Loading:', isLoading)
  console.log('[GestionUsuariosView] Error:', error)
  console.log('[GestionUsuariosView] Filters:', filters)
  
  // Logs adicionales para debugging
  console.log('[GestionUsuariosView] usersData type:', typeof usersData)
  console.log('[GestionUsuariosView] usersData keys:', usersData ? Object.keys(usersData) : 'null')
  console.log('[GestionUsuariosView] usersData.users:', usersData?.users)
  console.log('[GestionUsuariosView] usersData.users length:', usersData?.users?.length)
  console.log('[GestionUsuariosView] Raw users array:', usersData?.users)
  console.log('[GestionUsuariosView] Users array length:', usersData?.users?.length)

  // Handlers de filtros
  const handleSearchChange = (value: string) => {
    setFilters(prev => ({ ...prev, search: value, page: 1 }))
  }

  const handleStatusFilter = (status: string) => {
    setFilters(prev => ({ ...prev, status: status === 'all' ? '' : status, page: 1 }))
  }

  const handleAreaFilter = (area: string) => {
    setFilters(prev => ({ ...prev, area: area === 'all' ? '' : area, page: 1 }))
  }

  const resetFilters = () => {
    setFilters({
      page: 1,
      limit: 10,
      search: '',
      status: '',
      area: '',
      role: ''
    })
  }

  // Handlers de paginación
  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({ ...prev, page: newPage }))
  }

  const handleLimitChange = (newLimit: string) => {
    setFilters(prev => ({ ...prev, limit: parseInt(newLimit), page: 1 }))
  }

  // Handlers de acciones
  const handleView = (user: User) => {
    toast.info(`Viendo usuario: ${user.name}`)
  }

  const handleEdit = (user: User) => {
    setEditModal({ open: true, user })
  }

  const handleDelete = (user: User) => {
    setDeleteModal({ open: true, user })
  }

  const handlePasswordChange = (user: User) => {
    setPasswordModal({ open: true, user })
  }

  const handleStatusChange = async (user: User, newStatus: 'activo' | 'inactivo' | 'bloqueado') => {
    try {
      await updateStatusMutation.mutateAsync({ id: user.id, status: newStatus })

      // Usar las nuevas notificaciones animadas
      switch (newStatus) {
        case 'activo':
          notifyUserActivated(user.name, `${user.name} ahora puede acceder al sistema.`)
          break
        case 'inactivo':
          notifyUserDeactivated(user.name, `${user.name} ha sido desactivado temporalmente.`)
          break
        case 'bloqueado':
          notifyUserBlocked(user.name, `${user.name} ha sido bloqueado por seguridad.`)
          break
      }
    } catch (error) {
      notifyUserError(user.name, 'Error al cambiar el estado del usuario')
      console.error('Error changing user status:', error)
    }
  }

  const handleDeleteConfirm = async () => {
    if (deleteModal.user) {
      try {
        await deleteUserMutation.mutateAsync(deleteModal.user.id)
        notifyUserDeleted(deleteModal.user.name, `${deleteModal.user.name} ha sido eliminado permanentemente del sistema.`)
        setDeleteModal({ open: false, user: null })
      } catch (error) {
        notifyUserError(deleteModal.user.name, 'Error al eliminar el usuario')
        console.error('Error deleting user:', error)
      }
    }
  }

  // Función para obtener el badge de estado
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'activo':
        return <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">Activo</Badge>
      case 'inactivo':
        return <Badge variant="secondary" className="bg-gray-100 text-gray-800 dark:bg-slate-600 dark:text-slate-300">Inactivo</Badge>
      case 'bloqueado':
        return <Badge variant="destructive" className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">Bloqueado</Badge>
      default:
        return <Badge variant="outline" className="bg-gray-100 text-gray-800 dark:bg-slate-600 dark:text-slate-300">{status}</Badge>
    }
  }

  if (error) {
    return (
      <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
        <CardContent className="p-6">
          <div className="text-center">
            <p className="text-red-600 dark:text-red-400">Error al cargar usuarios: {error.message}</p>
            <Button onClick={() => refetch()} className="mt-2">
              <RefreshCw className="w-4 h-4 mr-2" />
              Reintentar
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  const users = Array.isArray(usersData?.users) ? usersData.users : []
  if (!Array.isArray(users)) {
    console.error('[GestionUsuariosView] users no es array:', users)
  }
  const totalPages = usersData?.totalPages || 1

  return (
    <div className="min-h-screen bg-gradient-to-br from-moka-peach/80 via-moka-lion/70 to-moka-brown/60 dark:bg-gradient-to-br dark:from-slate-900 dark:via-blue-950 dark:to-red-950">
      <div className="space-y-6 p-6 max-w-full overflow-hidden">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-medium tracking-tight flex items-center gap-2 text-moka-bistre dark:text-foreground">
            <Users className="w-6 h-6" />
            Gestión de Usuarios
          </h1>
          <p className="text-moka-brown/80 dark:text-muted-foreground">
            Administra usuarios, roles y permisos del sistema
          </p>
        </div>
        <Button onClick={() => setEditModal({ open: true, user: null })} className="shrink-0 bg-moka-falu hover:bg-moka-falu/90 text-white border-0">
          <Plus className="w-4 h-4 mr-2" />
          Crear Usuario
        </Button>
      </div>

      {/* Filtros */}
      <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
        <CardHeader>
          <CardTitle className="text-gray-900 dark:text-slate-100">Filtros</CardTitle>
          <CardDescription className="text-gray-600 dark:text-slate-400">
            Busca y filtra usuarios por diferentes criterios
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div
            className="gap-4"
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(min(100%, 200px), 1fr))'
            }}
          >
            {/* Búsqueda */}
            <div className="space-y-2">
              <Label htmlFor="search" className="text-gray-700 dark:text-slate-300">Buscar</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Nombre o email..."
                  value={filters.search}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="pl-8 bg-white/50 dark:bg-slate-700/50 border-gray-200 dark:border-slate-600"
                />
              </div>
            </div>

            {/* Filtro por estado */}
            <div className="space-y-2">
              <Label className="text-gray-700 dark:text-slate-300">Estado</Label>
              <Select value={filters.status || 'all'} onValueChange={handleStatusFilter}>
                <SelectTrigger className="bg-white/50 dark:bg-slate-700/50 border-gray-200 dark:border-slate-600">
                  <SelectValue placeholder="Todos los estados" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los estados</SelectItem>
                  <SelectItem value="activo">Activo</SelectItem>
                  <SelectItem value="inactivo">Inactivo</SelectItem>
                  <SelectItem value="bloqueado">Bloqueado</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Filtro por área */}
            <div className="space-y-2">
              <Label className="text-gray-700 dark:text-slate-300">Área</Label>
              <Select value={filters.area || 'all'} onValueChange={handleAreaFilter}>
                <SelectTrigger className="bg-white/50 dark:bg-slate-700/50 border-gray-200 dark:border-slate-600">
                  <SelectValue placeholder="Todas las áreas" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas las áreas</SelectItem>
                  <SelectItem value="Sistemas">Sistemas</SelectItem>
                  <SelectItem value="Ventas">Ventas</SelectItem>
                  <SelectItem value="Administración">Administración</SelectItem>
                  <SelectItem value="Almacén">Almacén</SelectItem>
                  <SelectItem value="Logística">Logística</SelectItem>
                  <SelectItem value="Metrología">Metrología</SelectItem>
                  <SelectItem value="RH">Recursos Humanos</SelectItem>
                  <SelectItem value="Calidad">Calidad</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Botón reset */}
            <div className="space-y-2">
              <Label className="text-gray-700 dark:text-slate-300">&nbsp;</Label>
              <Button variant="outline" onClick={resetFilters} className="w-full">
                <Filter className="w-4 h-4 mr-2" />
                Limpiar Filtros
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabla de usuarios */}
      <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
        <CardHeader className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <CardTitle className="text-gray-900 dark:text-slate-100">Usuarios ({usersData?.total || 0})</CardTitle>
            <CardDescription className="text-gray-600 dark:text-slate-400">
              Página {filters.page} de {totalPages}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Label htmlFor="limit" className="text-gray-700 dark:text-slate-300">Mostrar:</Label>
            <Select value={filters.limit?.toString()} onValueChange={handleLimitChange}>
              <SelectTrigger className="w-20 bg-white/50 dark:bg-slate-700/50 border-gray-200 dark:border-slate-600">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2 text-gray-600 dark:text-slate-400" />
              <p className="text-gray-600 dark:text-slate-400">Cargando usuarios...</p>
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-8">
              <Users className="w-12 h-12 mx-auto mb-4 text-gray-400 dark:text-slate-500" />
              <h3 className="text-lg font-medium mb-2 text-gray-900 dark:text-slate-100">No se encontraron usuarios</h3>
              <p className="text-gray-600 dark:text-slate-400 mb-4">
                No hay usuarios que coincidan con los filtros aplicados.
              </p>
              <Button variant="outline" onClick={resetFilters}>
                Limpiar filtros
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Tabla responsive con scroll horizontal y altura controlada */}
              <div className="rounded-md border border-gray-200 dark:border-slate-700 bg-white/30 dark:bg-slate-800/30 overflow-x-auto max-h-[60vh] overflow-y-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="border-gray-200 dark:border-slate-700">
                        <TableHead className="min-w-[200px] text-gray-700 dark:text-slate-300">Usuario</TableHead>
                        <TableHead className="min-w-[120px] text-gray-700 dark:text-slate-300">Contacto</TableHead>
                        <TableHead className="min-w-[100px] text-gray-700 dark:text-slate-300">Área</TableHead>
                        <TableHead className="min-w-[80px] text-gray-700 dark:text-slate-300">Estado</TableHead>
                        <TableHead className="min-w-[120px] text-gray-700 dark:text-slate-300">Roles</TableHead>
                        <TableHead className="min-w-[100px] text-gray-700 dark:text-slate-300">Último Acceso</TableHead>
                        <TableHead className="text-right min-w-[80px] text-gray-700 dark:text-slate-300">Acciones</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {users.map((user) => (
                        <TableRow key={user.id} className="border-gray-200 dark:border-slate-700">
                          <TableCell>
                            <div>
                              <div className="font-medium text-gray-900 dark:text-slate-100">{user.name}</div>
                              <div className="text-sm text-gray-600 dark:text-slate-400 truncate max-w-[180px]">{user.email}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm text-gray-600 dark:text-slate-400">
                              {user.phone || 'No especificado'}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="bg-gray-100 text-gray-800 dark:bg-slate-600 dark:text-slate-300">
                              {user.area || 'Sin área'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(user.status)}
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1 max-w-[120px]">
                              {user.roles?.map((role) => (
                                <Badge key={role.id} variant="secondary" className="text-xs bg-amber-100 text-amber-800 dark:bg-slate-700 dark:text-slate-300">
                                  {role.name}
                                </Badge>
                              )) || <span className="text-sm text-gray-600 dark:text-slate-400">Sin roles</span>}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm text-gray-600 dark:text-slate-400">
                              {new Date(user.updated_at).toLocaleDateString()}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu modal={false}>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent
                                align={isReducedHeight ? "center" : "end"}
                                className={`w-48 ${isReducedHeight ? 'max-h-[40vh]' : 'max-h-[80vh]'} overflow-y-auto`}
                                side={isReducedHeight ? "left" : "bottom"}
                                sideOffset={isReducedHeight ? 10 : 5}
                                alignOffset={isReducedHeight ? 0 : -5}
                                avoidCollisions={true}
                                collisionPadding={isReducedHeight ? 16 : 8}
                                forceMount={false}
                              >
                                <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onSelect={() => handleEdit(user)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Editar
                                </DropdownMenuItem>
                                <DropdownMenuItem onSelect={() => handlePasswordChange(user)}>
                                  <Key className="mr-2 h-4 w-4" />
                                  Cambiar Contraseña
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                {user.status === 'activo' ? (
                                  <DropdownMenuItem
                                    onSelect={() => handleStatusChange(user, 'inactivo')}
                                    className="text-orange-600"
                                  >
                                    <UserX className="mr-2 h-4 w-4" />
                                    Desactivar
                                  </DropdownMenuItem>
                                ) : (
                                  <DropdownMenuItem
                                    onSelect={() => handleStatusChange(user, 'activo')}
                                    className="text-green-600"
                                  >
                                    <UserCheck className="mr-2 h-4 w-4" />
                                    Activar
                                  </DropdownMenuItem>
                                )}
                                <DropdownMenuItem
                                  onSelect={() => handleStatusChange(user, 'bloqueado')}
                                  className="text-red-600"
                                >
                                  <Shield className="mr-2 h-4 w-4" />
                                  Bloquear
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onSelect={() => handleDelete(user)}
                                  className="text-destructive"
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Eliminar
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
              </div>

              {/* Paginación */}
              {totalPages > 1 && (
                <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                  <div className="text-sm text-gray-600 dark:text-slate-400">
                    Mostrando {users.length} de {usersData?.total} usuarios
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(filters.page! - 1)}
                      disabled={filters.page === 1}
                    >
                      Anterior
                    </Button>
                    <span className="text-sm text-gray-700 dark:text-slate-300">
                      Página {filters.page} de {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(filters.page! + 1)}
                      disabled={filters.page === totalPages}
                    >
                      Siguiente
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modal de formulario (crear/editar) */}
      <UserFormModal
        open={editModal.open}
        user={editModal.user}
                 onOpenChange={(open: boolean) => setEditModal({ open, user: editModal.user })}
         onClose={() => setEditModal({ open: false, user: null })}
       />

       {/* Modal de cambio de contraseña */}
       <PasswordChangeModal
         open={passwordModal.open}
         user={passwordModal.user}
         onOpenChange={(open: boolean) => setPasswordModal({ open, user: passwordModal.user })}
        onClose={() => setPasswordModal({ open: false, user: null })}
      />

      {/* Modal de confirmación de eliminación */}
             <AlertDialog open={deleteModal.open} onOpenChange={(open: boolean) => setDeleteModal({ open, user: deleteModal.user })}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>¿Estás seguro?</AlertDialogTitle>
            <AlertDialogDescription>
              Esta acción eliminará permanentemente al usuario <strong>{deleteModal.user?.name}</strong>.
              Esta acción no se puede deshacer.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeleteModal({ open: false, user: null })}>
              Cancelar
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteConfirm}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={deleteUserMutation.isPending}
            >
              {deleteUserMutation.isPending ? 'Eliminando...' : 'Eliminar'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      </div>
    </div>
  )
}