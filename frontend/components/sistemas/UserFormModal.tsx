"use client"

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Loader2, Eye, EyeOff, User, Mail, Phone, Building, Shield } from 'lucide-react'
import { toast } from 'sonner'
import { useUserNotifications } from '@/components/ui/notification-provider'
import {
  use<PERSON>reate<PERSON><PERSON>,
  useUpdateUser,
  useRoles
} from '@/hooks/useUsers'
import { User as UserType, CreateUserData, UpdateUserData } from '@/lib/services/user'
import { PasswordInputWithRequirements } from '@/components/ui/password-requirements'
import { validatePassword, PasswordValidationResult } from '@/lib/utils/password-policy'

// Schema de validación para el formulario
const userFormSchema = z.object({
  name: z
    .string()
    .min(2, 'El nombre debe tener al menos 2 caracteres')
    .max(100, 'El nombre no puede exceder 100 caracteres'),
  email: z
    .string()
    .email('Email inválido')
    .max(100, 'El email no puede exceder 100 caracteres'),
  phone: z
    .string()
    .regex(/^[0-9]{10}$/, 'El teléfono debe tener 10 dígitos')
    .optional()
    .or(z.literal('')),
  area: z
    .string()
    .max(100, 'El área no puede exceder 100 caracteres')
    .optional(),
  password: z.string()
    .refine((password) => {
      if (!password) return true; // Opcional para edición
      const validation = validatePassword(password);
      return validation.isValid;
    }, {
      message: 'La contraseña no cumple con las políticas de seguridad requeridas'
    })
    .optional(),
  confirmPassword: z.string().optional(),
  status: z.enum(['activo', 'inactivo', 'bloqueado']),
  roleIds: z.array(z.number()).min(1, 'Debe seleccionar al menos un rol'),
})

type UserFormData = z.infer<typeof userFormSchema>

interface UserFormModalProps {
  open: boolean
  user: UserType | null
  onOpenChange: (open: boolean) => void
  onClose: () => void
}

const areas = [
  'Sistemas',
  'Ventas', 
  'Administración',
  'Almacén',
  'Logística', 
  'Metrología',
  'Recursos Humanos',
  'Calidad'
]

export function UserFormModal({ open, user, onOpenChange, onClose }: UserFormModalProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [passwordValidation, setPasswordValidation] = useState<PasswordValidationResult>({
    isValid: false,
    errors: [],
    requirements: []
  })
  
  const isEditing = !!user
  const title = isEditing ? 'Editar Usuario' : 'Crear Nuevo Usuario'
  const description = isEditing 
    ? 'Modifica la información del usuario. Los campos marcados con * son obligatorios.'
    : 'Completa la información para crear un nuevo usuario. Los campos marcados con * son obligatorios.'

  // Hooks
  const { data: rolesData, isLoading: isLoadingRoles } = useRoles()
  // Validación defensiva para manejar estructura anidada
  const roles = Array.isArray(rolesData) ? rolesData : Array.isArray((rolesData as any)?.data) ? (rolesData as any).data : []

  // Notificaciones de usuario
  const { notifyUserCreated, notifyUserUpdated, notifyUserError } = useUserNotifications()
  
  const createUserMutation = useCreateUser()
  const updateUserMutation = useUpdateUser()

  const isLoading = createUserMutation.isPending || updateUserMutation.isPending

  // Configuración del formulario con el esquema correcto
  const form = useForm<UserFormData>({
    resolver: zodResolver(userFormSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      area: '',
      password: '',
      confirmPassword: '',
      status: 'activo',
      roleIds: [],
    },
  })

  // Efecto para reinicializar el formulario cuando cambie el modo o usuario
  useEffect(() => {
    if (isEditing && user) {
      form.reset({
        name: user.name,
        email: user.email,
        phone: user.phone || '',
        area: user.area || '',
        password: '', // No cargamos la contraseña por seguridad
        confirmPassword: '',
        status: user.status,
        roleIds: user.roles?.map(role => parseInt(role.id.toString(), 10)) || [],
      })
      // Resetear validación de contraseña para edición
      setPasswordValidation({
        isValid: false,
        errors: [],
        requirements: []
      })
    } else {
      form.reset({
        name: '',
        email: '',
        phone: '',
        area: '',
        password: '',
        confirmPassword: '',
        status: 'activo',
        roleIds: [],
      })
      // Inicializar validación de contraseña para nuevo usuario
      const initialValidation = validatePassword('')
      setPasswordValidation(initialValidation)
    }

    // Limpiar errores de validación
    form.clearErrors()
  }, [user, isEditing, form])

  // Handler para envío del formulario
  const onSubmit = async (data: UserFormData) => {
    console.log('🚀 [UserFormModal] onSubmit EJECUTADO!', data)
    console.log('[UserFormModal] isEditing:', isEditing)
    console.log('[UserFormModal] user:', user)
    console.log('[UserFormModal] data.roleIds:', data.roleIds, 'typeof:', typeof data.roleIds, 'value:', data.roleIds)
    try {
      if (isEditing && user) {
        // Aseguramos que roleIds sea un array de números
        let roleIds: number[] = Array.isArray(data.roleIds)
          ? data.roleIds.map((id: any) => parseInt(id, 10)).filter((id: any) => !isNaN(id))
          : [];
        const updateData: UpdateUserData = {
          name: data.name,
          email: data.email,
          phone: data.phone || undefined,
          area: data.area || undefined,
          status: data.status,
          roleIds,
        }
        console.log('[UserFormModal] Updating user with data (payload enviado):', updateData)
        console.log('[UserFormModal] User ID:', user.id)
        await updateUserMutation.mutateAsync({ id: user.id, data: updateData })
        console.log('[UserFormModal] Update successful')

        // Notificación de actualización exitosa
        notifyUserUpdated(data.name, `La información de ${data.name} ha sido actualizada correctamente.`)

        onClose()
        return
      } else {
        // Crear nuevo usuario - validar contraseña
        if (!data.password) {
          form.setError('password', {
            type: 'manual',
            message: 'La contraseña es obligatoria'
          })
          return
        }

        // Validar contraseña con nuestro validador
        const validation = validatePassword(data.password)
        if (!validation.isValid) {
          form.setError('password', {
            type: 'manual',
            message: `La contraseña no cumple con los requisitos: ${validation.errors.join(', ')}`
          })
          return
        }
        
        if (data.password !== data.confirmPassword) {
          form.setError('confirmPassword', { 
            type: 'manual', 
            message: 'Las contraseñas no coinciden' 
          })
          return
        }

        const createData: CreateUserData = {
          name: data.name,
          email: data.email,
          phone: data.phone || undefined,
          area: data.area || undefined,
          password: data.password,
          status: data.status,
          roleIds: data.roleIds,
        }
        
        console.log('[UserFormModal] Creating user with data:', createData)
        await createUserMutation.mutateAsync(createData)
        console.log('[UserFormModal] Create successful')

        // Notificación de creación exitosa
        notifyUserCreated(data.name, `${data.name} ha sido creado exitosamente y puede acceder al sistema.`)
      }

      onClose()
    } catch (error) {
      console.error('[UserFormModal] Error al enviar formulario:', error)
      // Notificación de error
      const userName = isEditing ? user?.name || 'Usuario' : form.getValues('name') || 'Usuario'
      notifyUserError(userName, `Error al ${isEditing ? 'actualizar' : 'crear'} el usuario. Por favor, intenta nuevamente.`)
    }
  }

  // Handler para cerrar modal
  const handleClose = () => {
    form.reset()
    onClose()
  }

  // Handler para toggle de roles
  const handleRoleToggle = (roleId: number) => {
    const currentRoles = form.getValues('roleIds')
    if (currentRoles.includes(roleId)) {
      form.setValue('roleIds', currentRoles.filter(id => id !== roleId))
    } else {
      form.setValue('roleIds', [...currentRoles, roleId])
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="w-5 h-5" />
            {title}
          </DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[60vh] pr-4">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Información Personal */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <User className="w-4 h-4" />
                  Información Personal
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nombre *</FormLabel>
                        <FormControl>
                          <Input placeholder="Nombre completo" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email *</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Mail className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                            <Input placeholder="<EMAIL>" className="pl-8" {...field} />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Teléfono</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Phone className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                            <Input placeholder="3333333333" className="pl-8" {...field} />
                          </div>
                        </FormControl>
                        <FormDescription>10 dígitos sin espacios</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="area"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Área</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Seleccionar área" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {areas.map((area) => (
                              <SelectItem key={area} value={area}>
                                {area}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Contraseña (solo para nuevos usuarios o si se quiere cambiar) */}
              {!isEditing && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Shield className="w-4 h-4" />
                    Contraseña
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Contraseña *</FormLabel>
                          <FormControl>
                            <PasswordInputWithRequirements
                              value={field.value || ''}
                              onChange={(value) => {
                                field.onChange(value)
                                // Actualizar validación en tiempo real
                                const validation = validatePassword(value)
                                setPasswordValidation(validation)
                              }}
                              requirements={passwordValidation.requirements}
                              placeholder="••••••••"
                              disabled={isLoading}
                              showRequirements={true}
                              showGenerator={true}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="confirmPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Confirmar Contraseña *</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Input 
                                type={showConfirmPassword ? "text" : "password"} 
                                placeholder="••••••••" 
                                {...field} 
                              />
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                              >
                                {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                              </Button>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              )}

              {/* Estado y Roles */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Building className="w-4 h-4" />
                  Configuración del Sistema
                </h3>
                
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Estado</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="activo">Activo</SelectItem>
                          <SelectItem value="inactivo">Inactivo</SelectItem>
                          <SelectItem value="bloqueado">Bloqueado</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="roleIds"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Roles *</FormLabel>
                      <FormDescription>
                        Selecciona uno o más roles para el usuario
                      </FormDescription>
                      {isLoadingRoles ? (
                        <div className="flex items-center gap-2">
                          <Loader2 className="w-4 h-4 animate-spin" />
                          <span>Cargando roles...</span>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {roles.map((role: any) => {
                            const roleIdNum = parseInt(role.id.toString(), 10);
                            return (
                              <div key={role.id} className="flex items-center space-x-2">
                                <Checkbox
                                  id={`role-${role.id}`}
                                  checked={field.value.includes(roleIdNum)}
                                  onCheckedChange={() => handleRoleToggle(roleIdNum)}
                                />
                                <label htmlFor={`role-${role.id}`} className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                                  {role.name}
                                </label>
                                {role.description && (
                                  <Badge variant="outline" className="text-xs">
                                    {role.description}
                                  </Badge>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Botones del formulario */}
              <div className="flex justify-end gap-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                  disabled={isLoading}
                >
                  Cancelar
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {isEditing ? 'Actualizando...' : 'Creando...'}
                    </>
                  ) : (
                    isEditing ? 'Actualizar Usuario' : 'Crear Usuario'
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
} 