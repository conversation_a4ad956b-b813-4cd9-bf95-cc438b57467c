"use client"

import { useState, useEffect } from "react"
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
  CardFooter,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"
import {
  ShieldCheck,
  PlusCircle,
  Save,
  Edit,
  Trash2,
  MoreHorizontal,
  Loader2
} from "lucide-react"
import {
  FormSection,
  FormGroup,
  CheckboxField,
  SelectableCard,
  PermissionStats,
  formStyles
} from "@/components/ui/form-styles"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { 
  useRoles, 
  usePermissionsByModule, 
  useCreateRole, 
  useUpdateRole, 
  useDeleteRole, 
  useAssignPermissions 
} from "@/hooks/useRoles"
import { Role, Permission, CreateRoleDto, UpdateRoleDto } from "@/lib/services/role"

interface RoleFormData {
  name: string
  // TODO: Uncomment when description column is added to Supabase
  // description: string
}

interface ModalState {
  isOpen: boolean
  mode: 'create' | 'edit'
  role?: Role
}

// Helper function to get readable permission labels
function getPermissionLabel(permission: any): string {
  if (permission.description && permission.description !== permission.name) {
    return permission.description
  }

  // Convert permission name to readable format
  const parts = permission.name.split(':')
  if (parts.length >= 3) {
    const [module, resource, action] = parts
    const actionLabels: Record<string, string> = {
      'create': 'Crear',
      'read': 'Ver',
      'update': 'Editar',
      'delete': 'Eliminar',
      'list': 'Listar',
      'activate': 'Activar',
      'deactivate': 'Desactivar',
      'assign': 'Asignar',
      'approve': 'Aprobar',
      'export': 'Exportar',
      'import': 'Importar'
    }

    const resourceLabels: Record<string, string> = {
      'users': 'usuarios',
      'roles': 'roles',
      'permissions': 'permisos',
      'audit': 'auditoría',
      'backup': 'respaldos',
      'stock': 'inventario',
      'products': 'productos'
    }

    const actionLabel = actionLabels[action] || action
    const resourceLabel = resourceLabels[resource] || resource

    return `${actionLabel} ${resourceLabel}`
  }

  return permission.name
}

export function RolesView() {
  console.log('[RolesView] Component rendering...')

  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [rolePermissions, setRolePermissions] = useState<Set<string>>(new Set())
  const [modalState, setModalState] = useState<ModalState>({ isOpen: false, mode: 'create' })
  const [formData, setFormData] = useState<RoleFormData>({ 
    name: ''
    // TODO: Uncomment when description column is added to Supabase
    // description: '' 
  })
  const [deleteDialog, setDeleteDialog] = useState<{ isOpen: boolean; role?: Role }>({ isOpen: false })
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  // Queries
  const { data: rolesData, isLoading: isLoadingRoles, error: rolesError } = useRoles()
  const { data: permissionsByModule = {}, isLoading: isLoadingPermissions } = usePermissionsByModule()

  // Ensure roles is always an array - handle nested data structure
  const roles = Array.isArray(rolesData) ? rolesData : Array.isArray((rolesData as any)?.data) ? (rolesData as any).data : []

  console.log('[RolesView] Raw roles data:', rolesData)
  console.log('[RolesView] Processed roles:', roles)
  console.log('[RolesView] Roles loading:', isLoadingRoles)
  console.log('[RolesView] Roles error:', rolesError)
  console.log('[RolesView] Permissions loading:', isLoadingPermissions)
  console.log('[RolesView] roles.length:', roles.length)
  console.log('[RolesView] First role:', roles[0])
  console.log('[RolesView] typeof rolesData:', typeof rolesData)
  console.log('[RolesView] Array.isArray(rolesData):', Array.isArray(rolesData))
  console.log('[RolesView] permissionsByModule:', permissionsByModule)
  console.log('[RolesView] typeof permissionsByModule:', typeof permissionsByModule)
  console.log('[RolesView] Object.keys(permissionsByModule):', Object.keys(permissionsByModule))
  console.log('[RolesView] selectedRole:', selectedRole)
  console.log('[RolesView] rolePermissions:', Array.from(rolePermissions))

  // Mutations
  const createRoleMutation = useCreateRole()
  const updateRoleMutation = useUpdateRole()
  const deleteRoleMutation = useDeleteRole()
  const assignPermissionsMutation = useAssignPermissions()

  // Effects
  useEffect(() => {
    if (!selectedRole && roles.length > 0) {
      setSelectedRole(roles[0])
    }
  }, [roles, selectedRole])

  useEffect(() => {
    if (selectedRole) {
      const currentPermissions = new Set(selectedRole.permissions?.map(p => p.name) || [])
      setRolePermissions(currentPermissions)
      setHasUnsavedChanges(false)
    }
  }, [selectedRole])

  // Handlers
  const handleSelectRole = (role: Role) => {
    if (hasUnsavedChanges) {
      // TODO: Show confirmation dialog for unsaved changes
    }
    setSelectedRole(role)
  }

  const handlePermissionChange = (permission: string) => {
    const newPermissions = new Set(rolePermissions)
    if (newPermissions.has(permission)) {
      newPermissions.delete(permission)
    } else {
      newPermissions.add(permission)
    }
    setRolePermissions(newPermissions)
    
    // Check if there are unsaved changes
    const originalPermissions = new Set(selectedRole?.permissions?.map(p => p.name) || [])
    const hasChanges = newPermissions.size !== originalPermissions.size || 
      [...newPermissions].some(p => !originalPermissions.has(p))
    setHasUnsavedChanges(hasChanges)
  }

  const handleSavePermissions = () => {
    if (selectedRole) {
      assignPermissionsMutation.mutate({
        roleId: selectedRole.id,
        permissions: Array.from(rolePermissions)
      }, {
        onSuccess: () => {
          setHasUnsavedChanges(false)
        }
      })
    }
  }

  const openCreateModal = () => {
    setFormData({ name: '' })
    setModalState({ isOpen: true, mode: 'create' })
  }

  const openEditModal = (role: Role) => {
    setFormData({ name: role.name })
    setModalState({ isOpen: true, mode: 'edit', role })
  }

  const closeModal = () => {
    setModalState({ isOpen: false, mode: 'create' })
    setFormData({ name: '' })
  }

  const handleSubmitRole = () => {
    if (!formData.name.trim()) {
      return
    }

    const roleData: CreateRoleDto | UpdateRoleDto = {
      name: formData.name.trim()
      // TODO: Uncomment when description column is added to Supabase
      // description: formData.description.trim() || undefined
    }

    if (modalState.mode === 'create') {
             createRoleMutation.mutate(roleData as CreateRoleDto, {
         onSuccess: (newRole) => {
           closeModal()
           if (newRole) setSelectedRole(newRole)
         }
       })
    } else if (modalState.mode === 'edit' && modalState.role) {
             updateRoleMutation.mutate({
         id: modalState.role.id,
         dto: roleData as UpdateRoleDto
       }, {
         onSuccess: (updatedRole) => {
           closeModal()
           if (updatedRole) setSelectedRole(updatedRole)
         }
       })
    }
  }

  const handleDeleteRole = (role: Role) => {
    setDeleteDialog({ isOpen: true, role })
  }

  const confirmDeleteRole = () => {
    if (deleteDialog.role) {
      deleteRoleMutation.mutate({
        id: deleteDialog.role.id,
        roleName: deleteDialog.role.name
      }, {
        onSuccess: () => {
          setDeleteDialog({ isOpen: false })
          if (selectedRole?.id === deleteDialog.role?.id) {
            setSelectedRole(roles.find((r: any) => r.id !== deleteDialog.role?.id) || null)
          }
        }
      })
    }
  }

  // Render loading state
  if (isLoadingRoles) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Cargando roles...</span>
        </div>
      </div>
    )
  }

  // Render error state
  if (rolesError) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <p className="text-destructive">Error al cargar roles</p>
          <p className="text-sm text-muted-foreground">{rolesError.message}</p>
        </div>
      </div>
    )
  }

  return (
    <div className={cn(
      "min-h-screen w-full transition-colors duration-500",
      "bg-gradient-to-br from-moka-peach/80 via-moka-lion/70 to-moka-brown/60",
      "dark:bg-gradient-to-br dark:from-slate-900 dark:via-blue-950 dark:to-red-950"
    )}>
      <div className="space-y-8 p-8 pt-6">
      {/* Header */}
      <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 pb-4 pt-2">
        <div className="space-y-2">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-moka-peach/30 border border-moka-brown/30 backdrop-blur-sm">
              <ShieldCheck className="h-6 w-6 text-moka-bistre dark:text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-medium tracking-tight text-moka-bistre dark:text-foreground">
                Roles y Permisos
              </h1>
            </div>
          </div>
          <p className="text-moka-brown/80 dark:text-muted-foreground leading-relaxed max-w-2xl">
            Gestiona los roles del sistema y sus permisos asociados. Define quién puede acceder a qué funcionalidades.
          </p>
        </div>
        <Button onClick={openCreateModal} size="default" className="shrink-0 bg-moka-falu hover:bg-moka-falu/90 text-white border-0">
          <PlusCircle className="mr-2 h-4 w-4" />
          Crear Nuevo Rol
        </Button>
      </header>

      <div className="grid lg:grid-cols-3 gap-8">
        {/* Roles List */}
        <Card className={cn(
          "lg:col-span-1 backdrop-blur-md border shadow-lg",
          "bg-white/60 dark:bg-slate-800/60 border-white/20 dark:border-slate-700/20"
        )}>
          <CardHeader className="pb-4">
            <CardTitle className="text-gray-900 dark:text-slate-100">Roles del Sistema</CardTitle>
            <CardDescription className="text-gray-600 dark:text-slate-400">
              {roles.length} {roles.length === 1 ? 'rol' : 'roles'} configurados
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {roles.map((role: any) => (
              <SelectableCard
                key={role.id}
                selected={selectedRole?.id === role.id}
                onClick={() => handleSelectRole(role)}
                className="relative"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0 space-y-1">
                    <p className="font-medium text-sm truncate text-gray-900 dark:text-slate-100">
                      {role.name}
                    </p>
                    <div className="flex items-center gap-2">
                      <span className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-gray-100 text-gray-800 dark:bg-slate-700 dark:text-slate-300">
                        {role.permissions?.length || 0} permisos
                      </span>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => openEditModal(role)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Editar
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDeleteRole(role)}
                        className="text-destructive"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Eliminar
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </SelectableCard>
            ))}
          </CardContent>
        </Card>

        {/* Permissions Panel */}
        <Card className={cn(
          "lg:col-span-2 max-h-[80vh] flex flex-col backdrop-blur-md border shadow-lg",
          "bg-white/60 dark:bg-slate-800/60 border-white/20 dark:border-slate-700/20"
        )}>
          {selectedRole ? (
            <>
              <CardHeader className="shrink-0 pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/50">
                      <ShieldCheck className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <CardTitle className="text-gray-900 dark:text-slate-100">
                        Permisos para: {selectedRole.name}
                      </CardTitle>
                      <CardDescription className="text-gray-600 dark:text-slate-400">
                        Gestiona los permisos para este rol
                      </CardDescription>
                    </div>
                  </div>
                  {hasUnsavedChanges && (
                    <span className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                      Cambios sin guardar
                    </span>
                  )}
                </div>
              </CardHeader>
              <div className="flex-1 min-h-0 flex flex-col">
                <CardContent className="flex-1 min-h-0 flex flex-col p-0">
                  {isLoadingPermissions ? (
                    <div className="flex items-center justify-center h-64">
                      <div className="flex items-center space-x-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span>Cargando permisos...</span>
                      </div>
                    </div>
                  ) : (
                    <ScrollArea className="flex-1 min-h-0 px-6 py-4">
                      <div className={formStyles.section}>
                        {Object.entries(permissionsByModule).map(([moduleName, permissions]) => (
                          <FormSection
                            key={moduleName}
                            title={
                              moduleName === 'sistemas' ? 'Sistema' :
                              moduleName === 'users' ? 'Usuarios' :
                              moduleName === 'roles' ? 'Roles' :
                              moduleName === 'audit' ? 'Auditoría' :
                              moduleName === 'almacen' ? 'Almacén' :
                              moduleName === 'compras' ? 'Compras' :
                              moduleName === 'quality' ? 'Calidad' :
                              moduleName.charAt(0).toUpperCase() + moduleName.slice(1)
                            }
                            description={`${permissions.filter((p: any) => rolePermissions.has(p.name)).length} de ${permissions.length} permisos seleccionados`}
                          >
                            <FormGroup className="gap-3">
                              {permissions.map((permission: any) => (
                                <CheckboxField
                                  key={permission.name}
                                  id={permission.name}
                                  checked={rolePermissions.has(permission.name)}
                                  onCheckedChange={() => handlePermissionChange(permission.name)}
                                  label={getPermissionLabel(permission)}
                                  description={permission.name}
                                />
                              ))}
                            </FormGroup>
                          </FormSection>
                        ))}
                      </div>
                    </ScrollArea>
                  )}
                </CardContent>
                <CardFooter className="shrink-0 justify-between p-6 border-t border-gray-200 dark:border-slate-700 bg-gray-50/50 dark:bg-slate-800/50">
                  <PermissionStats
                    selectedCount={Array.from(rolePermissions).length}
                    totalCount={Object.values(permissionsByModule).flat().length}
                  />
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        if (selectedRole) {
                          const originalPermissions = new Set(selectedRole.permissions?.map(p => p.name) || [])
                          setRolePermissions(originalPermissions)
                          setHasUnsavedChanges(false)
                        }
                      }}
                      disabled={!hasUnsavedChanges}
                    >
                      Descartar
                    </Button>
                    <Button
                      size="default"
                      onClick={handleSavePermissions}
                      disabled={!hasUnsavedChanges || assignPermissionsMutation.isPending}
                      className="min-w-[120px]"
                    >
                      {assignPermissionsMutation.isPending ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <Save className="mr-2 h-4 w-4" />
                      )}
                      Guardar Cambios
                    </Button>
                  </div>
                </CardFooter>
              </div>
            </>
          ) : (
            <CardContent className="flex flex-col items-center justify-center h-64 text-center space-y-4">
              <div className="p-4 rounded-full bg-gray-100 dark:bg-slate-800">
                <ShieldCheck className="h-8 w-8 text-gray-400 dark:text-slate-500" />
              </div>
              <div className="space-y-2">
                <p className="text-lg font-medium text-gray-600 dark:text-slate-400">
                  Selecciona un rol
                </p>
                <p className="text-sm text-gray-500 dark:text-slate-500 max-w-sm">
                  Elige un rol de la lista para gestionar sus permisos y configurar el acceso al sistema
                </p>
              </div>
            </CardContent>
          )}
        </Card>
      </div>

      {/* Create/Edit Role Modal */}
      <Dialog open={modalState.isOpen} onOpenChange={closeModal}>
        <DialogContent className="sm:max-w-[425px] bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700">
          <DialogHeader>
            <DialogTitle className="text-moka-bistre dark:text-foreground font-medium">
              {modalState.mode === 'create' ? 'Crear Nuevo Rol' : 'Editar Rol'}
            </DialogTitle>
            <DialogDescription className="text-moka-brown/80 dark:text-muted-foreground">
              {modalState.mode === 'create'
                ? 'Define un nuevo rol y asígnale permisos después de crearlo.'
                : 'Modifica la información del rol seleccionado.'
              }
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="role-name" className="text-moka-bistre dark:text-foreground font-normal">Nombre del Rol</Label>
              <Input
                id="role-name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Ej: ROLE_MANAGER"
                className="bg-moka-peach/20 border-moka-brown/40 text-moka-bistre placeholder:text-moka-brown/60 dark:bg-background dark:border-border dark:text-foreground"
              />
            </div>
            {/* TODO: Uncomment when description column is added to Supabase */}
            {/*
            <div className="grid gap-2">
              <Label htmlFor="role-description">Descripción</Label>
              <Textarea
                id="role-description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe las responsabilidades de este rol..."
                rows={3}
              />
            </div>
            */}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={closeModal}
              className="border-moka-brown/40 text-moka-bistre hover:bg-moka-peach/20 dark:border-border dark:text-foreground dark:hover:bg-accent"
            >
              Cancelar
            </Button>
            <Button
              onClick={handleSubmitRole}
              disabled={!formData.name.trim() || createRoleMutation.isPending || updateRoleMutation.isPending}
              className="bg-moka-falu hover:bg-moka-falu/90 text-white border-0"
            >
              {(createRoleMutation.isPending || updateRoleMutation.isPending) ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              {modalState.mode === 'create' ? 'Crear Rol' : 'Guardar Cambios'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialog.isOpen} onOpenChange={(open) => setDeleteDialog({ isOpen: open })}>
        <AlertDialogContent className="bg-moka-peach/30 border-moka-brown/40 backdrop-blur-md dark:bg-background dark:border-border">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-moka-bistre dark:text-foreground font-medium">¿Estás seguro?</AlertDialogTitle>
            <AlertDialogDescription className="text-moka-brown/80 dark:text-muted-foreground">
              Esta acción eliminará permanentemente el rol "{deleteDialog.role?.name}" y
              no podrá deshacerse. Los usuarios que tengan este rol perderán estos permisos.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="border-moka-brown/40 text-moka-bistre hover:bg-moka-peach/20 dark:border-border dark:text-foreground dark:hover:bg-accent">
              Cancelar
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteRole}
              className="bg-destructive hover:bg-destructive/90"
              disabled={deleteRoleMutation.isPending}
            >
              {deleteRoleMutation.isPending ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              Eliminar Rol
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      </div>
    </div>
  )
}