"use client"

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as Yup from 'yup'
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { userService } from '@/lib/services/user'
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { useAuth } from "@/components/providers/auth-provider"
import { loggingService } from "@/lib/utils/logging.service"

const validationSchema = Yup.object().shape({
  name: Yup.string().required('El nombre es requerido'),
  email: Yup.string().email('Email inválido').required('El email es requerido'),
  phone: Yup.string(),
  area: Yup.string(),
  password: Yup.string().min(6, 'La contraseña debe tener al menos 6 caracteres'),
  roles: Yup.array().min(1, 'Al menos un rol es requerido'),
});

interface UserEditModalProps {
  user: any
  isOpen: boolean
  onClose: () => void
  rolesList: any[]
}

export function UserEditModal({ user, isOpen, onClose, rolesList }: UserEditModalProps) {
  const { hasRole } = useAuth()
  const isAdmin = hasRole('ROLE_ADMIN')
  const queryClient = useQueryClient()
  
  const { register, handleSubmit, setValue, watch, formState: { errors }, reset } = useForm({
    resolver: yupResolver(validationSchema),
  });

  const selectedRoles = watch('roles') || []

  // Reset form when user changes
  useEffect(() => {
    if (user) {
      reset()
      setValue('name', user.name || '')
      setValue('email', user.email || '')
      setValue('phone', user.phone || '')
      setValue('area', user.area || '')
      setValue('roles', user.roles?.map((r: any) => r.name) || [])
      setValue('password', '') // Clear password field
    }
  }, [user, setValue, reset])

  const updateUserMutation = useMutation({
    mutationFn: (data: any) => {
      loggingService.info(`Updating user ${user.id}`, data)
      // Remove empty password
      if (!data.password) {
        delete data.password
      }
      return userService.updateUser(user.id, data)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      loggingService.info("User updated successfully")
      onClose()
    },
    onError: (error) => {
      loggingService.error("Failed to update user", { error })
    },
  })

  const onSubmit = (data: any) => {
    // Convertir roles (array de nombres) a roleIds (array de números)
    const roleIds = Array.isArray(data.roles)
      ? data.roles.map((r: any) => parseInt(r, 10)).filter((id: any) => !isNaN(id))
      : [];
    const updateData = {
      ...data,
      roleIds,
    };
    delete updateData.roles;
    updateUserMutation.mutate(updateData);
  }

  const handleRoleToggle = (roleName: string) => {
    const currentRoles = selectedRoles
    const newRoles = currentRoles.includes(roleName)
      ? currentRoles.filter((r: string) => r !== roleName)
      : [...currentRoles, roleName]
    setValue('roles', newRoles)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700">
        <DialogHeader>
          <DialogTitle>Editar Usuario: {user?.name}</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nombre *</Label>
              <Input
                id="name"
                {...register('name')}
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                {...register('email')}
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && (
                <p className="text-sm text-red-500">{errors.email.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Teléfono</Label>
              <Input
                id="phone"
                {...register('phone')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="area">Área</Label>
              <Input
                id="area"
                {...register('area')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Nueva Contraseña (opcional)</Label>
              <Input
                id="password"
                type="password"
                {...register('password')}
                placeholder="Dejar vacío para mantener la actual"
                className={errors.password ? 'border-red-500' : ''}
              />
              {errors.password && (
                <p className="text-sm text-red-500">{errors.password.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label>Roles *</Label>
            <div className="flex flex-wrap gap-2">
              {rolesList.map((role: any) => (
                <Badge
                  key={role.id}
                  variant={selectedRoles.includes(role.name) ? "default" : "outline"}
                  className="cursor-pointer"
                  onClick={() => handleRoleToggle(role.name)}
                >
                  {role.name}
                </Badge>
              ))}
            </div>
            {errors.roles && (
              <p className="text-sm text-red-500">{errors.roles.message}</p>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button 
              type="submit" 
              disabled={updateUserMutation.isPending}
            >
              {updateUserMutation.isPending ? 'Guardando...' : 'Guardar Cambios'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
} 