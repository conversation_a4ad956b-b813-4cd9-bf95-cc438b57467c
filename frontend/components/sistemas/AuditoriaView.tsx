"use client"

import { useState, useEffect } from "react"
import { useQuery } from "@tanstack/react-query"
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table"
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { DateRange } from "react-day-picker"
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import { cn } from "@/lib/utils"
import { Calendar as CalendarIcon, Shield } from "lucide-react"
import { auditService } from "@/lib/services/audit.service"
import { loggingService } from "@/lib/utils/logging.service"

export function AuditoriaView() {
  const [searchTerm, setSearchTerm] = useState("")
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("")
  const [date, setDate] = useState<DateRange | undefined>()

  // Función helper para formatear detalles de auditoría
  const formatAuditDetails = (details: any) => {
    if (!details) return 'Sin detalles'

    if (typeof details === 'string') {
      return details
    }

    if (typeof details === 'object') {
      // Casos especiales para mejorar la legibilidad
      if (details.email && details.loginTime) {
        return `Login: ${details.email} (${new Date(details.loginTime).toLocaleString()})`
      }

      if (details.oldValues && details.newValues) {
        return `Cambios: ${Object.keys(details.newValues).join(', ')}`
      }

      // Formato general para objetos
      return Object.entries(details)
        .map(([key, value]) => `${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}`)
        .join(', ')
    }

    return String(details)
  }

  // Función para obtener colores de badge según la acción
  const getActionBadgeColor = (action: string) => {
    switch (action) {
      case 'LOGIN':
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
      case 'CREATE':
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
      case 'UPDATE':
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
      case 'DELETE':
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-slate-600 dark:text-slate-300"
    }
  }

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchTerm])

  const { data: auditLogs = [], isLoading, error } = useQuery({
    queryKey: ['auditLogs', debouncedSearchTerm, date],
    queryFn: () => {
      const params = {
        search: debouncedSearchTerm,
        startDate: date?.from?.toISOString(),
        endDate: date?.to?.toISOString(),
      }
      loggingService.info("Fetching audit logs", params)
      return auditService.getAuditLogs(params).then(res => {
        console.log('[AuditoriaView] Raw API response:', res.data)
        console.log('[AuditoriaView] Extracted data:', res.data.data)
        console.log('[AuditoriaView] Data length:', res.data.data?.length || 0)
        return res.data.data
      })
    },
  })

  if (error) {
    loggingService.error("Error fetching audit logs", { error })
  }

  // Debug logs
  console.log('[AuditoriaView] Component state:')
  console.log('- auditLogs:', auditLogs)
  console.log('- isLoading:', isLoading)
  console.log('- error:', error)
  console.log('- auditLogs length:', auditLogs?.length || 0)

  return (
    <div className="min-h-screen bg-gradient-to-br from-moka-peach/80 via-moka-lion/70 to-moka-brown/60 dark:bg-gradient-to-br dark:from-slate-900 dark:via-blue-950 dark:to-red-950">
      <div className="space-y-6 p-6">
        <header className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-medium tracking-tight text-moka-bistre dark:text-slate-100 flex items-center gap-2">
              <Shield className="w-8 h-8" />
              Auditoría del Sistema
            </h1>
            <p className="text-moka-brown/80 dark:text-slate-400">
              Rastrea y revisa todas las acciones importantes realizadas en el sistema.
            </p>
          </div>
        </header>

        <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
          <CardHeader>
            <CardTitle className="text-gray-900 dark:text-slate-100">Registro de Auditoría</CardTitle>
            <CardDescription className="text-gray-600 dark:text-slate-400">
              Filtra los registros para encontrar eventos específicos.
            </CardDescription>
            <div className="pt-4 flex flex-col sm:flex-row gap-4">
              <Input
                placeholder="Buscar por usuario, módulo, acción..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm bg-white/50 dark:bg-slate-700/50 border-gray-200 dark:border-slate-600"
              />
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="date"
                  variant={"outline"}
                  className={cn(
                    "w-full sm:w-[300px] justify-start text-left font-normal",
                    !date && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date?.from ? (
                    date.to ? (
                      <>
                        {format(date.from, "LLL dd, y", { locale: es })} -{" "}
                        {format(date.to, "LLL dd, y", { locale: es })}
                      </>
                    ) : (
                      format(date.from, "LLL dd, y", { locale: es })
                    )
                  ) : (
                    <span>Filtrar por fecha</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  initialFocus
                  mode="range"
                  defaultMonth={date?.from}
                  selected={date}
                  onSelect={setDate}
                  numberOfMonths={2}
                />
              </PopoverContent>
            </Popover>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border border-gray-200 dark:border-slate-700 bg-white/30 dark:bg-slate-800/30">
            <Table>
              <TableHeader>
                <TableRow className="border-gray-200 dark:border-slate-700">
                  <TableHead className="hidden lg:table-cell text-gray-700 dark:text-slate-300">Fecha y Hora</TableHead>
                  <TableHead className="text-gray-700 dark:text-slate-300">Usuario</TableHead>
                  <TableHead className="text-gray-700 dark:text-slate-300">Entidad</TableHead>
                  <TableHead className="text-gray-700 dark:text-slate-300">Acción</TableHead>
                  <TableHead className="text-gray-700 dark:text-slate-300">Detalles</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow className="border-gray-200 dark:border-slate-700">
                    <TableCell colSpan={5} className="h-24 text-center text-gray-600 dark:text-slate-400">
                      Cargando...
                    </TableCell>
                  </TableRow>
                ) : error ? (
                  <TableRow className="border-gray-200 dark:border-slate-700">
                    <TableCell colSpan={5} className="h-24 text-center text-red-600 dark:text-red-400">
                      Error al cargar los registros de auditoría.
                    </TableCell>
                  </TableRow>
                ) : auditLogs.length > 0 ? (
                  auditLogs.map((log: any) => (
                    <TableRow key={log.id} className="border-gray-200 dark:border-slate-700">
                      <TableCell className="hidden lg:table-cell text-sm text-gray-600 dark:text-slate-400">
                        {format(new Date(log.created_at), "PPpp", { locale: es })}
                      </TableCell>
                      <TableCell className="font-medium text-gray-900 dark:text-slate-100">{log.user?.name || log.user?.email || 'N/A'}</TableCell>
                      <TableCell className="text-gray-600 dark:text-slate-400">{log.target_entity || 'N/A'}</TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={getActionBadgeColor(log.action)}
                        >
                          {log.action}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-sm text-gray-600 dark:text-slate-400 max-w-xs">
                        <div className="truncate" title={formatAuditDetails(log.details)}>
                          {formatAuditDetails(log.details)}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow className="border-gray-200 dark:border-slate-700">
                    <TableCell colSpan={5} className="h-24 text-center text-gray-600 dark:text-slate-400">
                      No se encontraron registros con los filtros actuales.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
      </div>
    </div>
  )
}
