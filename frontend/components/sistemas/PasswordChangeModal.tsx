"use client"

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Eye, EyeOff, Key, AlertTriangle } from 'lucide-react'
import { useAdminChangePassword } from '@/hooks/useUsers'
import { useUserNotifications } from '@/components/ui/notification-provider'
import { User, AdminChangePasswordData } from '@/lib/services/user'

// Schema de validación para el cambio de contraseña
const passwordChangeSchema = z.object({
  newPassword: z
    .string()
    .min(8, 'La contraseña debe tener al menos 8 caracteres')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^a-zA-Z0-9])/, 'La contraseña debe contener mayúscula, minúscula, número y carácter especial'),
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: 'Las contraseñas no coinciden',
  path: ['confirmPassword'],
})

type PasswordChangeData = z.infer<typeof passwordChangeSchema>

interface PasswordChangeModalProps {
  open: boolean
  user: User | null
  onOpenChange: (open: boolean) => void
  onClose: () => void
}

export function PasswordChangeModal({ open, user, onOpenChange, onClose }: PasswordChangeModalProps) {
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  // Hooks
  const changePasswordMutation = useAdminChangePassword()
  const isLoading = changePasswordMutation.isPending

  // Notificaciones de usuario
  const { notifyPasswordChanged, notifyUserError } = useUserNotifications()

  // Configuración del formulario
  const form = useForm<PasswordChangeData>({
    resolver: zodResolver(passwordChangeSchema),
    defaultValues: {
      newPassword: '',
      confirmPassword: '',
    },
  })

  // Handler para envío del formulario
  const onSubmit = async (data: PasswordChangeData) => {
    if (!user) return

    try {
      await changePasswordMutation.mutateAsync({
        id: user.id,
        data: {
          newPassword: data.newPassword,
          confirmPassword: data.confirmPassword,
        }
      })

      // Notificación de éxito
      notifyPasswordChanged(user.name, `La contraseña de ${user.name} ha sido actualizada correctamente.`)

      // Limpiar formulario y cerrar modal
      form.reset()
      onClose()
    } catch (error) {
      console.error('Error al cambiar contraseña:', error)
      // Notificación de error
      notifyUserError(user.name, 'Error al cambiar la contraseña. Por favor, intenta nuevamente.')
    }
  }

  // Handler para cerrar modal
  const handleClose = () => {
    form.reset()
    onClose()
  }

  if (!user) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Key className="w-5 h-5" />
            Cambiar Contraseña
          </DialogTitle>
          <DialogDescription>
            Cambia la contraseña del usuario <strong>{user.name}</strong> ({user.email})
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Esta acción cambiará la contraseña del usuario permanentemente. El usuario deberá usar la nueva contraseña en su próximo inicio de sesión.
            </AlertDescription>
          </Alert>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="newPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nueva Contraseña</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input 
                          type={showNewPassword ? "text" : "password"} 
                          placeholder="••••••••" 
                          {...field} 
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowNewPassword(!showNewPassword)}
                        >
                          {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </FormControl>
                    <FormDescription>
                      Mínimo 8 caracteres, incluir mayúscula, minúscula, número y carácter especial
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirmar Nueva Contraseña</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input 
                          type={showConfirmPassword ? "text" : "password"} 
                          placeholder="••••••••" 
                          {...field} 
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        >
                          {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </form>
          </Form>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancelar
          </Button>
          <Button
            type="submit"
            onClick={form.handleSubmit(onSubmit)}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Cambiando Contraseña...
              </>
            ) : (
              'Cambiar Contraseña'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 