"use client"

import { useState, useEffect } from "react"
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query"
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
  CardFooter,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"
import { 
  UserPlus, 
  Edit, 
  Trash2, 
  Shield, 
  Users, 
  PlusCircle,
  Save,
  X,
  Plus
} from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { toast } from "sonner"
import { userService } from '@/lib/services/user'
import { roleService } from "@/src/modules/systems/services/role.service"
import { useAuth } from "@/components/providers/auth-provider"
import { loggingService } from "@/src/modules/core/services/logging.service"
import { ROLES, getRoleLabel } from "@/constants/roles"

interface User {
  id: number
  name: string
  email: string
  phone: string
  status: string
  area: string
  roles: Array<{
    id: number
    name: string
    permissions: Array<{
      id: number
      name: string
    }>
  }>
  created_at: string
  updated_at: string
}

interface Role {
  id: number
  name: string
  description?: string
  permissions: Array<{
    id: number
    name: string
  }>
}

export function UsuariosView() {
  const { hasRole } = useAuth()
  const isAdmin = hasRole('ROLE_ADMIN')
  const [searchTerm, setSearchTerm] = useState("")
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("")
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [isUserModalOpen, setIsUserModalOpen] = useState(false)
  const [isRoleModalOpen, setIsRoleModalOpen] = useState(false)
  const [editingUser, setEditingUser] = useState<Partial<User>>({})
  const [selectedRoles, setSelectedRoles] = useState<number[]>([])
  const queryClient = useQueryClient()

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 300)
    return () => clearTimeout(timer)
  }, [searchTerm])

  // Query para usuarios
  const { data: usersResponse, isLoading, error } = useQuery({
    queryKey: ['users', debouncedSearchTerm],
    queryFn: () => {
      loggingService.info("Fetching users", { search: debouncedSearchTerm })
      return userService.getUsers({ search: debouncedSearchTerm })
    },
    staleTime: 5 * 60 * 1000,
  })

  // Query para roles
  const { data: roles = [], isLoading: isLoadingRoles } = useQuery({
    queryKey: ['roles'],
    queryFn: () => roleService.getRoles().then(res => res.data),
  })

  // Query para permisos
  const { data: permissions = [], isLoading: isLoadingPermissions } = useQuery({
    queryKey: ['permissions'],
    queryFn: () => roleService.getPermissions().then(res => res.data),
  })

  const users: User[] = Array.isArray(usersResponse?.users)
    ? usersResponse.users.map(u => ({
        ...u,
        phone: u.phone ?? '',
        area: u.area ?? '',
        roles: (u.roles ?? []).map(r => ({
          ...r,
          permissions: (r.permissions ?? []).map(p => ({ id: Number(p.id), name: `${p.resource}:${p.action}` }))
        }))
      }))
    : [];
  if (!Array.isArray(users)) {
    console.error('[UsuariosView] users no es array:', users);
  }

  // Mutations
  const userMutation = useMutation({
    mutationFn: async (variables: { action: string; userId?: number; data?: any }) => {
      const { action, userId, data } = variables
      switch (action) {
        case 'create':
          loggingService.info("Creating new user", data)
          return userService.createUser(data)
        case 'update':
          loggingService.info(`Updating user ${userId}`, data)
          return userService.updateUser(userId!, data)
        case 'delete':
          loggingService.info(`Deleting user ${userId}`)
          await userService.deleteUser(userId!)
          return {} as User
        case 'assignRoles':
          loggingService.info(`Assigning roles to user ${userId}`, data)
          return userService.assignRoles(userId!, data.roles)
        case 'activate':
          loggingService.info(`Activating user ${userId}`)
          return userService.updateUserStatus(userId!, 'activo')
        case 'deactivate':
          loggingService.info(`Deactivating user ${userId}`)
          return userService.updateUserStatus(userId!, 'inactivo')
        default:
          return Promise.reject(new Error("Invalid action"))
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success("Operación exitosa")
      loggingService.info("User operation successful")
      setIsUserModalOpen(false)
      setEditingUser({})
      setSelectedRoles([])
    },
    onError: (error: any) => {
      toast.error("Error en la operación", { description: error.message })
      loggingService.error("User operation failed", { error })
    },
  })

  const roleMutation = useMutation({
    mutationFn: (variables: { action: string; data?: any }) => {
      const { action, data } = variables
      switch (action) {
        case 'createRole':
          loggingService.info("Creating new role", data)
          return roleService.createRole(data)
        default:
          return Promise.reject(new Error("Invalid action"))
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] })
      toast.success("Rol creado exitosamente")
      loggingService.info("Role creation successful")
      setIsRoleModalOpen(false)
    },
    onError: (error: any) => {
      toast.error("Error al crear rol", { description: error.message })
      loggingService.error("Role creation failed", { error })
    },
  })

  // Handlers
  const handleUserAction = (action: string, user: User) => {
    loggingService.info(`Performing action: ${action} for user ID: ${user.id}`)
    
    switch (action) {
      case 'edit':
        setSelectedUser(user)
        setEditingUser({
          name: user.name,
          email: user.email,
          phone: user.phone,
          area: user.area,
          status: user.status
        })
        setSelectedRoles(user.roles.map(r => parseInt(r.id.toString(), 10)))
        setIsUserModalOpen(true)
        break
      case 'delete':
        if (window.confirm("¿Estás seguro de que quieres eliminar este usuario?")) {
          userMutation.mutate({ action, userId: user.id })
        }
        break
      case 'activate':
        userMutation.mutate({ action, userId: user.id })
        break
      case 'deactivate':
        userMutation.mutate({ action, userId: user.id })
        break
    }
  }

  const handleSaveUser = () => {
    let rolesToAssign = selectedRoles;
    // Si no hay roles seleccionados, asignar ROLE_USER por defecto
    if (!rolesToAssign || rolesToAssign.length === 0) {
      // Buscar el id del rol ROLE_USER
      const userRole = roles.find((r: Role) => r.name === ROLES.USER);
      if (userRole) {
        rolesToAssign = [userRole.id];
      }
    }
    if (selectedUser) {
      // Actualizar usuario existente
      if (!rolesToAssign || rolesToAssign.length === 0) {
        toast.error("El usuario debe tener al menos un rol asignado");
        return;
      }
      userMutation.mutate({
        action: 'update',
        userId: selectedUser.id,
        data: { ...editingUser, roleIds: selectedRoles.map(id => Number(id)) }
      })
    } else {
      // Crear nuevo usuario
      userMutation.mutate({
        action: 'create',
        data: { ...editingUser, roleIds: selectedRoles.map(id => Number(id)) }
      })
    }
  }

  const handleAssignRoles = () => {
    if (selectedUser) {
      userMutation.mutate({
        action: 'assignRoles',
        userId: selectedUser.id,
        data: { roles: selectedRoles }
      })
    }
  }

  const handleCreateRole = (roleData: { name: string; description: string }) => {
    roleMutation.mutate({
      action: 'createRole',
      data: roleData
    })
  }

  const handleRoleToggle = (roleId: number) => {
    setSelectedRoles(prev => 
      prev.includes(roleId) 
        ? prev.filter(id => id !== roleId)
        : [...prev, roleId]
    )
  }

  if (error) {
    loggingService.error("Error fetching users", { error })
  }

  return (
    <div className="space-y-6">
      <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">Gestión de Usuarios</h1>
          <p className="text-muted-foreground">
            Administra usuarios del sistema y asigna roles
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setIsRoleModalOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Crear Rol
          </Button>
          <Button onClick={() => {
            setSelectedUser(null)
            setEditingUser({})
            setSelectedRoles([])
            setIsUserModalOpen(true)
          }}>
            <Plus className="h-4 w-4 mr-2" />
            Crear Usuario
          </Button>
        </div>
      </header>

      <div className="space-y-4">
        <div className="flex gap-4">
          <div className="flex-1">
            <div className="flex gap-2 mb-4">
              <Input
                placeholder="Buscar usuarios..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm"
              />
            </div>
            <UsersList
              users={users}
              isLoading={isLoading}
              onUserAction={handleUserAction}
              onEditUser={(user) => {
                setSelectedUser(user)
                setEditingUser({
                  name: user.name,
                  email: user.email,
                  phone: user.phone,
                  area: user.area,
                  status: user.status
                })
                setSelectedRoles(user.roles.map(r => parseInt(r.id.toString(), 10)))
                setIsUserModalOpen(true)
              }}
            />
          </div>
        </div>
      </div>

      {/* Modal para crear/editar usuario */}
      <Dialog open={isUserModalOpen} onOpenChange={setIsUserModalOpen}>
        <UserDialog
          user={selectedUser}
          editingUser={editingUser}
          setEditingUser={setEditingUser}
          roles={roles}
          selectedRoles={selectedRoles}
          onRoleToggle={handleRoleToggle}
          onSave={handleSaveUser}
          isLoading={userMutation.isPending}
        />
      </Dialog>

      {/* Modal para crear rol */}
      <Dialog open={isRoleModalOpen} onOpenChange={setIsRoleModalOpen}>
        <CreateRoleDialog onCreateRole={handleCreateRole} />
      </Dialog>
    </div>
  )
}

// Componente para la lista de usuarios
function UsersList({ 
  users, 
  isLoading, 
  onUserAction, 
  onEditUser 
}: {
  users: User[]
  isLoading: boolean
  onUserAction: (action: string, user: User) => void
  onEditUser: (user: User) => void
}) {
  if (isLoading) {
    return <div className="text-center p-8">Cargando usuarios...</div>
  }

  if (users.length === 0) {
    return <div className="text-center text-muted-foreground p-8">No hay usuarios</div>
  }

  return (
    <div className="space-y-2">
      {users.map((user) => (
        <div
          key={user.id}
          className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50"
        >
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <h3 className="font-medium">{user.name}</h3>
              <Badge variant={user.status === 'activo' ? 'default' : 'secondary'}>
                {user.status}
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">{user.email}</p>
            <p className="text-sm text-muted-foreground">{user.area}</p>
            <div className="flex gap-1 mt-1 flex-wrap max-w-xs">
              {user.roles.map((role) => (
                <Badge key={role.id} variant="outline" className="text-xs mb-1">
                  {getRoleLabel(role.name)}
                </Badge>
              ))}
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => onEditUser(user)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            {user.status === 'activo' ? (
              <Button
                size="sm"
                variant="outline"
                onClick={() => onUserAction('deactivate', user)}
              >
                Desactivar
              </Button>
            ) : (
              <Button
                size="sm"
                variant="outline"
                onClick={() => onUserAction('activate', user)}
              >
                Activar
              </Button>
            )}
            <Button
              size="sm"
              variant="destructive"
              onClick={() => onUserAction('delete', user)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ))}
    </div>
  )
}

// Componente para el diálogo de usuario
function UserDialog({
  user,
  editingUser,
  setEditingUser,
  roles,
  selectedRoles,
  onRoleToggle,
  onSave,
  isLoading
}: {
  user: User | null
  editingUser: Partial<User>
  setEditingUser: (user: Partial<User>) => void
  roles: Role[]
  selectedRoles: number[]
  onRoleToggle: (roleId: number) => void
  onSave: () => void
  isLoading: boolean
}) {
  return (
    <DialogContent className="sm:max-w-[600px] bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700">
      <DialogHeader>
        <DialogTitle>
          {user ? 'Editar Usuario' : 'Crear Nuevo Usuario'}
        </DialogTitle>
        <DialogDescription>
          {user ? 'Modifica la información del usuario' : 'Completa la información del nuevo usuario'}
        </DialogDescription>
      </DialogHeader>
      <div className="grid gap-4 py-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name">Nombre</Label>
            <Input
              id="name"
              value={editingUser.name || ''}
              onChange={(e) => setEditingUser({ ...editingUser, name: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={editingUser.email || ''}
              onChange={(e) => setEditingUser({ ...editingUser, email: e.target.value })}
            />
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="phone">Teléfono</Label>
            <Input
              id="phone"
              value={editingUser.phone || ''}
              onChange={(e) => setEditingUser({ ...editingUser, phone: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="area">Área</Label>
            <Select
              value={editingUser.area || ''}
              onValueChange={(value) => setEditingUser({ ...editingUser, area: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecciona un área" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Sistemas">Sistemas</SelectItem>
                <SelectItem value="Ventas">Ventas</SelectItem>
                <SelectItem value="Almacén">Almacén</SelectItem>
                <SelectItem value="Calidad">Calidad</SelectItem>
                <SelectItem value="Metrología">Metrología</SelectItem>
                <SelectItem value="Logística">Logística</SelectItem>
                <SelectItem value="RH">RH</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="space-y-2">
          <Label>Roles</Label>
          <ScrollArea className="h-32 border rounded-md p-2">
            <div className="space-y-2">
              {roles.map((role) => (
                <div key={role.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`dialog-role-${role.id}`}
                    checked={selectedRoles.includes(Number(role.id))}
                    onCheckedChange={() => onRoleToggle(Number(role.id))}
                  />
                  <Label htmlFor={`dialog-role-${role.id}`} className="text-sm">
                    {getRoleLabel(role.name)}
                  </Label>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      </div>
      <DialogFooter>
        <Button onClick={onSave} disabled={isLoading}>
          {isLoading ? 'Guardando...' : 'Guardar'}
        </Button>
      </DialogFooter>
    </DialogContent>
  )
}

// Componente para crear roles
function CreateRoleDialog({ onCreateRole }: { onCreateRole: (data: { name: string; description: string }) => void }) {
  const [name, setName] = useState("")
  const [description, setDescription] = useState("")

  const handleSubmit = () => {
    if (!name.trim() || !description.trim()) {
      toast.error("Faltan datos", { description: "El nombre y la descripción no pueden estar vacíos." })
      return
    }
    onCreateRole({ name, description })
    setName("")
    setDescription("")
  }

  return (
    <DialogContent className="sm:max-w-[425px] bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700">
      <DialogHeader>
        <DialogTitle>Crear Nuevo Rol</DialogTitle>
        <DialogDescription>
          Define un nuevo rol para el sistema.
        </DialogDescription>
      </DialogHeader>
      <div className="grid gap-4 py-4">
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="role-name" className="text-right">
            Nombre
          </Label>
          <Input
            id="role-name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="col-span-3"
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="role-description" className="text-right">
            Descripción
          </Label>
          <Input
            id="role-description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="col-span-3"
          />
        </div>
      </div>
      <DialogFooter>
        <Button onClick={handleSubmit}>Crear Rol</Button>
      </DialogFooter>
    </DialogContent>
  )
} 