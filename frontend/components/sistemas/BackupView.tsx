"use client"

import { useState } from "react"
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query"
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
} from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  Download,
  Trash2,
  RotateCcw,
  Plus,
  Clock,
  HardDrive,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Loader2,
  Database,
  Upload
} from "lucide-react"
import {
  backupService,
  Backup,
  BackupType,
  BackupStatus,
  CreateBackupRequest,
  ScheduleBackupRequest
} from "@/lib/services/backup.service"
import { toast } from "sonner"
import { format } from "date-fns"
import { authHeader } from "@/lib/services/auth-header"
import { es } from "date-fns/locale"
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue
} from "@/components/ui/select"

export function BackupView() {
  const queryClient = useQueryClient()
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false)
  const [newBackup, setNewBackup] = useState<CreateBackupRequest>({ name: "", description: "" })
  const [scheduledBackup, setScheduledBackup] = useState<ScheduleBackupRequest>({ name: '', frequency: 'daily', description: '' })
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false)
  const [uploadedFile, setUploadedFile] = useState<{
    fileName: string;
    fileSize: number;
    filePath: string;
    validation: any;
  } | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [isExecuting, setIsExecuting] = useState(false)

  // Fetch backups
  const { data: backupsResponse, isLoading, error } = useQuery({
    queryKey: ['backups'],
    queryFn: () => backupService.getAllBackups(),
    staleTime: 30 * 1000, // 30 seconds
  })

  // Fetch stats
  const { data: statsResponse } = useQuery({
    queryKey: ['backup-stats'],
    queryFn: () => backupService.getBackupStats(),
    staleTime: 60 * 1000, // 1 minute
  })

  const backups = backupsResponse?.data || []
  const stats = statsResponse?.data

  // Create backup mutation
  const createBackupMutation = useMutation({
    mutationFn: (data: CreateBackupRequest) => backupService.createBackup(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['backups'] })
      queryClient.invalidateQueries({ queryKey: ['backup-stats'] })
      setIsCreateDialogOpen(false)
      setNewBackup({ name: "", description: "" })
      toast.success("Backup creado", {
        description: "El backup se está creando en segundo plano.",
      })
    },
    onError: (error: any) => {
      toast.error("Error al crear el backup", {
        description: error.response?.data?.message || "Error al crear el backup",
      })
    },
  })

  // Schedule backup mutation
  const scheduleBackupMutation = useMutation({
    mutationFn: (data: ScheduleBackupRequest) => backupService.scheduleBackup(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['backups'] })
      setIsScheduleDialogOpen(false)
      setScheduledBackup({ name: '', frequency: 'daily', description: '' })
      toast.success('Backup programado', {
        description: 'El backup se ha programado correctamente.',
      })
    },
    onError: (error: any) => {
      toast.error('Error al programar el backup', {
        description: error.response?.data?.message || 'Error al programar el backup',
      })
    },
  })

  // Delete backup mutation
  const deleteBackupMutation = useMutation({
    mutationFn: (id: number) => backupService.deleteBackup(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['backups'] })
      queryClient.invalidateQueries({ queryKey: ['backup-stats'] })
      toast.success("Backup eliminado", {
        description: "El backup se ha eliminado correctamente.",
      })
    },
    onError: (error: any) => {
      toast.error("Error al eliminar el backup", {
        description: error.response?.data?.message || "Error al eliminar el backup",
      })
    },
  })

  // Función para manejar la subida de archivos
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsUploading(true)
    try {
      const formData = new FormData()
      formData.append('backupFile', file)

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api'}/systems/backups/upload`, {
        method: 'POST',
        headers: {
          ...authHeader(),
        },
        body: formData,
      })

      const result = await response.json()

      if (response.ok) {
        setUploadedFile({
          fileName: result.fileName,
          fileSize: result.fileSize,
          filePath: result.filePath,
          validation: result.validation,
        })
        toast.success("Archivo validado", {
          description: `${result.fileName} ha sido validado correctamente.`,
        })
      } else {
        throw new Error(result.message || 'Error al subir archivo')
      }
    } catch (error: any) {
      console.error('Error uploading file:', error)
      toast.error("Error al subir archivo", {
        description: error.message,
      })
    } finally {
      setIsUploading(false)
    }
  }

  // Función para ejecutar backup subido
  const handleExecuteUploadedBackup = async () => {
    if (!uploadedFile) return

    const confirmed = window.confirm(
      '⚠️ ADVERTENCIA: Esta acción restaurará la base de datos desde el archivo subido.\n\n' +
      '• Se creará un backup de seguridad automáticamente\n' +
      '• Los datos actuales serán reemplazados\n' +
      '• Esta acción no se puede deshacer\n\n' +
      '¿Estás seguro de que quieres continuar?'
    )

    if (!confirmed) return

    setIsExecuting(true)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api'}/systems/backups/execute-uploaded`, {
        method: 'POST',
        headers: {
          ...authHeader(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          filePath: uploadedFile.filePath,
          confirm: true,
        }),
      })

      const result = await response.json()

      if (response.ok) {
        toast.success("Backup ejecutado", {
          description: "La base de datos ha sido restaurada correctamente.",
        })
        setUploadedFile(null)
        setIsUploadDialogOpen(false)
        queryClient.invalidateQueries({ queryKey: ['backups'] })
      } else {
        throw new Error(result.message || 'Error al ejecutar backup')
      }
    } catch (error: any) {
      console.error('Error executing backup:', error)
      toast.error("Error al ejecutar backup", {
        description: error.message,
      })
    } finally {
      setIsExecuting(false)
    }
  }

  // Restore backup mutation
  const restoreBackupMutation = useMutation({
    mutationFn: (id: number) => backupService.restoreBackup(id),
    onSuccess: () => {
      toast.success("Backup restaurado", {
        description: "El backup se ha restaurado correctamente.",
      })
    },
    onError: (error: any) => {
      toast.error("Error al restaurar el backup", {
        description: error.response?.data?.message || "Error al restaurar el backup",
      })
    },
  })

  // Download backup
  const handleDownload = async (backup: Backup) => {
    try {
      const response = await backupService.downloadBackup(backup.id)
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `backup-${backup.id}-${backup.name}.sql`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)
      
      toast.success("Descarga iniciada", {
        description: "El archivo de backup se está descargando.",
      })
    } catch (error: any) {
      toast.error("Error al descargar el backup", {
        description: "Error al descargar el backup",
      })
    }
  }

  // Cleanup expired backups
  const cleanupMutation = useMutation({
    mutationFn: () => backupService.cleanupExpiredBackups(),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['backups'] })
      queryClient.invalidateQueries({ queryKey: ['backup-stats'] })
      toast.success("Limpieza completada", {
        description: `Se eliminaron ${response.data.deletedCount} backups expirados.`,
      })
    },
    onError: (error: any) => {
      toast.error("Error al limpiar backups expirados", {
        description: error.response?.data?.message || "Error al limpiar backups expirados",
      })
    },
  })

  const getStatusIcon = (status: BackupStatus) => {
    switch (status) {
      case BackupStatus.COMPLETED:
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case BackupStatus.FAILED:
        return <XCircle className="h-4 w-4 text-red-500" />
      case BackupStatus.IN_PROGRESS:
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
      default:
        return <Clock className="h-4 w-4 text-yellow-500" />
    }
  }

  const getStatusBadge = (status: BackupStatus) => {
    switch (status) {
      case BackupStatus.COMPLETED:
        return <Badge variant="default" className="bg-green-100 text-green-800">Completado</Badge>
      case BackupStatus.FAILED:
        return <Badge variant="destructive">Fallido</Badge>
      case BackupStatus.IN_PROGRESS:
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">En Progreso</Badge>
      default:
        return <Badge variant="outline">Pendiente</Badge>
    }
  }

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return "N/A"
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  if (error) {
    return (
      <div className="text-center text-red-600 p-8">
        Error al cargar los backups: {error.message}
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-moka-peach/80 via-moka-lion/70 to-moka-brown/60 dark:bg-gradient-to-br dark:from-slate-900 dark:via-blue-950 dark:to-red-950">
      <div className="space-y-6 p-6">
      <header className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-medium tracking-tight text-moka-bistre dark:text-slate-100 flex items-center gap-2">
            <Database className="w-8 h-8" />
            Backup y Restauración
          </h1>
          <p className="text-moka-brown/80 dark:text-slate-400">
            Gestiona los backups de la base de datos del sistema.
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => cleanupMutation.mutate()} disabled={cleanupMutation.isPending} className="border-moka-brown/40 text-moka-bistre hover:bg-moka-peach/20 dark:border-border dark:text-foreground dark:hover:bg-accent">
            {cleanupMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Limpiar Expirados
          </Button>
          <Dialog open={isScheduleDialogOpen} onOpenChange={setIsScheduleDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="border-moka-brown/40 text-moka-bistre hover:bg-moka-peach/20 dark:border-border dark:text-foreground dark:hover:bg-accent">
                <Clock className="mr-2 h-4 w-4" />
                Programar
              </Button>
            </DialogTrigger>
            <DialogContent className="bg-moka-peach/30 border-moka-brown/40 backdrop-blur-md dark:bg-background dark:border-border">
              <DialogHeader>
                <DialogTitle className="text-moka-bistre dark:text-foreground font-medium">Programar Backup Automático</DialogTitle>
                <DialogDescription className="text-moka-brown/80 dark:text-muted-foreground">
                  Programa un backup automático usando una expresión cron.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="schedule-name" className="text-moka-bistre dark:text-foreground font-normal">Nombre</Label>
                  <Input
                    id="schedule-name"
                    value={scheduledBackup.name}
                    onChange={(e) => setScheduledBackup({ ...scheduledBackup, name: e.target.value })}
                    placeholder="Backup diario"
                    className="bg-moka-peach/20 border-moka-brown/40 text-moka-bistre placeholder:text-moka-brown/60 dark:bg-background dark:border-border dark:text-foreground"
                  />
                </div>
                <div>
                  <Label htmlFor="frequency" className="text-moka-bistre dark:text-foreground font-normal">Frecuencia</Label>
                  <Select
                    value={scheduledBackup.frequency}
                    onValueChange={value => setScheduledBackup({ ...scheduledBackup, frequency: value as 'daily' | 'weekly' | 'monthly' })}
                  >
                    <SelectTrigger id="frequency" className="w-full bg-moka-peach/20 border-moka-brown/40 text-moka-bistre dark:bg-background dark:border-border dark:text-foreground" >
                      <SelectValue placeholder="Selecciona frecuencia" />
                    </SelectTrigger>
                    <SelectContent className="bg-moka-peach/90 dark:bg-background border-moka-brown/40 dark:border-border backdrop-blur-md">
                      <SelectItem value="daily">Diario (todos los días a las 2:00 AM)</SelectItem>
                      <SelectItem value="weekly">Semanal (domingo a las 2:00 AM)</SelectItem>
                      <SelectItem value="monthly">Mensual (día 1 a las 2:00 AM)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="schedule-description" className="text-moka-bistre dark:text-foreground font-normal">Descripción</Label>
                  <Textarea
                    id="schedule-description"
                    value={scheduledBackup.description}
                    onChange={(e) => setScheduledBackup({ ...scheduledBackup, description: e.target.value })}
                    placeholder="Descripción opcional"
                    className="bg-moka-peach/20 border-moka-brown/40 text-moka-bistre placeholder:text-moka-brown/60 dark:bg-background dark:border-border dark:text-foreground"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsScheduleDialogOpen(false)} className="border-moka-brown/40 text-moka-bistre hover:bg-moka-peach/20 dark:border-border dark:text-foreground dark:hover:bg-accent">
                  Cancelar
                </Button>
                <Button onClick={() => scheduleBackupMutation.mutate(scheduledBackup)} disabled={scheduleBackupMutation.isPending} className="bg-moka-falu hover:bg-moka-falu/90 text-white border-0">
                  {scheduleBackupMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Programar
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-moka-falu hover:bg-moka-falu/90 text-white border-0">
                <Plus className="mr-2 h-4 w-4" />
                Crear Backup
              </Button>
            </DialogTrigger>
            <DialogContent className="bg-moka-peach/30 border-moka-brown/40 backdrop-blur-md dark:bg-background dark:border-border">
              <DialogHeader>
                <DialogTitle className="text-moka-bistre dark:text-foreground font-medium">Crear Backup Manual</DialogTitle>
                <DialogDescription className="text-moka-brown/80 dark:text-muted-foreground">
                  Crea un backup manual de la base de datos del sistema.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="backup-name" className="text-moka-bistre dark:text-foreground font-normal">Nombre</Label>
                  <Input
                    id="backup-name"
                    value={newBackup.name}
                    onChange={(e) => setNewBackup({ ...newBackup, name: e.target.value })}
                    placeholder="Backup manual"
                    className="bg-moka-peach/20 border-moka-brown/40 text-moka-bistre placeholder:text-moka-brown/60 dark:bg-background dark:border-border dark:text-foreground"
                  />
                </div>
                <div>
                  <Label htmlFor="backup-description" className="text-moka-bistre dark:text-foreground font-normal">Descripción</Label>
                  <Textarea
                    id="backup-description"
                    value={newBackup.description}
                    onChange={(e) => setNewBackup({ ...newBackup, description: e.target.value })}
                    placeholder="Descripción opcional"
                    className="bg-moka-peach/20 border-moka-brown/40 text-moka-bistre placeholder:text-moka-brown/60 dark:bg-background dark:border-border dark:text-foreground"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)} className="border-moka-brown/40 text-moka-bistre hover:bg-moka-peach/20 dark:border-border dark:text-foreground dark:hover:bg-accent">
                  Cancelar
                </Button>
                <Button onClick={() => createBackupMutation.mutate(newBackup)} disabled={createBackupMutation.isPending} className="bg-moka-falu hover:bg-moka-falu/90 text-white border-0">
                  {createBackupMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Crear
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Botón de Subir Backup */}
          <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="border-moka-brown/40 text-moka-bistre hover:bg-moka-peach/20 dark:border-border dark:text-foreground dark:hover:bg-accent">
                <Upload className="mr-2 h-4 w-4" />
                Subir Backup
              </Button>
            </DialogTrigger>
            <DialogContent className="bg-moka-peach/30 border-moka-brown/40 backdrop-blur-md dark:bg-background dark:border-border max-w-2xl">
              <DialogHeader>
                <DialogTitle className="text-moka-bistre dark:text-foreground font-medium">Subir y Ejecutar Backup</DialogTitle>
                <DialogDescription className="text-moka-brown/80 dark:text-muted-foreground">
                  <div className="space-y-2">
                    <p>Sube un archivo de backup local para validar y ejecutar en la base de datos.</p>
                    <div className="bg-red-50 dark:bg-red-950/30 p-3 rounded-lg border border-red-200 dark:border-red-800/30">
                      <p className="text-sm font-medium text-red-800 dark:text-red-200 mb-1">⚠️ Advertencia de Seguridad:</p>
                      <ul className="text-xs text-red-700 dark:text-red-300 space-y-1">
                        <li>• Solo archivos .sql de backups de COMINTEC</li>
                        <li>• Se validará la estructura y contenido</li>
                        <li>• Se creará backup de seguridad antes de ejecutar</li>
                        <li>• Esta acción reemplazará los datos actuales</li>
                      </ul>
                    </div>
                  </div>
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4">
                {!uploadedFile ? (
                  <div className="space-y-4">
                    <div className="border-2 border-dashed border-moka-brown/30 dark:border-slate-600 rounded-lg p-6 text-center">
                      <input
                        type="file"
                        accept=".sql"
                        onChange={handleFileUpload}
                        disabled={isUploading}
                        className="hidden"
                        id="backup-file-input"
                      />
                      <label
                        htmlFor="backup-file-input"
                        className="cursor-pointer flex flex-col items-center space-y-2"
                      >
                        <Upload className="h-8 w-8 text-moka-brown/60 dark:text-slate-400" />
                        <span className="text-sm text-moka-brown/80 dark:text-slate-300">
                          {isUploading ? 'Validando archivo...' : 'Seleccionar archivo .sql'}
                        </span>
                        <span className="text-xs text-moka-brown/60 dark:text-slate-500">
                          Máximo 100MB
                        </span>
                      </label>
                    </div>
                    {isUploading && (
                      <div className="flex items-center justify-center">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        <span className="text-sm">Validando archivo...</span>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="bg-green-50 dark:bg-green-950/30 p-4 rounded-lg border border-green-200 dark:border-green-800/30">
                      <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">✅ Archivo Validado</h4>
                      <div className="space-y-2 text-sm">
                        <p><strong>Archivo:</strong> {uploadedFile.fileName}</p>
                        <p><strong>Tamaño:</strong> {(uploadedFile.fileSize / 1024 / 1024).toFixed(2)} MB</p>

                        {uploadedFile.validation && (
                          <div className="mt-3">
                            <p className="font-medium mb-1">Validaciones:</p>
                            <div className="grid grid-cols-2 gap-2 text-xs">
                              {uploadedFile.validation.sqlFormat && (
                                <span className="text-green-600 dark:text-green-400">✓ Formato SQL válido</span>
                              )}
                              {uploadedFile.validation.comintecFormat && (
                                <span className="text-green-600 dark:text-green-400">✓ Backup de COMINTEC</span>
                              )}
                              {uploadedFile.validation.securityCheck && (
                                <span className="text-green-600 dark:text-green-400">✓ Seguridad aprobada</span>
                              )}
                              {uploadedFile.validation.cleanFormat && (
                                <span className="text-green-600 dark:text-green-400">✓ Formato limpio</span>
                              )}
                              {uploadedFile.validation.insertStatements && (
                                <span className="text-blue-600 dark:text-blue-400">📊 {uploadedFile.validation.insertStatements} registros</span>
                              )}
                              {uploadedFile.validation.totalTables && (
                                <span className="text-blue-600 dark:text-blue-400">📋 {uploadedFile.validation.totalTables} tablas</span>
                              )}
                              {uploadedFile.validation.criticalTablesFound && (
                                <span className="text-green-600 dark:text-green-400">🔑 {uploadedFile.validation.criticalTablesFound.length}/3 tablas críticas</span>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        onClick={() => setUploadedFile(null)}
                        className="flex-1"
                      >
                        Subir Otro Archivo
                      </Button>
                      <Button
                        onClick={handleExecuteUploadedBackup}
                        disabled={isExecuting}
                        className="flex-1 bg-red-600 hover:bg-red-700 text-white"
                      >
                        {isExecuting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        {isExecuting ? 'Ejecutando...' : 'Ejecutar Backup'}
                      </Button>
                    </div>
                  </div>
                )}
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => {
                  setIsUploadDialogOpen(false)
                  setUploadedFile(null)
                }} className="border-moka-brown/40 text-moka-bistre hover:bg-moka-peach/20 dark:border-border dark:text-foreground dark:hover:bg-accent">
                  Cerrar
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </header>

      {/* Stats Cards */}
      {stats && (
        <div className="grid md:grid-cols-4 gap-4">
          <Card className="bg-moka-peach/20 dark:bg-slate-800/60 backdrop-blur-md border border-moka-brown/40 dark:border-slate-700/20">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-moka-brown/80 dark:text-slate-400">Total Backups</p>
                  <p className="text-2xl font-medium text-moka-bistre dark:text-slate-100">{stats.total}</p>
                </div>
                <HardDrive className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              </div>
            </CardContent>
          </Card>
          <Card className="bg-moka-peach/20 dark:bg-slate-800/60 backdrop-blur-md border border-moka-brown/40 dark:border-slate-700/20">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-moka-brown/80 dark:text-slate-400">Completados</p>
                  <p className="text-2xl font-medium text-green-600 dark:text-green-400">{stats.completed}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
            </CardContent>
          </Card>
          <Card className="bg-moka-peach/20 dark:bg-slate-800/60 backdrop-blur-md border border-moka-brown/40 dark:border-slate-700/20">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-moka-brown/80 dark:text-slate-400">Fallidos</p>
                  <p className="text-2xl font-medium text-red-600 dark:text-red-400">{stats.failed}</p>
                </div>
                <XCircle className="h-8 w-8 text-red-600 dark:text-red-400" />
              </div>
            </CardContent>
          </Card>
          <Card className="bg-moka-peach/20 dark:bg-slate-800/60 backdrop-blur-md border border-moka-brown/40 dark:border-slate-700/20">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-moka-brown/80 dark:text-slate-400">Tamaño Total</p>
                  <p className="text-2xl font-medium text-purple-600 dark:text-purple-400">{formatFileSize(stats.totalSize)}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-purple-600 dark:text-purple-400" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Backups Table */}
      <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
        <CardHeader>
          <CardTitle className="text-gray-900 dark:text-slate-100">Lista de Backups</CardTitle>
          <CardDescription className="text-gray-600 dark:text-slate-400">
            Gestiona todos los backups del sistema.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <Loader2 className="h-8 w-8 animate-spin text-gray-600 dark:text-slate-400" />
              <span className="ml-2 text-gray-600 dark:text-slate-400">Cargando backups...</span>
            </div>
          ) : backups.length === 0 ? (
            <div className="text-center text-gray-500 dark:text-slate-400 p-8">
              No hay backups disponibles.
            </div>
          ) : (
            <div className="rounded-md border border-gray-200 dark:border-slate-700 bg-white/30 dark:bg-slate-800/30">
              <Table>
                <TableHeader>
                  <TableRow className="border-gray-200 dark:border-slate-700">
                    <TableHead className="text-gray-700 dark:text-slate-300">Estado</TableHead>
                    <TableHead className="text-gray-700 dark:text-slate-300">Nombre</TableHead>
                    <TableHead className="text-gray-700 dark:text-slate-300">Tipo</TableHead>
                    <TableHead className="text-gray-700 dark:text-slate-300">Tamaño</TableHead>
                    <TableHead className="text-gray-700 dark:text-slate-300">Creado</TableHead>
                    <TableHead className="text-gray-700 dark:text-slate-300">Acciones</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {backups.map((backup: Backup) => (
                    <TableRow key={backup.id} className="border-gray-200 dark:border-slate-700">
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(backup.status)}
                          {getStatusBadge(backup.status)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium text-gray-900 dark:text-slate-100">{backup.name}</p>
                          {backup.description && (
                            <p className="text-sm text-gray-600 dark:text-slate-400">{backup.description}</p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="bg-gray-100 text-gray-800 dark:bg-slate-600 dark:text-slate-300">
                          {backup.type === BackupType.MANUAL && "Manual"}
                          {backup.type === BackupType.AUTOMATIC && "Automático"}
                          {backup.type === BackupType.SCHEDULED && "Programado"}
                        </Badge>
                      </TableCell>
                      <TableCell>{formatFileSize(backup.fileSize)}</TableCell>
                      <TableCell>
                        {format(new Date(backup.createdAt), "dd/MM/yyyy HH:mm", { locale: es })}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          {backup.status === BackupStatus.COMPLETED && (
                            <>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDownload(backup)}
                              >
                                <Download className="h-4 w-4" />
                              </Button>
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button size="sm" variant="outline">
                                    <RotateCcw className="h-4 w-4" />
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>¿Restaurar backup?</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      Esta acción restaurará la base de datos al estado del backup "{backup.name}". 
                                      Se creará un backup de seguridad antes de la restauración.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => restoreBackupMutation.mutate(backup.id)}
                                      className="bg-red-600 hover:bg-red-700"
                                    >
                                      Restaurar
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </>
                          )}
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button size="sm" variant="outline" className="text-red-600">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>¿Eliminar backup?</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Esta acción eliminará permanentemente el backup "{backup.name}".
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => deleteBackupMutation.mutate(backup.id)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  Eliminar
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
      </div>
    </div>
  )
}