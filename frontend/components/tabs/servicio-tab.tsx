"use client"
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Wrench, Clock, Users, FileText, Package, Calendar } from "lucide-react"

export function ServicioTab() {
  const servicioItems = [
    { titulo: "Solicitudes de Servicio", descripcion: "Gestionar solicitudes", icono: Wrench },
    { titulo: "Historial de Servicios", descripcion: "Historial completo", icono: Clock },
    { titulo: "Gestión de Técnicos", descripcion: "Administrar técnicos", icono: Users },
    { titulo: "Reportes de Servicio", descripcion: "Informes de servicio", icono: FileText },
    { titulo: "Inventario de Herramientas", descripcion: "Control de herramientas", icono: Package },
    { titulo: "Calendario de Servicios", descripcion: "Programación de servicios", icono: Calendar },
  ]

  const servicios = [
    { id: "SRV-001", cliente: "Empresa ABC", tecnico: "<PERSON> Pérez", fecha: "2024-01-20", estado: "En Proceso" },
    { id: "SRV-002", cliente: "Corporativo XYZ", tecnico: "María García", fecha: "2024-01-22", estado: "Programado" },
    { id: "SRV-003", cliente: "Industrias DEF", tecnico: "Carlos López", fecha: "2024-01-18", estado: "Completado" },
  ]

  return (
    <div className="space-y-6">
      <section className="p-4">
        <h2 className="text-lg font-semibold mb-2">Módulo de Servicio</h2>
        <p className="text-sm text-muted-foreground">Solicitudes de servicio, técnicos y calendario de servicios.</p>
      </section>

      <div className="grid md:grid-cols-4 gap-4">
        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Servicios Activos</p>
                <p className="text-2xl font-bold text-blue-600">15</p>
              </div>
              <Wrench className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Técnicos Disponibles</p>
                <p className="text-2xl font-bold text-green-600">8</p>
              </div>
              <Users className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Servicios Hoy</p>
                <p className="text-2xl font-bold text-orange-600">5</p>
              </div>
              <Calendar className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Herramientas</p>
                <p className="text-2xl font-bold text-purple-600">127</p>
              </div>
              <Package className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        <Card className="border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Servicios Recientes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {servicios.map((servicio) => (
                <div key={servicio.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">{servicio.id}</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{servicio.cliente}</p>
                      <p className="text-xs text-gray-500">Técnico: {servicio.tecnico}</p>
                      <p className="text-xs text-gray-500">{servicio.fecha}</p>
                    </div>
                    <Badge
                      variant="secondary"
                      className={
                        servicio.estado === "Completado"
                          ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                          : servicio.estado === "En Proceso"
                            ? "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
                            : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"
                      }
                    >
                      {servicio.estado}
                    </Badge>
                  </div>
                  <div className="flex space-x-2 mt-3">
                    <Button variant="outline" size="sm">
                      Ver Detalles
                    </Button>
                    <Button variant="outline" size="sm">
                      Editar
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Herramientas de Servicio</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {servicioItems.map((item, index) => (
                <div
                  key={index}
                  className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow"
                >
                  <div className="flex items-center space-x-2 mb-2">
                    <item.icono className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    <h4 className="font-medium text-sm text-gray-900 dark:text-gray-100">{item.titulo}</h4>
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">{item.descripcion}</p>
                  <Button variant="outline" size="sm" className="w-full text-xs">
                    Acceder
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
