"use client"
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Megaphone, Users, BarChart3, Mail, Share2, FileText } from "lucide-react"

export function MarketingTab() {
  const marketingItems = [
    { titulo: "Campañas de Marketing", descripcion: "Gestionar campañas publicitarias", ref: "044", icono: Megaphone },
    { titulo: "Gestión de Leads", descripcion: "Administrar prospectos", ref: "045", icono: Users },
    { titulo: "Análisis de Marketing", descripcion: "Métricas y análisis", ref: "046", icono: BarChart3 },
    { titulo: "Email Marketing", descripcion: "Campañas por correo", ref: "047", icono: Mail },
    { titulo: "Social Media", descripcion: "Redes sociales", ref: "048", icono: Share2 },
    { titulo: "Reportes de Marketing", descripcion: "Informes de rendimiento", ref: "049", icono: FileText },
  ]

  return (
    <div className="space-y-6">
      <section className="p-4">
        <h2 className="text-lg font-semibold mb-2">Módulo de Marketing</h2>
        <p className="text-sm text-muted-foreground">Campañas, gestión de leads y reportes de marketing.</p>
      </section>

      <div className="grid md:grid-cols-4 gap-4">
        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Campañas Activas</p>
                <p className="text-2xl font-bold text-purple-600">12</p>
              </div>
              <Megaphone className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Leads Generados</p>
                <p className="text-2xl font-bold text-green-600">156</p>
              </div>
              <Users className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Emails Enviados</p>
                <p className="text-2xl font-bold text-blue-600">2,340</p>
              </div>
              <Mail className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Conversiones</p>
                <p className="text-2xl font-bold text-orange-600">23</p>
              </div>
              <BarChart3 className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">Herramientas de Marketing</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {marketingItems.map((item, index) => (
              <div
                key={index}
                className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow"
              >
                <div className="flex items-start space-x-3">
                  <div className="p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <item.icono className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">{item.titulo}</h4>
                      <Badge variant="secondary" className="text-xs">
                        Ref: {item.ref}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{item.descripcion}</p>
                    <Button variant="outline" size="sm" className="w-full">
                      Acceder
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
