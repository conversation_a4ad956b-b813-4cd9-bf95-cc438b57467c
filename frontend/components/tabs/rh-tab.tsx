import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Calendar, UserCheck, FolderKanban, AlertTriangle, BookOpen, Users, FileText } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

// --- Simulación de API y Tipos de Datos para RH ---

interface Capacitacion {
  id: string;
  nombre: string;
  progreso: number;
  estado: "Completada" | "En Progreso" | "Pendiente";
}

interface Seguimiento {
  id: string;
  empleado: string;
  fechaReunion: string;
  objetivo: string;
}

interface Expediente {
  id: string;
  empleado: string;
  tipo: "Contractual" | "Laboral";
  documentos: number;
}

interface RhData {
  capacitaciones: Capacitacion[];
  seguimientos: Seguimiento[];
  expedientes: Expediente[];
}

const fetchRhData = async (): Promise<RhData> => {
  console.log("Fetching RH data...");
  await new Promise(resolve => setTimeout(resolve, 1200));
  return {
    capacitaciones: [
      { id: "CAP-01", nombre: "Seguridad Industrial", progreso: 100, estado: "Completada" },
      { id: "CAP-02", nombre: "Uso de Software CRM", progreso: 75, estado: "En Progreso" },
      { id: "CAP-03", nombre: "Norma ISO 9001", progreso: 20, estado: "En Progreso" },
      { id: "CAP-04", nombre: "Primeros Auxilios", progreso: 0, estado: "Pendiente" },
    ],
    seguimientos: [
      { id: "SEG-01", empleado: "Ana Torres", fechaReunion: "2024-07-15", objetivo: "Revisión de metas Q2" },
      { id: "SEG-02", empleado: "Luis Campos", fechaReunion: "2024-07-20", objetivo: "Plan de desarrollo profesional" },
    ],
    expedientes: [
      { id: "EXP-01", empleado: "Ana Torres", tipo: "Contractual", documentos: 5 },
      { id: "EXP-02", empleado: "Luis Campos", tipo: "Laboral", documentos: 8 },
    ],
  };
};

const useRhData = () => {
  return useQuery<RhData, Error>({
    queryKey: ["rhData"],
    queryFn: fetchRhData,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

const RhTabSkeleton = () => (
  <div className="space-y-6">
    <div className="grid lg:grid-cols-3 gap-6">
      <Card className="border-0 shadow-sm">
        <CardHeader><Skeleton className="h-8 w-3/4" /></CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-20 w-full" />
          <Skeleton className="h-20 w-full" />
        </CardContent>
      </Card>
      <Card className="border-0 shadow-sm">
        <CardHeader><Skeleton className="h-8 w-3/4" /></CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-20 w-full" />
          <Skeleton className="h-20 w-full" />
        </CardContent>
      </Card>
      <Card className="border-0 shadow-sm">
        <CardHeader><Skeleton className="h-8 w-3/4" /></CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-20 w-full" />
          <Skeleton className="h-20 w-full" />
        </CardContent>
      </Card>
    </div>
  </div>
);

export function RhTab() {
  const { data, isLoading, isError, error } = useRhData();

  if (isLoading) {
    return <RhTabSkeleton />;
  }

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center h-64 bg-red-50 text-red-700 rounded-lg">
        <AlertTriangle className="h-12 w-12 mb-4" />
        <h2 className="text-xl font-semibold">Error al cargar datos de RH</h2>
        <p>{error.message}</p>
      </div>
    );
  }

  if (!data) {
    return null;
  }

  const { capacitaciones, seguimientos, expedientes } = data;

  return (
    <div className="space-y-6 p-6">
      {/* Header Section */}
      <header className="space-y-2 mb-8">
        <h1 className="text-3xl font-medium tracking-tight text-gray-900 dark:text-slate-100">
          Recursos Humanos
        </h1>
        <p className="text-gray-600 dark:text-slate-400">
          Gestión de capacitaciones, seguimiento de personal y expedientes digitales.
        </p>
      </header>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* Capacitación (RFP) */}
        <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg font-medium flex items-center text-gray-900 dark:text-slate-100">
                <BookOpen className="h-5 w-5 mr-2 text-blue-600" />
                Capacitación (RFP)
              </CardTitle>
              <Button variant="outline" size="sm">
                <Calendar className="h-4 w-4 mr-2" />
                Ver Agenda
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {capacitaciones.map((cap: Capacitacion) => (
                <div key={cap.id} className="p-3 border border-gray-200 dark:border-slate-700 rounded-lg bg-white/30 dark:bg-slate-800/30">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-slate-100">{cap.nombre}</h4>
                      <p className="text-sm text-gray-600 dark:text-slate-400">Progreso: {cap.progreso}%</p>
                    </div>
                    <Badge variant={cap.estado === "Completada" ? "default" : "secondary"} className={
                      cap.estado === "Completada" ? "bg-green-100 text-green-800" :
                      cap.estado === "En Progreso" ? "bg-blue-100 text-blue-800" :
                      "bg-gray-100 text-gray-800"
                    }>
                      {cap.estado}
                    </Badge>
                  </div>
                  <Progress value={cap.progreso} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Seguimiento (RSP) */}
        <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg font-medium flex items-center text-gray-900 dark:text-slate-100">
                <Users className="h-5 w-5 mr-2 text-purple-600" />
                Seguimiento (RSP)
              </CardTitle>
              <Button variant="outline" size="sm">
                <UserCheck className="h-4 w-4 mr-2" />
                Nuevo Seguimiento
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {seguimientos.map((seg: Seguimiento) => (
                <div key={seg.id} className="p-3 border border-gray-200 dark:border-slate-700 rounded-lg bg-white/30 dark:bg-slate-800/30">
                  <h4 className="font-medium text-gray-900 dark:text-slate-100">{seg.empleado}</h4>
                  <p className="text-sm text-gray-600 dark:text-slate-400">Próxima reunión: {seg.fechaReunion}</p>
                  <p className="text-sm text-gray-600 dark:text-slate-400 mt-1">Objetivo: {seg.objetivo}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Expedientes */}
        <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg font-medium flex items-center text-gray-900 dark:text-slate-100">
                <FileText className="h-5 w-5 mr-2 text-orange-600" />
                Expedientes
              </CardTitle>
              <Button variant="outline" size="sm">
                <FolderKanban className="h-4 w-4 mr-2" />
                Gestionar
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {expedientes.map((exp: Expediente) => (
                <div key={exp.id} className="p-3 border border-gray-200 dark:border-slate-700 rounded-lg bg-white/30 dark:bg-slate-800/30 flex justify-between items-center">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-slate-100">{exp.empleado}</h4>
                    <p className="text-sm text-gray-600 dark:text-slate-400">Tipo: {exp.tipo}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-lg text-gray-900 dark:text-slate-100">{exp.documentos}</p>
                    <p className="text-sm text-gray-600 dark:text-slate-400">docs.</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
