import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Bell, AlertTriangle, CheckCircle, Clock, Users, Calendar, FileText, Settings } from "lucide-react"
import { FormCard } from "@/components/ui/form-styles"

const notificaciones = [
  {
    id: 1,
    tipo: "urgente",
    categoria: "Inventario",
    titulo: "Stock Crítico",
    mensaje: "Conectores eléctricos por debajo del mínimo (5 unidades)",
    tiempo: "Hace 5 min",
    icono: AlertTriangle,
    leida: false,
  },
  {
    id: 2,
    tipo: "info",
    categoria: "Proyectos",
    titulo: "Proyecto Completado",
    mensaje: "Instalación HVAC - Empresa ABC finalizada exitosamente",
    tiempo: "Hace 15 min",
    icono: CheckCircle,
    leida: false,
  },
  {
    id: 3,
    tipo: "recordatorio",
    categoria: "Capacitación",
    titulo: "Capacitación Programada",
    mensaje: "Seguridad Industrial - Mañana 9:00 AM - Sala de Juntas",
    tiempo: "Hace 1 hora",
    icono: Calendar,
    leida: false,
  },
  {
    id: 4,
    tipo: "alerta",
    categoria: "Metrología",
    titulo: "Calibración Vencida",
    mensaje: "Osciloscopio Tektronix requiere calibración inmediata",
    tiempo: "Hace 2 horas",
    icono: Clock,
    leida: true,
  },
  {
    id: 5,
    tipo: "info",
    categoria: "Ventas",
    titulo: "Nueva Cotización",
    mensaje: "COT-2024-016 generada para Corporativo XYZ",
    tiempo: "Hace 3 horas",
    icono: FileText,
    leida: true,
  },
  {
    id: 6,
    tipo: "recordatorio",
    categoria: "RH",
    titulo: "Evaluación Pendiente",
    mensaje: "Evaluación de desempeño de Juan Pérez vence en 2 días",
    tiempo: "Hace 4 horas",
    icono: Users,
    leida: true,
  },
]

const categorias = [
  { nombre: "Todas", count: 16, color: "bg-gray-100 text-gray-800" },
  { nombre: "Inventario", count: 4, color: "bg-red-100 text-red-800" },
  { nombre: "Proyectos", count: 3, color: "bg-blue-100 text-blue-800" },
  { nombre: "Capacitación", count: 2, color: "bg-green-100 text-green-800" },
  { nombre: "Metrología", count: 3, color: "bg-yellow-100 text-yellow-800" },
  { nombre: "Ventas", count: 2, color: "bg-purple-100 text-purple-800" },
  { nombre: "RH", count: 2, color: "bg-pink-100 text-pink-800" },
]

export function NotificacionesTab() {
  const getNotificationColor = (tipo: string) => {
    switch (tipo) {
      case "urgente":
        return "border-l-moka-falu bg-moka-falu/10"
      case "alerta":
        return "border-l-moka-brown bg-moka-peach/30"
      case "info":
        return "border-l-moka-lion bg-moka-lion/20"
      case "recordatorio":
        return "border-l-moka-lion bg-moka-lion/15"
      default:
        return "border-l-moka-brown bg-moka-peach/20"
    }
  }

  const getIconColor = (tipo: string) => {
    switch (tipo) {
      case "urgente":
        return "text-moka-falu"
      case "alerta":
        return "text-moka-brown"
      case "info":
        return "text-moka-lion"
      case "recordatorio":
        return "text-moka-lion"
      default:
        return "text-moka-brown"
    }
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <FormCard>
        {/* Resumen de Notificaciones */}
      <div className="grid md:grid-cols-4 gap-4">
        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Notificaciones</p>
                <p className="text-2xl font-bold text-gray-900">16</p>
              </div>
              <Bell className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">No Leídas</p>
                <p className="text-2xl font-bold text-red-600">3</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Urgentes</p>
                <p className="text-2xl font-bold text-orange-600">1</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Recordatorios</p>
                <p className="text-2xl font-bold text-green-600">2</p>
              </div>
              <Clock className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid lg:grid-cols-4 gap-6">
        {/* Filtros por Categoría */}
        <Card className="border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Categorías</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {categorias.map((categoria, index) => (
                <Button key={index} variant="ghost" className="w-full justify-between p-3 h-auto">
                  <span>{categoria.nombre}</span>
                  <Badge variant="secondary" className={categoria.color}>
                    {categoria.count}
                  </Badge>
                </Button>
              ))}
            </div>

            <div className="mt-6">
              <Button variant="outline" className="w-full">
                <Settings className="h-4 w-4 mr-2" />
                Configurar Alertas
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Lista de Notificaciones */}
        <div className="lg:col-span-3">
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg font-semibold">Notificaciones Recientes</CardTitle>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    Marcar todas como leídas
                  </Button>
                  <Button variant="outline" size="sm">
                    Limpiar todas
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {notificaciones.map((notif) => (
                  <div
                    key={notif.id}
                    className={`p-4 border-l-4 rounded-lg ${getNotificationColor(notif.tipo)} ${
                      !notif.leida ? "bg-opacity-100" : "bg-opacity-50"
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3 flex-1">
                        <notif.icono className={`h-5 w-5 mt-0.5 ${getIconColor(notif.tipo)}`} />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <Badge variant="secondary" className="text-xs">
                              {notif.categoria}
                            </Badge>
                            {!notif.leida && <div className="w-2 h-2 bg-blue-500 rounded-full"></div>}
                          </div>
                          <h4 className={`font-medium ${!notif.leida ? "text-gray-900" : "text-gray-600"}`}>
                            {notif.titulo}
                          </h4>
                          <p className="text-sm text-gray-600 mt-1">{notif.mensaje}</p>
                          <p className="text-xs text-gray-400 mt-2">{notif.tiempo}</p>
                        </div>
                      </div>
                      <div className="flex space-x-1">
                        <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                          <CheckCircle className="h-3 w-3" />
                        </Button>
                        <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                          ×
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-6 text-center">
                <Button variant="outline">Cargar más notificaciones</Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      </FormCard>
    </div>
  )
}
