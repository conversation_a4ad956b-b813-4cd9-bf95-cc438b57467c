"use client"
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Settings, Users, Shield, Key, Puzzle, Link } from "lucide-react"

export function ConfiguracionTab() {
  const configuracionItems = [
    { titulo: "Configuración General", descripcion: "Configuración del sistema", ref: "062", icono: Settings },
    { titulo: "Configuración de Usuarios", descripcion: "Gestión de usuarios", ref: "063", icono: Users },
    { titulo: "Configuración de Roles", descripcion: "Administrar roles", ref: "064", icono: Shield },
    { titulo: "Configuración de Permisos", descripcion: "Gestionar permisos", ref: "065", icono: Key },
    { titulo: "Configuración de Módulos", descripcion: "Configurar módulos", ref: "066", icono: Puzzle },
    { titulo: "Configuración de Integraciones", descripcion: "Gestionar integraciones", ref: "067", icono: Link },
  ]

  return (
    <div className="space-y-6">
      <section className="p-4">
        <h2 className="text-lg font-semibold mb-2">Configuración del Sistema</h2>
        <p className="text-sm text-muted-foreground">Ajustes generales, usuarios, roles, permisos e integraciones.</p>
      </section>

      <div className="grid md:grid-cols-3 gap-4">
        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Módulos Activos</p>
                <p className="text-2xl font-bold text-blue-600">12</p>
              </div>
              <Puzzle className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Integraciones</p>
                <p className="text-2xl font-bold text-green-600">8</p>
              </div>
              <Link className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Configuraciones</p>
                <p className="text-2xl font-bold text-purple-600">24</p>
              </div>
              <Settings className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">Panel de Configuración</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {configuracionItems.map((item, index) => (
              <div
                key={index}
                className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow"
              >
                <div className="flex items-start space-x-3">
                  <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <item.icono className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">{item.titulo}</h4>
                      <Badge variant="secondary" className="text-xs">
                        Ref: {item.ref}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{item.descripcion}</p>
                    <Button variant="outline" size="sm" className="w-full">
                      Configurar
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card className="border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">Configuración Rápida</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 rounded">
              <h4 className="font-medium text-blue-900 dark:text-blue-400 mb-2">Configuración del Sistema</h4>
              <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
                Configura los parámetros básicos del sistema
              </p>
              <Button variant="outline" size="sm">
                Configurar Ahora
              </Button>
            </div>

            <div className="p-4 bg-green-50 dark:bg-green-900/20 border-l-4 border-green-400 rounded">
              <h4 className="font-medium text-green-900 dark:text-green-400 mb-2">Backup Automático</h4>
              <p className="text-sm text-green-700 dark:text-green-300 mb-3">
                Configura respaldos automáticos del sistema
              </p>
              <Button variant="outline" size="sm">
                Configurar Backup
              </Button>
            </div>

            <div className="p-4 bg-purple-50 dark:bg-purple-900/20 border-l-4 border-purple-400 rounded">
              <h4 className="font-medium text-purple-900 dark:text-purple-400 mb-2">Notificaciones</h4>
              <p className="text-sm text-purple-700 dark:text-purple-300 mb-3">
                Personaliza las notificaciones del sistema
              </p>
              <Button variant="outline" size="sm">
                Configurar Notificaciones
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
