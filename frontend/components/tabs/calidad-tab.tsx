"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { FileText, Link as LinkIcon, ListChecks, PlusCircle, Download, Trash2, UploadCloud, Search, Edit } from "lucide-react"

import * as React from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import {
  Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogClose
} from "@/components/ui/dialog"
import {
  AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger
} from "@/components/ui/alert-dialog"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { FormCard } from "@/components/ui/form-styles"
import { toast } from "@/components/ui/use-toast"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"
import { es } from "date-fns/locale"

// Tipos para Procedimientos
type Procedimiento = {
  id: string;
  nombre: string;
  descripcion: string;
  tipo: "Técnico (Calibración)" | "General (Área)";
  areaResponsable?: string; // Ej: Ventas, Administración, Almacén, etc.
  version: string;
  fechaSubida: Date;
  urlArchivo: string; // Simulación de URL
};

const procedimientoFormSchema = z.object({
  nombre: z.string().min(1, "Nombre es requerido."),
  descripcion: z.string().min(1, "Descripción es requerida."),
  tipo: z.enum(["Técnico (Calibración)", "General (Área)"]),
  areaResponsable: z.string().optional(),
  version: z.string().min(1, "Versión es requerida, ej: v1.0."),
  archivo: z.any().refine(file => file?.name, "El archivo PDF es requerido."),
});
type ProcedimientoFormValues = z.infer<typeof procedimientoFormSchema>;

const initialProcedimientos: Procedimiento[] = [
  { id: "PROC-TEC-001", nombre: "Calibración Termómetros Digitales", descripcion: "Procedimiento detallado para la calibración de termómetros digitales.", tipo: "Técnico (Calibración)", version: "v2.1", fechaSubida: new Date("2023-05-10"), urlArchivo: "/docs/sim/proc_tec_001.pdf", areaResponsable: "Metrología" },
  { id: "PROC-GEN-001", nombre: "Gestión de Compras", descripcion: "Flujo para la solicitud y aprobación de compras.", tipo: "General (Área)", areaResponsable: "Compras", version: "v1.5", fechaSubida: new Date("2023-08-20"), urlArchivo: "/docs/sim/proc_gen_001.pdf" },
  { id: "PROC-GEN-002", nombre: "Recepción de Mercancía en Almacén", descripcion: "Pasos para la correcta recepción y verificación de mercancía.", tipo: "General (Área)", areaResponsable: "Almacén", version: "v3.0", fechaSubida: new Date("2024-01-15"), urlArchivo: "/docs/sim/proc_gen_002.pdf" },
  { id: "PROC-TEC-002", nombre: "Verificación Manómetros", descripcion: "Guía para la verificación periódica de manómetros.", tipo: "Técnico (Calibración)", version: "v1.0", fechaSubida: new Date("2022-11-01"), urlArchivo: "/docs/sim/proc_tec_002.pdf", areaResponsable: "Metrología" },
];

// Simulación de rol de usuario (para habilitar/deshabilitar botones de gestión)
const esAdminCalidadOSistemas = true; // Cambiar a false para simular rol sin permisos de gestión

const ProcedimientosSection = () => {
  const [procedimientos, setProcedimientos] = React.useState<Procedimiento[]>(initialProcedimientos);
  const [isFormOpen, setIsFormOpen] = React.useState(false);
  const [searchTerm, setSearchTerm] = React.useState("");

  const form = useForm<ProcedimientoFormValues>({
    resolver: zodResolver(procedimientoFormSchema),
    defaultValues: { tipo: "General (Área)", version: "v1.0" }
  });

  const onSubmit = (values: ProcedimientoFormValues) => {
    const nuevoProcedimiento: Procedimiento = {
      id: `PROC-${Date.now().toString().slice(-4)}`,
      ...values,
      fechaSubida: new Date(),
      urlArchivo: `/docs/sim/${values.archivo.name}`, // Simulación
    };
    setProcedimientos(prev => [nuevoProcedimiento, ...prev]);
    toast({ title: "Procedimiento Cargado", description: `${values.nombre} ha sido añadido.` });
    setIsFormOpen(false);
    form.reset();
  };

  const handleDelete = (id: string) => {
    setProcedimientos(prev => prev.filter(p => p.id !== id));
    toast({ title: "Procedimiento Eliminado", variant: "destructive" });
  };

  const filteredProcedimientos = procedimientos.filter(p =>
    p.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
    p.descripcion.toLowerCase().includes(searchTerm.toLowerCase()) ||
    p.tipo.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (p.areaResponsable && p.areaResponsable.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const procedimientosTecnicos = filteredProcedimientos.filter(p => p.tipo === "Técnico (Calibración)");
  const procedimientosGenerales = filteredProcedimientos.filter(p => p.tipo === "General (Área)");

  const renderProcedimientoTable = (data: Procedimiento[], title: string) => (
    <div className="mt-4">
        <h3 className="text-lg font-semibold mb-2">{title}</h3>
        {data.length === 0 && <p className="text-sm text-muted-foreground">No hay procedimientos de este tipo {searchTerm && "que coincidan con la búsqueda"} .</p>}
        {data.length > 0 && (
            <div className="rounded-md border">
            <Table>
                <TableHeader>
                <TableRow>
                    <TableHead>Nombre</TableHead>
                    <TableHead className="hidden md:table-cell">Descripción</TableHead>
                    <TableHead className="hidden sm:table-cell">Versión</TableHead>
                    <TableHead className="hidden lg:table-cell">Área</TableHead>
                    <TableHead className="text-right">Acciones</TableHead>
                </TableRow>
                </TableHeader>
                <TableBody>
                {data.map((proc) => (
                    <TableRow key={proc.id}>
                    <TableCell className="font-medium">{proc.nombre}</TableCell>
                    <TableCell className="hidden md:table-cell text-sm text-muted-foreground truncate max-w-xs">{proc.descripcion}</TableCell>
                    <TableCell className="hidden sm:table-cell"><Badge variant="outline">{proc.version}</Badge></TableCell>
                    <TableCell className="hidden lg:table-cell">{proc.areaResponsable || "N/A"}</TableCell>
                    <TableCell className="text-right">
                        <Button variant="ghost" size="sm" asChild><a href={proc.urlArchivo} target="_blank" rel="noopener noreferrer"><Download className="h-4 w-4 mr-1" />Ver</a></Button>
                        {esAdminCalidadOSistemas && (
                        <AlertDialog>
                            <AlertDialogTrigger asChild><Button variant="ghost" size="sm" className="text-red-500 hover:text-red-600"><Trash2 className="h-4 w-4" /></Button></AlertDialogTrigger>
                            <AlertDialogContent>
                            <AlertDialogHeader><AlertDialogTitle>¿Eliminar Procedimiento?</AlertDialogTitle><AlertDialogDescription>Esta acción no se puede deshacer. Se eliminará "{proc.nombre}".</AlertDialogDescription></AlertDialogHeader>
                            <AlertDialogFooter><AlertDialogCancel>Cancelar</AlertDialogCancel><AlertDialogAction onClick={() => handleDelete(proc.id)} className="bg-red-600 hover:bg-red-700">Eliminar</AlertDialogAction></AlertDialogFooter>
                            </AlertDialogContent>
                        </AlertDialog>
                        )}
                    </TableCell>
                    </TableRow>
                ))}
                </TableBody>
            </Table>
            </div>
        )}
    </div>
  );


  return (
    <Card>
      <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
        <div>
          <CardTitle className="text-xl">Gestión de Procedimientos</CardTitle>
          <CardDescription>Visualiza, carga y administra los procedimientos técnicos y generales.</CardDescription>
        </div>
        {esAdminCalidadOSistemas && (
          <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
            <DialogTrigger asChild>
              <Button size="sm"><PlusCircle className="mr-2 h-4 w-4" /> Cargar Procedimiento</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-lg">
              <DialogHeader><DialogTitle>Cargar Nuevo Procedimiento</DialogTitle></DialogHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-2">
                  <FormField control={form.control} name="nombre" render={({ field }) => (<FormItem><FormLabel>Nombre del Procedimiento</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>)} />
                  <FormField control={form.control} name="descripcion" render={({ field }) => (<FormItem><FormLabel>Descripción Breve</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>)} />
                  <FormField control={form.control} name="tipo" render={({ field }) => (<FormItem><FormLabel>Tipo</FormLabel><Select onValueChange={field.onChange} defaultValue={field.value}><FormControl><SelectTrigger><SelectValue /></SelectTrigger></FormControl><SelectContent><SelectItem value="Técnico (Calibración)">Técnico (Calibración)</SelectItem><SelectItem value="General (Área)">General (Área)</SelectItem></SelectContent></Select><FormMessage /></FormItem>)} />
                  {form.watch("tipo") === "General (Área)" && <FormField control={form.control} name="areaResponsable" render={({ field }) => (<FormItem><FormLabel>Área Responsable (Opcional)</FormLabel><FormControl><Input placeholder="Ej: Ventas, Almacén" {...field} /></FormControl><FormMessage /></FormItem>)} />}
                  <FormField control={form.control} name="version" render={({ field }) => (<FormItem><FormLabel>Versión</FormLabel><FormControl><Input placeholder="Ej: v1.0, v2.1.3" {...field} /></FormControl><FormMessage /></FormItem>)} />
                  <FormField control={form.control} name="archivo" render={({ field }) => (<FormItem><FormLabel>Archivo PDF</FormLabel><FormControl><Input type="file" accept=".pdf" onChange={(e) => field.onChange(e.target.files ? e.target.files[0] : null)} /></FormControl><FormMessage /></FormItem>)} />
                  <DialogFooter className="pt-3"><DialogClose asChild><Button type="button" variant="outline">Cancelar</Button></DialogClose><Button type="submit">Cargar Procedimiento</Button></DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        )}
      </CardHeader>
      <CardContent>
        <div className="mb-4 relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
                type="search"
                placeholder="Buscar procedimientos por nombre, descripción, tipo, área..."
                className="pl-8 w-full"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
            />
        </div>
        {renderProcedimientoTable(procedimientosTecnicos, "Procedimientos Técnicos (de Calibración)")}
        {renderProcedimientoTable(procedimientosGenerales, "Procedimientos Generales (por Área)")}
      </CardContent>
    </Card>
  );
};

export const CalidadProcedimientosSection = ProcedimientosSection;

export function CalidadTab() {
  return (
    <div className="max-w-7xl mx-auto p-6">
      <FormCard>
        <header className="mb-6">
          <h1 className="text-3xl font-bold tracking-tight">Módulo de Calidad</h1>
          <p className="text-muted-foreground">
            Herramientas y funcionalidades para la gestión de la calidad, procedimientos y seguimiento.
          </p>
        </header>

      {/* Sección 1: Procedimientos */}
      <ProcedimientosSection />

      {/* Sección 2: Enlaces QuestionPro */}
      <QuestionProSection />

      {/* Sección 3: Seguimiento de Incidencias */}
      <SeguimientoIncidenciasSection />

      </FormCard>
    </div>
  );
}

// Placeholder for QuestionProSection
export function QuestionProSection() {
  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle className="text-xl">Enlaces QuestionPro</CardTitle>
        <CardDescription>Acceso y gestión de links de encuestas y supervisiones (TODO: implementar funcionalidad real).</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-muted-foreground text-sm">Próximamente: aquí se mostrarán los enlaces de QuestionPro para encuestas de satisfacción y supervisión.</div>
      </CardContent>
    </Card>
  );
}

export function SeguimientoIncidenciasSection() {
  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle className="text-xl">Seguimiento de Incidencias</CardTitle>
        <CardDescription>Gestión y seguimiento de minutas, quejas, no conformidades, acciones correctivas, etc. (TODO: implementar funcionalidad real).</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-muted-foreground text-sm">Próximamente: aquí se gestionarán y visualizarán las incidencias y su seguimiento.</div>
      </CardContent>
    </Card>
  );
}
