import { useQuery } from "@tanstack/react-query"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Package, AlertTriangle, Download, QrCode, TrendingUp, Bell, Loader2 } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"

// --- Simulación de API y Tipos de Datos ---
// En una aplicación real, esto estaría en archivos separados (ej: `lib/api.ts`, `types/admin.ts`)

interface InventarioItem {
  item: string;
  stock: number;
  minimo: number;
  estado: "normal" | "bajo" | "critico";
}

interface SolicitudItem {
  id: string;
  solicitante: string;
  area: string;
  estado: "Pendiente" | "Aprobada" | "En Revisión";
  fecha: string;
}

interface AdminData {
  inventario: InventarioItem[];
  solicitudes: SolicitudItem[];
}

// Simula una llamada a la API con un retardo
const fetchAdminData = async (): Promise<AdminData> => {
  console.log("Fetching admin data...")
  await new Promise(resolve => setTimeout(resolve, 1500)) // Simula latencia de red
  return {
    inventario: [
      { item: "Válvulas Industriales", stock: 45, minimo: 20, estado: "normal" },
      { item: "Sensores de Presión", stock: 8, minimo: 15, estado: "bajo" },
      { item: "Cables de Control", stock: 120, minimo: 50, estado: "normal" },
      { item: "Conectores Eléctricos", stock: 5, minimo: 25, estado: "critico" },
    ],
    solicitudes: [
      { id: "SOL-001", solicitante: "Juan Pérez", area: "Logística", estado: "Pendiente", fecha: "2024-01-15" },
      { id: "SOL-002", solicitante: "María García", area: "Ventas", estado: "Aprobada", fecha: "2024-01-14" },
      { id: "SOL-003", solicitante: "Carlos López", area: "Calidad", estado: "En Revisión", fecha: "2024-01-13" },
    ],
  }
}

// --- Custom Hook para obtener los datos ---
// Encapsula la lógica de `useQuery` para ser reutilizable.
const useAdminData = () => {
  return useQuery<AdminData, Error>({
    queryKey: ["adminData"], // Clave única para esta query en el caché
    queryFn: fetchAdminData, // Función que obtiene los datos
    staleTime: 5 * 60 * 1000, // 5 minutos: los datos se consideran "frescos" por este tiempo
  })
}

// --- Componente de Esqueleto (Skeleton) para el estado de carga ---
const AdminTabSkeleton = () => (
  <div className="space-y-6">
    <div className="grid md:grid-cols-4 gap-4">
      <Skeleton className="h-28" />
      <Skeleton className="h-28" />
      <Skeleton className="h-28" />
      <Skeleton className="h-28" />
    </div>
    <div className="grid lg:grid-cols-2 gap-6">
      <Card className="border-0 shadow-sm">
        <CardHeader><Skeleton className="h-8 w-3/4" /></CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-24 w-full" />
        </CardContent>
      </Card>
      <Card className="border-0 shadow-sm">
        <CardHeader><Skeleton className="h-8 w-3/4" /></CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-24 w-full" />
        </CardContent>
      </Card>
    </div>
  </div>
)

export function AdministracionTab() {
  const { data, isLoading, isError, error } = useAdminData()

  if (isLoading) {
    return <AdminTabSkeleton />
  }

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center h-64 bg-red-50 text-red-700 rounded-lg">
        <AlertTriangle className="h-12 w-12 mb-4" />
        <h2 className="text-xl font-semibold">Error al cargar los datos</h2>
        <p>{error.message}</p>
      </div>
    )
  }

  // Después de los checks de isLoading/isError, data no debería ser undefined.
  // Añadimos un check para satisfacer a TypeScript y para casos extremos.
  if (!data) {
    return null
  }

  const { inventario, solicitudes } = data

  return (
    <div className="space-y-6 p-6">
      {/* Header Section */}
      <header className="space-y-2 mb-8">
        <h1 className="text-3xl font-medium tracking-tight text-gray-900 dark:text-slate-100">
          Administración
        </h1>
        <p className="text-gray-600 dark:text-slate-400">
          Gestión de proyectos, inventario y solicitudes administrativas.
        </p>
      </header>

      {/* Estadísticas de Administración */}
      <div className="grid md:grid-cols-4 gap-4">
        <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-slate-400">Proyectos Liberados</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-slate-100">18</p>
                <p className="text-sm text-green-600">+3 esta semana</p>
              </div>
              <div className="p-3 bg-green-50 dark:bg-green-900/30 rounded-lg">
                <Package className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-slate-400">Inventario Total</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-slate-100">1,247</p>
                <p className="text-sm text-blue-600">Items activos</p>
              </div>
              <div className="p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-slate-400">Solicitudes Pendientes</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-slate-100">12</p>
                <p className="text-sm text-orange-600">Requieren atención</p>
              </div>
              <div className="p-3 bg-orange-50 dark:bg-orange-900/30 rounded-lg">
                <AlertTriangle className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-slate-400">Ventas del Mes</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-slate-100">$89,450</p>
                <p className="text-sm text-green-600">+15.2%</p>
              </div>
              <div className="p-3 bg-purple-50 dark:bg-purple-900/30 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        {/* Inventario en Tiempo Real */}
        <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg font-medium text-gray-900 dark:text-slate-100">Inventario en Tiempo Real</CardTitle>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm">
                  <QrCode className="h-4 w-4 mr-2" />
                  Escanear QR
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Excel
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {inventario.map((item: InventarioItem, index: number) => (
                <div key={index} className="p-4 border border-gray-200 dark:border-slate-700 rounded-lg bg-white/30 dark:bg-slate-800/30">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-slate-100">{item.item}</h4>
                      <p className="text-sm text-gray-600 dark:text-slate-400">
                        Stock: {item.stock} | Mínimo: {item.minimo}
                      </p>
                    </div>
                    <Badge
                      variant="secondary"
                      className={
                        item.estado === "critico"
                          ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                          : item.estado === "bajo"
                            ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
                            : "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                      }
                    >
                      {item.estado === "critico" ? "Crítico" : item.estado === "bajo" ? "Bajo" : "Normal"}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <Progress
                      value={(item.stock / (item.minimo * 2)) * 100}
                      className={`h-2 ${
                        item.estado === "critico"
                          ? "[&>div]:bg-red-500"
                          : item.estado === "bajo"
                            ? "[&>div]:bg-yellow-500"
                            : "[&>div]:bg-green-500"
                      }`}
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Solicitudes de Material */}
        <Card className="border-0 shadow-sm">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg font-semibold">Solicitudes de Material</CardTitle>
              <Button variant="outline" size="sm">
                <Bell className="h-4 w-4 mr-2" />
                Alertas
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {solicitudes.map((solicitud: SolicitudItem) => (
                <div key={solicitud.id} className="p-4 border border-gray-200 rounded-lg">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium text-gray-900">{solicitud.id}</h4>
                      <p className="text-sm text-gray-600">
                        {solicitud.solicitante} - {solicitud.area}
                      </p>
                      <p className="text-xs text-gray-500">{solicitud.fecha}</p>
                    </div>
                    <Badge
                      variant="secondary"
                      className={
                        solicitud.estado === "Aprobada"
                          ? "bg-green-100 text-green-800"
                          : solicitud.estado === "En Revisión"
                            ? "bg-blue-100 text-blue-800"
                            : "bg-yellow-100 text-yellow-800"
                      }
                    >
                      {solicitud.estado}
                    </Badge>
                  </div>
                  <div className="flex space-x-2 mt-3">
                    <Button variant="outline" size="sm">
                      Revisar
                    </Button>
                    <Button variant="outline" size="sm">
                      Aprobar
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 space-y-3">
              <h4 className="font-medium text-gray-900">Alertas Administrativas</h4>
              <div className="space-y-2">
                <div className="p-3 bg-red-50 border-l-4 border-red-400 rounded">
                  <p className="text-sm font-medium text-red-800">Stock crítico: Conectores Eléctricos (5 unidades)</p>
                </div>
                <div className="p-3 bg-yellow-50 border-l-4 border-yellow-400 rounded">
                  <p className="text-sm font-medium text-yellow-800">Solicitud SOL-001 pendiente de aprobación</p>
                </div>
                <div className="p-3 bg-blue-50 border-l-4 border-blue-400 rounded">
                  <p className="text-sm font-medium text-blue-800">Nuevo proyecto liberado: P-2024-008</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
