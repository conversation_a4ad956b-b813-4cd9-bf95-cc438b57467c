"use client"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Users, Star, TrendingUp, FileText, Heart, ShoppingBag, FileDown, FolderOpen, ListChecks } from "lucide-react"
import { useClients, useCreateClient, useUpdateClient } from "@/hooks/use-client";
import { useState, useEffect, useRef } from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogClose, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { PlusCircle, Edit, Eye } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { Label } from "@/components/ui/label";
import { ClientForm } from "@/components/clientes/ClientForm";
import Link from "next/link";

type Client = {
  id?: string;
  commercialName: string;
  legalName: string;
  rfc: string;
  contactName?: string;
  status: string;
  // ...otros campos si se requieren...
};

const initialForm: Client = {
  commercialName: "",
  legalName: "",
  rfc: "",
  contactName: "",
  status: "active",
};

const TABS = [
  { key: "listado", label: "Clientes", icon: Users },
  { key: "altaCompleta", label: "Alta Completa", icon: PlusCircle },
  { key: "altaRapida", label: "Alta Rápida", icon: Star },
  { key: "historial", label: "Historial", icon: ListChecks },
  { key: "categorizacion", label: "Categorización", icon: TrendingUp },
  { key: "documentos", label: "Documentos", icon: FolderOpen },
  { key: "exportar", label: "KPIs", icon: FileDown },
  { key: "indicadores", label: "Exportar", icon: FileText },
];

export function ClientesTab() {
  const [activeTab, setActiveTab] = useState("listado");
  const [filters, setFilters] = useState({ search: "", rfc: "", status: "" });
  const [page, setPage] = useState(1);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingClient, setEditingClient] = useState<Client | null>(null);
  const [form, setForm] = useState<Client>(initialForm);
  const [formError, setFormError] = useState("");
  const { data, isLoading, error } = useClients({ ...filters, page });
  const createClient = useCreateClient();
  const updateClient = useUpdateClient();

  useEffect(() => {
    if (editingClient) setForm(editingClient);
    else setForm(initialForm);
  }, [editingClient, isFormOpen]);

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setForm(f => ({ ...f, [name]: value }));
  };

  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setFormError("");
    if (!form.commercialName || !form.legalName || !form.rfc) {
      setFormError("Nombre comercial, razón social y RFC son obligatorios");
      return;
    }
    if (editingClient && editingClient.id) {
      updateClient.mutate({ id: editingClient.id, data: form }, { onSuccess: () => setIsFormOpen(false), onError: (err: any) => setFormError(err.message || "Error al actualizar cliente") });
    } else {
      createClient.mutate(form, { onSuccess: () => setIsFormOpen(false), onError: (err: any) => setFormError(err.message || "Error al registrar cliente") });
    }
  };

  const handleFilterChange = (field: string, value: string) => {
    setFilters(f => ({ ...f, [field]: value }));
    setPage(1);
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header Section */}
      <header className="space-y-2 mb-8">
        <h1 className="text-3xl font-medium tracking-tight text-gray-900 dark:text-slate-100">
          Clientes
        </h1>
        <p className="text-gray-600 dark:text-slate-400">
          Gestión de clientes, historial y categorización.
        </p>
      </header>

      <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
        <CardContent className="p-6">
          <div className="flex gap-2 border-b border-gray-200 dark:border-slate-700 mb-6">
            {TABS.map(tab => (
              <button
                key={tab.key}
                className={`flex items-center gap-1 px-4 py-2 border-b-2 transition-all ${activeTab === tab.key ? "border-blue-600 text-blue-600 font-medium" : "border-transparent text-gray-600 dark:text-slate-400"}`}
                onClick={() => setActiveTab(tab.key)}
              >
                <tab.icon className="h-4 w-4" /> {tab.label}
              </button>
            ))}
          </div>
          {activeTab === "listado" && (
            <>
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
                <div>
                  <h2 className="text-lg font-medium text-gray-900 dark:text-slate-100 mb-1">Lista de Clientes</h2>
                  <p className="text-sm text-gray-600 dark:text-slate-400">Gestión y administración de la base de clientes.</p>
                </div>
            <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
              <DialogTrigger asChild>
                <Button onClick={() => { setEditingClient(null); setIsFormOpen(true); }} size="sm"><PlusCircle className="mr-2 h-4 w-4" /> Nuevo Cliente</Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-lg">
                <DialogHeader><DialogTitle>{editingClient ? "Editar Cliente" : "Nuevo Cliente"}</DialogTitle></DialogHeader>
                <form onSubmit={handleFormSubmit} className="space-y-4 py-2">
                  <Input name="commercialName" placeholder="Nombre comercial*" value={form.commercialName} onChange={handleFormChange} required />
                  <Input name="legalName" placeholder="Razón social*" value={form.legalName} onChange={handleFormChange} required />
                  <Input name="rfc" placeholder="RFC*" value={form.rfc} onChange={handleFormChange} required />
                  <Input name="contactName" placeholder="Contacto principal" value={form.contactName || ""} onChange={handleFormChange} />
                  <select name="status" className="input input-bordered w-full" value={form.status} onChange={handleFormChange}>
                    <option value="active">Activo</option>
                    <option value="inactive">Inactivo</option>
                  </select>
                  {formError && <div className="text-red-500 text-sm">{formError}</div>}
                  <DialogFooter className="pt-3">
                    <DialogClose asChild><Button type="button" variant="outline">Cancelar</Button></DialogClose>
                    <Button type="submit">{editingClient ? "Guardar Cambios" : "Registrar Cliente"}</Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </section>
          <Card className="border-0 shadow-sm">
            <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
              <div className="flex gap-2">
                <Input placeholder="Buscar por nombre..." value={filters.search} onChange={e => handleFilterChange("search", e.target.value)} className="max-w-xs" />
                <Input placeholder="RFC..." value={filters.rfc} onChange={e => handleFilterChange("rfc", e.target.value)} className="max-w-xs" />
                <select className="input input-bordered" value={filters.status} onChange={e => handleFilterChange("status", e.target.value)}>
                  <option value="">Todos</option>
                  <option value="active">Activo</option>
                  <option value="inactive">Inactivo</option>
                </select>
              </div>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="py-10 text-center">Cargando...</div>
              ) : error ? (
                <div className="py-10 text-center text-red-500">Error al cargar clientes</div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Nombre comercial</TableHead>
                        <TableHead>Razón social</TableHead>
                        <TableHead>RFC</TableHead>
                        <TableHead>Contacto</TableHead>
                        <TableHead>Estatus</TableHead>
                        <TableHead className="text-right">Acciones</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {(data?.clients as Client[] | undefined)?.map((c: Client) => (
                        <TableRow key={c.id}>
                          <TableCell>{c.commercialName}</TableCell>
                          <TableCell>{c.legalName}</TableCell>
                          <TableCell>{c.rfc}</TableCell>
                          <TableCell>{c.contactName}</TableCell>
                          <TableCell>
                            <Badge variant={c.status === "active" ? "outline" : "secondary"}>{c.status === "active" ? "Activo" : "Inactivo"}</Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Link href={`/dashboard/clientes/${c.id}/documentos`}>
                              <Button variant="ghost" size="icon">
                                <FolderOpen className="h-4 w-4" />
                              </Button>
                            </Link>
                            <Button variant="ghost" size="icon" onClick={() => {/* ver detalles */}}><Eye className="h-4 w-4" /></Button>
                            <Button variant="ghost" size="icon" onClick={() => { setEditingClient(c); setIsFormOpen(true); }}><Edit className="h-4 w-4" /></Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  <div className="flex justify-end mt-4 gap-2">
                    <Button size="sm" variant="outline" disabled={page <= 1} onClick={() => setPage(p => Math.max(1, p - 1))}>Anterior</Button>
                    <span className="px-2">Página {data?.page} de {data?.totalPages}</span>
                    <Button size="sm" variant="outline" disabled={page >= (data?.totalPages ?? 1)} onClick={() => setPage(p => p + 1)}>Siguiente</Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </>
      )}
      {activeTab === "altaCompleta" && (
        <div className="p-6 max-w-2xl mx-auto">
          <h2 className="text-xl font-bold mb-2">Alta Completa de Cliente</h2>
          <ClientForm
            onSubmit={async (data) => {
              try {
                await createClient.mutateAsync(data);
                toast({ title: "Cliente registrado exitosamente" });
              } catch (error: any) {
                toast({ title: "Error al registrar cliente", description: error?.message || "Error desconocido", variant: "destructive" });
              }
            }}
            loading={createClient.status === 'pending'}
          />
        </div>
      )}
      {activeTab === "altaRapida" && (
        <div className="p-6 max-w-lg mx-auto">
          <h2 className="text-xl font-bold mb-2">Alta Rápida de Cliente</h2>
          <QuickClientForm />
        </div>
      )}
      {activeTab === "historial" && (
        <div className="p-6"><h2 className="text-xl font-bold mb-2">Historial de Cliente</h2><p>Historial de compras, cotizaciones, productos y notas de seguimiento (TODO: implementar).</p></div>
      )}
      {activeTab === "categorizacion" && (
        <div className="p-6"><h2 className="text-xl font-bold mb-2">Categorización y Segmentos</h2><p>Segmentos, tipo de cliente, área, tamaño, giro, etc. (TODO: implementar).</p></div>
      )}
      {activeTab === "documentos" && (
        <div className="p-6"><h2 className="text-xl font-bold mb-2">Documentos</h2><p>Gestión de archivos y documentos asociados al cliente (TODO: implementar).</p></div>
      )}
      {activeTab === "exportar" && (
        <div className="p-6"><h2 className="text-xl font-bold mb-2">Exportar Clientes</h2><p>Exportación de datos de clientes a Excel/CSV (TODO: implementar).</p></div>
      )}
      {activeTab === "indicadores" && (
        <div className="p-6"><h2 className="text-xl font-bold mb-2">Indicadores y KPIs</h2><p>Dashboard de indicadores de clientes, estado de cuenta, etc. (TODO: implementar).</p></div>
      )}
    </div>
  )
}

// --- Formulario Alta Rápida ---
function QuickClientForm() {
  const createClient = useCreateClient();
  const [form, setForm] = useState({
    commercialName: "",
    rfc: "",
    assignedSalespersonId: "",
  });
  const [error, setError] = useState("");
  const formRef = useRef<HTMLFormElement>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setForm(f => ({ ...f, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    if (!form.commercialName || !form.rfc) {
      setError("Nombre comercial y RFC son obligatorios");
      return;
    }
    if (!/^[A-Z&Ñ]{3,4}\d{6}[A-Z\d]{3}$/.test(form.rfc)) {
      setError("El RFC no tiene un formato válido");
      return;
    }
    const payload = {
      ...form,
      assignedSalespersonId: form.assignedSalespersonId ? Number(form.assignedSalespersonId) : undefined,
    };
    // Endpoint rápido
    fetch("/api/clients/quick", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload),
    })
      .then(async res => {
        if (!res.ok) throw new Error((await res.json()).message || "Error al registrar cliente");
        toast({ title: "Cliente registrado correctamente" });
        setForm({ commercialName: "", rfc: "", assignedSalespersonId: "" });
        formRef.current?.reset();
      })
      .catch(err => setError(err.message || "Error al registrar cliente"));
  };

  return (
    <form ref={formRef} onSubmit={handleSubmit} className="grid grid-cols-1 gap-4">
      <div>
        <Label htmlFor="commercialName">Nombre comercial*</Label>
        <Input id="commercialName" name="commercialName" value={form.commercialName} onChange={handleChange} required />
      </div>
      <div>
        <Label htmlFor="rfc">RFC*</Label>
        <Input id="rfc" name="rfc" value={form.rfc} onChange={handleChange} required maxLength={13} />
      </div>
      <div>
        <Label htmlFor="assignedSalespersonId">ID Vendedor asignado</Label>
        <Input id="assignedSalespersonId" name="assignedSalespersonId" value={form.assignedSalespersonId} onChange={handleChange} type="number" min={0} />
      </div>
      {error && <div className="text-red-500 text-sm col-span-1">{error}</div>}
      <div className="col-span-1 flex justify-end gap-2 mt-2">
        <Button type="submit">Registrar Cliente</Button>
      </div>
    </form>
  );
}
