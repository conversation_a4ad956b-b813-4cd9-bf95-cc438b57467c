"use client"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ShoppingCart, Package, FileText, TrendingUp, Users, DollarSign } from "lucide-react"

export function ComprasTab() {
  const compras = [
    { id: "PO-001", proveedor: "Proveedor ABC", monto: "$15,000", fecha: "2024-01-20", estado: "Pendiente" },
    { id: "PO-002", proveedor: "Suministros XYZ", monto: "$8,500", fecha: "2024-01-19", estado: "Aprobada" },
    { id: "PO-003", proveedor: "Materiales DEF", monto: "$22,000", fecha: "2024-01-18", estado: "Recibida" },
  ]

  return (
    <div className="space-y-6">
      <section className="p-4">
        <h2 className="text-lg font-semibold mb-2"><PERSON><PERSON><PERSON><PERSON></h2>
        <p className="text-sm text-muted-foreground">
          <PERSON><PERSON><PERSON><PERSON> aparecer<PERSON> las herramientas para gestionar órdenes de compra, proveedores y cotizaciones.
        </p>
      </section>

      <div className="grid md:grid-cols-4 gap-4">
        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Órdenes Activas</p>
                <p className="text-2xl font-bold text-blue-600">24</p>
              </div>
              <ShoppingCart className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Proveedores</p>
                <p className="text-2xl font-bold text-green-600">45</p>
              </div>
              <Users className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Gasto del Mes</p>
                <p className="text-2xl font-bold text-purple-600">$125,430</p>
              </div>
              <DollarSign className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Productos</p>
                <p className="text-2xl font-bold text-orange-600">1,247</p>
              </div>
              <Package className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        <Card className="border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Órdenes de Compra Recientes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {compras.map((compra) => (
                <div key={compra.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">{compra.id}</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{compra.proveedor}</p>
                      <p className="text-xs text-gray-500">{compra.fecha}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900 dark:text-gray-100">{compra.monto}</p>
                      <Badge
                        variant="secondary"
                        className={
                          compra.estado === "Recibida"
                            ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                            : compra.estado === "Aprobada"
                              ? "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
                              : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"
                        }
                      >
                        {compra.estado}
                      </Badge>
                    </div>
                  </div>
                  <div className="flex space-x-2 mt-3">
                    <Button variant="outline" size="sm">
                      Ver Detalles
                    </Button>
                    <Button variant="outline" size="sm">
                      Seguimiento
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Acciones Rápidas</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
                <ShoppingCart className="h-6 w-6 mb-2" />
                <span className="text-sm">Nueva Orden</span>
              </Button>
              <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
                <Users className="h-6 w-6 mb-2" />
                <span className="text-sm">Proveedores</span>
              </Button>
              <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
                <FileText className="h-6 w-6 mb-2" />
                <span className="text-sm">Cotizaciones</span>
              </Button>
              <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
                <TrendingUp className="h-6 w-6 mb-2" />
                <span className="text-sm">Reportes</span>
              </Button>
            </div>

            <div className="mt-6 space-y-3">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">Alertas de Compras</h4>
              <div className="space-y-2">
                <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-400 rounded">
                  <p className="text-sm font-medium text-yellow-800 dark:text-yellow-400">
                    Orden PO-001 pendiente de aprobación
                  </p>
                </div>
                <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 rounded">
                  <p className="text-sm font-medium text-blue-800 dark:text-blue-400">
                    Nueva cotización recibida de Proveedor ABC
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
