"use client"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Package, TrendingUp, TrendingDown, RotateCcw, FileText, AlertTriangle, QrCode, FileArchive, Truck, CalendarClock, Handshake } from "lucide-react"

export function AlmacenTab() { // Renombrado de InventarioTab a AlmacenTab
  const almacenItems = [
    { titulo: "Stock Actual", descripcion: "Visualizar inventario en tiempo real", icono: Package },
    { titulo: "Entradas de Stock (QR/Scanner)", descripcion: "Registrar ingresos de mercancía", icono: QrCode },
    { titulo: "Salidas de Stock (Vale)", descripcion: "Registrar salidas con vale", icono: FileArchive },
    { titulo: "Préstamo de Equipo", descripcion: "Gestionar préstamos y devoluciones", icono: Handshak<PERSON> },
    { titulo: "Reporte de Inventario", descripcion: "Gráficos y datos de stock", icono: FileText },
    { titulo: "Solicitud de Viáticos", descripcion: "Gestionar viáticos de almacén", icono: Truck },
    { titulo: "Solicitud de Tiempo", descripcion: "Registrar solicitudes de tiempo", icono: CalendarClock },
  ]

  const productos = [
    { nombre: "Válvulas Industriales", stock: 45, minimo: 20, estado: "normal" },
    { nombre: "Sensores de Presión", stock: 8, minimo: 15, estado: "bajo" },
    { nombre: "Cables de Control", stock: 120, minimo: 50, estado: "normal" },
    { nombre: "Conectores Eléctricos", stock: 5, minimo: 25, estado: "critico" },
  ]

  // Placeholder para simular notificaciones contextuales de viáticos y tiempo
  const notificacionesContextuales = {
    viaticos: 2, // Ejemplo: 2 solicitudes de viáticos pendientes para Almacén
    tiempo: 1,   // Ejemplo: 1 solicitud de tiempo pendiente para Almacén
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header Section */}
      <header className="space-y-2 mb-8">
        <h1 className="text-3xl font-medium tracking-tight text-gray-900 dark:text-slate-100">
          Almacén
        </h1>
        <p className="text-gray-600 dark:text-slate-400">
          Visualiza y actualiza el stock, movimientos, préstamos y solicitudes específicas del área de almacén.
        </p>
      </header>

      {/* Resumen General y Alertas */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-slate-400">Total Productos</p>
                <p className="text-2xl font-bold text-blue-600">1,247</p>
              </div>
              <Package className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-slate-400">Stock Bajo/Crítico</p>
                <p className="text-2xl font-bold text-yellow-500">28</p> {/* Suma de bajo y crítico */}
              </div>
              <AlertTriangle className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
         <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-slate-400">Solic. Viáticos Pendientes</p>
                <p className="text-2xl font-bold text-orange-500">{notificacionesContextuales.viaticos}</p>
              </div>
              <Truck className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
        <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-slate-400">Solic. Tiempo Pendientes</p>
                <p className="text-2xl font-bold text-purple-500">{notificacionesContextuales.tiempo}</p>
              </div>
              <CalendarClock className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Acciones Principales y Listado de Productos con Stock Bajo */}
      <div className="grid lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-1 border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Herramientas de Almacén</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {almacenItems.map((item, index) => (
              <Button
                key={index}
                variant="outline"
                className="w-full justify-start text-left py-3 h-auto"
              >
                <item.icono className="mr-3 h-5 w-5 text-moka-lion" />
                <div>
                  <p className="font-medium text-sm text-gray-800 dark:text-gray-200">{item.titulo}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{item.descripcion}</p>
                </div>
                {item.titulo === "Solicitud de Viáticos" && notificacionesContextuales.viaticos > 0 && (
                  <Badge className="ml-auto bg-moka-falu text-white">{notificacionesContextuales.viaticos}</Badge>
                )}
                {item.titulo === "Solicitud de Tiempo" && notificacionesContextuales.tiempo > 0 && (
                  <Badge className="ml-auto bg-purple-500 text-white">{notificacionesContextuales.tiempo}</Badge>
                )}
              </Button>
            ))}
          </CardContent>
        </Card>

        <Card className="lg:col-span-2 border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Productos con Stock Bajo o Crítico</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto"> {/* Limitar altura y permitir scroll */}
              {productos.filter(p => p.estado === "bajo" || p.estado === "critico").map((producto, index) => (
                <div key={index} className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg flex items-center justify-between hover:shadow-md transition-shadow">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-gray-100">{producto.nombre}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Stock: {producto.stock} | Mínimo: {producto.minimo}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge
                      variant={producto.estado === "critico" ? "destructive" : "default"}
                      className={
                        producto.estado === "critico"
                          ? "bg-red-100 text-red-700 dark:bg-red-700/30 dark:text-red-300"
                          : "bg-yellow-100 text-yellow-700 dark:bg-yellow-600/30 dark:text-yellow-300"
                      }
                    >
                      {producto.estado === "critico" ? "Crítico" : "Bajo"}
                    </Badge>
                    <Button variant="outline" size="sm" className="text-xs">
                      Revisar
                    </Button>
                  </div>
                </div>
              ))}
              {productos.filter(p => p.estado === "bajo" || p.estado === "critico").length === 0 && (
                <p className="text-sm text-gray-500 dark:text-gray-400 text-center py-4">
                  No hay productos con stock bajo o crítico actualmente.
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
