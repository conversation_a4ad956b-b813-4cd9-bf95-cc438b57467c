import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, <PERSON>Title } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { BarChart3, Download, FileSpreadsheet, FileText, Calendar, TrendingUp } from "lucide-react"

export function ReportesTab() {
  const reportes = [
    { nombre: "Ventas Mensuales", tipo: "Excel", fecha: "2024-01-15", tamaño: "2.3 MB" },
    { nombre: "Inventario General", tipo: "PDF", fecha: "2024-01-14", tamaño: "1.8 MB" },
    { nombre: "Proyectos Activos", tipo: "Excel", fecha: "2024-01-13", tamaño: "3.1 MB" },
    { nombre: "Capacitaciones", tipo: "PDF", fecha: "2024-01-12", tamaño: "1.2 MB" },
  ]

  return (
    <div className="space-y-6 p-6">
      {/* Header Section */}
      <header className="space-y-2 mb-8">
        <h1 className="text-3xl font-medium tracking-tight text-gray-900 dark:text-slate-100">
          Reportes
        </h1>
        <p className="text-gray-600 dark:text-slate-400">
          Generación y gestión de reportes del sistema.
        </p>
      </header>

      {/* Generador de Reportes */}
      <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
        <CardHeader>
          <CardTitle className="text-lg font-medium text-gray-900 dark:text-slate-100">Generador de Reportes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-slate-300">Tipo de Reporte</label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ventas">Ventas</SelectItem>
                  <SelectItem value="inventario">Inventario</SelectItem>
                  <SelectItem value="proyectos">Proyectos</SelectItem>
                  <SelectItem value="rh">Recursos Humanos</SelectItem>
                  <SelectItem value="calidad">Calidad</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-slate-300">Período</label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar período" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hoy">Hoy</SelectItem>
                  <SelectItem value="semana">Esta Semana</SelectItem>
                  <SelectItem value="mes">Este Mes</SelectItem>
                  <SelectItem value="trimestre">Trimestre</SelectItem>
                  <SelectItem value="año">Año</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">Formato</label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Formato" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="excel">Excel</SelectItem>
                  <SelectItem value="pdf">PDF</SelectItem>
                  <SelectItem value="csv">CSV</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button className="w-full">
                <BarChart3 className="h-4 w-4 mr-2" />
                Generar Reporte
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* Reportes Recientes */}
        <div className="lg:col-span-2">
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg font-semibold">Reportes Recientes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportes.map((reporte, index) => (
                  <div key={index} className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex justify-between items-start">
                      <div className="flex items-start space-x-3">
                        <div className="p-2 bg-blue-50 rounded-lg">
                          {reporte.tipo === "Excel" ? (
                            <FileSpreadsheet className="h-5 w-5 text-green-600" />
                          ) : (
                            <FileText className="h-5 w-5 text-red-600" />
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{reporte.nombre}</h4>
                          <p className="text-sm text-gray-600">
                            {reporte.tipo} - {reporte.tamaño}
                          </p>
                          <p className="text-xs text-gray-500">{reporte.fecha}</p>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Descargar
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Accesos Rápidos */}
        <Card className="border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Accesos Rápidos</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Button variant="outline" className="w-full justify-start">
                <TrendingUp className="h-4 w-4 mr-2" />
                Dashboard Ejecutivo
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <BarChart3 className="h-4 w-4 mr-2" />
                Métricas de Ventas
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                Inventario Detallado
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Calendar className="h-4 w-4 mr-2" />
                Programación Mensual
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <FileText className="h-4 w-4 mr-2" />
                Reporte de Calidad
              </Button>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Programar Reportes</h4>
              <p className="text-sm text-blue-700 mb-3">
                Configura reportes automáticos para recibir información periódica
              </p>
              <Button variant="outline" size="sm" className="w-full">
                Configurar
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
