"use client"
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Users, Shield, Settings, Database, Activity, HardDrive } from "lucide-react"

export function SistemasTab() {
  const sistemasItems = [
    { titulo: "Gestión de Usuarios", descripcion: "Administrar usuarios del sistema", ref: "038", icono: Users },
    { titulo: "Permisos y Roles", descripcion: "Configurar roles y permisos", ref: "039", icono: Shield },
    { titulo: "Integraciones", descripcion: "Gestionar integraciones", ref: "041", icono: Database },
    { titulo: "Auditoría del Sistema", descripcion: "Logs y auditoría", ref: "042", icono: Activity },
    { titulo: "Backup y Restauración", descripcion: "<PERSON><PERSON>aldos del sistema", ref: "043", icono: HardDrive },
  ]

  return (
    <div className="space-y-6">
      <section className="p-4">
        <h2 className="text-lg font-semibold mb-2">Módulo de Sistemas</h2>
        <p className="text-sm text-muted-foreground">
          Administración de usuarios, roles, permisos y configuración del sistema.
        </p>
      </section>

      <div className="grid md:grid-cols-3 gap-4">
        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Usuarios Activos</p>
                <p className="text-2xl font-bold text-blue-600">89</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Roles Configurados</p>
                <p className="text-2xl font-bold text-green-600">12</p>
              </div>
              <Shield className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Integraciones</p>
                <p className="text-2xl font-bold text-purple-600">8</p>
              </div>
              <Database className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">Módulos del Sistema</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {sistemasItems.map((item, index) => (
              <div
                key={index}
                className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow"
              >
                <div className="flex items-start space-x-3">
                  <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <item.icono className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">{item.titulo}</h4>
                      <Badge variant="secondary" className="text-xs">
                        Ref: {item.ref}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{item.descripcion}</p>
                    <Button variant="outline" size="sm" className="w-full">
                      Acceder
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
