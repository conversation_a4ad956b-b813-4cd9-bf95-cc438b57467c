"use client";

import { usePathname } from "next/navigation";
import { AsignarFolioView } from "@/components/informes/AsignarFolioView";
import { StatusInformesView } from "@/components/informes/StatusInformesView";
import { HistorialFoliosView } from "@/components/informes/HistorialFoliosView";
import { EstadisticosInformesView } from "@/components/informes/EstadisticosInformesView";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export function InformesTab() {
  const pathname = usePathname();

  // Determina qué componente renderizar basado en la ruta
  // Esta es una lógica básica, podría necesitar ser más robusta
  // si hay más niveles de anidación o rutas dinámicas dentro de informes.
  let content = null;
  if (pathname === "/dashboard/informes/asignar-folio") {
    content = <AsignarFolioView />;
  } else if (pathname === "/dashboard/informes/status") {
    content = <StatusInformesView />;
  } else if (pathname === "/dashboard/informes/historial-folios") {
    content = <HistorialFoliosView />;
  } else if (pathname === "/dashboard/informes/estadisticos") {
    content = <EstadisticosInformesView />;
  } else {
    // Contenido por defecto o una vista general si se accede a /dashboard/informes directamente
    // Por ahora, podemos mostrar un mensaje o el primer sub-item (ej. Asignar Folio)
    // O podrías tener un componente "InformesHomeView"
    content = (
      <Card>
        <CardHeader>
          <CardTitle>Pestaña de Informes</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Seleccione una opción del submenú de Informes en la barra lateral.</p>
          <p>Ruta actual: {pathname}</p>
           {/* Podrías redirigir a la primera sub-opción o mostrar un resumen aquí */}
        </CardContent>
      </Card>
    );
  }

  return <div className="space-y-6">{content}</div>;
}
