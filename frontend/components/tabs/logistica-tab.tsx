import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Truck, Calendar, FolderOpen, BarChart3, <PERSON><PERSON><PERSON><PERSON>gle, File<PERSON>lock } from "lucide-react"
import Link from "next/link"

// Mock data for logistics summary
const summaryData = {
  servicesToday: 3,
  pendingProjects: 5,
  recentEntries: 2,
  alerts: 1,
}

const recentServices = [
    { id: "SRV-021", client: "Industrias Acme S.A.", status: "En Ruta" },
    { id: "SRV-022", client: "Laboratorios Beta", status: "Programado" },
    { id: "SRV-023", client: "Constructora Delta", status: "Completado" },
]

export function LogisticaTab() {
  return (
    <div className="space-y-6 p-6">
      {/* Header Section */}
      <header className="space-y-2 mb-8">
        <h1 className="text-3xl font-medium tracking-tight text-gray-900 dark:text-slate-100">
          Logística
        </h1>
        <p className="text-gray-600 dark:text-slate-400">
          Gestión de rutas de entrega, inventario de vehículos y seguimiento de envíos.
        </p>
      </header>

      {/* Quick Stats */}
      <div className="grid md:grid-cols-4 gap-4">
        <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-900 dark:text-slate-100">Servicios Hoy</CardTitle>
            <Truck className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900 dark:text-slate-100">{summaryData.servicesToday}</div>
            <p className="text-xs text-gray-600 dark:text-slate-400">programados para hoy</p>
          </CardContent>
        </Card>
        <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-900 dark:text-slate-100">Proyectos Pendientes</CardTitle>
            <FolderOpen className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900 dark:text-slate-100">{summaryData.pendingProjects}</div>
            <p className="text-xs text-gray-600 dark:text-slate-400">esperando acción</p>
          </CardContent>
        </Card>
        <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-900 dark:text-slate-100">Ingresos Recientes</CardTitle>
            <BarChart3 className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900 dark:text-slate-100">+{summaryData.recentEntries}</div>
            <p className="text-xs text-gray-600 dark:text-slate-400">en las últimas 24h</p>
          </CardContent>
        </Card>
        <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-900 dark:text-slate-100">Alertas</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900 dark:text-slate-100">{summaryData.alerts}</div>
            <p className="text-xs text-gray-600 dark:text-slate-400">requieren atención</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid lg:grid-cols-2 gap-6">
        <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
          <CardHeader>
            <CardTitle className="text-gray-900 dark:text-slate-100">Servicios Programados Recientemente</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentServices.map((service) => (
                <div key={service.id} className="flex items-center justify-between p-3 bg-white/30 dark:bg-slate-800/30 border border-gray-200 dark:border-slate-700 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900 dark:text-slate-100">{service.client}</p>
                    <p className="text-sm text-gray-600 dark:text-slate-400">{service.id}</p>
                  </div>
                  <Badge
                    className={
                      service.status === "En Ruta" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300" :
                      service.status === "Programado" ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300" :
                      "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                    }
                  >
                    {service.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
        <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20">
          <CardHeader>
            <CardTitle className="text-gray-900 dark:text-slate-100">Accesos Directos de Logística</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-2 gap-4">
            <Link href="/dashboard/logistica/calendario">
              <Button variant="outline" className="w-full h-20 flex flex-col">
                <Calendar className="h-6 w-6 mb-1 text-blue-600" />
                <span>Calendario</span>
              </Button>
            </Link>
            <Link href="/dashboard/logistica/proyectos">
              <Button variant="outline" className="w-full h-20 flex flex-col">
                <FolderOpen className="h-6 w-6 mb-1 text-yellow-600" />
                <span>Proyectos</span>
              </Button>
            </Link>
            <Link href="/dashboard/logistica/ingresos">
              <Button variant="outline" className="w-full h-20 flex flex-col">
                <FileClock className="h-6 w-6 mb-1 text-teal-600" />
                <span>Ingresos</span>
              </Button>
            </Link>
            <Link href="/dashboard/logistica/estadisticos">
              <Button variant="outline" className="w-full h-20 flex flex-col">
                <BarChart3 className="h-6 w-6 mb-1 text-indigo-600" />
                <span>Estadísticos</span>
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
