"use client"

import { useState } from "react"
import { CheckboxField } from "@/components/ui/form-styles"

export function SimpleCheckboxTest() {
  const [checked1, setChecked1] = useState(false)
  const [checked2, setChecked2] = useState(true)

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-orange-50 to-amber-100 dark:from-slate-900 dark:via-blue-900 dark:to-purple-900 p-8">
      <div className="max-w-2xl mx-auto space-y-6">
        
        <h1 className="text-2xl font-bold text-center mb-8">
          Prueba de Visibilidad de Checkboxes
        </h1>

        {/* Test con fondo similar al dashboard */}
        <div className="backdrop-blur-md border shadow-lg bg-white/25 dark:bg-white/10 border-white/30 dark:border-white/20 rounded-lg p-6">
          <h2 className="text-lg font-semibold mb-4">Checkboxes en Card Transparente</h2>
          
          <div className="space-y-4">
            <CheckboxField
              id="test-1"
              checked={checked1}
              onCheckedChange={setChecked1}
              label="Checkbox desmarcado (debe ser visible)"
              description="Este checkbox debe tener borde visible siempre"
            />
            
            <CheckboxField
              id="test-2"
              checked={checked2}
              onCheckedChange={setChecked2}
              label="Checkbox marcado"
              description="Este checkbox está marcado por defecto"
            />
          </div>
        </div>

        {/* Test con fondo blanco sólido */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border">
          <h2 className="text-lg font-semibold mb-4">Checkboxes en Fondo Sólido</h2>
          
          <div className="space-y-4">
            <CheckboxField
              id="test-3"
              checked={!checked1}
              onCheckedChange={(c) => setChecked1(!c)}
              label="Checkbox inverso"
              description="Se marca cuando el otro se desmarca"
            />
            
            <CheckboxField
              id="test-4"
              checked={!checked2}
              onCheckedChange={(c) => setChecked2(!c)}
              label="Otro checkbox inverso"
              description="También se invierte con el otro"
            />
          </div>
        </div>

        {/* Estado actual */}
        <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
          <h3 className="font-semibold mb-2">Estado Actual:</h3>
          <pre className="text-sm">
            Checkbox 1: {checked1 ? 'Marcado' : 'Desmarcado'}
            Checkbox 2: {checked2 ? 'Marcado' : 'Desmarcado'}
          </pre>
        </div>

        <div className="text-center text-sm text-gray-600 dark:text-gray-400">
          <p>Los checkboxes desmarcados deben tener bordes visibles</p>
          <p>sin necesidad de pasar el cursor por encima</p>
        </div>

      </div>
    </div>
  )
}
