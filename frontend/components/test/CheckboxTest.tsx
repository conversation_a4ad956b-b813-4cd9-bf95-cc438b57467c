"use client"

import { useState } from "react"
import { CheckboxField } from "@/components/ui/form-styles"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export function CheckboxTest() {
  const [customChecked, setCustomChecked] = useState(false)
  const [shadcnChecked, setShadcnChecked] = useState(false)
  const [multipleChecks, setMultipleChecks] = useState({
    option1: false,
    option2: true,
    option3: false,
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-orange-50 to-amber-100 dark:from-slate-900 dark:via-blue-900 dark:to-purple-900">
      <div className="p-6 space-y-6 max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Prueba de Visibilidad de Checkboxes</CardTitle>
        </CardHeader>
        <CardContent className="space-y-8">
          
          {/* Custom CheckboxField Component */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">CheckboxField Personalizado</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <CheckboxField
                id="custom-test-1"
                checked={customChecked}
                onCheckedChange={setCustomChecked}
                label="Checkbox personalizado"
                description="Este checkbox debería ser siempre visible"
              />
              
              <CheckboxField
                id="custom-test-2"
                checked={!customChecked}
                onCheckedChange={(checked) => setCustomChecked(!checked)}
                label="Checkbox inverso"
                description="Este se marca cuando el otro se desmarca"
              />
            </div>
          </div>

          {/* Shadcn Checkbox Component */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Checkbox de Shadcn/UI</h3>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="shadcn-test"
                checked={shadcnChecked}
                onCheckedChange={setShadcnChecked}
              />
              <label
                htmlFor="shadcn-test"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Checkbox básico de shadcn/ui
              </label>
            </div>
          </div>

          {/* Multiple Permission-like Checkboxes */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Simulación de Permisos</h3>
            <div className="space-y-3">
              <CheckboxField
                id="perm-1"
                checked={multipleChecks.option1}
                onCheckedChange={(checked) => 
                  setMultipleChecks(prev => ({ ...prev, option1: checked }))
                }
                label="sistemas:users:create"
                description="Crear usuarios del sistema"
              />
              
              <CheckboxField
                id="perm-2"
                checked={multipleChecks.option2}
                onCheckedChange={(checked) => 
                  setMultipleChecks(prev => ({ ...prev, option2: checked }))
                }
                label="sistemas:users:read"
                description="Ver usuarios del sistema"
              />
              
              <CheckboxField
                id="perm-3"
                checked={multipleChecks.option3}
                onCheckedChange={(checked) => 
                  setMultipleChecks(prev => ({ ...prev, option3: checked }))
                }
                label="sistemas:roles:update"
                description="Editar roles del sistema"
              />
            </div>
          </div>

          {/* Estado actual */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Estado Actual</h3>
            <div className="bg-muted/50 p-4 rounded-lg">
              <pre className="text-sm">
{JSON.stringify({
  customChecked,
  shadcnChecked,
  multipleChecks
}, null, 2)}
              </pre>
            </div>
          </div>

        </CardContent>
      </Card>

      {/* Test with dashboard-like background */}
      <Card className="backdrop-blur-md border shadow-lg bg-white/25 dark:bg-white/10 border-white/30 dark:border-white/20">
        <CardHeader>
          <CardTitle>Prueba con Fondo del Dashboard</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <CheckboxField
              id="dashboard-test-1"
              checked={multipleChecks.option1}
              onCheckedChange={(checked) =>
                setMultipleChecks(prev => ({ ...prev, option1: checked }))
              }
              label="Crear usuarios del sistema"
              description="sistemas:users:create"
            />

            <CheckboxField
              id="dashboard-test-2"
              checked={multipleChecks.option2}
              onCheckedChange={(checked) =>
                setMultipleChecks(prev => ({ ...prev, option2: checked }))
              }
              label="Ver usuarios del sistema"
              description="sistemas:users:read"
            />

            <CheckboxField
              id="dashboard-test-3"
              checked={multipleChecks.option3}
              onCheckedChange={(checked) =>
                setMultipleChecks(prev => ({ ...prev, option3: checked }))
              }
              label="Editar roles del sistema"
              description="sistemas:roles:update"
            />
          </div>
        </CardContent>
      </Card>

    </div>
    </div>
  )
}
