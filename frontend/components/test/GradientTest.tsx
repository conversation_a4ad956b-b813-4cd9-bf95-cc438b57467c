"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { useTheme } from "next-themes"
import { Sun, Moon } from "lucide-react"

export function GradientTest() {
  const { theme, setTheme } = useTheme()

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-orange-50 to-amber-100 dark:bg-gradient-to-br dark:from-slate-900 dark:via-blue-900 dark:to-purple-900">
      <div className="p-8 space-y-6">
        
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Prueba de Gradientes</h1>
          <Button
            variant="outline"
            size="icon"
            onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
          >
            {theme === "dark" ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
          </But<PERSON>>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          
          {/* Card con fondo transparente */}
          <Card className="backdrop-blur-md border shadow-lg bg-white/25 dark:bg-white/10 border-white/30 dark:border-white/20">
            <CardHeader>
              <CardTitle>Card Transparente</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Esta card tiene fondo transparente para mostrar el gradiente de fondo.
              </p>
              <div className="mt-4 space-y-2">
                <div className="h-2 bg-primary/20 rounded"></div>
                <div className="h-2 bg-primary/40 rounded"></div>
                <div className="h-2 bg-primary/60 rounded"></div>
              </div>
            </CardContent>
          </Card>

          {/* Card con fondo sólido */}
          <Card>
            <CardHeader>
              <CardTitle>Card Sólida</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Esta card tiene fondo sólido normal.
              </p>
              <div className="mt-4 space-y-2">
                <div className="h-2 bg-green-200 dark:bg-green-800 rounded"></div>
                <div className="h-2 bg-blue-200 dark:bg-blue-800 rounded"></div>
                <div className="h-2 bg-purple-200 dark:bg-purple-800 rounded"></div>
              </div>
            </CardContent>
          </Card>

        </div>

        <Card className="backdrop-blur-md border shadow-lg bg-white/25 dark:bg-white/10 border-white/30 dark:border-white/20">
          <CardHeader>
            <CardTitle>Información del Gradiente</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold mb-2">Modo Claro:</h3>
              <code className="text-sm bg-muted p-2 rounded block">
                bg-gradient-to-br from-yellow-50 via-orange-50 to-amber-100
              </code>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Modo Oscuro:</h3>
              <code className="text-sm bg-muted p-2 rounded block">
                dark:bg-gradient-to-br dark:from-slate-900 dark:via-blue-900 dark:to-purple-900
              </code>
            </div>
            <div className="pt-4 border-t">
              <p className="text-sm text-muted-foreground">
                <strong>Tema actual:</strong> {theme || 'system'}
              </p>
              <p className="text-sm text-muted-foreground mt-1">
                Cambia entre modo claro y oscuro para ver los diferentes gradientes.
              </p>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="h-20 bg-yellow-50 rounded-lg border flex items-center justify-center text-xs">
            yellow-50
          </div>
          <div className="h-20 bg-orange-50 rounded-lg border flex items-center justify-center text-xs">
            orange-50
          </div>
          <div className="h-20 bg-amber-100 rounded-lg border flex items-center justify-center text-xs">
            amber-100
          </div>
          <div className="h-20 bg-slate-900 text-white rounded-lg border flex items-center justify-center text-xs">
            slate-900
          </div>
          <div className="h-20 bg-blue-900 text-white rounded-lg border flex items-center justify-center text-xs">
            blue-900
          </div>
          <div className="h-20 bg-purple-900 text-white rounded-lg border flex items-center justify-center text-xs">
            purple-900
          </div>
        </div>

      </div>
    </div>
  )
}
