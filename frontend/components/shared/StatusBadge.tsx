import { Badge } from "@/components/ui/badge"

interface StatusBadgeProps {
  status: string
  variant?: "active" | "inactive" | "default"
}

export function StatusBadge({ status, variant }: StatusBadgeProps) {
  const getVariantStyles = () => {
    switch (variant || (status === "Activo" ? "active" : "inactive")) {
      case "active":
        return "bg-moka-lion/30 text-moka-bistre border border-moka-brown/40 hover:bg-moka-lion/40"
      case "inactive":
        return "bg-moka-falu/20 text-moka-falu border border-moka-falu/40 hover:bg-moka-falu/30"
      default:
        return "bg-moka-peach/40 text-moka-bistre border border-moka-brown/40"
    }
  }

  return (
    <Badge 
      variant={variant === "inactive" ? "destructive" : "secondary"} 
      className={getVariantStyles()}
    >
      {status}
    </Badge>
  )
}
