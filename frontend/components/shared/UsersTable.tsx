import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { MoreHorizontal } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { StatusBadge } from "./StatusBadge"
import { useAuth } from "@/components/providers/auth-provider"

interface User {
  id: number
  name: string
  email: string
  role: string
  status: string
  lastLogin: string
}

interface UsersTableProps {
  users: User[]
  isLoading?: boolean
  onUserAction?: (action: string, userId: number) => void
}

export function UsersTable({ users, isLoading, onUserAction }: UsersTableProps) {
  const { user: currentUser, hasRole } = useAuth()
  const isAdmin = hasRole('ROLE_ADMIN')

  const handleAction = (action: string, userId: number) => {
    if (onUserAction) {
      onUserAction(action, userId)
    }
  }

  const canEditUser = (targetUserId: number) => {
    return isAdmin && currentUser?.id !== targetUserId
  }

  const canDeleteUser = (targetUserId: number) => {
    return isAdmin && currentUser?.id !== targetUserId
  }

  if (isLoading) {
    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nombre</TableHead>
              <TableHead className="hidden md:table-cell">Email</TableHead>
              <TableHead>Rol</TableHead>
              <TableHead className="hidden lg:table-cell">Último Acceso</TableHead>
              <TableHead>Estado</TableHead>
              <TableHead>
                <span className="sr-only">Acciones</span>
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {[...Array(3)].map((_, index) => (
              <TableRow key={index}>
                <TableCell className="font-medium">
                  <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                </TableCell>
                <TableCell className="hidden md:table-cell">
                  <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                </TableCell>
                <TableCell>
                  <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                </TableCell>
                <TableCell className="hidden lg:table-cell">
                  <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                </TableCell>
                <TableCell>
                  <div className="h-6 w-16 bg-gray-200 rounded animate-pulse"></div>
                </TableCell>
                <TableCell>
                  <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    )
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Nombre</TableHead>
            <TableHead className="hidden md:table-cell">Email</TableHead>
            <TableHead>Rol</TableHead>
            <TableHead className="hidden lg:table-cell">Último Acceso</TableHead>
            <TableHead>Estado</TableHead>
            <TableHead>
              <span className="sr-only">Acciones</span>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.length > 0 ? (
            users.map((user) => (
              <TableRow key={user.id}>
                <TableCell className="font-medium">{user.name}</TableCell>
                <TableCell className="hidden md:table-cell">{user.email}</TableCell>
                <TableCell>{user.role}</TableCell>
                <TableCell className="hidden lg:table-cell">{user.lastLogin}</TableCell>
                <TableCell>
                  <StatusBadge status={user.status} />
                </TableCell>
                <TableCell>
                  {isAdmin ? (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Abrir menú</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                        {canEditUser(user.id) && (
                          <>
                            <DropdownMenuItem onClick={() => handleAction('edit', user.id)}>
                              Editar Usuario
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleAction('changeRole', user.id)}>
                              Cambiar Rol
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleAction('resetPassword', user.id)}>
                              Resetear Contraseña
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {user.status === "Activo" ? (
                              <DropdownMenuItem 
                                className="text-orange-600"
                                onClick={() => handleAction('deactivate', user.id)}
                              >
                                Desactivar Usuario
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem 
                                className="text-green-600"
                                onClick={() => handleAction('activate', user.id)}
                              >
                                Activar Usuario
                              </DropdownMenuItem>
                            )}
                          </>
                        )}
                        {canDeleteUser(user.id) && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              className="text-red-600"
                              onClick={() => handleAction('delete', user.id)}
                            >
                              Eliminar Usuario
                            </DropdownMenuItem>
                          </>
                        )}
                        {currentUser?.id === user.id && (
                          <DropdownMenuItem disabled className="text-gray-400">
                            No puedes editarte a ti mismo
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  ) : (
                    <span className="text-sm text-gray-400">Sin permisos</span>
                  )}
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={6} className="h-24 text-center">
                No se encontraron usuarios.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  )
}
