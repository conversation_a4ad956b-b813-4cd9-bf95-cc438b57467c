"use client"

import * as React from "react"
import { Check } from "lucide-react"
import { cn } from "@/lib/utils"

interface VisibleCheckboxProps {
  id?: string
  checked?: boolean
  onCheckedChange?: (checked: boolean) => void
  disabled?: boolean
  className?: string
  style?: React.CSSProperties
}

export const VisibleCheckbox = React.forwardRef<
  HTMLDivElement,
  VisibleCheckboxProps
>(({ id, checked = false, onCheckedChange, disabled = false, className, style, ...props }, ref) => {
  const handleClick = () => {
    if (!disabled && onCheckedChange) {
      onCheckedChange(!checked)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.key === ' ' || e.key === 'Enter') && !disabled && onCheckedChange) {
      e.preventDefault()
      onCheckedChange(!checked)
    }
  }

  return (
    <div
      ref={ref}
      id={id}
      role="checkbox"
      aria-checked={checked}
      tabIndex={disabled ? -1 : 0}
      className={cn(
        "h-5 w-5 shrink-0 rounded-sm border-2 transition-all duration-200 cursor-pointer",
        "flex items-center justify-center focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        "permission-checkbox", // Clase específica para permisos
        disabled && "cursor-not-allowed opacity-50",
        className
      )}
      style={{
        // Estilos que funcionan bien en ambos modos
        border: checked
          ? '2px solid #1f2937'
          : '2px solid #1f2937',
        backgroundColor: checked
          ? '#1f2937'
          : '#ffffff',
        boxShadow: checked
          ? '0 2px 8px 0 rgb(31 41 55 / 0.4), inset 0 1px 0 0 rgb(255 255 255 / 0.1)'
          : '0 1px 3px 0 rgb(0 0 0 / 0.1), inset 0 1px 0 0 rgb(255 255 255 / 0.8)',
        ...style
      }}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      {...props}
    >
      {checked && (
        <div
          className="font-bold text-sm flex items-center justify-center text-gray-500 dark:text-white"
          style={{
            fontSize: '14px',
            fontWeight: 'bold',
            textShadow: '0 1px 2px rgba(0,0,0,0.3)'
          }}
        >
          ✓
        </div>
      )}
    </div>
  )
})

VisibleCheckbox.displayName = "VisibleCheckbox"
