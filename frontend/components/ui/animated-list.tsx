"use client"

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { cn } from '@/lib/utils'

interface AnimatedListProps<T> {
  items: T[]
  onItemSelect?: (item: T, index: number) => void
  showGradients?: boolean
  enableArrowNavigation?: boolean
  displayScrollbar?: boolean
  className?: string
  itemClassName?: string
  renderItem?: (item: T, index: number, isSelected: boolean) => React.ReactNode
}

export default function AnimatedList<T>({
  items,
  onItemSelect,
  showGradients = true,
  enableArrowNavigation = true,
  displayScrollbar = false,
  className,
  itemClassName,
  renderItem
}: AnimatedListProps<T>) {
  const [selectedIndex, setSelectedIndex] = useState<number>(-1)
  const [visibleItems, setVisibleItems] = useState<T[]>([])
  const listRef = useRef<HTMLDivElement>(null)
  const itemRefs = useRef<(HTMLDivElement | null)[]>([])

  // Animate items in
  useEffect(() => {
    const timer = setTimeout(() => {
      setVisibleItems(items)
    }, 100)
    return () => clearTimeout(timer)
  }, [items])

  // Handle keyboard navigation
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!enableArrowNavigation || items.length === 0) return

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault()
        setSelectedIndex(prev => {
          const newIndex = prev < items.length - 1 ? prev + 1 : 0
          return newIndex
        })
        break
      case 'ArrowUp':
        event.preventDefault()
        setSelectedIndex(prev => {
          const newIndex = prev > 0 ? prev - 1 : items.length - 1
          return newIndex
        })
        break
      case 'Enter':
        if (selectedIndex >= 0 && onItemSelect) {
          onItemSelect(items[selectedIndex], selectedIndex)
        }
        break
      case 'Escape':
        setSelectedIndex(-1)
        break
    }
  }, [enableArrowNavigation, items, selectedIndex, onItemSelect])

  useEffect(() => {
    if (enableArrowNavigation) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [enableArrowNavigation, handleKeyDown])

  // Scroll selected item into view
  useEffect(() => {
    if (selectedIndex >= 0 && itemRefs.current[selectedIndex]) {
      itemRefs.current[selectedIndex]?.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest'
      })
    }
  }, [selectedIndex])

  const handleItemClick = (item: T, index: number) => {
    setSelectedIndex(index)
    onItemSelect?.(item, index)
  }

  const defaultRenderItem = (item: T, index: number, isSelected: boolean) => (
    <div
      className={cn(
        "px-3 py-2 text-sm transition-all duration-200 cursor-pointer rounded-lg",
        isSelected 
          ? "bg-moka-falu/20 text-moka-falu dark:bg-red-500/20 dark:text-red-400" 
          : "hover:bg-muted/50",
        itemClassName
      )}
    >
      {String(item)}
    </div>
  )

  return (
    <div className={cn("relative", className)}>
      {/* Top gradient */}
      {showGradients && (
        <div className="absolute top-0 left-0 right-0 h-4 bg-gradient-to-b from-background to-transparent z-10 pointer-events-none" />
      )}
      
      {/* List container */}
      <div
        ref={listRef}
        className={cn(
          "overflow-y-auto space-y-1 p-2",
          !displayScrollbar && "scrollbar-hide",
          showGradients && "py-4"
        )}
        style={{ maxHeight: '300px' }}
      >
        {visibleItems.map((item, index) => {
          const isSelected = selectedIndex === index
          return (
            <div
              key={index}
              ref={el => itemRefs.current[index] = el}
              onClick={() => handleItemClick(item, index)}
              className={cn(
                "transform transition-all duration-300 ease-out",
                "animate-in slide-in-from-left-2 fade-in-0"
              )}
              style={{
                animationDelay: `${index * 50}ms`,
                animationFillMode: 'both'
              }}
            >
              {renderItem ? renderItem(item, index, isSelected) : defaultRenderItem(item, index, isSelected)}
            </div>
          )
        })}
      </div>

      {/* Bottom gradient */}
      {showGradients && (
        <div className="absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-t from-background to-transparent z-10 pointer-events-none" />
      )}
    </div>
  )
}
