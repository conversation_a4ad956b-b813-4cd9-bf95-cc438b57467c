"use client"

import React, { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, CheckCircle, AlertCircle, Info, AlertTriangle, User, UserCheck, UserX, Shield, Trash2, Edit, Key, Users, Settings, Lock } from 'lucide-react'
import { cn } from '@/lib/utils'

export type NotificationType = 'success' | 'error' | 'warning' | 'info'
export type UserActionType = 'created' | 'updated' | 'activated' | 'deactivated' | 'blocked' | 'deleted' | 'password_changed'
export type RoleActionType = 'role_created' | 'role_updated' | 'role_deleted' | 'permissions_assigned'

export interface NotificationData {
  id: string
  type: NotificationType
  title: string
  message: string
  userAction?: UserActionType
  roleAction?: RoleActionType
  userName?: string
  roleName?: string
  duration?: number
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center'
}

const typeConfig = {
  success: {
    icon: CheckCircle,
    bgColor: 'bg-green-500/20 dark:bg-green-400/20',
    borderColor: 'border-green-500/30 dark:border-green-400/30',
    iconColor: 'text-green-600 dark:text-green-400',
    titleColor: 'text-green-800 dark:text-green-300'
  },
  error: {
    icon: AlertCircle,
    bgColor: 'bg-red-500/20 dark:bg-red-400/20',
    borderColor: 'border-red-500/30 dark:border-red-400/30',
    iconColor: 'text-red-600 dark:text-red-400',
    titleColor: 'text-red-800 dark:text-red-300'
  },
  warning: {
    icon: AlertTriangle,
    bgColor: 'bg-orange-500/20 dark:bg-orange-400/20',
    borderColor: 'border-orange-500/30 dark:border-orange-400/30',
    iconColor: 'text-orange-600 dark:text-orange-400',
    titleColor: 'text-orange-800 dark:text-orange-300'
  },
  info: {
    icon: Info,
    bgColor: 'bg-blue-500/20 dark:bg-blue-400/20',
    borderColor: 'border-blue-500/30 dark:border-blue-400/30',
    iconColor: 'text-blue-600 dark:text-blue-400',
    titleColor: 'text-blue-800 dark:text-blue-300'
  }
}

const userActionConfig = {
  created: { icon: User, color: 'text-green-600 dark:text-green-400' },
  updated: { icon: Edit, color: 'text-blue-600 dark:text-blue-400' },
  activated: { icon: UserCheck, color: 'text-green-600 dark:text-green-400' },
  deactivated: { icon: UserX, color: 'text-orange-600 dark:text-orange-400' },
  blocked: { icon: Shield, color: 'text-red-600 dark:text-red-400' },
  deleted: { icon: Trash2, color: 'text-red-600 dark:text-red-400' },
  password_changed: { icon: Key, color: 'text-purple-600 dark:text-purple-400' }
}

const roleActionConfig = {
  role_created: { icon: Users, color: 'text-green-600 dark:text-green-400' },
  role_updated: { icon: Edit, color: 'text-blue-600 dark:text-blue-400' },
  role_deleted: { icon: Trash2, color: 'text-red-600 dark:text-red-400' },
  permissions_assigned: { icon: Lock, color: 'text-purple-600 dark:text-purple-400' }
}

const positionClasses = {
  'top-right': 'top-4 right-4',
  'top-left': 'top-4 left-4',
  'bottom-right': 'bottom-4 right-4',
  'bottom-left': 'bottom-4 left-4',
  'top-center': 'top-4 left-1/2 -translate-x-1/2',
  'bottom-center': 'bottom-4 left-1/2 -translate-x-1/2'
}

interface NotificationToastProps {
  notification: NotificationData
  onClose: (id: string) => void
}

export function NotificationToast({ notification, onClose }: NotificationToastProps) {
  const [isVisible, setIsVisible] = useState(true)
  const [progress, setProgress] = useState(100)
  
  const config = typeConfig[notification.type]
  const userActionIcon = notification.userAction ? userActionConfig[notification.userAction] : null
  const roleActionIcon = notification.roleAction ? roleActionConfig[notification.roleAction] : null
  const duration = notification.duration || 5000
  const position = notification.position || 'top-right'

  useEffect(() => {
    const timer = setTimeout(() => {
      handleClose()
    }, duration)

    // Progress bar animation
    const progressTimer = setInterval(() => {
      setProgress(prev => {
        const decrement = 100 / (duration / 100)
        return Math.max(0, prev - decrement)
      })
    }, 100)

    return () => {
      clearTimeout(timer)
      clearInterval(progressTimer)
    }
  }, [duration])

  const handleClose = () => {
    setIsVisible(false)
    setTimeout(() => onClose(notification.id), 300)
  }

  const slideDirection = position.includes('right') ? 'right' : 
                        position.includes('left') ? 'left' : 'center'

  const getInitialX = () => {
    switch (slideDirection) {
      case 'right': return 400
      case 'left': return -400
      default: return 0
    }
  }

  const getInitialY = () => {
    return position.includes('top') ? -100 : 100
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ 
            x: getInitialX(), 
            y: getInitialY(), 
            opacity: 0, 
            scale: 0.8,
            rotateX: -15
          }}
          animate={{ 
            x: 0, 
            y: 0, 
            opacity: 1, 
            scale: 1,
            rotateX: 0
          }}
          exit={{ 
            x: getInitialX(), 
            y: getInitialY(), 
            opacity: 0, 
            scale: 0.8,
            rotateX: 15
          }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 30,
            mass: 0.8
          }}
          className={cn(
            "fixed z-50 w-full max-w-sm mx-4 sm:mx-0 sm:w-96",
            "backdrop-blur-xl bg-white/80 dark:bg-slate-900/80",
            "border border-white/20 dark:border-slate-700/50",
            "rounded-2xl shadow-2xl shadow-black/10 dark:shadow-black/30",
            "overflow-hidden group hover:scale-[1.02] transition-transform duration-200",
            positionClasses[position],
            config.borderColor
          )}
          style={{
            background: `linear-gradient(135deg, 
              rgba(255, 255, 255, 0.1) 0%, 
              rgba(255, 255, 255, 0.05) 100%
            )`,
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)'
          }}
        >
          {/* Progress bar */}
          <div className="absolute top-0 left-0 h-1 bg-gradient-to-r from-transparent via-white/30 to-transparent">
            <motion.div
              className={cn("h-full rounded-full", config.bgColor)}
              initial={{ width: "100%" }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.1, ease: "linear" }}
            />
          </div>

          <div className="p-4">
            <div className="flex items-start gap-3">
              {/* Icon container */}
              <div className={cn(
                "flex-shrink-0 p-2 rounded-xl",
                config.bgColor,
                "ring-1 ring-white/20 dark:ring-slate-700/50"
              )}>
                <config.icon className={cn("w-5 h-5", config.iconColor)} />
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className={cn("font-semibold text-sm", config.titleColor)}>
                    {notification.title}
                  </h4>
                  {userActionIcon && (
                    <userActionIcon.icon className={cn("w-4 h-4", userActionIcon.color)} />
                  )}
                  {roleActionIcon && (
                    <roleActionIcon.icon className={cn("w-4 h-4", roleActionIcon.color)} />
                  )}
                </div>
                
                <p className="text-sm text-gray-700 dark:text-slate-300 leading-relaxed">
                  {notification.message}
                </p>

                {notification.userName && (
                  <div className="mt-2 px-2 py-1 rounded-lg bg-white/30 dark:bg-slate-800/30 border border-white/20 dark:border-slate-700/30">
                    <span className="text-xs font-medium text-gray-600 dark:text-slate-400">
                      Usuario: <span className="text-gray-800 dark:text-slate-200">{notification.userName}</span>
                    </span>
                  </div>
                )}

                {notification.roleName && (
                  <div className="mt-2 px-2 py-1 rounded-lg bg-white/30 dark:bg-slate-800/30 border border-white/20 dark:border-slate-700/30">
                    <span className="text-xs font-medium text-gray-600 dark:text-slate-400">
                      Rol: <span className="text-gray-800 dark:text-slate-200">{notification.roleName}</span>
                    </span>
                  </div>
                )}
              </div>

              {/* Close button */}
              <button
                onClick={handleClose}
                className="flex-shrink-0 p-1 rounded-lg hover:bg-white/20 dark:hover:bg-slate-800/30 transition-colors duration-200 group"
              >
                <X className="w-4 h-4 text-gray-500 dark:text-slate-400 group-hover:text-gray-700 dark:group-hover:text-slate-200" />
              </button>
            </div>
          </div>

          {/* Glassmorphism overlay */}
          <div 
            className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
            style={{
              background: `linear-gradient(135deg, 
                rgba(255, 255, 255, 0.1) 0%, 
                transparent 50%, 
                rgba(255, 255, 255, 0.05) 100%
              )`
            }}
          />
        </motion.div>
      )}
    </AnimatePresence>
  )
}
