"use client"

import React, { create<PERSON>ontext, useContext, useState, useC<PERSON>back, ReactNode, useEffect } from 'react'
import { NotificationToast, NotificationData, NotificationType, UserActionType, RoleActionType } from './notification-toast'

interface NotificationContextType {
  notifications: NotificationData[]
  addNotification: (notification: Omit<NotificationData, 'id'>) => void
  removeNotification: (id: string) => void
  clearAll: () => void
  // Helpers específicos para acciones de usuarios
  notifyUserAction: (action: UserActionType, userName: string, details?: string) => void
  // Helpers específicos para acciones de roles
  notifyRoleAction: (action: RoleActionType, roleName: string, details?: string) => void
  notifySuccess: (title: string, message: string, userName?: string) => void
  notifyError: (title: string, message: string, userName?: string) => void
  notifyWarning: (title: string, message: string, userName?: string) => void
  notifyInfo: (title: string, message: string, userName?: string) => void
  notifyLogout: (userName?: string) => void
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined)

interface NotificationProviderProps {
  children: ReactNode
  maxNotifications?: number
  defaultPosition?: NotificationData['position']
  defaultDuration?: number
}

export function NotificationProvider({
  children,
  maxNotifications = 5,
  defaultPosition = 'top-right',
  defaultDuration = 5000
}: NotificationProviderProps) {
  const [notifications, setNotifications] = useState<NotificationData[]>([])
  const [isMobile, setIsMobile] = useState(false)

  // Detectar si es móvil para ajustar posición
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Ajustar posición automáticamente según el dispositivo
  const getOptimalPosition = (requestedPosition?: NotificationData['position']): NotificationData['position'] => {
    if (requestedPosition) return requestedPosition
    return isMobile ? 'top-center' : defaultPosition
  }

  const addNotification = useCallback((notification: Omit<NotificationData, 'id'>) => {
    const id = `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const newNotification: NotificationData = {
      id,
      position: getOptimalPosition(notification.position),
      duration: defaultDuration,
      ...notification
    }

    setNotifications(prev => {
      const updated = [newNotification, ...prev]
      // Limitar el número máximo de notificaciones
      return updated.slice(0, maxNotifications)
    })
  }, [defaultPosition, defaultDuration, maxNotifications, getOptimalPosition])

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id))
  }, [])

  const clearAll = useCallback(() => {
    setNotifications([])
  }, [])

  // Helper específico para acciones de usuarios
  const notifyUserAction = useCallback((action: UserActionType, userName: string, details?: string) => {
    const actionMessages = {
      created: {
        title: 'Usuario Creado',
        message: details || `El usuario ha sido creado exitosamente.`,
        type: 'success' as NotificationType
      },
      updated: {
        title: 'Usuario Actualizado',
        message: details || `La información del usuario ha sido actualizada.`,
        type: 'success' as NotificationType
      },
      activated: {
        title: 'Usuario Activado',
        message: details || `El usuario ha sido activado y puede acceder al sistema.`,
        type: 'success' as NotificationType
      },
      deactivated: {
        title: 'Usuario Desactivado',
        message: details || `El usuario ha sido desactivado temporalmente.`,
        type: 'warning' as NotificationType
      },
      blocked: {
        title: 'Usuario Bloqueado',
        message: details || `El usuario ha sido bloqueado por seguridad.`,
        type: 'error' as NotificationType
      },
      deleted: {
        title: 'Usuario Eliminado',
        message: details || `El usuario ha sido eliminado permanentemente.`,
        type: 'error' as NotificationType
      },
      password_changed: {
        title: 'Contraseña Cambiada',
        message: details || `La contraseña del usuario ha sido actualizada.`,
        type: 'info' as NotificationType
      }
    }

    const config = actionMessages[action]
    addNotification({
      ...config,
      userAction: action,
      userName,
      duration: action === 'deleted' ? 7000 : defaultDuration // Más tiempo para acciones críticas
    })
  }, [addNotification, defaultDuration])

  // Helper específico para acciones de roles
  const notifyRoleAction = useCallback((action: RoleActionType, roleName: string, details?: string) => {
    const actionMessages = {
      role_created: {
        title: 'Rol Creado',
        message: details || `El rol ha sido creado exitosamente.`,
        type: 'success' as NotificationType
      },
      role_updated: {
        title: 'Rol Actualizado',
        message: details || `La información del rol ha sido actualizada.`,
        type: 'success' as NotificationType
      },
      role_deleted: {
        title: 'Rol Eliminado',
        message: details || `El rol ha sido eliminado permanentemente.`,
        type: 'error' as NotificationType
      },
      permissions_assigned: {
        title: 'Permisos Asignados',
        message: details || `Los permisos han sido asignados al rol correctamente.`,
        type: 'success' as NotificationType
      }
    }

    const config = actionMessages[action]
    addNotification({
      ...config,
      roleAction: action,
      roleName,
      duration: action === 'role_deleted' ? 7000 : defaultDuration // Más tiempo para eliminaciones
    })
  }, [addNotification, defaultDuration])

  // Helpers para tipos específicos de notificaciones
  const notifySuccess = useCallback((title: string, message: string, userName?: string) => {
    addNotification({
      type: 'success',
      title,
      message,
      userName
    })
  }, [addNotification])

  const notifyError = useCallback((title: string, message: string, userName?: string) => {
    addNotification({
      type: 'error',
      title,
      message,
      userName,
      duration: 7000 // Más tiempo para errores
    })
  }, [addNotification])

  const notifyWarning = useCallback((title: string, message: string, userName?: string) => {
    addNotification({
      type: 'warning',
      title,
      message,
      userName
    })
  }, [addNotification])

  const notifyInfo = useCallback((title: string, message: string, userName?: string) => {
    addNotification({
      type: 'info',
      title,
      message,
      userName
    })
  }, [addNotification])

  // Helper específico para logout
  const notifyLogout = useCallback((userName?: string) => {
    addNotification({
      type: 'info',
      title: 'Sesión Cerrada',
      message: `¡Hasta pronto${userName ? `, ${userName}` : ''}! Tu sesión se ha cerrado correctamente.`,
      userName,
      duration: 2000, // Más corto para logout
      position: getOptimalPosition('top-center') // Siempre centrado para logout
    })
  }, [addNotification, getOptimalPosition])

  const value: NotificationContextType = {
    notifications,
    addNotification,
    removeNotification,
    clearAll,
    notifyUserAction,
    notifyRoleAction,
    notifySuccess,
    notifyError,
    notifyWarning,
    notifyInfo,
    notifyLogout
  }

  return (
    <NotificationContext.Provider value={value}>
      {children}
      
      {/* Render notifications */}
      <div className="fixed inset-0 pointer-events-none z-50">
        {notifications.map(notification => (
          <NotificationToast
            key={notification.id}
            notification={notification}
            onClose={removeNotification}
          />
        ))}
      </div>
    </NotificationContext.Provider>
  )
}

export function useNotifications() {
  const context = useContext(NotificationContext)
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider')
  }
  return context
}

// Hook específico para acciones de usuarios
export function useUserNotifications() {
  const { notifyUserAction, notifyError } = useNotifications()
  
  return {
    notifyUserCreated: (userName: string, details?: string) => 
      notifyUserAction('created', userName, details),
    
    notifyUserUpdated: (userName: string, details?: string) => 
      notifyUserAction('updated', userName, details),
    
    notifyUserActivated: (userName: string, details?: string) => 
      notifyUserAction('activated', userName, details),
    
    notifyUserDeactivated: (userName: string, details?: string) => 
      notifyUserAction('deactivated', userName, details),
    
    notifyUserBlocked: (userName: string, details?: string) => 
      notifyUserAction('blocked', userName, details),
    
    notifyUserDeleted: (userName: string, details?: string) => 
      notifyUserAction('deleted', userName, details),
    
    notifyPasswordChanged: (userName: string, details?: string) => 
      notifyUserAction('password_changed', userName, details),
    
    notifyUserError: (userName: string, error: string) =>
      notifyError('Error de Usuario', error, userName)
  }
}

// Hook específico para acciones de roles
export function useRoleNotifications() {
  const { notifyRoleAction, notifyError } = useNotifications()

  return {
    notifyRoleCreated: (roleName: string, details?: string) =>
      notifyRoleAction('role_created', roleName, details),

    notifyRoleUpdated: (roleName: string, details?: string) =>
      notifyRoleAction('role_updated', roleName, details),

    notifyRoleDeleted: (roleName: string, details?: string) =>
      notifyRoleAction('role_deleted', roleName, details),

    notifyPermissionsAssigned: (roleName: string, details?: string) =>
      notifyRoleAction('permissions_assigned', roleName, details),

    notifyRoleError: (roleName: string, error: string) =>
      notifyError('Error de Rol', error, roleName)
  }
}
