import { cn } from "@/lib/utils"
import { ReactNode } from "react"
import { VisibleCheckbox } from './visible-checkbox'

// ============================================================================
// FORM LAYOUT COMPONENTS
// ============================================================================

interface FormSectionProps {
  title: string
  description?: string
  children: ReactNode
  className?: string
}

export function FormSection({ title, description, children, className }: FormSectionProps) {
  return (
    <div className={cn("space-y-4", className)}>
      <div className="space-y-1">
        <h3 className="text-lg font-medium leading-none tracking-tight text-moka-bistre dark:text-foreground">
          {title}
        </h3>
        {description && (
          <p className="text-sm text-moka-brown/80 dark:text-muted-foreground leading-relaxed">
            {description}
          </p>
        )}
      </div>
      <div className="space-y-4 pl-1">
        {children}
      </div>
    </div>
  )
}

interface FormGroupProps {
  children: ReactNode
  className?: string
}

export function FormGroup({ children, className }: FormGroupProps) {
  return (
    <div className={cn("grid grid-cols-1 sm:grid-cols-2 gap-4", className)}>
      {children}
    </div>
  )
}

// ============================================================================
// CHECKBOX COMPONENTS WITH IMPROVED STYLING
// ============================================================================

interface CheckboxFieldProps {
  id: string
  checked: boolean
  onCheckedChange: (checked: boolean) => void
  label: string
  description?: string
  disabled?: boolean
  className?: string
}

export function CheckboxField({
  id,
  checked,
  onCheckedChange,
  label,
  description,
  disabled = false,
  className
}: CheckboxFieldProps) {
  return (
    <div className={cn(
      "flex items-start space-x-3 p-4 rounded-lg border transition-all duration-200",
      checked
        ? "border-moka-falu/50 bg-moka-peach/20 shadow-sm dark:border-primary/60 dark:bg-primary/10"
        : "border-moka-brown/40 bg-moka-peach/10 hover:border-moka-brown/60 hover:bg-moka-peach/20 dark:border-white/40 dark:bg-white/5 dark:hover:border-white/50 dark:hover:bg-white/10",
      disabled && "opacity-50 cursor-not-allowed",
      className
    )}>
      <div className="flex items-center h-6 pt-0.5">
        <VisibleCheckbox
          id={id}
          checked={checked}
          onCheckedChange={onCheckedChange}
          disabled={disabled}
        />
      </div>
      <div className="grid gap-1.5 leading-none flex-1">
        <label
          htmlFor={id}
          className={cn(
            "text-sm font-normal leading-none cursor-pointer select-none text-moka-bistre dark:text-foreground",
            checked && "text-moka-bistre dark:text-foreground font-medium",
            disabled && "cursor-not-allowed opacity-70"
          )}
          onClick={() => !disabled && onCheckedChange(!checked)}
        >
          {label}
        </label>
        {description && (
          <p className="text-xs text-moka-brown/80 dark:text-muted-foreground leading-relaxed">
            {description}
          </p>
        )}
      </div>
    </div>
  )
}

// ============================================================================
// CARD VARIANTS
// ============================================================================

interface FormCardProps {
  children: ReactNode
  className?: string
}

export function FormCard({ children, className }: FormCardProps) {
  return (
    <div className={cn(
      "rounded-lg border border-white/20 bg-white/60 text-gray-900 shadow-sm backdrop-blur-md",
      "dark:border-slate-700/20 dark:bg-slate-800/60 dark:text-slate-100",
      "p-6 space-y-6",
      className
    )}>
      {children}
    </div>
  )
}

interface SelectableCardProps {
  children: ReactNode
  selected?: boolean
  onClick?: () => void
  className?: string
}

export function SelectableCard({ children, selected = false, onClick, className }: SelectableCardProps) {
  return (
    <div
      className={cn(
        "rounded-lg border transition-all duration-200 cursor-pointer backdrop-blur-sm",
        "p-4 space-y-2",
        selected
          ? "border-moka-falu/50 bg-moka-peach/20 shadow-sm ring-1 ring-moka-falu/20 dark:border-primary dark:bg-primary/5 dark:ring-primary/20"
          : "border-moka-brown/30 bg-moka-peach/10 hover:border-moka-brown/50 hover:bg-moka-peach/15 dark:border-border dark:bg-card dark:hover:border-border/80 dark:hover:bg-accent/50",
        onClick && "cursor-pointer",
        className
      )}
      onClick={onClick}
    >
      {children}
    </div>
  )
}

// ============================================================================
// UTILITY CLASSES
// ============================================================================

export const formStyles = {
  // Spacing
  section: "space-y-6",
  group: "space-y-4",
  field: "space-y-2",

  // Typography - Usando colores neutros para mejor contraste
  title: "text-lg font-medium leading-none tracking-tight text-gray-900 dark:text-slate-100",
  subtitle: "text-sm text-gray-600 dark:text-slate-400 leading-relaxed",
  label: "text-sm font-normal leading-none text-gray-900 dark:text-slate-100",
  description: "text-xs text-gray-600 dark:text-slate-400 leading-relaxed",

  // Layout
  grid: "grid grid-cols-1 sm:grid-cols-2 gap-4",
  gridThree: "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",

  // Interactive elements - Usando estilo blanco consistente
  card: "rounded-lg border border-gray-200 bg-white/30 p-4 transition-colors backdrop-blur-sm dark:border-slate-700 dark:bg-slate-800/30",
  cardHover: "hover:border-gray-300 hover:bg-white/40 dark:hover:border-slate-600 dark:hover:bg-slate-800/40",
  cardSelected: "border-moka-falu/50 bg-white/50 ring-1 ring-moka-falu/20 dark:border-primary dark:bg-slate-800/50 dark:ring-primary/20",

  // Buttons
  buttonGroup: "flex items-center gap-2",

  // Containers
  scrollArea: "max-h-[60vh] overflow-y-auto pr-2",

  // Form inputs - Clases reutilizables para inputs limpios
  input: "bg-white/30 border-gray-200 text-gray-900 placeholder:text-gray-500 dark:bg-slate-800/30 dark:border-slate-700 dark:text-slate-100 dark:placeholder:text-slate-400",
  textarea: "bg-white/30 border-gray-200 text-gray-900 placeholder:text-gray-500 dark:bg-slate-800/30 dark:border-slate-700 dark:text-slate-100 dark:placeholder:text-slate-400",
  select: "bg-white/30 border-gray-200 text-gray-900 dark:bg-slate-800/30 dark:border-slate-700 dark:text-slate-100",
  selectContent: "bg-white border-gray-200 dark:bg-slate-800 dark:border-slate-700",
  selectItem: "text-gray-900 dark:text-slate-100",
  button: "bg-white/30 border-gray-200 text-gray-900 hover:bg-white/40 dark:bg-slate-800/30 dark:border-slate-700 dark:text-slate-100 dark:hover:bg-slate-800/40",
  popover: "bg-white border-gray-200 dark:bg-slate-800 dark:border-slate-700",
  calendar: "text-gray-900 dark:text-slate-100",

  // Status indicators - Usando colores moka
  badge: "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-normal",
  badgeSecondary: "bg-moka-lion/20 text-moka-bistre border border-moka-brown/30 dark:bg-secondary dark:text-secondary-foreground",
  badgeSuccess: "bg-moka-lion/30 text-moka-bistre border border-moka-brown/40 dark:bg-green-900 dark:text-green-300",
  badgeWarning: "bg-moka-peach/40 text-moka-bistre border border-moka-brown/40 dark:bg-yellow-900 dark:text-yellow-300",

  // Enhanced checkbox styles - Usando colores moka
  checkboxContainer: "flex items-start space-x-3 p-4 rounded-lg border transition-all duration-200",
  checkboxContainerChecked: "border-moka-falu/50 bg-moka-peach/20 shadow-sm dark:border-primary/60 dark:bg-primary/10",
  checkboxContainerUnchecked: "border-moka-brown/40 bg-moka-peach/10 hover:border-moka-brown/60 hover:bg-moka-peach/20 dark:border-white/40 dark:bg-white/5 dark:hover:border-white/50 dark:hover:bg-white/10",

  // Permission specific styles - Usando colores moka
  permissionCard: "p-4 rounded-lg border border-moka-brown/30 bg-moka-peach/10 hover:border-moka-brown/50 hover:bg-moka-peach/15 transition-all duration-200 backdrop-blur-sm dark:border-white/40 dark:bg-white/5 dark:hover:border-white/50 dark:hover:bg-white/10",
  permissionCardSelected: "border-moka-falu/50 bg-moka-peach/20 shadow-sm dark:border-primary/60 dark:bg-primary/10",

  // Module section styles - Usando colores moka
  moduleHeader: "flex items-center gap-2 pb-3 border-b border-moka-brown/30 dark:border-border/50",
  moduleIcon: "p-1.5 rounded-md bg-moka-peach/30 text-moka-bistre dark:bg-primary/10 dark:text-primary",
  moduleTitle: "text-base font-medium text-moka-bistre dark:text-foreground",
  moduleDescription: "text-sm text-moka-brown/80 dark:text-muted-foreground mt-1",
}

// ============================================================================
// PERMISSION STATS COMPONENT
// ============================================================================

interface PermissionStatsProps {
  selectedCount: number
  totalCount: number
  className?: string
}

export function PermissionStats({ selectedCount, totalCount, className }: PermissionStatsProps) {
  const percentage = totalCount > 0 ? Math.round((selectedCount / totalCount) * 100) : 0

  return (
    <div className={cn("flex items-center gap-4 text-sm", className)}>
      <div className="flex items-center gap-2">
        <span className="text-gray-600 dark:text-slate-400">Seleccionados:</span>
        <span className="font-medium text-gray-900 dark:text-slate-100">
          {selectedCount} de {totalCount}
        </span>
      </div>
      <div className="flex items-center gap-2">
        <div className="w-16 h-2 bg-gray-200 dark:bg-slate-700 rounded-full overflow-hidden">
          <div
            className="h-full bg-blue-600 dark:bg-blue-500 transition-all duration-300 ease-out"
            style={{ width: `${percentage}%` }}
          />
        </div>
        <span className="text-xs text-gray-600 dark:text-slate-400 font-medium">
          {percentage}%
        </span>
      </div>
    </div>
  )
}

// ============================================================================
// CLEAN FORM COMPONENTS - REUTILIZABLES
// ============================================================================

import { Input } from "./input"
import { Textarea } from "./textarea"
import { Select, SelectContent, SelectItem, SelectTrigger } from "./select"
import { Button } from "./button"
import { PopoverContent } from "./popover"
import { Calendar } from "./calendar"

// Input limpio reutilizable
export function CleanInput({ className, ...props }: React.ComponentProps<typeof Input>) {
  return (
    <Input
      className={cn(formStyles.input, className)}
      {...props}
    />
  )
}

// Textarea limpio reutilizable
export function CleanTextarea({ className, ...props }: React.ComponentProps<typeof Textarea>) {
  return (
    <Textarea
      className={cn(formStyles.textarea, className)}
      {...props}
    />
  )
}

// Select limpio reutilizable
export function CleanSelect({ children, ...props }: React.ComponentProps<typeof Select>) {
  return <Select {...props}>{children}</Select>
}

export function CleanSelectTrigger({ className, ...props }: React.ComponentProps<typeof SelectTrigger>) {
  return (
    <SelectTrigger
      className={cn(formStyles.select, className)}
      {...props}
    />
  )
}

export function CleanSelectContent({ className, ...props }: React.ComponentProps<typeof SelectContent>) {
  return (
    <SelectContent
      className={cn(formStyles.selectContent, className)}
      {...props}
    />
  )
}

export function CleanSelectItem({ className, ...props }: React.ComponentProps<typeof SelectItem>) {
  return (
    <SelectItem
      className={cn(formStyles.selectItem, className)}
      {...props}
    />
  )
}

// Button limpio reutilizable (para date pickers, etc)
export function CleanButton({ className, variant = "outline", ...props }: React.ComponentProps<typeof Button>) {
  return (
    <Button
      variant={variant}
      className={cn(formStyles.button, className)}
      {...props}
    />
  )
}

// Popover limpio reutilizable
export function CleanPopoverContent({ className, ...props }: React.ComponentProps<typeof PopoverContent>) {
  return (
    <PopoverContent
      className={cn(formStyles.popover, className)}
      {...props}
    />
  )
}

// Calendar limpio reutilizable
export function CleanCalendar({ className, ...props }: React.ComponentProps<typeof Calendar>) {
  return (
    <Calendar
      className={cn(formStyles.calendar, className)}
      {...props}
    />
  )
}
