import React from 'react';
import { Check, <PERSON>, <PERSON>, <PERSON>Off, RefreshCw, HelpCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { PasswordRequirement, generateSecurePassword, generateExamplePasswords } from '@/lib/utils/password-policy';

interface PasswordRequirementsProps {
  requirements: PasswordRequirement[];
  className?: string;
}

export const PasswordRequirements: React.FC<PasswordRequirementsProps> = ({
  requirements,
  className,
}) => {
  const validCount = requirements.filter(req => req.isValid).length;
  const totalCount = requirements.length;
  const progressPercentage = totalCount > 0 ? (validCount / totalCount) * 100 : 0;

  return (
    <div className={cn('space-y-3', className)}>
      <div className="flex items-center justify-between">
        <p className="text-sm font-medium text-gray-700">Requisitos de contraseña:</p>
        <span className="text-xs text-gray-500">
          {validCount}/{totalCount} completados
        </span>
      </div>

      {/* Barra de progreso */}
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={cn(
            "h-2 rounded-full transition-all duration-300",
            progressPercentage === 100 ? "bg-green-500" :
            progressPercentage >= 50 ? "bg-yellow-500" : "bg-red-500"
          )}
          style={{ width: `${progressPercentage}%` }}
        />
      </div>

      <div className="space-y-1">
        {requirements.map((requirement) => (
          <div
            key={requirement.id}
            className="flex items-center gap-2 text-sm"
          >
            {requirement.isValid ? (
              <Check className="w-4 h-4 text-green-500 flex-shrink-0" />
            ) : (
              <X className="w-4 h-4 text-red-500 flex-shrink-0" />
            )}
            <span
              className={cn(
                'text-sm transition-colors duration-200',
                requirement.isValid ? 'text-green-700' : 'text-red-600'
              )}
            >
              {requirement.description}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

interface PasswordInputWithRequirementsProps {
  value: string;
  onChange: (value: string) => void;
  requirements: PasswordRequirement[];
  placeholder?: string;
  disabled?: boolean;
  error?: string;
  showRequirements?: boolean;
  showGenerator?: boolean;
  className?: string;
}

export const PasswordInputWithRequirements: React.FC<PasswordInputWithRequirementsProps> = ({
  value,
  onChange,
  requirements,
  placeholder = 'Ingresa tu contraseña',
  disabled = false,
  error,
  showRequirements = true,
  showGenerator = true,
  className,
}) => {
  const [showPassword, setShowPassword] = React.useState(false);
  const [showExamples, setShowExamples] = React.useState(false);

  const handleGeneratePassword = () => {
    const newPassword = generateSecurePassword(12);
    onChange(newPassword);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleUseExample = (examplePassword: string) => {
    onChange(examplePassword);
    setShowExamples(false);
  };

  return (
    <div className={cn('space-y-3', className)}>
      <div className="relative">
        <input
          type={showPassword ? 'text' : 'password'}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          className={cn(
            'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
            error && 'border-red-500 focus-visible:ring-red-500',
            showGenerator && 'pr-20'
          )}
        />
        
        <div className="absolute inset-y-0 right-0 flex items-center gap-1 pr-3">
          {showGenerator && (
            <>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setShowExamples(!showExamples)}
                disabled={disabled}
                className="h-6 w-6 p-0 hover:bg-gray-100"
                title="Ver ejemplos de contraseñas válidas"
              >
                <HelpCircle className="w-3 h-3" />
              </Button>

              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handleGeneratePassword}
                disabled={disabled}
                className="h-6 w-6 p-0 hover:bg-gray-100"
                title="Generar contraseña segura"
              >
                <RefreshCw className="w-3 h-3" />
              </Button>
            </>
          )}

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={togglePasswordVisibility}
            disabled={disabled}
            className="h-6 w-6 p-0 hover:bg-gray-100"
            title={showPassword ? 'Ocultar contraseña' : 'Mostrar contraseña'}
          >
            {showPassword ? (
              <EyeOff className="w-3 h-3" />
            ) : (
              <Eye className="w-3 h-3" />
            )}
          </Button>
        </div>
      </div>

      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}

      {showExamples && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
          <p className="text-sm font-medium text-blue-800 mb-2">Ejemplos de contraseñas válidas:</p>
          <div className="grid grid-cols-2 gap-2">
            {generateExamplePasswords().slice(0, 4).map((example, index) => (
              <button
                key={index}
                type="button"
                onClick={() => handleUseExample(example)}
                disabled={disabled}
                className="text-left text-sm bg-white border border-blue-300 rounded px-2 py-1 hover:bg-blue-100 transition-colors"
              >
                {example}
              </button>
            ))}
          </div>
          <p className="text-xs text-blue-600 mt-2">Haz clic en cualquier ejemplo para usarlo</p>
        </div>
      )}

      {showRequirements && requirements.length > 0 && (
        <PasswordRequirements requirements={requirements} />
      )}
    </div>
  );
};

export default PasswordInputWithRequirements;
