"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"

interface ResponsiveAutoGridProps {
  children: React.ReactNode
  className?: string
  minItemWidth?: number // en px, default 200
  gap?: "sm" | "md" | "lg"
  aspectRatio?: "square" | "video" | "auto"
}

/**
 * Grid responsivo automático que se adapta al contenido
 * Basado en CSS Grid auto-fit con minmax inteligente
 */
export function ResponsiveAutoGrid({
  children,
  className,
  minItemWidth = 200,
  gap = "md",
  aspectRatio = "auto"
}: ResponsiveAutoGridProps) {
  const isMobile = useIsMobile()
  
  const gapClasses = {
    sm: "gap-2",
    md: "gap-4", 
    lg: "gap-6"
  }
  
  const aspectClasses = {
    square: "aspect-square",
    video: "aspect-video",
    auto: ""
  }
  
  // En móvil, forzamos una sola columna para mejor UX
  const gridStyle = isMobile ? {
    display: 'grid',
    gridTemplateColumns: '1fr',
  } : {
    display: 'grid',
    gridTemplateColumns: `repeat(auto-fit, minmax(min(100%, ${minItemWidth}px), 1fr))`,
  }

  return (
    <div 
      className={cn(
        gapClasses[gap],
        aspectRatio !== "auto" && aspectClasses[aspectRatio],
        className
      )}
      style={gridStyle}
    >
      {children}
    </div>
  )
}

interface ResponsiveGridItemProps {
  children: React.ReactNode
  className?: string
  colorIndex?: number // Para aplicar colores automáticos como en tu ejemplo
}

/**
 * Item individual del grid con estilos consistentes del proyecto
 */
export function ResponsiveGridItem({
  children,
  className,
  colorIndex
}: ResponsiveGridItemProps) {
  // Colores basados en tu paleta moka
  const colorVariants = [
    "bg-moka-peach/30 border-moka-peach/50",
    "bg-moka-lion/30 border-moka-lion/50", 
    "bg-moka-brown/30 border-moka-brown/50",
    "bg-moka-bistre/30 border-moka-bistre/50",
    "bg-moka-falu/30 border-moka-falu/50",
    "bg-gray-200/30 border-gray-300/50"
  ]
  
  const colorClass = colorIndex !== undefined 
    ? colorVariants[colorIndex % colorVariants.length]
    : "bg-white/60 dark:bg-slate-800/60 border-white/20 dark:border-slate-700/20"

  return (
    <div className={cn(
      // Estilos base consistentes con tu proyecto
      "flex items-center justify-center",
      "backdrop-blur-md rounded-3xl border",
      "transition-all duration-200",
      "hover:scale-[1.02] hover:shadow-lg",
      // Colores
      colorClass,
      // Texto
      "text-gray-900 dark:text-slate-100",
      className
    )}>
      {children}
    </div>
  )
}

interface FluidTextProps {
  children: React.ReactNode
  className?: string
  size?: "sm" | "md" | "lg" | "xl"
  as?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "p" | "span"
}

/**
 * Texto fluido que se adapta al viewport
 * Implementa clamp() de forma consistente con Tailwind
 */
export function FluidText({
  children,
  className,
  size = "md",
  as: Component = "p"
}: FluidTextProps) {
  const sizeStyles = {
    sm: { fontSize: "clamp(0.75rem, 1.5vw + 0.5rem, 1rem)" },
    md: { fontSize: "clamp(0.875rem, 2vw + 0.5rem, 1.125rem)" },
    lg: { fontSize: "clamp(1rem, 2.5vw + 0.5rem, 1.5rem)" },
    xl: { fontSize: "clamp(1.25rem, 3vw + 0.5rem, 2rem)" }
  }

  return (
    <Component 
      className={cn(
        "leading-relaxed text-gray-900 dark:text-slate-100",
        className
      )}
      style={sizeStyles[size]}
    >
      {children}
    </Component>
  )
}

// Hook personalizado para detectar el número óptimo de columnas
export function useOptimalColumns(containerWidth: number, minItemWidth: number = 200, gap: number = 16) {
  return Math.floor((containerWidth + gap) / (minItemWidth + gap)) || 1
}

// Ejemplo de uso con tus datos del proyecto
interface ProjectGridProps {
  projects: Array<{
    id: string
    title: string
    company: string
    status: string
    progress: number
  }>
  className?: string
}

export function ProjectGrid({ projects, className }: ProjectGridProps) {
  return (
    <ResponsiveAutoGrid 
      className={className}
      minItemWidth={250} // Basado en tus preferencias de 250px
      gap="md"
    >
      {projects.map((project, index) => (
        <ResponsiveGridItem 
          key={project.id}
          colorIndex={index}
          className="p-6 min-h-[120px] flex-col space-y-3"
        >
          <FluidText as="h3" size="lg" className="font-medium text-center">
            {project.company}
          </FluidText>
          <FluidText as="p" size="sm" className="text-center opacity-80">
            {project.title}
          </FluidText>
          <div className="w-full bg-gray-200 dark:bg-slate-700 rounded-full h-2">
            <div 
              className="bg-moka-falu h-2 rounded-full transition-all duration-300"
              style={{ width: `${project.progress}%` }}
            />
          </div>
        </ResponsiveGridItem>
      ))}
    </ResponsiveAutoGrid>
  )
}
