"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"

interface ResponsiveFormProps {
  title?: string
  description?: string
  children: React.ReactNode
  className?: string
  actions?: React.ReactNode
  maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl" | "full"
  padding?: "sm" | "md" | "lg"
}

// Componente de formulario responsive
export function ResponsiveForm({
  title,
  description,
  children,
  className,
  actions,
  maxWidth = "2xl",
  padding = "md"
}: ResponsiveFormProps) {
  const isMobile = useIsMobile()

  const maxWidthClasses = {
    sm: "max-w-sm",
    md: "max-w-md", 
    lg: "max-w-lg",
    xl: "max-w-xl",
    "2xl": "max-w-2xl",
    full: "max-w-full"
  }

  const paddingClasses = {
    sm: isMobile ? "p-3" : "p-4",
    md: isMobile ? "p-4" : "p-6", 
    lg: isMobile ? "p-4" : "p-8"
  }

  return (
    <div className={cn(
      "w-full mx-auto",
      maxWidthClasses[maxWidth],
      className
    )}>
      <Card className="backdrop-blur-xl border shadow-lg bg-white/30 dark:bg-slate-800/30 border-gray-300/30 dark:border-slate-700/30">
        {(title || description) && (
          <CardHeader className={cn(
            "space-y-2",
            isMobile ? "p-4 pb-2" : "p-6 pb-4"
          )}>
            {title && (
              <CardTitle className={cn(
                "font-medium tracking-tight text-gray-900 dark:text-slate-100",
                isMobile ? "text-xl" : "text-2xl"
              )}>
                {title}
              </CardTitle>
            )}
            {description && (
              <CardDescription className="text-gray-600 dark:text-slate-400 leading-relaxed">
                {description}
              </CardDescription>
            )}
          </CardHeader>
        )}
        
        <CardContent className={paddingClasses[padding]}>
          <div className="space-y-6">
            {children}
          </div>
          
          {actions && (
            <div className={cn(
              "flex gap-3 pt-6 border-t border-gray-200/50 dark:border-slate-700/50 mt-6",
              isMobile ? "flex-col" : "flex-row justify-end"
            )}>
              {actions}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

// Componente para secciones de formulario
interface ResponsiveFormSectionProps {
  title?: string
  description?: string
  children: React.ReactNode
  className?: string
  collapsible?: boolean
  defaultExpanded?: boolean
}

export function ResponsiveFormSection({
  title,
  description,
  children,
  className,
  collapsible = false,
  defaultExpanded = true
}: ResponsiveFormSectionProps) {
  const isMobile = useIsMobile()
  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded)

  return (
    <div className={cn("space-y-4", className)}>
      {(title || description) && (
        <div className="space-y-1">
          {title && (
            <div className="flex items-center justify-between">
              <h3 className={cn(
                "font-medium text-gray-900 dark:text-slate-100",
                isMobile ? "text-lg" : "text-xl"
              )}>
                {title}
              </h3>
              {collapsible && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="text-muted-foreground"
                >
                  {isExpanded ? "Contraer" : "Expandir"}
                </Button>
              )}
            </div>
          )}
          {description && (
            <p className="text-sm text-gray-600 dark:text-slate-400 leading-relaxed">
              {description}
            </p>
          )}
        </div>
      )}
      
      {(!collapsible || isExpanded) && (
        <div className="space-y-4">
          {children}
        </div>
      )}
    </div>
  )
}

// Grid responsive para campos de formulario
interface ResponsiveFormGridProps {
  children: React.ReactNode
  columns?: 1 | 2 | 3 | 4
  className?: string
}

export function ResponsiveFormGrid({
  children,
  columns = 2,
  className
}: ResponsiveFormGridProps) {
  const isMobile = useIsMobile()
  
  const gridClasses = {
    1: "grid-cols-1",
    2: isMobile ? "grid-cols-1" : "grid-cols-1 sm:grid-cols-2",
    3: isMobile ? "grid-cols-1" : "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3", 
    4: isMobile ? "grid-cols-1" : "grid-cols-1 sm:grid-cols-2 lg:grid-cols-4"
  }

  return (
    <div className={cn(
      "grid gap-4",
      gridClasses[columns],
      className
    )}>
      {children}
    </div>
  )
}

// Campo de formulario responsive
interface ResponsiveFormFieldProps {
  label?: string
  description?: string
  error?: string
  required?: boolean
  children: React.ReactNode
  className?: string
}

export function ResponsiveFormField({
  label,
  description,
  error,
  required,
  children,
  className
}: ResponsiveFormFieldProps) {
  const isMobile = useIsMobile()

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <label className={cn(
          "text-sm font-medium text-gray-900 dark:text-slate-100 leading-none",
          required && "after:content-['*'] after:text-red-500 after:ml-1"
        )}>
          {label}
        </label>
      )}
      
      {children}
      
      {description && (
        <p className="text-xs text-gray-600 dark:text-slate-400 leading-relaxed">
          {description}
        </p>
      )}
      
      {error && (
        <p className="text-xs text-red-600 dark:text-red-400 leading-relaxed">
          {error}
        </p>
      )}
    </div>
  )
}

// Modal/Dialog responsive
interface ResponsiveModalProps {
  children: React.ReactNode
  className?: string
  size?: "sm" | "md" | "lg" | "xl" | "full"
}

export function ResponsiveModal({
  children,
  className,
  size = "md"
}: ResponsiveModalProps) {
  const isMobile = useIsMobile()

  const sizeClasses = {
    sm: isMobile ? "max-w-[95vw]" : "max-w-md",
    md: isMobile ? "max-w-[95vw]" : "max-w-lg",
    lg: isMobile ? "max-w-[95vw]" : "max-w-2xl",
    xl: isMobile ? "max-w-[95vw]" : "max-w-4xl",
    full: isMobile ? "max-w-[95vw] max-h-[90vh]" : "max-w-6xl max-h-[90vh]"
  }

  return (
    <div className={cn(
      "w-full mx-auto",
      sizeClasses[size],
      isMobile && "max-h-[90vh] overflow-y-auto",
      className
    )}>
      {isMobile && size === "full" ? (
        <ScrollArea className="h-full">
          {children}
        </ScrollArea>
      ) : (
        children
      )}
    </div>
  )
}
