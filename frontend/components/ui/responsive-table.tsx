"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ChevronDown, ChevronRight } from "lucide-react"

// Tipos para la tabla responsive
interface ResponsiveTableColumn {
  key: string
  label: string
  className?: string
  render?: (value: any, row: any) => React.ReactNode
  mobileLabel?: string // Label específico para móvil
  hideOnMobile?: boolean // Ocultar columna en móvil
}

interface ResponsiveTableProps {
  data: any[]
  columns: ResponsiveTableColumn[]
  className?: string
  mobileCardClassName?: string
  onRowClick?: (row: any) => void
  expandable?: boolean
  renderExpandedContent?: (row: any) => React.ReactNode
}

// Componente de tabla responsive
export function ResponsiveTable({
  data,
  columns,
  className,
  mobileCardClassName,
  onRowClick,
  expandable = false,
  renderExpandedContent
}: ResponsiveTableProps) {
  const isMobile = useIsMobile()
  const [expandedRows, setExpandedRows] = React.useState<Set<number>>(new Set())

  const toggleExpanded = (index: number) => {
    const newExpanded = new Set(expandedRows)
    if (newExpanded.has(index)) {
      newExpanded.delete(index)
    } else {
      newExpanded.add(index)
    }
    setExpandedRows(newExpanded)
  }

  // Vista móvil - Cards
  if (isMobile) {
    return (
      <div className={cn("space-y-3", className)}>
        {data.map((row, index) => (
          <Card 
            key={index} 
            className={cn(
              "transition-all duration-200",
              onRowClick && "cursor-pointer hover:shadow-md",
              mobileCardClassName
            )}
            onClick={() => onRowClick?.(row)}
          >
            <CardContent className="p-4">
              <div className="space-y-2">
                {columns
                  .filter(col => !col.hideOnMobile)
                  .map((column) => {
                    const value = row[column.key]
                    const displayValue = column.render ? column.render(value, row) : value
                    
                    return (
                      <div key={column.key} className="flex justify-between items-start">
                        <span className="text-sm font-medium text-muted-foreground min-w-0 flex-shrink-0 mr-3">
                          {column.mobileLabel || column.label}:
                        </span>
                        <div className="text-sm text-right min-w-0 flex-1">
                          {displayValue}
                        </div>
                      </div>
                    )
                  })}
                
                {expandable && renderExpandedContent && (
                  <div className="pt-2 border-t">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        toggleExpanded(index)
                      }}
                      className="w-full justify-between"
                    >
                      <span>Más detalles</span>
                      {expandedRows.has(index) ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </Button>
                    
                    {expandedRows.has(index) && (
                      <div className="mt-3 pt-3 border-t">
                        {renderExpandedContent(row)}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  // Vista desktop - Tabla tradicional
  return (
    <div className="relative w-full overflow-auto">
      <table className={cn("w-full caption-bottom text-sm", className)}>
        <thead>
          <tr className="border-b">
            {columns.map((column) => (
              <th
                key={column.key}
                className={cn(
                  "h-12 px-4 text-left align-middle font-medium text-muted-foreground",
                  column.className
                )}
              >
                {column.label}
              </th>
            ))}
            {expandable && <th className="w-12"></th>}
          </tr>
        </thead>
        <tbody>
          {data.map((row, index) => (
            <React.Fragment key={index}>
              <tr 
                className={cn(
                  "border-b transition-colors hover:bg-muted/50",
                  onRowClick && "cursor-pointer"
                )}
                onClick={() => onRowClick?.(row)}
              >
                {columns.map((column) => {
                  const value = row[column.key]
                  const displayValue = column.render ? column.render(value, row) : value
                  
                  return (
                    <td
                      key={column.key}
                      className={cn("p-4 align-middle", column.className)}
                    >
                      {displayValue}
                    </td>
                  )
                })}
                
                {expandable && (
                  <td className="p-4 align-middle">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        toggleExpanded(index)
                      }}
                    >
                      {expandedRows.has(index) ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </Button>
                  </td>
                )}
              </tr>
              
              {expandable && expandedRows.has(index) && renderExpandedContent && (
                <tr>
                  <td colSpan={columns.length + 1} className="p-4 bg-muted/20">
                    {renderExpandedContent(row)}
                  </td>
                </tr>
              )}
            </React.Fragment>
          ))}
        </tbody>
      </table>
    </div>
  )
}

// Hook para crear columnas fácilmente
export function useResponsiveTableColumns() {
  return {
    createColumn: (
      key: string,
      label: string,
      options?: Partial<ResponsiveTableColumn>
    ): ResponsiveTableColumn => ({
      key,
      label,
      ...options
    }),
    
    // Helpers para tipos comunes de columnas
    textColumn: (key: string, label: string, mobileLabel?: string) => ({
      key,
      label,
      mobileLabel
    }),
    
    badgeColumn: (key: string, label: string, colorMap?: Record<string, string>) => ({
      key,
      label,
      render: (value: string) => (
        <Badge className={colorMap?.[value] || ""}>{value}</Badge>
      )
    }),
    
    dateColumn: (key: string, label: string) => ({
      key,
      label,
      render: (value: string | Date) => {
        const date = typeof value === 'string' ? new Date(value) : value
        return date.toLocaleDateString('es-ES')
      }
    }),
    
    currencyColumn: (key: string, label: string) => ({
      key,
      label,
      render: (value: number) => 
        new Intl.NumberFormat('es-ES', { 
          style: 'currency', 
          currency: 'EUR' 
        }).format(value),
      className: "text-right"
    })
  }
}
