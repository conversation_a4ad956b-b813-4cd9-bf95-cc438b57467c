"use client"

import { useState } from "react"
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
  CardFooter,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Handshake, Send, Download, Calendar as CalendarIcon } from "lucide-react"
import { toast } from "sonner"
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { FormCard } from "@/components/ui/form-styles"

// --- Mock Data & Types ---
const mockInventoryData = [
  { id: "VLV-001", name: "Válvula de Bola Inoxidable 1/2\"", stock: 150 },
  { id: "SEN-001", name: "Sensor de Presión 0-100 PSI", stock: 45 },
  { id: "PLC-001", name: "PLC Compacto Allen-Bradley", stock: 0 },
  { id: "MAN-001", name: "Manómetro Glicerina 2.5\"", stock: 75 },
]

const mockPrestamos = [
    { id: "P-001", product: "Sensor de Presión 0-100 PSI", solicitante: "Juan Pérez", cliente: "Industrias Acme", fechaPrestamo: "2024-07-20", fechaDevolucion: "2024-08-05", status: "Activo" },
    { id: "P-002", product: "Manómetro Glicerina 2.5\"", solicitante: "María García", cliente: "Constructora Delta", fechaPrestamo: "2024-07-15", fechaDevolucion: "2024-07-25", status: "Devuelto" },
    { id: "P-003", product: "Válvula de Bola Inoxidable 1/2\"", solicitante: "Juan Pérez", cliente: "Químicos del Sur", fechaPrestamo: "2024-06-10", fechaDevolucion: "2024-06-20", status: "Devuelto" },
]

export function PrestamoEquipoView() {
  const [selectedProduct, setSelectedProduct] = useState("")
  const [cliente, setCliente] = useState("")
  const [fechaDevolucion, setFechaDevolucion] = useState<Date | undefined>()

  const handleRequestLoan = () => {
    if (!selectedProduct || !cliente || !fechaDevolucion) {
      toast.error("Faltan datos", { description: "Por favor, selecciona un producto, un cliente y una fecha de devolución." })
      return
    }

    const product = mockInventoryData.find(p => p.id === selectedProduct)
    const valeId = `PR-${Date.now().toString().slice(-6)}`

    toast.success("Solicitud de Préstamo Enviada", {
      description: `Alerta enviada a Almacén para el préstamo de "${product?.name}" al cliente ${cliente}. Folio: ${valeId}`,
      action: {
        label: "Descargar Vale",
        onClick: () => alert(`Descargando vale de préstamo ${valeId}...`),
      },
    })

    // Reset form
    setSelectedProduct("")
    setCliente("")
    setFechaDevolucion(undefined)
  }
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Activo":
        return <Badge variant="secondary" className="bg-moka-peach/60 text-moka-bistre border border-moka-brown/40">{status}</Badge>
      case "Devuelto":
        return <Badge variant="secondary" className="bg-moka-lion/30 text-moka-bistre border border-moka-brown/40">{status}</Badge>
      default:
        return <Badge className="bg-moka-peach/40 text-moka-bistre border border-moka-brown/40">{status}</Badge>
    }
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <FormCard>
        <header>
          <h1 className="text-3xl font-bold tracking-tight">Préstamo de Equipo</h1>
          <p className="text-muted-foreground">
            Solicita equipos del almacén para demostraciones o préstamos a clientes.
          </p>
        </header>

      <div className="grid lg:grid-cols-3 gap-8">
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle>Nueva Solicitud</CardTitle>
            <CardDescription>Genera una alerta a almacén y un vale de préstamo.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="producto">Producto a Prestar</Label>
              <Select value={selectedProduct} onValueChange={setSelectedProduct}>
                <SelectTrigger id="producto">
                  <SelectValue placeholder="Selecciona un equipo..." />
                </SelectTrigger>
                <SelectContent>
                  {mockInventoryData.map(p => (
                    <SelectItem key={p.id} value={p.id} disabled={p.stock === 0}>
                      {p.name} (Stock: {p.stock})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="cliente">Cliente</Label>
              <Input id="cliente" placeholder="Nombre del cliente" value={cliente} onChange={e => setCliente(e.target.value)} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="fecha-devolucion">Fecha Estimada de Devolución</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="fecha-devolucion"
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !fechaDevolucion && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {fechaDevolucion ? format(fechaDevolucion, "PPP") : <span>Elige una fecha</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={fechaDevolucion}
                    onSelect={setFechaDevolucion}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </CardContent>
          <CardFooter>
            <Button className="w-full" onClick={handleRequestLoan}>
              <Send className="mr-2 h-4 w-4" />
              Enviar Solicitud y Generar Vale
            </Button>
          </CardFooter>
        </Card>

        <Card className="lg:col-span-2 bg-moka-peach/20 dark:bg-card border-moka-brown/40 dark:border-border backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="font-medium text-moka-bistre dark:text-foreground">Historial de Préstamos</CardTitle>
            <CardDescription className="text-moka-brown/80 dark:text-muted-foreground">Registro de todos los equipos prestados.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border border-moka-brown/40 dark:border-border bg-moka-peach/10 dark:bg-background backdrop-blur-sm">
              <Table>
                <TableHeader>
                  <TableRow className="border-moka-brown/30 dark:border-border">
                    <TableHead className="text-moka-bistre dark:text-foreground font-normal">Producto</TableHead>
                    <TableHead className="text-moka-bistre dark:text-foreground font-normal">Cliente</TableHead>
                    <TableHead className="text-moka-bistre dark:text-foreground font-normal">Fecha Préstamo</TableHead>
                    <TableHead className="text-moka-bistre dark:text-foreground font-normal">Fecha Devolución</TableHead>
                    <TableHead className="text-moka-bistre dark:text-foreground font-normal">Estado</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockPrestamos.map(item => (
                    <TableRow key={item.id} className="border-moka-brown/30 dark:border-border hover:bg-moka-peach/10 dark:hover:bg-accent/50">
                      <TableCell className="font-normal text-moka-bistre dark:text-foreground">{item.product}</TableCell>
                      <TableCell className="text-moka-brown/80 dark:text-muted-foreground">{item.cliente}</TableCell>
                      <TableCell className="text-moka-brown/80 dark:text-muted-foreground">{item.fechaPrestamo}</TableCell>
                      <TableCell className="text-moka-brown/80 dark:text-muted-foreground">{item.fechaDevolucion}</TableCell>
                      <TableCell>{getStatusBadge(item.status)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
      </FormCard>
    </div>
  )
}