"use client"

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardContent,
  CardDescription,
  CardFooter,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Calendar as CalendarIcon, Send } from "lucide-react"
import { DateRange } from "react-day-picker"
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import { cn } from "@/lib/utils"
import { useState } from "react"
import { toast } from "sonner"
import { FormCard } from "@/components/ui/form-styles"

export function TiempoView() {
  const [date, setDate] = useState<DateRange | undefined>()
  const [requestType, setRequestType] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    const formData = new FormData(e.target as HTMLFormElement)
    const justification = formData.get("justification")

    if (!requestType || !justification || !date) {
        toast.error("Faltan datos", { description: "Por favor, completa todos los campos del formulario." })
        return
    }

    toast.success("Solicitud Enviada", {
        description: `Tu solicitud de tiempo (${requestType}) ha sido enviada para su aprobación.`
    })
    
    console.log({
        requestType,
        justification,
        startDate: date.from,
        endDate: date.to,
    })
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <FormCard>
        <header>
          <h1 className="text-3xl font-bold tracking-tight">Solicitud de Tiempo</h1>
          <p className="text-muted-foreground">
            Solicita tiempo para horas extra, permisos o asignación a proyectos.
          </p>
        </header>
      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Detalles de la Solicitud</CardTitle>
            <CardDescription>Proporciona la información necesaria para la solicitud de tiempo.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid sm:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="request-type">Tipo de Solicitud</Label>
                <Select onValueChange={setRequestType} value={requestType}>
                  <SelectTrigger id="request-type">
                    <SelectValue placeholder="Elige un tipo..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Horas Extra">Horas Extra</SelectItem>
                    <SelectItem value="Permiso con Goce de Sueldo">Permiso con Goce de Sueldo</SelectItem>
                    <SelectItem value="Permiso sin Goce de Sueldo">Permiso sin Goce de Sueldo</SelectItem>
                    <SelectItem value="Asignación a Proyecto Interno">Asignación a Proyecto Interno</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="date">Fechas Requeridas</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="date"
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !date && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date?.from ? (
                        date.to ? (
                          <>
                            {format(date.from, "LLL dd, y", { locale: es })} -{" "}
                            {format(date.to, "LLL dd, y", { locale: es })}
                          </>
                        ) : (
                          format(date.from, "LLL dd, y", { locale: es })
                        )
                      ) : (
                        <span>Elige un rango de fechas</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      initialFocus
                      mode="range"
                      defaultMonth={date?.from}
                      selected={date}
                      onSelect={setDate}
                      numberOfMonths={2}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="justification">Justificación</Label>
              <Textarea id="justification" name="justification" placeholder="Describe el motivo de tu solicitud..." />
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" size="lg">
              <Send className="mr-2 h-4 w-4" />
              Enviar Solicitud
            </Button>
          </CardFooter>
        </Card>
      </form>
      </FormCard>
    </div>
  )
}