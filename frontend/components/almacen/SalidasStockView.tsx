"use client"

import { useState } from "react"
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
  CardFooter,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { PlusCircle, Trash2, Printer, FileText } from "lucide-react"
import { toast } from "sonner"
import { FormCard } from "@/components/ui/form-styles"
import { productService } from "@/lib/services/product"

// --- Mock Data & Types ---
const mockInventoryData = [
  { id: "VLV-001", name: "Válvula de Bola Inoxidable 1/2\"", stock: 150 },
  { id: "SEN-001", name: "Sensor de Presión 0-100 PSI", stock: 45 },
  { id: "CAB-001", name: "Cable de Control 4 Hilos", stock: 8 },
  { id: "CON-001", name: "Conector M12 4 Pines", stock: 250 },
  { id: "PLC-001", name: "PLC Compacto Allen-Bradley", stock: 0 },
]

interface CartItem {
  id: string
  name: string
  quantity: number
  stock: number
}

export function SalidasStockView() {
  const [solicitante, setSolicitante] = useState("")
  const [proyecto, setProyecto] = useState("")
  const [cart, setCart] = useState<CartItem[]>([])
  const [selectedProduct, setSelectedProduct] = useState("")

  const handleAddProduct = () => {
    if (!selectedProduct) {
      toast.warning("Selecciona un producto", { description: "Debes elegir un producto de la lista para añadirlo." })
      return
    }
    
    const productInCart = cart.find(item => item.id === selectedProduct)
    if (productInCart) {
      toast.info("Producto ya en el vale", { description: `"${productInCart.name}" ya ha sido añadido.` })
      return
    }

    const productData = mockInventoryData.find(p => p.id === selectedProduct)
    if (productData) {
      setCart([...cart, { id: productData.id, name: productData.name, quantity: 1, stock: productData.stock }])
      setSelectedProduct("")
    }
  }

  // Manejar producto escaneado por QR
  const handleQRScanned = async (qrData: string) => {
    try {
      // Intentar procesar como QR primero
      const qrResult = await productService.processQRData(qrData)
      
      if (qrResult.success && qrResult.data?.producto) {
        // QR procesado exitosamente
        const product = qrResult.data.producto
        setSelectedProduct(product.id.toString())
        toast.success("Producto encontrado por QR", {
          description: `${product.nombre} (${product.codigoItem})`,
        })
      } else if (qrResult.data?.error) {
        // Error en el procesamiento QR, intentar búsqueda directa
        const productData = mockInventoryData.find(p => 
          p.id.toLowerCase() === qrData.toLowerCase() ||
          p.name.toLowerCase().includes(qrData.toLowerCase())
        )
        
        if (productData) {
          setSelectedProduct(productData.id)
          toast.success("Producto encontrado", {
            description: `"${productData.name}" seleccionado para añadir al vale.`,
          })
        } else {
          toast.error("Producto no encontrado", {
            description: `No se encontró ningún producto con el código: ${qrData}`,
          })
        }
      }
    } catch (e) {
      // Fallback a búsqueda directa
      const productData = mockInventoryData.find(p => 
        p.id.toLowerCase() === qrData.toLowerCase() ||
        p.name.toLowerCase().includes(qrData.toLowerCase())
      )
      
      if (productData) {
        setSelectedProduct(productData.id)
        toast.success("Producto encontrado", {
          description: `"${productData.name}" seleccionado para añadir al vale.`,
        })
      } else {
        toast.error("Producto no encontrado", {
          description: `No se encontró ningún producto con el código: ${qrData}`,
        })
      }
    }
  }

  const handleQuantityChange = (id: string, newQuantity: number) => {
    setCart(cart.map(item => {
      if (item.id === id) {
        const qty = Math.max(1, Math.min(item.stock, newQuantity))
        if (newQuantity > item.stock) {
          toast.error("Stock insuficiente", { description: `Solo hay ${item.stock} unidades de "${item.name}".` })
        }
        return { ...item, quantity: qty }
      }
      return item
    }))
  }

  const handleRemoveItem = (id: string) => {
    setCart(cart.filter(item => item.id !== id))
  }

  const handleGenerateVale = () => {
    if (cart.length === 0 || !solicitante || !proyecto) {
      toast.error("Faltan datos", { description: "Por favor, completa los datos del solicitante, el proyecto y añade al menos un producto." })
      return
    }
    
    const valeId = `VS-${Date.now().toString().slice(-6)}`
    toast.success("Vale de Salida Generado", {
      description: `El vale ${valeId} ha sido creado con ${cart.length} producto(s).`,
      action: {
        label: "Imprimir",
        onClick: () => alert(`Imprimiendo vale ${valeId}...`),
      },
    })

    // Reset form
    setSolicitante("")
    setProyecto("")
    setCart([])
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <FormCard>
        <header>
          <h1 className="text-3xl font-bold tracking-tight">Vale de Salida de Stock</h1>
          <p className="text-muted-foreground">
            Genera un vale para registrar la salida de productos del almacén.
          </p>
        </header>

      <div className="grid lg:grid-cols-3 gap-8">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Crear Vale de Salida</CardTitle>
            <CardDescription>Completa los detalles y añade los productos a retirar.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="solicitante">Solicitante</Label>
                <Input id="solicitante" placeholder="Ej: Juan Pérez" value={solicitante} onChange={e => setSolicitante(e.target.value)} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="proyecto">Departamento / Proyecto</Label>
                <Input id="proyecto" placeholder="Ej: Proyecto Alpha" value={proyecto} onChange={e => setProyecto(e.target.value)} />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Añadir Producto al Vale</Label>
              <div className="space-y-3">
                <div className="flex gap-2">
                  <Select value={selectedProduct} onValueChange={setSelectedProduct}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecciona un producto..." />
                    </SelectTrigger>
                    <SelectContent>
                      {mockInventoryData.map(p => (
                        <SelectItem key={p.id} value={p.id} disabled={p.stock === 0}>
                          {p.name} (Stock: {p.stock})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button onClick={handleAddProduct} variant="outline">
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Añadir
                  </Button>
                </div>
                
                {/* Botón de escaneo QR para móviles */}
                <div className="flex justify-center">
                  {/* QRScanner component was removed, so this placeholder is now empty */}
                </div>
              </div>
            </div>

            <div className="rounded-md border border-moka-brown/40 dark:border-border bg-moka-peach/10 dark:bg-background backdrop-blur-sm">
              <Table>
                <TableHeader>
                  <TableRow className="border-moka-brown/30 dark:border-border">
                    <TableHead className="w-[50%] text-moka-bistre dark:text-foreground font-normal">Producto</TableHead>
                    <TableHead className="text-moka-bistre dark:text-foreground font-normal">Stock Disp.</TableHead>
                    <TableHead className="w-[120px] text-moka-bistre dark:text-foreground font-normal">Cantidad</TableHead>
                    <TableHead className="text-moka-bistre dark:text-foreground font-normal">Acciones</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {cart.length > 0 ? (
                    cart.map(item => (
                      <TableRow key={item.id} className="border-moka-brown/30 dark:border-border hover:bg-moka-peach/10 dark:hover:bg-accent/50">
                        <TableCell className="font-normal text-moka-bistre dark:text-foreground">{item.name}</TableCell>
                        <TableCell className="text-moka-brown/80 dark:text-muted-foreground">{item.stock}</TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            className="h-8 w-20 text-center bg-moka-peach/20 border-moka-brown/40 text-moka-bistre dark:bg-background dark:border-border dark:text-foreground"
                            value={item.quantity}
                            max={item.stock}
                            min={1}
                            onChange={e => handleQuantityChange(item.id, parseInt(e.target.value, 10))}
                          />
                        </TableCell>
                        <TableCell>
                          <Button variant="ghost" size="icon" onClick={() => handleRemoveItem(item.id)}>
                            <Trash2 className="h-4 w-4 text-moka-falu" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow className="border-moka-brown/30 dark:border-border">
                      <TableCell colSpan={4} className="h-24 text-center text-moka-brown/80 dark:text-muted-foreground">
                        Añade productos para crear el vale.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
          <CardFooter>
            <Button size="lg" className="w-full sm:w-auto" onClick={handleGenerateVale} disabled={cart.length === 0}>
              <FileText className="mr-2 h-5 w-5" />
              Generar Vale de Salida
            </Button>
          </CardFooter>
        </Card>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Vales Recientes</CardTitle>
              <CardDescription>Historial de vales generados hoy.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Placeholder for recent vales */}
                <div className="flex items-center justify-between">
                  <div className="font-medium">VS-849123</div>
                  <div className="text-sm text-muted-foreground">Juan Pérez</div>
                  <Button variant="ghost" size="sm"><Printer className="h-4 w-4"/></Button>
                </div>
                <div className="flex items-center justify-between">
                  <div className="font-medium">VS-849122</div>
                  <div className="text-sm text-muted-foreground">María García</div>
                  <Button variant="ghost" size="sm"><Printer className="h-4 w-4"/></Button>
                </div>
                <p className="text-center text-sm text-muted-foreground pt-4">No hay más vales hoy.</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      </FormCard>
    </div>
  )
}