"use client"

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { Alert<PERSON>riangle, Loader2, Trash2 } from "lucide-react"
import { Product } from "@/lib/services/product"

interface DeleteProductModalProps {
  product: Product | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: () => Promise<void>
  isLoading?: boolean
}

export function DeleteProductModal({
  product,
  open,
  onOpenChange,
  onConfirm,
  isLoading = false
}: DeleteProductModalProps) {
  if (!product) return null

  const stockReal = product.stockReal ?? (product.stockDisponible - product.stockComprometido)
  const hasStock = stockReal > 0 || product.stockComprometido > 0

  const handleConfirm = async () => {
    try {
      await onConfirm()
      onOpenChange(false)
    } catch (error) {
      // Error is handled by the parent component
      console.error("Error deleting product:", error)
    }
  }

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Confirmar Eliminación
          </AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div className="space-y-4">
              <p>
                ¿Estás seguro de que deseas eliminar el siguiente producto? Esta acción no se puede deshacer.
              </p>
              
              <div className="p-4 bg-gray-50 rounded-lg space-y-2">
                <div className="font-medium text-gray-900">
                  {product.nombre}
                </div>
                <div className="text-sm text-gray-600">
                  Código: {product.codigoItem}
                </div>
                <div className="flex gap-2">
                  <Badge variant="outline" className={
                    product.tipoAlmacen === 'ROTATIVO' 
                      ? "border-blue-500 text-blue-700" 
                      : "border-gray-500 text-gray-700"
                  }>
                    {product.tipoAlmacen}
                  </Badge>
                  <Badge className={
                    product.estado === 'DISPONIBLE' 
                      ? "bg-green-100 text-green-800"
                      : product.estado === 'AGOTADO'
                      ? "bg-red-100 text-red-800"
                      : "bg-gray-100 text-gray-800"
                  }>
                    {product.estado}
                  </Badge>
                </div>
              </div>

              {hasStock && (
                <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                  <div className="flex items-center gap-2 text-amber-800">
                    <AlertTriangle className="h-4 w-4" />
                    <span className="font-medium">Advertencia de Stock</span>
                  </div>
                  <div className="text-sm text-amber-700 mt-1">
                    Este producto tiene stock disponible ({product.stockDisponible} unidades)
                    {product.stockComprometido > 0 && ` y stock comprometido (${product.stockComprometido} unidades)`}.
                    Al eliminarlo, también se perderán estos registros de inventario.
                  </div>
                </div>
              )}
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>
            Cancelar
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isLoading}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Eliminando...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                Eliminar Producto
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
} 