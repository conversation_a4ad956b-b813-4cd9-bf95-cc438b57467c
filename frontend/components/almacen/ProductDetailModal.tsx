"use client"

import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Calendar, Package, MapPin, Truck, AlertTriangle } from "lucide-react"
import { Product } from "@/lib/services/product"

interface ProductDetailModalProps {
  product: Product | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ProductDetailModal({ product, open, onOpenChange }: ProductDetailModalProps) {
  if (!product) return null

  const stockReal = product.stockReal ?? (product.stockDisponible - product.stockComprometido)
  
  const getStockBadge = () => {
    const color = product.colorIndicador || 'green'

    if (stockReal === 0) {
      return <Badge variant="destructive" className="bg-moka-falu/20 text-moka-falu border border-moka-falu/40">Sin Stock</Badge>
    }

    if (product.stockComprometido > 0) {
      return <Badge className="bg-moka-peach/60 text-moka-bistre border border-moka-brown/40 hover:bg-moka-peach/80">Comprometido</Badge>
    }

    if (stockReal <= 10) {
      return <Badge className="bg-moka-peach/80 text-moka-bistre border border-moka-brown/40 hover:bg-moka-peach/90">Stock Bajo</Badge>
    }

    return <Badge className="bg-moka-lion/30 text-moka-bistre border border-moka-brown/40 hover:bg-moka-lion/40">En Stock</Badge>
  }

  const getTipoAlmacenBadge = () => {
    return product.tipoAlmacen === 'ROTATIVO' ? (
      <Badge variant="outline" className="border-blue-500 text-blue-700">
        Rotativo
      </Badge>
    ) : (
      <Badge variant="outline" className="border-gray-500 text-gray-700">
        General
      </Badge>
    )
  }

  const getEstadoBadge = () => {
    switch (product.estado) {
      case 'DISPONIBLE':
        return <Badge className="bg-green-100 text-green-800">Disponible</Badge>
      case 'AGOTADO':
        return <Badge className="bg-red-100 text-red-800">Agotado</Badge>
      case 'DESCONTINUADO':
        return <Badge className="bg-gray-100 text-gray-800">Descontinuado</Badge>
      default:
        return <Badge variant="secondary">{product.estado}</Badge>
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {product.nombre}
          </DialogTitle>
          <DialogDescription>
            Código: {product.codigoItem}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Información básica */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Información General</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Código</label>
                  <p className="text-sm">{product.codigoItem}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Nombre</label>
                  <p className="text-sm">{product.nombre}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Marca</label>
                  <p className="text-sm">{product.marca || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Modelo</label>
                  <p className="text-sm">{product.modelo || 'N/A'}</p>
                </div>
              </div>
              
              {product.descripcion && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Descripción</label>
                  <p className="text-sm">{product.descripcion}</p>
                </div>
              )}

              <div className="flex flex-wrap gap-2">
                {getTipoAlmacenBadge()}
                {getEstadoBadge()}
                {getStockBadge()}
              </div>
            </CardContent>
          </Card>

          {/* Stock e inventario */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Truck className="h-5 w-5" />
                Inventario
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {product.stockDisponible}
                  </div>
                  <div className="text-sm text-blue-600">Disponible</div>
                </div>
                <div className="text-center p-3 bg-yellow-50 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">
                    {product.stockComprometido}
                  </div>
                  <div className="text-sm text-yellow-600">Comprometido</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {stockReal}
                  </div>
                  <div className="text-sm text-green-600">Stock Real</div>
                </div>
                <div className="text-center p-3 bg-red-50 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">
                    {product.stockMinimo}
                  </div>
                  <div className="text-sm text-red-600">Stock Mínimo</div>
                </div>
              </div>
              
              {stockReal <= product.stockMinimo && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2 text-red-800">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="text-sm">
                    ⚠️ Stock por debajo del mínimo requerido
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Información técnica */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Información Técnica</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Número de Serie</label>
                  <p className="text-sm">{product.numeroSerie || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Pedimento</label>
                  <p className="text-sm">{product.pedimento || 'N/A'}</p>
                </div>
                <div className="col-span-2">
                  <label className="text-sm font-medium text-gray-500 flex items-center gap-1">
                    <MapPin className="h-4 w-4" />
                    Ubicación
                  </label>
                  <p className="text-sm">{product.ubicacion || 'No especificada'}</p>
                </div>
              </div>
              
              {product.observaciones && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Observaciones</label>
                  <p className="text-sm bg-gray-50 p-2 rounded">{product.observaciones}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Metadatos */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Registro
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <label className="text-gray-500">Creado</label>
                  <p>{new Date(product.createdAt).toLocaleDateString('es-ES', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}</p>
                </div>
                <div>
                  <label className="text-gray-500">Actualizado</label>
                  <p>{new Date(product.updatedAt).toLocaleDateString('es-ES', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  )
} 