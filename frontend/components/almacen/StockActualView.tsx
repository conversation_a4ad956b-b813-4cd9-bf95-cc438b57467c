"use client"

import { useState, useMemo } from "react"
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table"
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  PackageSearch, 
  ArrowUpDown, 
  MoreHorizontal, 
  PlusCircle, 
  Filter,
  Package,
  Loader2,
  AlertCircle,
  Eye,
  Edit,
  Trash2
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { useProducts, useInventoryStats, useCreateProduct, useUpdateProduct, useDeleteProduct } from "@/hooks/useProducts"
import { ProductFilters, Product } from "@/lib/services/product"
import { ProductDetailModal } from "./ProductDetailModal"
import { ProductFormModal } from "./ProductFormModal"
import { DeleteProductModal } from "./DeleteProductModal"
import { ProductQRGenerator } from "./ProductQRGenerator"
import { toast } from "sonner"
import { FormCard } from "@/components/ui/form-styles"

export function StockActualView() {
  const [filters, setFilters] = useState<ProductFilters>({
    page: 1,
    limit: 20,
    sortBy: 'nombre',
    sortOrder: 'ASC'
  })
  
  // Modal states
  const [detailModal, setDetailModal] = useState({ open: false, product: null as Product | null })
  const [formModal, setFormModal] = useState({ open: false, product: null as Product | null })
  const [deleteModal, setDeleteModal] = useState({ open: false, product: null as Product | null })
  const [qrModal, setQrModal] = useState({ open: false, product: null as Product | null })

  const { data: productsData, isLoading, error, isError } = useProducts(filters)
  const { data: stats, isLoading: isStatsLoading } = useInventoryStats()
  
  // Mutations
  const createProductMutation = useCreateProduct()
  const updateProductMutation = useUpdateProduct()
  const deleteProductMutation = useDeleteProduct()

  const updateFilters = (newFilters: Partial<ProductFilters>) => {
    setFilters(prev => ({ 
      ...prev, 
      ...newFilters,
      // Reset page when changing filters (except when changing page itself)
      ...(newFilters.page === undefined ? { page: 1 } : {})
    }))
  }

  // Modal handlers
  const handleView = (product: Product) => {
    setDetailModal({ open: true, product })
  }

  const handleEdit = (product: Product) => {
    setFormModal({ open: true, product })
  }

  const handleDelete = (product: Product) => {
    setDeleteModal({ open: true, product })
  }

  const handleGenerateQR = (product: Product) => {
    setQrModal({ open: true, product })
  }

  const handleCreate = () => {
    setFormModal({ open: true, product: null })
  }

  const handleFormSubmit = async (data: any) => {
    if (formModal.product) {
      // Edit mode
      await updateProductMutation.mutateAsync({
        id: formModal.product.id,
        data
      })
      toast.success("Producto actualizado correctamente")
    } else {
      // Create mode
      await createProductMutation.mutateAsync(data)
      toast.success("Producto creado correctamente")
    }
    setFormModal({ open: false, product: null })
  }

  const handleDeleteConfirm = async () => {
    if (deleteModal.product) {
      await deleteProductMutation.mutateAsync(deleteModal.product.id)
      toast.success("Producto eliminado correctamente")
      setDeleteModal({ open: false, product: null })
    }
  }

  const handleSort = (sortBy: ProductFilters['sortBy']) => {
    const newSortOrder = 
      filters.sortBy === sortBy && filters.sortOrder === 'ASC' 
        ? 'DESC' 
        : 'ASC'
    
    updateFilters({ sortBy, sortOrder: newSortOrder })
  }

  const getSortIndicator = (column: ProductFilters['sortBy']) => {
    if (filters.sortBy !== column) {
      return <ArrowUpDown className="ml-2 h-4 w-4 opacity-30" />
    }
    return (
      <ArrowUpDown 
        className={`ml-2 h-4 w-4 ${
          filters.sortOrder === 'DESC' ? 'rotate-180' : ''
        }`} 
      />
    )
  }

  const getStockBadge = (product: Product) => {
    const nivel = product.nivelStock || 'NORMAL'
    const color = product.colorIndicador || 'green'
    
    const stockReal = product.stockReal ?? (product.stockDisponible - product.stockComprometido)
    
    let variant: "default" | "secondary" | "destructive" | "outline" = "secondary"
    let className = ""
    
    switch (color) {
      case 'red':
        className = "bg-moka-falu/20 text-moka-falu border border-moka-falu/40 hover:bg-moka-falu/30"
        break
      case 'yellow':
        className = "bg-moka-peach/60 text-moka-bistre border border-moka-brown/40 hover:bg-moka-peach/80"
        break
      case 'green':
        className = "bg-moka-lion/30 text-moka-bistre border border-moka-brown/40 hover:bg-moka-lion/40"
        break
      case 'blue':
        className = "bg-moka-peach/40 text-moka-bistre border border-moka-brown/40 hover:bg-moka-peach/60"
        break
    }

    const stockText = stockReal === 0 ? 'Sin Stock' : 
                     nivel === 'BAJO' ? 'Stock Bajo' :
                     product.stockComprometido > 0 ? 'Comprometido' : 
                     'En Stock'

    return (
      <Badge variant={variant} className={className}>
        {stockText}
      </Badge>
    )
  }

  const getEstadoBadge = (estado: Product['estado']) => {
    switch (estado) {
      case 'DISPONIBLE':
        return <Badge variant="secondary" className="bg-moka-lion/30 text-moka-bistre border border-moka-brown/40">Disponible</Badge>
      case 'AGOTADO':
        return <Badge variant="destructive" className="bg-moka-falu/20 text-moka-falu border border-moka-falu/40">Agotado</Badge>
      case 'DESCONTINUADO':
        return <Badge variant="outline" className="border-moka-brown/60 text-moka-brown">Descontinuado</Badge>
      default:
        return <Badge className="bg-moka-peach/40 text-moka-bistre border border-moka-brown/40">{estado}</Badge>
    }
  }

  const getTipoAlmacenBadge = (tipo: Product['tipoAlmacen']) => {
    return (
      <Badge variant="outline" className={
        tipo === 'ROTATIVO'
          ? "border-moka-lion/60 text-moka-lion bg-moka-lion/10"
          : "border-moka-brown/60 text-moka-brown bg-moka-brown/10"
      }>
        {tipo}
      </Badge>
    )
  }

  // Calculate total pages
  const totalPages = productsData ? Math.ceil(productsData.total / (filters.limit || 20)) : 1

  if (isError) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Error al cargar los productos: {error?.message || 'Error desconocido'}
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <FormCard>
        <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Stock Actual</h1>
            <p className="text-muted-foreground">
              Visualiza y gestiona el inventario de productos en tiempo real.
            </p>
          </div>
          <Button onClick={handleCreate}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Agregar Producto
          </Button>
        </header>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Items</CardTitle>
            <PackageSearch className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isStatsLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                stats?.totalProducts || 0
              )}
            </div>
            <p className="text-xs text-muted-foreground">Productos únicos en inventario</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stock Bajo</CardTitle>
            <PackageSearch className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isStatsLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                stats?.lowStockProducts || 0
              )}
            </div>
            <p className="text-xs text-muted-foreground">Necesitan reabastecimiento</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sin Stock</CardTitle>
            <PackageSearch className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isStatsLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                stats?.outOfStockProducts || 0
              )}
            </div>
            <p className="text-xs text-muted-foreground">Productos agotados</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Valor Total</CardTitle>
            <PackageSearch className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isStatsLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                `$${(stats?.totalValue || 0).toLocaleString()}`
              )}
            </div>
            <p className="text-xs text-muted-foreground">Valor estimado del inventario</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros de Inventario
          </CardTitle>
          <CardDescription>
            Busca y filtra productos por diferentes criterios.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
            <div className="space-y-2">
              <label className="text-sm font-medium">Buscar</label>
              <Input
                placeholder="Nombre, código, marca..."
                value={filters.search || ""}
                onChange={(e) => updateFilters({ search: e.target.value || undefined })}
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Tipo de Almacén</label>
              <Select 
                value={filters.tipoAlmacen || "ALL"} 
                onValueChange={(value) => updateFilters({ 
                  tipoAlmacen: value === "ALL" ? undefined : value as 'GENERAL' | 'ROTATIVO' 
                })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Todos los tipos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">Todos los tipos</SelectItem>
                  <SelectItem value="GENERAL">General</SelectItem>
                  <SelectItem value="ROTATIVO">Rotativo</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Estado</label>
              <Select 
                value={filters.estado || "ALL"} 
                onValueChange={(value) => updateFilters({ 
                  estado: value === "ALL" ? undefined : value as 'DISPONIBLE' | 'AGOTADO' | 'DESCONTINUADO' 
                })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Todos los estados" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">Todos los estados</SelectItem>
                  <SelectItem value="DISPONIBLE">Disponible</SelectItem>
                  <SelectItem value="AGOTADO">Agotado</SelectItem>
                  <SelectItem value="DESCONTINUADO">Descontinuado</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Ubicación/Bodega</label>
              <Select 
                value={filters.ubicacion || "ALL"} 
                onValueChange={(value) => updateFilters({ 
                  ubicacion: value === "ALL" ? undefined : value 
                })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Todas las ubicaciones" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">Todas las ubicaciones</SelectItem>
                  <SelectItem value="GDL">Guadalajara (GDL)</SelectItem>
                  <SelectItem value="MTY">Monterrey (MTY)</SelectItem>
                  <SelectItem value="CDMX">Ciudad de México (CDMX)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Elementos por página</label>
              <Select 
                value={filters.limit?.toString() || "20"} 
                onValueChange={(value) => updateFilters({ limit: parseInt(value) })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Products Table */}
      <Card className="bg-moka-peach/20 dark:bg-card border-moka-brown/40 dark:border-border backdrop-blur-sm">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="font-medium text-moka-bistre dark:text-foreground">Inventario de Productos</CardTitle>
              <CardDescription className="text-moka-brown/80 dark:text-muted-foreground">
                {productsData && (
                  `Mostrando ${productsData.products.length} de ${productsData.total} productos`
                )}
              </CardDescription>
            </div>
            {isLoading && (
              <Loader2 className="h-4 w-4 animate-spin text-moka-brown/60 dark:text-muted-foreground" />
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border border-moka-brown/40 dark:border-border bg-moka-peach/10 dark:bg-background backdrop-blur-sm">
            <Table>
              <TableHeader>
                <TableRow className="border-moka-brown/30 dark:border-border">
                  <TableHead
                    onClick={() => handleSort('nombre')}
                    className="cursor-pointer text-moka-bistre dark:text-foreground font-normal"
                  >
                    <div className="flex items-center">
                      Producto {getSortIndicator('nombre')}
                    </div>
                  </TableHead>
                  <TableHead
                    onClick={() => handleSort('codigoItem')}
                    className="cursor-pointer hidden md:table-cell text-moka-bistre dark:text-foreground font-normal"
                  >
                    <div className="flex items-center">
                      Código {getSortIndicator('codigoItem')}
                    </div>
                  </TableHead>
                  <TableHead
                    onClick={() => handleSort('marca')}
                    className="cursor-pointer hidden lg:table-cell text-moka-bistre dark:text-foreground font-normal"
                  >
                    <div className="flex items-center">
                      Marca {getSortIndicator('marca')}
                    </div>
                  </TableHead>
                  <TableHead className="text-moka-bistre dark:text-foreground font-normal">Tipo</TableHead>
                  <TableHead className="hidden sm:table-cell text-moka-bistre dark:text-foreground font-normal">Ubicación</TableHead>
                  <TableHead
                    onClick={() => handleSort('stockDisponible')}
                    className="cursor-pointer text-right text-moka-bistre dark:text-foreground font-normal"
                  >
                    <div className="flex items-center justify-end">
                      Stock {getSortIndicator('stockDisponible')}
                    </div>
                  </TableHead>
                  <TableHead className="text-moka-bistre dark:text-foreground font-normal">Estado Stock</TableHead>
                  <TableHead className="hidden md:table-cell text-moka-bistre dark:text-foreground font-normal">Estado</TableHead>
                  <TableHead className="text-moka-bistre dark:text-foreground font-normal">
                    <span className="sr-only">Acciones</span>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow className="border-moka-brown/30 dark:border-border">
                    <TableCell colSpan={8} className="h-24 text-center text-moka-brown/80 dark:text-muted-foreground">
                      <div className="flex items-center justify-center">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Cargando productos...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : productsData?.products.length ? (
                  productsData.products.map((product) => (
                    <TableRow key={product.id} className="border-moka-brown/30 dark:border-border hover:bg-moka-peach/10 dark:hover:bg-accent/50">
                      <TableCell className="font-normal text-moka-bistre dark:text-foreground">
                        <div>
                          <div>{product.nombre}</div>
                          {product.descripcion && (
                            <div className="text-sm text-moka-brown/80 dark:text-muted-foreground truncate max-w-[200px]">
                              {product.descripcion}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="hidden md:table-cell text-moka-brown/80 dark:text-muted-foreground">{product.codigoItem}</TableCell>
                      <TableCell className="hidden lg:table-cell text-moka-brown/80 dark:text-muted-foreground">{product.marca || '-'}</TableCell>
                      <TableCell>{getTipoAlmacenBadge(product.tipoAlmacen)}</TableCell>
                      <TableCell className="hidden sm:table-cell text-moka-brown/80 dark:text-muted-foreground">{product.ubicacion || '-'}</TableCell>
                      <TableCell className="text-right">
                        <div className="text-right">
                          <div className="font-normal text-moka-bistre dark:text-foreground">
                            {product.stockReal ?? (product.stockDisponible - product.stockComprometido)}
                          </div>
                          {product.stockComprometido > 0 && (
                            <div className="text-sm text-moka-brown/80 dark:text-muted-foreground">
                              ({product.stockComprometido} comprometido)
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{getStockBadge(product)}</TableCell>
                      <TableCell className="hidden md:table-cell">
                        {getEstadoBadge(product.estado)}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Abrir menú</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleView(product)}>
                              <Eye className="mr-2 h-4 w-4" />
                              Ver Detalles
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleEdit(product)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Editar Producto
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => handleDelete(product)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Eliminar Producto
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleGenerateQR(product)}>
                              <Package className="mr-2 h-4 w-4" />
                              Generar QR
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="h-24 text-center">
                      <div className="flex flex-col items-center">
                        <Package className="h-8 w-8 text-muted-foreground mb-2" />
                        <div>No se encontraron productos.</div>
                        <div className="text-sm text-muted-foreground">
                          Ajusta los filtros o agrega nuevos productos.
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          
          {/* Pagination */}
          {productsData && totalPages > 1 && (
            <div className="flex items-center justify-between px-2 py-4">
              <div className="text-sm text-muted-foreground">
                Página {filters.page || 1} de {totalPages}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => updateFilters({ page: Math.max(1, (filters.page || 1) - 1) })}
                  disabled={!filters.page || filters.page <= 1}
                >
                  Anterior
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => updateFilters({ page: (filters.page || 1) + 1 })}
                  disabled={!filters.page || filters.page >= totalPages}
                >
                  Siguiente
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modales */}
      <ProductDetailModal
        open={detailModal.open}
        product={detailModal.product}
        onOpenChange={(open) => setDetailModal({ open, product: open ? detailModal.product : null })}
      />

      <ProductFormModal
        open={formModal.open}
        product={formModal.product}
        onOpenChange={(open) => setFormModal({ open, product: open ? formModal.product : null })}
        onSubmit={handleFormSubmit}
        isLoading={createProductMutation.isPending || updateProductMutation.isPending}
      />

      <DeleteProductModal
        open={deleteModal.open}
        product={deleteModal.product}
        onOpenChange={(open) => setDeleteModal({ open, product: open ? deleteModal.product : null })}
        onConfirm={handleDeleteConfirm}
        isLoading={deleteProductMutation.isPending}
      />

      {/* Modal de Generador de QR */}
      {qrModal.product && (
        <ProductQRGenerator
          products={[qrModal.product]}
          onGenerate={(productId, qrData, qrImage) => {
            toast.success('QR generado correctamente', {
              description: `QR para ${qrModal.product?.nombre} generado y listo para imprimir`,
            })
            setQrModal({ open: false, product: null })
          }}
        />
      )}
      </FormCard>
    </div>
  )
}