"use client"

import React from "react"
import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Loader2, Package, Save, X } from "lucide-react"
import { Product, CreateProductData, UpdateProductData } from "@/lib/services/product"
import { toast } from "sonner"

const productFormSchema = z.object({
  codigoItem: z.string().min(1, "El código del producto es requerido"),
  nombre: z.string().min(1, "El nombre del producto es requerido"),
  descripcion: z.string().optional(),
  marca: z.string().optional(),
  modelo: z.string().optional(),
  numeroSerie: z.string().optional(),
  pedimento: z.string().optional(),
  observaciones: z.string().optional(),
  tipoAlmacen: z.enum(["GENERAL", "ROTATIVO"], {
    required_error: "Debe seleccionar un tipo de almacén",
  }),
  stockDisponible: z.coerce.number().min(0, "El stock debe ser mayor o igual a 0"),
  stockComprometido: z.coerce.number().min(0, "El stock comprometido debe ser mayor o igual a 0"),
  stockMinimo: z.coerce.number().min(0, "El stock mínimo debe ser mayor o igual a 0"),
  estado: z.enum(["DISPONIBLE", "AGOTADO", "DESCONTINUADO"], {
    required_error: "Debe seleccionar un estado",
  }),
  ubicacion: z.enum(["GDL", "MTY", "CDMX"], {
    required_error: "Debe seleccionar una ubicación",
  }),
})

type ProductFormData = z.infer<typeof productFormSchema>

interface ProductFormModalProps {
  product?: Product | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: CreateProductData | UpdateProductData) => Promise<void>
  isLoading?: boolean
}

export function ProductFormModal({ 
  product, 
  open, 
  onOpenChange, 
  onSubmit, 
  isLoading = false 
}: ProductFormModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const isEdit = !!product

  const form = useForm<ProductFormData>({
    resolver: zodResolver(productFormSchema),
    defaultValues: {
      codigoItem: "",
      nombre: "",
      descripcion: "",
      marca: "",
      modelo: "",
      numeroSerie: "",
      pedimento: "",
      observaciones: "",
      tipoAlmacen: "GENERAL",
      stockDisponible: 0,
      stockComprometido: 0,
      stockMinimo: 0,
      estado: "DISPONIBLE",
      ubicacion: "GDL",
    },
  })

  useEffect(() => {
    if (product && open) {
      form.reset({
        codigoItem: product.codigoItem,
        nombre: product.nombre,
        descripcion: product.descripcion || "",
        marca: product.marca || "",
        modelo: product.modelo || "",
        numeroSerie: product.numeroSerie || "",
        pedimento: product.pedimento || "",
        observaciones: product.observaciones || "",
        tipoAlmacen: product.tipoAlmacen,
        stockDisponible: product.stockDisponible,
        stockComprometido: product.stockComprometido,
        stockMinimo: product.stockMinimo,
        estado: product.estado,
        ubicacion: (product.ubicacion as "GDL" | "MTY" | "CDMX") || "GDL",
      })
    } else if (!product && open) {
      form.reset({
        codigoItem: "",
        nombre: "",
        descripcion: "",
        marca: "",
        modelo: "",
        numeroSerie: "",
        pedimento: "",
        observaciones: "",
        tipoAlmacen: "GENERAL",
        stockDisponible: 0,
        stockComprometido: 0,
        stockMinimo: 0,
        estado: "DISPONIBLE",
        ubicacion: "GDL",
      })
    }
  }, [product, open, form])

  const handleSubmit = async (data: ProductFormData) => {
    try {
      setIsSubmitting(true)
      
      const submitData = {
        ...data,
        descripcion: data.descripcion || undefined,
        marca: data.marca || undefined,
        modelo: data.modelo || undefined,
        numeroSerie: data.numeroSerie || undefined,
        pedimento: data.pedimento || undefined,
        observaciones: data.observaciones || undefined,
        ubicacion: data.ubicacion || undefined,
      }

      await onSubmit(submitData)
      
      toast.success(
        isEdit ? "Producto actualizado correctamente" : "Producto creado correctamente"
      )
      
      onOpenChange(false)
      
      if (!isEdit) {
        form.reset()
      }
    } catch (error) {
      console.error("Error al guardar producto:", error)
      toast.error(
        isEdit ? "Error al actualizar el producto" : "Error al crear el producto"
      )
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onOpenChange(false)
      form.reset()
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {isEdit ? "Editar Producto" : "Crear Nuevo Producto"}
          </DialogTitle>
          <DialogDescription>
            {isEdit 
              ? "Modifica la información del producto seleccionado"
              : "Ingresa la información del nuevo producto"
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Información básica */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Información Básica</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="codigoItem"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Código del Producto *</FormLabel>
                      <FormControl>
                        <Input placeholder="Ej: PROD001" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="nombre"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nombre *</FormLabel>
                      <FormControl>
                        <Input placeholder="Nombre del producto" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="descripcion"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Descripción</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Descripción detallada del producto"
                        rows={3}
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="marca"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Marca</FormLabel>
                      <FormControl>
                        <Input placeholder="Marca del producto" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="modelo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Modelo</FormLabel>
                      <FormControl>
                        <Input placeholder="Modelo del producto" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Categorización y estado */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Categorización</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="tipoAlmacen"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tipo de Almacén *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecciona tipo" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="GENERAL">General</SelectItem>
                          <SelectItem value="ROTATIVO">Rotativo</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="estado"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Estado *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecciona estado" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="DISPONIBLE">Disponible</SelectItem>
                          <SelectItem value="AGOTADO">Agotado</SelectItem>
                          <SelectItem value="DESCONTINUADO">Descontinuado</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Stock */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Inventario</h3>
              
              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="stockDisponible"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Stock Disponible *</FormLabel>
                      <FormControl>
                        <Input type="number" min="0" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="stockComprometido"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Stock Comprometido</FormLabel>
                      <FormControl>
                        <Input type="number" min="0" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="stockMinimo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Stock Mínimo *</FormLabel>
                      <FormControl>
                        <Input type="number" min="0" {...field} />
                      </FormControl>
                      <FormDescription>
                        Nivel mínimo de stock para alertas
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Información adicional */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Información Adicional</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="numeroSerie"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Número de Serie</FormLabel>
                      <FormControl>
                        <Input placeholder="Número de serie" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="pedimento"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pedimento</FormLabel>
                      <FormControl>
                        <Input placeholder="Número de pedimento" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="ubicacion"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ubicación *</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecciona ubicación" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="GDL">GDL</SelectItem>
                        <SelectItem value="MTY">MTY</SelectItem>
                        <SelectItem value="CDMX">CDMX</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="observaciones"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Observaciones</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Notas adicionales sobre el producto"
                        rows={3}
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter className="gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                <X className="h-4 w-4 mr-1" />
                Cancelar
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-1" />
                )}
                {isEdit ? "Actualizar" : "Crear"} Producto
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
} 