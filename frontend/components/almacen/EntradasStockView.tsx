"use client"

import { useState, useR<PERSON>, useEffect } from "react"
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
  CardFooter,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { QrCode, ScanLine, Package, CheckCircle, AlertTriangle, Plus, Minus } from "lucide-react"
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table"
import { toast } from "sonner"
import { FormCard } from "@/components/ui/form-styles"
import { productService } from "@/lib/services/product"
import { QRScanner } from "./QRScanner"

// --- Mock Data & Types ---
const mockInventoryData = [
  { id: "VLV-001", name: "Válvula de Bola Inoxidable 1/2\"", stock: 150 },
  { id: "SEN-001", name: "Sensor de Presión 0-100 PSI", stock: 45 },
  { id: "CAB-001", name: "Cable de Control 4 Hilos", stock: 8 },
  { id: "CON-001", name: "Conector M12 4 Pines", stock: 250 },
]

interface Product {
  id: string
  name: string
  stock: number
}

interface StockEntry {
  id: string
  name: string
  quantity: number
  timestamp: string
}

// --- Mock API Call ---
const findProductById = async (id: string): Promise<Product | null> => {
  await new Promise(resolve => setTimeout(resolve, 300)) // Simulate network latency
  const product = mockInventoryData.find(p => p.id.toLowerCase() === id.toLowerCase())
  return product || null
}

export function EntradasStockView() {
  const [scannedId, setScannedId] = useState("")
  const [foundProduct, setFoundProduct] = useState<Product | null>(null)
  const [quantity, setQuantity] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [recentEntries, setRecentEntries] = useState<StockEntry[]>([])
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    // Focus the input on initial render
    inputRef.current?.focus()
  }, [])

  const handleScan = async () => {
    if (!scannedId) return
    setIsLoading(true)
    setError("")
    setFoundProduct(null)
    try {
      // Intentar procesar como QR primero
      const qrResult = await productService.processQRData(scannedId)
      
      if (qrResult.success && qrResult.data?.producto) {
        // QR procesado exitosamente
        const product = qrResult.data.producto
        setFoundProduct({
          id: product.id.toString(),
          name: product.nombre,
          stock: product.stockDisponible
        })
        setQuantity(1)
        toast.success("Producto encontrado por QR", {
          description: `${product.nombre} (${product.codigoItem})`,
        })
      } else if (qrResult.data?.error) {
        // Error en el procesamiento QR, intentar búsqueda directa
        const product = await findProductById(scannedId)
        if (product) {
          setFoundProduct(product)
          setQuantity(1)
        } else {
          setError(`Producto con ID "${scannedId}" no encontrado.`)
          toast.error("Producto no encontrado", {
            description: `No se encontró ningún producto con el ID/SKU: ${scannedId}`,
          })
        }
      }
    } catch (e) {
      // Fallback a búsqueda directa
      try {
        const product = await findProductById(scannedId)
        if (product) {
          setFoundProduct(product)
          setQuantity(1)
        } else {
          setError(`Producto con ID "${scannedId}" no encontrado.`)
          toast.error("Producto no encontrado", {
            description: `No se encontró ningún producto con el ID/SKU: ${scannedId}`,
          })
        }
      } catch (fallbackError) {
        setError("Error al buscar el producto.")
      }
    } finally {
      setIsLoading(false)
    }
  }

  // Manejar datos escaneados por QR
  const handleQRScanned = (qrData: string) => {
    setScannedId(qrData)
    // Automáticamente buscar el producto después de escanear
    setTimeout(() => {
      handleScan()
    }, 100)
  }

  const handleRegisterEntry = () => {
    if (!foundProduct || quantity <= 0) return

    const newEntry: StockEntry = {
      id: foundProduct.id,
      name: foundProduct.name,
      quantity: quantity,
      timestamp: new Date().toLocaleTimeString(),
    }

    setRecentEntries([newEntry, ...recentEntries])
    
    toast.success("Entrada Registrada", {
      description: `${quantity} unidad(es) de "${foundProduct.name}" añadidas al stock.`,
    })

    // Reset for next scan
    setFoundProduct(null)
    setScannedId("")
    setQuantity(1)
    inputRef.current?.focus()
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <FormCard>
        <header>
          <h1 className="text-3xl font-bold tracking-tight">Entradas de Stock</h1>
          <p className="text-muted-foreground">
            Registra nuevos productos en el inventario usando un escáner de QR o código de barras.
          </p>
        </header>

      <div className="grid md:grid-cols-2 gap-8">
        <Card className="flex flex-col">
          <CardHeader>
            <CardTitle className="flex items-center">
              <ScanLine className="mr-2 h-6 w-6" />
              Escanear Producto
            </CardTitle>
            <CardDescription>
              Ingresa el ID del producto, escanea el código QR o usa el escáner de cámara.
            </CardDescription>
          </CardHeader>
          <CardContent className="flex-grow">
            <form onSubmit={(e) => { e.preventDefault(); handleScan(); }}>
              <div className="space-y-3">
                <div className="flex gap-2">
                  <Input
                    ref={inputRef}
                    placeholder="ID, SKU o código de barras..."
                    value={scannedId}
                    onChange={(e) => setScannedId(e.target.value)}
                    disabled={isLoading}
                    className="text-lg h-12"
                  />
                  <Button type="submit" size="lg" disabled={isLoading || !scannedId}>
                    {isLoading ? "Buscando..." : "Buscar"}
                  </Button>
                </div>
                
                {/* Botón de escaneo QR para móviles */}
                <div className="flex justify-center">
                  <QRScanner
                    onQRScanned={handleQRScanned}
                    title="Escanear QR"
                    description="Escanea un código QR de producto con la cámara"
                    placeholder="Ingresa código de producto..."
                  />
                </div>
              </div>
            </form>

            {error && (
              <div className="mt-4 flex items-center text-red-600 bg-red-50 p-3 rounded-md">
                <AlertTriangle className="h-5 w-5 mr-2" />
                <span className="text-sm">{error}</span>
              </div>
            )}

            {foundProduct && (
              <Card className="mt-6 bg-slate-50 dark:bg-slate-800/50">
                <CardHeader>
                  <CardTitle className="text-xl">{foundProduct.name}</CardTitle>
                  <CardDescription>ID: {foundProduct.id}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Stock Actual:</span>
                    <span className="text-lg font-bold">{foundProduct.stock}</span>
                  </div>
                  <div className="mt-4">
                    <label htmlFor="quantity" className="block text-sm font-medium mb-2">
                      Cantidad a Ingresar:
                    </label>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="icon" onClick={() => setQuantity(q => Math.max(1, q - 1))}><Minus className="h-4 w-4"/></Button>
                      <Input
                        id="quantity"
                        type="number"
                        value={quantity}
                        onChange={(e) => setQuantity(Number(e.target.value))}
                        className="h-12 text-center text-xl font-bold w-24"
                        min="1"
                      />
                      <Button variant="outline" size="icon" onClick={() => setQuantity(q => q + 1)}><Plus className="h-4 w-4"/></Button>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button className="w-full" size="lg" onClick={handleRegisterEntry}>
                    <CheckCircle className="mr-2 h-5 w-5" />
                    Registrar Entrada
                  </Button>
                </CardFooter>
              </Card>
            )}
          </CardContent>
        </Card>

        <Card className="bg-moka-peach/20 dark:bg-card border-moka-brown/40 dark:border-border backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="font-medium text-moka-bistre dark:text-foreground">Entradas Recientes</CardTitle>
            <CardDescription className="text-moka-brown/80 dark:text-muted-foreground">Historial de los últimos productos ingresados hoy.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border border-moka-brown/40 dark:border-border bg-moka-peach/10 dark:bg-background backdrop-blur-sm">
              <Table>
                <TableHeader>
                  <TableRow className="border-moka-brown/30 dark:border-border">
                    <TableHead className="text-moka-bistre dark:text-foreground font-normal">Producto</TableHead>
                    <TableHead className="text-right text-moka-bistre dark:text-foreground font-normal">Cantidad</TableHead>
                    <TableHead className="text-right text-moka-bistre dark:text-foreground font-normal">Hora</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentEntries.length > 0 ? (
                    recentEntries.map((entry, index) => (
                      <TableRow key={index} className="border-moka-brown/30 dark:border-border hover:bg-moka-peach/10 dark:hover:bg-accent/50">
                        <TableCell className="font-normal text-moka-bistre dark:text-foreground">{entry.name}</TableCell>
                        <TableCell className="text-right font-normal text-moka-lion">+{entry.quantity}</TableCell>
                        <TableCell className="text-right text-moka-brown/80 dark:text-muted-foreground">{entry.timestamp}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow className="border-moka-brown/30 dark:border-border">
                      <TableCell colSpan={3} className="h-24 text-center text-moka-brown/80 dark:text-muted-foreground">
                        No hay entradas recientes.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
      </FormCard>
    </div>
  )
}