"use client"

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardFooter,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Calendar as CalendarIcon, Send } from "lucide-react"
import { DateRange } from "react-day-picker"
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import { cn } from "@/lib/utils"
import { useState } from "react"
import { toast } from "sonner"
import { FormCard } from "@/components/ui/form-styles"

export function ViaticosView() {
  const [date, setDate] = useState<DateRange | undefined>()

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    const formData = new FormData(e.target as HTMLFormElement)
    const destination = formData.get("destination")
    const reason = formData.get("reason")
    const amount = formData.get("amount")

    if (!destination || !reason || !amount || !date) {
        toast.error("Faltan datos", { description: "Por favor, completa todos los campos del formulario." })
        return
    }

    toast.success("Solicitud Enviada", {
        description: `Tu solicitud de viáticos para ${destination} ha sido enviada para su aprobación.`
    })

    // Aquí iría la lógica para enviar los datos al backend
    console.log({
        destination,
        reason,
        amount,
        startDate: date.from,
        endDate: date.to,
    })
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <FormCard>
        <header>
          <h1 className="text-3xl font-bold tracking-tight">Solicitud de Viáticos</h1>
          <p className="text-muted-foreground">
            Completa el formulario para solicitar viáticos para tu próximo viaje de trabajo.
          </p>
        </header>
      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Detalles del Viaje</CardTitle>
            <CardDescription>Proporciona la información necesaria para la solicitud.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid sm:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="destination">Destino</Label>
                <Input id="destination" name="destination" placeholder="Ej: Monterrey, NL" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="date">Fechas del Viaje</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="date"
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !date && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date?.from ? (
                        date.to ? (
                          <>
                            {format(date.from, "LLL dd, y", { locale: es })} -{" "}
                            {format(date.to, "LLL dd, y", { locale: es })}
                          </>
                        ) : (
                          format(date.from, "LLL dd, y", { locale: es })
                        )
                      ) : (
                        <span>Elige un rango de fechas</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      initialFocus
                      mode="range"
                      defaultMonth={date?.from}
                      selected={date}
                      onSelect={setDate}
                      numberOfMonths={2}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="reason">Motivo del Viaje</Label>
              <Textarea id="reason" name="reason" placeholder="Ej: Visita a cliente para instalación de equipo..." />
            </div>
            <div className="space-y-2">
              <Label htmlFor="amount">Monto Estimado (MXN)</Label>
              <Input id="amount" name="amount" type="number" placeholder="Ej: 5000" />
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" size="lg">
              <Send className="mr-2 h-4 w-4" />
              Enviar Solicitud
            </Button>
          </CardFooter>
        </Card>
      </form>
      </FormCard>
    </div>
  )
}