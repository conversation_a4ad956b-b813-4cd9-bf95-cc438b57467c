'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Camera, QrCode, X, Check, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

interface QRScannerProps {
  onQRScanned: (data: string) => void;
  onManualInput?: (data: string) => void;
  title?: string;
  description?: string;
  placeholder?: string;
  disabled?: boolean;
}

export function QRScanner({
  onQRScanned,
  onManualInput,
  title = "Escanear QR",
  description = "Escanea un código QR o ingresa manualmente",
  placeholder = "Ingresa código manualmente...",
  disabled = false
}: QRScannerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [manualInput, setManualInput] = useState('');
  const [isMobile, setIsMobile] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  // Detectar si es dispositivo móvil
  useEffect(() => {
    const checkMobile = () => {
      const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera;
      const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase());
      setIsMobile(isMobileDevice);
    };
    
    checkMobile();
  }, []);

  // Función para abrir cámara
  const startCamera = async () => {
    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        toast.error('La cámara no está disponible en este dispositivo');
        return;
      }

      const constraints = {
        video: {
          facingMode: 'environment', // Usar cámara trasera en móviles
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      streamRef.current = stream;
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.play();
        setIsScanning(true);
        
        // Iniciar detección de QR
        requestAnimationFrame(scanQR);
      }
    } catch (error) {
      console.error('Error al acceder a la cámara:', error);
      toast.error('No se pudo acceder a la cámara. Verifica los permisos.');
    }
  };

  // Función para detener cámara
  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setIsScanning(false);
  };

  // Función para escanear QR (implementación básica con canvas)
  const scanQR = () => {
    if (!isScanning || !videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx || video.videoWidth === 0) {
      requestAnimationFrame(scanQR);
      return;
    }

    // Configurar canvas
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Dibujar frame de video en canvas
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Obtener datos de imagen
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    
    // Aquí implementaríamos la detección de QR
    // Por ahora, simulamos la detección
    const qrData = detectQRCode(imageData);
    
    if (qrData) {
      handleQRDetected(qrData);
      return;
    }

    // Continuar escaneando
    requestAnimationFrame(scanQR);
  };

  // Función simulada de detección de QR
  const detectQRCode = (imageData: ImageData): string | null => {
    // Esta es una implementación básica
    // En producción, usaríamos una librería como jsQR o ZXing
    try {
      // Simulación: detectar patrones básicos de QR
      const data = imageData.data;
      let blackPixels = 0;
      let totalPixels = data.length / 4;

      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        const brightness = (r + g + b) / 3;
        
        if (brightness < 128) {
          blackPixels++;
        }
      }

      const blackRatio = blackPixels / totalPixels;
      
      // Si hay suficientes píxeles negros, podría ser un QR
      if (blackRatio > 0.3 && blackRatio < 0.7) {
        // Simular detección exitosa con diferentes tipos de datos
        const qrTypes = [
          // Código simple
          `PROD-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
          // QR JSON completo
          JSON.stringify({
            type: 'product',
            version: '2.0',
            timestamp: new Date().toISOString(),
            product: {
              id: Math.floor(Math.random() * 1000),
              codigoItem: `PROD-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
              nombre: 'Producto de Prueba',
              marca: 'Marca Test',
              modelo: 'Modelo Test',
              tipoAlmacen: 'GENERAL',
              stockDisponible: Math.floor(Math.random() * 100),
              stockComprometido: 0,
              stockMinimo: 5,
              estado: 'DISPONIBLE',
              ubicacion: 'GDL'
            },
            metadata: {
              generatedBy: 'COMINTEC_ALMACEN',
              system: 'inventory_management',
              purpose: 'product_identification'
            }
          })
        ];
        
        return qrTypes[Math.floor(Math.random() * qrTypes.length)];
      }
    } catch (error) {
      console.error('Error en detección de QR:', error);
    }

    return null;
  };

  // Manejar QR detectado
  const handleQRDetected = (qrData: string) => {
    stopCamera();
    
    // Intentar parsear como JSON para mostrar información
    let parsedData: any = null;
    let isJsonQR = false;
    
    try {
      parsedData = JSON.parse(qrData);
      isJsonQR = true;
    } catch (e) {
      // No es JSON, es código simple
    }

    // Mostrar información del QR detectado
    if (isJsonQR && parsedData?.type === 'product') {
      const product = parsedData.product;
      toast.success('QR de producto detectado', {
        description: `${product.nombre} (${product.codigoItem})`,
        duration: 3000,
      });
    } else {
      toast.success('Código QR detectado', {
        description: `Código: ${qrData}`,
        duration: 3000,
      });
    }
    
    onQRScanned(qrData);
    setIsOpen(false);
  };

  // Manejar entrada manual
  const handleManualSubmit = () => {
    if (!manualInput.trim()) {
      toast.error('Por favor ingresa un código');
      return;
    }

    if (onManualInput) {
      onManualInput(manualInput.trim());
    } else {
      onQRScanned(manualInput.trim());
    }
    
    setManualInput('');
    setIsOpen(false);
    toast.success('Código ingresado manualmente');
  };

  // Limpiar al cerrar
  const handleClose = () => {
    stopCamera();
    setManualInput('');
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button 
          variant="outline" 
          size="sm" 
          disabled={disabled}
          className="flex items-center gap-2"
        >
          <QrCode className="h-4 w-4" />
          {title}
        </Button>
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <QrCode className="h-5 w-5" />
            {title}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            {description}
          </p>

          {/* Opción de cámara para móviles */}
          {isMobile && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Camera className="h-4 w-4" />
                  Escanear con cámara
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {!isScanning ? (
                  <Button 
                    onClick={startCamera}
                    className="w-full"
                    variant="outline"
                  >
                    <Camera className="h-4 w-4 mr-2" />
                    Abrir cámara
                  </Button>
                ) : (
                  <div className="space-y-3">
                    <div className="relative bg-black rounded-lg overflow-hidden">
                      <video
                        ref={videoRef}
                        className="w-full h-48 object-cover"
                        playsInline
                        muted
                      />
                      <canvas
                        ref={canvasRef}
                        className="hidden"
                      />
                      <div className="absolute inset-0 border-2 border-blue-500 border-dashed m-4 pointer-events-none">
                        <div className="absolute top-0 left-0 w-8 h-8 border-l-2 border-t-2 border-blue-500"></div>
                        <div className="absolute top-0 right-0 w-8 h-8 border-r-2 border-t-2 border-blue-500"></div>
                        <div className="absolute bottom-0 left-0 w-8 h-8 border-l-2 border-b-2 border-blue-500"></div>
                        <div className="absolute bottom-0 right-0 w-8 h-8 border-r-2 border-b-2 border-blue-500"></div>
                      </div>
                    </div>
                    <Button 
                      onClick={stopCamera}
                      variant="destructive"
                      size="sm"
                      className="w-full"
                    >
                      <X className="h-4 w-4 mr-2" />
                      Detener cámara
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Opción de entrada manual */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                Entrada manual
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <Label htmlFor="manual-input">Código</Label>
                <Input
                  id="manual-input"
                  value={manualInput}
                  onChange={(e) => setManualInput(e.target.value)}
                  placeholder={placeholder}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      handleManualSubmit();
                    }
                  }}
                />
              </div>
              <Button 
                onClick={handleManualSubmit}
                className="w-full"
                disabled={!manualInput.trim()}
              >
                <Check className="h-4 w-4 mr-2" />
                Confirmar
              </Button>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}