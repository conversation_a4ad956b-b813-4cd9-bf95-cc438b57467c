"use client"
import React from "react"
import type { Meta, StoryObj } from "@storybook/react"
import { fn } from "@storybook/test" // Si usas Storybook 8+
import { ProductFormModal } from "./ProductFormModal"
import type { Product } from "@/lib/services/product"

// Mock data for the product in edit mode
const mockProduct: Product = {
  id: 1,
  codigoItem: "PROD-001",
  nombre: "Laptop Pro 15 pulgadas",
  descripcion: "Laptop de alto rendimiento para profesionales.",
  marca: "TechCorp",
  modelo: "TX-1500",
  numeroSerie: "SN123456789",
  pedimento: "PED-987654",
  observaciones: "Caja ligeramente dañada.",
  tipoAlmacen: "GENERAL",
  stockDisponible: 25,
  stockComprometido: 5,
  stockMinimo: 10,
  estado: "DISPONIBLE",
  ubicacion: "Estante A, Nivel 3",
  createdAt: new Date("2023-01-15T09:00:00Z"),
  updatedAt: new Date("2023-10-20T14:30:00Z"),
}

const meta: Meta<typeof ProductFormModal> = {
  title: "Almacen/ProductFormModal",
  component: ProductFormModal,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    open: { control: "boolean" },
    isLoading: { control: "boolean" },
  },
  args: {
    open: true,
    isLoading: false,
    onOpenChange: fn(),
    onSubmit: fn(),
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const CrearProducto: Story = {
  args: {
    product: null,
  },
  render: (args) => (
    <div className="h-[500px] w-[800px]">
      <ProductFormModal {...args} />
    </div>
  ),
}

export const EditarProducto: Story = {
  args: {
    product: mockProduct,
  },
  render: (args) => (
    <div className="h-[500px] w-[800px]">
      <ProductFormModal {...args} />
    </div>
  ),
}

export const CreandoProducto: Story = {
  args: {
    product: null,
    isLoading: true,
  },
  render: (args) => (
    <div className="h-[500px] w-[800px]">
      <ProductFormModal {...args} />
    </div>
  ),
}

export const EditandoProducto: Story = {
  args: {
    product: mockProduct,
    isLoading: true,
  },
  render: (args) => (
    <div className="h-[500px] w-[800px]">
      <ProductFormModal {...args} />
    </div>
  ),
} 