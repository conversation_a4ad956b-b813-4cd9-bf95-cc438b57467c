'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { QrCode, Download, Copy, Check } from 'lucide-react';
import { toast } from 'sonner';

interface QRGeneratorProps {
  defaultData?: string;
  title?: string;
  description?: string;
  onGenerate?: (data: string, qrImage: string) => void;
  disabled?: boolean;
}

export function QRGenerator({
  defaultData = '',
  title = "Generar QR",
  description = "Genera un código QR con los datos especificados",
  onGenerate,
  disabled = false
}: QRGeneratorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [qrData, setQrData] = useState(defaultData);
  const [qrImage, setQrImage] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Generar QR usando canvas nativo (sin dependencias externas)
  const generateQR = async () => {
    if (!qrData.trim()) {
      toast.error('Por favor ingresa datos para generar el QR');
      return;
    }

    setIsGenerating(true);
    
    try {
      // Usar la API nativa de QR del navegador si está disponible
      if ('QRCode' in window) {
        await generateQRWithNativeAPI();
      } else {
        // Fallback: generar QR básico con canvas
        await generateQRWithCanvas();
      }
    } catch (error) {
      console.error('Error generando QR:', error);
      toast.error('Error al generar el código QR');
    } finally {
      setIsGenerating(false);
    }
  };

  // Generar QR con API nativa del navegador
  const generateQRWithNativeAPI = async () => {
    try {
      // Esta es una implementación simulada
      // En navegadores modernos podríamos usar la Web API para QR
      const qrCode = new (window as any).QRCode(canvasRef.current, {
        text: qrData,
        width: 256,
        height: 256,
        colorDark: '#000000',
        colorLight: '#ffffff',
        correctLevel: (window as any).QRCode.CorrectLevel.H
      });
      
      // Convertir canvas a imagen
      const imageData = canvasRef.current?.toDataURL('image/png');
      if (imageData) {
        setQrImage(imageData);
        if (onGenerate) {
          onGenerate(qrData, imageData);
        }
        toast.success('Código QR generado correctamente');
      }
    } catch (error) {
      // Fallback a canvas
      await generateQRWithCanvas();
    }
  };

  // Generar QR con canvas (fallback)
  const generateQRWithCanvas = async () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Configurar canvas
    canvas.width = 256;
    canvas.height = 256;

    // Limpiar canvas
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Generar patrón QR básico (simulado)
    const qrPattern = generateBasicQRPattern(qrData);
    
    // Dibujar patrón
    ctx.fillStyle = '#000000';
    const cellSize = canvas.width / qrPattern.length;
    
    for (let row = 0; row < qrPattern.length; row++) {
      for (let col = 0; col < qrPattern[row].length; col++) {
        if (qrPattern[row][col]) {
          ctx.fillRect(
            col * cellSize,
            row * cellSize,
            cellSize,
            cellSize
          );
        }
      }
    }

    // Convertir a imagen
    const imageData = canvas.toDataURL('image/png');
    setQrImage(imageData);
    
    if (onGenerate) {
      onGenerate(qrData, imageData);
    }
    
    toast.success('Código QR generado correctamente');
  };

  // Generar patrón QR básico (simulado)
  const generateBasicQRPattern = (data: string): boolean[][] => {
    // Esta es una implementación básica que simula un QR
    // En producción, usaríamos una librería como qrcode.js
    
    const size = 25; // Tamaño del patrón QR
    const pattern: boolean[][] = [];
    
    // Inicializar patrón
    for (let i = 0; i < size; i++) {
      pattern[i] = [];
      for (let j = 0; j < size; j++) {
        pattern[i][j] = false;
      }
    }

    // Generar patrón basado en los datos
    const hash = simpleHash(data);
    let index = 0;
    
    for (let i = 0; i < size; i++) {
      for (let j = 0; j < size; j++) {
        // Patrón de esquinas (marcadores de posición)
        if ((i < 7 && j < 7) || (i < 7 && j >= size - 7) || (i >= size - 7 && j < 7)) {
          pattern[i][j] = true;
        }
        // Patrón de datos
        else if (index < hash.length) {
          pattern[i][j] = (hash.charCodeAt(index) % 2) === 0;
          index++;
        }
      }
    }

    return pattern;
  };

  // Hash simple para generar patrón
  const simpleHash = (str: string): string => {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convertir a 32-bit integer
    }
    return hash.toString(16);
  };

  // Descargar QR
  const downloadQR = () => {
    if (!qrImage) {
      toast.error('No hay código QR para descargar');
      return;
    }

    const link = document.createElement('a');
    link.download = `qr_${Date.now()}.png`;
    link.href = qrImage;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast.success('Código QR descargado');
  };

  // Copiar datos al portapapeles
  const copyData = async () => {
    try {
      await navigator.clipboard.writeText(qrData);
      toast.success('Datos copiados al portapapeles');
    } catch (error) {
      console.error('Error copiando datos:', error);
      toast.error('No se pudo copiar al portapapeles');
    }
  };

  // Limpiar al cerrar
  const handleClose = () => {
    setQrData(defaultData);
    setQrImage('');
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button 
          variant="outline" 
          size="sm" 
          disabled={disabled}
          className="flex items-center gap-2"
        >
          <QrCode className="h-4 w-4" />
          {title}
        </Button>
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <QrCode className="h-5 w-5" />
            {title}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            {description}
          </p>

          {/* Entrada de datos */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Datos del QR</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <Label htmlFor="qr-data">Contenido</Label>
                <Textarea
                  id="qr-data"
                  value={qrData}
                  onChange={(e) => setQrData(e.target.value)}
                  placeholder="Ingresa el contenido para el código QR..."
                  rows={3}
                />
              </div>
              <Button 
                onClick={generateQR}
                className="w-full"
                disabled={!qrData.trim() || isGenerating}
              >
                {isGenerating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Generando...
                  </>
                ) : (
                  <>
                    <QrCode className="h-4 w-4 mr-2" />
                    Generar QR
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Visualización del QR */}
          {qrImage && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Código QR Generado</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-center">
                  <img 
                    src={qrImage} 
                    alt="Código QR" 
                    className="border rounded-lg"
                    style={{ maxWidth: '200px', maxHeight: '200px' }}
                  />
                </div>
                
                <canvas
                  ref={canvasRef}
                  className="hidden"
                />
                
                <div className="flex gap-2">
                  <Button 
                    onClick={downloadQR}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Descargar
                  </Button>
                  <Button 
                    onClick={copyData}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copiar Datos
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}