"use client"

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardContent,
  CardDescription,
  CardFooter,
} from "@/components/ui/card"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import { Button } from "@/components/ui/button"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, PieChart, Pie, Cell } from "recharts"
import { Download } from "lucide-react"
import { FormCard } from "@/components/ui/form-styles"

// --- Mock Data ---
const salesByBrandData = [
  { brand: "Parker", sales: 4500 },
  { brand: "Honeywell", sales: 3200 },
  { brand: "Belden", sales: 2800 },
  { brand: "<PERSON>rck", sales: 2500 },
  { brand: "Rockwell", sales: 1800 },
  { brand: "Siemens", sales: 1500 },
  { brand: "Festo", sales: 1200 },
]

const salesByModelData = [
  { name: "Vál<PERSON>la de Bola", value: 400 },
  { name: "Sensor de Presión", value: 300 },
  { name: "Conector M12", value: 250 },
  { name: "Cable de Control", value: 200 },
  { name: "<PERSON><PERSON><PERSON>", value: 180 },
]

// Colores usando la paleta moka
const COLORS = ["#6f1d1b", "#cc9f69", "#ffe6a7", "#a66030", "#432818"]; // moka-falu, moka-lion, moka-peach, moka-brown, moka-bistre

export function ReporteInventarioView() {
  return (
    <div className="max-w-7xl mx-auto p-6">
      <FormCard>
        <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Reporte de Inventario</h1>
            <p className="text-muted-foreground">
              Análisis gráfico de ventas por marcas y modelos de equipos.
            </p>
          </div>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Exportar a Excel
          </Button>
        </header>

      <div className="grid lg:grid-cols-2 gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Ventas por Marca</CardTitle>
            <CardDescription>Total de ventas ($) de los últimos 6 meses.</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={{}} className="h-[300px] w-full">
              <BarChart data={salesByBrandData} margin={{ top: 5, right: 20, left: -10, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="brand" tick={{ fontSize: 12 }} />
                <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  cursor={{ fill: "hsl(var(--muted) / 0.5)" }}
                />
                <Bar dataKey="sales" fill="#6f1d1b" radius={4} />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Ventas por Modelo de Equipo</CardTitle>
            <CardDescription>Distribución de unidades vendidas.</CardDescription>
          </CardHeader>
          <CardContent className="flex items-center justify-center">
            <ChartContainer config={{}} className="h-[300px] w-full">
              <PieChart>
                <ChartTooltip
                  content={<ChartTooltipContent nameKey="name" hideLabel />}
                />
                <Pie
                  data={salesByModelData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={120}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {salesByModelData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
              </PieChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>
      </FormCard>
    </div>
  )
}