'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { QrCode, Download, Printer, Package, Info } from 'lucide-react';
import { toast } from 'sonner';
import { QRGenerator } from './QRGenerator';
import { productService, Product, QRGenerateData } from '@/lib/services/product';

interface Product {
  id: number;
  codigoItem: string;
  nombre: string;
  descripcion?: string;
  marca?: string;
  modelo?: string;
  numeroSerie?: string;
  pedimento?: string;
  tipoAlmacen: 'GENERAL' | 'ROTATIVO';
  stockDisponible: number;
  stockComprometido: number;
  stockMinimo: number;
  estado: 'DISPONIBLE' | 'AGOTADO' | 'DESCONTINUADO';
  ubicacion?: string;
  observaciones?: string;
}

interface ProductQRGeneratorProps {
  products: Product[];
  onGenerate?: (productId: string, qrData: string, qrImage: string) => void;
  disabled?: boolean;
}

export function ProductQRGenerator({
  products,
  onGenerate,
  disabled = false
}: ProductQRGeneratorProps) {
  const [selectedProduct, setSelectedProduct] = useState<string>('');
  const [qrImage, setQrImage] = useState<string>('');
  const [generatedQRData, setGeneratedQRData] = useState<string>('');

  const handleProductSelect = (productId: string) => {
    setSelectedProduct(productId);
    const product = products.find(p => p.id === parseInt(productId));
    if (product) {
      // Generar datos QR completos usando el servicio
      const qrData = productService.generateQRData(product);
      setGeneratedQRData(JSON.stringify(qrData));
    }
  };

  const handleQRGenerated = (data: string, image: string) => {
    setQrImage(image);
    if (onGenerate && selectedProduct) {
      onGenerate(selectedProduct, data, image);
    }
  };

  const handlePrintQR = () => {
    if (!qrImage) {
      toast.error('No hay código QR para imprimir');
      return;
    }

    // Crear ventana de impresión mejorada
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const selectedProductData = products.find(p => p.id === selectedProduct);
      
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Etiqueta QR - ${selectedProductData?.nombre}</title>
          <style>
            body { 
              font-family: Arial, sans-serif; 
              margin: 0; 
              padding: 20px; 
              text-align: center;
            }
            .qr-label {
              border: 2px solid #000;
              padding: 20px;
              max-width: 350px;
              margin: 0 auto;
            }
            .qr-code {
              margin: 15px 0;
            }
            .product-info {
              margin-top: 15px;
              font-size: 12px;
              text-align: left;
            }
            .product-name {
              font-weight: bold;
              font-size: 16px;
              margin-bottom: 8px;
              text-align: center;
            }
            .product-id {
              color: #666;
              margin-bottom: 5px;
            }
            .product-details {
              margin-top: 10px;
              padding: 8px;
              background: #f5f5f5;
              border-radius: 4px;
            }
            .detail-row {
              display: flex;
              justify-content: space-between;
              margin-bottom: 3px;
              font-size: 11px;
            }
            .detail-label {
              font-weight: bold;
              color: #333;
            }
            .detail-value {
              color: #666;
            }
            .stock-info {
              margin-top: 8px;
              padding: 5px;
              background: #e8f5e8;
              border-radius: 3px;
              font-size: 11px;
            }
            @media print {
              body { margin: 0; }
              .qr-label { border: 1px solid #000; }
            }
          </style>
        </head>
        <body>
          <div class="qr-label">
            <div class="product-info">
              <div class="product-name">${selectedProductData?.nombre || 'Producto'}</div>
              <div class="product-id">Código: ${selectedProductData?.codigoItem || ''}</div>
              
              <div class="product-details">
                <div class="detail-row">
                  <span class="detail-label">Marca:</span>
                  <span class="detail-value">${selectedProductData?.marca || 'N/A'}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">Modelo:</span>
                  <span class="detail-value">${selectedProductData?.modelo || 'N/A'}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">Tipo:</span>
                  <span class="detail-value">${selectedProductData?.tipoAlmacen || 'GENERAL'}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">Ubicación:</span>
                  <span class="detail-value">${selectedProductData?.ubicacion || 'GDL'}</span>
                </div>
              </div>
              
              <div class="stock-info">
                <div class="detail-row">
                  <span class="detail-label">Stock Disponible:</span>
                  <span class="detail-value">${selectedProductData?.stockDisponible || 0}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">Stock Mínimo:</span>
                  <span class="detail-value">${selectedProductData?.stockMinimo || 1}</span>
                </div>
              </div>
            </div>
            
            <div class="qr-code">
              <img src="${qrImage}" alt="Código QR" style="max-width: 200px; height: auto;" />
            </div>
            
            <div style="font-size: 10px; color: #999; margin-top: 10px;">
              Escanea para verificar producto - COMINTEC Almacén
            </div>
          </div>
        </body>
        </html>
      `);
      
      printWindow.document.close();
      printWindow.print();
    }
  };

  const selectedProductData = products.find(p => p.id === selectedProduct);

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button 
          variant="outline" 
          size="sm" 
          disabled={disabled}
          className="flex items-center gap-2"
        >
          <QrCode className="h-4 w-4" />
          Generar QR de Producto
        </Button>
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Generar QR de Producto
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Genera un código QR completo para etiquetar productos físicos en el almacén.
          </p>

          {/* Selección de producto */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Seleccionar Producto</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <Label htmlFor="product-select">Producto</Label>
                <Select value={selectedProduct} onValueChange={handleProductSelect}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona un producto..." />
                  </SelectTrigger>
                  <SelectContent>
                    {products.map(product => (
                      <SelectItem key={product.id} value={product.id}>
                        {product.nombre} ({product.codigoItem})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedProductData && (
                <div className="p-3 bg-muted rounded-md">
                  <div className="text-sm space-y-2">
                    <div className="font-medium text-base">{selectedProductData.nombre}</div>
                    <div className="text-muted-foreground">Código: {selectedProductData.codigoItem}</div>
                    {selectedProductData.marca && (
                      <div className="text-muted-foreground">Marca: {selectedProductData.marca}</div>
                    )}
                    {selectedProductData.modelo && (
                      <div className="text-muted-foreground">Modelo: {selectedProductData.modelo}</div>
                    )}
                    <div className="text-muted-foreground">
                      Tipo: {selectedProductData.tipoAlmacen}
                    </div>
                    <div className="text-muted-foreground">
                      Ubicación: {selectedProductData.ubicacion || 'GDL'}
                    </div>
                    <div className="text-muted-foreground">
                      Stock: {selectedProductData.stockDisponible} disponible, {selectedProductData.stockComprometido} comprometido
                    </div>
                    <div className="text-muted-foreground">
                      Estado: {selectedProductData.estado}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Generador de QR */}
          {selectedProduct && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Generar Código QR</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <QRGenerator
                  defaultData={generatedQRData}
                  title="Generar QR"
                  description="Genera el código QR completo para este producto"
                  onGenerate={handleQRGenerated}
                />
              </CardContent>
            </Card>
          )}

          {/* Acciones con QR generado */}
          {qrImage && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Acciones</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex gap-2">
                  <Button 
                    onClick={handlePrintQR}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    <Printer className="h-4 w-4 mr-2" />
                    Imprimir Etiqueta
                  </Button>
                  <Button 
                    onClick={() => {
                      const link = document.createElement('a');
                      link.download = `qr_${selectedProductData?.codigoItem}_${Date.now()}.png`;
                      link.href = qrImage;
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                      toast.success('Código QR descargado');
                    }}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Descargar
                  </Button>
                </div>
                
                <div className="text-xs text-muted-foreground flex items-center gap-1">
                  <Info className="h-3 w-3" />
                  El QR contiene información completa del producto para identificación rápida
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}