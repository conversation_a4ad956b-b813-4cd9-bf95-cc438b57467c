"use client"

import { <PERSON>u, <PERSON> } from "lucide-react"
import { cn } from "@/lib/utils"

interface MobileMenuButtonProps {
  isOpen: boolean
  onClick: () => void
  darkMode?: boolean
  className?: string
}

export function MobileMenuButton({ 
  isOpen, 
  onClick, 
  darkMode = false, 
  className 
}: MobileMenuButtonProps) {
  return (
    <button
      onClick={onClick}
      className={cn(
        "md:hidden p-2 rounded-lg transition-all duration-200",
        "hover:scale-105 active:scale-95",
        darkMode
          ? "text-gray-100 hover:bg-white/10"
          : "text-white hover:bg-white/10",
        className
      )}
      aria-label={isOpen ? "Cerrar menú" : "Abrir menú"}
    >
      <div className="relative w-6 h-6">
        <Menu 
          className={cn(
            "absolute inset-0 transition-all duration-300",
            isOpen ? "opacity-0 rotate-180 scale-0" : "opacity-100 rotate-0 scale-100"
          )}
          size={24}
        />
        <X 
          className={cn(
            "absolute inset-0 transition-all duration-300",
            isOpen ? "opacity-100 rotate-0 scale-100" : "opacity-0 rotate-180 scale-0"
          )}
          size={24}
        />
      </div>
    </button>
  )
}
