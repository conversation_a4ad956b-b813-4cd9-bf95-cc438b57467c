"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  ShoppingCart,
  Truck,
  ChevronRight,
  Home,
  Briefcase,
  CheckSquare,
  UsersRound,
  FileText,
  Archive,
  DatabaseZap,
  FlaskConical,
  ConciergeBell,
  UserCog,
  FileSearch,
} from "lucide-react"

const menuItems = [
  {
    title: "Dashboard",
    icon: Home,
    href: "/dashboard",
  },
  {
    title: "Administración",
    icon: Briefcase,
    children: [
      { title: "Solicitud de Viáticos", href: "/dashboard/admin/viaticos" },
      { title: "Solicitud de Paquetería", href: "/dashboard/admin/paqueteria" },
      { title: "Asigna Número Fiscal", href: "/dashboard/admin/num-fiscal" },
      { title: "Mis Solicitudes", href: "/dashboard/admin/mis-solicitudes" },
      { title: "Contratos Repse", href: "/dashboard/admin/contratos-repse" },
      { title: "Facturas", href: "/dashboard/admin/facturas" },
      { title: "Solicitud de Crédito", href: "/dashboard/admin/solicitud-credito" },
      { title: "Morosos", href: "/dashboard/admin/morosos" },
      { title: "Gráficos Administrativos", href: "/dashboard/admin/graficos" },
    ],
  },
  {
    title: "Almacén",
    icon: Archive,
    children: [
      { title: "Stock Actual", href: "/dashboard/almacen/stock" },
      { title: "Entradas de Stock", href: "/dashboard/almacen/entradas" },
      { title: "Salidas de Stock", href: "/dashboard/almacen/salidas" },
      { title: "Préstamo de Equipo", href: "/dashboard/almacen/prestamos" },
      { title: "Reporte de Inventario", href: "/dashboard/almacen/reporte" },
    ],
  },
  {
    title: "Calidad",
    icon: CheckSquare,
    children: [
      { title: "Gestión de Procedimientos", href: "/dashboard/calidad/procedimientos" },
      { title: "Enlaces QuestionPro", href: "/dashboard/calidad/questionpro" },
      { title: "Seguimiento de Incidencias", href: "/dashboard/calidad/incidencias" },
    ],
  },
  {
    title: "Clientes",
    icon: UsersRound,
    children: [
      { title: "Mis Clientes", href: "/dashboard/clientes" },
      { title: "Alta Completa", href: "/dashboard/clientes/alta" },
      { title: "Alta Rápida", href: "/dashboard/clientes/alta-rapida" },
      { title: "Historial", href: "/dashboard/clientes/historial" },
      { title: "Categorización", href: "/dashboard/clientes/categorizacion" },
      { title: "Documentos", href: "/dashboard/clientes/documentos" },
      { title: "Exportar", href: "/dashboard/clientes/exportar" },
      { title: "KPIs", href: "/dashboard/clientes/kpis" },
    ],
  },
  {
    title: "Informes",
    icon: FileText,
    children: [
      { title: "Asignar Folio", href: "/dashboard/informes/asignar-folio" },
      { title: "Status de Informes", href: "/dashboard/informes/status" },
      { title: "Historial de Folios", href: "/dashboard/informes/historial-folios" },
      { title: "Estadísticos de Informes", href: "/dashboard/informes/estadisticos" },
    ],
  },
  {
    title: "Logística",
    icon: Truck,
    children: [
      { title: "Calendario de Programación", href: "/dashboard/logistica/calendario" },
      { title: "Proyectos (Logística)", href: "/dashboard/logistica/proyectos" },
      { title: "Historial de Ingresos", href: "/dashboard/logistica/ingresos" },
      { title: "Estadísticos de Logística", href: "/dashboard/logistica/estadisticos" },
    ],
  },
  {
    title: "Metrología",
    icon: FlaskConical,
    children: [
      { title: "Entregas de Informes", href: "/dashboard/metrologia/entregas" },
      { title: "Asignación de Tareas", href: "/dashboard/metrologia/tareas" },
      { title: "Calendario de Capacitaciones", href: "/dashboard/metrologia/calendario-capacitaciones" },
      { title: "Dashboard de Rendimiento", href: "/dashboard/metrologia/rendimiento" },
      { title: "Patrones", href: "/dashboard/metrologia/patrones" },
    ],
  },
  {
    title: "Recursos Humanos",
    icon: UserCog,
    children: [
      { title: "Capacitación RFP", href: "/dashboard/rh/capacitacion-rfp" },
      { title: "Agenda de Capacitación", href: "/dashboard/rh/agenda-capacitacion" },
      { title: "Seguimiento Personal (RSP)", href: "/dashboard/rh/rsp" },
      { title: "Expediente Contractual", href: "/dashboard/rh/exp-contractual" },
      { title: "Expediente Laboral", href: "/dashboard/rh/exp-laboral" },
    ],
  },
  {
    title: "Reportes",
    icon: FileSearch,
    children: [
      { title: "Reporte General Ventas", href: "/dashboard/reports/ventas-global" },
      { title: "Reporte General Almacén", href: "/dashboard/reports/almacen-global" },
    ],
  },
  {
    title: "Servicio",
    icon: ConciergeBell,
    children: [
      { title: "Agenda de Servicios", href: "/dashboard/servicio/agenda" },
      { title: "Solicitud de Viáticos (Servicio)", href: "/dashboard/servicio/viaticos" },
      { title: "Solicitud de Tiempo (Servicio)", href: "/dashboard/servicio/tiempo" },
    ],
  },
  {
    title: "Sistemas",
    icon: DatabaseZap,
    children: [
      { title: "Gestión de Usuarios", href: "/dashboard/sistemas/usuarios" },
      { title: "Roles y Permisos", href: "/dashboard/sistemas/roles" },
      { title: "Auditoría del Sistema", href: "/dashboard/sistemas/auditoria" },
      { title: "Backup y Restauración", href: "/dashboard/sistemas/backup" },
    ],
  },
  {
    title: "Ventas",
    icon: ShoppingCart,
    children: [
      { title: "Agregar Cliente (Ventas)", href: "/dashboard/ventas/agregar-cliente" },
      { title: "Mis Clientes (Ventas)", href: "/dashboard/ventas/mis-clientes" },
      { title: "Borrador de Cotización", href: "/dashboard/ventas/borrador-cotizacion" },
      { title: "Existencias de Productos", href: "/dashboard/ventas/existencias" },
      { title: "Asignación de Clientes", href: "/dashboard/ventas/asignacion-clientes" },
      { title: "Fichas Técnicas", href: "/dashboard/ventas/fichas-tecnicas" },
      { title: "Cotizar", href: "/dashboard/ventas/cotizar" },
      { title: "Proceso de Proyecto (CRM)", href: "/dashboard/ventas/crm-proyectos" },
      { title: "Estadísticos de Ventas", href: "/dashboard/ventas/estadisticos" },
    ],
  },
]

interface SidebarProps {
  darkMode?: boolean
  className?: string
  isMobileOpen?: boolean
  onMobileToggle?: () => void
}

export function DashboardSidebar({ darkMode = false, className, isMobileOpen = false, onMobileToggle }: SidebarProps) {
  const pathname = usePathname()
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const toggleExpanded = (title: string) => {
    setExpandedItems((prev) => (prev.includes(title) ? prev.filter((item) => item !== title) : [...prev, title]))
  }

  return (
    <>
      <style jsx global>{`
        .sidebar-scrollbar-hide {
          -ms-overflow-style: none !important;
          scrollbar-width: none !important;
          overflow: -moz-scrollbars-none !important;
        }
        .sidebar-scrollbar-hide::-webkit-scrollbar {
          display: none !important;
          width: 0 !important;
          height: 0 !important;
          background: transparent !important;
        }
        .sidebar-scrollbar-hide::-webkit-scrollbar-track {
          display: none !important;
        }
        .sidebar-scrollbar-hide::-webkit-scrollbar-thumb {
          display: none !important;
        }
        .sidebar-scrollbar-hide::-webkit-scrollbar-corner {
          display: none !important;
        }
      `}</style>
      {/* Mobile Overlay */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={onMobileToggle}
        />
      )}

      <aside
      className={cn(
        "fixed left-0 top-16 h-[calc(100vh-4rem)] w-52 flex flex-col backdrop-blur-xl border-r",
        "overflow-y-auto sidebar-scrollbar-hide transition-transform duration-300 ease-in-out",
        // Mobile: slide from left, hidden by default
        "md:translate-x-0 z-50 md:z-30",
        isMobileOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0",
        darkMode
          ? "bg-gradient-to-b from-slate-900/95 via-slate-800/95 to-slate-900/95 border-slate-700/30 text-gray-100"
          : "bg-gradient-to-b from-moka-peach/80 via-moka-lion/70 to-moka-brown/60 border-moka-brown/50 text-black",
        className,
      )}
    >
      <nav className="p-3 space-y-1">
        {menuItems.map((item) => (
          <div key={item.title}>
            {item.children ? (
              <div>
                <button
                  onClick={() => toggleExpanded(item.title)}
                  className={cn(
                    "w-full flex items-center justify-between px-3 py-2.5 text-sm font-medium rounded-lg text-left",
                    "transition-all duration-300 ease-out hover:translate-x-1 hover:scale-[1.02]",
                    expandedItems.includes(item.title) && "bg-white/10",
                    darkMode
                      ? "text-gray-300 hover:bg-slate-800/60 hover:text-gray-100 hover:shadow-lg"
                      : "text-black hover:bg-white/20 hover:text-black hover:shadow-lg",
                  )}
                >
                  <div className="flex items-center">
                    <item.icon className="mr-3 h-4 w-4 flex-shrink-0" />
                    <span className="truncate">{item.title}</span>
                  </div>
                  <ChevronRight
                    className={cn(
                      "h-4 w-4 flex-shrink-0 transition-all duration-300 ease-out",
                      expandedItems.includes(item.title) && "rotate-90 text-moka-falu dark:text-blue-400",
                    )}
                  />
                </button>
                <div
                  className={cn(
                    "overflow-hidden transition-all duration-300 ease-out",
                    expandedItems.includes(item.title)
                      ? "max-h-96 opacity-100 mt-1"
                      : "max-h-0 opacity-0 mt-0"
                  )}
                >
                  <div className="ml-7 space-y-1 pb-1">
                    {item.children.map((child, index) => (
                      <Link
                        key={child.href}
                        href={child.href}
                        className={cn(
                          "block px-3 py-2 text-sm rounded-lg",
                          "transition-all duration-300 ease-out hover:translate-x-1 hover:scale-105",
                          pathname === child.href
                            ? darkMode
                              ? "bg-blue-600/70 text-blue-100 font-medium shadow-lg"
                              : "bg-moka-falu/20 text-moka-bistre font-medium shadow-lg"
                            : darkMode
                              ? "text-gray-400 hover:bg-slate-800/50 hover:text-gray-200 hover:shadow-md"
                              : "text-black/80 hover:bg-white/20 hover:text-black hover:shadow-md",
                        )}
                        style={{
                          transitionDelay: expandedItems.includes(item.title) ? `${index * 50}ms` : '0ms'
                        }}
                      >
                        <span className="truncate">{child.title}</span>
                      </Link>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <Link
                href={item.href!}
                className={cn(
                  "flex items-center px-3 py-2.5 text-sm font-medium rounded-lg",
                  "transition-all duration-300 ease-out hover:translate-x-1 hover:scale-105",
                  pathname === item.href
                    ? darkMode
                      ? "bg-blue-600/70 text-blue-100 shadow-lg"
                      : "bg-moka-falu/20 text-moka-bistre shadow-lg"
                    : darkMode
                      ? "text-gray-300 hover:bg-slate-800/60 hover:text-gray-100 hover:shadow-md"
                      : "text-black hover:bg-white/20 hover:text-black hover:shadow-md",
                )}
              >
                <item.icon className="mr-3 h-4 w-4 flex-shrink-0 transition-all duration-300" />
                <span className="truncate">{item.title}</span>
              </Link>
            )}
          </div>
        ))}
      </nav>
    </aside>
    </>
  )
}
