"use client";

import { useState, useEffect } from "react";
import { useTheme } from "next-themes";
import { DashboardHeader } from "@/components/layout/header";
import { DashboardSidebar } from "@/components/layout/sidebar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useAuth } from "@/components/providers/auth-provider";
import { useNotifications } from "@/components/ui/notification-provider";

// Tipo mínimo de notificación (ajusta si tienes uno global)
type Notification = {
  id: number;
  title: string;
  message: string;
  type: "info" | "warning" | "success" | "error";
  read: boolean;
  time: string;
};

export function DashboardShell({ children }: { children: React.ReactNode }) {
  const { resolvedTheme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const isMobile = useIsMobile();

  // Auth y notificaciones
  const { logout, user } = useAuth();
  const { notifyLogout } = useNotifications();

  // Evitar hidration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Cerrar menú móvil cuando se cambia a desktop
  useEffect(() => {
    if (!isMobile && isMobileMenuOpen) {
      setIsMobileMenuOpen(false);
    }
  }, [isMobile, isMobileMenuOpen]);

  const darkMode = mounted ? resolvedTheme === "dark" : false;

  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: 1,
      title: "Bienvenido",
      message: "Has iniciado sesión correctamente.",
      type: "success",
      read: false,
      time: "Hace 1 min",
    },
  ]);

  function handleToggleDarkMode(checked: boolean) {
    setTheme(checked ? "dark" : "light");
  }

  function handleMarkAsRead(id: number) {
    setNotifications((prev) =>
      prev.map((n) => (n.id === id ? { ...n, read: true } : n))
    );
  }

  function handleDeleteNotification(id: number) {
    setNotifications((prev) => prev.filter((n) => n.id !== id));
  }

  // Función de logout con notificación
  const handleLogout = async () => {
    try {
      // Mostrar notificación de despedida elegante
      notifyLogout(user?.name);

      // Pequeño delay para que se vea la notificación antes del redirect
      setTimeout(async () => {
        await logout();
      }, 1500);
    } catch (error) {
      console.error('Error during logout:', error);
      // Forzar logout aunque haya error
      await logout();
    }
  };

  // Mostrar un loading state hasta que el tema esté resuelto
  if (!mounted) {
    return (
      <div className="flex min-h-screen bg-gray-100">
        <div className="flex-1 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="flex min-h-screen transition-colors duration-500 overflow-x-hidden bg-gradient-to-br from-moka-peach/80 via-moka-lion/70 to-moka-brown/60 dark:bg-gradient-to-br dark:from-slate-900 dark:via-blue-950 dark:to-red-950"
    >
      <DashboardSidebar
        darkMode={darkMode}
        isMobileOpen={isMobileMenuOpen}
        onMobileToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
      />
      <main className="md:ml-52 min-h-screen flex-1 flex flex-col overflow-x-hidden relative z-10">
        <DashboardHeader
          darkMode={darkMode}
          onToggleDarkMode={handleToggleDarkMode}
          notifications={notifications}
          onMarkAsRead={handleMarkAsRead}
          onDeleteNotification={handleDeleteNotification}
          onLogout={handleLogout}
          user={user ? { name: user.name, email: user.email } : undefined}
          isMobileMenuOpen={isMobileMenuOpen}
          onMobileMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        />
        <div className="flex-1 p-0 w-full overflow-x-hidden">
          {children}
        </div>
      </main>
    </div>
  );
}