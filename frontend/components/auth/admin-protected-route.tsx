"use client"

import { useAuth } from '@/components/providers/auth-provider'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Shield, Home } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface AdminProtectedRouteProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  redirectTo?: string
  showAlert?: boolean
}

export function AdminProtectedRoute({ 
  children, 
  fallback, 
  redirectTo = '/dashboard',
  showAlert = true 
}: AdminProtectedRouteProps) {
  const { user, isAuthenticated, isLoading, hasRole } = useAuth()
  const router = useRouter()
  const [showDenied, setShowDenied] = useState(false)

  useEffect(() => {
    if (!isLoading && isAuthenticated && !(hasRole('ROLE_ADMIN') || hasRole('ROLE_SISTEMAS'))) {
      setShowDenied(true)
      const timer = setTimeout(() => {
        router.push(redirectTo)
      }, 3000)
      return () => clearTimeout(timer)
    }
  }, [isAuthenticated, isLoading, hasRole, router, redirectTo])

  // Mostrar loading mientras se verifica la autenticación
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  // Si no está autenticado, el ProtectedRoute padre se encargará
  if (!isAuthenticated) {
    return null
  }

  // Si no es admin, mostrar mensaje de no autorizado durante 3 segundos
  if (showDenied) {
    return (
      <div className="flex flex-col items-center justify-center h-96">
        <h2 className="text-2xl font-bold mb-4 text-red-600">Acceso no autorizado</h2>
        <p className="mb-4">No tienes permisos para acceder a esta sección.</p>
        <Button onClick={() => router.push(redirectTo)}>Volver al Dashboard</Button>
      </div>
    )
  }

  if (!isAuthenticated || !(hasRole('ROLE_ADMIN') || hasRole('ROLE_SISTEMAS'))) {
    return fallback || null
  }

  return <>{children}</>
}
