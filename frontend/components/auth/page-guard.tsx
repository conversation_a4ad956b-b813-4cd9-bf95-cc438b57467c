"use client"

import type React from "react"
import { usePagePermissions } from "@/hooks/usePagePermissions"
import { UnauthorizedPage } from "./unauthorized-page"

interface PageGuardProps {
  children: React.ReactNode
  requiredPermissions?: string[]
  requiredRoles?: string[]
  unauthorizedTitle?: string
  unauthorizedMessage?: string
}

export function PageGuard({
  children,
  requiredPermissions = [],
  requiredRoles = [],
  unauthorizedTitle,
  unauthorizedMessage
}: PageGuardProps) {
  const { hasAccess } = usePagePermissions({ 
    requiredPermissions, 
    requiredRoles 
  })

  if (!hasAccess) {
    return (
      <UnauthorizedPage 
        title={unauthorizedTitle}
        message={unauthorizedMessage}
      />
    )
  }

  return <>{children}</>
} 