"use client"

import type React from "react"
import { useAuth } from "@/components/providers/auth-provider"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Shield } from "lucide-react"

interface PermissionGuardProps {
  children: React.ReactNode
  permission?: string  // Un solo permiso requerido
  roles?: string[]     // Lista de roles permitidos
  fallback?: React.ReactNode
  showAlert?: boolean
}

export function PermissionGuard({
  children,
  permission,
  roles = [],
  fallback,
  showAlert = true,
}: PermissionGuardProps) {
  const { hasPermission, hasRole, user } = useAuth()

  // Función de validación de acceso
  const hasAccess = () => {
    // Si no hay usuario, no hay acceso
    if (!user) return false;

    // Si es ROLE_ADMIN, tiene acceso total
    if (hasRole("ROLE_ADMIN")) return true;

    // Si se requiere un permiso específico
    if (permission) {
      return hasPermission(permission);
    }

    // Si se requieren roles específicos
    if (roles.length > 0) {
      return roles.some(role => hasRole(role));
    }

    // Si no se especifica ni permiso ni roles, no dar acceso
    return false;
  }

  // Verificar acceso
  if (!hasAccess()) {
    if (fallback) {
      return <>{fallback}</>;
    }

    if (showAlert) {
      return (
        <Alert className="border-orange-200 bg-orange-50">
          <Shield className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800">
            No tienes permisos para ver este contenido.
          </AlertDescription>
        </Alert>
      );
    }

    return null;
  }

  return <>{children}</>;
}
