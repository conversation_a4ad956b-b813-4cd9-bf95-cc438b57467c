import axios from 'axios';
import { authHeader } from '../../lib/services/auth-header';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';

export interface SystemConfig {
  id: number;
  key: string;
  value: string;
  description: string;
  type: 'string' | 'boolean' | 'number' | 'json';
  editable: boolean;
  createdAt: string;
  updatedAt: string;
}

export class SystemConfigService {
  getAllConfig() {
    return axios.get<SystemConfig[]>(`${API_URL}/systems/config`, { headers: authHeader() });
  }

  getConfigByKey(key: string) {
    return axios.get<SystemConfig>(`${API_URL}/systems/config/${key}`, { headers: authHeader() });
  }

  updateConfig(key: string, value: string) {
    return axios.put<SystemConfig>(`${API_URL}/systems/config/${key}`, { value }, { headers: authHeader() });
  }

  createConfig(configData: Partial<SystemConfig>) {
    return axios.post<SystemConfig>(`${API_URL}/systems/config`, configData, { headers: authHeader() });
  }

  deleteConfig(key: string) {
    return axios.delete(`${API_URL}/systems/config/${key}`, { headers: authHeader() });
  }

  initializeConfig() {
    return axios.post(`${API_URL}/systems/config/initialize`, {}, { headers: authHeader() });
  }
}

export const systemConfigService = new SystemConfigService(); 