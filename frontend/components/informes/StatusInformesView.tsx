"use client";

import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Edit3Icon, EyeIcon, SearchIcon } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { format, differenceInBusinessDays, addBusinessDays, isValid } from "date-fns";
import { es } from "date-fns/locale"; // Para formato en español si es necesario
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";

// Definición del tipo para los datos de estado de un informe
interface InformeStatusData {
  id: string; // ID del folio/informe
  folio: string;
  cliente: string;
  servicioRealizadoPor: string;
  fechaServicio: Date; // Fecha en que se realizó el servicio
  fechaLimiteEntrega?: Date; // Calculada: fechaServicio + 10 días hábiles
  tiempoRestante?: number; // En días
  estadoActual: "Informe enviado" | "En proceso de emisión" | "Template no recibido" | "Informe moroso" | "Template listo pero equipo no entregado" | "Pendiente de asignación";
  // Otros campos relevantes
  ordenCompra?: string;
  cotizacion?: string;
}

// Datos de ejemplo (simulando lo que vendría del backend)
const sampleInformesStatus: InformeStatusData[] = [
  { id: "folio-001", folio: "INF-2024-001", cliente: "Empresa Alpha", servicioRealizadoPor: "Juan Pérez", fechaServicio: new Date("2024-07-10"), estadoActual: "En proceso de emisión", ordenCompra: "OC-1", cotizacion: "COT-1" },
  { id: "folio-002", folio: "INF-2024-002", cliente: "Corporativo Beta", servicioRealizadoPor: "Ana Gómez", fechaServicio: new Date("2024-07-15"), estadoActual: "Template no recibido", ordenCompra: "OC-2", cotizacion: "COT-2" },
  { id: "folio-003", folio: "INF-2024-003", cliente: "Industrias Gamma", servicioRealizadoPor: "Luis Castro", fechaServicio: new Date("2024-06-20"), estadoActual: "Informe enviado", ordenCompra: "OC-3", cotizacion: "COT-3" },
  { id: "folio-004", folio: "INF-2024-004", cliente: "Comercial Delta", servicioRealizadoPor: "Juan Pérez", fechaServicio: new Date("2024-07-01"), estadoActual: "Informe moroso", ordenCompra: "OC-4", cotizacion: "COT-4" },
  { id: "folio-005", folio: "INF-2024-005", cliente: "Servicios Epsilon", servicioRealizadoPor: "Pedro Martin", fechaServicio: new Date("2024-07-18"), estadoActual: "Template listo pero equipo no entregado", ordenCompra: "OC-5", cotizacion: "COT-5" },
];

const ESTADOS_INFORME: InformeStatusData["estadoActual"][] = [
  "Pendiente de asignación",
  "En proceso de emisión",
  "Template no recibido",
  "Template listo pero equipo no entregado",
  "Informe moroso",
  "Informe enviado",
];

// Función para calcular fecha límite y tiempo restante
const calcularDatosEntrega = (fechaServicio: Date): Pick<InformeStatusData, "fechaLimiteEntrega" | "tiempoRestante"> => {
  if (!isValid(fechaServicio)) return { fechaLimiteEntrega: undefined, tiempoRestante: undefined };
  const fechaLimite = addBusinessDays(fechaServicio, 10);
  const hoy = new Date();
  // Solo calcular días restantes si la fecha límite no ha pasado
  const tiempoRestante = fechaLimite > hoy ? differenceInBusinessDays(fechaLimite, hoy) : 0;
  return { fechaLimiteEntrega: fechaLimite, tiempoRestante };
};


export function StatusInformesView() {
  const [informes, setInformes] = useState<InformeStatusData[]>([]);
  const [filtroEstado, setFiltroEstado] = useState<string>("todos");
  const [filtroBusqueda, setFiltroBusqueda] = useState<string>("");
  const [filtroFechaDesde, setFiltroFechaDesde] = useState<Date | undefined>();
  const [filtroFechaHasta, setFiltroFechaHasta] = useState<Date | undefined>();

  // Simulación de carga de datos y cálculo de fechas
  useEffect(() => {
    const datosProcesados = sampleInformesStatus.map(inf => ({
      ...inf,
      ...calcularDatosEntrega(inf.fechaServicio)
    }));
    setInformes(datosProcesados);
    // En una implementación real:
    // fetch('/api/informes/status')
    //   .then(res => res.json())
    //   .then(data => {
    //     const procesados = data.map(inf => ({...inf, ...calcularDatosEntrega(new Date(inf.fechaServicio))}));
    //     setInformes(procesados);
    //   });
  }, []);

  const handleUpdateStatus = (informeId: string, nuevoEstado: InformeStatusData["estadoActual"]) => {
    console.log(`Actualizar estado del informe ${informeId} a ${nuevoEstado}`);
    // TODO: Lógica para llamar al backend y actualizar el estado
    // Luego, actualizar el estado local 'informes'
    setInformes(prevInformes =>
      prevInformes.map(inf =>
        inf.id === informeId ? { ...inf, estadoActual: nuevoEstado } : inf
      )
    );
  };

  const getBadgeClass = (estado: InformeStatusData["estadoActual"]) => {
    switch (estado) {
      case "Informe enviado": return "bg-green-100 text-green-700 hover:bg-green-200";
      case "En proceso de emisión": return "bg-blue-100 text-blue-700 hover:bg-blue-200";
      case "Template no recibido": return "bg-yellow-100 text-yellow-700 hover:bg-yellow-200";
      case "Informe moroso": return "bg-red-100 text-red-700 hover:bg-red-200";
      case "Template listo pero equipo no entregado": return "bg-purple-100 text-purple-700 hover:bg-purple-200";
      case "Pendiente de asignación": return "bg-gray-100 text-gray-700 hover:bg-gray-200";
      default: return "bg-gray-200 text-gray-800";
    }
  };

  const filteredInformes = useMemo(() => {
    return informes.filter(informe => {
      const busquedaLower = filtroBusqueda.toLowerCase();
      const matchBusqueda = filtroBusqueda === "" ||
                            informe.folio.toLowerCase().includes(busquedaLower) ||
                            informe.cliente.toLowerCase().includes(busquedaLower) ||
                            informe.servicioRealizadoPor.toLowerCase().includes(busquedaLower);

      const matchEstado = filtroEstado === "todos" || informe.estadoActual === filtroEstado;

      const matchFechaDesde = !filtroFechaDesde || new Date(informe.fechaServicio) >= filtroFechaDesde;
      const matchFechaHasta = !filtroFechaHasta || new Date(informe.fechaServicio) <= filtroFechaHasta;

      return matchBusqueda && matchEstado && matchFechaDesde && matchFechaHasta;
    });
  }, [informes, filtroEstado, filtroBusqueda, filtroFechaDesde, filtroFechaHasta]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Status de Informes</CardTitle>
        <CardDescription>
          Visualiza y gestiona el estado de los informes de servicio. El tiempo restante se calcula en días hábiles.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-4 border rounded-lg">
          <div>
            <Label htmlFor="filtroBusqueda">Buscar</Label>
            <div className="relative">
              <SearchIcon className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
              <Input
                id="filtroBusqueda"
                placeholder="Folio, cliente, técnico..."
                value={filtroBusqueda}
                onChange={(e) => setFiltroBusqueda(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
          <div>
            <Label htmlFor="filtroEstado">Estado del Informe</Label>
            <Select value={filtroEstado} onValueChange={setFiltroEstado}>
              <SelectTrigger id="filtroEstado">
                <SelectValue placeholder="Todos los estados" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todos">Todos los estados</SelectItem>
                {ESTADOS_INFORME.map(estado => (
                  <SelectItem key={estado} value={estado}>{estado}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="filtroFechaDesde">Fecha Servicio Desde</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start text-left font-normal">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {filtroFechaDesde ? format(filtroFechaDesde, "dd/MM/yyyy") : "Seleccionar"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar mode="single" selected={filtroFechaDesde} onSelect={setFiltroFechaDesde} />
              </PopoverContent>
            </Popover>
          </div>
          <div>
            <Label htmlFor="filtroFechaHasta">Fecha Servicio Hasta</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start text-left font-normal">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {filtroFechaHasta ? format(filtroFechaHasta, "dd/MM/yyyy") : "Seleccionar"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar mode="single" selected={filtroFechaHasta} onSelect={setFiltroFechaHasta} />
              </PopoverContent>
            </Popover>
          </div>
        </div>

        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Folio</TableHead>
                <TableHead>Cliente</TableHead>
                <TableHead>Técnico</TableHead>
                <TableHead>Fecha Servicio</TableHead>
                <TableHead>Fecha Límite</TableHead>
                <TableHead className="text-center">Tiempo Restante</TableHead>
                <TableHead>Estado Actual</TableHead>
                <TableHead className="text-center">Acciones</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredInformes.length > 0 ? (
                filteredInformes.map((informe) => (
                  <TableRow key={informe.id}>
                    <TableCell className="font-medium">{informe.folio}</TableCell>
                    <TableCell>{informe.cliente}</TableCell>
                    <TableCell>{informe.servicioRealizadoPor}</TableCell>
                    <TableCell>{isValid(informe.fechaServicio) ? format(informe.fechaServicio, "dd/MM/yyyy") : "N/A"}</TableCell>
                    <TableCell>{informe.fechaLimiteEntrega && isValid(informe.fechaLimiteEntrega) ? format(informe.fechaLimiteEntrega, "dd/MM/yyyy") : "N/A"}</TableCell>
                    <TableCell className="text-center">
                      {informe.tiempoRestante !== undefined ? (
                        <Badge variant={informe.tiempoRestante <= 2 ? "destructive" : informe.tiempoRestante <=5 ? "secondary" : "default"}>
                          {informe.tiempoRestante} días
                        </Badge>
                      ) : "N/A"}
                    </TableCell>
                    <TableCell>
                      <Badge className={cn("cursor-pointer", getBadgeClass(informe.estadoActual))} variant="outline">
                        {/* Aquí podríamos poner un Popover o Dropdown para cambiar el estado */}
                        {informe.estadoActual}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-center">
                      {/* <PermissionGuard permissions={["INFORMES_STATUS_EDIT"]}> */}
                      <Popover>
                          <PopoverTrigger asChild>
                              <Button variant="ghost" size="icon" title="Cambiar Estado">
                                  <Edit3Icon className="h-4 w-4" />
                              </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-56 p-2">
                              <p className="text-xs font-medium p-2">Cambiar estado para {informe.folio}:</p>
                              {ESTADOS_INFORME.map(estado => (
                                  <Button
                                      key={estado}
                                      variant="ghost"
                                      className="w-full justify-start text-sm"
                                      onClick={() => handleUpdateStatus(informe.id, estado)}
                                      disabled={informe.estadoActual === estado}
                                  >
                                      {estado}
                                  </Button>
                              ))}
                          </PopoverContent>
                      </Popover>
                      {/* </PermissionGuard> */}
                       <Button variant="ghost" size="icon" title="Ver Detalles (pendiente)">
                          <EyeIcon className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={8} className="text-center h-24">
                    No hay informes que coincidan con los filtros.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
