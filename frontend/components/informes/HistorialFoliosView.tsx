"use client";

import { useState, useEffect, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { SearchIcon, EyeIcon, EditIcon, DownloadIcon } from "lucide-react"; // EditIcon para posible edición
import { format } from "date-fns";
import { es } from "date-fns/locale"; // Para formato en español si es necesario

// Reutilizamos o adaptamos la interfaz FolioData de AsignarFolioView
// Es posible que necesitemos campos adicionales o variaciones para el historial
interface FolioHistorialData {
  id: string;
  folioNumero: string; // Asumiendo que el backend genera este número
  tipoServicio: "Laboratorio" | "Planta";
  cotizacion: string;
  ordenCompra: string;
  tecnicoResponsable: string;
  // Datos del cliente (podrían venir de una relación en el backend)
  clienteNombre?: string;
  // Datos del equipo
  descripcionEquipo: string;
  marcaEquipo: string;
  modeloEquipo: string;
  serieEquipo: string;
  idInternoEquipo: string;
  // Fechas
  fechaIngreso?: Date;
  fechaCalibracion?: Date;
  // Sede (importante para el filtro)
  sede: "GDL" | "CDMX" | "MTY" | string; // String para otras posibles sedes
  // Otros campos que puedan ser útiles para el historial
  estadoInforme?: string; // Podría ser útil mostrar el último estado conocido
}

// Datos de ejemplo para el historial
const sampleFoliosHistorial: FolioHistorialData[] = [
  { id: "hfolio-001", folioNumero: "LAB-2024-001", tipoServicio: "Laboratorio", cotizacion: "COT-2024-001", ordenCompra: "OC-CLIENTEA-001", tecnicoResponsable: "Laura Valle", clienteNombre: "Cliente Alpha", descripcionEquipo: "Manómetro Digital", marcaEquipo: "Omega", modeloEquipo: "PGM-01", serieEquipo: "SN111222", idInternoEquipo: "EQ-001", fechaIngreso: new Date("2024-01-10"), fechaCalibracion: new Date("2024-01-12"), sede: "GDL", estadoInforme: "Informe enviado" },
  { id: "hfolio-002", folioNumero: "PLA-2024-001", tipoServicio: "Planta", cotizacion: "COT-2024-002", ordenCompra: "OC-CLIENTEB-002", tecnicoResponsable: "Carlos Solis", clienteNombre: "Cliente Beta", descripcionEquipo: "Termómetro Infrarrojo", marcaEquipo: "Fluke", modeloEquipo: "568", serieEquipo: "SN333444", idInternoEquipo: "EQ-002", fechaIngreso: new Date("2024-02-05"), fechaCalibracion: new Date("2024-02-06"), sede: "CDMX", estadoInforme: "En proceso de emisión" },
  { id: "hfolio-003", folioNumero: "LAB-2024-002", tipoServicio: "Laboratorio", cotizacion: "COT-2024-003", ordenCompra: "OC-CLIENTEC-003", tecnicoResponsable: "Laura Valle", clienteNombre: "Cliente Gamma", descripcionEquipo: "Balanza Analítica", marcaEquipo: "Sartorius", modeloEquipo: "BSA224S", serieEquipo: "SN555666", idInternoEquipo: "EQ-003", fechaIngreso: new Date("2024-03-15"), fechaCalibracion: new Date("2024-03-18"), sede: "GDL", estadoInforme: "Informe moroso" },
  { id: "hfolio-004", folioNumero: "LAB-2024-003", tipoServicio: "Laboratorio", cotizacion: "COT-2024-004", ordenCompra: "OC-CLIENTEA-004", tecnicoResponsable: "Ana Torres", clienteNombre: "Cliente Alpha", descripcionEquipo: "Micrómetro", marcaEquipo: "Mitutoyo", modeloEquipo: "293-340-30", serieEquipo: "SN777888", idInternoEquipo: "EQ-004", fechaIngreso: new Date("2024-04-10"), fechaCalibracion: new Date("2024-04-12"), sede: "MTY", estadoInforme: "Informe enviado" },
];

export function HistorialFoliosView() {
  const [foliosHistorial, setFoliosHistorial] = useState<FolioHistorialData[]>([]);
  const [filtroBusqueda, setFiltroBusqueda] = useState<string>("");
  const [filtroSede, setFiltroSede] = useState<string>("GDL"); // Por defecto GDL según requerimiento

  // Simulación de carga de datos
  useEffect(() => {
    // En una implementación real, aquí se haría fetch al backend:
    // fetch(`/api/folios/historial?sede=${filtroSede}&q=${filtroBusqueda}`)
    //   .then(res => res.json())
    //   .then(data => setFoliosHistorial(data.map(f => ({...f, fechaIngreso: new Date(f.fechaIngreso), fechaCalibracion: new Date(f.fechaCalibracion)}))));
    setFoliosHistorial(sampleFoliosHistorial.map(f => ({
        ...f,
        fechaIngreso: f.fechaIngreso ? new Date(f.fechaIngreso) : undefined,
        fechaCalibracion: f.fechaCalibracion ? new Date(f.fechaCalibracion) : undefined,
    })));
  }, []); // Podría recargar con cambios en filtros si el filtrado es full backend

  const handleVerDetalles = (folioId: string) => {
    console.log("Ver detalles del folio:", folioId);
    // TODO: Implementar navegación o modal para ver detalles
    // Podría redirigir a AsignarFolioView con el ID para edición o vista detallada
  };

  const handleExportar = () => {
    console.log("Exportando historial de folios...");
    // TODO: Implementar lógica de exportación (CSV, Excel)
  };

  const filteredFolios = useMemo(() => {
    return foliosHistorial.filter(folio => {
      const busquedaLower = filtroBusqueda.toLowerCase();
      const matchBusqueda = filtroBusqueda === "" ||
                            folio.folioNumero.toLowerCase().includes(busquedaLower) ||
                            folio.cotizacion.toLowerCase().includes(busquedaLower) ||
                            folio.ordenCompra.toLowerCase().includes(busquedaLower) ||
                            (folio.clienteNombre && folio.clienteNombre.toLowerCase().includes(busquedaLower)) ||
                            folio.descripcionEquipo.toLowerCase().includes(busquedaLower) ||
                            folio.marcaEquipo.toLowerCase().includes(busquedaLower) ||
                            folio.modeloEquipo.toLowerCase().includes(busquedaLower) ||
                            folio.serieEquipo.toLowerCase().includes(busquedaLower) ||
                            folio.idInternoEquipo.toLowerCase().includes(busquedaLower);

      const matchSede = filtroSede === "TODAS" || folio.sede === filtroSede;

      return matchBusqueda && matchSede;
    });
  }, [foliosHistorial, filtroBusqueda, filtroSede]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Historial de Folios</CardTitle>
        <CardDescription>
          Consulta el historial de todos los folios de servicio registrados.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col md:flex-row gap-4 p-4 border rounded-lg">
          <div className="flex-grow">
            <Label htmlFor="filtroBusquedaHistorial">Buscar Folio</Label>
            <div className="relative">
              <SearchIcon className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
              <Input
                id="filtroBusquedaHistorial"
                placeholder="No. Folio, Cotización, OC, Cliente, Equipo (Serie, ID, Desc)..."
                value={filtroBusqueda}
                onChange={(e) => setFiltroBusqueda(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
          <div className="min-w-[180px]">
            <Label htmlFor="filtroSede">Sede</Label>
            <Select value={filtroSede} onValueChange={setFiltroSede}>
              <SelectTrigger id="filtroSede">
                <SelectValue placeholder="Seleccionar Sede" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="GDL">Guadalajara (GDL)</SelectItem>
                <SelectItem value="CDMX">Ciudad de México (CDMX)</SelectItem>
                <SelectItem value="MTY">Monterrey (MTY)</SelectItem>
                <SelectItem value="TODAS">Todas las Sedes</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="self-end">
             <Button onClick={handleExportar} variant="outline">
                <DownloadIcon className="mr-2 h-4 w-4" />
                Exportar
            </Button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>No. Folio</TableHead>
                <TableHead>Cliente</TableHead>
                <TableHead>Tipo Servicio</TableHead>
                <TableHead>Equipo (Descripción)</TableHead>
                <TableHead>Marca / Modelo</TableHead>
                <TableHead>Serie / ID Interno</TableHead>
                <TableHead>Fecha Ingreso</TableHead>
                <TableHead>Fecha Calibración</TableHead>
                <TableHead>Cotización</TableHead>
                <TableHead>OC</TableHead>
                <TableHead>Sede</TableHead>
                <TableHead className="text-center">Acciones</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredFolios.length > 0 ? (
                filteredFolios.map((folio) => (
                  <TableRow key={folio.id}>
                    <TableCell className="font-medium">{folio.folioNumero}</TableCell>
                    <TableCell>{folio.clienteNombre || "N/A"}</TableCell>
                    <TableCell>{folio.tipoServicio}</TableCell>
                    <TableCell>{folio.descripcionEquipo}</TableCell>
                    <TableCell>{folio.marcaEquipo} / {folio.modeloEquipo}</TableCell>
                    <TableCell>{folio.serieEquipo} / {folio.idInternoEquipo}</TableCell>
                    <TableCell>{folio.fechaIngreso ? format(folio.fechaIngreso, "dd/MM/yyyy") : "N/A"}</TableCell>
                    <TableCell>{folio.fechaCalibracion ? format(folio.fechaCalibracion, "dd/MM/yyyy") : "N/A"}</TableCell>
                    <TableCell>{folio.cotizacion}</TableCell>
                    <TableCell>{folio.ordenCompra}</TableCell>
                    <TableCell>{folio.sede}</TableCell>
                    <TableCell className="text-center">
                      <Button variant="ghost" size="icon" title="Ver Detalles / Editar" onClick={() => handleVerDetalles(folio.id)}>
                        {/* Icono podría ser EyeIcon o EditIcon dependiendo de la acción principal */}
                        <EditIcon className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={12} className="text-center h-24">
                    No hay folios en el historial que coincidan con los filtros.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        {/* TODO: Paginación si es necesario */}
      </CardContent>
    </Card>
  );
}
