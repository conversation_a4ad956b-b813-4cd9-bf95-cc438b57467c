"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Calendar } from "@/components/ui/calendar"; // Asumiendo que Calendar es para seleccionar fechas
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"; // Para el DatePicker
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

// Definición del tipo para los datos del folio
interface FolioData {
  id?: string; // Para edición
  tipoServicio: "Laboratorio" | "Planta";
  cotizacion: string;
  ordenCompra: string;
  tecnicoResponsable: string;
  // Datos del equipo
  descripcionEquipo: string;
  marcaEquipo: string;
  modeloEquipo: string;
  serieEquipo: string;
  idInternoEquipo: string;
  // Fechas
  fechaIngreso?: Date;
  fechaCalibracion?: Date;
  // Otros campos que puedan surgir
  [key: string]: any; // Para flexibilidad
}

const initialFolioData: Omit<FolioData, 'id' | 'tipoServicio'> = {
  cotizacion: "",
  ordenCompra: "",
  tecnicoResponsable: "",
  descripcionEquipo: "",
  marcaEquipo: "",
  modeloEquipo: "",
  serieEquipo: "",
  idInternoEquipo: "",
  fechaIngreso: undefined,
  fechaCalibracion: undefined,
};

export function AsignarFolioView() {
  const [activeTab, setActiveTab] = useState<"Laboratorio" | "Planta">("Laboratorio");
  const [formData, setFormData] = useState<Omit<FolioData, 'id' | 'tipoServicio'>>(initialFolioData);
  // TODO: Estado para lista de folios existentes
  // const [folios, setFolios] = useState<FolioData[]>([]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleDateChange = (name: string, date?: Date) => {
    setFormData((prev) => ({ ...prev, [name]: date }));
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const fullFormData: FolioData = { ...formData, tipoServicio: activeTab };
    console.log("Formulario a enviar:", fullFormData);
    // TODO: Lógica para enviar al backend (crear o actualizar folio)
    // Después de enviar, limpiar formulario y/o actualizar lista de folios
    // setFormData(initialFolioData);
  };

  const FolioFormFields = () => (
    <div className="space-y-4">
      <div className="grid md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor={`cotizacion-${activeTab}`}>Número de Cotización</Label>
          <Input
            id={`cotizacion-${activeTab}`}
            name="cotizacion"
            value={formData.cotizacion}
            onChange={handleInputChange}
            placeholder="Ej: COT-2024-123"
          />
        </div>
        <div>
          <Label htmlFor={`ordenCompra-${activeTab}`}>Número de Orden de Compra</Label>
          <Input
            id={`ordenCompra-${activeTab}`}
            name="ordenCompra"
            value={formData.ordenCompra}
            onChange={handleInputChange}
            placeholder="Ej: OC-2024-456"
          />
        </div>
      </div>

      <div>
        <Label htmlFor={`tecnicoResponsable-${activeTab}`}>Técnico/Responsable</Label>
        <Input // Podría ser un Select si hay una lista fija o búsqueda
          id={`tecnicoResponsable-${activeTab}`}
          name="tecnicoResponsable"
          value={formData.tecnicoResponsable}
          onChange={handleInputChange}
          placeholder="Nombre del técnico o responsable"
        />
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-md">Datos del Equipo</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor={`descripcionEquipo-${activeTab}`}>Descripción</Label>
              <Input
                id={`descripcionEquipo-${activeTab}`}
                name="descripcionEquipo"
                value={formData.descripcionEquipo}
                onChange={handleInputChange}
                placeholder="Ej: Multímetro digital"
              />
            </div>
            <div>
              <Label htmlFor={`marcaEquipo-${activeTab}`}>Marca</Label>
              <Input
                id={`marcaEquipo-${activeTab}`}
                name="marcaEquipo"
                value={formData.marcaEquipo}
                onChange={handleInputChange}
                placeholder="Ej: Fluke"
              />
            </div>
          </div>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor={`modeloEquipo-${activeTab}`}>Modelo</Label>
              <Input
                id={`modeloEquipo-${activeTab}`}
                name="modeloEquipo"
                value={formData.modeloEquipo}
                onChange={handleInputChange}
                placeholder="Ej: 87V"
              />
            </div>
            <div>
              <Label htmlFor={`serieEquipo-${activeTab}`}>Serie</Label>
              <Input
                id={`serieEquipo-${activeTab}`}
                name="serieEquipo"
                value={formData.serieEquipo}
                onChange={handleInputChange}
                placeholder="Número de serie"
              />
            </div>
          </div>
          <div>
            <Label htmlFor={`idInternoEquipo-${activeTab}`}>ID Interno</Label>
            <Input
              id={`idInternoEquipo-${activeTab}`}
              name="idInternoEquipo"
              value={formData.idInternoEquipo}
              onChange={handleInputChange}
              placeholder="Identificador interno del equipo"
            />
          </div>
        </CardContent>
      </Card>

      <div className="grid md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor={`fechaIngreso-${activeTab}`}>Fecha de Ingreso</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={"outline"}
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !formData.fechaIngreso && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formData.fechaIngreso ? format(formData.fechaIngreso, "PPP") : <span>Seleccionar fecha</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={formData.fechaIngreso}
                onSelect={(date) => handleDateChange("fechaIngreso", date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
        <div>
          <Label htmlFor={`fechaCalibracion-${activeTab}`}>Fecha de Calibración</Label>
           <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={"outline"}
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !formData.fechaCalibracion && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formData.fechaCalibracion ? format(formData.fechaCalibracion, "PPP") : <span>Seleccionar fecha</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={formData.fechaCalibracion}
                onSelect={(date) => handleDateChange("fechaCalibracion", date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </div>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Asignar Folio</CardTitle>
        <CardDescription>
          Complete la información para generar un nuevo folio de servicio para Laboratorio o Planta.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "Laboratorio" | "Planta")}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="Laboratorio">Laboratorio</TabsTrigger>
            <TabsTrigger value="Planta">Planta</TabsTrigger>
          </TabsList>
          <form onSubmit={handleSubmit}>
            <TabsContent value="Laboratorio" className="mt-4">
              <Card>
                <CardHeader><CardTitle className="text-lg">Nuevo Folio - Laboratorio</CardTitle></CardHeader>
                <CardContent>
                  <FolioFormFields />
                </CardContent>
                <CardFooter className="flex justify-end space-x-2">
                  <Button type="button" variant="outline" onClick={() => setFormData(initialFolioData)}>Limpiar</Button>
                  <Button type="submit">Guardar Folio Laboratorio</Button>
                </CardFooter>
              </Card>
            </TabsContent>
            <TabsContent value="Planta" className="mt-4">
              <Card>
                <CardHeader><CardTitle className="text-lg">Nuevo Folio - Planta</CardTitle></CardHeader>
                <CardContent>
                  <FolioFormFields />
                </CardContent>
                <CardFooter className="flex justify-end space-x-2">
                  <Button type="button" variant="outline" onClick={() => setFormData(initialFolioData)}>Limpiar</Button>
                  <Button type="submit">Guardar Folio Planta</Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </form>
        </Tabs>

        {/* TODO: Sección para mostrar folios existentes */}
        <div className="mt-8">
          <h3 className="text-lg font-semibold mb-4">Folios Asignados ({activeTab})</h3>
          {/* Aquí iría una tabla o lista de folios */}
          <p className="text-sm text-gray-500">
            La tabla de folios asignados se implementará aquí.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
