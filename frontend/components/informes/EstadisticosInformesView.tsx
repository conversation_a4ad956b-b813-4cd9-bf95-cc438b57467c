"use client";

import { useState, useEffect, useMemo, memo } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DownloadIcon, BarChartBigIcon, PieChartIcon, TrendingUpIcon, ListChecksIcon } from "lucide-react";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart";
import { Bar, BarChart, CartesianGrid, XAxis, YAxis, Pie, <PERSON>hart as RechartsPieChart, Cell, ResponsiveContainer, Legend, Tooltip as RechartsTooltip } from "recharts";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

// Interfaz para los datos estadísticos que vendrían del backend
interface EstadisticasInformes {
  totalFoliosPeriodo: number;
  promedioTiempoEntrega?: number; // en días
  porcentajeMorosos: number;
  foliosPorMes: { mes: string; anio: number; laboratorio: number; planta: number; total: number }[];
  foliosPorEstado: { estado: string; cantidad: number }[];
  foliosPorSede: { sede: string; cantidad: number }[];
  // Otros datos que puedan ser necesarios
}

// Colores para las gráficas de pastel/donas
const PIE_CHART_COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#AF19FF", "#FF19AF"];

// Datos de ejemplo
const sampleEstadisticas: EstadisticasInformes = {
  totalFoliosPeriodo: 125,
  promedioTiempoEntrega: 7.5,
  porcentajeMorosos: 15,
  foliosPorMes: [
    { mes: "Enero", anio: 2024, laboratorio: 20, planta: 10, total: 30 },
    { mes: "Febrero", anio: 2024, laboratorio: 25, planta: 15, total: 40 },
    { mes: "Marzo", anio: 2024, laboratorio: 22, planta: 18, total: 40 },
    { mes: "Abril", anio: 2024, laboratorio: 10, planta: 5, total: 15 },
  ],
  foliosPorEstado: [
    { estado: "Informe enviado", cantidad: 80 },
    { estado: "En proceso de emisión", cantidad: 20 },
    { estado: "Informe moroso", cantidad: 15 },
    { estado: "Template no recibido", cantidad: 5 },
    { estado: "Pendiente de asignación", cantidad: 5 },
  ],
  foliosPorSede: [
    { sede: "GDL", cantidad: 70 },
    { sede: "CDMX", cantidad: 35 },
    { sede: "MTY", cantidad: 20 },
  ]
};

// Componentes memoizados para mejorar rendimiento
const StatsCard = memo(({ title, value, subtitle, icon: Icon, bgColor }: {
  title: string;
  value: string | number;
  subtitle: string;
  icon: any;
  bgColor: string;
}) => (
  <Card className="shadow-sm force-no-border card-no-border">
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      <Icon className="h-4 w-4 text-muted-foreground" />
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      <p className="text-xs text-muted-foreground">{subtitle}</p>
    </CardContent>
  </Card>
));

const BarChartComponent = memo(({ data, config }: { data: any[]; config: any }) => (
  <ChartContainer config={config} className="min-h-[300px] w-full">
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={data}>
        <CartesianGrid vertical={false} />
        <XAxis dataKey="mes" tickLine={false} tickMargin={10} axisLine={false} />
        <YAxis />
        <RechartsTooltip
          cursor={false}
          content={<ChartTooltipContent indicator="dashed" />}
        />
        <Legend content={<ChartLegendContent />} />
        <Bar dataKey="laboratorio" fill="var(--color-laboratorio, #8884d8)" radius={4} name="Laboratorio" />
        <Bar dataKey="planta" fill="var(--color-planta, #82ca9d)" radius={4} name="Planta" />
      </BarChart>
    </ResponsiveContainer>
  </ChartContainer>
));

const PieChartComponent = memo(({ data, colors }: { data: any[]; colors: string[] }) => (
  <ChartContainer config={{}} className="min-h-[400px] w-full">
    <ResponsiveContainer width="100%" height={400}>
      <RechartsPieChart>
        <RechartsTooltip
          content={({ active, payload }) => {
            if (active && payload && payload.length) {
              const data = payload[0];
              const value = data.value as number;
              const total = data.payload?.payload ?
                Object.values(data.payload.payload).reduce((sum: number, item: any) => sum + (typeof item === 'number' ? item : 0), 0) :
                value;
              return (
                <div className="bg-white p-2 border rounded shadow-lg">
                  <p className="font-medium">{data.name}</p>
                  <p className="text-sm">Cantidad: {value}</p>
                  <p className="text-sm">Porcentaje: {((value / total) * 100).toFixed(1)}%</p>
                </div>
              );
            }
            return null;
          }}
        />
        <Pie
          data={data}
          dataKey="cantidad"
          nameKey="estado"
          cx="50%"
          cy="50%"
          outerRadius={120}
          innerRadius={40}
          paddingAngle={2}
          label={false}
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
          ))}
        </Pie>
        <Legend
          verticalAlign="bottom"
          height={36}
          formatter={(value) => (
            <span style={{ fontSize: '14px' }}>
              {value}
            </span>
          )}
        />
      </RechartsPieChart>
    </ResponsiveContainer>
  </ChartContainer>
));

export const EstadisticosInformesView = memo(() => {
  const [statsData, setStatsData] = useState<EstadisticasInformes | null>(null);
  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({
    from: new Date(new Date().getFullYear(), 0, 1), // Default: inicio del año actual
    to: new Date(), // Default: hoy
  });
  const [filtroTipoServicio, setFiltroTipoServicio] = useState<string>("TODOS");
  const [filtroSede, setFiltroSede] = useState<string>("TODAS");

  useEffect(() => {
    // Simular carga de datos del backend con debounce
    const timer = setTimeout(() => {
      console.log("Cargando estadísticas para:", dateRange, filtroTipoServicio, filtroSede);
      setStatsData(sampleEstadisticas);
    }, 300);

    return () => clearTimeout(timer);
  }, [dateRange, filtroTipoServicio, filtroSede]);

  const handleExport = useMemo(() => () => {
    console.log("Exportando datos estadísticos...", statsData);
    // TODO: Implementar lógica de exportación a Excel/CSV
  }, [statsData]);

  // Configuración memoizada para la gráfica de Folios por Mes
  const barChartConfig = useMemo(() => ({
    labPlanta: { label: "Lab/Planta", color: "hsl(var(--chart-1))" },
  }), []);

  // Datos memoizados para evitar re-renders innecesarios
  const memoizedStatsCards = useMemo(() => {
    if (!statsData) return null;
    
    return [
      {
        title: "Total Folios (Período)",
        value: statsData.totalFoliosPeriodo,
        subtitle: "folios registrados",
        icon: ListChecksIcon,
        bgColor: "bg-blue-50"
      },
      {
        title: "Prom. Tiempo Entrega",
        value: `${statsData.promedioTiempoEntrega?.toFixed(1) ?? 'N/A'} días`,
        subtitle: "tiempo promedio",
        icon: TrendingUpIcon,
        bgColor: "bg-green-50"
      },
      {
        title: "% Informes Morosos",
        value: `${statsData.porcentajeMorosos.toFixed(1)}%`,
        subtitle: "informes atrasados",
        icon: PieChartIcon,
        bgColor: "bg-red-50"
      }
    ];
  }, [statsData]);

  if (!statsData) {
    return (
      <Card>
        <CardHeader><CardTitle>Estadísticas de Informes</CardTitle></CardHeader>
        <CardContent><p>Cargando datos estadísticos...</p></CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Filtros de Estadísticas</CardTitle>
        </CardHeader>
        <CardContent className="grid sm:grid-cols-2 md:grid-cols-3 gap-4">
          <div>
            <Label>Rango de Fechas</Label>
            <Input type="text" readOnly value={`Desde: ${dateRange.from?.toLocaleDateString() ?? '-'} - Hasta: ${dateRange.to?.toLocaleDateString() ?? '-'}`} placeholder="Seleccionar rango (componente pendiente)" />
          </div>
          <div>
            <Label htmlFor="filtroTipoServicioStats">Tipo de Servicio</Label>
            <Select value={filtroTipoServicio} onValueChange={setFiltroTipoServicio}>
              <SelectTrigger id="filtroTipoServicioStats"><SelectValue /></SelectTrigger>
              <SelectContent>
                <SelectItem value="TODOS">Todos</SelectItem>
                <SelectItem value="Laboratorio">Laboratorio</SelectItem>
                <SelectItem value="Planta">Planta</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="filtroSedeStats">Sede</Label>
            <Select value={filtroSede} onValueChange={setFiltroSede}>
              <SelectTrigger id="filtroSedeStats"><SelectValue /></SelectTrigger>
              <SelectContent>
                <SelectItem value="TODAS">Todas las Sedes</SelectItem>
                <SelectItem value="GDL">Guadalajara (GDL)</SelectItem>
                <SelectItem value="CDMX">Ciudad de México (CDMX)</SelectItem>
                <SelectItem value="MTY">Monterrey (MTY)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Tarjetas de Resumen (KPIs) */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {memoizedStatsCards?.map((card, index) => (
          <StatsCard
            key={index}
            title={card.title}
            value={card.value}
            subtitle={card.subtitle}
            icon={card.icon}
            bgColor={card.bgColor}
          />
        ))}
      </div>

      {/* Gráficas */}
      <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Folios por Mes (Lab vs Planta)</CardTitle>
            <CardDescription>{`Datos para ${filtroTipoServicio === 'TODOS' ? 'todos los servicios' : filtroTipoServicio}, ${filtroSede === 'TODAS' ? 'todas las sedes' : filtroSede}`}</CardDescription>
          </CardHeader>
          <CardContent>
            <BarChartComponent data={statsData.foliosPorMes} config={barChartConfig} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Distribución de Folios por Estado</CardTitle>
            <CardDescription>{`Datos para ${filtroTipoServicio === 'TODOS' ? 'todos los servicios' : filtroTipoServicio}, ${filtroSede === 'TODAS' ? 'todas las sedes' : filtroSede}`}</CardDescription>
          </CardHeader>
          <CardContent>
            <PieChartComponent data={statsData.foliosPorEstado} colors={PIE_CHART_COLORS} />
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-end">
        <Button onClick={handleExport}>
          <DownloadIcon className="mr-2 h-4 w-4" />
          Exportar Datos a Excel
        </Button>
      </div>
    </div>
  );
});

// Asignar displayName para debugging
EstadisticosInformesView.displayName = 'EstadisticosInformesView';
