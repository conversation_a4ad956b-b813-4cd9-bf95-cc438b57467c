"use client"

import type React from "react"
import { createContext, useContext, useEffect } from "react"
import { useWebSocket, type NotificationData } from "@/lib/websocket"
import wsManager from "@/lib/websocket"
import { toast } from "sonner"
import { useAuth } from '@/components/providers/auth-provider'

interface WebSocketContextType {
  isConnected: boolean
  notifications: NotificationData[]
  notificationStats: { unread: number; total: number }
  clearNotifications: () => void
  markAsRead: (id: string) => Promise<void>
  requestNotificationPermission: () => Promise<boolean>
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined)

console.log('[WS][PROVIDER] WebSocketProvider montado');

export function WebSocketProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  // Obtener token del storage si hay usuario
  const token = (typeof window !== 'undefined' && user) ? localStorage.getItem('auth_token') : null;
  // Pasar user y token al hook
  const wsData = useWebSocket(user, token);

  // Suscribirse a los roles del usuario autenticado
  useEffect(() => {
    if (user && user.roles && user.roles.length > 0) {
      user.roles.forEach((role: any) => {
        const roleId = typeof role === 'string' ? role : role.id;
        wsManager.subscribeToRole(roleId);
      });
    }
  }, [user]);

  useEffect(() => {
    console.log('[WS][PROVIDER] useEffect disparado');
    if (wsData.notifications.length > 0) {
      const latestNotification = wsData.notifications[0]
      if (latestNotification.status === "unread") {
        const toastOptions = {
          description: latestNotification.message,
          duration: 5000,
        }
        switch (latestNotification.type) {
          case "success":
            toast.success(latestNotification.title, toastOptions)
            break
          case "error":
            toast.error(latestNotification.title, toastOptions)
            break
          case "warning":
            toast.warning(latestNotification.title, toastOptions)
            break
          case "info":
          default:
            toast.info(latestNotification.title, toastOptions)
            break
        }
      }
    }
  }, [wsData.notifications])

  useEffect(() => {
    if (wsData.isConnected) {
      console.log('🔗 WebSocket conectado - Notificaciones en tiempo real activas')
    } else {
      console.log('🔌 WebSocket desconectado - Notificaciones en tiempo real pausadas')
    }
  }, [wsData.isConnected])

  // Siempre renderiza el provider, pero si no hay usuario, expón un valor vacío seguro
  const emptyWsContext = {
    isConnected: false,
    notifications: [],
    notificationStats: { unread: 0, total: 0 },
    clearNotifications: () => {},
    markAsRead: async () => {},
    requestNotificationPermission: async () => false,
  };

  return (
    <WebSocketContext.Provider value={user ? wsData : emptyWsContext}>
      {children}
    </WebSocketContext.Provider>
  );
}

export function useWebSocketContext() {
  const context = useContext(WebSocketContext)
  if (context === undefined) {
    throw new Error("useWebSocketContext must be used within a WebSocketProvider")
  }
  return context
}

export { useWebSocket }
