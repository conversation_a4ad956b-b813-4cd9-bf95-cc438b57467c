"use client";

import { ThemeProvider } from "./theme-provider";
import { AuthProvider } from "./auth-provider";
import { WebSocketProvider } from "./websocket-provider";
import { QueryProvider } from "./query-provider";
import { NotificationProvider } from "../ui/notification-provider";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider>
      <AuthProvider>
        <WebSocketProvider>
          <QueryProvider>
            <NotificationProvider
              maxNotifications={5}
              defaultPosition="top-right"
              defaultDuration={5000}
            >
              {children}
            </NotificationProvider>
          </QueryProvider>
        </WebSocketProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}