"use client"

import type React from "react"
import { createContext, useContext, useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { authService, type User, type LoginCredentials } from "@/lib/auth"

interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => Promise<void>
  hasPermission: (permission: string) => boolean
  hasRole: (role: string) => boolean
  canAccessModule: (module: string) => boolean
  token: string | null
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    // Verificar autenticación al cargar
    const initAuth = async () => {
      if (typeof window !== 'undefined' && (window as any).__justLoggedIn) {
        delete (window as any).__justLoggedIn;
        setIsLoading(false);
        return;
      }
      try {
        if (authService.isAuthenticated()) {
          const userData = authService.getUser()
          setUser(userData)
        } else {
          const newToken = await authService.refreshToken()
          if (newToken) {
            const userData = authService.getUser()
            setUser(userData)
          } else {
            authService.clearAuth()
          }
        }
      } catch (error) {
        authService.clearAuth()
      } finally {
        setIsLoading(false)
      }
    }
    initAuth()
  }, [])

  const login = async (credentials: LoginCredentials) => {
    try {
      setIsLoading(true)
      const authData = await authService.login(credentials)
      setUser(authData.user)
      if (typeof window !== 'undefined') {
        (window as any).__justLoggedIn = true;
      }
      router.push("/dashboard")
    } catch (error) {
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    try {
      setIsLoading(true)
      await authService.logout()
      setUser(null)
      router.push("/login")
    } catch (error) {
    } finally {
      setIsLoading(false)
    }
  }

  const hasPermission = (permission: string): boolean => {
    return authService.hasPermission(permission)
  }

  const hasRole = (role: string): boolean => {
    return authService.hasRole(role)
  }

  const canAccessModule = (module: string): boolean => {
    return authService.canAccessModule(module)
  }

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    logout,
    hasPermission,
    hasRole,
    canAccessModule,
    token: authService.getToken(),
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
