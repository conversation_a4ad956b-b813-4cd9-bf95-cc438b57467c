"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Bell, LogOut, Check, Trash2, Moon, Sun } from "lucide-react"
import { cn } from "@/lib/utils"
// Definir el tipo Notification localmente para evitar error de importación
export type Notification = {
  id: number
  title: string
  message: string
  type: keyof typeof notificationTypeColors
  time: string
  read: boolean
}

interface HeaderProps {
  darkMode: boolean
  onToggleDarkMode: (darkMode: boolean) => void
  notifications: Notification[]
  onMarkAsRead: (id: number) => void
  onDeleteNotification: (id: number) => void
  onLogout?: () => void
  user?: {
    name: string
    email: string
  }
  className?: string
}

const notificationTypeColors = {
  info: "bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-200",
  warning: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/40 dark:text-yellow-200",
  success: "bg-green-100 text-green-800 dark:bg-green-900/40 dark:text-green-200",
  error: "bg-red-100 text-red-800 dark:bg-red-900/40 dark:text-red-200",
}

export function DashboardHeader({
  darkMode,
  onToggleDarkMode,
  notifications = [],
  onMarkAsRead,
  onDeleteNotification,
  onLogout,
  user = { name: "Admin User", email: "<EMAIL>" },
  className,
}: HeaderProps) {
  const [notificationsOpen, setNotificationsOpen] = useState(false)
  const unreadCount = notifications.filter((n) => !n.read).length

  return (
    <>
      {/* Spacer to prevent content from going under fixed header */}
      <div className="h-6" />

      <header
        className={cn(
          "fixed top-0 left-0 right-0 h-16 flex items-center justify-between px-6 backdrop-blur-xl border-b transition-all duration-300 z-50",
          darkMode
            ? "bg-slate-900/90 border-slate-700/30 text-slate-100"
            : "bg-white/80 border-gray-200/50 text-gray-900",
          className,
        )}
      >
        {/* Left side - Brand Logo */}
        <div className="flex items-center">
          <div
            className={cn(
              "text-xl font-semibold tracking-wide px-4 py-2 rounded-lg transition-all duration-300",
              darkMode
                ? "text-slate-100 bg-slate-800/60 border border-slate-700/50"
                : "text-gray-900 bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200/50",
            )}
          >
            COMINTEC
          </div>
        </div>

        {/* Right side - Controls */}
        <div className="flex items-center gap-4">
          {/* Dark Mode Toggle */}
          <div className="flex items-center gap-2">
            <Sun className={cn("h-4 w-4", darkMode ? "text-slate-400" : "text-yellow-600")} />
            <Switch
              checked={darkMode}
              onCheckedChange={onToggleDarkMode}
              className={cn(
                "data-[state=checked]:bg-slate-600 transition-colors duration-300",
                darkMode ? "data-[state=unchecked]:bg-slate-700" : "data-[state=unchecked]:bg-orange-300",
              )}
            />
            <Moon className={cn("h-4 w-4", darkMode ? "text-blue-400" : "text-slate-500")} />
          </div>

          {/* Notifications */}
          <DropdownMenu open={notificationsOpen} onOpenChange={setNotificationsOpen}>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className={cn(
                  "rounded-full backdrop-blur-md border relative transition-all duration-500",
                  darkMode
                    ? "bg-slate-800/50 border-slate-700/50 hover:bg-slate-700/60 text-slate-200"
                    : "bg-yellow-100/50 border-orange-300/50 hover:bg-orange-200/60 text-gray-800",
                )}
              >
                <Bell className="h-5 w-5" />
                {unreadCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {unreadCount}
                  </span>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className={cn(
                "w-80 max-h-96 overflow-y-auto backdrop-blur-xl border shadow-2xl",
                darkMode
                  ? "bg-slate-900/95 border-slate-700/50 text-slate-100"
                  : "bg-yellow-50/95 border-orange-300/50 text-gray-900",
              )}
            >
              <div
                className={cn(
                  "p-3 border-b font-light",
                  darkMode ? "border-slate-700/50 text-slate-100" : "border-orange-300/50 text-gray-800",
                )}
              >
                Notificaciones ({unreadCount} sin leer)
              </div>
              {notifications.length === 0 ? (
                <div
                  className={cn("p-4 text-center text-sm font-light", darkMode ? "text-slate-400" : "text-gray-500")}
                >
                  No hay notificaciones
                </div>
              ) : (
                notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={cn(
                      "p-3 border-b transition-colors",
                      darkMode
                        ? "border-slate-700/30 hover:bg-slate-800/40"
                        : "border-orange-300/30 hover:bg-orange-100/40",
                      !notification.read && (darkMode ? "bg-slate-800/30" : "bg-orange-100/30"),
                    )}
                  >
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className={cn("text-sm font-light", darkMode ? "text-slate-100" : "text-gray-800")}> 
                            {notification.title}
                          </h4>
                          <Badge className={cn("text-xs font-light", notificationTypeColors[notification.type])}>
                            {notification.type}
                          </Badge>
                        </div>
                        <p className={cn("text-xs font-light mb-1", darkMode ? "text-slate-300" : "text-gray-600")}> 
                          {notification.message}
                        </p>
                        <p className={cn("text-xs font-light", darkMode ? "text-slate-400" : "text-gray-500")}> 
                          {notification.time}
                        </p>
                      </div>
                      <div className="flex items-center gap-1">
                        {!notification.read && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onMarkAsRead(notification.id)}
                            className="h-6 w-6 p-0"
                          >
                            <Check className="h-3 w-3" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onDeleteNotification(notification.id)}
                          className="h-6 w-6 p-0 text-red-500 hover:text-red-600"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className={cn(
                  "rounded-full backdrop-blur-md border transition-all duration-500",
                  darkMode
                    ? "bg-slate-800/50 border-slate-700/50 hover:bg-slate-700/60 text-slate-200"
                    : "bg-yellow-100/50 border-orange-300/50 hover:bg-orange-200/60 text-gray-800",
                )}
              >
                <span className="text-sm font-medium">AD</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className={cn(
                "backdrop-blur-xl border shadow-2xl",
                darkMode
                  ? "bg-slate-900/95 border-slate-700/50 text-slate-100"
                  : "bg-yellow-50/95 border-orange-300/50 text-gray-900",
              )}
            >
              <div className={cn("p-3 border-b", darkMode ? "border-slate-700/50" : "border-orange-300/50")}> 
                <p className={cn("font-light", darkMode ? "text-slate-100" : "text-gray-800")}>{user.name}</p>
                <p className={cn("text-sm font-light", darkMode ? "text-slate-400" : "text-gray-500")}>{user.email}</p>
              </div>
              <DropdownMenuSeparator className={darkMode ? "bg-slate-700/50" : "bg-orange-300/50"} />
              <DropdownMenuItem
                onClick={onLogout}
                className={cn(
                  "font-light",
                  darkMode ? "text-slate-300 hover:bg-slate-800/50" : "text-gray-700 hover:bg-orange-100/50",
                )}
              >
                <LogOut className="mr-2 h-4 w-4" />
                Cerrar Sesión
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </header>
    </>
  )
}
