import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';

const validationSchema = Yup.object().shape({
  name: Yup.string().required('El nombre es obligatorio'),
  email: Yup.string().email('Email inválido').required('El email es obligatorio'),
  phone: Yup.string(),
  area: Yup.string(),
  password: Yup.string()
    .min(8, 'La contraseña debe tener al menos 8 caracteres')
    .matches(/[a-z]/, 'Debe contener al menos una minúscula')
    .matches(/[A-Z]/, 'Debe contener al menos una mayúscula')
    .matches(/[0-9]/, 'Debe contener al menos un número')
    .matches(/[^a-zA-Z0-9]/, 'Debe contener al menos un carácter especial'),
  roles: Yup.array().min(1, '<PERSON><PERSON>cciona al menos un rol'),
});

interface UserFormProps {
  user?: any;
  onSubmit: (data: any) => void;
  rolesList: any[];
}

export function UserForm({ user, onSubmit, rolesList }: UserFormProps) {
  const { register, handleSubmit, setValue, formState: { errors } } = useForm({
    resolver: yupResolver(validationSchema),
  });

  useEffect(() => {
    if (user) {
      setValue('name', user.name);
      setValue('email', user.email);
      setValue('phone', user.phone);
      setValue('area', user.area);
      setValue('roles', user.roles.map((r: any) => r.name));
    }
  }, [user, setValue]);

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="form-group">
        <label>Name</label>
        <input type="text" {...register('name')} className={`form-control ${errors.name ? 'is-invalid' : ''}`} />
        <div className="invalid-feedback">{errors.name?.message}</div>
      </div>
      <div className="form-group">
        <label>Email</label>
        <input type="email" {...register('email')} className={`form-control ${errors.email ? 'is-invalid' : ''}`} />
        <div className="invalid-feedback">{errors.email?.message}</div>
      </div>
      <div className="form-group">
        <label>Phone</label>
        <input type="text" {...register('phone')} className="form-control" />
      </div>
      <div className="form-group">
        <label>Area</label>
        <input type="text" {...register('area')} className="form-control" />
      </div>
      <div className="form-group">
        <label>Contraseña</label>
        <input type="password" placeholder="Mínimo 8 caracteres, 1 mayúscula, 1 minúscula, 1 número, 1 especial" {...register('password')} className={`form-control ${errors.password ? 'is-invalid' : ''}`} />
        <div className="invalid-feedback">{errors.password?.message}</div>
        <div className="form-text text-muted">Mínimo 8 caracteres, 1 mayúscula, 1 minúscula, 1 número, 1 especial.</div>
      </div>
      <div className="form-group">
        <label>Roles</label>
        <select multiple {...register('roles')} className={`form-control ${errors.roles ? 'is-invalid' : ''}`}>
          {rolesList.map((role) => (
            <option key={role.id} value={role.id}>{role.name}</option>
          ))}
        </select>
        <div className="invalid-feedback">{errors.roles?.message}</div>
      </div>
      <div className="form-group">
        <button type="submit" className="btn btn-primary">Submit</button>
      </div>
    </form>
  );
} 