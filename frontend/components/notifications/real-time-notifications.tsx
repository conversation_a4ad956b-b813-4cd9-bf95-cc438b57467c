"use client"

import React, { useState, useEffect } from "react"
import { Bell, Check, X, AlertCircle, Info, CheckCircle, AlertTriangle, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuItem
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"
import { useWebSocketContext } from "@/components/providers/websocket-provider"
import { formatDistanceToNow } from "date-fns"
import { es } from "date-fns/locale"
import { Dialog, DialogContent, DialogTitle, DialogDescription, DialogHeader, DialogFooter, DialogClose } from "@/components/ui/dialog"
import { useAuth } from '@/components/providers/auth-provider'
import { useRouter } from 'next/navigation'

const getNotificationIcon = (type: string) => {
  switch (type) {
    case "success":
      return <CheckCircle className="h-4 w-4 text-green-500" />
    case "error":
      return <AlertCircle className="h-4 w-4 text-red-500" />
    case "warning":
      return <AlertTriangle className="h-4 w-4 text-yellow-500" />
    case "info":
    default:
      return <Info className="h-4 w-4 text-blue-500" />
  }
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case "high":
      return "bg-red-100 text-red-800 border-red-200"
    case "medium":
      return "bg-blue-100 text-blue-800 border-blue-200"
    case "low":
    default:
      return "bg-gray-100 text-gray-800 border-gray-200"
  }
}

interface NotificationData {
  id: string
  title: string
  message: string
  type: string
  priority: string
  status: 'unread' | 'read'
  timestamp: string
}

function getNotificationDateString(n: any) {
  const dateStr = n.timestamp || n.created_at;
  const date = dateStr ? new Date(dateStr) : null;
  if (!date || isNaN(date.getTime())) return "Sin fecha";
  return formatDistanceToNow(date, { addSuffix: true, locale: es });
}

function NotificationCenter({ open, onClose }: { open: boolean, onClose: () => void }) {
  const [notifications, setNotifications] = useState<NotificationData[]>([])
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [filter, setFilter] = useState<'all'|'unread'|'read'>('all')
  const router = useRouter();
  const token = typeof window !== 'undefined' ? localStorage.getItem('access_token') : null

  const fetchNotifications = async () => {
    setLoading(true)
    setError(null)
    try {
      let url = `/api/notifications?page=${page}&limit=20`;
      if (filter !== 'all') {
        url += `&status=${filter}`;
      }
      const res = await fetch(url, {
        headers: token ? { 'Authorization': `Bearer ${token}` } : {}
      })
      if (res.status === 401) {
        // Token inválido, limpiar sesión y redirigir
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user');
        setError('Sesión expirada. Por favor, inicia sesión de nuevo.');
        setTimeout(() => router.push('/login'), 1500);
        return;
      }
      if (!res.ok) {
        throw new Error('Error al cargar notificaciones');
      }
      const data = await res.json()
      setNotifications(data.data)
      setTotalPages(data.totalPages || 1)
    } catch (e: any) {
      setNotifications([])
      setError(e.message || 'Error de red al cargar notificaciones')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (!open) return
    fetchNotifications()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, page, filter, token])

  const handleDeleteNotification = async (notificationId: string) => {
    if (typeof window === 'undefined') return;
    const token = localStorage.getItem('auth_token');
    const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
    const url = `${API_URL}/notifications/${notificationId}`;
    console.log('[DEBUG][DELETE] Token:', token, 'URL:', url);
    if (!token) {
      alert('No autenticado. Por favor, inicia sesión.');
      return;
    }
    const res = await fetch(url, {
      method: 'DELETE',
      headers: { 'Authorization': `Bearer ${token}` }
    });
    if (!res.ok) {
      const error = await res.text();
      alert('Error eliminando notificación: ' + error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-lg w-full">
        <DialogHeader>
          <DialogTitle>Centro de Notificaciones</DialogTitle>
          <DialogDescription>Histórico de notificaciones recibidas</DialogDescription>
        </DialogHeader>
        <div className="flex gap-2 mb-2">
          <Button size="sm" variant={filter==='all'?'default':'outline'} onClick={()=>setFilter('all')}>Todas</Button>
          <Button size="sm" variant={filter==='unread'?'default':'outline'} onClick={()=>setFilter('unread')}>No leídas</Button>
          <Button size="sm" variant={filter==='read'?'default':'outline'} onClick={()=>setFilter('read')}>Leídas</Button>
        </div>
        <ScrollArea className="h-80">
          {loading ? (
            <div className="flex flex-col items-center justify-center p-4">
              <Loader2 className="w-6 h-6 animate-spin mb-2" />
              <span>Cargando notificaciones...</span>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center p-4">
              <AlertCircle className="w-8 h-8 text-red-500 mb-2" />
              <p className="text-red-600">{error}</p>
              {error !== 'Sesión expirada. Por favor, inicia sesión de nuevo.' && (
                <Button onClick={fetchNotifications} className="mt-2">Reintentar</Button>
              )}
            </div>
          ) : deduplicateNotifications(notifications).length === 0 ? (
            <div className="p-4 text-center text-muted-foreground">No hay notificaciones</div>
          ) : (
            deduplicateNotifications(notifications).map(n => (
              <div
                key={n.id}
                className={cn(
                  "p-3 border-b flex items-start gap-3 transition-opacity",
                  n.status === 'read' ? 'opacity-50 bg-gray-100' : 'bg-white'
                )}
              >
                {getNotificationIcon(n.type)}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 flex-wrap">
                    <span className="font-medium text-sm truncate">{n.title}</span>
                    <Badge className={getPriorityColor(n.priority)}>{n.priority}</Badge>
                  </div>
                  <div className="text-xs text-muted-foreground break-words whitespace-pre-line">{n.message}</div>
                  <div className="text-xs text-muted-foreground">{getNotificationDateString(n)}</div>
                </div>
                <div className="flex flex-col gap-1 items-end min-w-[90px]">
                  {n.status === 'unread' && (
                    <Button size="sm" variant="ghost" className="w-full" onClick={() => handleDeleteNotification(n.id)}>
                      Eliminar
                    </Button>
                  )}
                </div>
              </div>
            ))
          )}
        </ScrollArea>
        <div className="flex justify-between items-center mt-2">
          <Button size="sm" disabled={page<=1} onClick={()=>setPage(p=>p-1)}>Anterior</Button>
          <span className="text-xs">Página {page} de {totalPages}</span>
          <Button size="sm" disabled={page>=totalPages} onClick={()=>setPage(p=>p+1)}>Siguiente</Button>
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline">Cerrar</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Función de deduplicación
function deduplicateNotifications(notifications: NotificationData[]) {
  // Si no hay notificaciones, retorna []
  if (!notifications || notifications.length === 0) return [];
  const map = new Map<string, NotificationData>();
  for (const n of notifications) {
    const meta = (n as any).metadata;
    const createdAt = n.timestamp || (n as any).created_at;
    const key = `${n.title}-${meta?.productId ?? ''}`;
    // Si ya existe una notificación con esa key
    if (!map.has(key)) {
      map.set(key, n);
    } else {
      const existing = map.get(key)!;
      const existingCreatedAt = existing.timestamp || (existing as any).created_at;
      // Si la nueva es unread, siempre la priorizamos
      if (n.status === 'unread') {
        map.set(key, n);
      } else if (existing.status === 'unread') {
        // Si la existente es unread, la dejamos
        continue;
      } else {
        // Ambas son read, dejamos la más reciente
        if (new Date(createdAt) > new Date(existingCreatedAt)) {
          map.set(key, n);
        }
      }
    }
  }
  return Array.from(map.values()).sort((a, b) => {
    const aDate = a.timestamp || (a as any).created_at;
    const bDate = b.timestamp || (b as any).created_at;
    return new Date(bDate).getTime() - new Date(aDate).getTime();
  });
}

// Añadir función utilitaria para formatear fecha si no existe
function formatDate(dateString: string) {
  if (!dateString) return '';
  const d = new Date(dateString);
  return d.toLocaleString('es-MX', { dateStyle: 'short', timeStyle: 'short' });
}

export function RealTimeNotifications() {
  const { user, token } = useAuth();
  if (!user) return null;
  const { 
    notifications, 
    notificationStats, 
    isConnected, 
    markAsRead, 
    clearNotifications,
    requestNotificationPermission 
  } = useWebSocketContext()
  
  const [isOpen, setIsOpen] = useState(false)
  const [showCenter, setShowCenter] = useState(false)

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markAsRead(notificationId)
    } catch (error) {
      console.error("Error marking notification as read:", error)
    }
  }

  const handleRequestPermission = async () => {
    try {
      const granted = await requestNotificationPermission()
      if (granted) {
        console.log("Permisos de notificación concedidos")
      } else {
        console.log("Permisos de notificación denegados")
      }
    } catch (error) {
      console.error("Error requesting notification permission:", error)
    }
  }

  const unreadCount = notifications.filter(n => n.status === 'unread').length;
  const hasUnread = unreadCount > 0

  const handleDeleteNotification = async (notificationId: string) => {
    if (!token) {
      alert('No autenticado. Por favor, inicia sesión.');
      return;
    }
    const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
    const url = `${API_URL}/notifications/${notificationId}`;
    console.log('[DEBUG][DELETE] Token:', token, 'URL:', url);
    const res = await fetch(url, {
      method: 'DELETE',
      headers: { 'Authorization': `Bearer ${token}` }
    });
    if (!res.ok) {
      const error = await res.text();
      alert('Error eliminando notificación: ' + error);
    }
  };

  return (
    <div className="relative">
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" className="relative">
            <Bell className="h-5 w-5" />
            {hasUnread && (
              <Badge 
                variant="destructive" 
                className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
              >
                {unreadCount > 99 ? "99+" : unreadCount}
              </Badge>
            )}
            <span className="sr-only">Notificaciones</span>
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent align="end" className="w-80">
          <div className="p-4">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold">Notificaciones</h3>
              <div className="flex items-center gap-2">
                <div 
                  className={cn(
                    "h-2 w-2 rounded-full",
                    isConnected ? "bg-green-500" : "bg-red-500"
                  )}
                  title={isConnected ? "Conectado" : "Desconectado"}
                />
                <span className="text-xs text-muted-foreground">
                  {isConnected ? "En tiempo real" : "Sin conexión"}
                </span>
              </div>
            </div>
            <Button
              size="sm"
              variant="outline"
              className="mt-2 w-full"
              onClick={() => setShowCenter(true)}
            >
              Ver historial completo
            </Button>
          </div>
          <DropdownMenuSeparator />
          <ScrollArea className="h-80">
            {notifications.length === 0 ? (
              <div className="p-4 text-center text-muted-foreground text-xs">No hay notificaciones</div>
            ) : (
              notifications.map((n) => (
                <div
                  key={n.id}
                  className={cn(
                    "p-3 border-b flex items-start gap-3 transition-opacity",
                    n.status === 'read' ? 'opacity-50 bg-gray-100' : 'bg-white'
                  )}
                >
                  {getNotificationIcon(n.type)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 flex-wrap">
                      <span className="font-medium text-sm truncate">{n.title}</span>
                      <Badge className={getPriorityColor(n.priority)}>{n.priority}</Badge>
                    </div>
                    <div className="text-xs text-muted-foreground break-words whitespace-pre-line">{n.message}</div>
                    <div className="text-[10px] text-muted-foreground mt-1">{formatDate(n.timestamp)}</div>
                  </div>
                  <div className="flex flex-col gap-1 ml-2">
                    {n.status === 'unread' && (
                      <Button size="sm" variant="outline" onClick={() => markAsRead(n.id)}>
                        Marcar leída
                      </Button>
                    )}
                    <Button size="sm" variant="ghost" onClick={() => handleDeleteNotification(n.id)}>
                      Eliminar
                    </Button>
                  </div>
                </div>
              ))
            )}
          </ScrollArea>
        </DropdownMenuContent>
      </DropdownMenu>
      {showCenter && <NotificationCenter open={showCenter} onClose={()=>setShowCenter(false)} />}
    </div>
  )
}

/* Opcionalmente, también lo exportamos por default */
export default RealTimeNotifications
