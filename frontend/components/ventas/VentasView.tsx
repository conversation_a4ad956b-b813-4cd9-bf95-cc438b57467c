'use client';

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { VentasDashboard } from './VentasDashboard';
// Import other sales components as they are created
// import { ClientesTable } from './ClientesTable';
// import { CotizacionesTable } from './CotizacionesTable';

export function VentasView() {
  return (
    <div className="space-y-6 p-6">
      {/* Header Section */}
      <header className="space-y-2 mb-8">
        <h1 className="text-3xl font-medium tracking-tight text-gray-900 dark:text-slate-100">
          Ventas
        </h1>
        <p className="text-gray-600 dark:text-slate-400">
          Gestión de clientes, cotizaciones y seguimiento de ventas.
        </p>
      </header>

      <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20 rounded-lg p-6">
        <Tabs defaultValue="dashboard">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
            <TabsTrigger value="clientes">Clientes</TabsTrigger>
            <TabsTrigger value="cotizaciones">Cotizaciones</TabsTrigger>
            <TabsTrigger value="productos">Productos</TabsTrigger>
          </TabsList>
          <TabsContent value="dashboard" className="mt-6">
            <VentasDashboard />
          </TabsContent>
          <TabsContent value="clientes" className="mt-6">
            {/* <ClientesTable /> */}
            <div className="text-center py-8">
              <p className="text-gray-600 dark:text-slate-400">Tabla de clientes aquí.</p>
            </div>
          </TabsContent>
          <TabsContent value="cotizaciones" className="mt-6">
            {/* <CotizacionesTable /> */}
            <div className="text-center py-8">
              <p className="text-gray-600 dark:text-slate-400">Tabla de cotizaciones aquí.</p>
            </div>
          </TabsContent>
          <TabsContent value="productos" className="mt-6">
            <div className="text-center py-8">
              <p className="text-gray-600 dark:text-slate-400">Vista de productos aquí.</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}