"use client"
import { cn } from "@/lib/utils"
import { useState } from "react"
import { InputField, Toggle, Segment, FileDrop } from "@/components/playground"

const ProjectForm = () => {
  const [urgent, setUrgent] = useState(false)
  const [type, setType] = useState<"calibration" | "delivery" | "training">("calibration")

  return (
    <form
      onSubmit={(e) => e.preventDefault()}
      className="max-w-xl mx-auto p-8 rounded-3xl bg-gray-200/20 dark:bg-slate-800/20 backdrop-blur-2xl space-y-6"
    >
      <h2 className="text-2xl font-light tracking-tight">Nuevo Proyecto</h2>

      <InputField label="Nombre del cliente" name="customer" required />
      <InputField label="Nombre del proyecto" name="project" required />
      <InputField label="Valor (USD)" name="value" type="number" required />

      <div>
        <label className="block mb-2 text-sm font-light">Tipo de servicio</label>
        <Segment
          options={[
            { value: "calibration", label: "Calibración" },
            { value: "delivery", label: "Entrega" },
            { value: "training", label: "Capacitación" },
          ]}
          value={type}
          onChange={setType}
        />
      </div>

      <div className="flex items-center justify-between">
        <span className="text-sm font-light">¿Es urgente?</span>
        <Toggle checked={urgent} onChange={setUrgent} />
      </div>

      <FileDrop onFiles={(files) => console.log(files)} />

      <button
        type="submit"
        className={cn(
          "w-full rounded-2xl bg-gradient-to-r from-moka-falu to-red-600 px-6 py-3 text-white font-light shadow-lg",
          "hover:shadow-xl hover:scale-[1.02] active:scale-[0.98] transition-all duration-200 animate-float"
        )}
      >
        Crear proyecto
      </button>
    </form>
  )
}

export default ProjectForm;