"use client"
import { useState, useRef } from "react"
import { UploadCloud } from "lucide-react"
import { cn } from "@/lib/utils"

export const FileDrop = ({ onFiles }: { onFiles: (files: File[]) => void }) => {
  const [hover, setHover] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    const files = Array.from(e.dataTransfer.files)
    onFiles(files)
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    onFiles(files)
  }

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  return (
    <div
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
      className={cn(
        "relative rounded-2xl border-2 border-dashed p-8 text-center transition-all duration-300 cursor-pointer",
        "border-gray-300/50 dark:border-gray-600/50 bg-glass-light dark:bg-glass-dark",
        (hover || isDragging) && "border-moka-falu dark:border-red-500 scale-[1.02]"
      )}
      onClick={openFileDialog}
    >
      <input
        ref={fileInputRef}
        type="file"
        multiple
        onChange={handleFileSelect}
        className="hidden"
      />
      <UploadCloud className="mx-auto mb-2 h-10 w-10 text-gray-400 dark:text-gray-500" />
      <p className="text-sm font-light">Arrastra archivos o</p>
      <button
        type="button"
        onClick={(e) => {
          e.stopPropagation()
          openFileDialog()
        }}
        className="text-sm font-semibold text-moka-falu dark:text-red-400 underline"
      >
        selecciona
      </button>
    </div>
  )
}