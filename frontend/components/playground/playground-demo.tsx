"use client"

import React, { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { 
  FileDrop, 
  InputField, 
  Toggle, 
  Segment, 
  ComponentShowcase,
  ModernComponentTemplate 
} from "@/components/playground"

export function PlaygroundDemo() {
  const [toggleValue, setToggleValue] = useState(false)
  const [segmentValue, setSegmentValue] = useState<"option1" | "option2" | "option3">("option1")
  const [files, setFiles] = useState<File[]>([])

  return (
    <div className="space-y-8 p-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-moka-falu to-moka-brown bg-clip-text text-transparent">
          🎨 Playground de Componentes
        </h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Experimenta con componentes modernos usando la paleta moka y efectos de cristal
        </p>
      </div>

      {/* Componentes Individuales */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* InputField Demo */}
        <ComponentShowcase
          title="Campo de Entrada"
          description="Input con efectos de cristal y animaciones"
          component={
            <div className="space-y-4">
              <InputField label="Nombre completo" placeholder="Ingresa tu nombre" />
              <InputField label="Email" type="email" placeholder="<EMAIL>" />
            </div>
          }
          code={`<InputField label="Nombre completo" placeholder="Ingresa tu nombre" />
<InputField label="Email" type="email" placeholder="<EMAIL>" />`}
        />

        {/* Toggle Demo */}
        <ComponentShowcase
          title="Interruptor"
          description="Toggle con gradientes moka"
          component={
            <div className="flex items-center gap-4">
              <span>Notificaciones:</span>
              <Toggle checked={toggleValue} onChange={setToggleValue} />
              <span className="text-sm text-muted-foreground">
                {toggleValue ? "Activadas" : "Desactivadas"}
              </span>
            </div>
          }
          code={`const [toggleValue, setToggleValue] = useState(false)

<Toggle checked={toggleValue} onChange={setToggleValue} />`}
        />

        {/* Segment Demo */}
        <ComponentShowcase
          title="Selector de Segmentos"
          description="Selector con múltiples opciones"
          component={
            <div className="space-y-2">
              <Segment
                options={[
                  { value: "option1", label: "Opción 1" },
                  { value: "option2", label: "Opción 2" },
                  { value: "option3", label: "Opción 3" },
                ]}
                value={segmentValue}
                onChange={setSegmentValue}
              />
              <p className="text-sm text-muted-foreground">
                Seleccionado: {segmentValue}
              </p>
            </div>
          }
          code={`<Segment
  options={[
    { value: "option1", label: "Opción 1" },
    { value: "option2", label: "Opción 2" },
    { value: "option3", label: "Opción 3" },
  ]}
  value={segmentValue}
  onChange={setSegmentValue}
/>`}
        />

        {/* FileDrop Demo */}
        <ComponentShowcase
          title="Arrastrar y Soltar"
          description="Componente para subir archivos"
          component={
            <div className="space-y-2">
              <FileDrop onFiles={(newFiles) => {
                setFiles(newFiles)
                console.log("Archivos seleccionados:", newFiles)
              }} />
              {files.length > 0 && (
                <div className="text-sm text-muted-foreground">
                  {files.length} archivo(s) seleccionado(s):
                  <ul className="list-disc list-inside mt-1">
                    {files.map((file, index) => (
                      <li key={index}>{file.name}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          }
          code={`<FileDrop onFiles={(files) => {
  console.log("Archivos seleccionados:", files)
}} />`}
        />
      </div>

      {/* Template Demo */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Templates de Componentes</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <ModernComponentTemplate 
            title="Template Default"
            description="Estilo por defecto"
            variant="default"
          />
          <ModernComponentTemplate 
            title="Template Moka"
            description="Con paleta moka"
            variant="moka"
          />
          <ModernComponentTemplate 
            title="Template Glass"
            description="Efecto de cristal"
            variant="glass"
          />
        </div>
      </div>

      {/* Paleta de Colores */}
      <Card>
        <CardHeader>
          <CardTitle>Paleta de Colores Moka</CardTitle>
          <CardDescription>Colores disponibles para usar en los componentes</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center space-y-2">
              <div className="w-full h-16 bg-moka-peach rounded-lg border"></div>
              <p className="text-sm font-medium">Peach</p>
              <code className="text-xs text-muted-foreground">#ffe6a7</code>
            </div>
            <div className="text-center space-y-2">
              <div className="w-full h-16 bg-moka-lion rounded-lg border"></div>
              <p className="text-sm font-medium">Lion</p>
              <code className="text-xs text-muted-foreground">#cc9f69</code>
            </div>
            <div className="text-center space-y-2">
              <div className="w-full h-16 bg-moka-brown rounded-lg border"></div>
              <p className="text-sm font-medium">Brown</p>
              <code className="text-xs text-muted-foreground">#a66030</code>
            </div>
            <div className="text-center space-y-2">
              <div className="w-full h-16 bg-moka-bistre rounded-lg border"></div>
              <p className="text-sm font-medium">Bistre</p>
              <code className="text-xs text-muted-foreground">#432818</code>
            </div>
            <div className="text-center space-y-2">
              <div className="w-full h-16 bg-moka-falu rounded-lg border"></div>
              <p className="text-sm font-medium">Falu</p>
              <code className="text-xs text-muted-foreground">#6f1d1b</code>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Botones de Prueba */}
      <Card>
        <CardHeader>
          <CardTitle>Botones con Animaciones</CardTitle>
          <CardDescription>Diferentes estilos de botones con efectos</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <Button className="bg-moka-falu hover:bg-moka-falu/90 animate-float">
              Float Animation
            </Button>
            <Button className="bg-moka-lion hover:bg-moka-lion/90 text-moka-bistre animate-pulse-glow">
              Pulse Glow
            </Button>
            <Button className="bg-gradient-to-r from-moka-falu to-moka-brown hover:scale-105 transition-transform">
              Gradient Hover
            </Button>
            <Button className="bg-moka-peach hover:bg-moka-peach/90 text-moka-bistre animate-fade-in">
              Fade In
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
