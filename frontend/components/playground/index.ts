// 🎨 Playground Components Index
// Exporta aquí todos tus componentes modernos de prueba

export { ModernComponentTemplate, ExampleUsage } from "./modern-component-template"
export { ComponentShowcase, ShowcaseExample } from "./component-showcase"
export { FileDrop } from "./FileDrop"
export { InputField } from "./InputField"
export { Toggle } from "./Toggle"
export { Segment } from "./Segment"
export { default as ProjectForm } from "./ProjectForm"
export { PlaygroundDemo } from "./playground-demo"

// Descomenta y agrega tus componentes según los vayas creando:
// export { ModernButton } from "./modern-button"
// export { ModernCard } from "./modern-card"
// export { ModernModal } from "./modern-modal"
// export { ModernTable } from "./modern-table"
// export { ModernForm } from "./modern-form"
// export { ModernChart } from "./modern-chart"
// export { ModernNavigation } from "./modern-navigation"
// export { ModernDashboard } from "./modern-dashboard"

// Tipos y interfaces compartidas
export interface PlaygroundComponentProps {
  className?: string
  variant?: "default" | "moka" | "glass" | "modern"
  size?: "sm" | "md" | "lg"
  children?: React.ReactNode
}

// Utilidades para el playground
export const playgroundUtils = {
  // Paleta moka para referencia rápida
  mokaColors: {
    peach: "#ffe6a7",
    lion: "#cc9f69", 
    brown: "#a66030",
    bistre: "#432818",
    falu: "#6f1d1b",
  },
  
  // Clases CSS comunes para componentes modernos
  commonClasses: {
    card: "rounded-lg border transition-all duration-300 hover:shadow-lg",
    cardMoka: "bg-moka-peach/20 border-moka-brown/40 text-moka-bistre",
    cardGlass: "bg-white/30 backdrop-blur-md border-white/20 dark:bg-slate-800/30 dark:border-slate-700/20",
    button: "transition-all duration-200 hover:scale-105",
    buttonMoka: "bg-moka-falu hover:bg-moka-falu/90 text-white",
  }
}
