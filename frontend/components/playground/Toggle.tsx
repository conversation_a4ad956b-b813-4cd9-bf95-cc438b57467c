"use client"
import { cn } from "@/lib/utils"

export const Toggle = ({ checked, onChange }: { checked: boolean; onChange: (c: boolean) => void }) => (
  <button
    type="button"
    onClick={() => onChange(!checked)}
    className={cn(
      "relative inline-flex h-7 w-14 items-center rounded-full transition-colors duration-300",
      checked ? "bg-gradient-to-r from-moka-falu to-red-600" : "bg-gray-300 dark:bg-gray-600"
    )}
  >
    <span
      className={cn(
        "inline-block h-5 w-5 rounded-full bg-white shadow-lg transition-transform duration-300",
        checked ? "translate-x-7" : "translate-x-1"
      )}
    />
  </button>
)