"use client"
import { forwardRef } from "react"
import { cn } from "@/lib/utils"

export const InputField = forwardRef<
  HTMLInputElement,
  React.InputHTMLAttributes<HTMLInputElement> & { label: string }
>(({ label, className, ...props }, ref) => (
  <div className="group relative mb-5">
    <input
      ref={ref}
      {...props}
      placeholder=" "
      className={cn(
        "peer block w-full rounded-2xl border bg-glass-light dark:bg-glass-dark backdrop-blur-xl px-4 py-3 text-sm",
        "border-gray-300/40 dark:border-gray-700/40",
        "focus:outline-none focus:ring-2 focus:ring-moka-falu/70 dark:focus:ring-red-500/70 focus:animate-pulse-glow",
        "transition-all duration-300 group-hover:scale-[1.01] group-hover:animate-float",
        className
      )}
    />
    <label
      className={cn(
        "absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 dark:text-gray-500",
        "transition-all duration-200 peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-sm",
        "peer-focus:top-0 peer-focus:text-xs peer-focus:text-moka-falu dark:peer-focus:text-red-400",
        "peer-not-placeholder-shown:top-0 peer-not-placeholder-shown:text-xs"
      )}
    >
      {label}
    </label>
    {/* floating glow on focus */}
    <div className="absolute inset-0 rounded-2xl opacity-0 peer-focus:opacity-100 animate-pulse-glow pointer-events-none" />
  </div>
))
InputField.displayName = "InputField"