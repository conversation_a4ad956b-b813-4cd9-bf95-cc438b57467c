"use client"

import React, { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Code, Copy, Eye } from "lucide-react"

interface ComponentShowcaseProps {
  title: string
  description: string
  component: React.ReactNode
  code?: string
  variants?: Array<{
    name: string
    component: React.ReactNode
    code?: string
  }>
}

export function ComponentShowcase({ 
  title, 
  description, 
  component, 
  code,
  variants = []
}: ComponentShowcaseProps) {
  const [showCode, setShowCode] = useState(false)
  const [activeVariant, setActiveVariant] = useState(0)

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // Aquí podrías agregar un toast de confirmación
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {title}
              <Badge variant="outline" className="text-xs">
                Playground
              </Badge>
            </CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setShowCode(!showCode)}
            >
              <Code className="h-4 w-4 mr-1" />
              {showCode ? "Vista" : "Código"}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="preview" className="w-full">
          <TabsList>
            <TabsTrigger value="preview">
              <Eye className="h-4 w-4 mr-1" />
              Vista Previa
            </TabsTrigger>
            {code && (
              <TabsTrigger value="code">
                <Code className="h-4 w-4 mr-1" />
                Código
              </TabsTrigger>
            )}
            {variants.length > 0 && (
              <TabsTrigger value="variants">
                Variantes ({variants.length})
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="preview" className="mt-4">
            <div className="p-6 border-2 border-dashed border-moka-brown/30 rounded-lg bg-moka-peach/5">
              {component}
            </div>
          </TabsContent>

          {code && (
            <TabsContent value="code" className="mt-4">
              <div className="relative">
                <Button
                  size="sm"
                  variant="outline"
                  className="absolute top-2 right-2 z-10"
                  onClick={() => copyToClipboard(code)}
                >
                  <Copy className="h-4 w-4" />
                </Button>
                <pre className="bg-moka-bistre/10 p-4 rounded-lg overflow-x-auto text-sm">
                  <code>{code}</code>
                </pre>
              </div>
            </TabsContent>
          )}

          {variants.length > 0 && (
            <TabsContent value="variants" className="mt-4">
              <div className="space-y-4">
                <div className="flex gap-2 flex-wrap">
                  {variants.map((variant, index) => (
                    <Button
                      key={index}
                      size="sm"
                      variant={activeVariant === index ? "default" : "outline"}
                      onClick={() => setActiveVariant(index)}
                    >
                      {variant.name}
                    </Button>
                  ))}
                </div>
                
                <div className="p-6 border-2 border-dashed border-moka-brown/30 rounded-lg bg-moka-peach/5">
                  {variants[activeVariant]?.component}
                </div>
                
                {variants[activeVariant]?.code && (
                  <div className="relative">
                    <Button
                      size="sm"
                      variant="outline"
                      className="absolute top-2 right-2 z-10"
                      onClick={() => copyToClipboard(variants[activeVariant].code || "")}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                    <pre className="bg-moka-bistre/10 p-4 rounded-lg overflow-x-auto text-sm">
                      <code>{variants[activeVariant].code}</code>
                    </pre>
                  </div>
                )}
              </div>
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
    </Card>
  )
}

// Ejemplo de uso del ComponentShowcase
export function ShowcaseExample() {
  const exampleComponent = (
    <Button className="bg-moka-falu hover:bg-moka-falu/90">
      Botón de Ejemplo
    </Button>
  )

  const exampleCode = `<Button className="bg-moka-falu hover:bg-moka-falu/90">
  Botón de Ejemplo
</Button>`

  const variants = [
    {
      name: "Default",
      component: <Button>Default Button</Button>,
      code: `<Button>Default Button</Button>`
    },
    {
      name: "Moka",
      component: <Button className="bg-moka-lion hover:bg-moka-lion/90 text-moka-bistre">Moka Button</Button>,
      code: `<Button className="bg-moka-lion hover:bg-moka-lion/90 text-moka-bistre">Moka Button</Button>`
    }
  ]

  return (
    <ComponentShowcase
      title="Botón Moderno"
      description="Ejemplo de cómo documentar un componente con variantes"
      component={exampleComponent}
      code={exampleCode}
      variants={variants}
    />
  )
}
