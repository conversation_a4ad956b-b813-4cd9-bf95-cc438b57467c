
"use client"
import { cn } from "@/lib/utils"

export const Segment = <T extends string>({
  options,
  value,
  onChange,
}: {
  options: { value: T; label: string }[]
  value: T
  onChange: (v: T) => void
}) => (
  <div className="flex rounded-2xl bg-gray-200/30 dark:bg-gray-800/30 p-1 backdrop-blur-xl">
    {options.map((opt) => (
      <button
        key={opt.value}
        onClick={() => onChange(opt.value)}
        className={cn(
          "flex-1 rounded-xl px-3 py-1.5 text-sm font-medium transition-all duration-200",
          value === opt.value
            ? "bg-gradient-to-r from-moka-falu to-red-600 text-white shadow-md"
            : "text-gray-600 dark:text-gray-300 hover:bg-gray-300/40 dark:hover:bg-gray-700/40"
        )}
      >
        {opt.label}
      </button>
    ))}
  </div>
)