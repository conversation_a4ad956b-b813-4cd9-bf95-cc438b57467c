"use client"

import React from "react"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface ModernComponentProps {
  title?: string
  description?: string
  variant?: "default" | "moka" | "glass"
}

export function ModernComponentTemplate({ 
  title = "Componente Moderno",
  description = "Descripción del componente",
  variant = "default"
}: ModernComponentProps) {
  
  const getVariantStyles = () => {
    switch (variant) {
      case "moka":
        return "bg-moka-peach/20 border-moka-brown/40 text-moka-bistre"
      case "glass":
        return "bg-white/30 backdrop-blur-md border-white/20 dark:bg-slate-800/30 dark:border-slate-700/20"
      default:
        return "bg-background border-border"
    }
  }

  return (
    <Card className={`transition-all duration-300 hover:shadow-lg ${getVariantStyles()}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          <Badge className="bg-moka-falu text-white">Nuevo</Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Este es un template para crear componentes modernos. 
            Puedes personalizar los estilos y funcionalidad según necesites.
          </p>
          
          <div className="flex gap-2">
            <Button size="sm" className="bg-moka-lion hover:bg-moka-lion/90 text-moka-bistre">
              Acción 1
            </Button>
            <Button size="sm" variant="outline">
              Acción 2
            </Button>
          </div>
          
          {/* Área para tu contenido personalizado */}
          <div className="mt-4 p-4 border-2 border-dashed border-moka-brown/30 rounded-lg">
            <p className="text-center text-moka-brown text-sm">
              Área de contenido personalizable
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Ejemplo de uso:
export function ExampleUsage() {
  return (
    <div className="space-y-4">
      <ModernComponentTemplate 
        title="Componente Default"
        description="Usando estilos por defecto"
        variant="default"
      />
      
      <ModernComponentTemplate 
        title="Componente Moka"
        description="Usando la paleta moka"
        variant="moka"
      />
      
      <ModernComponentTemplate 
        title="Componente Glass"
        description="Con efecto de cristal"
        variant="glass"
      />
    </div>
  )
}
