"use client";

import React, { useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";

const quickClientSchema = z.object({
  commercialName: z.string().min(2, "Nombre comercial requerido"),
  rfc: z.string()
    .min(12, "RFC inválido")
    .max(13, "RFC inválido")
    .regex(/^[A-Z&Ñ]{3,4}\d{6}[A-Z\d]{3}$/, "El RFC no tiene un formato válido"),
  contactPhones: z.array(z.string().min(7, "Teléfono requerido")).min(1, "Al menos un teléfono"),
  contactEmails: z.array(z.string().email("Email inválido")).min(1, "Al menos un email"),
  contactName: z.string().optional(),
});

export type QuickClientFormData = {
  commercialName: string;
  rfc: string;
  contactPhones: { value: string }[];
  contactEmails: { value: string }[];
  contactName?: string;
};

export const QuickClientForm: React.FC<{
  onSubmit: (data: QuickClientFormData) => Promise<void> | void;
  defaultValues?: Partial<QuickClientFormData>;
  loading?: boolean;
}> = ({ onSubmit, defaultValues, loading }) => {
  const [tipoPersona, setTipoPersona] = React.useState<'moral' | 'fisica'>('moral');
  const form = useForm<QuickClientFormData>({
    resolver: zodResolver(quickClientSchema),
    defaultValues: {
      contactPhones: [{ value: '' }],
      contactEmails: [{ value: '' }],
      ...defaultValues,
    },
  });
  console.log('[DEBUG][QuickClientForm] Valores iniciales:', defaultValues);

  const phones = useFieldArray({ control: form.control, name: 'contactPhones' });
  const emails = useFieldArray({ control: form.control, name: 'contactEmails' });

  useEffect(() => {
    if (phones.fields.length === 0) phones.append({ value: '' });
    if (emails.fields.length === 0) emails.append({ value: '' });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit((data) => {
        console.log('[DEBUG][QuickClientForm] Payload enviado:', data);
        onSubmit(data);
      })} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <FormLabel>Tipo de persona *</FormLabel>
            <Select value={tipoPersona} onValueChange={v => setTipoPersona(v as 'moral' | 'fisica')}>
              <SelectTrigger>
                <SelectValue placeholder="Selecciona tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="moral">Persona Moral</SelectItem>
                <SelectItem value="fisica">Persona Física</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <FormField control={form.control} name="commercialName" render={({ field }) => (
            <FormItem>
              <FormLabel>Nombre comercial *</FormLabel>
              <FormControl><Input {...field} /></FormControl>
              <FormMessage />
            </FormItem>
          )} />
          <FormField control={form.control} name="rfc" render={({ field }) => (
            <FormItem>
              <FormLabel>RFC *</FormLabel>
              <FormControl>
                <Input {...field} placeholder={tipoPersona === 'moral' ? 'Ejemplo: ABC0102031A2' : 'Ejemplo: ABCD0102031A2'} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )} />
          <FormField control={form.control} name="contactName" render={({ field }) => (
            <FormItem>
              <FormLabel>Nombre de contacto</FormLabel>
              <FormControl><Input {...field} /></FormControl>
              <FormMessage />
            </FormItem>
          )} />
        </div>
        <div>
          <FormLabel>Teléfonos *</FormLabel>
          {phones.fields.map((field, idx) => (
            <div key={field.id} className="flex gap-2 items-center mb-2">
              <Input
                {...form.register(`contactPhones.${idx}.value` as const)}
                placeholder="Teléfono de contacto"
                type="tel"
                autoComplete="tel"
                className="flex-1"
              />
              <Button type="button" variant="ghost" onClick={() => phones.remove(idx)} disabled={phones.fields.length === 1}>-</Button>
              {idx === phones.fields.length - 1 && (
                <Button type="button" variant="ghost" onClick={() => phones.append({ value: '' })}>+</Button>
              )}
            </div>
          ))}
          <FormMessage>{form.formState.errors.contactPhones?.message as string}</FormMessage>
        </div>
        <div>
          <FormLabel>Emails *</FormLabel>
          {emails.fields.map((field, idx) => (
            <div key={field.id} className="flex gap-2 items-center mb-2">
              <Input
                {...form.register(`contactEmails.${idx}.value` as const)}
                placeholder="Email de contacto"
                type="email"
                autoComplete="email"
                className="flex-1"
              />
              <Button type="button" variant="ghost" onClick={() => emails.remove(idx)} disabled={emails.fields.length === 1}>-</Button>
              {idx === emails.fields.length - 1 && (
                <Button type="button" variant="ghost" onClick={() => emails.append({ value: '' })}>+</Button>
              )}
            </div>
          ))}
          <FormMessage>{form.formState.errors.contactEmails?.message as string}</FormMessage>
        </div>
        <div className="flex justify-end gap-2">
          <Button type="submit" disabled={loading}>{loading ? "Guardando..." : "Guardar cliente"}</Button>
        </div>
      </form>
    </Form>
  );
};

export default QuickClientForm; 