import React from "react";
import { useClientQuotations } from "../../hooks/use-client";

interface ClientQuotationsTableProps {
  clientId: string | number;
}

export const ClientQuotationsTable: React.FC<ClientQuotationsTableProps> = ({ clientId }) => {
  const { data, isLoading, error, refetch } = useClientQuotations(clientId);

  if (isLoading) return <div className="py-4 text-center">Cargando cotizaciones...</div>;
  if (error) return (
    <div className="py-4 text-center text-red-500">
      Error al cargar cotizaciones
      <button className="ml-2 underline" onClick={() => refetch()}>Reintentar</button>
    </div>
  );
  if (!data || data.length === 0) return <div className="py-4 text-center text-muted-foreground">Sin cotizaciones registradas</div>;

  return (
    <div className="overflow-x-auto mt-8">
      <h2 className="text-lg font-semibold mb-2"><PERSON><PERSON> de Cotizaciones</h2>
      <table className="min-w-full border text-sm">
        <thead>
          <tr className="bg-muted">
            <th className="px-3 py-2 border">Folio</th>
            <th className="px-3 py-2 border">Fecha</th>
            <th className="px-3 py-2 border">Total</th>
            <th className="px-3 py-2 border">Estatus</th>
            <th className="px-3 py-2 border">Acciones</th>
          </tr>
        </thead>
        <tbody>
          {data.map((q: any) => (
            <tr key={q.id} className="hover:bg-accent">
              <td className="px-3 py-2 border">{q.folio || q.id}</td>
              <td className="px-3 py-2 border">{q.date ? new Date(q.date).toLocaleDateString() : '-'}</td>
              <td className="px-3 py-2 border">{q.total ? `$${q.total.toFixed(2)}` : '-'}</td>
              <td className="px-3 py-2 border">{q.status || '-'}</td>
              <td className="px-3 py-2 border">
                {/* Aquí puedes agregar botón de ver/descargar/editar si aplica */}
                <button className="btn btn-xs btn-outline">Ver</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ClientQuotationsTable; 