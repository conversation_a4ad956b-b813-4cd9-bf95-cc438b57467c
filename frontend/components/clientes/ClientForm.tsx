"use client";

import React, { useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { toast } from "@/hooks/use-toast";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";

// Esquema Zod para validación
const clientSchema = z.object({
  commercialName: z.string().min(2, 'El nombre comercial es obligatorio'),
  legalName: z.string().optional(),
  rfc: z.string().min(12, 'RFC inválido').max(13, 'RFC inválido'),
  taxRegime: z.string().optional(),
  street: z.string().optional(),
  neighborhood: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zipCode: z.string().optional(),
  country: z.string().optional(),
  contactName: z.string().optional(),
  contactPosition: z.string().optional(),
  contactPhones: z.array(z.string()).optional().default([]),
  contactEmails: z.array(z.string().email('Email inválido')).optional().default([]),
  industry: z.string().optional(),
  companyArea: z.string().optional(),
  companySize: z.string().optional(),
  clientType: z.enum(['Prospecto', 'Cliente', 'Frecuente'], { required_error: 'El tipo de cliente es obligatorio' }),
  creditLimit: z.coerce.number().optional(),
  creditDays: z.coerce.number().optional(),
  preferredPayment: z.string().optional(),
  website: z.string().optional(),
  observations: z.string().optional(),
  assignedSalespersonId: z.coerce.number().optional(),
  status: z.string().optional(),
});

type ClientFormData = z.infer<typeof clientSchema>;

// Define el tipo de payload para el backend
export type ClientPayload = Omit<ClientFormData, 'contactPhones' | 'contactEmails'> & {
  contactPhones: string[];
  contactEmails: string[];
};

export function ClientForm({ onSubmit, loading, initialValues }: any) {
  const [tipoPersona, setTipoPersona] = React.useState<'moral' | 'fisica'>('moral');
  // Normalizar initialValues para que los arrays sean string[]
  const normalizedInitialValues: ClientFormData = {
    ...(initialValues || {}),
    contactPhones: Array.isArray(initialValues?.contactPhones)
      ? (initialValues.contactPhones as any[]).map((p) => typeof p === 'string' ? p : (p?.value ?? ''))
      : [],
    contactEmails: Array.isArray(initialValues?.contactEmails)
      ? (initialValues.contactEmails as any[]).map((e) => typeof e === 'string' ? e : (e?.value ?? ''))
      : [],
  };

  const form = useForm<ClientFormData>({
    resolver: zodResolver(clientSchema),
    defaultValues: normalizedInitialValues,
  });

  const phones = useFieldArray<any>({ control: form.control, name: 'contactPhones' });
  const emails = useFieldArray<any>({ control: form.control, name: 'contactEmails' });

  useEffect(() => {
    if (phones.fields.length === 0) phones.append("");
    if (emails.fields.length === 0) emails.append("");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSubmit = (data: ClientFormData) => {
    const payload = {
      ...data,
      contactPhones: (data.contactPhones || []).filter(Boolean),
      contactEmails: (data.contactEmails || []).filter(Boolean),
      creditLimit: data.creditLimit ? Number(data.creditLimit) : 0,
      creditDays: data.creditDays ? Number(data.creditDays) : 0,
      assignedSalespersonId: data.assignedSalespersonId ? Number(data.assignedSalespersonId) : undefined,
    };
    onSubmit(payload);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <Card>
          <CardHeader>Datos Generales</CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <FormLabel>Tipo de persona *</FormLabel>
              <Select value={tipoPersona} onValueChange={v => setTipoPersona(v as 'moral' | 'fisica')}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="moral">Persona Moral</SelectItem>
                  <SelectItem value="fisica">Persona Física</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <FormField control={form.control} name="commercialName" render={({ field }) => (
              <FormItem>
                <FormLabel>Nombre comercial *</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <FormField control={form.control} name="legalName" render={({ field }) => (
              <FormItem>
                <FormLabel>Razón social *</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <FormField control={form.control} name="rfc" render={({ field }) => (
              <FormItem>
                <FormLabel>RFC *</FormLabel>
                <FormControl>
                  <Input {...field} placeholder={tipoPersona === 'moral' ? 'Ejemplo: ABC0102031A2' : 'Ejemplo: ABCD0102031A2'} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <FormField control={form.control} name="taxRegime" render={({ field }) => (
              <FormItem>
                <FormLabel>Régimen fiscal *</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
          </CardContent>
        </Card>
        <Card>
          <CardHeader>Dirección Fiscal</CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField control={form.control} name="street" render={({ field }) => (
              <FormItem>
                <FormLabel>Calle *</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <FormField control={form.control} name="neighborhood" render={({ field }) => (
              <FormItem>
                <FormLabel>Colonia *</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <FormField control={form.control} name="city" render={({ field }) => (
              <FormItem>
                <FormLabel>Ciudad *</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <FormField control={form.control} name="state" render={({ field }) => (
              <FormItem>
                <FormLabel>Estado *</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <FormField control={form.control} name="zipCode" render={({ field }) => (
              <FormItem>
                <FormLabel>Código postal *</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <FormField control={form.control} name="country" render={({ field }) => (
              <FormItem>
                <FormLabel>País</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
          </CardContent>
        </Card>
        <Card>
          <CardHeader>Contacto Principal</CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField control={form.control} name="contactName" render={({ field }) => (
              <FormItem>
                <FormLabel>Nombre de contacto *</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <FormField control={form.control} name="contactPosition" render={({ field }) => (
              <FormItem>
                <FormLabel>Puesto</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            {/* Teléfonos dinámicos */}
            <div className="col-span-2">
              <FormLabel>Teléfonos *</FormLabel>
              {phones.fields.map((field, idx) => (
                <div key={field.id} className="flex gap-2 mb-2">
                  <Input {...form.register(`contactPhones.${idx}` as const)} placeholder="Teléfono" value={form.watch(`contactPhones.${idx}`) || ''} />
                  <Button type="button" variant="ghost" onClick={() => phones.remove(idx)} disabled={phones.fields.length === 1}>-</Button>
                  {idx === phones.fields.length - 1 && (
                    <Button type="button" variant="ghost" onClick={() => phones.append("")}>+</Button>
                  )}
                </div>
              ))}
              <FormMessage>{form.formState.errors.contactPhones?.message as string}</FormMessage>
            </div>
            {/* Emails dinámicos */}
            <div className="col-span-2">
              <FormLabel>Emails *</FormLabel>
              {emails.fields.map((field, idx) => (
                <div key={field.id} className="flex gap-2 mb-2">
                  <Input {...form.register(`contactEmails.${idx}` as const)} placeholder="Email" value={form.watch(`contactEmails.${idx}`) || ''} />
                  <Button type="button" variant="ghost" onClick={() => emails.remove(idx)} disabled={emails.fields.length === 1}>-</Button>
                  {idx === emails.fields.length - 1 && (
                    <Button type="button" variant="ghost" onClick={() => emails.append("")}>+</Button>
                  )}
                </div>
              ))}
              <FormMessage>{form.formState.errors.contactEmails?.message as string}</FormMessage>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>Datos Comerciales</CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField control={form.control} name="industry" render={({ field }) => (
              <FormItem>
                <FormLabel>Giro</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <FormField control={form.control} name="companyArea" render={({ field }) => (
              <FormItem>
                <FormLabel>Área empresa</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <FormField control={form.control} name="companySize" render={({ field }) => (
              <FormItem>
                <FormLabel>Tamaño empresa</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <FormField control={form.control} name="clientType" render={({ field }) => (
              <FormItem>
                <FormLabel>Tipo de cliente *</FormLabel>
                <FormControl>
                  <Select value={field.value} onValueChange={field.onChange} defaultValue={field.value}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecciona tipo" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Prospecto">Prospecto</SelectItem>
                      <SelectItem value="Cliente">Cliente</SelectItem>
                      <SelectItem value="Frecuente">Frecuente</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <FormField control={form.control} name="creditLimit" render={({ field }) => (
              <FormItem>
                <FormLabel>Límite de crédito</FormLabel>
                <FormControl><Input type="number" {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <FormField control={form.control} name="creditDays" render={({ field }) => (
              <FormItem>
                <FormLabel>Días de crédito</FormLabel>
                <FormControl><Input type="number" {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <FormField control={form.control} name="preferredPayment" render={({ field }) => (
              <FormItem>
                <FormLabel>Forma de pago preferida</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <FormField control={form.control} name="website" render={({ field }) => (
              <FormItem>
                <FormLabel>Sitio web</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <FormField control={form.control} name="observations" render={({ field }) => (
              <FormItem>
                <FormLabel>Observaciones</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
          </CardContent>
        </Card>
        <div className="flex justify-end gap-2">
          <Button type="submit" disabled={loading}>{loading ? "Guardando..." : "Guardar cliente"}</Button>
        </div>
      </form>
    </Form>
  );
}

export default ClientForm; 