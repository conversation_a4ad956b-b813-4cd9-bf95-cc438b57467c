// Ejemplo de cómo usar el ResponsiveAutoGrid en tu proyecto

import { ResponsiveAutoGrid, ResponsiveGridItem, FluidText, ProjectGrid } from '@/components/ui/responsive-grid'

// 1. Grid básico para cards de estadísticas
export function StatsGrid() {
  const stats = [
    { label: "Proyectos Activos", value: "24", color: 0 },
    { label: "Clientes", value: "12", color: 1 },
    { label: "Tareas Pendientes", value: "156", color: 2 },
    { label: "Completadas", value: "89%", color: 3 }
  ]

  return (
    <ResponsiveAutoGrid minItemWidth={200} gap="md" className="mb-8">
      {stats.map((stat, index) => (
        <ResponsiveGridItem 
          key={stat.label}
          colorIndex={stat.color}
          className="p-6 text-center space-y-2"
        >
          <FluidText as="h3" size="xl" className="font-bold">
            {stat.value}
          </FluidText>
          <FluidText as="p" size="sm" className="opacity-80">
            {stat.label}
          </FluidText>
        </ResponsiveGridItem>
      ))}
    </ResponsiveAutoGrid>
  )
}

// 2. Grid para productos del almacén
export function AlmacenGrid() {
  const productos = [
    { id: "1", nombre: "Laptop Dell", categoria: "Hardware", stock: 15 },
    { id: "2", nombre: "Monitor Samsung", categoria: "Periféricos", stock: 8 },
    { id: "3", nombre: "Teclado Mecánico", categoria: "Periféricos", stock: 25 },
    { id: "4", nombre: "Mouse Inalámbrico", categoria: "Periféricos", stock: 30 },
    { id: "5", nombre: "Impresora HP", categoria: "Hardware", stock: 5 },
    { id: "6", nombre: "Cables HDMI", categoria: "Accesorios", stock: 50 }
  ]

  return (
    <div className="space-y-6">
      <FluidText as="h2" size="xl" className="font-medium">
        Inventario de Almacén
      </FluidText>
      
      <ResponsiveAutoGrid 
        minItemWidth={280} 
        gap="lg"
        aspectRatio="auto"
      >
        {productos.map((producto, index) => (
          <ResponsiveGridItem 
            key={producto.id}
            colorIndex={index}
            className="p-6 space-y-4 min-h-[140px]"
          >
            <div className="flex justify-between items-start">
              <FluidText as="h3" size="lg" className="font-medium">
                {producto.nombre}
              </FluidText>
              <span className={`px-2 py-1 rounded-full text-xs ${
                producto.stock > 20 ? 'bg-green-100 text-green-800' :
                producto.stock > 10 ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {producto.stock} unidades
              </span>
            </div>
            
            <FluidText as="p" size="sm" className="opacity-70">
              {producto.categoria}
            </FluidText>
            
            <div className="flex justify-between items-center pt-2">
              <button className="px-4 py-2 bg-moka-falu text-white rounded-lg text-sm hover:bg-moka-falu/90 transition-colors">
                Ver Detalles
              </button>
              <button className="px-4 py-2 border border-current rounded-lg text-sm hover:bg-current hover:text-white transition-colors">
                Editar
              </button>
            </div>
          </ResponsiveGridItem>
        ))}
      </ResponsiveAutoGrid>
    </div>
  )
}

// 3. Grid para sistemas/servicios
export function SistemasGrid() {
  const sistemas = [
    { id: "1", nombre: "Sistema ERP", estado: "Activo", version: "v2.1.0" },
    { id: "2", nombre: "CRM Ventas", estado: "Mantenimiento", version: "v1.8.5" },
    { id: "3", nombre: "Portal Web", estado: "Activo", version: "v3.0.2" },
    { id: "4", nombre: "API Gateway", estado: "Activo", version: "v1.5.1" }
  ]

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <FluidText as="h2" size="xl" className="font-medium">
          Sistemas en Producción
        </FluidText>
        <button className="px-4 py-2 bg-moka-falu text-white rounded-lg hover:bg-moka-falu/90 transition-colors">
          Nuevo Sistema
        </button>
      </div>
      
      <ResponsiveAutoGrid 
        minItemWidth={300} 
        gap="md"
        className="auto-rows-fr" // Hace que todas las filas tengan la misma altura
      >
        {sistemas.map((sistema, index) => (
          <ResponsiveGridItem 
            key={sistema.id}
            colorIndex={index}
            className="p-6 space-y-4 flex flex-col justify-between"
          >
            <div>
              <FluidText as="h3" size="lg" className="font-medium mb-2">
                {sistema.nombre}
              </FluidText>
              <FluidText as="p" size="sm" className="opacity-70">
                Versión {sistema.version}
              </FluidText>
            </div>
            
            <div className="flex justify-between items-center">
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                sistema.estado === 'Activo' 
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                  : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
              }`}>
                {sistema.estado}
              </span>
              
              <div className="flex gap-2">
                <button className="p-2 hover:bg-white/20 rounded-lg transition-colors">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </button>
                <button className="p-2 hover:bg-white/20 rounded-lg transition-colors">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                  </svg>
                </button>
              </div>
            </div>
          </ResponsiveGridItem>
        ))}
      </ResponsiveAutoGrid>
    </div>
  )
}

// 4. Uso con tus proyectos existentes (adaptado a tu estructura actual)
export function DashboardProjectsWithAutoGrid() {
  // Simulando la estructura de tus proyectos actuales
  const projects = [
    { id: "1", title: "Sistema ERP", company: "COMINTEC", status: "En Progreso", progress: 75 },
    { id: "2", title: "Portal Web", company: "Cliente A", status: "Completado", progress: 100 },
    { id: "3", title: "App Móvil", company: "Cliente B", status: "Iniciado", progress: 25 },
    { id: "4", title: "Dashboard", company: "Cliente C", status: "En Progreso", progress: 60 }
  ]

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <FluidText as="h2" size="xl" className="font-medium">
          Proyectos Activos
        </FluidText>
        <FluidText as="p" size="sm" className="opacity-70">
          {projects.length} proyectos en total
        </FluidText>
      </div>
      
      <ProjectGrid projects={projects} />
    </div>
  )
}
