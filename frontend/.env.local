# Frontend Configuration - Supabase
# Copia este contenido a frontend/.env.local

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://awlxzhrubqkryrenunun.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF3bHh6aHJ1YnFrcnlyZW51bnVuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA0NzAzOTIsImV4cCI6MjA2NjA0NjM5Mn0.0LCZRkXz88rQwh_kEqA7wysZpj2GQ-NX5qPEYaJN-po

# API Configuration
NEXT_PUBLIC_API_URL=http://192.168.100.40:3000/api

# Environment
NODE_ENV=development

# Otros servicios (opcional)
# NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id
# NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn
PORT=3001
