"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogTrigger } from "@/components/ui/dialog"
import {
  ChevronLeft,
  ChevronRight,
  FileText,
  Truck,
  Wrench,
  FlaskConical,
  GraduationCap,
  UserCheck,
  ClipboardCheck,
  Quote,
  Package,
  File,
  FileSpreadsheet,
  ArrowRight,
} from "lucide-react"
import { cn } from "@/lib/utils"

// Datos específicos de COMINTEC
interface Project {
  id: number
  customer: string
  project: string
  value: number
  status: "pending" | "in_progress" | "completed" | "cancelled"
  department: string
  date: string
  progress: number
  responsible: string
  contact: {
    name: string
    email: string
    phone: string
  }
  address: string
  description: string
  timeline: {
    id: number
    date: string
    title: string
    description: string
    status: "completed" | "current" | "pending"
  }[]
  documents: {
    id: number
    name: string
    type: string
    date: string
    size: string
    fileType: "pdf" | "docx" | "xlsx"
    url: string
  }[]
}

interface CalendarEvent {
  id: number
  title: string
  date: string
  time: string
  department: string
  type: "delivery" | "installation" | "calibration" | "training" | "meeting" | "inspection" | "quotation" | "inventory"
  relatedProject?: number
  responsible: string
  status: "scheduled" | "confirmed" | "pending" | "completed" | "cancelled" | "draft"
  description?: string
}

// Datos de proyectos de COMINTEC
const projectsData: Project[] = [
  {
    id: 1,
    customer: "Metro de Lima",
    project: "Sistema de Medición Línea 5",
    value: 850000,
    status: "in_progress",
    department: "logistics",
    date: "2024-03-15",
    progress: 75,
    responsible: "Carlos Ruiz",
    contact: {
      name: "Juan Pérez",
      email: "<EMAIL>",
      phone: "+51 1 234-5678",
    },
    address: "Av. Paseo de la República 123, Lima",
    description:
      "Implementación completa del sistema de medición automatizado para la nueva línea 5 del metro, incluyendo sensores de última generación, software de monitoreo en tiempo real y capacitación especializada del personal técnico.",
    timeline: [
      {
        id: 1,
        date: "2024-03-15",
        title: "Propuesta enviada",
        description: "Envío de propuesta técnica y comercial detallada",
        status: "completed",
      },
      {
        id: 2,
        date: "2024-03-20",
        title: "Aprobación cliente",
        description: "Cliente aprueba propuesta y firma contrato",
        status: "completed",
      },
      {
        id: 3,
        date: "2024-04-01",
        title: "Inicio proyecto",
        description: "Reunión de inicio y planificación detallada",
        status: "completed",
      },
      {
        id: 4,
        date: "2024-05-15",
        title: "Entrega equipos",
        description: "Entrega e instalación de equipos principales",
        status: "current",
      },
      {
        id: 5,
        date: "2024-06-01",
        title: "Capacitación",
        description: "Capacitación del personal técnico",
        status: "pending",
      },
      {
        id: 6,
        date: "2024-06-15",
        title: "Cierre proyecto",
        description: "Entrega final y cierre del proyecto",
        status: "pending",
      },
    ],
    documents: [
      {
        id: 1,
        name: "Contrato_Metro_Lima.pdf",
        type: "Contrato",
        date: "2024-03-20",
        size: "2.5 MB",
        fileType: "pdf",
        url: "/documents/contrato_metro_lima.pdf",
      },
      {
        id: 2,
        name: "Especificaciones_Tecnicas.pdf",
        type: "Especificaciones",
        date: "2024-03-15",
        size: "1.8 MB",
        fileType: "pdf",
        url: "/documents/especificaciones_tecnicas.pdf",
      },
      {
        id: 3,
        name: "Cronograma_Proyecto.xlsx",
        type: "Cronograma",
        date: "2024-04-01",
        size: "456 KB",
        fileType: "xlsx",
        url: "/documents/cronograma_proyecto.xlsx",
      },
    ],
  },
  {
    id: 2,
    customer: "Hospital Nacional",
    project: "Calibración Equipos Médicos",
    value: 125000,
    status: "pending",
    department: "metrology",
    date: "2024-04-08",
    progress: 30,
    responsible: "Diana Torres",
    contact: {
      name: "María González",
      email: "<EMAIL>",
      phone: "+51 1 987-6543",
    },
    address: "Av. Grau 456, Lima",
    description:
      "Servicio especializado de calibración para equipos médicos críticos incluyendo monitores de signos vitales, ventiladores mecánicos y equipos de imagen diagnóstica.",
    timeline: [
      {
        id: 1,
        date: "2024-04-08",
        title: "Solicitud recibida",
        description: "Recepción de solicitud de calibración",
        status: "completed",
      },
      {
        id: 2,
        date: "2024-04-10",
        title: "Evaluación técnica",
        description: "Evaluación inicial de equipos",
        status: "current",
      },
      {
        id: 3,
        date: "2024-04-20",
        title: "Cronograma definido",
        description: "Definición de cronograma de calibración",
        status: "pending",
      },
    ],
    documents: [
      {
        id: 1,
        name: "Solicitud_Calibracion.pdf",
        type: "Solicitud",
        date: "2024-04-08",
        size: "1.2 MB",
        fileType: "pdf",
        url: "/documents/solicitud_calibracion.pdf",
      },
    ],
  },
]

const calendarEvents: CalendarEvent[] = [
  {
    id: 1,
    title: "Entrega Proyecto Metro Línea 5",
    date: "2024-07-15",
    time: "09:00",
    department: "logistics",
    type: "delivery",
    relatedProject: 1,
    responsible: "Carlos Ruiz",
    status: "scheduled",
    description: "Entrega e instalación de equipos principales del sistema de medición",
  },
  {
    id: 2,
    title: "Calibración Hospital Nacional",
    date: "2024-07-18",
    time: "14:00",
    department: "metrology",
    type: "calibration",
    relatedProject: 2,
    responsible: "Diana Torres",
    status: "confirmed",
    description: "Calibración de equipos médicos críticos",
  },
]

// Configuración de departamentos COMINTEC
const departmentColors = {
  sales: "bg-blue-500",
  admin: "bg-green-500",
  warehouse: "bg-orange-500",
  logistics: "bg-purple-500",
  quality: "bg-red-500",
  metrology: "bg-cyan-500",
  service: "bg-yellow-500",
  hr: "bg-pink-500",
  systems: "bg-indigo-500",
}

const departmentNames = {
  sales: "Ventas",
  admin: "Administración",
  warehouse: "Almacén",
  logistics: "Logística",
  quality: "Calidad",
  metrology: "Metrología",
  service: "Servicio",
  hr: "Recursos Humanos",
  systems: "Sistemas",
}

const statusColors = {
  pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/40 dark:text-yellow-200",
  in_progress: "bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-200",
  completed: "bg-green-100 text-green-800 dark:bg-green-900/40 dark:text-green-200",
  cancelled: "bg-red-100 text-red-800 dark:bg-red-900/40 dark:text-red-200",
}

const statusNames = {
  pending: "Pendiente",
  in_progress: "En Progreso",
  completed: "Completado",
  cancelled: "Cancelado",
}

const eventTypeIcons = {
  delivery: Truck,
  installation: Wrench,
  calibration: FlaskConical,
  training: GraduationCap,
  meeting: UserCheck,
  inspection: ClipboardCheck,
  quotation: Quote,
  inventory: Package,
}

const eventTypeNames = {
  delivery: "Entrega",
  installation: "Instalación",
  calibration: "Calibración",
  training: "Capacitación",
  meeting: "Reunión",
  inspection: "Inspección",
  quotation: "Cotización",
  inventory: "Inventario",
}

const eventStatusColors = {
  scheduled: "bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-200",
  confirmed: "bg-green-100 text-green-800 dark:bg-green-900/40 dark:text-green-200",
  pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/40 dark:text-yellow-200",
  completed: "bg-gray-100 text-gray-800 dark:bg-gray-800/40 dark:text-gray-200",
  cancelled: "bg-red-100 text-red-800 dark:bg-red-900/40 dark:text-red-200",
  draft: "bg-orange-100 text-orange-800 dark:bg-orange-900/40 dark:text-orange-200",
}

const eventStatusNames = {
  scheduled: "Programado",
  confirmed: "Confirmado",
  pending: "Pendiente",
  completed: "Completado",
  cancelled: "Cancelado",
  draft: "Borrador",
}

const fileTypeColors = {
  pdf: "text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-900/30",
  docx: "text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/30",
  xlsx: "text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/30",
}

const fileTypeIcons = {
  pdf: FileText,
  docx: File,
  xlsx: FileSpreadsheet,
}

const MONTHS = [
  "Enero",
  "Febrero",
  "Marzo",
  "Abril",
  "Mayo",
  "Junio",
  "Julio",
  "Agosto",
  "Septiembre",
  "Octubre",
  "Noviembre",
  "Diciembre",
]

const WEEKDAYS = ["Dom", "Lun", "Mar", "Mié", "Jue", "Vie", "Sáb"]

export default function SalesDashboard() {
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [departmentFilter, setDepartmentFilter] = useState("all")
  const [currentDate, setCurrentDate] = useState(new Date())
  const [calendarDepartmentFilter, setCalendarDepartmentFilter] = useState("all")
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)

  const filteredProjects = projectsData.filter((project) => {
    const matchesSearch =
      project.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.project.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.responsible.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || project.status === statusFilter
    const matchesDepartment = departmentFilter === "all" || project.department === departmentFilter

    return matchesSearch && matchesStatus && matchesDepartment
  })

  const handleDocumentAction = (doc: Project["documents"][0], action: "open" | "download") => {
    if (action === "open") {
      window.open(doc.url, "_blank")
    } else {
      const link = document.createElement("a")
      link.href = doc.url
      link.download = doc.name
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const startDate = new Date(firstDay)
    startDate.setDate(startDate.getDate() - firstDay.getDay())

    const days = []
    const current = new Date(startDate)

    for (let i = 0; i < 42; i++) {
      days.push(new Date(current))
      current.setDate(current.getDate() + 1)
    }

    return days
  }

  const getEventsForDate = (date: Date) => {
    const dateString = date.toISOString().split("T")[0]
    return calendarEvents.filter((event) => {
      const eventDate = event.date
      const matchesDate = eventDate === dateString
      const matchesDepartment = calendarDepartmentFilter === "all" || event.department === calendarDepartmentFilter
      return matchesDate && matchesDepartment
    })
  }

  const navigateMonth = (direction: "prev" | "next") => {
    const newDate = new Date(currentDate)
    if (direction === "prev") {
      newDate.setMonth(newDate.getMonth() - 1)
    } else {
      newDate.setMonth(newDate.getMonth() + 1)
    }
    setCurrentDate(newDate)
  }

  const isToday = (date: Date) => {
    const today = new Date()
    return date.toDateString() === today.toDateString()
  }

  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentDate.getMonth()
  }

  const selectedDateEvents = selectedDate ? getEventsForDate(selectedDate) : []

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-light mb-4 tracking-wide text-gray-900 dark:text-slate-100">Dashboard COMINTEC</h1>
        <p className="font-light text-lg text-gray-600 dark:text-slate-400">Sistema Integral de Gestión Empresarial</p>
      </div>

      {/* Projects Section */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-light tracking-wide text-gray-900 dark:text-slate-100">Proyectos Activos</h2>
          <div className="flex items-center gap-4">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filtrar por estado" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los estados</SelectItem>
                <SelectItem value="pending">Pendiente</SelectItem>
                <SelectItem value="in_progress">En Progreso</SelectItem>
                <SelectItem value="completed">Completado</SelectItem>
                <SelectItem value="cancelled">Cancelado</SelectItem>
              </SelectContent>
            </Select>
            <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filtrar departamento" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los departamentos</SelectItem>
                {Object.entries(departmentNames).map(([key, name]) => (
                  <SelectItem key={key} value={key}>
                    <div className="flex items-center gap-2">
                      <div
                        className={cn("w-3 h-3 rounded-full", departmentColors[key as keyof typeof departmentColors])}
                      />
                      {name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map((project) => (
            <Dialog key={project.id}>
              <DialogTrigger asChild>
                <Card className="cursor-pointer transition-all duration-300 hover:shadow-lg">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div
                          className={cn(
                            "w-3 h-3 rounded-full",
                            departmentColors[project.department as keyof typeof departmentColors],
                          )}
                        />
                        <Badge className={cn("text-xs", statusColors[project.status])}>
                          {statusNames[project.status]}
                        </Badge>
                      </div>
                      <ArrowRight className="w-4 h-4 transition-transform duration-200 group-hover:translate-x-1" />
                    </div>
                    <CardTitle className="text-lg font-light">{project.customer}</CardTitle>
                    <p className="text-sm text-muted-foreground">{project.project}</p>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Valor</span>
                        <span className="text-lg font-light text-green-600">${project.value.toLocaleString()}</span>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Progreso</span>
                          <span className="text-sm">{project.progress}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${project.progress}%` }}
                          />
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Responsable</span>
                        <span>{project.responsible}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </DialogTrigger>
              {/* Project Detail Modal would go here */}
            </Dialog>
          ))}
        </div>
      </div>

      {/* Calendar Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl font-light">Calendario de Actividades</CardTitle>
            <div className="flex items-center gap-4">
              <Select value={calendarDepartmentFilter} onValueChange={setCalendarDepartmentFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Filtrar departamento" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los departamentos</SelectItem>
                  {Object.entries(departmentNames).map(([key, name]) => (
                    <SelectItem key={key} value={key}>
                      <div className="flex items-center gap-2">
                        <div
                          className={cn("w-3 h-3 rounded-full", departmentColors[key as keyof typeof departmentColors])}
                        />
                        {name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="sm" onClick={() => navigateMonth("prev")}>
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <h3 className="font-light text-lg">
                  {MONTHS[currentDate.getMonth()]} {currentDate.getFullYear()}
                </h3>
                <Button variant="ghost" size="sm" onClick={() => navigateMonth("next")}>
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-7 gap-1 mb-4">
            {WEEKDAYS.map((day) => (
              <div key={day} className="p-3 text-center text-sm font-medium text-muted-foreground">
                {day}
              </div>
            ))}
            {getDaysInMonth(currentDate).map((date, index) => {
              const dayEvents = getEventsForDate(date)
              const isCurrentMonthDate = isCurrentMonth(date)
              const isTodayDate = isToday(date)

              return (
                <div
                  key={index}
                  className={cn(
                    "p-2 min-h-20 border rounded cursor-pointer transition-colors",
                    !isCurrentMonthDate && "bg-muted/20 text-muted-foreground",
                    isTodayDate && "bg-blue-50 border-blue-200",
                    selectedDate?.toDateString() === date.toDateString() && "bg-blue-100 border-blue-300",
                  )}
                  onClick={() => setSelectedDate(date)}
                >
                  <div className="text-sm font-medium mb-1">{date.getDate()}</div>
                  <div className="space-y-1">
                    {dayEvents.slice(0, 2).map((event) => {
                      const IconComponent = eventTypeIcons[event.type]
                      return (
                        <div
                          key={event.id}
                          className={cn(
                            "text-xs p-1 rounded text-white truncate",
                            departmentColors[event.department as keyof typeof departmentColors],
                          )}
                          title={event.title}
                        >
                          <div className="flex items-center gap-1">
                            <IconComponent className="w-3 h-3" />
                            <span className="truncate">{event.title}</span>
                          </div>
                        </div>
                      )
                    })}
                    {dayEvents.length > 2 && (
                      <div className="text-xs text-center text-muted-foreground">+{dayEvents.length - 2} más</div>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
