import { useEffect } from 'react';

console.log('[WS][HOOK] useWebSocket hook ejecutado');

export function useWebSocket() {
  // ... existing code ...

  useEffect(() => {
    console.log('[WS][HOOK] useEffect de useWebSocket disparado');
    // Obtener token y userId
    const token = localStorage.getItem('auth_token');
    const userRaw = localStorage.getItem('user');
    let userId = null;
    if (userRaw) {
      try {
        const user = JSON.parse(userRaw);
        userId = user.id || user.userId;
      } catch (e) {
        console.warn('[WS][HOOK] Error parseando user:', e);
      }
    }
    console.log('[WS][HOOK] Llamando a wsManager.connect con:', token, userId);
    // Aquí deberías tener la llamada real:
    // wsManager.connect(token, userId);
    // ...resto del código...
  }, []);

  // ... existing code ...
} 