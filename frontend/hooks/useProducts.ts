import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { 
  productService, 
  Product, 
  ProductFilters, 
  CreateProductData, 
  UpdateProductData, 
  StockMovement,
  InventoryStats 
} from '@/lib/services/product'
import { PaginatedResponse } from '@/lib/api'

// Query keys
export const productKeys = {
  all: ['products'] as const,
  lists: () => [...productKeys.all, 'list'] as const,
  list: (filters: ProductFilters) => [...productKeys.lists(), filters] as const,
  details: () => [...productKeys.all, 'detail'] as const,
  detail: (id: number) => [...productKeys.details(), id] as const,
  byCode: (code: string) => [...productKeys.all, 'by-code', code] as const,
  lowStock: () => [...productKeys.all, 'low-stock'] as const,
  stats: () => [...productKeys.all, 'stats'] as const,
}

// Main products list hook
export function useProducts(filters?: ProductFilters) {
  return useQuery({
    queryKey: productKeys.list(filters || {}),
    queryFn: () => productService.getAllProducts(filters),
    select: (response) => response.success ? response.data : null,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

// Single product by ID
export function useProduct(id: number) {
  return useQuery({
    queryKey: productKeys.detail(id),
    queryFn: () => productService.getProductById(id),
    select: (response) => response.success ? response.data : null,
    enabled: !!id,
  })
}

// Product by code
export function useProductByCode(code: string) {
  return useQuery({
    queryKey: productKeys.byCode(code),
    queryFn: () => productService.getProductByCode(code),
    select: (response) => response.success ? response.data : null,
    enabled: !!code,
  })
}

// Low stock products
export function useLowStockProducts() {
  return useQuery({
    queryKey: productKeys.lowStock(),
    queryFn: () => productService.getLowStockProducts(),
    select: (response) => response.success ? response.data : [],
    staleTime: 1000 * 60 * 2, // 2 minutes
  })
}

// Inventory statistics
export function useInventoryStats() {
  return useQuery({
    queryKey: productKeys.stats(),
    queryFn: () => productService.getInventoryStats(),
    select: (response) => response.success ? response.data : null,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

// Create product mutation
export function useCreateProduct() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: CreateProductData) => productService.createProduct(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: productKeys.lists() })
      queryClient.invalidateQueries({ queryKey: productKeys.stats() })
    },
  })
}

// Update product mutation
export function useUpdateProduct() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateProductData }) => 
      productService.updateProduct(id, data),
    onSuccess: (response, { id }) => {
      queryClient.invalidateQueries({ queryKey: productKeys.lists() })
      queryClient.invalidateQueries({ queryKey: productKeys.detail(id) })
      queryClient.invalidateQueries({ queryKey: productKeys.stats() })
    },
  })
}

// Delete product mutation
export function useDeleteProduct() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (id: number) => productService.deleteProduct(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: productKeys.lists() })
      queryClient.invalidateQueries({ queryKey: productKeys.stats() })
    },
  })
}

// Reserve stock mutation
export function useReserveStock() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, cantidad }: { id: number; cantidad: number }) => 
      productService.reserveStock(id, cantidad),
    onSuccess: (response, { id }) => {
      queryClient.invalidateQueries({ queryKey: productKeys.lists() })
      queryClient.invalidateQueries({ queryKey: productKeys.detail(id) })
      queryClient.invalidateQueries({ queryKey: productKeys.stats() })
    },
  })
}

// Adjust stock mutation
export function useAdjustStock() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, movement }: { id: number; movement: StockMovement }) => 
      productService.adjustStock(id, movement),
    onSuccess: (response, { id }) => {
      queryClient.invalidateQueries({ queryKey: productKeys.lists() })
      queryClient.invalidateQueries({ queryKey: productKeys.detail(id) })
      queryClient.invalidateQueries({ queryKey: productKeys.stats() })
      queryClient.invalidateQueries({ queryKey: productKeys.lowStock() })
    },
  })
} 