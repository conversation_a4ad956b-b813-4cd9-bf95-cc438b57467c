"use client"

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { toast } from "sonner"
import { roleService, Role, Permission, CreateRoleDto, UpdateRoleDto, PermissionsByModule } from "../lib/services/role"
import { useRoleNotifications } from "@/components/ui/notification-provider"

// Query Keys
export const roleKeys = {
  all: ['roles'] as const,
  lists: () => [...roleKeys.all, 'list'] as const,
  list: (filters?: any) => [...roleKeys.lists(), { filters }] as const,
  details: () => [...roleKeys.all, 'detail'] as const,
  detail: (id: number) => [...roleKeys.details(), id] as const,
  permissions: () => ['permissions'] as const,
  permissionsByModule: () => [...roleKeys.permissions(), 'by-module'] as const,
}

// ============================================================================
// QUERIES
// ============================================================================

/**
 * Hook para obtener todos los roles
 */
export function useRoles() {
  return useQuery({
    queryKey: roleKeys.lists(),
    queryFn: async () => {
      const response = await roleService.getRoles()
      if (!response.success) {
        throw new Error(response.message || 'Error al obtener roles')
      }
      return response.data
    },
  })
}

/**
 * Hook para obtener un rol específico por ID
 */
export function useRole(id: number, enabled: boolean = true) {
  return useQuery({
    queryKey: roleKeys.detail(id),
    queryFn: async () => {
      const response = await roleService.getRole(id)
      if (!response.success) {
        throw new Error(response.message || 'Error al obtener rol')
      }
      return response.data
    },
    enabled: enabled && !!id,
  })
}

/**
 * Hook para obtener todos los permisos disponibles
 */
export function usePermissions() {
  return useQuery({
    queryKey: roleKeys.permissions(),
    queryFn: async () => {
      const response = await roleService.getPermissions()
      if (!response.success) {
        throw new Error(response.message || 'Error al obtener permisos')
      }
      return response.data
    },
  })
}

/**
 * Hook para obtener permisos agrupados por módulo
 */
export function usePermissionsByModule() {
  return useQuery({
    queryKey: roleKeys.permissionsByModule(),
    queryFn: async () => {
      const response = await roleService.getPermissionsByModule()
      if (!response.success) {
        throw new Error(response.message || 'Error al obtener permisos por módulo')
      }
      return response.data
    },
  })
}

// ============================================================================
// MUTATIONS
// ============================================================================

/**
 * Hook para crear un nuevo rol
 */
export function useCreateRole() {
  const queryClient = useQueryClient()
  const { notifyRoleCreated, notifyRoleError } = useRoleNotifications()

  return useMutation({
    mutationFn: async (dto: CreateRoleDto) => {
      const response = await roleService.createRole(dto)
      if (!response.success) {
        throw new Error(response.message || 'Error al crear rol')
      }
      return response.data
    },
    onSuccess: (data) => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: roleKeys.lists() })

      // Usar notificación animada
      notifyRoleCreated(data?.name || 'nuevo rol', `El rol "${data?.name || 'nuevo rol'}" ha sido creado correctamente.`)
    },
    onError: (error: Error) => {
      notifyRoleError('nuevo rol', error.message)
    },
  })
}

/**
 * Hook para actualizar un rol existente
 */
export function useUpdateRole() {
  const queryClient = useQueryClient()
  const { notifyRoleUpdated, notifyRoleError } = useRoleNotifications()

  return useMutation({
    mutationFn: async ({ id, dto }: { id: number; dto: UpdateRoleDto }) => {
      const response = await roleService.updateRole(id, dto)
      if (!response.success) {
        throw new Error(response.message || 'Error al actualizar rol')
      }
      return response.data
    },
    onSuccess: (data, variables) => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: roleKeys.lists() })
      queryClient.invalidateQueries({ queryKey: roleKeys.detail(variables.id) })

      // Usar notificación animada
      notifyRoleUpdated(data?.name || 'rol', `El rol "${data?.name || 'rol'}" ha sido actualizado correctamente.`)
    },
    onError: (error: Error) => {
      notifyRoleError('rol', error.message)
    },
  })
}

/**
 * Hook para eliminar un rol
 */
export function useDeleteRole() {
  const queryClient = useQueryClient()
  const { notifyRoleDeleted, notifyRoleError } = useRoleNotifications()

  return useMutation({
    mutationFn: async ({ id, roleName }: { id: number; roleName: string }) => {
      const response = await roleService.deleteRole(id)
      if (!response.success) {
        throw new Error(response.message || 'Error al eliminar rol')
      }
      return { ...response.data, roleName }
    },
    onSuccess: (data, variables) => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: roleKeys.lists() })
      queryClient.removeQueries({ queryKey: roleKeys.detail(variables.id) })

      // Usar notificación animada
      notifyRoleDeleted(variables.roleName, `El rol "${variables.roleName}" ha sido eliminado permanentemente del sistema.`)
    },
    onError: (error: Error, variables) => {
      console.error('[useDeleteRole] Error details:', error)

      // Manejo específico para diferentes tipos de errores
      let errorDescription = error.message

      if (error.message.includes('user(s) still have this role')) {
        errorDescription = 'Este rol está asignado a uno o más usuarios. Primero debes reasignar o eliminar esos usuarios.'
      } else if (error.message.includes('HTTP 500')) {
        errorDescription = 'Ocurrió un error interno. Por favor, inténtalo de nuevo o contacta al administrador.'
      }

      // Usar notificación animada para errores
      notifyRoleError(variables.roleName, errorDescription)
    },
  })
}

/**
 * Hook para asignar permisos a un rol
 */
export function useAssignPermissions() {
  const queryClient = useQueryClient()
  const { notifyPermissionsAssigned, notifyRoleError } = useRoleNotifications()

  return useMutation({
    mutationFn: async ({ roleId, permissions }: { roleId: number; permissions: string[] }) => {
      const response = await roleService.assignPermissionsToRole(roleId, permissions)
      if (!response.success) {
        throw new Error(response.message || 'Error al asignar permisos')
      }
      return response.data
    },
    onSuccess: (data, variables) => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: roleKeys.lists() })
      queryClient.invalidateQueries({ queryKey: roleKeys.detail(variables.roleId) })

      // Usar notificación animada
      notifyPermissionsAssigned(data?.name || 'rol', `Los permisos han sido asignados al rol "${data?.name || 'rol'}" correctamente.`)
    },
    onError: (error: Error) => {
      notifyRoleError('rol', error.message)
    },
  })
} 