"use client"

import { useState, useEffect } from "react"
import { apiClient, type ApiResponse } from "@/lib/api"

export function useApi<T>(
  endpoint: string,
  options: {
    immediate?: boolean
    dependencies?: any[]
  } = {},
) {
  const { immediate = true, dependencies = [] } = options
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Simular respuesta
  const execute = async () => {
    setLoading(false)
    setError(null)
    setData(null)
  }

  useEffect(() => {
    if (immediate) {
      execute()
    }
  }, dependencies)

  const refetch = () => {
    execute()
  }

  return {
    data,
    loading,
    error,
    refetch,
    execute,
  }
}

export function useMutation<T, P = any>(mutationFn: (params: P) => Promise<ApiResponse<T>>) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Simular mutación
  const mutate = async (params: P): Promise<T | null> => {
    setLoading(false)
    setError(null)
    return null
  }

  return {
    mutate,
    loading,
    error,
  }
}
