"use client"

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { 
  userService, 
  User, 
  CreateUserData, 
  UpdateUserData, 
  ChangePasswordData,
  AdminChangePasswordData,
  UserFilters 
} from '@/lib/services/user'

// Keys para React Query
export const userKeys = {
  all: ['users'] as const,
  lists: () => [...userKeys.all, 'list'] as const,
  list: (filters: UserFilters) => [...userKeys.lists(), filters] as const,
  details: () => [...userKeys.all, 'detail'] as const,
  detail: (id: number) => [...userKeys.details(), id] as const,
  roles: ['roles'] as const,
  areas: ['areas'] as const,
}

// Hook para obtener usuarios con filtros
export function useUsers(filters: UserFilters = {}) {
  return useQuery({
    queryKey: userKeys.list(filters),
    queryFn: () => userService.getUsers(filters),
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
  })
}

// Hook para obtener un usuario específico
export function useUser(id: number) {
  return useQuery({
    queryKey: userKeys.detail(id),
    queryFn: () => userService.getUser(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  })
}

// Hook para obtener roles
export function useRoles() {
  return useQuery({
    queryKey: userKeys.roles,
    queryFn: () => userService.getRoles(),
    staleTime: 10 * 60 * 1000, // Los roles cambian menos frecuentemente
  })
}

// Hook para obtener áreas
export function useAreas() {
  return useQuery({
    queryKey: userKeys.areas,
    queryFn: () => userService.getAreas(),
    staleTime: 10 * 60 * 1000,
  })
}

// Hook para crear usuario
export function useCreateUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateUserData) => userService.createUser(data),
    onSuccess: (newUser) => {
      // Invalidar listas de usuarios
      queryClient.invalidateQueries({ queryKey: userKeys.lists() })

      // Agregar usuario al cache
      queryClient.setQueryData(userKeys.detail(newUser.id), newUser)

      toast.success('Usuario creado exitosamente')
    },
    onError: (error: Error) => {
      toast.error('Error al crear usuario', {
        description: error.message
      })
    }
  })
}

// Hook para activar usuario
export function useActivateUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: number) => userService.activateUser(id),
    onSuccess: (updatedUser) => {
      // Invalidar listas de usuarios
      queryClient.invalidateQueries({ queryKey: userKeys.lists() })

      // Actualizar usuario en cache
      queryClient.setQueryData(userKeys.detail(updatedUser.id), updatedUser)

      toast.success('Usuario activado exitosamente')
    },
    onError: (error: Error) => {
      toast.error('Error al activar usuario', {
        description: error.message
      })
    }
  })
}

// Hook para desactivar usuario
export function useDeactivateUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: number) => userService.deactivateUser(id),
    onSuccess: (updatedUser) => {
      // Invalidar listas de usuarios
      queryClient.invalidateQueries({ queryKey: userKeys.lists() })

      // Actualizar usuario en cache
      queryClient.setQueryData(userKeys.detail(updatedUser.id), updatedUser)

      toast.success('Usuario desactivado exitosamente')
    },
    onError: (error: Error) => {
      toast.error('Error al desactivar usuario', {
        description: error.message
      })
    }
  })
}

// Hook para cambiar estado del usuario
export function useUpdateUserStatus() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, status }: { id: number; status: 'activo' | 'inactivo' | 'bloqueado' }) =>
      userService.updateUserStatus(id, status),
    onSuccess: (updatedUser) => {
      // Invalidar listas de usuarios
      queryClient.invalidateQueries({ queryKey: userKeys.lists() })

      // Actualizar usuario en cache
      queryClient.setQueryData(userKeys.detail(updatedUser.id), updatedUser)

      toast.success('Estado del usuario actualizado exitosamente')
    },
    onError: (error: Error) => {
      toast.error('Error al actualizar estado del usuario', {
        description: error.message
      })
    }
  })
}

// Hook para actualizar usuario
export function useUpdateUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateUserData }) => 
      userService.updateUser(id, data),
    onSuccess: (updatedUser, { id }) => {
      // Invalidar listas de usuarios
      queryClient.invalidateQueries({ queryKey: userKeys.lists() })
      
      // Actualizar cache del usuario específico
      queryClient.setQueryData(userKeys.detail(id), updatedUser)
      
      toast.success('Usuario actualizado exitosamente')
    },
    onError: (error: Error) => {
      toast.error('Error al actualizar usuario', {
        description: error.message
      })
    }
  })
}

// Hook para eliminar usuario
export function useDeleteUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: number) => userService.deleteUser(id),
    onSuccess: (_, id) => {
      // Invalidar listas de usuarios
      queryClient.invalidateQueries({ queryKey: userKeys.lists() })

      // Remover del cache
      queryClient.removeQueries({ queryKey: userKeys.detail(id) })

      toast.success('Usuario eliminado exitosamente')
    },
    onError: (error: Error) => {
      toast.error('Error al eliminar usuario', {
        description: error.message
      })
    }
  })
}

// Hook para cambio de contraseña por admin
export function useAdminChangePassword() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ userId, newPassword }: { userId: number; newPassword: string }) =>
      userService.adminChangePassword(userId, newPassword),
    onSuccess: (updatedUser) => {
      // Actualizar usuario en cache
      queryClient.setQueryData(userKeys.detail(updatedUser.id), updatedUser)

      toast.success('Contraseña cambiada exitosamente')
    },
    onError: (error: Error) => {
      toast.error('Error al cambiar contraseña', {
        description: error.message
      })
    }
  })
}

// Hook para cambiar contraseña
export function useChangePassword() {
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: ChangePasswordData }) => 
      userService.changePassword(id, data),
    onSuccess: () => {
      toast.success('Contraseña cambiada exitosamente')
    },
    onError: (error: Error) => {
      toast.error('Error al cambiar contraseña', {
        description: error.message
      })
    }
  })
}

// Hook para asignar roles
export function useAssignRoles() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ userId, roleIds }: { userId: number; roleIds: number[] }) => 
      userService.assignRoles(userId, roleIds),
    onSuccess: (updatedUser, { userId }) => {
      // Invalidar listas de usuarios
      queryClient.invalidateQueries({ queryKey: userKeys.lists() })
      
      // Actualizar cache del usuario específico
      queryClient.setQueryData(userKeys.detail(userId), updatedUser)
      
      toast.success('Roles asignados exitosamente')
    },
    onError: (error: Error) => {
      toast.error('Error al asignar roles', {
        description: error.message
      })
    }
  })
} 