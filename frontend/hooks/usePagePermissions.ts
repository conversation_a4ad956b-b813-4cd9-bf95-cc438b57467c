import { useAuth } from "@/components/providers/auth-provider"

interface UsePagePermissionsProps {
  requiredPermissions?: string[]
  requiredRoles?: string[] // Mantenemos por compatibilidad pero no lo usaremos
}

export function usePagePermissions({ 
  requiredPermissions = [], 
  requiredRoles = [] // Mantenemos por compatibilidad
}: UsePagePermissionsProps = {}) {
  const { hasPermission, hasRole } = useAuth()

  // Si el usuario tiene ROLE_ADMIN, dar acceso completo
  if (hasRole("ROLE_ADMIN")) {
    return { hasAccess: true, isAdmin: true }
  }

  // Si no hay permisos requeridos, permitir acceso
  if (requiredPermissions.length === 0) {
    return { hasAccess: true, isAdmin: false }
  }

  // Verificar permisos específicos - necesita al menos uno
  const hasRequiredPermissions = requiredPermissions.some((permission) => hasPermission(permission))

  // Ya no verificamos roles, solo permisos
  const hasAccess = hasRequiredPermissions

  return { 
    hasAccess, 
    isAdmin: false,
    missingPermissions: requiredPermissions.filter(p => !hasPermission(p)),
    missingRoles: [] // Ya no es relevante
  }
} 