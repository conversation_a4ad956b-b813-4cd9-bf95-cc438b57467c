"use client"

import { useState, useEffect } from 'react'
import { apiClient } from '@/lib/api'

interface DashboardStats {
  totalUsers: number
  totalProducts: number
  totalCustomers: number
  totalInventoryItems: number
  totalSales: number
  alerts: number
  urgentAlerts: number
}

export function useDashboardStats() {
  // Simulación de datos para desconexión del backend
  const [stats, setStats] = useState<DashboardStats | null>({
    totalUsers: 10,
    totalProducts: 25,
    totalCustomers: 5,
    totalInventoryItems: 100,
    totalSales: 12,
    alerts: 2,
    urgentAlerts: 1,
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // No fetch real
  const fetchStats = async () => {
    setLoading(false)
    setError(null)
    setStats({
      totalUsers: 10,
      totalProducts: 25,
      totalCustomers: 5,
      totalInventoryItems: 100,
      totalSales: 12,
      alerts: 2,
      urgentAlerts: 1,
    })
  }

  useEffect(() => {
    // No fetch real
  }, [])

  return {
    stats,
    loading,
    error,
    refetch: fetchStats
  }
}