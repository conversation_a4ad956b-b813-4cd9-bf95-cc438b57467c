import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { clientService, getClientDocuments } from "../lib/services/client";
import { toast } from "../components/ui/use-toast";

export function useClients(filters: any = {}) {
  return useQuery({
    queryKey: ["clients", filters],
    queryFn: () => clientService.getClients(filters),
    staleTime: 5 * 60 * 1000,
  });
}

export function useClient(id: string | number) {
  return useQuery({
    queryKey: ["client", id],
    queryFn: () => clientService.getClient(id),
    enabled: !!id,
  });
}

export function useCreateClient() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: any) => clientService.createClient(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["clients"] });
      toast({ title: "Cliente creado correctamente", variant: "default" });
    },
    onError: (error: any) => {
      toast({ title: "Error al crear cliente", description: error.message, variant: "destructive" });
    },
  });
}

export function useUpdateClient() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: string | number; data: any }) => clientService.updateClient(id, data),
    onSuccess: (updated) => {
      queryClient.setQueryData(["client", updated.id], updated);
      queryClient.invalidateQueries({ queryKey: ["clients"] });
      toast({ title: "Cliente actualizado correctamente", variant: "default" });
    },
    onError: (error: any) => {
      toast({ title: "Error al actualizar cliente", description: error.message, variant: "destructive" });
    },
  });
}

export function useDeleteClient() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: string | number) => clientService.deleteClient(id),
    onSuccess: (_, deletedId) => {
      queryClient.removeQueries({ queryKey: ["client", deletedId] });
      queryClient.invalidateQueries({ queryKey: ["clients"] });
      toast({ title: "Cliente eliminado correctamente", variant: "default" });
    },
    onError: (error: any) => {
      toast({ title: "Error al eliminar cliente", description: error.message, variant: "destructive" });
    },
  });
}

export function useClientQuotations(clientId: string | number) {
  return useQuery({
    queryKey: ["client-quotations", clientId],
    queryFn: () => clientService.getQuotationsByClientId(clientId),
    enabled: !!clientId,
    staleTime: 2 * 60 * 1000,
  });
}

export function useClientPurchases(clientId: string | number) {
  return useQuery({
    queryKey: ["client-purchases", clientId],
    queryFn: () => clientService.getClientPurchases(clientId),
    enabled: !!clientId,
    staleTime: 2 * 60 * 1000,
  });
}

export function useClientTopProducts(clientId: string | number) {
  return useQuery({
    queryKey: ["client-top-products", clientId],
    queryFn: () => clientService.getClientTopProducts(clientId),
    enabled: !!clientId,
    staleTime: 2 * 60 * 1000,
  });
}

export function useClientDocuments(clientId: string | number) {
  const isValid = !!clientId && clientId !== 'undefined' && clientId !== '' && clientId !== '__invalid__';
  return useQuery(
    ['client-documents', clientId],
    isValid ? () => getClientDocuments(clientId) : undefined,
    // @ts-expect-error: react-query types allow options as second argument
    { enabled: isValid }
  );
}

export function useUploadClientDocument(clientId: string | number) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (formData: FormData) => clientService.uploadClientDocument(clientId, formData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["client-documents", clientId] });
      toast({ title: "Documento subido correctamente", variant: "default" });
    },
    onError: (error: any) => {
      toast({ title: "Error al subir documento", description: error.message, variant: "destructive" });
    },
  });
}

export function useDeleteClientDocument(clientId: string | number) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (docId: string | number) => clientService.deleteClientDocument(clientId, docId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["client-documents", clientId] });
      toast({ title: "Documento eliminado correctamente", variant: "default" });
    },
    onError: (error: any) => {
      toast({ title: "Error al eliminar documento", description: error.message, variant: "destructive" });
    },
  });
}

export function useClientKpis(clientId: string | number) {
  return useQuery({
    queryKey: ["client-kpis", clientId],
    queryFn: () => clientService.getClientKpis(clientId),
    enabled: !!clientId,
    staleTime: 2 * 60 * 1000,
  });
} 