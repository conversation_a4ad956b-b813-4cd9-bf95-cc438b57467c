"use client"

import { useEffect, useState } from "react"
import { io, Socket } from "socket.io-client"
import authService from "@/lib/auth"

export interface NotificationData {
  id: string
  type: "info" | "warning" | "error" | "success"
  title: string
  message: string
  timestamp: string
  user_id?: string
  role_id?: string
  priority: "low" | "medium" | "high"
  status: "unread" | "read"
  action_url?: string
  metadata?: Record<string, any>
}

export interface AuthenticatedSocket extends Socket {
  auth: {
    token: string
    userId: string
  }
}

class NotificationWebSocketManager {
  private socket: Socket | null = null
  private isConnected = false
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private listeners: ((notification: NotificationData) => void)[] = []
  private connectionListeners: ((connected: boolean) => void)[] = []
  private statsListeners: ((stats: { unread: number; total: number }) => void)[] = []
  private isAuthenticated = false

  constructor() {
    if (typeof window !== 'undefined') {
      // Escuchar confirmación de notificación leída desde el backend
      this.onNotificationRead = this.onNotificationRead.bind(this);
    }
  }

  async connect(token: string, userId: string) {
    // Refrescar token si está expirado antes de conectar
    let validToken = token;
    try {
      if (!authService.isAuthenticated()) {
        const refreshed = await authService.refreshToken();
        if (!refreshed) {
          console.warn('[WS][CONNECT] No se pudo refrescar el token, cerrando sesión');
          authService.clearAuth();
          this.disconnect();
          return;
        }
        validToken = refreshed;
      }
    } catch (e) {
      console.error('[WS][CONNECT] Error refrescando token:', e);
      authService.clearAuth();
      this.disconnect();
      return;
    }
    console.log('[WS][CONNECT] Intentando conectar con token:', validToken, 'userId:', userId);
    if (this.socket) {
      if (this.socket.connected) {
        console.log('[WS][CONNECT] Socket ya conectado, enviando autenticación nuevamente');
        this.socket.emit('authenticate', { token: validToken, userId: userId.toString() });
        return;
      } else {
        console.log('[WS][CONNECT] Socket existente pero desconectado, reintentando conexión');
        try {
          this.socket.auth = { token: validToken, userId } as any;
          this.socket.connect();
        } catch (e) {
          console.warn('[WS][CONNECT] Error reconectando socket existente, creando nuevo:', e);
          this.socket = null;
        }
      }
    }
    try {
      // Configurar Socket.IO para conectar con nuestro backend
      const socketUrl = process.env.NODE_ENV === "production" 
        ? "wss://your-backend-url.com" 
        : "http://localhost:3000"
      this.socket = io(socketUrl, {
        auth: {
          token: validToken,
          userId
        },
        transports: ['websocket', 'polling'],
        timeout: 20000,
        autoConnect: true
      })
      this.setupEventListeners(validToken, userId)
    } catch (error) {
      console.error("Error connecting to Socket.IO:", error)
      this.handleReconnect(validToken, userId)
    }
  }

  private setupEventListeners(token: string, userId: string) {
    if (!this.socket) return

    this.socket.on('connect', () => {
      console.log('[WS][CONNECTED] Conectado al WebSocket. Socket ID:', this.socket?.id);
      this.isConnected = true
      this.reconnectAttempts = 0
      this.isAuthenticated = false
      
      // Autenticar el socket
      this.socket?.emit('authenticate', { token, userId: userId.toString() })
      
      this.notifyConnectionListeners(true)
    })

    this.socket.on('disconnect', (reason: string) => {
      console.warn('[WS][DISCONNECT] Desconectado del WebSocket. Razón:', reason);
      this.isConnected = false
      this.isAuthenticated = false
      this.notifyConnectionListeners(false)
    })

    this.socket.on('connect_error', (err: any) => {
      console.error('[WS][ERROR] Error de conexión:', err);
    })

    // Eventos de notificaciones
    this.socket.on('new_notification_alert', (notif: any) => {
      console.log('[WS][NOTIF] Notificación recibida (raw):', notif);
      this.notifyListeners(notif)
    })

    this.socket.on('pending_notifications_alert', (data: { unread: number; total: number }) => {
      console.log('📊 Estadísticas de notificaciones:', data)
      this.notifyStatsListeners(data)
    })

    this.socket.on('notification_read', this.onNotificationRead)

    this.socket.on('stats_updated', (stats: { unread: number; total: number }) => {
      console.log('📈 Estadísticas actualizadas:', stats)
      this.notifyStatsListeners(stats)
    })

    // Manejo de autenticación
    this.socket.on('authenticated', (data: any) => {
      console.log('[WS][AUTH] Autenticado correctamente:', data);
      this.isAuthenticated = true;
    })

    this.socket.on('authentication_error', (data: any) => {
      console.error('[WS][AUTH] Error de autenticación:', data);
      this.isAuthenticated = false;
    })
  }

  async handleReconnect(token: string, userId: string) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      const delay = 1000 * Math.pow(2, this.reconnectAttempts - 1)
      console.log(`🔄 Intentando reconectar en ${delay}ms (intento ${this.reconnectAttempts})`)
      setTimeout(async () => {
        // Refrescar token antes de reconectar
        let validToken = token;
        try {
          if (!authService.isAuthenticated()) {
            const refreshed = await authService.refreshToken();
            if (!refreshed) {
              console.warn('[WS][RECONNECT] No se pudo refrescar el token, cerrando sesión');
              authService.clearAuth();
              this.disconnect();
              return;
            }
            validToken = refreshed;
          }
        } catch (e) {
          console.error('[WS][RECONNECT] Error refrescando token:', e);
          authService.clearAuth();
          this.disconnect();
          return;
        }
        this.connect(validToken, userId)
      }, delay)
    } else {
      console.error('❌ Máximo número de intentos de reconexión alcanzado')
    }
  }

  // Métodos para listeners
  addNotificationListener(callback: (notification: NotificationData) => void) {
    this.listeners.push(callback)
    return () => {
      this.listeners = this.listeners.filter((listener) => listener !== callback)
    }
  }

  addConnectionListener(callback: (connected: boolean) => void) {
    this.connectionListeners.push(callback)
    return () => {
      this.connectionListeners = this.connectionListeners.filter((listener) => listener !== callback)
    }
  }

  addStatsListener(callback: (stats: { unread: number; total: number }) => void) {
    this.statsListeners.push(callback)
    return () => {
      this.statsListeners = this.statsListeners.filter((listener) => listener !== callback)
    }
  }

  private notifyListeners(notification: NotificationData) {
    // DEDUPLICACIÓN: Solo notificar si no existe ya una notificación con ese id
    this.listeners.forEach((listener) => {
      try {
        listener(notification)
      } catch (error) {
        console.error("Error en listener de notificación:", error)
      }
    })
  }

  private notifyConnectionListeners(connected: boolean) {
    this.connectionListeners.forEach((listener) => {
      try {
        listener(connected)
      } catch (error) {
        console.error("Error en listener de conexión:", error)
      }
    })
  }

  private notifyStatsListeners(stats: { unread: number; total: number }) {
    this.statsListeners.forEach((listener) => {
      try {
        listener(stats)
      } catch (error) {
        console.error("Error en listener de estadísticas:", error)
      }
    })
  }

  // Métodos para interactuar con el servidor
  markNotificationAsRead(notificationId: string | number) {
    if (this.socket?.connected && this.isAuthenticated) {
      const payload = /^[0-9]+$/.test(String(notificationId)) ? Number(notificationId) : notificationId;
      return new Promise<boolean>((resolve) => {
        // Escuchar confirmación del backend (puede ser número o { notificationId })
        const handler = (readData: any) => {
          const readId = typeof readData === 'object' && readData.notificationId ? readData.notificationId : readData;
          if (readId === payload) {
            if (this.socket) this.socket.off('notification_read', handler);
            resolve(true);
          }
        };
        if (this.socket) this.socket.on('notification_read', handler);
        // Enviar solo el número
        if (this.socket) this.socket.emit('mark_notification_read', payload);
        // Fallback: timeout si no hay confirmación en 2s
        setTimeout(() => {
          if (this.socket) this.socket.off('notification_read', handler);
          resolve(false);
        }, 2000);
      });
    } else if (this.socket?.connected) {
      // esperar hasta 500ms a que se autentique
      return new Promise<boolean>((resolve) => {
        const waitStart = Date.now();
        const waitForAuth = () => {
          if (this.isAuthenticated) {
            this.markNotificationAsRead(notificationId).then(resolve);
          } else if (Date.now() - waitStart > 500) {
            resolve(false);
          } else {
            setTimeout(waitForAuth, 50);
          }
        };
        waitForAuth();
      });
    } else {
      // Si no hay WebSocket, intenta por HTTP
      const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
      const token = localStorage.getItem('auth_token');
      return fetch(`${API_URL}/notifications/${notificationId}/read`, {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${token}` }
      })
        .then(res => res.ok)
        .catch(() => false);
    }
  }

  subscribeToRole(roleId: string | number) {
    console.log('[WS][ROLE] Suscribiéndose al rol:', roleId);
    if (this.socket && this.isConnected) {
      this.socket.emit('subscribeToRole', { roleId });
      this.socket.on('role_subscription_success', (data: any) => {
        console.log('[WS][ROLE] Suscripción exitosa al rol:', data);
      });
      this.socket.on('role_subscription_error', (data: any) => {
        console.error('[WS][ROLE] Error al suscribirse al rol:', data);
      });
    } else {
      console.warn('[WS][ROLE] No se pudo suscribir al rol porque el socket no está conectado.');
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
  }

  getConnectionStatus() {
    return this.isConnected
  }

  onNotificationRead(readData: any) {
    // Puede ser notificationId (número) o { notificationId, timestamp }
    const id = typeof readData === 'object' && readData.notificationId ? readData.notificationId : readData;
    // Notificar a listeners para que actualicen el estado local (cambio de status)
    this.listeners.forEach((cb) => {
      try {
        cb({ id, status: 'read' } as any);
      } catch (e) {}
    });
  }
}

// Singleton instance
const wsManager = new NotificationWebSocketManager()

console.log('[WS][LIB] useWebSocket hook ejecutado');

export function useWebSocket(user?: { id: string | number } | null, token?: string | null) {
  const [isConnected, setIsConnected] = useState(false)
  const [notifications, setNotifications] = useState<NotificationData[]>([])
  const [notificationStats, setNotificationStats] = useState({ unread: 0, total: 0 })
  const [initialized, setInitialized] = useState(false);

  // Si no se pasa user/token, intentar obtenerlos de localStorage (para compatibilidad)
  const userId = user?.id ? user.id.toString() : (() => {
    if (typeof window === 'undefined') return null;
    const userRaw = localStorage.getItem('user_data');
    if (!userRaw) return null;
    try {
      const parsed = JSON.parse(userRaw);
      return parsed.id || parsed.userId || null;
    } catch {
      return null;
    }
  })();
  const authToken = token ?? (typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null);

  // Precargar notificaciones al conectar (traer todas, no solo no leídas)
  useEffect(() => {
    if (!authToken || !userId) return;
    if (initialized) return;
    const fetchInitialNotifications = async () => {
      try {
        const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
        const res = await fetch(`${API_URL}/notifications?limit=10`, {
          headers: { 'Authorization': `Bearer ${authToken}` }
        });
        const data = await res.json();
        if (Array.isArray(data.data)) {
          setNotifications(data.data); // Ya deduplicadas por backend
          setNotificationStats((prev) => ({ ...prev, unread: data.data.filter((n:any)=>n.status==='unread').length }));
        }
      } catch (e) {
        console.warn('[WS][LIB] Error precargando notificaciones:', e);
      } finally {
        setInitialized(true);
      }
    };
    fetchInitialNotifications();
  }, [authToken, userId, initialized]);

  // Inicializar WebSocket cuando user/token cambian
  useEffect(() => {
    if (!authToken || !userId) return;
    wsManager.connect(authToken, userId);
  }, [authToken, userId]);

  // Configurar listeners
  function isStatusUpdate(obj: any): obj is { id: string; status: 'read' } {
    return obj && typeof obj === 'object' && obj.status === 'read' && typeof obj.id === 'string';
  }
  useEffect(() => {
    const removeConnectionListener = wsManager.addConnectionListener(setIsConnected)
    const removeNotificationListener = wsManager.addNotificationListener((notification: NotificationData | { id: string; status: 'read' }) => {
      if (isStatusUpdate(notification)) {
        setNotifications((prev) => prev.map(n => n.id === notification.id ? { ...n, status: 'read' } : n))
        return;
      }
      // Evitar duplicados por ID
      setNotifications((prev) => {
        if (prev.some(n => n.id === notification.id)) return prev;
        // Si existe una notificación con el mismo título, producto y status 'unread', actualiza el status
        if (!notification.metadata || !notification.metadata.productId) {
          return [notification as NotificationData, ...prev.slice(0, 49)]
        }
        const idx = prev.findIndex(n => n.title === notification.title && n.metadata && notification.metadata && n.metadata.productId === notification.metadata.productId && n.status === 'unread')
        if (idx !== -1) {
          const updated = [...prev]
          updated[idx] = { ...updated[idx], ...notification }
          return updated
        }
        return [notification as NotificationData, ...prev.slice(0, 49)]
      })
      // Mostrar notificación del navegador si está permitido
      if ("Notification" in window && Notification.permission === "granted") {
        const n = notification as NotificationData;
        new Notification(n.title, {
          body: n.message,
          icon: "/favicon.ico",
          tag: n.id
        })
      }
    })
    const removeStatsListener = wsManager.addStatsListener(setNotificationStats)
    return () => {
      removeConnectionListener();
      removeNotificationListener();
      removeStatsListener();
    }
  }, [])

  const requestNotificationPermission = async () => {
    if ("Notification" in window && Notification.permission === "default") {
      const permission = await Notification.requestPermission()
      return permission === "granted"
    }
    return Notification.permission === "granted"
  }

  const clearNotifications = () => {
    setNotifications([])
  }

  const markAsRead = async (notificationId: string) => {
    const ref = notifications.find(n => n.id === notificationId)
    if (ref && ref.metadata && typeof ref.metadata === 'object' && 'productId' in ref.metadata && ref.metadata.productId) {
      // Marcar duplicados en backend
      const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
      const token = authToken;
      try {
        await fetch(`${API_URL}/notifications/mark-duplicates-read`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ title: ref.title, productId: ref.metadata.productId })
        });
      } catch {}
      // Marcar localmente todas las duplicadas
      setNotifications((prev) => prev.map(n =>
        n.title === ref.title && n.metadata && typeof n.metadata === 'object' && 'productId' in n.metadata && ref.metadata && n.metadata.productId === ref.metadata.productId
          ? { ...n, status: 'read' }
          : n
      ))
      return;
    }
    // Si no hay metadata/productId, solo por id (flujo original)
    const ok = await wsManager.markNotificationAsRead(notificationId)
    if (!ok) {
      console.warn('[WS][LIB] No se recibió confirmación, probablemente ya estaba leída o no pertenece al usuario')
    }
    setNotifications((prev) => prev.map(n => n.id === notificationId ? { ...n, status: 'read' } : n))
  }

  // Polling HTTP cada 20 segundos para sincronizar con la BD (traer todas)
  useEffect(() => {
    if (!authToken || !userId) return;
    const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
    let stopped = false;
    const fetchNotifications = async () => {
      try {
        const res = await fetch(`${API_URL}/notifications?limit=10`, {
          headers: { 'Authorization': `Bearer ${authToken}` }
        });
        const data = await res.json();
        if (Array.isArray(data.data)) {
          setNotifications(data.data); // Ya deduplicadas por backend
          setNotificationStats((prev) => ({ ...prev, unread: data.data.filter((n:any)=>n.status==='unread').length }));
        }
      } catch (e) {
        // Silenciar errores de red
      }
    };
    const interval = setInterval(() => {
      if (!stopped) fetchNotifications();
    }, 20000);
    return () => {
      stopped = true;
      clearInterval(interval);
    };
  }, [authToken, userId]);

  // Si no hay sesión, exponer estado vacío seguro
  if (!authToken || !userId) {
    return {
      isConnected: false,
      notifications: [],
      notificationStats: { unread: 0, total: 0 },
      clearNotifications: () => {},
      markAsRead: async () => {},
      requestNotificationPermission: async () => false,
    };
  }

  return {
    isConnected,
    notifications,
    notificationStats,
    clearNotifications,
    markAsRead,
    requestNotificationPermission,
  }
}

export default wsManager
