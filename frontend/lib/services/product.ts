import { apiClient, ApiResponse, PaginatedResponse } from '../api'

// Types based on backend DTOs
export interface Product {
  id: number
  codigoItem: string
  nombre: string
  descripcion?: string
  marca?: string
  modelo?: string
  numeroSerie?: string
  pedimento?: string
  observaciones?: string
  tipoAlmacen: 'GENERAL' | 'ROTATIVO'
  stockDisponible: number
  stockComprometido: number
  stockMinimo: number
  estado: 'DISPONIBLE' | 'AGOTADO' | 'DESCONTINUADO'
  ubicacion?: string
  createdAt: Date
  updatedAt: Date
  createdBy?: number
  updatedBy?: number
  // Computed fields from backend
  stockReal?: number
  nivelStock?: 'CRITICO' | 'BAJO' | 'NORMAL' | 'ALTO'
  colorIndicador?: 'red' | 'yellow' | 'green' | 'blue'
  disponibleParaVenta?: boolean
}

export interface CreateProductData {
  codigoItem: string
  nombre: string
  descripcion?: string
  marca?: string
  modelo?: string
  numeroSerie?: string
  pedimento?: string
  observaciones?: string
  tipoAlmacen?: 'GENERAL' | 'ROTATIVO'
  stockDisponible?: number
  stockComprometido?: number
  stockMinimo?: number
  estado?: 'DISPONIBLE' | 'AGOTADO' | 'DESCONTINUADO'
  ubicacion?: string
}

export interface UpdateProductData {
  codigoItem?: string
  nombre?: string
  descripcion?: string
  marca?: string
  modelo?: string
  numeroSerie?: string
  pedimento?: string
  observaciones?: string
  tipoAlmacen?: 'GENERAL' | 'ROTATIVO'
  stockDisponible?: number
  stockComprometido?: number
  stockMinimo?: number
  estado?: 'DISPONIBLE' | 'AGOTADO' | 'DESCONTINUADO'
  ubicacion?: string
}

export interface ProductFilters {
  search?: string
  tipoAlmacen?: 'GENERAL' | 'ROTATIVO'
  estado?: 'DISPONIBLE' | 'AGOTADO' | 'DESCONTINUADO'
  marca?: string
  modelo?: string
  ubicacion?: string
  page?: number
  limit?: number
  sortBy?: 'codigoItem' | 'nombre' | 'marca' | 'modelo' | 'stockDisponible' | 'createdAt'
  sortOrder?: 'ASC' | 'DESC'
}

export interface StockMovement {
  tipo: 'ENTRADA' | 'SALIDA'
  cantidad: number
  motivo?: string
}

export interface QRScanResult {
  producto?: Product
  error?: string
  message?: string
  qrData?: any
}

export interface QRGenerateData {
  type: 'product'
  version: string
  timestamp: string
  product: {
    id: number
    codigoItem: string
    nombre: string
    descripcion?: string
    marca?: string
    modelo?: string
    numeroSerie?: string
    pedimento?: string
    tipoAlmacen: 'GENERAL' | 'ROTATIVO'
    stockDisponible: number
    stockComprometido: number
    stockMinimo: number
    estado: 'DISPONIBLE' | 'AGOTADO' | 'DESCONTINUADO'
    ubicacion?: string
    observaciones?: string
  }
  metadata: {
    generatedBy: string
    system: string
    purpose: string
  }
}

export interface InventoryStats {
  totalProducts: number
  lowStockProducts: number
  outOfStockProducts: number
  totalValue: number
  generalProducts: number
  rotativoProducts: number
  availableProducts: number
  discontinuedProducts: number
}

export class ProductService {
  private baseUrl = '/product'

  async getAllProducts(filters?: ProductFilters): Promise<ApiResponse<PaginatedResponse<Product>>> {
    const params = new URLSearchParams()
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString())
        }
      })
    }

    const queryString = params.toString()
    const endpoint = queryString ? `${this.baseUrl}?${queryString}` : this.baseUrl
    
    return apiClient.get<PaginatedResponse<Product>>(endpoint)
  }

  async getProductById(id: number): Promise<ApiResponse<Product>> {
    return apiClient.get<Product>(`${this.baseUrl}/${id}`)
  }

  async getProductByCode(code: string): Promise<ApiResponse<Product>> {
    return apiClient.get<Product>(`${this.baseUrl}/by-code/${code}`)
  }

  async getLowStockProducts(): Promise<ApiResponse<Product[]>> {
    return apiClient.get<Product[]>(`${this.baseUrl}/low-stock`)
  }

  async getInventoryStats(): Promise<ApiResponse<InventoryStats>> {
    return apiClient.get<InventoryStats>(`${this.baseUrl}/stats`)
  }

  async createProduct(data: CreateProductData): Promise<ApiResponse<Product>> {
    return apiClient.post<Product>(this.baseUrl, data)
  }

  async updateProduct(id: number, data: UpdateProductData): Promise<ApiResponse<Product>> {
    return apiClient.put<Product>(`${this.baseUrl}/${id}`, data)
  }

  async deleteProduct(id: number): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`${this.baseUrl}/${id}`)
  }

  async reserveStock(id: number, cantidad: number): Promise<ApiResponse<Product>> {
    return apiClient.post<Product>(`${this.baseUrl}/${id}/reserve`, { cantidad })
  }

  async adjustStock(id: number, movement: StockMovement): Promise<ApiResponse<Product>> {
    return apiClient.post<Product>(`${this.baseUrl}/${id}/adjust-stock`, movement)
  }

  async processQRData(qrData: string): Promise<ApiResponse<QRScanResult>> {
    return apiClient.post<QRScanResult>('/inventory-movements/qr/scan', { qrData })
  }

  generateQRData(product: Product): QRGenerateData {
    return {
      type: 'product',
      version: '2.0',
      timestamp: new Date().toISOString(),
      product: {
        id: product.id,
        codigoItem: product.codigoItem,
        nombre: product.nombre,
        descripcion: product.descripcion,
        marca: product.marca,
        modelo: product.modelo,
        numeroSerie: product.numeroSerie,
        pedimento: product.pedimento,
        tipoAlmacen: product.tipoAlmacen,
        stockDisponible: product.stockDisponible,
        stockComprometido: product.stockComprometido,
        stockMinimo: product.stockMinimo,
        estado: product.estado,
        ubicacion: product.ubicacion,
        observaciones: product.observaciones
      },
      metadata: {
        generatedBy: 'COMINTEC_ALMACEN',
        system: 'inventory_management',
        purpose: 'product_identification'
      }
    }
  }
}

export const productService = new ProductService() 