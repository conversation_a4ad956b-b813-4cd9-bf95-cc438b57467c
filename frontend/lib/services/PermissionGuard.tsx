import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { authService } from '@/lib/auth';

export function PermissionGuard({ children, permissions }: { children: React.ReactNode; permissions: string[] }) {
  const router = useRouter();
  const [authorized, setAuthorized] = useState(false);

  useEffect(() => {
    const user = authService.getUser();
    if (user) {
      const userPermissions = user.permissions || [];
      const hasPermission = permissions.every(p => userPermissions.includes(p));
      if (hasPermission) {
        setAuthorized(true);
      } else {
        setAuthorized(false);
        router.push('/unauthorized');
      }
    } else {
      setAuthorized(false);
      router.push('/login');
    }
  }, [router, permissions]);

  return authorized ? <>{children}</> : null;
} 