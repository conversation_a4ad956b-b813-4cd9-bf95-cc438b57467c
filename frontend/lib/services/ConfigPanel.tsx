import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';

const validationSchema = Yup.object().shape({
  companyName: Yup.string().required('Company Name is required'),
  logo: Yup.mixed().test('fileSize', 'File too large', (value: any) => {
    if (!value || !value.length) return true; // attachment is optional
    return value[0].size <= 2000000;
  }),
  timezone: Yup.string().required('Timezone is required'),
  currency: Yup.string().required('Currency is required'),
});

interface ConfigPanelProps {
  config?: any;
  onSubmit: (data: any) => void;
}

export function ConfigPanel({ config, onSubmit }: ConfigPanelProps) {
  const { register, handleSubmit, formState: { errors } } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: config,
  });

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="form-group">
        <label>Company Name</label>
        <input type="text" {...register('companyName')} className={`form-control ${errors.companyName ? 'is-invalid' : ''}`} />
        <div className="invalid-feedback">{errors.companyName?.message as string}</div>
      </div>
      <div className="form-group">
        <label>Logo</label>
        <input type="file" {...register('logo')} className={`form-control ${errors.logo ? 'is-invalid' : ''}`} />
        <div className="invalid-feedback">{errors.logo?.message as string}</div>
      </div>
      <div className="form-group">
        <label>Timezone</label>
        <input type="text" {...register('timezone')} className={`form-control ${errors.timezone ? 'is-invalid' : ''}`} />
        <div className="invalid-feedback">{errors.timezone?.message as string}</div>
      </div>
      <div className="form-group">
        <label>Currency</label>
        <input type="text" {...register('currency')} className={`form-control ${errors.currency ? 'is-invalid' : ''}`} />
        <div className="invalid-feedback">{errors.currency?.message as string}</div>
      </div>
      <div className="form-group">
        <button type="submit" className="btn btn-primary">Save Configuration</button>
      </div>
    </form>
  );
} 