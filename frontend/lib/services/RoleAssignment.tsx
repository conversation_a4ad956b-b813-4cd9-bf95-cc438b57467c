import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';
import { userService } from '@/lib/services/user';

const validationSchema = Yup.object().shape({
  userId: Yup.string().required('User is required'),
  roles: Yup.array().min(1, 'At least one role is required'),
});

interface RoleAssignmentProps {
  users: any[];
  rolesList: any[];
}

export function RoleAssignment({ users, rolesList }: RoleAssignmentProps) {
  const { register, handleSubmit, formState: { errors } } = useForm({
    resolver: yupResolver(validationSchema),
  });

  const onSubmit = async (data: any) => {
    try {
      // Convertir roles (array de nombres o ids) a roleIds (array de números)
      const roleIds = Array.isArray(data.roles)
        ? data.roles.map((id: any) => Number(id)).filter((id: any) => !isNaN(id))
        : [];
      await userService.assignRoles(data.userId, roleIds);
      alert('Roles assigned successfully');
    } catch (error) {
      alert('Error assigning roles');
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="form-group">
        <label>User</label>
        <select {...register('userId')} className={`form-control ${errors.userId ? 'is-invalid' : ''}`}>
          {users.map((user: any) => (
            <option key={user.id} value={user.id}>{user.name}</option>
          ))}
        </select>
        <div className="invalid-feedback">{errors.userId?.message}</div>
      </div>
      <div className="form-group">
        <label>Roles</label>
        <select multiple {...register('roles')} className={`form-control ${errors.roles ? 'is-invalid' : ''}`}>
          {rolesList.map((role) => (
            <option key={role.id} value={role.id}>{role.name}</option>
          ))}
        </select>
        <div className="invalid-feedback">{errors.roles?.message}</div>
      </div>
      <div className="form-group">
        <button type="submit" className="btn btn-primary">Assign Roles</button>
      </div>
    </form>
  );
} 