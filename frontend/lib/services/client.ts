import { apiClient } from "../api";

export class ClientService {
  private baseUrl = "/clients";

  async getClients(params: Record<string, any> = {}) {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== "") searchParams.append(key, value);
    });
    const response = await apiClient.get<any>(`${this.baseUrl}?${searchParams}`);
    if (!response.success) throw new Error(response.message || "Error al obtener clientes");
    // Robust normalization of backend response
    let clients: any[] = [];
    let total = 0, page = 1, limit = 10, totalPages = 1;
    if (response.data) {
      if (Array.isArray(response.data)) {
        clients = response.data;
      } else if (response.data.data) {
        clients = response.data.data;
        // meta may be inside response.data
        const meta = response.data.meta || {};
        total = response.data.total || meta.total || clients.length;
        page = response.data.page || meta.page || 1;
        limit = response.data.limit || meta.limit || 10;
        totalPages = response.data.totalPages || meta.totalPages || 1;
      } else {
        // fallback: treat as array or object
        clients = Array.isArray(response.data) ? response.data : [response.data];
      }
    }
    return { clients, total, page, limit, totalPages };
  }

  async getClient(id: string | number) {
    const response = await apiClient.get<any>(`${this.baseUrl}/${id}`);
    if (!response.success) throw new Error(response.message || "Error al obtener cliente");
    return response.data!;
  }

  async createClient(data: any) {
    const response = await apiClient.post<any>(this.baseUrl, data);
    if (!response.success) throw new Error(response.message || "Error al crear cliente");
    return response.data!;
  }

  async updateClient(id: string | number, data: any) {
    const response = await apiClient.put<any>(`${this.baseUrl}/${id}`, data);
    if (!response.success) throw new Error(response.message || "Error al actualizar cliente");
    return response.data!;
  }

  async deleteClient(id: string | number) {
    const response = await apiClient.delete<any>(`${this.baseUrl}/${id}`);
    if (!response.success) throw new Error(response.message || "Error al eliminar cliente");
    return response.data!;
  }

  async getQuotationsByClientId(clientId: string | number) {
    const response = await apiClient.get<any>(`${this.baseUrl}/${clientId}/quotations`);
    if (!response.success) throw new Error(response.message || "Error al obtener cotizaciones del cliente");
    return response.data || [];
  }

  async getClientPurchases(clientId: string | number) {
    const response = await apiClient.get<any>(`${this.baseUrl}/${clientId}/purchases`);
    if (!response.success) throw new Error(response.message || "Error al obtener historial de compras");
    return response.data || [];
  }

  async getClientTopProducts(clientId: string | number) {
    const response = await apiClient.get<any>(`${this.baseUrl}/${clientId}/top-products`);
    if (!response.success) throw new Error(response.message || "Error al obtener productos más comprados");
    return response.data || [];
  }

  async getClientDocuments(clientId: string | number) {
    const response = await apiClient.get<any>(`${this.baseUrl}/${clientId}/documents`);
    if (!response.success) throw new Error(response.message || "Error al obtener documentos del cliente");
    return response.data || [];
  }

  async uploadClientDocument(clientId: string | number, formData: FormData) {
    const response = await apiClient.post<any>(`${this.baseUrl}/${clientId}/documents`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    if (!response.success) throw new Error(response.message || "Error al subir documento");
    return response.data;
  }

  async deleteClientDocument(clientId: string | number, docId: string | number) {
    const response = await apiClient.delete<any>(`${this.baseUrl}/${clientId}/documents/${docId}`);
    if (!response.success) throw new Error(response.message || "Error al eliminar documento");
    return response.data;
  }

  async getClientKpis(clientId: string | number) {
    const response = await apiClient.get<any>(`${this.baseUrl}/${clientId}/kpis`);
    if (!response.success) throw new Error(response.message || "Error al obtener KPIs del cliente");
    return response.data || {};
  }
}

export const clientService = new ClientService();
export const getClientDocuments = clientService.getClientDocuments.bind(clientService);