import axios from 'axios';
import { authHeader } from './auth-header';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';

export enum BackupType {
  MANUAL = 'manual',
  AUTOMATIC = 'automatic',
  SCHEDULED = 'scheduled'
}

export enum BackupStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export interface Backup {
  id: number;
  name: string;
  description?: string;
  type: BackupType;
  status: BackupStatus;
  filePath?: string;
  fileSize?: number;
  errorMessage?: string;
  metadata?: any;
  scheduledAt?: string;
  completedAt?: string;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface BackupStats {
  total: number;
  completed: number;
  failed: number;
  pending: number;
  totalSize: number;
}

export interface CreateBackupRequest {
  name: string;
  description?: string;
  type?: BackupType;
}

export interface ScheduleBackupRequest {
  name: string;
  cronExpression?: string;
  frequency?: 'daily' | 'weekly' | 'monthly';
  description?: string;
}

export class BackupService {
  getAllBackups() {
    return axios.get<Backup[]>(`${API_URL}/systems/backups`, { headers: authHeader() });
  }

  getBackupById(id: number) {
    return axios.get<Backup>(`${API_URL}/systems/backups/${id}`, { headers: authHeader() });
  }

  createBackup(data: CreateBackupRequest) {
    return axios.post<Backup>(`${API_URL}/systems/backups`, data, { headers: authHeader() });
  }

  deleteBackup(id: number) {
    return axios.delete(`${API_URL}/systems/backups/${id}`, { headers: authHeader() });
  }

  downloadBackup(id: number) {
    return axios.get(`${API_URL}/systems/backups/${id}/download`, {
      headers: authHeader(),
      responseType: 'blob'
    });
  }

  restoreBackup(id: number, confirm: boolean = true) {
    return axios.post(`${API_URL}/systems/backups/${id}/restore`, { confirm }, { headers: authHeader() });
  }

  scheduleBackup(data: ScheduleBackupRequest) {
    return axios.post<Backup>(`${API_URL}/systems/backups/schedule`, data, { headers: authHeader() });
  }

  getBackupStats() {
    return axios.get<BackupStats>(`${API_URL}/systems/backups/stats`, { headers: authHeader() });
  }

  cleanupExpiredBackups() {
    return axios.post(`${API_URL}/systems/backups/cleanup`, {}, { headers: authHeader() });
  }
}

export const backupService = new BackupService(); 