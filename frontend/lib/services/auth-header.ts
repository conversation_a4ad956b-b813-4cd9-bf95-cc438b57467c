export function authHeader(): { [key: string]: string } {
  console.log('=== [AUTH HEADER] ===');
  
  // Usar el sistema de autenticación principal
  const token = localStorage.getItem('auth_token');
  console.log('Token from auth_token:', token ? 'YES' : 'NO');

  if (token) {
    const header = { Authorization: 'Bearer ' + token };
    console.log('✅ Returning auth header:', header);
    return header;
  } else {
    console.log('❌ No token found, returning empty object');
    return {};
  }
} 