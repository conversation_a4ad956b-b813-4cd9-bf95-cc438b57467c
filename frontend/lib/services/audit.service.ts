import axios from 'axios';
import { authHeader } from './auth-header';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';

class AuditService {
  getAuditLogs(params?: { search?: string; startDate?: string; endDate?: string }) {
    let url = `${API_URL}/systems/audit`;
    const queryParams = new URLSearchParams();
    if (params?.search) {
      queryParams.append('search', params.search);
    }
    if (params?.startDate) {
      queryParams.append('startDate', params.startDate);
    }
    if (params?.endDate) {
      queryParams.append('endDate', params.endDate);
    }
    const queryString = queryParams.toString();
    if (queryString) {
      url += `?${queryString}`;
    }
    return axios.get(url, { headers: authHeader() });
  }
}

export const auditService = new AuditService();