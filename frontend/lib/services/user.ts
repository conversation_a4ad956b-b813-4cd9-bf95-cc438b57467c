"use client"

import { apiClient, ApiResponse } from '../api'

// Types para usuarios
export interface User {
  id: number
  name: string
  email: string
  phone?: string
  password?: string
  status: 'activo' | 'inactivo' | 'bloqueado'
  area?: string
  refresh_token?: string
  active: boolean
  created_at: string
  updated_at: string
  created_by?: number
  updated_by?: number
  roles?: Role[]
}

export interface Role {
  id: number
  name: string
  description?: string
  permissions?: Permission[]
}

export interface Permission {
  id: number
  resource: string
  action: string
  conditions?: any
}

export interface CreateUserData {
  name: string
  email: string
  phone?: string
  password: string
  area?: string
  status?: 'activo' | 'inactivo' | 'bloqueado'
  roleIds?: number[]
}

export interface UpdateUserData {
  name?: string
  email?: string
  phone?: string
  area?: string
  status?: 'activo' | 'inactivo' | 'bloqueado'
  roleIds?: number[]
}

export interface ChangePasswordData {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export interface AdminChangePasswordData {
  newPassword: string
  confirmPassword: string
}

export interface UserFilters {
  search?: string
  status?: string
  area?: string
  role?: string
  page?: number
  limit?: number
}

export interface UsersResponse {
  users: User[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// Clase de servicio
export class UserService {
  private baseUrl = '/systems/users'

  // Obtener usuarios con filtros y paginación
  async getUsers(filters: UserFilters = {}): Promise<UsersResponse> {
    const params = new URLSearchParams()
    if (filters.search) params.append('search', filters.search)
    if (filters.status) params.append('status', filters.status)
    if (filters.area) params.append('area', filters.area)
    if (filters.role) params.append('role', filters.role)
    if (filters.page) params.append('page', filters.page.toString())
    if (filters.limit) params.append('limit', filters.limit.toString())

    const response = await apiClient.get<any>(`${this.baseUrl}?${params}`)
    if (!response.success) {
      throw new Error(response.message || 'Error fetching users')
    }
    // Normaliza siempre la respuesta para que users sea array
    let users: User[] = [];
    let total = 0;
    let page = 1;
    let limit = 10;
    let totalPages = 1;
    if (Array.isArray(response.data)) {
      users = response.data;
      total = users.length;
    } else if (response.data && typeof response.data === 'object') {
      users = Array.isArray(response.data.data)
        ? response.data.data
        : (Array.isArray(response.data.users) ? response.data.users : []);
      total = response.data.total ?? users.length;
      page = response.data.page ?? 1;
      limit = response.data.limit ?? 10;
      totalPages = response.data.totalPages ?? 1;
    }
    return {
      users,
      total,
      page,
      limit,
      totalPages
    }
  }

  // Obtener usuario por ID
  async getUser(id: number): Promise<User> {
    const response = await apiClient.get<User>(`${this.baseUrl}/${id}`)
    if (!response.success) {
      throw new Error(response.message || 'Error fetching user')
    }
    return response.data!
  }

  // Crear usuario
  async createUser(data: CreateUserData): Promise<User> {
    const { roleIds, ...otherData } = data;
    // El endpoint moderno espera roles: number[]
    const requestData = {
      ...otherData,
      ...(roleIds && { roles: roleIds })
    }
    const response = await apiClient.post<User>(this.baseUrl, requestData);
    if (!response.success) {
      throw new Error(response.message || 'Error creating user')
    }
    return response.data!
  }

  // Actualizar usuario
  async updateUser(id: number, data: UpdateUserData): Promise<User> {
    const { roleIds, ...otherData } = data;
    const requestData = {
      ...otherData,
      ...(roleIds && { roles: roleIds })
    }
    const response = await apiClient.put<User>(`${this.baseUrl}/${id}`, requestData)
    if (!response.success) {
      throw new Error(response.message || 'Error updating user')
    }
    return response.data!
  }

  // Eliminar usuario
  async deleteUser(id: number): Promise<void> {
    const response = await apiClient.delete<void>(`${this.baseUrl}/${id}`)
    if (!response.success) {
      throw new Error(response.message || 'Error deleting user')
    }
  }

  // Cambiar contraseña
  async changePassword(id: number, data: ChangePasswordData): Promise<void> {
    const response = await apiClient.put<void>(`${this.baseUrl}/${id}/password`, data)
    if (!response.success) {
      throw new Error(response.message || 'Error changing password')
    }
  }

  // Cambiar estado del usuario (bloquear/desbloquear)
  async updateUserStatus(id: number, status: 'activo' | 'inactivo' | 'bloqueado'): Promise<User> {
    // El endpoint moderno usa updateUserStatus(id, status) por body
    const response = await apiClient.put<User>(`${this.baseUrl}/${id}/status`, { status })
    if (!response.success) {
      throw new Error(response.message || 'Error updating user status')
    }
    return response.data!
  }

  // Obtener roles disponibles
  async getRoles(): Promise<Role[]> {
    const response = await apiClient.get<Role[]>('/systems/roles')
    if (!response.success) {
      throw new Error(response.message || 'Error fetching roles')
    }
    return response.data!
  }

  // Asignar roles a usuario
  async assignRoles(userId: number, roleIds: number[]): Promise<User> {
    // El endpoint moderno usa updateUserWithRoleIds
    const response = await apiClient.put<User>(`${this.baseUrl}/${userId}`, { roles: roleIds })
    if (!response.success) {
      throw new Error(response.message || 'Error assigning roles')
    }
    return response.data!
  }

  // Obtener áreas disponibles
  async getAreas(): Promise<string[]> {
    const response = await apiClient.get<string[]>('/systems/users/areas')
    if (!response.success) {
      throw new Error(response.message || 'Error fetching areas')
    }
    return response.data!
  }

  // Activar usuario
  async activateUser(id: number): Promise<User> {
    const response = await apiClient.post<User>(`${this.baseUrl}/${id}/activate`)
    if (!response.success) {
      throw new Error(response.message || 'Error activating user')
    }
    return response.data!
  }

  // Desactivar usuario
  async deactivateUser(id: number): Promise<User> {
    const response = await apiClient.post<User>(`${this.baseUrl}/${id}/deactivate`)
    if (!response.success) {
      throw new Error(response.message || 'Error deactivating user')
    }
    return response.data!
  }

  // Cambiar estado específico del usuario
  async updateUserStatus(id: number, status: 'activo' | 'inactivo' | 'bloqueado'): Promise<User> {
    const response = await apiClient.put<User>(`${this.baseUrl}/${id}/status`, { status })
    if (!response.success) {
      throw new Error(response.message || 'Error updating user status')
    }
    return response.data!
  }

  // Cambiar contraseña (admin)
  async adminChangePassword(userId: number, newPassword: string): Promise<User> {
    const response = await apiClient.put<User>(`${this.baseUrl}/${userId}/admin-change-password`, {
      userId,
      newPassword
    })
    if (!response.success) {
      throw new Error(response.message || 'Error changing password')
    }
    return response.data!
  }
}

// Instancia del servicio
export const userService = new UserService()
