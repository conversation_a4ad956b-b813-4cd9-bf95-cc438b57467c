"use client"

import { apiClient, ApiResponse } from "../api"

// Types for Role entity
export interface Role {
  id: number
  name: string
  description?: string
  permissions?: Permission[]
}

export interface Permission {
  id: number
  name: string
  description?: string
  module?: string
  action?: string
}

export interface CreateRoleDto {
  name: string
  // TODO: Uncomment when description column is added to Supabase
  // description?: string
  permissions?: string[]
}

export interface UpdateRoleDto {
  name?: string
  // TODO: Uncomment when description column is added to Supabase
  // description?: string
  permissions?: string[]
}

export interface AssignPermissionsDto {
  roleId: number
  permissions: string[]
}

// Response type for permissions grouped by module
export interface PermissionsByModule {
  [module: string]: Permission[]
}

class RoleService {
  private readonly BASE_URL = "/systems"

  /**
   * Get all roles with their permissions
   */
  async getRoles(): Promise<ApiResponse<Role[]>> {
    return apiClient.get<Role[]>(`${this.BASE_URL}/roles`)
  }

  /**
   * Get a specific role by ID
   */
  async getRole(id: number): Promise<ApiResponse<Role>> {
    return apiClient.get<Role>(`${this.BASE_URL}/roles/${id}`)
  }

  /**
   * Create a new role
   */
  async createRole(dto: CreateRoleDto): Promise<ApiResponse<Role>> {
    return apiClient.post<Role>(`${this.BASE_URL}/roles`, dto)
  }

  /**
   * Update an existing role
   */
  async updateRole(id: number, dto: UpdateRoleDto): Promise<ApiResponse<Role>> {
    return apiClient.put<Role>(`${this.BASE_URL}/roles/${id}`, dto)
  }

  /**
   * Remove a role
   */
  async deleteRole(id: number): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`${this.BASE_URL}/roles/${id}`)
  }

  /**
   * Get all available permissions
   */
  async getPermissions(): Promise<ApiResponse<Permission[]>> {
    return apiClient.get<Permission[]>(`${this.BASE_URL}/permissions`)
  }

  /**
   * Get permissions grouped by module for easier UI display
   */
  async getPermissionsByModule(): Promise<ApiResponse<PermissionsByModule>> {
    const response = await this.getPermissions()
    if (response.success && response.data) {
      // Handle nested data structure from backend
      const permissions = Array.isArray(response.data) ? response.data : Array.isArray((response.data as any)?.data) ? (response.data as any).data : []
      
      const grouped = permissions.reduce((acc: PermissionsByModule, permission: Permission) => {
        const module = permission.module || 'other'
        if (!acc[module]) {
          acc[module] = []
        }
        acc[module].push(permission)
        return acc
      }, {})
      
      return {
        success: response.success,
        data: grouped,
        message: response.message,
        errors: response.errors
      }
    }
    return {
      success: false,
      data: {},
      message: response.message || 'Failed to fetch permissions',
      errors: response.errors
    }
  }

  /**
   * Assign permissions to a role
   */
  async assignPermissionsToRole(roleId: number, permissions: string[]): Promise<ApiResponse<Role>> {
    return apiClient.post<Role>(`${this.BASE_URL}/roles/assign-permissions`, {
      roleId,
      permissionNames: permissions
    })
  }
}

export const roleService = new RoleService() 