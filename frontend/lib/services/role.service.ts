import axios from 'axios';
import { authHeader } from './auth-header';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';

class RoleService {
  getRoles() {
    return axios.get(`${API_URL}/systems/roles`, { headers: authHeader() });
  }

  createRole(role: { name: string; description: string }) {
    return axios.post(`${API_URL}/systems/roles`, role, { headers: authHeader() });
  }

  getPermissions() {
    return axios.get(`${API_URL}/systems/permissions`, { headers: authHeader() });
  }

  assignPermissionsToRole(roleId: string, permissions: string[]) {
    return axios.post(`${API_URL}/systems/roles/assign-permissions`, { roleId, permissions }, { headers: authHeader() });
  }
}

export const roleService = new RoleService(); 