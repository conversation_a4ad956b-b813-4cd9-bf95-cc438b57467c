"use client"

import { authService } from "./auth"

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  errors?: Record<string, string[]>
}

export interface PaginatedResponse<T> {
  products: T[]
  total: number
  currentPage: number
  pageSize: number
  totalPages: number
}

class ApiClient {
  private baseUrl: string

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000/api"
    console.log("[ApiClient] Base URL inicializada:", this.baseUrl)
  }

  private getAuthHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      "Content-Type": "application/json"
    }
    const token = authService.getToken()
    if (token) {
      headers["Authorization"] = `Bearer ${token}`
    }
    return headers
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    if (!endpoint || typeof endpoint !== "string" || endpoint === "undefined") {
      throw new Error(`[ApiClient] Endpoint inválido: ${endpoint}`);
    }
    const url = `${this.baseUrl}${endpoint}`
    console.log(`[ApiClient] Making request to: ${url}`)
    const headers = {
      ...this.getAuthHeaders(),
      ...options.headers
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers
      })

      console.log(`[ApiClient] Response status: ${response.status} for ${url}`)

      if (!response.ok) {
        console.error(`[ApiClient] Error response: ${response.status} ${response.statusText}`)
        const errorText = await response.text()
        console.error(`[ApiClient] Error body:`, errorText)
        
        if (response.status === 401) {
          // Handle unauthorized - could implement token refresh here
          console.log('[ApiClient] Unauthorized access detected')
        }
        
        return {
          success: false,
          data: undefined,
          message: `HTTP ${response.status}: ${response.statusText}`,
          errors: { general: [errorText] }
        }
      }

      if (response.status === 204) {
        // No Content: no intentes parsear JSON
        return {
          success: true,
          data: undefined,
          message: 'No Content'
        }
      }

      const responseData = await response.json()
      console.log(`[ApiClient] Response data for ${url}:`, responseData)

      // Si la respuesta del backend ya tiene el formato ApiResponse
      if (responseData.hasOwnProperty('success')) {
        return responseData
      }

      // Si es una respuesta directa, envolvemos en el formato ApiResponse
      return {
        success: true,
        data: responseData,
        message: 'Success'
      }
    } catch (error) {
      console.error(`[ApiClient] Network error for ${url}:`, error)
      return {
        success: false,
        data: undefined,
        message: error instanceof Error ? error.message : 'Network error',
        errors: { general: [error instanceof Error ? error.message : 'Unknown error'] }
      }
    }
  }

  // Métodos HTTP básicos
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "GET" })
  }
  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "POST", body: data ? JSON.stringify(data) : undefined })
  }
  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "PUT", body: data ? JSON.stringify(data) : undefined })
  }
  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "PATCH", body: data ? JSON.stringify(data) : undefined })
  }
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "DELETE" })
  }
}

export const apiClient = new ApiClient()

