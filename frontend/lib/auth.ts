"use client"

import { jwtDecode } from "jwt-decode"

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'

console.log('[AUTH SERVICE] API_URL configurado como:', API_URL)

// Configuración del backend

export interface User {
  id: number
  name: string
  email: string
  active: boolean
  roles: string[]
  permissions?: string[]
  roleDetails?: any[]
  createdAt?: string
  updatedAt?: string
}

export interface JWTPayload {
  sub: string
  userId: number
  email: string
  active: boolean
  roles: string[]
  iat: number
  exp: number
}

export interface LoginCredentials {
  credential: string
  password: string
}

export interface AuthResponse {
  success: boolean
  token: string
  user: User
}

class AuthService {
  private readonly TOKEN_KEY = "auth_token"
  private readonly USER_KEY = "user_data"
  private readonly REFRESH_TOKEN_KEY = "refresh_token"

  async refreshToken(): Promise<string | null> {
    console.log('Attempting to refresh token...');
    const refreshToken = typeof window !== 'undefined' ? localStorage.getItem(this.REFRESH_TOKEN_KEY) : null;
    
    if (!refreshToken) {
      console.log('No refresh token found');
      return null;
    }

    try {
      console.log('Sending refresh token request to:', `${API_URL}/auth/refresh-token`);
      const response = await fetch(`${API_URL}/auth/refresh-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ refreshToken }),
        credentials: 'include' // Por si usas cookies httpOnly
      });

      const responseData = await response.text();
      console.log('Refresh token response status:', response.status);
      console.log('Refresh token response data:', responseData);

      if (!response.ok) {
        // Intenta parsear el error para mostrar mensaje útil
        try {
          const errorData = JSON.parse(responseData);
          throw new Error(errorData.message || `Failed to refresh token: ${response.status} ${response.statusText}`);
        } catch (e) {
          throw new Error(`Failed to refresh token: ${response.status} ${response.statusText}`);
        }
      }

      let data;
      try {
        data = JSON.parse(responseData);
      } catch (e) {
        console.error('Failed to parse refresh token response:', e);
        throw new Error('Invalid response format from server');
      }

      // El backend puede responder con { accessToken, refreshToken, user } o { token, refreshToken }
      const newToken = data.accessToken || data.token;
      if (!newToken) {
        throw new Error('No token received in refresh response');
      }

      console.log('Token refresh successful');
      this.setToken(newToken);
      // Actualiza refresh token si el backend lo envía
      if (data.refreshToken) {
        console.log('New refresh token received, updating...');
        this.setRefreshToken(data.refreshToken);
      }
      // Actualiza usuario si viene en la respuesta
      if (data.user) {
        this.setUser(data.user);
      }
      return newToken;
    } catch (error) {
      console.error('Error refreshing token:', error);
      this.clearAuth();
      return null;
    }
  }

  private setRefreshToken(refreshToken: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
    }
  }

  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const loginUrl = `${API_URL}/auth/login`;
      console.log('[AUTH SERVICE] Intentando login en:', loginUrl);
      console.log('[AUTH SERVICE] Credenciales:', { credential: credentials.credential, password: '***' });
      
      const response = await fetch(loginUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(credentials),
        credentials: 'include' // Important for cookies if using httpOnly refresh tokens
      })

      console.log('[AUTH SERVICE] Response status:', response.status);
      console.log('[AUTH SERVICE] Response ok:', response.ok);

      if (!response.ok) {
        const error = await response.json();
        console.error('Server error on login:', error);
        throw new Error(error.message || 'Error de autenticación');
      }

      const data = await response.json();
      console.log('[AUTH SERVICE] Login response data:', data);

      if (!data.success) {
        console.error('Authentication failed:', data.message);
        throw new Error(data.message || 'Authentication failed');
      }

      if (!data.token || !data.user) {
        throw new Error('Invalid response from server')
      }

      this.setToken(data.token)
      this.setUser(data.user)
      
      // Store refresh token if provided
      if (data.refreshToken) {
        this.setRefreshToken(data.refreshToken)
      }
      
      return { success: true, token: data.token, user: data.user }
    } catch (error) {
      console.error("Login error:", error)
      throw error
    }
  }

  async logout(): Promise<void> {
    try {
      const token = this.getToken()
      const refreshToken = typeof window !== 'undefined' ? localStorage.getItem(this.REFRESH_TOKEN_KEY) : null;
      if (token && refreshToken) {
        await fetch(`${API_URL}/auth/logout`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({ refreshToken })
        })
      }
    } catch (error) {
      console.error("Logout error:", error)
    } finally {
      this.clearAuth()
    }
  }

  getToken(): string | null {
    if (typeof window === "undefined") return null
    return localStorage.getItem(this.TOKEN_KEY)
  }

  getUser(): User | null {
    if (typeof window === "undefined") return null
    const userData = localStorage.getItem(this.USER_KEY)
    return userData ? JSON.parse(userData) : null
  }

  setToken(token: string): void {
    if (typeof window !== "undefined") {
      localStorage.setItem(this.TOKEN_KEY, token)
    }
  }

  setUser(user: User): void {
    if (typeof window !== "undefined") {
      localStorage.setItem(this.USER_KEY, JSON.stringify(user))
    }
  }

  clearAuth(): void {
    if (typeof window !== "undefined") {
      localStorage.removeItem(this.TOKEN_KEY)
      localStorage.removeItem(this.USER_KEY)
      localStorage.removeItem(this.REFRESH_TOKEN_KEY)
    }
  }

  isAuthenticated(): boolean {
    const token = this.getToken()
    if (!token) return false

    try {
      const decoded = jwtDecode<JWTPayload>(token)
      const now = Date.now() / 1000
      return decoded.exp > now
    } catch {
      return false
    }
  }

  hasPermission(permission: string): boolean {
    const user = this.getUser()
    
    // Si el usuario tiene ROLE_ADMIN, permitir todo
    if (user?.roles?.includes('ROLE_ADMIN')) {
      return true
    }
    
    // Verificar en el array de permisos específicos
    return user?.permissions?.includes(permission) || false
  }

  hasRole(role: string): boolean {
    const user = this.getUser()
    return user?.roles?.includes(role) || false
  }

  hasAnyRole(roles: string[]): boolean {
    const user = this.getUser()
    return roles.some(role => user?.roles?.includes(role)) || false
  }

  canAccessModule(module: string): boolean {
    const user = this.getUser()
    return user?.roles?.some(role => role.includes(module)) || false
  }

  async authenticatedFetch(url: string, options: RequestInit = {}): Promise<Response> {
    const token = this.getToken()
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string> || {}),
    }
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }
    
    const response = await fetch(url, {
      ...options,
      headers,
    })
    
    if (response.status === 401) {
      const newToken = await this.refreshToken()
      if (newToken) {
        headers['Authorization'] = `Bearer ${newToken}`
        return fetch(url, {
          ...options,
          headers,
        })
      } else {
        this.clearAuth()
        throw new Error('Authentication failed')
      }
    }
    
    return response
  }
}

export const authService = new AuthService()
export default authService
