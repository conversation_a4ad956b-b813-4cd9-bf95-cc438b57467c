import { createClient } from '@supabase/supabase-js';

// Asegúrate de que estas variables de entorno estén definidas en tu archivo .env.local
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Falta configurar las variables de entorno de Supabase. Por favor, revisa tu archivo .env.local');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Función de utilidad para manejar errores
export const handleSupabaseError = (error: any) => {
  console.error('Error de Supabase:', error);
  throw error;
};
