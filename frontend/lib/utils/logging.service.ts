import { isDev } from '@/lib/utils';

enum LogLevel {
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
  DEBUG = 'DEBUG',
}

class LoggingService {
  private log(level: LogLevel, message: string, context?: object) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level}] ${message}`;

    if (isDev()) {
      switch (level) {
        case LogLevel.INFO:
          console.info(logMessage, context || '');
          break;
        case LogLevel.WARN:
          console.warn(logMessage, context || '');
          break;
        case LogLevel.ERROR:
          console.error(logMessage, context || '');
          break;
        case LogLevel.DEBUG:
          console.debug(logMessage, context || '');
          break;
        default:
          console.log(logMessage, context || '');
      }
    }
    // En un entorno de producción, podrías enviar los logs a un servicio externo
    // como Sentry, LogRocket, etc.
  }

  info(message: string, context?: object) {
    this.log(LogLevel.INFO, message, context);
  }

  warn(message: string, context?: object) {
    this.log(LogLevel.WARN, message, context);
  }

  error(message: string, context?: object) {
    this.log(LogLevel.ERROR, message, context);
  }

  debug(message: string, context?: object) {
    this.log(LogLevel.DEBUG, message, context);
  }
}

export const loggingService = new LoggingService();