export interface PasswordPolicyOptions {
  minLength?: number;
  maxLength?: number;
  requireUppercase?: boolean;
  requireLowercase?: boolean;
  requireNumbers?: boolean;
  requireSpecialChars?: boolean;
  forbiddenPatterns?: RegExp[];
  forbiddenWords?: string[];
  maxConsecutiveChars?: number;
  minUniqueChars?: number;
}

const DEFAULT_PASSWORD_POLICY: PasswordPolicyOptions = {
  minLength: 8,
  maxLength: 128,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  forbiddenPatterns: [
    /(.)\1{2,}/, // No más de 2 caracteres consecutivos iguales
    /123456/, // Secuencias numéricas simples
    /abcdef/, // Secuencias alfabéticas simples
    /qwerty/i, // Patrones de teclado
    /password/i, // Palabra "password"
    /admin/i, // Palabra "admin"
  ],
  forbiddenWords: [
    'comintec',
    'empresa',
    'usuario',
    'contraseña',
    'password',
    'admin',
    'administrador',
  ],
  maxConsecutiveChars: 2,
  minUniqueChars: 6,
};

export interface PasswordValidationResult {
  isValid: boolean;
  errors: string[];
  requirements: PasswordRequirement[];
}

export interface PasswordRequirement {
  id: string;
  description: string;
  isValid: boolean;
  required: boolean;
}

export class PasswordPolicyValidator {
  private options: PasswordPolicyOptions;

  constructor(options?: PasswordPolicyOptions) {
    this.options = { ...DEFAULT_PASSWORD_POLICY, ...(options || {}) };
  }

  validate(password: string): PasswordValidationResult {
    const errors = this.getPasswordErrors(password);
    const requirements = this.getPasswordRequirements(password);
    
    return {
      isValid: errors.length === 0,
      errors,
      requirements,
    };
  }

  private getPasswordErrors(password: string): string[] {
    const errors: string[] = [];

    // Validar longitud
    if (this.options.minLength && password.length < this.options.minLength) {
      errors.push(`mínimo ${this.options.minLength} caracteres`);
    }
    if (this.options.maxLength && password.length > this.options.maxLength) {
      errors.push(`máximo ${this.options.maxLength} caracteres`);
    }

    // Validar requisitos de caracteres
    if (this.options.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('al menos una letra mayúscula');
    }
    if (this.options.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('al menos una letra minúscula');
    }
    if (this.options.requireNumbers && !/\d/.test(password)) {
      errors.push('al menos un número');
    }
    if (this.options.requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('al menos un carácter especial');
    }

    // Validar patrones prohibidos
    if (this.options.forbiddenPatterns) {
      for (const pattern of this.options.forbiddenPatterns) {
        if (pattern.test(password)) {
          errors.push('no debe contener patrones comunes o secuencias');
          break;
        }
      }
    }

    // Validar palabras prohibidas
    if (this.options.forbiddenWords) {
      const lowerPassword = password.toLowerCase();
      for (const word of this.options.forbiddenWords) {
        if (lowerPassword.includes(word.toLowerCase())) {
          errors.push('no debe contener palabras comunes o relacionadas con la empresa');
          break;
        }
      }
    }

    // Validar caracteres consecutivos
    if (this.options.maxConsecutiveChars && this.options.maxConsecutiveChars > 0) {
      const consecutivePattern = new RegExp(`(.)\\1{${this.options.maxConsecutiveChars},}`);
      if (consecutivePattern.test(password)) {
        errors.push(`no debe tener más de ${this.options.maxConsecutiveChars} caracteres consecutivos iguales`);
      }
    }

    // Validar caracteres únicos
    if (this.options.minUniqueChars && this.options.minUniqueChars > 0) {
      const uniqueChars = new Set(password).size;
      if (uniqueChars < this.options.minUniqueChars) {
        errors.push(`debe tener al menos ${this.options.minUniqueChars} caracteres únicos`);
      }
    }

    return errors;
  }

  private getPasswordRequirements(password: string): PasswordRequirement[] {
    const requirements: PasswordRequirement[] = [];

    // Longitud mínima
    if (this.options.minLength) {
      requirements.push({
        id: 'minLength',
        description: `Al menos ${this.options.minLength} caracteres`,
        isValid: password.length >= this.options.minLength,
        required: true,
      });
    }

    // Mayúscula
    if (this.options.requireUppercase) {
      requirements.push({
        id: 'uppercase',
        description: 'Al menos una letra mayúscula (A-Z)',
        isValid: /[A-Z]/.test(password),
        required: true,
      });
    }

    // Minúscula
    if (this.options.requireLowercase) {
      requirements.push({
        id: 'lowercase',
        description: 'Al menos una letra minúscula (a-z)',
        isValid: /[a-z]/.test(password),
        required: true,
      });
    }

    // Número
    if (this.options.requireNumbers) {
      requirements.push({
        id: 'numbers',
        description: 'Al menos un número (0-9)',
        isValid: /\d/.test(password),
        required: true,
      });
    }

    // Carácter especial
    if (this.options.requireSpecialChars) {
      requirements.push({
        id: 'specialChars',
        description: 'Al menos un carácter especial (!@#$%^&*)',
        isValid: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password),
        required: true,
      });
    }

    // Caracteres únicos
    if (this.options.minUniqueChars) {
      const uniqueChars = new Set(password).size;
      requirements.push({
        id: 'uniqueChars',
        description: `Al menos ${this.options.minUniqueChars} caracteres únicos`,
        isValid: uniqueChars >= this.options.minUniqueChars,
        required: true,
      });
    }

    // Patrones prohibidos
    requirements.push({
      id: 'forbiddenPatterns',
      description: 'No contener patrones comunes (password, admin, 123456, etc.)',
      isValid: !this.hasForbiddenPatterns(password),
      required: true,
    });

    return requirements;
  }

  private hasForbiddenPatterns(password: string): boolean {
    // Verificar patrones prohibidos
    if (this.options.forbiddenPatterns) {
      for (const pattern of this.options.forbiddenPatterns) {
        if (pattern.test(password)) {
          return true;
        }
      }
    }

    // Verificar palabras prohibidas
    if (this.options.forbiddenWords) {
      const lowerPassword = password.toLowerCase();
      for (const word of this.options.forbiddenWords) {
        if (lowerPassword.includes(word.toLowerCase())) {
          return true;
        }
      }
    }

    return false;
  }

  // Método para generar una contraseña segura
  generateSecurePassword(length: number = 12): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    
    let password = '';
    let allChars = '';
    
    // Asegurar que se incluya al menos un carácter de cada tipo requerido
    if (this.options.requireLowercase) {
      password += this.getRandomChar(lowercase);
      allChars += lowercase;
    }
    if (this.options.requireUppercase) {
      password += this.getRandomChar(uppercase);
      allChars += uppercase;
    }
    if (this.options.requireNumbers) {
      password += this.getRandomChar(numbers);
      allChars += numbers;
    }
    if (this.options.requireSpecialChars) {
      password += this.getRandomChar(specialChars);
      allChars += specialChars;
    }
    
    // Completar la longitud restante
    for (let i = password.length; i < length; i++) {
      password += this.getRandomChar(allChars);
    }
    
    // Mezclar los caracteres para evitar patrones predecibles
    return this.shuffleString(password);
  }
  
  private getRandomChar(chars: string): string {
    return chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  private shuffleString(str: string): string {
    return str.split('').sort(() => Math.random() - 0.5).join('');
  }
}

// Instancia por defecto
export const passwordValidator = new PasswordPolicyValidator();

// Función de utilidad para validación rápida
export const validatePassword = (password: string): PasswordValidationResult => {
  return passwordValidator.validate(password);
};

// Función para generar contraseña segura
export const generateSecurePassword = (length: number = 12): string => {
  return passwordValidator.generateSecurePassword(length);
};

// Función para generar contraseñas de ejemplo que cumplan las políticas
export const generateExamplePasswords = (): string[] => {
  return [
    'Secure123!',
    'MyStr0ng#',
    'C0mpl3x$',
    'S4f3ty&9',
    'Pr0t3ct@',
    'V4lid8*',
    'Str0ng#2',
    'S3cur3!9'
  ];
};
