# Clean Form Styling - G<PERSON><PERSON> de Uso

## 🎯 Objetivo

Este sistema proporciona componentes de formulario reutilizables con estilo blanco/limpio consistente, similar al que se ve en BackupView. Todos los componentes automáticamente aplican el estilo correcto sin necesidad de especificar clases CSS manualmente.

## 🚀 Componentes Disponibles

### 1. Componentes Básicos Limpios

```tsx
import { 
  CleanInput, 
  CleanTextarea, 
  CleanSelect, 
  CleanSelectTrigger, 
  CleanSelectContent, 
  CleanSelectItem,
  CleanButton,
  CleanPopoverContent,
  CleanCalendar 
} from "@/components/ui/form-styles"
```

### 2. Uso de Input Limpio

**❌ Antes (manual):**
```tsx
<Input 
  className="bg-white/30 border-gray-200 text-gray-900 placeholder:text-gray-500 dark:bg-slate-800/30 dark:border-slate-700 dark:text-slate-100 dark:placeholder:text-slate-400"
  placeholder="Ingresa texto..."
/>
```

**✅ Ahora (automático):**
```tsx
<CleanInput placeholder="Ingresa texto..." />
```

### 3. Uso de Textarea Limpio

**❌ Antes:**
```tsx
<Textarea 
  className="bg-white/30 border-gray-200 text-gray-900 placeholder:text-gray-500 dark:bg-slate-800/30 dark:border-slate-700 dark:text-slate-100 dark:placeholder:text-slate-400"
  placeholder="Describe..."
/>
```

**✅ Ahora:**
```tsx
<CleanTextarea placeholder="Describe..." />
```

### 4. Uso de Select Limpio

**❌ Antes:**
```tsx
<Select>
  <SelectTrigger className="bg-white/30 border-gray-200 text-gray-900 dark:bg-slate-800/30 dark:border-slate-700 dark:text-slate-100">
    <SelectValue placeholder="Selecciona..." />
  </SelectTrigger>
  <SelectContent className="bg-white border-gray-200 dark:bg-slate-800 dark:border-slate-700">
    <SelectItem value="option1" className="text-gray-900 dark:text-slate-100">Opción 1</SelectItem>
  </SelectContent>
</Select>
```

**✅ Ahora:**
```tsx
<CleanSelect>
  <CleanSelectTrigger>
    <SelectValue placeholder="Selecciona..." />
  </CleanSelectTrigger>
  <CleanSelectContent>
    <CleanSelectItem value="option1">Opción 1</CleanSelectItem>
  </CleanSelectContent>
</CleanSelect>
```

### 5. Uso de Date Picker Limpio

**❌ Antes:**
```tsx
<Popover>
  <PopoverTrigger asChild>
    <Button variant="outline" className="bg-white/30 border-gray-200 text-gray-900 hover:bg-white/40 dark:bg-slate-800/30 dark:border-slate-700 dark:text-slate-100">
      {date ? format(date, "PPP") : "Selecciona fecha"}
    </Button>
  </PopoverTrigger>
  <PopoverContent className="bg-white border-gray-200 dark:bg-slate-800 dark:border-slate-700">
    <Calendar className="text-gray-900 dark:text-slate-100" />
  </PopoverContent>
</Popover>
```

**✅ Ahora:**
```tsx
<Popover>
  <PopoverTrigger asChild>
    <CleanButton>
      {date ? format(date, "PPP") : "Selecciona fecha"}
    </CleanButton>
  </PopoverTrigger>
  <CleanPopoverContent>
    <CleanCalendar />
  </CleanPopoverContent>
</Popover>
```

## 🎨 Estilos Aplicados Automáticamente

### Light Mode:
- **Background**: `bg-white/30` (blanco semi-transparente)
- **Border**: `border-gray-200` (gris claro)
- **Text**: `text-gray-900` (gris oscuro)
- **Placeholder**: `text-gray-500` (gris medio)

### Dark Mode:
- **Background**: `bg-slate-800/30` (slate semi-transparente)
- **Border**: `border-slate-700` (slate oscuro)
- **Text**: `text-slate-100` (slate claro)
- **Placeholder**: `text-slate-400` (slate medio)

## 🔧 Personalización

Puedes seguir agregando clases adicionales si necesitas personalización específica:

```tsx
<CleanInput 
  placeholder="Email" 
  className="w-full md:w-1/2" // Clases adicionales
/>
```

## 📋 Migración de Formularios Existentes

### Paso 1: Importar componentes limpios
```tsx
import { CleanInput, CleanTextarea, CleanSelect, CleanSelectTrigger, CleanSelectContent, CleanSelectItem } from "@/components/ui/form-styles"
```

### Paso 2: Reemplazar componentes
- `Input` → `CleanInput`
- `Textarea` → `CleanTextarea`
- `SelectTrigger` → `CleanSelectTrigger`
- `SelectContent` → `CleanSelectContent`
- `SelectItem` → `CleanSelectItem`

### Paso 3: Remover clases CSS manuales
Elimina todas las clases relacionadas con colores de fondo, bordes y texto que estaban aplicadas manualmente.

## ✅ Beneficios

1. **Consistencia**: Todos los formularios tienen el mismo estilo automáticamente
2. **Mantenibilidad**: Un solo lugar para cambiar estilos globalmente
3. **Productividad**: No necesitas recordar/copiar clases CSS largas
4. **Menos errores**: Reduce inconsistencias visuales
5. **Responsive**: Automáticamente funciona en light/dark mode

## 🚨 Importante

- Los estilos CSS globales en `frontend/styles/form-components.css` también aplican automáticamente
- El `FormCard` ya usa el estilo blanco limpio por defecto
- Los componentes mantienen todas las props originales de shadcn/ui
