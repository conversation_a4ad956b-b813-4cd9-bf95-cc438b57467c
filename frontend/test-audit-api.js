const axios = require('axios');

// Configuración
const API_URL = 'http://localhost:3000/api';

async function testAuditAPI() {
  try {
    console.log('🧪 Probando endpoint de auditoría...');
    
    // Primero necesitamos hacer login para obtener el token
    console.log('🔐 Haciendo login...');
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      credential: '<EMAIL>',
      password: 'password'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ Login exitoso, token obtenido');
    
    // Ahora probamos el endpoint de auditoría
    console.log('📋 Consultando logs de auditoría...');
    const auditResponse = await axios.get(`${API_URL}/systems/audit`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Respuesta del API de auditoría:');
    console.log('Status:', auditResponse.status);
    console.log('Headers:', auditResponse.headers['content-type']);
    
    const auditData = auditResponse.data;
    console.log('📊 Datos recibidos:');
    console.log('- Estructura:', Object.keys(auditData));
    
    if (auditData.data && Array.isArray(auditData.data)) {
      console.log('- Total de registros:', auditData.data.length);
      
      if (auditData.data.length > 0) {
        console.log('\n📝 Primer registro de ejemplo:');
        const firstLog = auditData.data[0];
        console.log('- ID:', firstLog.id);
        console.log('- Acción:', firstLog.action);
        console.log('- Entidad:', firstLog.target_entity);
        console.log('- Usuario:', firstLog.user?.name || firstLog.user?.email || 'N/A');
        console.log('- Fecha:', firstLog.created_at);
        console.log('- Detalles:', firstLog.details ? 'Presentes' : 'No hay');
        
        console.log('\n📝 Últimos 5 registros:');
        auditData.data.slice(0, 5).forEach((log, index) => {
          console.log(`${index + 1}. ${log.action} - ${log.target_entity} - ${log.user?.name || 'N/A'} - ${new Date(log.created_at).toLocaleString()}`);
        });
      } else {
        console.log('❌ No hay registros de auditoría');
      }
    } else {
      console.log('❌ Formato de respuesta inesperado:', auditData);
    }
    
    // Probar con filtros
    console.log('\n🔍 Probando con filtros...');
    const filteredResponse = await axios.get(`${API_URL}/systems/audit?search=user`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Respuesta con filtro "user":');
    console.log('- Registros encontrados:', filteredResponse.data.data?.length || 0);
    
  } catch (error) {
    console.error('❌ Error en la prueba:', error.message);
    
    if (error.response) {
      console.error('- Status:', error.response.status);
      console.error('- Data:', error.response.data);
    }
    
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 El servidor backend no está corriendo en el puerto 3000');
    }
  }
}

// Ejecutar la prueba
testAuditAPI();
