/* Enhanced Form Components Styles */

/* Custom Checkbox Styles */
.custom-checkbox {
  @apply relative inline-flex items-center justify-center;
  @apply h-4 w-4 rounded border-2 transition-all duration-200;
  @apply cursor-pointer select-none;
}

.custom-checkbox:not(.checked) {
  border: 2px solid #94a3b8 !important;
  background-color: #ffffff !important;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05) !important;
}

.custom-checkbox:not(.checked):hover {
  border-color: hsl(var(--primary) / 0.6) !important;
  background-color: hsl(var(--primary) / 0.05) !important;
}

.dark .custom-checkbox:not(.checked) {
  @apply border-white/60 bg-white/10;
  @apply hover:border-primary/70 hover:bg-primary/10;
}

.custom-checkbox.checked {
  @apply bg-primary border-primary text-primary-foreground;
  @apply shadow-sm;
}

.custom-checkbox.disabled {
  @apply cursor-not-allowed opacity-50;
}

/* Checkbox Check Mark */
.custom-checkbox-check {
  @apply h-3 w-3 text-current opacity-0 transition-opacity duration-150;
}

.custom-checkbox.checked .custom-checkbox-check {
  @apply opacity-100;
}

/* Form Section Styles */
.form-section {
  @apply space-y-4;
}

.form-section-header {
  @apply space-y-1 pb-3 border-b border-border/50;
}

.form-section-title {
  @apply text-lg font-semibold leading-none tracking-tight text-foreground;
}

.form-section-description {
  @apply text-sm text-muted-foreground leading-relaxed;
}

/* Permission Card Styles */
.permission-card {
  @apply flex items-start space-x-3 p-4 rounded-lg border transition-all duration-200;
  @apply cursor-pointer select-none;
}

.permission-card:not(.selected) {
  @apply border-border bg-background;
  @apply hover:border-border hover:bg-accent/30;
}

.dark .permission-card:not(.selected) {
  @apply border-white/40 bg-white/5;
  @apply hover:border-white/50 hover:bg-white/10;
}

.permission-card.selected {
  @apply border-primary/50 bg-primary/5 shadow-sm;
}

.dark .permission-card.selected {
  @apply border-primary/60 bg-primary/10;
}

.permission-card.disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* Permission Label Styles */
.permission-label {
  @apply text-sm font-medium leading-none cursor-pointer select-none;
  @apply transition-colors duration-150;
}

.permission-description {
  @apply text-xs text-muted-foreground leading-relaxed mt-1;
}

/* Role Card Styles */
.role-card {
  @apply rounded-lg border transition-all duration-200 cursor-pointer;
  @apply p-4 space-y-2;
}

.role-card:not(.selected) {
  @apply border-border bg-card;
  @apply hover:border-border/80 hover:bg-accent/50;
}

.role-card.selected {
  @apply border-primary bg-primary/5 shadow-sm ring-1 ring-primary/20;
}

/* Stats Progress Bar */
.stats-progress {
  @apply w-16 h-2 bg-muted rounded-full overflow-hidden;
}

.stats-progress-fill {
  @apply h-full bg-primary transition-all duration-300 ease-out;
}

/* Button Group Styles */
.button-group {
  @apply flex items-center gap-2;
}

/* Enhanced Badge Styles */
.badge-enhanced {
  @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  @apply transition-colors duration-150;
}

.badge-secondary {
  @apply bg-secondary text-secondary-foreground;
}

.badge-success {
  @apply bg-green-100 text-green-800;
  @apply dark:bg-green-900 dark:text-green-300;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
  @apply dark:bg-yellow-900 dark:text-yellow-300;
}

.badge-info {
  @apply bg-blue-100 text-blue-800;
  @apply dark:bg-blue-900 dark:text-blue-300;
}

/* Module Header Styles */
.module-header {
  @apply flex items-center justify-between pb-3 border-b border-border/50;
}

.module-info {
  @apply space-y-1;
}

.module-title {
  @apply text-base font-semibold text-foreground;
}

.module-stats {
  @apply text-sm text-muted-foreground;
}

.module-actions {
  @apply flex items-center gap-2;
}

/* Responsive Grid Layouts */
.permission-grid {
  @apply grid grid-cols-1 gap-3;
}

@media (min-width: 640px) {
  .permission-grid {
    @apply grid-cols-2;
  }
}

@media (min-width: 1024px) {
  .permission-grid {
    @apply grid-cols-1;
  }
}

@media (min-width: 1280px) {
  .permission-grid {
    @apply grid-cols-2;
  }
}

/* Focus States */
.custom-checkbox:focus-visible {
  @apply ring-2 ring-ring ring-offset-2 ring-offset-background;
}

.permission-card:focus-visible {
  @apply ring-2 ring-ring ring-offset-2 ring-offset-background;
}

/* Animation Classes */
.fade-in {
  @apply animate-in fade-in-0 duration-200;
}

.slide-in {
  @apply animate-in slide-in-from-bottom-2 duration-200;
}

/* Loading States */
.loading-shimmer {
  @apply animate-pulse bg-muted rounded;
}

/* Empty States */
.empty-state {
  @apply flex flex-col items-center justify-center text-center space-y-4;
  @apply py-12 px-4;
}

.empty-state-icon {
  @apply p-4 rounded-full bg-muted/50;
}

.empty-state-title {
  @apply text-lg font-medium text-muted-foreground;
}

.empty-state-description {
  @apply text-sm text-muted-foreground max-w-sm;
}

/* ============================================================================
   GLOBAL FORM COMPONENT OVERRIDES - WHITE/CLEAN STYLING
   ============================================================================ */

/* Input components - Override default shadcn styles for white/clean look */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="search"] {
  background-color: rgb(255 255 255 / 0.3) !important; /* white/30 */
  border-color: rgb(229 231 235) !important; /* gray-200 */
  color: rgb(17 24 39) !important; /* gray-900 */
}

input::placeholder {
  color: rgb(107 114 128) !important; /* gray-500 */
}

/* Dark mode inputs */
.dark input[type="text"],
.dark input[type="email"],
.dark input[type="password"],
.dark input[type="number"],
.dark input[type="tel"],
.dark input[type="url"],
.dark input[type="search"] {
  background-color: rgb(51 65 85 / 0.3) !important; /* slate-700/30 */
  border-color: rgb(51 65 85) !important; /* slate-700 */
  color: rgb(248 250 252) !important; /* slate-100 */
}

.dark input::placeholder {
  color: rgb(148 163 184) !important; /* slate-400 */
}

/* Textarea components */
textarea {
  background-color: rgb(255 255 255 / 0.3) !important; /* white/30 */
  border-color: rgb(229 231 235) !important; /* gray-200 */
  color: rgb(17 24 39) !important; /* gray-900 */
}

textarea::placeholder {
  color: rgb(107 114 128) !important; /* gray-500 */
}

/* Dark mode textareas */
.dark textarea {
  background-color: rgb(51 65 85 / 0.3) !important; /* slate-700/30 */
  border-color: rgb(51 65 85) !important; /* slate-700 */
  color: rgb(248 250 252) !important; /* slate-100 */
}

.dark textarea::placeholder {
  color: rgb(148 163 184) !important; /* slate-400 */
}

/* Select components - Radix UI selects */
[data-radix-select-trigger] {
  background-color: rgb(255 255 255 / 0.3) !important; /* white/30 */
  border-color: rgb(229 231 235) !important; /* gray-200 */
  color: rgb(17 24 39) !important; /* gray-900 */
}

/* Dark mode selects */
.dark [data-radix-select-trigger] {
  background-color: rgb(51 65 85 / 0.3) !important; /* slate-700/30 */
  border-color: rgb(51 65 85) !important; /* slate-700 */
  color: rgb(248 250 252) !important; /* slate-100 */
}

/* Select content/dropdown */
[data-radix-select-content] {
  background-color: rgb(255 255 255) !important; /* white */
  border-color: rgb(229 231 235) !important; /* gray-200 */
}

.dark [data-radix-select-content] {
  background-color: rgb(51 65 85) !important; /* slate-700 */
  border-color: rgb(51 65 85) !important; /* slate-700 */
}

/* Select items */
[data-radix-select-item] {
  color: rgb(17 24 39) !important; /* gray-900 */
}

.dark [data-radix-select-item] {
  color: rgb(248 250 252) !important; /* slate-100 */
}

/* Button components - Date pickers, etc */
button[data-radix-popover-trigger] {
  background-color: rgb(255 255 255 / 0.3) !important; /* white/30 */
  border-color: rgb(229 231 235) !important; /* gray-200 */
  color: rgb(17 24 39) !important; /* gray-900 */
}

button[data-radix-popover-trigger]:hover {
  background-color: rgb(255 255 255 / 0.4) !important; /* white/40 */
}

/* Dark mode buttons */
.dark button[data-radix-popover-trigger] {
  background-color: rgb(51 65 85 / 0.3) !important; /* slate-700/30 */
  border-color: rgb(51 65 85) !important; /* slate-700 */
  color: rgb(248 250 252) !important; /* slate-100 */
}

.dark button[data-radix-popover-trigger]:hover {
  background-color: rgb(51 65 85 / 0.4) !important; /* slate-700/40 */
}

/* Popover content - Calendars, etc */
[data-radix-popover-content] {
  background-color: rgb(255 255 255) !important; /* white */
  border-color: rgb(229 231 235) !important; /* gray-200 */
}

.dark [data-radix-popover-content] {
  background-color: rgb(51 65 85) !important; /* slate-700 */
  border-color: rgb(51 65 85) !important; /* slate-700 */
}

/* Calendar components */
.rdp {
  color: rgb(17 24 39) !important; /* gray-900 */
}

.dark .rdp {
  color: rgb(248 250 252) !important; /* slate-100 */
}
