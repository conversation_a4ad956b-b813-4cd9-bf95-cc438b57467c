{"generatedAt": 1751408074383, "userSince": 1751407726178, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "metaFramework": {"name": "Next", "packageName": "next", "version": "15.2.4"}, "testPackages": {}, "hasRouterPackage": true, "packageManager": {"type": "npm", "agent": "npm"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "portableStoriesFileCount": 0, "applicationFileCount": 67, "language": "typescript", "storybookPackages": {"@storybook/blocks": {"version": "8.6.14"}, "@storybook/react-vite": {"version": "8.6.14"}, "@storybook/test": {"version": "8.6.14"}, "storybook": {"version": "8.6.14"}}, "addons": {"@storybook/addon-essentials": {"version": "8.6.14"}, "@storybook/addon-interactions": {"version": "8.6.14"}, "@storybook/addon-links": {"version": "8.6.14"}}}