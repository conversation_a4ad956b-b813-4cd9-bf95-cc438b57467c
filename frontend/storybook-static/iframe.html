<!doctype html>
<!--suppress HtmlUnknownTarget -->
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Storybook</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <style>
      @font-face {
        font-family: 'Nunito Sans';
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url('./sb-common-assets/nunito-sans-regular.woff2') format('woff2');
      }

      @font-face {
        font-family: 'Nunito Sans';
        font-style: italic;
        font-weight: 400;
        font-display: swap;
        src: url('./sb-common-assets/nunito-sans-italic.woff2') format('woff2');
      }

      @font-face {
        font-family: 'Nunito Sans';
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url('./sb-common-assets/nunito-sans-bold.woff2') format('woff2');
      }

      @font-face {
        font-family: 'Nunito Sans';
        font-style: italic;
        font-weight: 700;
        font-display: swap;
        src: url('./sb-common-assets/nunito-sans-bold-italic.woff2') format('woff2');
      }
    </style>

    <script>
      window.CONFIG_TYPE = 'PRODUCTION';
      window.LOGLEVEL = 'info';
      window.FRAMEWORK_OPTIONS = {};
      window.CHANNEL_OPTIONS = {};
      window.FEATURES = {"argTypeTargetsV7":true,"legacyDecoratorFileOrder":false,"disallowImplicitActionsInRenderV8":true};
      window.STORIES = [{"titlePrefix":"","directory":"./components","files":"**/*.stories.@(js|jsx|ts|tsx|mdx)","importPathMatcher":"^\\.[\\\\/](?:components(?:\\/(?!\\.)(?:(?:(?!(?:^|\\/)\\.).)*?)\\/|\\/|$)(?!\\.)(?=.)[^/]*?\\.stories\\.(js|jsx|ts|tsx|mdx))$"},{"titlePrefix":"","directory":"./app","files":"**/*.stories.@(js|jsx|ts|tsx|mdx)","importPathMatcher":"^\\.[\\\\/](?:app(?:\\/(?!\\.)(?:(?:(?!(?:^|\\/)\\.).)*?)\\/|\\/|$)(?!\\.)(?=.)[^/]*?\\.stories\\.(js|jsx|ts|tsx|mdx))$"}];
      window.DOCS_OPTIONS = {"defaultName":"Docs","autodocs":"tag"};
      window.TAGS_OPTIONS = {"dev-only":{"excludeFromDocsStories":true},"docs-only":{"excludeFromSidebar":true},"test-only":{"excludeFromSidebar":true,"excludeFromDocsStories":true}};

      

      // We do this so that "module && module.hot" etc. in Storybook source code
      // doesn't fail (it will simply be disabled)
      window.module = undefined;
      window.global = window;
    </script>
    <base target="_parent" />

<style>
  /* While we aren't showing the main block yet, but still preparing, we want everything the user
     has rendered, which may or may not be in #storybook-root, to be display none */
  .sb-show-preparing-story:not(.sb-show-main) > :not(.sb-preparing-story) {
    display: none;
  }

  .sb-show-preparing-docs:not(.sb-show-main) > :not(.sb-preparing-docs) {
    display: none;
  }

  /* Hide our own blocks when we aren't supposed to be showing them */
  :not(.sb-show-preparing-story) > .sb-preparing-story,
  :not(.sb-show-preparing-docs) > .sb-preparing-docs,
  :not(.sb-show-nopreview) > .sb-nopreview,
  :not(.sb-show-errordisplay) > .sb-errordisplay {
    display: none;
  }

  .sb-show-main.sb-main-centered {
    margin: 0;
    display: flex;
    align-items: center;
    min-height: 100vh;
  }

  .sb-show-main.sb-main-centered #storybook-root {
    box-sizing: border-box;
    margin: auto;
    padding: 1rem;
    max-height: 100%;
    /* Hack for centering correctly in IE11 */
  }

  /* Vertical centering fix for IE11 */
  @media screen and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    .sb-show-main.sb-main-centered:after {
      content: '';
      min-height: inherit;
      font-size: 0;
    }
  }

  .sb-show-main.sb-main-fullscreen {
    margin: 0;
    padding: 0;
    display: block;
  }

  .sb-show-main.sb-main-padded {
    margin: 0;
    padding: 1rem;
    display: block;
    box-sizing: border-box;
  }

  .sb-wrapper {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    box-sizing: border-box;

    padding: 40px;
    font-family:
      'Nunito Sans',
      -apple-system,
      '.SFNSText-Regular',
      'San Francisco',
      BlinkMacSystemFont,
      'Segoe UI',
      'Helvetica Neue',
      Helvetica,
      Arial,
      sans-serif;
    -webkit-font-smoothing: antialiased;
    overflow: auto;
  }

  @media (max-width: 700px) {
    .sb-wrapper {
      padding: 20px;
    }
  }

  @media (max-width: 500px) {
    .sb-wrapper {
      padding: 10px;
    }
  }

  .sb-heading {
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0.2px;
    margin: 10px 0;
    padding-right: 25px;
  }

  .sb-nopreview {
    display: flex;
    align-content: center;
    justify-content: center;
    box-sizing: border-box;
  }

  .sb-nopreview_main {
    margin: auto;
    padding: 30px;
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.03);
  }

  .sb-nopreview_heading {
    text-align: center;
  }

  .sb-errordisplay {
    background: #f6f9fc;
    color: black;
    z-index: 999999;
    width: 100vw;
    min-height: 100vh;
    box-sizing: border-box;

    & ol {
      padding-left: 18px;
      margin: 0;
    }

    & h1 {
      font-family: Nunito Sans;
      font-size: 22px;
      font-weight: 400;
      line-height: 30px;
      font-weight: normal;
      margin: 0;

      &::before {
        content: '';
        display: inline-block;
        width: 12px;
        height: 12px;
        background: #ff4400;
        border-radius: 50%;
        margin-right: 8px;
      }
    }

    & p,
    & ol {
      font-family: Nunito Sans;
      font-size: 14px;
      font-weight: 400;
      line-height: 19px;
      margin: 0;
    }

    & li + li {
      margin: 0;
      padding: 0;
      padding-top: 12px;
    }

    & a {
      color: currentColor;
    }
  }

  .sb-errordisplay_main {
    margin: auto;
    padding: 24px;
    display: flex;
    box-sizing: border-box;

    flex-direction: column;
    min-height: 100%;
    width: 100%;
    border-radius: 6px;
    background: white;
    border: 1px solid #ff0000;
    box-shadow: 0 0 64px rgba(0, 0, 0, 0.1);
    gap: 24px;
  }

  .sb-errordisplay_code {
    padding: 10px;
    flex: 1;
    background: #242424;
    color: #c6c6c6;
    box-sizing: border-box;

    font-size: 14px;
    font-weight: 400;
    line-height: 19px;
    border-radius: 4px;

    font-family: 'Operator Mono', 'Fira Code Retina', 'Fira Code', 'FiraCode-Retina', 'Andale Mono',
      'Lucida Console', Consolas, Monaco, monospace;
    margin: 0;
    overflow: auto;
  }

  .sb-errordisplay pre {
    white-space: pre-wrap;
    white-space: revert;
  }

  @-webkit-keyframes sb-rotate360 {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  @keyframes sb-rotate360 {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  @-webkit-keyframes sb-glow {
    0%,
    100% {
      opacity: 1;
    }

    50% {
      opacity: 0.4;
    }
  }

  @keyframes sb-glow {
    0%,
    100% {
      opacity: 1;
    }

    50% {
      opacity: 0.4;
    }
  }

  /* We display the preparing loaders *over* the rendering story */
  .sb-preparing-story,
  .sb-preparing-docs {
    background-color: white;
    /* Maximum possible z-index. It would be better to use stacking contexts to ensure it's always
    on top, but this isn't possible as it would require making CSS changes that could affect user code */
    z-index: 2147483647;
  }

  .sb-loader {
    -webkit-animation: sb-rotate360 0.7s linear infinite;
    animation: sb-rotate360 0.7s linear infinite;
    border-color: rgba(97, 97, 97, 0.29);
    border-radius: 50%;
    border-style: solid;
    border-top-color: #646464;
    border-width: 2px;
    display: inline-block;
    height: 32px;
    left: 50%;
    margin-left: -16px;
    margin-top: -16px;
    mix-blend-mode: difference;
    overflow: hidden;
    position: absolute;
    top: 50%;
    transition: all 200ms ease-out;
    vertical-align: top;
    width: 32px;
    z-index: 4;
  }

  .sb-previewBlock {
    background: #fff;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    box-shadow: rgba(0, 0, 0, 0.1) 0 1px 3px 0;
    margin: 25px auto 40px;
    max-width: 600px;
  }

  .sb-previewBlock_header {
    align-items: center;
    box-shadow: rgba(0, 0, 0, 0.1) 0 -1px 0 0 inset;
    display: flex;
    gap: 14px;
    height: 40px;
    padding: 0 12px;
  }

  .sb-previewBlock_icon {
    -webkit-animation: sb-glow 1.5s ease-in-out infinite;
    animation: sb-glow 1.5s ease-in-out infinite;
    background: #e6e6e6;
    height: 14px;
    width: 14px;
  }

  .sb-previewBlock_icon:last-child {
    margin-left: auto;
  }

  .sb-previewBlock_body {
    -webkit-animation: sb-glow 1.5s ease-in-out infinite;
    animation: sb-glow 1.5s ease-in-out infinite;
    height: 182px;
    position: relative;
  }

  .sb-argstableBlock {
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 13px;
    line-height: 20px;
    margin: 25px auto 40px;
    max-width: 600px;
    text-align: left;
    width: 100%;
  }

  .sb-argstableBlock th:first-of-type,
  .sb-argstableBlock td:first-of-type {
    padding-left: 20px;
  }

  .sb-argstableBlock th:nth-of-type(2),
  .sb-argstableBlock td:nth-of-type(2) {
    width: 35%;
  }

  .sb-argstableBlock th:nth-of-type(3),
  .sb-argstableBlock td:nth-of-type(3) {
    width: 15%;
  }

  .sb-argstableBlock th:last-of-type,
  .sb-argstableBlock td:last-of-type {
    width: 25%;
    padding-right: 20px;
  }

  .sb-argstableBlock th span,
  .sb-argstableBlock td span {
    -webkit-animation: sb-glow 1.5s ease-in-out infinite;
    animation: sb-glow 1.5s ease-in-out infinite;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 0;
    box-shadow: none;
    color: transparent;
  }

  .sb-argstableBlock th {
    padding: 10px 15px;
  }

  .sb-argstableBlock-body {
    border-radius: 4px;
    box-shadow:
      rgba(0, 0, 0, 0.1) 0 1px 3px 1px,
      rgba(0, 0, 0, 0.065) 0 0 0 1px;
  }

  .sb-argstableBlock-body tr {
    background: transparent;
    overflow: hidden;
  }

  .sb-argstableBlock-body tr:not(:first-child) {
    border-top: 1px solid #e6e6e6;
  }

  .sb-argstableBlock-body tr:first-child td:first-child {
    border-top-left-radius: 4px;
  }

  .sb-argstableBlock-body tr:first-child td:last-child {
    border-top-right-radius: 4px;
  }

  .sb-argstableBlock-body tr:last-child td:first-child {
    border-bottom-left-radius: 4px;
  }

  .sb-argstableBlock-body tr:last-child td:last-child {
    border-bottom-right-radius: 4px;
  }

  .sb-argstableBlock-body td {
    background: #fff;
    padding-bottom: 10px;
    padding-top: 10px;
    vertical-align: top;
  }

  .sb-argstableBlock-body td:not(:first-of-type) {
    padding-left: 15px;
    padding-right: 15px;
  }

  .sb-argstableBlock-body button {
    -webkit-animation: sb-glow 1.5s ease-in-out infinite;
    animation: sb-glow 1.5s ease-in-out infinite;
    background-color: rgba(0, 0, 0, 0.1);
    border: 0;
    border-radius: 0;
    box-shadow: none;
    color: transparent;
    display: inline;
    font-size: 12px;
    line-height: 1;
    padding: 10px 16px;
  }

  .sb-argstableBlock-summary {
    margin-top: 4px;
  }

  .sb-argstableBlock-code {
    margin-right: 4px;
    margin-bottom: 4px;
    padding: 2px 5px;
  }
</style>

<script>
  /* globals window */
  /* eslint-disable no-underscore-dangle */
  try {
    if (window.top !== window) {
      window.__REACT_DEVTOOLS_GLOBAL_HOOK__ = window.top.__REACT_DEVTOOLS_GLOBAL_HOOK__;
      window.__VUE_DEVTOOLS_GLOBAL_HOOK__ = window.top.__VUE_DEVTOOLS_GLOBAL_HOOK__;
      window.top.__VUE_DEVTOOLS_CONTEXT__ = window.document;
    }
  } catch (e) {
    // eslint-disable-next-line no-console
    console.warn('unable to connect to top frame for connecting dev tools');
  }
</script>

    <script type="module" crossorigin src="./assets/iframe-vtglT4P5.js"></script>
  </head>

  <body>
    <div class="sb-preparing-story sb-wrapper">
  <div class="sb-loader"></div>
</div>

<div class="sb-preparing-docs sb-wrapper">
  <div class="sb-previewBlock">
    <div class="sb-previewBlock_header">
      <div class="sb-previewBlock_icon"></div>
      <div class="sb-previewBlock_icon"></div>
      <div class="sb-previewBlock_icon"></div>
      <div class="sb-previewBlock_icon"></div>
    </div>
    <div class="sb-previewBlock_body">
      <div class="sb-loader"></div>
    </div>
  </div>
  <table aria-hidden="true" class="sb-argstableBlock">
    <thead class="sb-argstableBlock-head">
      <tr>
        <th><span>Name</span></th>
        <th><span>Description</span></th>
        <th><span>Default</span></th>
        <th><span>Control </span></th>
      </tr>
    </thead>
    <tbody class="sb-argstableBlock-body">
      <tr>
        <td><span>propertyName</span><span title="Required">*</span></td>
        <td>
          <div><span>This is a short description</span></div>
          <div class="sb-argstableBlock-summary">
            <div><span class="sb-argstableBlock-code">summary</span></div>
          </div>
        </td>
        <td>
          <div><span class="sb-argstableBlock-code">defaultValue</span></div>
        </td>
        <td><button>Set string</button></td>
      </tr>
      <tr>
        <td><span>propertyName</span><span>*</span></td>
        <td>
          <div><span>This is a short description</span></div>
          <div class="sb-argstableBlock-summary">
            <div><span class="sb-argstableBlock-code">summary</span></div>
          </div>
        </td>
        <td>
          <div><span class="sb-argstableBlock-code">defaultValue</span></div>
        </td>
        <td><button>Set string</button></td>
      </tr>
      <tr>
        <td><span>propertyName</span><span>*</span></td>
        <td>
          <div><span>This is a short description</span></div>
          <div class="sb-argstableBlock-summary">
            <div><span class="sb-argstableBlock-code">summary</span></div>
          </div>
        </td>
        <td>
          <div><span class="sb-argstableBlock-code">defaultValue</span></div>
        </td>
        <td><button>Set string</button></td>
      </tr>
    </tbody>
  </table>
</div>

<div class="sb-nopreview sb-wrapper">
  <div class="sb-nopreview_main">
    <h1 class="sb-nopreview_heading sb-heading">No Preview</h1>
    <p>Sorry, but you either have no stories or none are selected somehow.</p>
    <ul>
      <li>Please check the Storybook config.</li>
      <li>Try reloading the page.</li>
    </ul>
    <p>
      If the problem persists, check the browser console, or the terminal you've run Storybook from.
    </p>
  </div>
</div>

<div class="sb-errordisplay sb-wrapper">
  <div class="sb-errordisplay_main">
    <h1 id="error-message"></h1>
    <p>
      The component failed to render properly, likely due to a configuration issue in Storybook.
      Here are some common causes and how you can address them:
    </p>
    <ol>
      <li>
        <strong>Missing Context/Providers</strong>: You can use decorators to supply specific
        contexts or providers, which are sometimes necessary for components to render correctly. For
        detailed instructions on using decorators, please visit the
        <a href="https://storybook.js.org/docs/writing-stories/decorators"
          >Decorators documentation</a
        >.
      </li>
      <li>
        <strong>Misconfigured Webpack or Vite</strong>: Verify that Storybook picks up all necessary
        settings for loaders, plugins, and other relevant parameters. You can find step-by-step
        guides for configuring
        <a href="https://storybook.js.org/docs/builders/webpack">Webpack</a> or
        <a href="https://storybook.js.org/docs/builders/vite">Vite</a>
        with Storybook.
      </li>
      <li>
        <strong>Missing Environment Variables</strong>: Your Storybook may require specific
        environment variables to function as intended. You can set up custom environment variables
        as outlined in the
        <a href="https://storybook.js.org/docs/configure/environment-variables"
          >Environment Variables documentation</a
        >.
      </li>
    </ol>
    <pre class="sb-errordisplay_code"><code id="error-stack"></code></pre>
  </div>
</div>

    <div id="storybook-root"></div>
    <div id="storybook-docs"></div>
    <script>
      // eslint-disable-next-line no-underscore-dangle, @typescript-eslint/naming-convention
      function __onViteAppLoadingError(event) {
        const hostname = globalThis.location.hostname;
        if (hostname !== 'localhost' && globalThis.CONFIG_TYPE === 'DEVELOPMENT') {
          const message = `Failed to load the Storybook preview file 'vite-app.js':

It looks like you're visiting the Storybook development server on another hostname than localhost: '${hostname}', but you haven't configured the necessary security features to support this.
Please re-run your Storybook development server with the '--host ${hostname}' flag, or manually configure your Vite allowedHosts configuration with viteFinal.

See:`;
          const docs = [
            'https://storybook.js.org/docs/api/cli-options#dev',
            'https://storybook.js.org/docs/api/main-config/main-config-vite-final',
            'https://vite.dev/config/server-options.html#server-allowedhosts',
          ];
          console.error(`${message}\n${docs.map((doc) => `- ${doc}`).join('\n')}`);

          document.getElementById('storybook-root').innerHTML =
            `<p style="color: red; max-width: 70ch">${message.replaceAll(
              '\n',
              '<br/>'
            )}<ul>${docs.map((doc) => `<li><a href='${doc}' target='_blank'>${doc}</a></li>`).join('')}<ul></p>`;
          return;
        }
      }
    </script>
  </body>
</html>
