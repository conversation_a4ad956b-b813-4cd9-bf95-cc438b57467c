try{
(()=>{var p=__STORYBOOK_API__,{ActiveTabs:A,Consumer:O,ManagerContext:h,Provider:T,RequestResponseError:y,addons:n,combineParameters:N,controlOrMetaKey:b,controlOrMetaSymbol:d,eventMatchesShortcut:C,eventToShortcut:P,experimental_MockUniversalStore:R,experimental_UniversalStore:U,experimental_requestResponse:k,experimental_useUniversalStore:v,isMacLike:M,isShortcutTaken:B,keyToSymbol:L,merge:J,mockChannel:x,optionOrAltSymbol:X,shortcutMatchesShortcut:Y,shortcutToHumanString:g,types:z,useAddonState:V,useArgTypes:q,useArgs:Z,useChannel:j,useGlobalTypes:D,useGlobals:G,useParameter:K,useSharedState:Q,useStoryPrepared:w,useStorybookApi:F,useStorybookState:H}=__STORYBOOK_API__;var e="storybook/links",a={NAVIGATE:`${e}/navigate`,REQUEST:`${e}/request`,RECEIVE:`${e}/receive`};n.register(e,t=>{t.on(a.REQUEST,({kind:i,name:u})=>{let _=t.storyId(i,u);t.emit(a.RECEIVE,_)})});})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
