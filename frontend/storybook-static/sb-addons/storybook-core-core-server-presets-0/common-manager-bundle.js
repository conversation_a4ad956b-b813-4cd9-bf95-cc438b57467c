try{
(()=>{var d=__STORYBOOK_API__,{ActiveTabs:y,Consumer:A,ManagerContext:T,Provider:N,RequestResponseError:P,addons:l,combineParameters:g,controlOrMetaKey:C,controlOrMetaSymbol:E,eventMatchesShortcut:U,eventToShortcut:f,experimental_MockUniversalStore:k,experimental_UniversalStore:M,experimental_requestResponse:R,experimental_useUniversalStore:v,isMacLike:x,isShortcutTaken:B,keyToSymbol:L,merge:J,mockChannel:w,optionOrAltSymbol:X,shortcutMatchesShortcut:Y,shortcutToHumanString:z,types:j,useAddonState:F,useArgTypes:Z,useArgs:q,useChannel:G,useGlobalTypes:K,useGlobals:H,useParameter:V,useSharedState:Q,useStoryPrepared:D,useStorybookApi:W,useStorybookState:$}=__STORYBOOK_API__;var c=(()=>{let e;return typeof window<"u"?e=window:typeof globalThis<"u"?e=globalThis:typeof window<"u"?e=window:typeof self<"u"?e=self:e={},e})(),p="tag-filters",S="static-filter";l.register(p,e=>{let i=Object.entries(c.TAGS_OPTIONS??{}).reduce((t,r)=>{let[o,u]=r;return u.excludeFromSidebar&&(t[o]=!0),t},{});e.experimental_setFilter(S,t=>{let r=t.tags??[];return(r.includes("dev")||t.type==="docs")&&r.filter(o=>i[o]).length===0})});})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
