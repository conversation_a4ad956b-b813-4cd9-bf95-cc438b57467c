{"v": 5, "entries": {"ui-button--docs": {"id": "ui-button--docs", "title": "UI/Button", "name": "Docs", "importPath": "./components/ui/button.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "ui-button--default": {"type": "story", "id": "ui-button--default", "name": "<PERSON><PERSON><PERSON>", "title": "UI/Button", "importPath": "./components/ui/button.stories.tsx", "componentPath": "./components/ui/button.tsx", "tags": ["dev", "test", "autodocs"]}, "ui-button--secondary": {"type": "story", "id": "ui-button--secondary", "name": "Secondary", "title": "UI/Button", "importPath": "./components/ui/button.stories.tsx", "componentPath": "./components/ui/button.tsx", "tags": ["dev", "test", "autodocs"]}, "ui-button--destructive": {"type": "story", "id": "ui-button--destructive", "name": "Destructive", "title": "UI/Button", "importPath": "./components/ui/button.stories.tsx", "componentPath": "./components/ui/button.tsx", "tags": ["dev", "test", "autodocs"]}, "ui-button--outline": {"type": "story", "id": "ui-button--outline", "name": "Outline", "title": "UI/Button", "importPath": "./components/ui/button.stories.tsx", "componentPath": "./components/ui/button.tsx", "tags": ["dev", "test", "autodocs"]}, "ui-button--ghost": {"type": "story", "id": "ui-button--ghost", "name": "Ghost", "title": "UI/Button", "importPath": "./components/ui/button.stories.tsx", "componentPath": "./components/ui/button.tsx", "tags": ["dev", "test", "autodocs"]}, "ui-button--link": {"type": "story", "id": "ui-button--link", "name": "Link", "title": "UI/Button", "importPath": "./components/ui/button.stories.tsx", "componentPath": "./components/ui/button.tsx", "tags": ["dev", "test", "autodocs"]}, "ui-button--small": {"type": "story", "id": "ui-button--small", "name": "Small", "title": "UI/Button", "importPath": "./components/ui/button.stories.tsx", "componentPath": "./components/ui/button.tsx", "tags": ["dev", "test", "autodocs"]}, "ui-button--large": {"type": "story", "id": "ui-button--large", "name": "Large", "title": "UI/Button", "importPath": "./components/ui/button.stories.tsx", "componentPath": "./components/ui/button.tsx", "tags": ["dev", "test", "autodocs"]}, "ui-button--icon": {"type": "story", "id": "ui-button--icon", "name": "Icon", "title": "UI/Button", "importPath": "./components/ui/button.stories.tsx", "componentPath": "./components/ui/button.tsx", "tags": ["dev", "test", "autodocs"]}}}