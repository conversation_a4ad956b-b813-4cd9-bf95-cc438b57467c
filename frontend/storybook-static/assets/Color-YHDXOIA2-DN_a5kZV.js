import{d as ce,v as M,G as Z,g as he,a as fe,Z as de,M as ge,_ as be,b as q}from"./DocsRenderer-CFRXHY34-C80e4Yhz.js";import{r as p,R as m}from"./index-D4H_InIO.js";import"./iframe-vtglT4P5.js";import"./jsx-runtime-BO8uF4Og.js";import"./index-vYCkCKEW.js";import"./index-CXQShRbs.js";import"./index-DrFu-skq.js";import"./react-18-CL8RV0f0.js";var me=q({"../../node_modules/color-name/index.js"(n,o){o.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}}}),J=q({"../../node_modules/color-convert/conversions.js"(n,o){var c=me(),h={};for(let e of Object.keys(c))h[c[e]]=e;var i={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};o.exports=i;for(let e of Object.keys(i)){if(!("channels"in i[e]))throw new Error("missing channels property: "+e);if(!("labels"in i[e]))throw new Error("missing channel labels property: "+e);if(i[e].labels.length!==i[e].channels)throw new Error("channel and label counts mismatch: "+e);let{channels:t,labels:r}=i[e];delete i[e].channels,delete i[e].labels,Object.defineProperty(i[e],"channels",{value:t}),Object.defineProperty(i[e],"labels",{value:r})}i.rgb.hsl=function(e){let t=e[0]/255,r=e[1]/255,a=e[2]/255,l=Math.min(t,r,a),u=Math.max(t,r,a),s=u-l,f,g;u===l?f=0:t===u?f=(r-a)/s:r===u?f=2+(a-t)/s:a===u&&(f=4+(t-r)/s),f=Math.min(f*60,360),f<0&&(f+=360);let b=(l+u)/2;return u===l?g=0:b<=.5?g=s/(u+l):g=s/(2-u-l),[f,g*100,b*100]},i.rgb.hsv=function(e){let t,r,a,l,u,s=e[0]/255,f=e[1]/255,g=e[2]/255,b=Math.max(s,f,g),y=b-Math.min(s,f,g),v=function(w){return(b-w)/6/y+1/2};return y===0?(l=0,u=0):(u=y/b,t=v(s),r=v(f),a=v(g),s===b?l=a-r:f===b?l=1/3+t-a:g===b&&(l=2/3+r-t),l<0?l+=1:l>1&&(l-=1)),[l*360,u*100,b*100]},i.rgb.hwb=function(e){let t=e[0],r=e[1],a=e[2],l=i.rgb.hsl(e)[0],u=1/255*Math.min(t,Math.min(r,a));return a=1-1/255*Math.max(t,Math.max(r,a)),[l,u*100,a*100]},i.rgb.cmyk=function(e){let t=e[0]/255,r=e[1]/255,a=e[2]/255,l=Math.min(1-t,1-r,1-a),u=(1-t-l)/(1-l)||0,s=(1-r-l)/(1-l)||0,f=(1-a-l)/(1-l)||0;return[u*100,s*100,f*100,l*100]};function d(e,t){return(e[0]-t[0])**2+(e[1]-t[1])**2+(e[2]-t[2])**2}i.rgb.keyword=function(e){let t=h[e];if(t)return t;let r=1/0,a;for(let l of Object.keys(c)){let u=c[l],s=d(e,u);s<r&&(r=s,a=l)}return a},i.keyword.rgb=function(e){return c[e]},i.rgb.xyz=function(e){let t=e[0]/255,r=e[1]/255,a=e[2]/255;t=t>.04045?((t+.055)/1.055)**2.4:t/12.92,r=r>.04045?((r+.055)/1.055)**2.4:r/12.92,a=a>.04045?((a+.055)/1.055)**2.4:a/12.92;let l=t*.4124+r*.3576+a*.1805,u=t*.2126+r*.7152+a*.0722,s=t*.0193+r*.1192+a*.9505;return[l*100,u*100,s*100]},i.rgb.lab=function(e){let t=i.rgb.xyz(e),r=t[0],a=t[1],l=t[2];r/=95.047,a/=100,l/=108.883,r=r>.008856?r**(1/3):7.787*r+16/116,a=a>.008856?a**(1/3):7.787*a+16/116,l=l>.008856?l**(1/3):7.787*l+16/116;let u=116*a-16,s=500*(r-a),f=200*(a-l);return[u,s,f]},i.hsl.rgb=function(e){let t=e[0]/360,r=e[1]/100,a=e[2]/100,l,u,s;if(r===0)return s=a*255,[s,s,s];a<.5?l=a*(1+r):l=a+r-a*r;let f=2*a-l,g=[0,0,0];for(let b=0;b<3;b++)u=t+1/3*-(b-1),u<0&&u++,u>1&&u--,6*u<1?s=f+(l-f)*6*u:2*u<1?s=l:3*u<2?s=f+(l-f)*(2/3-u)*6:s=f,g[b]=s*255;return g},i.hsl.hsv=function(e){let t=e[0],r=e[1]/100,a=e[2]/100,l=r,u=Math.max(a,.01);a*=2,r*=a<=1?a:2-a,l*=u<=1?u:2-u;let s=(a+r)/2,f=a===0?2*l/(u+l):2*r/(a+r);return[t,f*100,s*100]},i.hsv.rgb=function(e){let t=e[0]/60,r=e[1]/100,a=e[2]/100,l=Math.floor(t)%6,u=t-Math.floor(t),s=255*a*(1-r),f=255*a*(1-r*u),g=255*a*(1-r*(1-u));switch(a*=255,l){case 0:return[a,g,s];case 1:return[f,a,s];case 2:return[s,a,g];case 3:return[s,f,a];case 4:return[g,s,a];case 5:return[a,s,f]}},i.hsv.hsl=function(e){let t=e[0],r=e[1]/100,a=e[2]/100,l=Math.max(a,.01),u,s;s=(2-r)*a;let f=(2-r)*l;return u=r*l,u/=f<=1?f:2-f,u=u||0,s/=2,[t,u*100,s*100]},i.hwb.rgb=function(e){let t=e[0]/360,r=e[1]/100,a=e[2]/100,l=r+a,u;l>1&&(r/=l,a/=l);let s=Math.floor(6*t),f=1-a;u=6*t-s,(s&1)!==0&&(u=1-u);let g=r+u*(f-r),b,y,v;switch(s){default:case 6:case 0:b=f,y=g,v=r;break;case 1:b=g,y=f,v=r;break;case 2:b=r,y=f,v=g;break;case 3:b=r,y=g,v=f;break;case 4:b=g,y=r,v=f;break;case 5:b=f,y=r,v=g;break}return[b*255,y*255,v*255]},i.cmyk.rgb=function(e){let t=e[0]/100,r=e[1]/100,a=e[2]/100,l=e[3]/100,u=1-Math.min(1,t*(1-l)+l),s=1-Math.min(1,r*(1-l)+l),f=1-Math.min(1,a*(1-l)+l);return[u*255,s*255,f*255]},i.xyz.rgb=function(e){let t=e[0]/100,r=e[1]/100,a=e[2]/100,l,u,s;return l=t*3.2406+r*-1.5372+a*-.4986,u=t*-.9689+r*1.8758+a*.0415,s=t*.0557+r*-.204+a*1.057,l=l>.0031308?1.055*l**(1/2.4)-.055:l*12.92,u=u>.0031308?1.055*u**(1/2.4)-.055:u*12.92,s=s>.0031308?1.055*s**(1/2.4)-.055:s*12.92,l=Math.min(Math.max(0,l),1),u=Math.min(Math.max(0,u),1),s=Math.min(Math.max(0,s),1),[l*255,u*255,s*255]},i.xyz.lab=function(e){let t=e[0],r=e[1],a=e[2];t/=95.047,r/=100,a/=108.883,t=t>.008856?t**(1/3):7.787*t+16/116,r=r>.008856?r**(1/3):7.787*r+16/116,a=a>.008856?a**(1/3):7.787*a+16/116;let l=116*r-16,u=500*(t-r),s=200*(r-a);return[l,u,s]},i.lab.xyz=function(e){let t=e[0],r=e[1],a=e[2],l,u,s;u=(t+16)/116,l=r/500+u,s=u-a/200;let f=u**3,g=l**3,b=s**3;return u=f>.008856?f:(u-16/116)/7.787,l=g>.008856?g:(l-16/116)/7.787,s=b>.008856?b:(s-16/116)/7.787,l*=95.047,u*=100,s*=108.883,[l,u,s]},i.lab.lch=function(e){let t=e[0],r=e[1],a=e[2],l;l=Math.atan2(a,r)*360/2/Math.PI,l<0&&(l+=360);let u=Math.sqrt(r*r+a*a);return[t,u,l]},i.lch.lab=function(e){let t=e[0],r=e[1],a=e[2]/360*2*Math.PI,l=r*Math.cos(a),u=r*Math.sin(a);return[t,l,u]},i.rgb.ansi16=function(e,t=null){let[r,a,l]=e,u=t===null?i.rgb.hsv(e)[2]:t;if(u=Math.round(u/50),u===0)return 30;let s=30+(Math.round(l/255)<<2|Math.round(a/255)<<1|Math.round(r/255));return u===2&&(s+=60),s},i.hsv.ansi16=function(e){return i.rgb.ansi16(i.hsv.rgb(e),e[2])},i.rgb.ansi256=function(e){let t=e[0],r=e[1],a=e[2];return t===r&&r===a?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(r/255*5)+Math.round(a/255*5)},i.ansi16.rgb=function(e){let t=e%10;if(t===0||t===7)return e>50&&(t+=3.5),t=t/10.5*255,[t,t,t];let r=(~~(e>50)+1)*.5,a=(t&1)*r*255,l=(t>>1&1)*r*255,u=(t>>2&1)*r*255;return[a,l,u]},i.ansi256.rgb=function(e){if(e>=232){let u=(e-232)*10+8;return[u,u,u]}e-=16;let t,r=Math.floor(e/36)/5*255,a=Math.floor((t=e%36)/6)/5*255,l=t%6/5*255;return[r,a,l]},i.rgb.hex=function(e){let t=(((Math.round(e[0])&255)<<16)+((Math.round(e[1])&255)<<8)+(Math.round(e[2])&255)).toString(16).toUpperCase();return"000000".substring(t.length)+t},i.hex.rgb=function(e){let t=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!t)return[0,0,0];let r=t[0];t[0].length===3&&(r=r.split("").map(f=>f+f).join(""));let a=parseInt(r,16),l=a>>16&255,u=a>>8&255,s=a&255;return[l,u,s]},i.rgb.hcg=function(e){let t=e[0]/255,r=e[1]/255,a=e[2]/255,l=Math.max(Math.max(t,r),a),u=Math.min(Math.min(t,r),a),s=l-u,f,g;return s<1?f=u/(1-s):f=0,s<=0?g=0:l===t?g=(r-a)/s%6:l===r?g=2+(a-t)/s:g=4+(t-r)/s,g/=6,g%=1,[g*360,s*100,f*100]},i.hsl.hcg=function(e){let t=e[1]/100,r=e[2]/100,a=r<.5?2*t*r:2*t*(1-r),l=0;return a<1&&(l=(r-.5*a)/(1-a)),[e[0],a*100,l*100]},i.hsv.hcg=function(e){let t=e[1]/100,r=e[2]/100,a=t*r,l=0;return a<1&&(l=(r-a)/(1-a)),[e[0],a*100,l*100]},i.hcg.rgb=function(e){let t=e[0]/360,r=e[1]/100,a=e[2]/100;if(r===0)return[a*255,a*255,a*255];let l=[0,0,0],u=t%1*6,s=u%1,f=1-s,g=0;switch(Math.floor(u)){case 0:l[0]=1,l[1]=s,l[2]=0;break;case 1:l[0]=f,l[1]=1,l[2]=0;break;case 2:l[0]=0,l[1]=1,l[2]=s;break;case 3:l[0]=0,l[1]=f,l[2]=1;break;case 4:l[0]=s,l[1]=0,l[2]=1;break;default:l[0]=1,l[1]=0,l[2]=f}return g=(1-r)*a,[(r*l[0]+g)*255,(r*l[1]+g)*255,(r*l[2]+g)*255]},i.hcg.hsv=function(e){let t=e[1]/100,r=e[2]/100,a=t+r*(1-t),l=0;return a>0&&(l=t/a),[e[0],l*100,a*100]},i.hcg.hsl=function(e){let t=e[1]/100,r=e[2]/100*(1-t)+.5*t,a=0;return r>0&&r<.5?a=t/(2*r):r>=.5&&r<1&&(a=t/(2*(1-r))),[e[0],a*100,r*100]},i.hcg.hwb=function(e){let t=e[1]/100,r=e[2]/100,a=t+r*(1-t);return[e[0],(a-t)*100,(1-a)*100]},i.hwb.hcg=function(e){let t=e[1]/100,r=1-e[2]/100,a=r-t,l=0;return a<1&&(l=(r-a)/(1-a)),[e[0],a*100,l*100]},i.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]},i.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]},i.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]},i.gray.hsl=function(e){return[0,0,e[0]]},i.gray.hsv=i.gray.hsl,i.gray.hwb=function(e){return[0,100,e[0]]},i.gray.cmyk=function(e){return[0,0,0,e[0]]},i.gray.lab=function(e){return[e[0],0,0]},i.gray.hex=function(e){let t=Math.round(e[0]/100*255)&255,r=((t<<16)+(t<<8)+t).toString(16).toUpperCase();return"000000".substring(r.length)+r},i.rgb.gray=function(e){return[(e[0]+e[1]+e[2])/3/255*100]}}}),ve=q({"../../node_modules/color-convert/route.js"(n,o){var c=J();function h(){let t={},r=Object.keys(c);for(let a=r.length,l=0;l<a;l++)t[r[l]]={distance:-1,parent:null};return t}function i(t){let r=h(),a=[t];for(r[t].distance=0;a.length;){let l=a.pop(),u=Object.keys(c[l]);for(let s=u.length,f=0;f<s;f++){let g=u[f],b=r[g];b.distance===-1&&(b.distance=r[l].distance+1,b.parent=l,a.unshift(g))}}return r}function d(t,r){return function(a){return r(t(a))}}function e(t,r){let a=[r[t].parent,t],l=c[r[t].parent][t],u=r[t].parent;for(;r[u].parent;)a.unshift(r[u].parent),l=d(c[r[u].parent][u],l),u=r[u].parent;return l.conversion=a,l}o.exports=function(t){let r=i(t),a={},l=Object.keys(r);for(let u=l.length,s=0;s<u;s++){let f=l[s];r[f].parent!==null&&(a[f]=e(f,r))}return a}}}),pe=q({"../../node_modules/color-convert/index.js"(n,o){var c=J(),h=ve(),i={},d=Object.keys(c);function e(r){let a=function(...l){let u=l[0];return u==null?u:(u.length>1&&(l=u),r(l))};return"conversion"in r&&(a.conversion=r.conversion),a}function t(r){let a=function(...l){let u=l[0];if(u==null)return u;u.length>1&&(l=u);let s=r(l);if(typeof s=="object")for(let f=s.length,g=0;g<f;g++)s[g]=Math.round(s[g]);return s};return"conversion"in r&&(a.conversion=r.conversion),a}d.forEach(r=>{i[r]={},Object.defineProperty(i[r],"channels",{value:c[r].channels}),Object.defineProperty(i[r],"labels",{value:c[r].labels});let a=h(r);Object.keys(a).forEach(l=>{let u=a[l];i[r][l]=t(u),i[r][l].raw=e(u)})}),o.exports=i}}),_=be(pe());function C(){return(C=Object.assign||function(n){for(var o=1;o<arguments.length;o++){var c=arguments[o];for(var h in c)Object.prototype.hasOwnProperty.call(c,h)&&(n[h]=c[h])}return n}).apply(this,arguments)}function D(n,o){if(n==null)return{};var c,h,i={},d=Object.keys(n);for(h=0;h<d.length;h++)o.indexOf(c=d[h])>=0||(i[c]=n[c]);return i}function L(n){var o=p.useRef(n),c=p.useRef(function(h){o.current&&o.current(h)});return o.current=n,c.current}var O=function(n,o,c){return o===void 0&&(o=0),c===void 0&&(c=1),n>c?c:n<o?o:n},S=function(n){return"touches"in n},B=function(n){return n&&n.ownerDocument.defaultView||self},T=function(n,o,c){var h=n.getBoundingClientRect(),i=S(o)?function(d,e){for(var t=0;t<d.length;t++)if(d[t].identifier===e)return d[t];return d[0]}(o.touches,c):o;return{left:O((i.pageX-(h.left+B(n).pageXOffset))/h.width),top:O((i.pageY-(h.top+B(n).pageYOffset))/h.height)}},W=function(n){!S(n)&&n.preventDefault()},V=m.memo(function(n){var o=n.onMove,c=n.onKey,h=D(n,["onMove","onKey"]),i=p.useRef(null),d=L(o),e=L(c),t=p.useRef(null),r=p.useRef(!1),a=p.useMemo(function(){var f=function(y){W(y),(S(y)?y.touches.length>0:y.buttons>0)&&i.current?d(T(i.current,y,t.current)):b(!1)},g=function(){return b(!1)};function b(y){var v=r.current,w=B(i.current),E=y?w.addEventListener:w.removeEventListener;E(v?"touchmove":"mousemove",f),E(v?"touchend":"mouseup",g)}return[function(y){var v=y.nativeEvent,w=i.current;if(w&&(W(v),!function(k,P){return P&&!S(k)}(v,r.current)&&w)){if(S(v)){r.current=!0;var E=v.changedTouches||[];E.length&&(t.current=E[0].identifier)}w.focus(),d(T(w,v,t.current)),b(!0)}},function(y){var v=y.which||y.keyCode;v<37||v>40||(y.preventDefault(),e({left:v===39?.05:v===37?-.05:0,top:v===40?.05:v===38?-.05:0}))},b]},[e,d]),l=a[0],u=a[1],s=a[2];return p.useEffect(function(){return s},[s]),m.createElement("div",C({},h,{onTouchStart:l,onMouseDown:l,className:"react-colorful__interactive",ref:i,onKeyDown:u,tabIndex:0,role:"slider"}))}),N=function(n){return n.filter(Boolean).join(" ")},F=function(n){var o=n.color,c=n.left,h=n.top,i=h===void 0?.5:h,d=N(["react-colorful__pointer",n.className]);return m.createElement("div",{className:d,style:{top:100*i+"%",left:100*c+"%"}},m.createElement("div",{className:"react-colorful__pointer-fill",style:{backgroundColor:o}}))},x=function(n,o,c){return o===void 0&&(o=0),c===void 0&&(c=Math.pow(10,o)),Math.round(c*n)/c},ye={grad:.9,turn:360,rad:360/(2*Math.PI)},xe=function(n){return re(X(n))},X=function(n){return n[0]==="#"&&(n=n.substring(1)),n.length<6?{r:parseInt(n[0]+n[0],16),g:parseInt(n[1]+n[1],16),b:parseInt(n[2]+n[2],16),a:n.length===4?x(parseInt(n[3]+n[3],16)/255,2):1}:{r:parseInt(n.substring(0,2),16),g:parseInt(n.substring(2,4),16),b:parseInt(n.substring(4,6),16),a:n.length===8?x(parseInt(n.substring(6,8),16)/255,2):1}},we=function(n,o){return o===void 0&&(o="deg"),Number(n)*(ye[o]||1)},ke=function(n){var o=/hsla?\(?\s*(-?\d*\.?\d+)(deg|rad|grad|turn)?[,\s]+(-?\d*\.?\d+)%?[,\s]+(-?\d*\.?\d+)%?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(n);return o?_e({h:we(o[1],o[2]),s:Number(o[3]),l:Number(o[4]),a:o[5]===void 0?1:Number(o[5])/(o[6]?100:1)}):{h:0,s:0,v:0,a:1}},_e=function(n){var o=n.s,c=n.l;return{h:n.h,s:(o*=(c<50?c:100-c)/100)>0?2*o/(c+o)*100:0,v:c+o,a:n.a}},Ee=function(n){return Ce(ee(n))},Q=function(n){var o=n.s,c=n.v,h=n.a,i=(200-o)*c/100;return{h:x(n.h),s:x(i>0&&i<200?o*c/100/(i<=100?i:200-i)*100:0),l:x(i/2),a:x(h,2)}},G=function(n){var o=Q(n);return"hsl("+o.h+", "+o.s+"%, "+o.l+"%)"},H=function(n){var o=Q(n);return"hsla("+o.h+", "+o.s+"%, "+o.l+"%, "+o.a+")"},ee=function(n){var o=n.h,c=n.s,h=n.v,i=n.a;o=o/360*6,c/=100,h/=100;var d=Math.floor(o),e=h*(1-c),t=h*(1-(o-d)*c),r=h*(1-(1-o+d)*c),a=d%6;return{r:x(255*[h,t,e,e,r,h][a]),g:x(255*[r,h,h,t,e,e][a]),b:x(255*[e,e,r,h,h,t][a]),a:x(i,2)}},Me=function(n){var o=/rgba?\(?\s*(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(n);return o?re({r:Number(o[1])/(o[2]?100/255:1),g:Number(o[3])/(o[4]?100/255:1),b:Number(o[5])/(o[6]?100/255:1),a:o[7]===void 0?1:Number(o[7])/(o[8]?100:1)}):{h:0,s:0,v:0,a:1}},j=function(n){var o=n.toString(16);return o.length<2?"0"+o:o},Ce=function(n){var o=n.r,c=n.g,h=n.b,i=n.a,d=i<1?j(x(255*i)):"";return"#"+j(o)+j(c)+j(h)+d},re=function(n){var o=n.r,c=n.g,h=n.b,i=n.a,d=Math.max(o,c,h),e=d-Math.min(o,c,h),t=e?d===o?(c-h)/e:d===c?2+(h-o)/e:4+(o-c)/e:0;return{h:x(60*(t<0?t+6:t)),s:x(d?e/d*100:0),v:x(d/255*100),a:i}},te=m.memo(function(n){var o=n.hue,c=n.onChange,h=N(["react-colorful__hue",n.className]);return m.createElement("div",{className:h},m.createElement(V,{onMove:function(i){c({h:360*i.left})},onKey:function(i){c({h:O(o+360*i.left,0,360)})},"aria-label":"Hue","aria-valuenow":x(o),"aria-valuemax":"360","aria-valuemin":"0"},m.createElement(F,{className:"react-colorful__hue-pointer",left:o/360,color:G({h:o,s:100,v:100,a:1})})))}),ne=m.memo(function(n){var o=n.hsva,c=n.onChange,h={backgroundColor:G({h:o.h,s:100,v:100,a:1})};return m.createElement("div",{className:"react-colorful__saturation",style:h},m.createElement(V,{onMove:function(i){c({s:100*i.left,v:100-100*i.top})},onKey:function(i){c({s:O(o.s+100*i.left,0,100),v:O(o.v-100*i.top,0,100)})},"aria-label":"Color","aria-valuetext":"Saturation "+x(o.s)+"%, Brightness "+x(o.v)+"%"},m.createElement(F,{className:"react-colorful__saturation-pointer",top:1-o.v/100,left:o.s/100,color:G(o)})))}),ae=function(n,o){if(n===o)return!0;for(var c in n)if(n[c]!==o[c])return!1;return!0},le=function(n,o){return n.replace(/\s/g,"")===o.replace(/\s/g,"")},$e=function(n,o){return n.toLowerCase()===o.toLowerCase()||ae(X(n),X(o))};function oe(n,o,c){var h=L(c),i=p.useState(function(){return n.toHsva(o)}),d=i[0],e=i[1],t=p.useRef({color:o,hsva:d});p.useEffect(function(){if(!n.equal(o,t.current.color)){var a=n.toHsva(o);t.current={hsva:a,color:o},e(a)}},[o,n]),p.useEffect(function(){var a;ae(d,t.current.hsva)||n.equal(a=n.fromHsva(d),t.current.color)||(t.current={hsva:d,color:a},h(a))},[d,n,h]);var r=p.useCallback(function(a){e(function(l){return Object.assign({},l,a)})},[]);return[d,r]}var Oe=typeof window<"u"?p.useLayoutEffect:p.useEffect,Se=function(){return typeof __webpack_nonce__<"u"?__webpack_nonce__:void 0},A=new Map,ue=function(n){Oe(function(){var o=n.current?n.current.ownerDocument:document;if(o!==void 0&&!A.has(o)){var c=o.createElement("style");c.innerHTML=`.react-colorful{position:relative;display:flex;flex-direction:column;width:200px;height:200px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:default}.react-colorful__saturation{position:relative;flex-grow:1;border-color:transparent;border-bottom:12px solid #000;border-radius:8px 8px 0 0;background-image:linear-gradient(0deg,#000,transparent),linear-gradient(90deg,#fff,hsla(0,0%,100%,0))}.react-colorful__alpha-gradient,.react-colorful__pointer-fill{content:"";position:absolute;left:0;top:0;right:0;bottom:0;pointer-events:none;border-radius:inherit}.react-colorful__alpha-gradient,.react-colorful__saturation{box-shadow:inset 0 0 0 1px rgba(0,0,0,.05)}.react-colorful__alpha,.react-colorful__hue{position:relative;height:24px}.react-colorful__hue{background:linear-gradient(90deg,red 0,#ff0 17%,#0f0 33%,#0ff 50%,#00f 67%,#f0f 83%,red)}.react-colorful__last-control{border-radius:0 0 8px 8px}.react-colorful__interactive{position:absolute;left:0;top:0;right:0;bottom:0;border-radius:inherit;outline:none;touch-action:none}.react-colorful__pointer{position:absolute;z-index:1;box-sizing:border-box;width:28px;height:28px;transform:translate(-50%,-50%);background-color:#fff;border:2px solid #fff;border-radius:50%;box-shadow:0 2px 4px rgba(0,0,0,.2)}.react-colorful__interactive:focus .react-colorful__pointer{transform:translate(-50%,-50%) scale(1.1)}.react-colorful__alpha,.react-colorful__alpha-pointer{background-color:#fff;background-image:url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>')}.react-colorful__saturation-pointer{z-index:3}.react-colorful__hue-pointer{z-index:2}`,A.set(o,c);var h=Se();h&&c.setAttribute("nonce",h),o.head.appendChild(c)}},[])},Ne=function(n){var o=n.className,c=n.colorModel,h=n.color,i=h===void 0?c.defaultColor:h,d=n.onChange,e=D(n,["className","colorModel","color","onChange"]),t=p.useRef(null);ue(t);var r=oe(c,i,d),a=r[0],l=r[1],u=N(["react-colorful",o]);return m.createElement("div",C({},e,{ref:t,className:u}),m.createElement(ne,{hsva:a,onChange:l}),m.createElement(te,{hue:a.h,onChange:l,className:"react-colorful__last-control"}))},je={defaultColor:"000",toHsva:xe,fromHsva:function(n){return Ee({h:n.h,s:n.s,v:n.v,a:1})},equal:$e},Re=function(n){return m.createElement(Ne,C({},n,{colorModel:je}))},ze=function(n){var o=n.className,c=n.hsva,h=n.onChange,i={backgroundImage:"linear-gradient(90deg, "+H(Object.assign({},c,{a:0}))+", "+H(Object.assign({},c,{a:1}))+")"},d=N(["react-colorful__alpha",o]),e=x(100*c.a);return m.createElement("div",{className:d},m.createElement("div",{className:"react-colorful__alpha-gradient",style:i}),m.createElement(V,{onMove:function(t){h({a:t.left})},onKey:function(t){h({a:O(c.a+t.left)})},"aria-label":"Alpha","aria-valuetext":e+"%","aria-valuenow":e,"aria-valuemin":"0","aria-valuemax":"100"},m.createElement(F,{className:"react-colorful__alpha-pointer",left:c.a,color:H(c)})))},ie=function(n){var o=n.className,c=n.colorModel,h=n.color,i=h===void 0?c.defaultColor:h,d=n.onChange,e=D(n,["className","colorModel","color","onChange"]),t=p.useRef(null);ue(t);var r=oe(c,i,d),a=r[0],l=r[1],u=N(["react-colorful",o]);return m.createElement("div",C({},e,{ref:t,className:u}),m.createElement(ne,{hsva:a,onChange:l}),m.createElement(te,{hue:a.h,onChange:l}),m.createElement(ze,{hsva:a,onChange:l,className:"react-colorful__last-control"}))},He={defaultColor:"hsla(0, 0%, 0%, 1)",toHsva:ke,fromHsva:H,equal:le},Ie=function(n){return m.createElement(ie,C({},n,{colorModel:He}))},qe={defaultColor:"rgba(0, 0, 0, 1)",toHsva:Me,fromHsva:function(n){var o=ee(n);return"rgba("+o.r+", "+o.g+", "+o.b+", "+o.a+")"},equal:le},Pe=function(n){return m.createElement(ie,C({},n,{colorModel:qe}))},Le=M.div({position:"relative",maxWidth:250,'&[aria-readonly="true"]':{opacity:.5}}),Be=M(Z)({position:"absolute",zIndex:1,top:4,left:4,"[aria-readonly=true] &":{cursor:"not-allowed"}}),Xe=M.div({width:200,margin:5,".react-colorful__saturation":{borderRadius:"4px 4px 0 0"},".react-colorful__hue":{boxShadow:"inset 0 0 0 1px rgb(0 0 0 / 5%)"},".react-colorful__last-control":{borderRadius:"0 0 4px 4px"}}),Ge=M(fe)(({theme:n})=>({fontFamily:n.typography.fonts.base})),Ke=M.div({display:"grid",gridTemplateColumns:"repeat(9, 16px)",gap:6,padding:3,marginTop:5,width:200}),De=M.div(({theme:n,active:o})=>({width:16,height:16,boxShadow:o?`${n.appBorderColor} 0 0 0 1px inset, ${n.textMutedColor}50 0 0 0 4px`:`${n.appBorderColor} 0 0 0 1px inset`,borderRadius:n.appBorderRadius})),Ve=`url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>')`,U=({value:n,style:o,...c})=>{let h=`linear-gradient(${n}, ${n}), ${Ve}, linear-gradient(#fff, #fff)`;return m.createElement(De,{...c,style:{...o,backgroundImage:h}})},Fe=M(de.Input)(({theme:n,readOnly:o})=>({width:"100%",paddingLeft:30,paddingRight:30,boxSizing:"border-box",fontFamily:n.typography.fonts.base})),Te=M(ge)(({theme:n})=>({position:"absolute",zIndex:1,top:6,right:7,width:20,height:20,padding:4,boxSizing:"border-box",cursor:"pointer",color:n.input.color})),se=(n=>(n.RGB="rgb",n.HSL="hsl",n.HEX="hex",n))(se||{}),R=Object.values(se),We=/\(([0-9]+),\s*([0-9]+)%?,\s*([0-9]+)%?,?\s*([0-9.]+)?\)/,Ae=/^\s*rgba?\(([0-9]+),\s*([0-9]+),\s*([0-9]+),?\s*([0-9.]+)?\)\s*$/i,Ue=/^\s*hsla?\(([0-9]+),\s*([0-9]+)%,\s*([0-9]+)%,?\s*([0-9.]+)?\)\s*$/i,K=/^\s*#?([0-9a-f]{3}|[0-9a-f]{6})\s*$/i,Ye=/^\s*#?([0-9a-f]{3})\s*$/i,Ze={hex:Re,rgb:Pe,hsl:Ie},z={hex:"transparent",rgb:"rgba(0, 0, 0, 0)",hsl:"hsla(0, 0%, 0%, 0)"},Y=n=>{let o=n==null?void 0:n.match(We);if(!o)return[0,0,0,1];let[,c,h,i,d=1]=o;return[c,h,i,d].map(Number)},$=n=>{if(!n)return;let o=!0;if(Ae.test(n)){let[e,t,r,a]=Y(n),[l,u,s]=_.default.rgb.hsl([e,t,r])||[0,0,0];return{valid:o,value:n,keyword:_.default.rgb.keyword([e,t,r]),colorSpace:"rgb",rgb:n,hsl:`hsla(${l}, ${u}%, ${s}%, ${a})`,hex:`#${_.default.rgb.hex([e,t,r]).toLowerCase()}`}}if(Ue.test(n)){let[e,t,r,a]=Y(n),[l,u,s]=_.default.hsl.rgb([e,t,r])||[0,0,0];return{valid:o,value:n,keyword:_.default.hsl.keyword([e,t,r]),colorSpace:"hsl",rgb:`rgba(${l}, ${u}, ${s}, ${a})`,hsl:n,hex:`#${_.default.hsl.hex([e,t,r]).toLowerCase()}`}}let c=n.replace("#",""),h=_.default.keyword.rgb(c)||_.default.hex.rgb(c),i=_.default.rgb.hsl(h),d=n;if(/[^#a-f0-9]/i.test(n)?d=c:K.test(n)&&(d=`#${c}`),d.startsWith("#"))o=K.test(d);else try{_.default.keyword.hex(d)}catch{o=!1}return{valid:o,value:d,keyword:_.default.rgb.keyword(h),colorSpace:"hex",rgb:`rgba(${h[0]}, ${h[1]}, ${h[2]}, 1)`,hsl:`hsla(${i[0]}, ${i[1]}%, ${i[2]}%, 1)`,hex:d}},Je=(n,o,c)=>{if(!n||!(o!=null&&o.valid))return z[c];if(c!=="hex")return(o==null?void 0:o[c])||z[c];if(!o.hex.startsWith("#"))try{return`#${_.default.keyword.hex(o.hex)}`}catch{return z.hex}let h=o.hex.match(Ye);if(!h)return K.test(o.hex)?o.hex:z.hex;let[i,d,e]=h[1].split("");return`#${i}${i}${d}${d}${e}${e}`},Qe=(n,o)=>{let[c,h]=p.useState(n||""),[i,d]=p.useState(()=>$(c)),[e,t]=p.useState((i==null?void 0:i.colorSpace)||"hex");p.useEffect(()=>{let u=n||"",s=$(u);h(u),d(s),t((s==null?void 0:s.colorSpace)||"hex")},[n]);let r=p.useMemo(()=>Je(c,i,e).toLowerCase(),[c,i,e]),a=p.useCallback(u=>{let s=$(u),f=(s==null?void 0:s.value)||u||"";h(f),f===""&&(d(void 0),o(void 0)),s&&(d(s),t(s.colorSpace),o(s.value))},[o]),l=p.useCallback(()=>{let u=R.indexOf(e)+1;u>=R.length&&(u=0),t(R[u]);let s=(i==null?void 0:i[R[u]])||"";h(s),o(s)},[i,e,o]);return{value:c,realValue:r,updateValue:a,color:i,colorSpace:e,cycleColorSpace:l}},I=n=>n.replace(/\s*/,"").toLowerCase(),er=(n,o,c)=>{let[h,i]=p.useState(o!=null&&o.valid?[o]:[]);p.useEffect(()=>{o===void 0&&i([])},[o]);let d=p.useMemo(()=>(n||[]).map(t=>typeof t=="string"?$(t):t.title?{...$(t.color),keyword:t.title}:$(t.color)).concat(h).filter(Boolean).slice(-27),[n,h]),e=p.useCallback(t=>{t!=null&&t.valid&&(d.some(r=>I(r[c])===I(t[c]))||i(r=>r.concat(t)))},[c,d]);return{presets:d,addPreset:e}},rr=({name:n,value:o,onChange:c,onFocus:h,onBlur:i,presetColors:d,startOpen:e=!1,argType:t})=>{var E;let r=p.useCallback(ce(c,200),[c]),{value:a,realValue:l,updateValue:u,color:s,colorSpace:f,cycleColorSpace:g}=Qe(o,r),{presets:b,addPreset:y}=er(d,s,f),v=Ze[f],w=!!((E=t==null?void 0:t.table)!=null&&E.readonly);return m.createElement(Le,{"aria-readonly":w},m.createElement(Be,{startOpen:e,trigger:w?[null]:void 0,closeOnOutsideClick:!0,onVisibleChange:()=>y(s),tooltip:m.createElement(Xe,null,m.createElement(v,{color:l==="transparent"?"#000000":l,onChange:u,onFocus:h,onBlur:i}),b.length>0&&m.createElement(Ke,null,b.map((k,P)=>m.createElement(Z,{key:`${k.value}-${P}`,hasChrome:!1,tooltip:m.createElement(Ge,{note:k.keyword||k.value})},m.createElement(U,{value:k[f],active:s&&I(k[f])===I(s[f]),onClick:()=>u(k.value)})))))},m.createElement(U,{value:l,style:{margin:4}})),m.createElement(Fe,{id:he(n),value:a,onChange:k=>u(k.target.value),onFocus:k=>k.target.select(),readOnly:w,placeholder:"Choose color..."}),a?m.createElement(Te,{onClick:g}):null)},cr=rr;export{rr as ColorControl,cr as default};
