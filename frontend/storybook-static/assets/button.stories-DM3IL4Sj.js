import{r as v}from"./index-D4H_InIO.js";import{j as H}from"./jsx-runtime-BO8uF4Og.js";function ce(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Ke(...e){return t=>{let r=!1;const o=e.map(s=>{const n=ce(s,t);return!r&&typeof n=="function"&&(r=!0),n});if(r)return()=>{for(let s=0;s<o.length;s++){const n=o[s];typeof n=="function"?n():ce(e[s],null)}}}}var Be=v.forwardRef((e,t)=>{const{children:r,...o}=e,s=v.Children.toArray(r),n=s.find(De);if(n){const i=n.props.children,a=s.map(d=>d===n?v.Children.count(i)>1?v.Children.only(null):v.isValidElement(i)?i.props.children:null:d);return H.jsx(Q,{...o,ref:t,children:v.isValidElement(i)?v.cloneElement(i,void 0,a):null})}return H.jsx(Q,{...o,ref:t,children:r})});Be.displayName="Slot";var Q=v.forwardRef((e,t)=>{const{children:r,...o}=e;if(v.isValidElement(r)){const s=Ye(r);return v.cloneElement(r,{...Qe(o,r.props),ref:t?Ke(t,s):s})}return v.Children.count(r)>1?v.Children.only(null):null});Q.displayName="SlotClone";var Xe=({children:e})=>H.jsx(H.Fragment,{children:e});function De(e){return v.isValidElement(e)&&e.type===Xe}function Qe(e,t){const r={...t};for(const o in t){const s=e[o],n=t[o];/^on[A-Z]/.test(o)?s&&n?r[o]=(...a)=>{n(...a),s(...a)}:s&&(r[o]=s):o==="style"?r[o]={...s,...n}:o==="className"&&(r[o]=[s,n].filter(Boolean).join(" "))}return{...e,...r}}function Ye(e){var o,s;let t=(o=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:o.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(t=(s=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:s.get,r=t&&"isReactWarning"in t&&t.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}function $e(e){var t,r,o="";if(typeof e=="string"||typeof e=="number")o+=e;else if(typeof e=="object")if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(r=$e(e[t]))&&(o&&(o+=" "),o+=r)}else for(r in e)e[r]&&(o&&(o+=" "),o+=r);return o}function Ue(){for(var e,t,r=0,o="",s=arguments.length;r<s;r++)(e=arguments[r])&&(t=$e(e))&&(o&&(o+=" "),o+=t);return o}const de=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,ue=Ue,er=(e,t)=>r=>{var o;if((t==null?void 0:t.variants)==null)return ue(e,r==null?void 0:r.class,r==null?void 0:r.className);const{variants:s,defaultVariants:n}=t,i=Object.keys(s).map(c=>{const f=r==null?void 0:r[c],h=n==null?void 0:n[c];if(f===null)return null;const m=de(f)||de(h);return s[c][m]}),a=r&&Object.entries(r).reduce((c,f)=>{let[h,m]=f;return m===void 0||(c[h]=m),c},{}),d=t==null||(o=t.compoundVariants)===null||o===void 0?void 0:o.reduce((c,f)=>{let{class:h,className:m,...k}=f;return Object.entries(k).every(x=>{let[b,u]=x;return Array.isArray(u)?u.includes({...n,...a}[b]):{...n,...a}[b]===u})?[...c,h,m]:c},[]);return ue(e,i,d,r==null?void 0:r.class,r==null?void 0:r.className)},ee="-",rr=e=>{const t=or(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:i=>{const a=i.split(ee);return a[0]===""&&a.length!==1&&a.shift(),Fe(a,t)||tr(i)},getConflictingClassGroupIds:(i,a)=>{const d=r[i]||[];return a&&o[i]?[...d,...o[i]]:d}}},Fe=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const r=e[0],o=t.nextPart.get(r),s=o?Fe(e.slice(1),o):void 0;if(s)return s;if(t.validators.length===0)return;const n=e.join(ee);return(i=t.validators.find(({validator:a})=>a(n)))==null?void 0:i.classGroupId},pe=/^\[(.+)\]$/,tr=e=>{if(pe.test(e)){const t=pe.exec(e)[1],r=t==null?void 0:t.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},or=e=>{const{theme:t,prefix:r}=e,o={nextPart:new Map,validators:[]};return sr(Object.entries(e.classGroups),r).forEach(([n,i])=>{Y(i,o,n,t)}),o},Y=(e,t,r,o)=>{e.forEach(s=>{if(typeof s=="string"){const n=s===""?t:ge(t,s);n.classGroupId=r;return}if(typeof s=="function"){if(nr(s)){Y(s(o),t,r,o);return}t.validators.push({validator:s,classGroupId:r});return}Object.entries(s).forEach(([n,i])=>{Y(i,ge(t,n),r,o)})})},ge=(e,t)=>{let r=e;return t.split(ee).forEach(o=>{r.nextPart.has(o)||r.nextPart.set(o,{nextPart:new Map,validators:[]}),r=r.nextPart.get(o)}),r},nr=e=>e.isThemeGetter,sr=(e,t)=>t?e.map(([r,o])=>{const s=o.map(n=>typeof n=="string"?t+n:typeof n=="object"?Object.fromEntries(Object.entries(n).map(([i,a])=>[t+i,a])):n);return[r,s]}):e,ar=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,o=new Map;const s=(n,i)=>{r.set(n,i),t++,t>e&&(t=0,o=r,r=new Map)};return{get(n){let i=r.get(n);if(i!==void 0)return i;if((i=o.get(n))!==void 0)return s(n,i),i},set(n,i){r.has(n)?r.set(n,i):s(n,i)}}},qe="!",ir=e=>{const{separator:t,experimentalParseClassName:r}=e,o=t.length===1,s=t[0],n=t.length,i=a=>{const d=[];let c=0,f=0,h;for(let u=0;u<a.length;u++){let y=a[u];if(c===0){if(y===s&&(o||a.slice(u,u+n)===t)){d.push(a.slice(f,u)),f=u+n;continue}if(y==="/"){h=u;continue}}y==="["?c++:y==="]"&&c--}const m=d.length===0?a:a.substring(f),k=m.startsWith(qe),x=k?m.substring(1):m,b=h&&h>f?h-f:void 0;return{modifiers:d,hasImportantModifier:k,baseClassName:x,maybePostfixModifierPosition:b}};return r?a=>r({className:a,parseClassName:i}):i},lr=e=>{if(e.length<=1)return e;const t=[];let r=[];return e.forEach(o=>{o[0]==="["?(t.push(...r.sort(),o),r=[]):r.push(o)}),t.push(...r.sort()),t},cr=e=>({cache:ar(e.cacheSize),parseClassName:ir(e),...rr(e)}),dr=/\s+/,ur=(e,t)=>{const{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:s}=t,n=[],i=e.trim().split(dr);let a="";for(let d=i.length-1;d>=0;d-=1){const c=i[d],{modifiers:f,hasImportantModifier:h,baseClassName:m,maybePostfixModifierPosition:k}=r(c);let x=!!k,b=o(x?m.substring(0,k):m);if(!b){if(!x){a=c+(a.length>0?" "+a:a);continue}if(b=o(m),!b){a=c+(a.length>0?" "+a:a);continue}x=!1}const u=lr(f).join(":"),y=h?u+qe:u,w=y+b;if(n.includes(w))continue;n.push(w);const M=s(b,x);for(let R=0;R<M.length;++R){const I=M[R];n.push(y+I)}a=c+(a.length>0?" "+a:a)}return a};function pr(){let e=0,t,r,o="";for(;e<arguments.length;)(t=arguments[e++])&&(r=He(t))&&(o&&(o+=" "),o+=r);return o}const He=e=>{if(typeof e=="string")return e;let t,r="";for(let o=0;o<e.length;o++)e[o]&&(t=He(e[o]))&&(r&&(r+=" "),r+=t);return r};function gr(e,...t){let r,o,s,n=i;function i(d){const c=t.reduce((f,h)=>h(f),e());return r=cr(c),o=r.cache.get,s=r.cache.set,n=a,a(d)}function a(d){const c=o(d);if(c)return c;const f=ur(d,r);return s(d,f),f}return function(){return n(pr.apply(null,arguments))}}const p=e=>{const t=r=>r[e]||[];return t.isThemeGetter=!0,t},Ze=/^\[(?:([a-z-]+):)?(.+)\]$/i,fr=/^\d+\/\d+$/,br=new Set(["px","full","screen"]),mr=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,hr=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,yr=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,vr=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,xr=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,S=e=>G(e)||br.has(e)||fr.test(e),z=e=>j(e,"length",Gr),G=e=>!!e&&!Number.isNaN(Number(e)),D=e=>j(e,"number",G),E=e=>!!e&&Number.isInteger(Number(e)),wr=e=>e.endsWith("%")&&G(e.slice(0,-1)),l=e=>Ze.test(e),A=e=>mr.test(e),kr=new Set(["length","size","percentage"]),Cr=e=>j(e,kr,Je),Sr=e=>j(e,"position",Je),zr=new Set(["image","url"]),Ar=e=>j(e,zr,Mr),Rr=e=>j(e,"",jr),N=()=>!0,j=(e,t,r)=>{const o=Ze.exec(e);return o?o[1]?typeof t=="string"?o[1]===t:t.has(o[1]):r(o[2]):!1},Gr=e=>hr.test(e)&&!yr.test(e),Je=()=>!1,jr=e=>vr.test(e),Mr=e=>xr.test(e),Pr=()=>{const e=p("colors"),t=p("spacing"),r=p("blur"),o=p("brightness"),s=p("borderColor"),n=p("borderRadius"),i=p("borderSpacing"),a=p("borderWidth"),d=p("contrast"),c=p("grayscale"),f=p("hueRotate"),h=p("invert"),m=p("gap"),k=p("gradientColorStops"),x=p("gradientColorStopPositions"),b=p("inset"),u=p("margin"),y=p("opacity"),w=p("padding"),M=p("saturate"),R=p("scale"),I=p("sepia"),te=p("skew"),oe=p("space"),ne=p("translate"),Z=()=>["auto","contain","none"],J=()=>["auto","hidden","clip","visible","scroll"],K=()=>["auto",l,t],g=()=>[l,t],se=()=>["",S,z],V=()=>["auto",G,l],ae=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],L=()=>["solid","dashed","dotted","double","none"],ie=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],X=()=>["start","end","center","between","around","evenly","stretch"],P=()=>["","0",l],le=()=>["auto","avoid","all","avoid-page","page","left","right","column"],C=()=>[G,l];return{cacheSize:500,separator:":",theme:{colors:[N],spacing:[S,z],blur:["none","",A,l],brightness:C(),borderColor:[e],borderRadius:["none","","full",A,l],borderSpacing:g(),borderWidth:se(),contrast:C(),grayscale:P(),hueRotate:C(),invert:P(),gap:g(),gradientColorStops:[e],gradientColorStopPositions:[wr,z],inset:K(),margin:K(),opacity:C(),padding:g(),saturate:C(),scale:C(),sepia:P(),skew:C(),space:g(),translate:g()},classGroups:{aspect:[{aspect:["auto","square","video",l]}],container:["container"],columns:[{columns:[A]}],"break-after":[{"break-after":le()}],"break-before":[{"break-before":le()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...ae(),l]}],overflow:[{overflow:J()}],"overflow-x":[{"overflow-x":J()}],"overflow-y":[{"overflow-y":J()}],overscroll:[{overscroll:Z()}],"overscroll-x":[{"overscroll-x":Z()}],"overscroll-y":[{"overscroll-y":Z()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[b]}],"inset-x":[{"inset-x":[b]}],"inset-y":[{"inset-y":[b]}],start:[{start:[b]}],end:[{end:[b]}],top:[{top:[b]}],right:[{right:[b]}],bottom:[{bottom:[b]}],left:[{left:[b]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",E,l]}],basis:[{basis:K()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",l]}],grow:[{grow:P()}],shrink:[{shrink:P()}],order:[{order:["first","last","none",E,l]}],"grid-cols":[{"grid-cols":[N]}],"col-start-end":[{col:["auto",{span:["full",E,l]},l]}],"col-start":[{"col-start":V()}],"col-end":[{"col-end":V()}],"grid-rows":[{"grid-rows":[N]}],"row-start-end":[{row:["auto",{span:[E,l]},l]}],"row-start":[{"row-start":V()}],"row-end":[{"row-end":V()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",l]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",l]}],gap:[{gap:[m]}],"gap-x":[{"gap-x":[m]}],"gap-y":[{"gap-y":[m]}],"justify-content":[{justify:["normal",...X()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...X(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...X(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[w]}],px:[{px:[w]}],py:[{py:[w]}],ps:[{ps:[w]}],pe:[{pe:[w]}],pt:[{pt:[w]}],pr:[{pr:[w]}],pb:[{pb:[w]}],pl:[{pl:[w]}],m:[{m:[u]}],mx:[{mx:[u]}],my:[{my:[u]}],ms:[{ms:[u]}],me:[{me:[u]}],mt:[{mt:[u]}],mr:[{mr:[u]}],mb:[{mb:[u]}],ml:[{ml:[u]}],"space-x":[{"space-x":[oe]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[oe]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",l,t]}],"min-w":[{"min-w":[l,t,"min","max","fit"]}],"max-w":[{"max-w":[l,t,"none","full","min","max","fit","prose",{screen:[A]},A]}],h:[{h:[l,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[l,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[l,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[l,t,"auto","min","max","fit"]}],"font-size":[{text:["base",A,z]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",D]}],"font-family":[{font:[N]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",l]}],"line-clamp":[{"line-clamp":["none",G,D]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",S,l]}],"list-image":[{"list-image":["none",l]}],"list-style-type":[{list:["none","disc","decimal",l]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[y]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...L(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",S,z]}],"underline-offset":[{"underline-offset":["auto",S,l]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:g()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",l]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",l]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[y]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...ae(),Sr]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Cr]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Ar]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[x]}],"gradient-via-pos":[{via:[x]}],"gradient-to-pos":[{to:[x]}],"gradient-from":[{from:[k]}],"gradient-via":[{via:[k]}],"gradient-to":[{to:[k]}],rounded:[{rounded:[n]}],"rounded-s":[{"rounded-s":[n]}],"rounded-e":[{"rounded-e":[n]}],"rounded-t":[{"rounded-t":[n]}],"rounded-r":[{"rounded-r":[n]}],"rounded-b":[{"rounded-b":[n]}],"rounded-l":[{"rounded-l":[n]}],"rounded-ss":[{"rounded-ss":[n]}],"rounded-se":[{"rounded-se":[n]}],"rounded-ee":[{"rounded-ee":[n]}],"rounded-es":[{"rounded-es":[n]}],"rounded-tl":[{"rounded-tl":[n]}],"rounded-tr":[{"rounded-tr":[n]}],"rounded-br":[{"rounded-br":[n]}],"rounded-bl":[{"rounded-bl":[n]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[y]}],"border-style":[{border:[...L(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[y]}],"divide-style":[{divide:L()}],"border-color":[{border:[s]}],"border-color-x":[{"border-x":[s]}],"border-color-y":[{"border-y":[s]}],"border-color-s":[{"border-s":[s]}],"border-color-e":[{"border-e":[s]}],"border-color-t":[{"border-t":[s]}],"border-color-r":[{"border-r":[s]}],"border-color-b":[{"border-b":[s]}],"border-color-l":[{"border-l":[s]}],"divide-color":[{divide:[s]}],"outline-style":[{outline:["",...L()]}],"outline-offset":[{"outline-offset":[S,l]}],"outline-w":[{outline:[S,z]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:se()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[y]}],"ring-offset-w":[{"ring-offset":[S,z]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",A,Rr]}],"shadow-color":[{shadow:[N]}],opacity:[{opacity:[y]}],"mix-blend":[{"mix-blend":[...ie(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":ie()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[o]}],contrast:[{contrast:[d]}],"drop-shadow":[{"drop-shadow":["","none",A,l]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[f]}],invert:[{invert:[h]}],saturate:[{saturate:[M]}],sepia:[{sepia:[I]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[d]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[f]}],"backdrop-invert":[{"backdrop-invert":[h]}],"backdrop-opacity":[{"backdrop-opacity":[y]}],"backdrop-saturate":[{"backdrop-saturate":[M]}],"backdrop-sepia":[{"backdrop-sepia":[I]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",l]}],duration:[{duration:C()}],ease:[{ease:["linear","in","out","in-out",l]}],delay:[{delay:C()}],animate:[{animate:["none","spin","ping","pulse","bounce",l]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[R]}],"scale-x":[{"scale-x":[R]}],"scale-y":[{"scale-y":[R]}],rotate:[{rotate:[E,l]}],"translate-x":[{"translate-x":[ne]}],"translate-y":[{"translate-y":[ne]}],"skew-x":[{"skew-x":[te]}],"skew-y":[{"skew-y":[te]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",l]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",l]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":g()}],"scroll-mx":[{"scroll-mx":g()}],"scroll-my":[{"scroll-my":g()}],"scroll-ms":[{"scroll-ms":g()}],"scroll-me":[{"scroll-me":g()}],"scroll-mt":[{"scroll-mt":g()}],"scroll-mr":[{"scroll-mr":g()}],"scroll-mb":[{"scroll-mb":g()}],"scroll-ml":[{"scroll-ml":g()}],"scroll-p":[{"scroll-p":g()}],"scroll-px":[{"scroll-px":g()}],"scroll-py":[{"scroll-py":g()}],"scroll-ps":[{"scroll-ps":g()}],"scroll-pe":[{"scroll-pe":g()}],"scroll-pt":[{"scroll-pt":g()}],"scroll-pr":[{"scroll-pr":g()}],"scroll-pb":[{"scroll-pb":g()}],"scroll-pl":[{"scroll-pl":g()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",l]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[S,z,D]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},Er=gr(Pr);function Nr(...e){return Er(Ue(e))}const Ir=er("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),re=v.forwardRef(({className:e,variant:t,size:r,asChild:o=!1,...s},n)=>{const i=o?Be:"button";return v.createElement(i,{className:Nr(Ir({variant:t,size:r,className:e})),ref:n,...s})});re.displayName="Button";re.__docgenInfo={description:"",methods:[],displayName:"Button",props:{asChild:{required:!1,tsType:{name:"boolean"},description:"",defaultValue:{value:"false",computed:!1}}},composes:["VariantProps"]};const Or={title:"UI/Button",component:re,parameters:{layout:"centered"},tags:["autodocs"],argTypes:{variant:{control:"select",options:["default","destructive","outline","secondary","ghost","link"]},size:{control:"select",options:["default","sm","lg","icon"]}}},O={args:{children:"Button"}},T={args:{variant:"secondary",children:"Secondary"}},W={args:{variant:"destructive",children:"Delete"}},_={args:{variant:"outline",children:"Outline"}},B={args:{variant:"ghost",children:"Ghost"}},$={args:{variant:"link",children:"Link"}},U={args:{size:"sm",children:"Small"}},F={args:{size:"lg",children:"Large"}},q={args:{size:"icon",children:"🚀"}};var fe,be,me;O.parameters={...O.parameters,docs:{...(fe=O.parameters)==null?void 0:fe.docs,source:{originalSource:`{
  args: {
    children: 'Button'
  }
}`,...(me=(be=O.parameters)==null?void 0:be.docs)==null?void 0:me.source}}};var he,ye,ve;T.parameters={...T.parameters,docs:{...(he=T.parameters)==null?void 0:he.docs,source:{originalSource:`{
  args: {
    variant: 'secondary',
    children: 'Secondary'
  }
}`,...(ve=(ye=T.parameters)==null?void 0:ye.docs)==null?void 0:ve.source}}};var xe,we,ke;W.parameters={...W.parameters,docs:{...(xe=W.parameters)==null?void 0:xe.docs,source:{originalSource:`{
  args: {
    variant: 'destructive',
    children: 'Delete'
  }
}`,...(ke=(we=W.parameters)==null?void 0:we.docs)==null?void 0:ke.source}}};var Ce,Se,ze;_.parameters={..._.parameters,docs:{...(Ce=_.parameters)==null?void 0:Ce.docs,source:{originalSource:`{
  args: {
    variant: 'outline',
    children: 'Outline'
  }
}`,...(ze=(Se=_.parameters)==null?void 0:Se.docs)==null?void 0:ze.source}}};var Ae,Re,Ge;B.parameters={...B.parameters,docs:{...(Ae=B.parameters)==null?void 0:Ae.docs,source:{originalSource:`{
  args: {
    variant: 'ghost',
    children: 'Ghost'
  }
}`,...(Ge=(Re=B.parameters)==null?void 0:Re.docs)==null?void 0:Ge.source}}};var je,Me,Pe;$.parameters={...$.parameters,docs:{...(je=$.parameters)==null?void 0:je.docs,source:{originalSource:`{
  args: {
    variant: 'link',
    children: 'Link'
  }
}`,...(Pe=(Me=$.parameters)==null?void 0:Me.docs)==null?void 0:Pe.source}}};var Ee,Ne,Ie;U.parameters={...U.parameters,docs:{...(Ee=U.parameters)==null?void 0:Ee.docs,source:{originalSource:`{
  args: {
    size: 'sm',
    children: 'Small'
  }
}`,...(Ie=(Ne=U.parameters)==null?void 0:Ne.docs)==null?void 0:Ie.source}}};var Ve,Le,Oe;F.parameters={...F.parameters,docs:{...(Ve=F.parameters)==null?void 0:Ve.docs,source:{originalSource:`{
  args: {
    size: 'lg',
    children: 'Large'
  }
}`,...(Oe=(Le=F.parameters)==null?void 0:Le.docs)==null?void 0:Oe.source}}};var Te,We,_e;q.parameters={...q.parameters,docs:{...(Te=q.parameters)==null?void 0:Te.docs,source:{originalSource:`{
  args: {
    size: 'icon',
    children: '🚀'
  }
}`,...(_e=(We=q.parameters)==null?void 0:We.docs)==null?void 0:_e.source}}};const Tr=["Default","Secondary","Destructive","Outline","Ghost","Link","Small","Large","Icon"];export{O as Default,W as Destructive,B as Ghost,q as Icon,F as Large,$ as Link,_ as Outline,T as Secondary,U as Small,Tr as __namedExportsOrder,Or as default};
