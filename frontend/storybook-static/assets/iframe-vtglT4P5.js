const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./button.stories-DM3IL4Sj.js","./index-D4H_InIO.js","./jsx-runtime-BO8uF4Og.js","./preview-Bt2nRSM_.js","./preview-CTHTOOCa.css","./entry-preview-Mp3O3HVQ.js","./chunk-XP5HYGXS-BpfKkqn7.js","./entry-preview-docs-BqSPhk5G.js","./index-CXQShRbs.js","./preview-B8lJiyuQ.js","./index-DrFu-skq.js","./preview-BWzBA1C2.js","./preview-N1j9pG9L.js","./index-Bd6o6BX5.js"])))=>i.map(i=>d[i]);
(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))o(a);new MutationObserver(a=>{for(const i of a)if(i.type==="childList")for(const u of i.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&o(u)}).observe(document,{childList:!0,subtree:!0});function r(a){const i={};return a.integrity&&(i.integrity=a.integrity),a.referrerPolicy&&(i.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?i.credentials="include":a.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function o(a){if(a.ep)return;a.ep=!0;const i=r(a);fetch(a.href,i)}})();const scriptRel="modulepreload",assetsURL=function(t,e){return new URL(t,e).href},seen={},__vitePreload=function(e,r,o){let a=Promise.resolve();if(r&&r.length>0){let u=function(m){return Promise.all(m.map(h=>Promise.resolve(h).then(g=>({status:"fulfilled",value:g}),g=>({status:"rejected",reason:g}))))};const l=document.getElementsByTagName("link"),c=document.querySelector("meta[property=csp-nonce]"),d=(c==null?void 0:c.nonce)||(c==null?void 0:c.getAttribute("nonce"));a=u(r.map(m=>{if(m=assetsURL(m,o),m in seen)return;seen[m]=!0;const h=m.endsWith(".css"),g=h?'[rel="stylesheet"]':"";if(!!o)for(let le=l.length-1;le>=0;le--){const J=l[le];if(J.href===m&&(!h||J.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${m}"]${g}`))return;const ne=document.createElement("link");if(ne.rel=h?"stylesheet":scriptRel,h||(ne.as="script"),ne.crossOrigin="",ne.href=m,d&&ne.setAttribute("nonce",d),document.head.appendChild(ne),h)return new Promise((le,J)=>{ne.addEventListener("load",le),ne.addEventListener("error",()=>J(new Error(`Unable to preload CSS for ${m}`)))})}))}function i(u){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=u,window.dispatchEvent(l),!l.defaultPrevented)throw u}return a.then(u=>{for(const l of u||[])l.status==="rejected"&&i(l.reason);return e().catch(i)})};var tl=Object.create,et=Object.defineProperty,ol=Object.getOwnPropertyDescriptor,nl=Object.getOwnPropertyNames,sl=Object.getPrototypeOf,il=Object.prototype.hasOwnProperty,n=(t,e)=>et(t,"name",{value:e,configurable:!0}),cr=(t=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(t,{get:(e,r)=>(typeof require<"u"?require:e)[r]}):t)(function(t){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+t+'" is not supported')}),q=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),_e=(t,e)=>{for(var r in e)et(t,r,{get:e[r],enumerable:!0})},al=(t,e,r,o)=>{if(e&&typeof e=="object"||typeof e=="function")for(let a of nl(e))!il.call(t,a)&&a!==r&&et(t,a,{get:()=>e[a],enumerable:!(o=ol(e,a))||o.enumerable});return t},ue=(t,e,r)=>(r=t!=null?tl(sl(t)):{},al(et(r,"default",{value:t,enumerable:!0}),t)),it=q((t,e)=>{(function(r){if(typeof t=="object"&&typeof e<"u")e.exports=r();else if(typeof define=="function"&&define.amd)define([],r);else{var o;typeof window<"u"?o=window:typeof global<"u"?o=global:typeof self<"u"?o=self:o=this,o.memoizerific=r()}})(function(){return n(function r(o,a,i){function u(d,m){if(!a[d]){if(!o[d]){var h=typeof cr=="function"&&cr;if(!m&&h)return h(d,!0);if(l)return l(d,!0);var g=new Error("Cannot find module '"+d+"'");throw g.code="MODULE_NOT_FOUND",g}var re=a[d]={exports:{}};o[d][0].call(re.exports,function(ne){var le=o[d][1][ne];return u(le||ne)},re,re.exports,r,o,a,i)}return a[d].exports}n(u,"s");for(var l=typeof cr=="function"&&cr,c=0;c<i.length;c++)u(i[c]);return u},"e")({1:[function(r,o,a){o.exports=function(i){if(typeof Map!="function"||i){var u=r("./similar");return new u}else return new Map}},{"./similar":2}],2:[function(r,o,a){function i(){return this.list=[],this.lastItem=void 0,this.size=0,this}n(i,"Similar"),i.prototype.get=function(u){var l;if(this.lastItem&&this.isEqual(this.lastItem.key,u))return this.lastItem.val;if(l=this.indexOf(u),l>=0)return this.lastItem=this.list[l],this.list[l].val},i.prototype.set=function(u,l){var c;return this.lastItem&&this.isEqual(this.lastItem.key,u)?(this.lastItem.val=l,this):(c=this.indexOf(u),c>=0?(this.lastItem=this.list[c],this.list[c].val=l,this):(this.lastItem={key:u,val:l},this.list.push(this.lastItem),this.size++,this))},i.prototype.delete=function(u){var l;if(this.lastItem&&this.isEqual(this.lastItem.key,u)&&(this.lastItem=void 0),l=this.indexOf(u),l>=0)return this.size--,this.list.splice(l,1)[0]},i.prototype.has=function(u){var l;return this.lastItem&&this.isEqual(this.lastItem.key,u)?!0:(l=this.indexOf(u),l>=0?(this.lastItem=this.list[l],!0):!1)},i.prototype.forEach=function(u,l){var c;for(c=0;c<this.size;c++)u.call(l||this,this.list[c].val,this.list[c].key,this)},i.prototype.indexOf=function(u){var l;for(l=0;l<this.size;l++)if(this.isEqual(this.list[l].key,u))return l;return-1},i.prototype.isEqual=function(u,l){return u===l||u!==u&&l!==l},o.exports=i},{}],3:[function(r,o,a){var i=r("map-or-similar");o.exports=function(d){var m=new i(!1),h=[];return function(g){var re=n(function(){var ne=m,le,J,ce=arguments.length-1,F=Array(ce+1),se=!0,he;if((re.numArgs||re.numArgs===0)&&re.numArgs!==ce+1)throw new Error("Memoizerific functions should always be called with the same number of arguments");for(he=0;he<ce;he++){if(F[he]={cacheItem:ne,arg:arguments[he]},ne.has(arguments[he])){ne=ne.get(arguments[he]);continue}se=!1,le=new i(!1),ne.set(arguments[he],le),ne=le}return se&&(ne.has(arguments[ce])?J=ne.get(arguments[ce]):se=!1),se||(J=g.apply(null,arguments),ne.set(arguments[ce],J)),d>0&&(F[ce]={cacheItem:ne,arg:arguments[ce]},se?u(h,F):h.push(F),h.length>d&&l(h.shift())),re.wasMemoized=se,re.numArgs=ce+1,J},"memoizerific");return re.limit=d,re.wasMemoized=!1,re.cache=m,re.lru=h,re}};function u(d,m){var h=d.length,g=m.length,re,ne,le;for(ne=0;ne<h;ne++){for(re=!0,le=0;le<g;le++)if(!c(d[ne][le].arg,m[le].arg)){re=!1;break}if(re)break}d.push(d.splice(ne,1)[0])}n(u,"moveToMostRecentLru");function l(d){var m=d.length,h=d[m-1],g,re;for(h.cacheItem.delete(h.arg),re=m-2;re>=0&&(h=d[re],g=h.cacheItem.get(h.arg),!g||!g.size);re--)h.cacheItem.delete(h.arg)}n(l,"removeCachedResult");function c(d,m){return d===m||d!==d&&m!==m}n(c,"isEqual")},{"map-or-similar":1}]},{},[3])(3)})}),wi=q(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isEqual=function(){var e=Object.prototype.toString,r=Object.getPrototypeOf,o=Object.getOwnPropertySymbols?function(a){return Object.keys(a).concat(Object.getOwnPropertySymbols(a))}:Object.keys;return function(a,i){return n(function u(l,c,d){var m,h,g,re=e.call(l),ne=e.call(c);if(l===c)return!0;if(l==null||c==null)return!1;if(d.indexOf(l)>-1&&d.indexOf(c)>-1)return!0;if(d.push(l,c),re!=ne||(m=o(l),h=o(c),m.length!=h.length||m.some(function(le){return!u(l[le],c[le],d)})))return!1;switch(re.slice(8,-1)){case"Symbol":return l.valueOf()==c.valueOf();case"Date":case"Number":return+l==+c||+l!=+l&&+c!=+c;case"RegExp":case"Function":case"String":case"Boolean":return""+l==""+c;case"Set":case"Map":m=l.entries(),h=c.entries();do if(!u((g=m.next()).value,h.next().value,d))return!1;while(!g.done);return!0;case"ArrayBuffer":l=new Uint8Array(l),c=new Uint8Array(c);case"DataView":l=new Uint8Array(l.buffer),c=new Uint8Array(c.buffer);case"Float32Array":case"Float64Array":case"Int8Array":case"Int16Array":case"Int32Array":case"Uint8Array":case"Uint16Array":case"Uint32Array":case"Uint8ClampedArray":case"Arguments":case"Array":if(l.length!=c.length)return!1;for(g=0;g<l.length;g++)if((g in l||g in c)&&(g in l!=g in c||!u(l[g],c[g],d)))return!1;return!0;case"Object":return u(r(l),r(c),d);default:return!1}},"n")(a,i,[])}}()}),qn=q(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.encodeString=o;var e=Array.from({length:256},(a,i)=>"%"+((i<16?"0":"")+i.toString(16)).toUpperCase()),r=new Int8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,1,1,1,1,0,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,0]);function o(a){let i=a.length;if(i===0)return"";let u="",l=0,c=0;e:for(;c<i;c++){let d=a.charCodeAt(c);for(;d<128;){if(r[d]!==1&&(l<c&&(u+=a.slice(l,c)),l=c+1,u+=e[d]),++c===i)break e;d=a.charCodeAt(c)}if(l<c&&(u+=a.slice(l,c)),d<2048){l=c+1,u+=e[192|d>>6]+e[128|d&63];continue}if(d<55296||d>=57344){l=c+1,u+=e[224|d>>12]+e[128|d>>6&63]+e[128|d&63];continue}if(++c,c>=i)throw new Error("URI malformed");let m=a.charCodeAt(c)&1023;l=c+1,d=65536+((d&1023)<<10|m),u+=e[240|d>>18]+e[128|d>>12&63]+e[128|d>>6&63]+e[128|d&63]}return l===0?a:l<i?u+a.slice(l):u}n(o,"encodeString")}),It=q(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultOptions=t.defaultShouldSerializeObject=t.defaultValueSerializer=void 0;var e=qn(),r=n(i=>{switch(typeof i){case"string":return(0,e.encodeString)(i);case"bigint":case"boolean":return""+i;case"number":if(Number.isFinite(i))return i<1e21?""+i:(0,e.encodeString)(""+i);break}return i instanceof Date?(0,e.encodeString)(i.toISOString()):""},"defaultValueSerializer");t.defaultValueSerializer=r;var o=n(i=>i instanceof Date,"defaultShouldSerializeObject");t.defaultShouldSerializeObject=o;var a=n(i=>i,"identityFunc");t.defaultOptions={nesting:!0,nestingSyntax:"dot",arrayRepeat:!1,arrayRepeatSyntax:"repeat",delimiter:38,valueDeserializer:a,valueSerializer:t.defaultValueSerializer,keyDeserializer:a,shouldSerializeObject:t.defaultShouldSerializeObject}}),Vn=q(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getDeepObject=a,t.stringifyObject=m;var e=It(),r=qn();function o(h){return h==="__proto__"||h==="constructor"||h==="prototype"}n(o,"isPrototypeKey");function a(h,g,re,ne,le){if(o(g))return h;let J=h[g];return typeof J=="object"&&J!==null?J:!ne&&(le||typeof re=="number"||typeof re=="string"&&re*0===0&&re.indexOf(".")===-1)?h[g]=[]:h[g]={}}n(a,"getDeepObject");var i=20,u="[]",l="[",c="]",d=".";function m(h,g,re=0,ne,le){let{nestingSyntax:J=e.defaultOptions.nestingSyntax,arrayRepeat:ce=e.defaultOptions.arrayRepeat,arrayRepeatSyntax:F=e.defaultOptions.arrayRepeatSyntax,nesting:se=e.defaultOptions.nesting,delimiter:he=e.defaultOptions.delimiter,valueSerializer:Ve=e.defaultOptions.valueSerializer,shouldSerializeObject:ve=e.defaultOptions.shouldSerializeObject}=g,we=typeof he=="number"?String.fromCharCode(he):he,Nt=le===!0&&ce,Bt=J==="dot"||J==="js"&&!le;if(re>i)return"";let Ft="",jt=!0,qe=!1;for(let Dt in h){let p=h[Dt],A;ne?(A=ne,Nt?F==="bracket"&&(A+=u):Bt?(A+=d,A+=Dt):(A+=l,A+=Dt,A+=c)):A=Dt,jt||(Ft+=we),typeof p=="object"&&p!==null&&!ve(p)?(qe=p.pop!==void 0,(se||ce&&qe)&&(Ft+=m(p,g,re+1,A,qe))):(Ft+=(0,r.encodeString)(A),Ft+="=",Ft+=Ve(p,Dt)),jt&&(jt=!1)}return Ft}n(m,"stringifyObject")}),na=q((t,e)=>{var r=12,o=0,a=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,6,7,7,7,7,7,7,7,7,7,7,7,7,8,7,7,10,9,9,9,11,4,4,4,4,4,4,4,4,4,4,4,0,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,24,36,48,60,72,84,96,0,12,12,12,0,0,0,0,0,0,0,0,0,0,0,24,0,0,0,0,0,0,0,0,0,24,24,24,0,0,0,0,0,0,0,0,0,24,24,0,0,0,0,0,0,0,0,0,0,48,48,48,0,0,0,0,0,0,0,0,0,0,48,48,0,0,0,0,0,0,0,0,0,48,0,0,0,0,0,0,0,0,0,0,127,63,63,63,0,31,15,15,15,7,7,7];function i(c){var d=c.indexOf("%");if(d===-1)return c;for(var m=c.length,h="",g=0,re=0,ne=d,le=r;d>-1&&d<m;){var J=l(c[d+1],4),ce=l(c[d+2],0),F=J|ce,se=a[F];if(le=a[256+le+se],re=re<<6|F&a[364+se],le===r)h+=c.slice(g,ne),h+=re<=65535?String.fromCharCode(re):String.fromCharCode(55232+(re>>10),56320+(re&1023)),re=0,g=d+3,d=ne=c.indexOf("%",g);else{if(le===o)return null;if(d+=3,d<m&&c.charCodeAt(d)===37)continue;return null}}return h+c.slice(g)}n(i,"decodeURIComponent");var u={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};function l(c,d){var m=u[c];return m===void 0?255:m<<d}n(l,"hexCodeToInt"),e.exports=i}),la=q(t=>{var e=t&&t.__importDefault||function(h){return h&&h.__esModule?h:{default:h}};Object.defineProperty(t,"__esModule",{value:!0}),t.numberValueDeserializer=t.numberKeyDeserializer=void 0,t.parse=m;var r=Vn(),o=It(),a=e(na()),i=n(h=>{let g=Number(h);return Number.isNaN(g)?h:g},"numberKeyDeserializer");t.numberKeyDeserializer=i;var u=n(h=>{let g=Number(h);return Number.isNaN(g)?h:g},"numberValueDeserializer");t.numberValueDeserializer=u;var l=/\+/g,c=n(function(){},"Empty");c.prototype=Object.create(null);function d(h,g,re,ne,le){let J=h.substring(g,re);return ne&&(J=J.replace(l," ")),le&&(J=(0,a.default)(J)||J),J}n(d,"computeKeySlice");function m(h,g){let{valueDeserializer:re=o.defaultOptions.valueDeserializer,keyDeserializer:ne=o.defaultOptions.keyDeserializer,arrayRepeatSyntax:le=o.defaultOptions.arrayRepeatSyntax,nesting:J=o.defaultOptions.nesting,arrayRepeat:ce=o.defaultOptions.arrayRepeat,nestingSyntax:F=o.defaultOptions.nestingSyntax,delimiter:se=o.defaultOptions.delimiter}=g??{},he=typeof se=="string"?se.charCodeAt(0):se,Ve=F==="js",ve=new c;if(typeof h!="string")return ve;let we=h.length,Nt="",Bt=-1,Ft=-1,jt=-1,qe=ve,Dt,p="",A="",B=!1,de=!1,pe=!1,Lt=!1,qt=!1,Ut=!1,Mt=!1,Gt=0,lr=-1,zr=-1,Qr=-1;for(let Vt=0;Vt<we+1;Vt++){if(Gt=Vt!==we?h.charCodeAt(Vt):he,Gt===he){if(Mt=Ft>Bt,Mt||(Ft=Vt),jt!==Ft-1&&(A=d(h,jt+1,lr>-1?lr:Ft,pe,B),p=ne(A),Dt!==void 0&&(qe=(0,r.getDeepObject)(qe,Dt,p,Ve&&qt,Ve&&Ut))),Mt||p!==""){Mt&&(Nt=h.slice(Ft+1,Vt),Lt&&(Nt=Nt.replace(l," ")),de&&(Nt=(0,a.default)(Nt)||Nt));let Jr=re(Nt,p);if(ce){let Zr=qe[p];Zr===void 0?lr>-1?qe[p]=[Jr]:qe[p]=Jr:Zr.pop?Zr.push(Jr):qe[p]=[Zr,Jr]}else qe[p]=Jr}Nt="",Bt=Vt,Ft=Vt,B=!1,de=!1,pe=!1,Lt=!1,qt=!1,Ut=!1,lr=-1,jt=Vt,qe=ve,Dt=void 0,p=""}else Gt===93?(ce&&le==="bracket"&&Qr===91&&(lr=zr),J&&(F==="index"||Ve)&&Ft<=Bt&&(jt!==zr&&(A=d(h,jt+1,Vt,pe,B),p=ne(A),Dt!==void 0&&(qe=(0,r.getDeepObject)(qe,Dt,p,void 0,Ve)),Dt=p,pe=!1,B=!1),jt=Vt,Ut=!0,qt=!1)):Gt===46?J&&(F==="dot"||Ve)&&Ft<=Bt&&(jt!==zr&&(A=d(h,jt+1,Vt,pe,B),p=ne(A),Dt!==void 0&&(qe=(0,r.getDeepObject)(qe,Dt,p,Ve)),Dt=p,pe=!1,B=!1),qt=!0,Ut=!1,jt=Vt):Gt===91?J&&(F==="index"||Ve)&&Ft<=Bt&&(jt!==zr&&(A=d(h,jt+1,Vt,pe,B),p=ne(A),Ve&&Dt!==void 0&&(qe=(0,r.getDeepObject)(qe,Dt,p,Ve)),Dt=p,pe=!1,B=!1,qt=!1,Ut=!0),jt=Vt):Gt===61?Ft<=Bt?Ft=Vt:de=!0:Gt===43?Ft>Bt?Lt=!0:pe=!0:Gt===37&&(Ft>Bt?de=!0:B=!0);zr=Vt,Qr=Gt}return ve}n(m,"parse")}),ca=q(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.stringify=r;var e=Vn();function r(o,a){if(o===null||typeof o!="object")return"";let i=a??{};return(0,e.stringifyObject)(o,i)}n(r,"stringify")}),kt=q(t=>{var e=t&&t.__createBinding||(Object.create?function(i,u,l,c){c===void 0&&(c=l);var d=Object.getOwnPropertyDescriptor(u,l);(!d||("get"in d?!u.__esModule:d.writable||d.configurable))&&(d={enumerable:!0,get:n(function(){return u[l]},"get")}),Object.defineProperty(i,c,d)}:function(i,u,l,c){c===void 0&&(c=l),i[c]=u[l]}),r=t&&t.__exportStar||function(i,u){for(var l in i)l!=="default"&&!Object.prototype.hasOwnProperty.call(u,l)&&e(u,i,l)};Object.defineProperty(t,"__esModule",{value:!0}),t.stringify=t.parse=void 0;var o=la();Object.defineProperty(t,"parse",{enumerable:!0,get:n(function(){return o.parse},"get")});var a=ca();Object.defineProperty(t,"stringify",{enumerable:!0,get:n(function(){return a.stringify},"get")}),r(It(),t)}),Kn=q((t,e)=>{e.exports={Aacute:"Á",aacute:"á",Abreve:"Ă",abreve:"ă",ac:"∾",acd:"∿",acE:"∾̳",Acirc:"Â",acirc:"â",acute:"´",Acy:"А",acy:"а",AElig:"Æ",aelig:"æ",af:"⁡",Afr:"𝔄",afr:"𝔞",Agrave:"À",agrave:"à",alefsym:"ℵ",aleph:"ℵ",Alpha:"Α",alpha:"α",Amacr:"Ā",amacr:"ā",amalg:"⨿",amp:"&",AMP:"&",andand:"⩕",And:"⩓",and:"∧",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angmsd:"∡",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"Å",angzarr:"⍼",Aogon:"Ą",aogon:"ą",Aopf:"𝔸",aopf:"𝕒",apacir:"⩯",ap:"≈",apE:"⩰",ape:"≊",apid:"≋",apos:"'",ApplyFunction:"⁡",approx:"≈",approxeq:"≊",Aring:"Å",aring:"å",Ascr:"𝒜",ascr:"𝒶",Assign:"≔",ast:"*",asymp:"≈",asympeq:"≍",Atilde:"Ã",atilde:"ã",Auml:"Ä",auml:"ä",awconint:"∳",awint:"⨑",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",Backslash:"∖",Barv:"⫧",barvee:"⊽",barwed:"⌅",Barwed:"⌆",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",Bcy:"Б",bcy:"б",bdquo:"„",becaus:"∵",because:"∵",Because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",Bernoullis:"ℬ",Beta:"Β",beta:"β",beth:"ℶ",between:"≬",Bfr:"𝔅",bfr:"𝔟",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bNot:"⫭",bnot:"⌐",Bopf:"𝔹",bopf:"𝕓",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxbox:"⧉",boxdl:"┐",boxdL:"╕",boxDl:"╖",boxDL:"╗",boxdr:"┌",boxdR:"╒",boxDr:"╓",boxDR:"╔",boxh:"─",boxH:"═",boxhd:"┬",boxHd:"╤",boxhD:"╥",boxHD:"╦",boxhu:"┴",boxHu:"╧",boxhU:"╨",boxHU:"╩",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxul:"┘",boxuL:"╛",boxUl:"╜",boxUL:"╝",boxur:"└",boxuR:"╘",boxUr:"╙",boxUR:"╚",boxv:"│",boxV:"║",boxvh:"┼",boxvH:"╪",boxVh:"╫",boxVH:"╬",boxvl:"┤",boxvL:"╡",boxVl:"╢",boxVL:"╣",boxvr:"├",boxvR:"╞",boxVr:"╟",boxVR:"╠",bprime:"‵",breve:"˘",Breve:"˘",brvbar:"¦",bscr:"𝒷",Bscr:"ℬ",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsolb:"⧅",bsol:"\\",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",Bumpeq:"≎",bumpeq:"≏",Cacute:"Ć",cacute:"ć",capand:"⩄",capbrcup:"⩉",capcap:"⩋",cap:"∩",Cap:"⋒",capcup:"⩇",capdot:"⩀",CapitalDifferentialD:"ⅅ",caps:"∩︀",caret:"⁁",caron:"ˇ",Cayleys:"ℭ",ccaps:"⩍",Ccaron:"Č",ccaron:"č",Ccedil:"Ç",ccedil:"ç",Ccirc:"Ĉ",ccirc:"ĉ",Cconint:"∰",ccups:"⩌",ccupssm:"⩐",Cdot:"Ċ",cdot:"ċ",cedil:"¸",Cedilla:"¸",cemptyv:"⦲",cent:"¢",centerdot:"·",CenterDot:"·",cfr:"𝔠",Cfr:"ℭ",CHcy:"Ч",chcy:"ч",check:"✓",checkmark:"✓",Chi:"Χ",chi:"χ",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",CircleDot:"⊙",circledR:"®",circledS:"Ⓢ",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",cir:"○",cirE:"⧃",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",clubs:"♣",clubsuit:"♣",colon:":",Colon:"∷",Colone:"⩴",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",Congruent:"≡",conint:"∮",Conint:"∯",ContourIntegral:"∮",copf:"𝕔",Copf:"ℂ",coprod:"∐",Coproduct:"∐",copy:"©",COPY:"©",copysr:"℗",CounterClockwiseContourIntegral:"∳",crarr:"↵",cross:"✗",Cross:"⨯",Cscr:"𝒞",cscr:"𝒸",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",cupbrcap:"⩈",cupcap:"⩆",CupCap:"≍",cup:"∪",Cup:"⋓",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"¤",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",dagger:"†",Dagger:"‡",daleth:"ℸ",darr:"↓",Darr:"↡",dArr:"⇓",dash:"‐",Dashv:"⫤",dashv:"⊣",dbkarow:"⤏",dblac:"˝",Dcaron:"Ď",dcaron:"ď",Dcy:"Д",dcy:"д",ddagger:"‡",ddarr:"⇊",DD:"ⅅ",dd:"ⅆ",DDotrahd:"⤑",ddotseq:"⩷",deg:"°",Del:"∇",Delta:"Δ",delta:"δ",demptyv:"⦱",dfisht:"⥿",Dfr:"𝔇",dfr:"𝔡",dHar:"⥥",dharl:"⇃",dharr:"⇂",DiacriticalAcute:"´",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",diam:"⋄",diamond:"⋄",Diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"¨",DifferentialD:"ⅆ",digamma:"ϝ",disin:"⋲",div:"÷",divide:"÷",divideontimes:"⋇",divonx:"⋇",DJcy:"Ђ",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",Dopf:"𝔻",dopf:"𝕕",Dot:"¨",dot:"˙",DotDot:"⃜",doteq:"≐",doteqdot:"≑",DotEqual:"≐",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",DoubleContourIntegral:"∯",DoubleDot:"¨",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrowBar:"⤓",downarrow:"↓",DownArrow:"↓",Downarrow:"⇓",DownArrowUpArrow:"⇵",DownBreve:"̑",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVectorBar:"⥖",DownLeftVector:"↽",DownRightTeeVector:"⥟",DownRightVectorBar:"⥗",DownRightVector:"⇁",DownTeeArrow:"↧",DownTee:"⊤",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",Dscr:"𝒟",dscr:"𝒹",DScy:"Ѕ",dscy:"ѕ",dsol:"⧶",Dstrok:"Đ",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",DZcy:"Џ",dzcy:"џ",dzigrarr:"⟿",Eacute:"É",eacute:"é",easter:"⩮",Ecaron:"Ě",ecaron:"ě",Ecirc:"Ê",ecirc:"ê",ecir:"≖",ecolon:"≕",Ecy:"Э",ecy:"э",eDDot:"⩷",Edot:"Ė",edot:"ė",eDot:"≑",ee:"ⅇ",efDot:"≒",Efr:"𝔈",efr:"𝔢",eg:"⪚",Egrave:"È",egrave:"è",egs:"⪖",egsdot:"⪘",el:"⪙",Element:"∈",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",Emacr:"Ē",emacr:"ē",empty:"∅",emptyset:"∅",EmptySmallSquare:"◻",emptyv:"∅",EmptyVerySmallSquare:"▫",emsp13:" ",emsp14:" ",emsp:" ",ENG:"Ŋ",eng:"ŋ",ensp:" ",Eogon:"Ę",eogon:"ę",Eopf:"𝔼",eopf:"𝕖",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",Epsilon:"Ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",Equal:"⩵",equals:"=",EqualTilde:"≂",equest:"≟",Equilibrium:"⇌",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erarr:"⥱",erDot:"≓",escr:"ℯ",Escr:"ℰ",esdot:"≐",Esim:"⩳",esim:"≂",Eta:"Η",eta:"η",ETH:"Ð",eth:"ð",Euml:"Ë",euml:"ë",euro:"€",excl:"!",exist:"∃",Exists:"∃",expectation:"ℰ",exponentiale:"ⅇ",ExponentialE:"ⅇ",fallingdotseq:"≒",Fcy:"Ф",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",Ffr:"𝔉",ffr:"𝔣",filig:"ﬁ",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",Fopf:"𝔽",fopf:"𝕗",forall:"∀",ForAll:"∀",fork:"⋔",forkv:"⫙",Fouriertrf:"ℱ",fpartint:"⨍",frac12:"½",frac13:"⅓",frac14:"¼",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"¾",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",fscr:"𝒻",Fscr:"ℱ",gacute:"ǵ",Gamma:"Γ",gamma:"γ",Gammad:"Ϝ",gammad:"ϝ",gap:"⪆",Gbreve:"Ğ",gbreve:"ğ",Gcedil:"Ģ",Gcirc:"Ĝ",gcirc:"ĝ",Gcy:"Г",gcy:"г",Gdot:"Ġ",gdot:"ġ",ge:"≥",gE:"≧",gEl:"⪌",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",gescc:"⪩",ges:"⩾",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",Gfr:"𝔊",gfr:"𝔤",gg:"≫",Gg:"⋙",ggg:"⋙",gimel:"ℷ",GJcy:"Ѓ",gjcy:"ѓ",gla:"⪥",gl:"≷",glE:"⪒",glj:"⪤",gnap:"⪊",gnapprox:"⪊",gne:"⪈",gnE:"≩",gneq:"⪈",gneqq:"≩",gnsim:"⋧",Gopf:"𝔾",gopf:"𝕘",grave:"`",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"𝒢",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",gtcc:"⪧",gtcir:"⩺",gt:">",GT:">",Gt:"≫",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",Hacek:"ˇ",hairsp:" ",half:"½",hamilt:"ℋ",HARDcy:"Ъ",hardcy:"ъ",harrcir:"⥈",harr:"↔",hArr:"⇔",harrw:"↭",Hat:"^",hbar:"ℏ",Hcirc:"Ĥ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",hfr:"𝔥",Hfr:"ℌ",HilbertSpace:"ℋ",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",hopf:"𝕙",Hopf:"ℍ",horbar:"―",HorizontalLine:"─",hscr:"𝒽",Hscr:"ℋ",hslash:"ℏ",Hstrok:"Ħ",hstrok:"ħ",HumpDownHump:"≎",HumpEqual:"≏",hybull:"⁃",hyphen:"‐",Iacute:"Í",iacute:"í",ic:"⁣",Icirc:"Î",icirc:"î",Icy:"И",icy:"и",Idot:"İ",IEcy:"Е",iecy:"е",iexcl:"¡",iff:"⇔",ifr:"𝔦",Ifr:"ℑ",Igrave:"Ì",igrave:"ì",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",IJlig:"Ĳ",ijlig:"ĳ",Imacr:"Ī",imacr:"ī",image:"ℑ",ImaginaryI:"ⅈ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",Im:"ℑ",imof:"⊷",imped:"Ƶ",Implies:"⇒",incare:"℅",in:"∈",infin:"∞",infintie:"⧝",inodot:"ı",intcal:"⊺",int:"∫",Int:"∬",integers:"ℤ",Integral:"∫",intercal:"⊺",Intersection:"⋂",intlarhk:"⨗",intprod:"⨼",InvisibleComma:"⁣",InvisibleTimes:"⁢",IOcy:"Ё",iocy:"ё",Iogon:"Į",iogon:"į",Iopf:"𝕀",iopf:"𝕚",Iota:"Ι",iota:"ι",iprod:"⨼",iquest:"¿",iscr:"𝒾",Iscr:"ℐ",isin:"∈",isindot:"⋵",isinE:"⋹",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",Itilde:"Ĩ",itilde:"ĩ",Iukcy:"І",iukcy:"і",Iuml:"Ï",iuml:"ï",Jcirc:"Ĵ",jcirc:"ĵ",Jcy:"Й",jcy:"й",Jfr:"𝔍",jfr:"𝔧",jmath:"ȷ",Jopf:"𝕁",jopf:"𝕛",Jscr:"𝒥",jscr:"𝒿",Jsercy:"Ј",jsercy:"ј",Jukcy:"Є",jukcy:"є",Kappa:"Κ",kappa:"κ",kappav:"ϰ",Kcedil:"Ķ",kcedil:"ķ",Kcy:"К",kcy:"к",Kfr:"𝔎",kfr:"𝔨",kgreen:"ĸ",KHcy:"Х",khcy:"х",KJcy:"Ќ",kjcy:"ќ",Kopf:"𝕂",kopf:"𝕜",Kscr:"𝒦",kscr:"𝓀",lAarr:"⇚",Lacute:"Ĺ",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",Lambda:"Λ",lambda:"λ",lang:"⟨",Lang:"⟪",langd:"⦑",langle:"⟨",lap:"⪅",Laplacetrf:"ℒ",laquo:"«",larrb:"⇤",larrbfs:"⤟",larr:"←",Larr:"↞",lArr:"⇐",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",latail:"⤙",lAtail:"⤛",lat:"⪫",late:"⪭",lates:"⪭︀",lbarr:"⤌",lBarr:"⤎",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",Lcaron:"Ľ",lcaron:"ľ",Lcedil:"Ļ",lcedil:"ļ",lceil:"⌈",lcub:"{",Lcy:"Л",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",le:"≤",lE:"≦",LeftAngleBracket:"⟨",LeftArrowBar:"⇤",leftarrow:"←",LeftArrow:"←",Leftarrow:"⇐",LeftArrowRightArrow:"⇆",leftarrowtail:"↢",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVectorBar:"⥙",LeftDownVector:"⇃",LeftFloor:"⌊",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",leftrightarrow:"↔",LeftRightArrow:"↔",Leftrightarrow:"⇔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",LeftRightVector:"⥎",LeftTeeArrow:"↤",LeftTee:"⊣",LeftTeeVector:"⥚",leftthreetimes:"⋋",LeftTriangleBar:"⧏",LeftTriangle:"⊲",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVectorBar:"⥘",LeftUpVector:"↿",LeftVectorBar:"⥒",LeftVector:"↼",lEg:"⪋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",lescc:"⪨",les:"⩽",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",lessgtr:"≶",LessLess:"⪡",lesssim:"≲",LessSlantEqual:"⩽",LessTilde:"≲",lfisht:"⥼",lfloor:"⌊",Lfr:"𝔏",lfr:"𝔩",lg:"≶",lgE:"⪑",lHar:"⥢",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",LJcy:"Љ",ljcy:"љ",llarr:"⇇",ll:"≪",Ll:"⋘",llcorner:"⌞",Lleftarrow:"⇚",llhard:"⥫",lltri:"◺",Lmidot:"Ŀ",lmidot:"ŀ",lmoustache:"⎰",lmoust:"⎰",lnap:"⪉",lnapprox:"⪉",lne:"⪇",lnE:"≨",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",longleftarrow:"⟵",LongLeftArrow:"⟵",Longleftarrow:"⟸",longleftrightarrow:"⟷",LongLeftRightArrow:"⟷",Longleftrightarrow:"⟺",longmapsto:"⟼",longrightarrow:"⟶",LongRightArrow:"⟶",Longrightarrow:"⟹",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",Lopf:"𝕃",lopf:"𝕝",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",LowerLeftArrow:"↙",LowerRightArrow:"↘",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",lscr:"𝓁",Lscr:"ℒ",lsh:"↰",Lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",Lstrok:"Ł",lstrok:"ł",ltcc:"⪦",ltcir:"⩹",lt:"<",LT:"<",Lt:"≪",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltri:"◃",ltrie:"⊴",ltrif:"◂",ltrPar:"⦖",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",macr:"¯",male:"♂",malt:"✠",maltese:"✠",Map:"⤅",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",Mcy:"М",mcy:"м",mdash:"—",mDDot:"∺",measuredangle:"∡",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"𝔐",mfr:"𝔪",mho:"℧",micro:"µ",midast:"*",midcir:"⫰",mid:"∣",middot:"·",minusb:"⊟",minus:"−",minusd:"∸",minusdu:"⨪",MinusPlus:"∓",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",Mopf:"𝕄",mopf:"𝕞",mp:"∓",mscr:"𝓂",Mscr:"ℳ",mstpos:"∾",Mu:"Μ",mu:"μ",multimap:"⊸",mumap:"⊸",nabla:"∇",Nacute:"Ń",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natural:"♮",naturals:"ℕ",natur:"♮",nbsp:" ",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",Ncaron:"Ň",ncaron:"ň",Ncedil:"Ņ",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",Ncy:"Н",ncy:"н",ndash:"–",nearhk:"⤤",nearr:"↗",neArr:"⇗",nearrow:"↗",ne:"≠",nedot:"≐̸",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",nequiv:"≢",nesear:"⤨",nesim:"≂̸",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:`
`,nexist:"∄",nexists:"∄",Nfr:"𝔑",nfr:"𝔫",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",nGg:"⋙̸",ngsim:"≵",nGt:"≫⃒",ngt:"≯",ngtr:"≯",nGtv:"≫̸",nharr:"↮",nhArr:"⇎",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",NJcy:"Њ",njcy:"њ",nlarr:"↚",nlArr:"⇍",nldr:"‥",nlE:"≦̸",nle:"≰",nleftarrow:"↚",nLeftarrow:"⇍",nleftrightarrow:"↮",nLeftrightarrow:"⇎",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nLl:"⋘̸",nlsim:"≴",nLt:"≪⃒",nlt:"≮",nltri:"⋪",nltrie:"⋬",nLtv:"≪̸",nmid:"∤",NoBreak:"⁠",NonBreakingSpace:" ",nopf:"𝕟",Nopf:"ℕ",Not:"⫬",not:"¬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",notin:"∉",notindot:"⋵̸",notinE:"⋹̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",NotLeftTriangleBar:"⧏̸",NotLeftTriangle:"⋪",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangleBar:"⧐̸",NotRightTriangle:"⋫",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",nparallel:"∦",npar:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",nprec:"⊀",npreceq:"⪯̸",npre:"⪯̸",nrarrc:"⤳̸",nrarr:"↛",nrArr:"⇏",nrarrw:"↝̸",nrightarrow:"↛",nRightarrow:"⇏",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",Nscr:"𝒩",nscr:"𝓃",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",Ntilde:"Ñ",ntilde:"ñ",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",Nu:"Ν",nu:"ν",num:"#",numero:"№",numsp:" ",nvap:"≍⃒",nvdash:"⊬",nvDash:"⊭",nVdash:"⊮",nVDash:"⊯",nvge:"≥⃒",nvgt:">⃒",nvHarr:"⤄",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwarhk:"⤣",nwarr:"↖",nwArr:"⇖",nwarrow:"↖",nwnear:"⤧",Oacute:"Ó",oacute:"ó",oast:"⊛",Ocirc:"Ô",ocirc:"ô",ocir:"⊚",Ocy:"О",ocy:"о",odash:"⊝",Odblac:"Ő",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",OElig:"Œ",oelig:"œ",ofcir:"⦿",Ofr:"𝔒",ofr:"𝔬",ogon:"˛",Ograve:"Ò",ograve:"ò",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",Omacr:"Ō",omacr:"ō",Omega:"Ω",omega:"ω",Omicron:"Ο",omicron:"ο",omid:"⦶",ominus:"⊖",Oopf:"𝕆",oopf:"𝕠",opar:"⦷",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",operp:"⦹",oplus:"⊕",orarr:"↻",Or:"⩔",or:"∨",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"ª",ordm:"º",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oS:"Ⓢ",Oscr:"𝒪",oscr:"ℴ",Oslash:"Ø",oslash:"ø",osol:"⊘",Otilde:"Õ",otilde:"õ",otimesas:"⨶",Otimes:"⨷",otimes:"⊗",Ouml:"Ö",ouml:"ö",ovbar:"⌽",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",para:"¶",parallel:"∥",par:"∥",parsim:"⫳",parsl:"⫽",part:"∂",PartialD:"∂",Pcy:"П",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",Pfr:"𝔓",pfr:"𝔭",Phi:"Φ",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",Pi:"Π",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plus:"+",plusdo:"∔",plusdu:"⨥",pluse:"⩲",PlusMinus:"±",plusmn:"±",plussim:"⨦",plustwo:"⨧",pm:"±",Poincareplane:"ℌ",pointint:"⨕",popf:"𝕡",Popf:"ℙ",pound:"£",prap:"⪷",Pr:"⪻",pr:"≺",prcue:"≼",precapprox:"⪷",prec:"≺",preccurlyeq:"≼",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",pre:"⪯",prE:"⪳",precsim:"≾",prime:"′",Prime:"″",primes:"ℙ",prnap:"⪹",prnE:"⪵",prnsim:"⋨",prod:"∏",Product:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",Proportional:"∝",Proportion:"∷",propto:"∝",prsim:"≾",prurel:"⊰",Pscr:"𝒫",pscr:"𝓅",Psi:"Ψ",psi:"ψ",puncsp:" ",Qfr:"𝔔",qfr:"𝔮",qint:"⨌",qopf:"𝕢",Qopf:"ℚ",qprime:"⁗",Qscr:"𝒬",qscr:"𝓆",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",quot:'"',QUOT:'"',rAarr:"⇛",race:"∽̱",Racute:"Ŕ",racute:"ŕ",radic:"√",raemptyv:"⦳",rang:"⟩",Rang:"⟫",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"»",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarr:"→",Rarr:"↠",rArr:"⇒",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",Rarrtl:"⤖",rarrtl:"↣",rarrw:"↝",ratail:"⤚",rAtail:"⤜",ratio:"∶",rationals:"ℚ",rbarr:"⤍",rBarr:"⤏",RBarr:"⤐",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",Rcaron:"Ř",rcaron:"ř",Rcedil:"Ŗ",rcedil:"ŗ",rceil:"⌉",rcub:"}",Rcy:"Р",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",Re:"ℜ",rect:"▭",reg:"®",REG:"®",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",rfisht:"⥽",rfloor:"⌋",rfr:"𝔯",Rfr:"ℜ",rHar:"⥤",rhard:"⇁",rharu:"⇀",rharul:"⥬",Rho:"Ρ",rho:"ρ",rhov:"ϱ",RightAngleBracket:"⟩",RightArrowBar:"⇥",rightarrow:"→",RightArrow:"→",Rightarrow:"⇒",RightArrowLeftArrow:"⇄",rightarrowtail:"↣",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVectorBar:"⥕",RightDownVector:"⇂",RightFloor:"⌋",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",RightTeeArrow:"↦",RightTee:"⊢",RightTeeVector:"⥛",rightthreetimes:"⋌",RightTriangleBar:"⧐",RightTriangle:"⊳",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVectorBar:"⥔",RightUpVector:"↾",RightVectorBar:"⥓",RightVector:"⇀",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoustache:"⎱",rmoust:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",ropf:"𝕣",Ropf:"ℝ",roplus:"⨮",rotimes:"⨵",RoundImplies:"⥰",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",Rrightarrow:"⇛",rsaquo:"›",rscr:"𝓇",Rscr:"ℛ",rsh:"↱",Rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",RuleDelayed:"⧴",ruluhar:"⥨",rx:"℞",Sacute:"Ś",sacute:"ś",sbquo:"‚",scap:"⪸",Scaron:"Š",scaron:"š",Sc:"⪼",sc:"≻",sccue:"≽",sce:"⪰",scE:"⪴",Scedil:"Ş",scedil:"ş",Scirc:"Ŝ",scirc:"ŝ",scnap:"⪺",scnE:"⪶",scnsim:"⋩",scpolint:"⨓",scsim:"≿",Scy:"С",scy:"с",sdotb:"⊡",sdot:"⋅",sdote:"⩦",searhk:"⤥",searr:"↘",seArr:"⇘",searrow:"↘",sect:"§",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",Sfr:"𝔖",sfr:"𝔰",sfrown:"⌢",sharp:"♯",SHCHcy:"Щ",shchcy:"щ",SHcy:"Ш",shcy:"ш",ShortDownArrow:"↓",ShortLeftArrow:"←",shortmid:"∣",shortparallel:"∥",ShortRightArrow:"→",ShortUpArrow:"↑",shy:"­",Sigma:"Σ",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",SmallCircle:"∘",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",SOFTcy:"Ь",softcy:"ь",solbar:"⌿",solb:"⧄",sol:"/",Sopf:"𝕊",sopf:"𝕤",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",Sqrt:"√",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",square:"□",Square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",squarf:"▪",squ:"□",squf:"▪",srarr:"→",Sscr:"𝒮",sscr:"𝓈",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",Star:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"¯",sub:"⊂",Sub:"⋐",subdot:"⪽",subE:"⫅",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",subset:"⊂",Subset:"⋐",subseteq:"⊆",subseteqq:"⫅",SubsetEqual:"⊆",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succapprox:"⪸",succ:"≻",succcurlyeq:"≽",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",SuchThat:"∋",sum:"∑",Sum:"∑",sung:"♪",sup1:"¹",sup2:"²",sup3:"³",sup:"⊃",Sup:"⋑",supdot:"⪾",supdsub:"⫘",supE:"⫆",supe:"⊇",supedot:"⫄",Superset:"⊃",SupersetEqual:"⊇",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",supset:"⊃",Supset:"⋑",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swarhk:"⤦",swarr:"↙",swArr:"⇙",swarrow:"↙",swnwar:"⤪",szlig:"ß",Tab:"	",target:"⌖",Tau:"Τ",tau:"τ",tbrk:"⎴",Tcaron:"Ť",tcaron:"ť",Tcedil:"Ţ",tcedil:"ţ",Tcy:"Т",tcy:"т",tdot:"⃛",telrec:"⌕",Tfr:"𝔗",tfr:"𝔱",there4:"∴",therefore:"∴",Therefore:"∴",Theta:"Θ",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",ThickSpace:"  ",ThinSpace:" ",thinsp:" ",thkap:"≈",thksim:"∼",THORN:"Þ",thorn:"þ",tilde:"˜",Tilde:"∼",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",timesbar:"⨱",timesb:"⊠",times:"×",timesd:"⨰",tint:"∭",toea:"⤨",topbot:"⌶",topcir:"⫱",top:"⊤",Topf:"𝕋",topf:"𝕥",topfork:"⫚",tosa:"⤩",tprime:"‴",trade:"™",TRADE:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",TripleDot:"⃛",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",Tscr:"𝒯",tscr:"𝓉",TScy:"Ц",tscy:"ц",TSHcy:"Ћ",tshcy:"ћ",Tstrok:"Ŧ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",Uacute:"Ú",uacute:"ú",uarr:"↑",Uarr:"↟",uArr:"⇑",Uarrocir:"⥉",Ubrcy:"Ў",ubrcy:"ў",Ubreve:"Ŭ",ubreve:"ŭ",Ucirc:"Û",ucirc:"û",Ucy:"У",ucy:"у",udarr:"⇅",Udblac:"Ű",udblac:"ű",udhar:"⥮",ufisht:"⥾",Ufr:"𝔘",ufr:"𝔲",Ugrave:"Ù",ugrave:"ù",uHar:"⥣",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",Umacr:"Ū",umacr:"ū",uml:"¨",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",uogon:"ų",Uopf:"𝕌",uopf:"𝕦",UpArrowBar:"⤒",uparrow:"↑",UpArrow:"↑",Uparrow:"⇑",UpArrowDownArrow:"⇅",updownarrow:"↕",UpDownArrow:"↕",Updownarrow:"⇕",UpEquilibrium:"⥮",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",UpperLeftArrow:"↖",UpperRightArrow:"↗",upsi:"υ",Upsi:"ϒ",upsih:"ϒ",Upsilon:"Υ",upsilon:"υ",UpTeeArrow:"↥",UpTee:"⊥",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",Uring:"Ů",uring:"ů",urtri:"◹",Uscr:"𝒰",uscr:"𝓊",utdot:"⋰",Utilde:"Ũ",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",Uuml:"Ü",uuml:"ü",uwangle:"⦧",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",varr:"↕",vArr:"⇕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",vBar:"⫨",Vbar:"⫫",vBarv:"⫩",Vcy:"В",vcy:"в",vdash:"⊢",vDash:"⊨",Vdash:"⊩",VDash:"⊫",Vdashl:"⫦",veebar:"⊻",vee:"∨",Vee:"⋁",veeeq:"≚",vellip:"⋮",verbar:"|",Verbar:"‖",vert:"|",Vert:"‖",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"𝔙",vfr:"𝔳",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",Vopf:"𝕍",vopf:"𝕧",vprop:"∝",vrtri:"⊳",Vscr:"𝒱",vscr:"𝓋",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",Vvdash:"⊪",vzigzag:"⦚",Wcirc:"Ŵ",wcirc:"ŵ",wedbar:"⩟",wedge:"∧",Wedge:"⋀",wedgeq:"≙",weierp:"℘",Wfr:"𝔚",wfr:"𝔴",Wopf:"𝕎",wopf:"𝕨",wp:"℘",wr:"≀",wreath:"≀",Wscr:"𝒲",wscr:"𝓌",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",Xfr:"𝔛",xfr:"𝔵",xharr:"⟷",xhArr:"⟺",Xi:"Ξ",xi:"ξ",xlarr:"⟵",xlArr:"⟸",xmap:"⟼",xnis:"⋻",xodot:"⨀",Xopf:"𝕏",xopf:"𝕩",xoplus:"⨁",xotime:"⨂",xrarr:"⟶",xrArr:"⟹",Xscr:"𝒳",xscr:"𝓍",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",Yacute:"Ý",yacute:"ý",YAcy:"Я",yacy:"я",Ycirc:"Ŷ",ycirc:"ŷ",Ycy:"Ы",ycy:"ы",yen:"¥",Yfr:"𝔜",yfr:"𝔶",YIcy:"Ї",yicy:"ї",Yopf:"𝕐",yopf:"𝕪",Yscr:"𝒴",yscr:"𝓎",YUcy:"Ю",yucy:"ю",yuml:"ÿ",Yuml:"Ÿ",Zacute:"Ź",zacute:"ź",Zcaron:"Ž",zcaron:"ž",Zcy:"З",zcy:"з",Zdot:"Ż",zdot:"ż",zeetrf:"ℨ",ZeroWidthSpace:"​",Zeta:"Ζ",zeta:"ζ",zfr:"𝔷",Zfr:"ℨ",ZHcy:"Ж",zhcy:"ж",zigrarr:"⇝",zopf:"𝕫",Zopf:"ℤ",Zscr:"𝒵",zscr:"𝓏",zwj:"‍",zwnj:"‌"}}),ha=q((t,e)=>{e.exports={Aacute:"Á",aacute:"á",Acirc:"Â",acirc:"â",acute:"´",AElig:"Æ",aelig:"æ",Agrave:"À",agrave:"à",amp:"&",AMP:"&",Aring:"Å",aring:"å",Atilde:"Ã",atilde:"ã",Auml:"Ä",auml:"ä",brvbar:"¦",Ccedil:"Ç",ccedil:"ç",cedil:"¸",cent:"¢",copy:"©",COPY:"©",curren:"¤",deg:"°",divide:"÷",Eacute:"É",eacute:"é",Ecirc:"Ê",ecirc:"ê",Egrave:"È",egrave:"è",ETH:"Ð",eth:"ð",Euml:"Ë",euml:"ë",frac12:"½",frac14:"¼",frac34:"¾",gt:">",GT:">",Iacute:"Í",iacute:"í",Icirc:"Î",icirc:"î",iexcl:"¡",Igrave:"Ì",igrave:"ì",iquest:"¿",Iuml:"Ï",iuml:"ï",laquo:"«",lt:"<",LT:"<",macr:"¯",micro:"µ",middot:"·",nbsp:" ",not:"¬",Ntilde:"Ñ",ntilde:"ñ",Oacute:"Ó",oacute:"ó",Ocirc:"Ô",ocirc:"ô",Ograve:"Ò",ograve:"ò",ordf:"ª",ordm:"º",Oslash:"Ø",oslash:"ø",Otilde:"Õ",otilde:"õ",Ouml:"Ö",ouml:"ö",para:"¶",plusmn:"±",pound:"£",quot:'"',QUOT:'"',raquo:"»",reg:"®",REG:"®",sect:"§",shy:"­",sup1:"¹",sup2:"²",sup3:"³",szlig:"ß",THORN:"Þ",thorn:"þ",times:"×",Uacute:"Ú",uacute:"ú",Ucirc:"Û",ucirc:"û",Ugrave:"Ù",ugrave:"ù",uml:"¨",Uuml:"Ü",uuml:"ü",Yacute:"Ý",yacute:"ý",yen:"¥",yuml:"ÿ"}}),Xn=q((t,e)=>{e.exports={amp:"&",apos:"'",gt:">",lt:"<",quot:'"'}}),ga=q((t,e)=>{e.exports={0:65533,128:8364,130:8218,131:402,132:8222,133:8230,134:8224,135:8225,136:710,137:8240,138:352,139:8249,140:338,142:381,145:8216,146:8217,147:8220,148:8221,149:8226,150:8211,151:8212,152:732,153:8482,154:353,155:8250,156:339,158:382,159:376}}),ba=q(t=>{var e=t&&t.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(t,"__esModule",{value:!0});var r=e(ga()),o=String.fromCodePoint||function(i){var u="";return i>65535&&(i-=65536,u+=String.fromCharCode(i>>>10&1023|55296),i=56320|i&1023),u+=String.fromCharCode(i),u};function a(i){return i>=55296&&i<=57343||i>1114111?"�":(i in r.default&&(i=r.default[i]),o(i))}n(a,"decodeCodePoint"),t.default=a}),Qn=q(t=>{var e=t&&t.__importDefault||function(m){return m&&m.__esModule?m:{default:m}};Object.defineProperty(t,"__esModule",{value:!0}),t.decodeHTML=t.decodeHTMLStrict=t.decodeXML=void 0;var r=e(Kn()),o=e(ha()),a=e(Xn()),i=e(ba()),u=/&(?:[a-zA-Z0-9]+|#[xX][\da-fA-F]+|#\d+);/g;t.decodeXML=l(a.default),t.decodeHTMLStrict=l(r.default);function l(m){var h=d(m);return function(g){return String(g).replace(u,h)}}n(l,"getStrictDecoder");var c=n(function(m,h){return m<h?1:-1},"sorter");t.decodeHTML=function(){for(var m=Object.keys(o.default).sort(c),h=Object.keys(r.default).sort(c),g=0,re=0;g<h.length;g++)m[re]===h[g]?(h[g]+=";?",re++):h[g]+=";";var ne=new RegExp("&(?:"+h.join("|")+"|#[xX][\\da-fA-F]+;?|#\\d+;?)","g"),le=d(r.default);function J(ce){return ce.substr(-1)!==";"&&(ce+=";"),le(ce)}return n(J,"replacer"),function(ce){return String(ce).replace(ne,J)}}();function d(m){return n(function(h){if(h.charAt(1)==="#"){var g=h.charAt(2);return g==="X"||g==="x"?i.default(parseInt(h.substr(3),16)):i.default(parseInt(h.substr(2),10))}return m[h.slice(1,-1)]||h},"replace")}n(d,"getReplacer")}),es=q(t=>{var e=t&&t.__importDefault||function(F){return F&&F.__esModule?F:{default:F}};Object.defineProperty(t,"__esModule",{value:!0}),t.escapeUTF8=t.escape=t.encodeNonAsciiHTML=t.encodeHTML=t.encodeXML=void 0;var r=e(Xn()),o=c(r.default),a=d(o);t.encodeXML=ce(o);var i=e(Kn()),u=c(i.default),l=d(u);t.encodeHTML=re(u,l),t.encodeNonAsciiHTML=ce(u);function c(F){return Object.keys(F).sort().reduce(function(se,he){return se[F[he]]="&"+he+";",se},{})}n(c,"getInverseObj");function d(F){for(var se=[],he=[],Ve=0,ve=Object.keys(F);Ve<ve.length;Ve++){var we=ve[Ve];we.length===1?se.push("\\"+we):he.push(we)}se.sort();for(var Nt=0;Nt<se.length-1;Nt++){for(var Bt=Nt;Bt<se.length-1&&se[Bt].charCodeAt(1)+1===se[Bt+1].charCodeAt(1);)Bt+=1;var Ft=1+Bt-Nt;Ft<3||se.splice(Nt,Ft,se[Nt]+"-"+se[Bt])}return he.unshift("["+se.join("")+"]"),new RegExp(he.join("|"),"g")}n(d,"getInverseReplacer");var m=/(?:[\x80-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])/g,h=String.prototype.codePointAt!=null?function(F){return F.codePointAt(0)}:function(F){return(F.charCodeAt(0)-55296)*1024+F.charCodeAt(1)-56320+65536};function g(F){return"&#x"+(F.length>1?h(F):F.charCodeAt(0)).toString(16).toUpperCase()+";"}n(g,"singleCharReplacer");function re(F,se){return function(he){return he.replace(se,function(Ve){return F[Ve]}).replace(m,g)}}n(re,"getInverse");var ne=new RegExp(a.source+"|"+m.source,"g");function le(F){return F.replace(ne,g)}n(le,"escape"),t.escape=le;function J(F){return F.replace(a,g)}n(J,"escapeUTF8"),t.escapeUTF8=J;function ce(F){return function(se){return se.replace(ne,function(he){return F[he]||g(he)})}}n(ce,"getASCIIEncoder")}),Da=q(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.decodeXMLStrict=t.decodeHTML5Strict=t.decodeHTML4Strict=t.decodeHTML5=t.decodeHTML4=t.decodeHTMLStrict=t.decodeHTML=t.decodeXML=t.encodeHTML5=t.encodeHTML4=t.escapeUTF8=t.escape=t.encodeNonAsciiHTML=t.encodeHTML=t.encodeXML=t.encode=t.decodeStrict=t.decode=void 0;var e=Qn(),r=es();function o(c,d){return(!d||d<=0?e.decodeXML:e.decodeHTML)(c)}n(o,"decode"),t.decode=o;function a(c,d){return(!d||d<=0?e.decodeXML:e.decodeHTMLStrict)(c)}n(a,"decodeStrict"),t.decodeStrict=a;function i(c,d){return(!d||d<=0?r.encodeXML:r.encodeHTML)(c)}n(i,"encode"),t.encode=i;var u=es();Object.defineProperty(t,"encodeXML",{enumerable:!0,get:n(function(){return u.encodeXML},"get")}),Object.defineProperty(t,"encodeHTML",{enumerable:!0,get:n(function(){return u.encodeHTML},"get")}),Object.defineProperty(t,"encodeNonAsciiHTML",{enumerable:!0,get:n(function(){return u.encodeNonAsciiHTML},"get")}),Object.defineProperty(t,"escape",{enumerable:!0,get:n(function(){return u.escape},"get")}),Object.defineProperty(t,"escapeUTF8",{enumerable:!0,get:n(function(){return u.escapeUTF8},"get")}),Object.defineProperty(t,"encodeHTML4",{enumerable:!0,get:n(function(){return u.encodeHTML},"get")}),Object.defineProperty(t,"encodeHTML5",{enumerable:!0,get:n(function(){return u.encodeHTML},"get")});var l=Qn();Object.defineProperty(t,"decodeXML",{enumerable:!0,get:n(function(){return l.decodeXML},"get")}),Object.defineProperty(t,"decodeHTML",{enumerable:!0,get:n(function(){return l.decodeHTML},"get")}),Object.defineProperty(t,"decodeHTMLStrict",{enumerable:!0,get:n(function(){return l.decodeHTMLStrict},"get")}),Object.defineProperty(t,"decodeHTML4",{enumerable:!0,get:n(function(){return l.decodeHTML},"get")}),Object.defineProperty(t,"decodeHTML5",{enumerable:!0,get:n(function(){return l.decodeHTML},"get")}),Object.defineProperty(t,"decodeHTML4Strict",{enumerable:!0,get:n(function(){return l.decodeHTMLStrict},"get")}),Object.defineProperty(t,"decodeHTML5Strict",{enumerable:!0,get:n(function(){return l.decodeHTMLStrict},"get")}),Object.defineProperty(t,"decodeXMLStrict",{enumerable:!0,get:n(function(){return l.decodeXML},"get")})}),Ha=q((t,e)=>{function r(p,A){if(!(p instanceof A))throw new TypeError("Cannot call a class as a function")}n(r,"_classCallCheck");function o(p,A){for(var B=0;B<A.length;B++){var de=A[B];de.enumerable=de.enumerable||!1,de.configurable=!0,"value"in de&&(de.writable=!0),Object.defineProperty(p,de.key,de)}}n(o,"_defineProperties");function a(p,A,B){return A&&o(p.prototype,A),B&&o(p,B),p}n(a,"_createClass");function i(p,A){var B=typeof Symbol<"u"&&p[Symbol.iterator]||p["@@iterator"];if(!B){if(Array.isArray(p)||(B=u(p))||A&&p&&typeof p.length=="number"){B&&(p=B);var de=0,pe=n(function(){},"F");return{s:pe,n:n(function(){return de>=p.length?{done:!0}:{done:!1,value:p[de++]}},"n"),e:n(function(Mt){throw Mt},"e"),f:pe}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var Lt=!0,qt=!1,Ut;return{s:n(function(){B=B.call(p)},"s"),n:n(function(){var Mt=B.next();return Lt=Mt.done,Mt},"n"),e:n(function(Mt){qt=!0,Ut=Mt},"e"),f:n(function(){try{!Lt&&B.return!=null&&B.return()}finally{if(qt)throw Ut}},"f")}}n(i,"_createForOfIteratorHelper");function u(p,A){if(p){if(typeof p=="string")return l(p,A);var B=Object.prototype.toString.call(p).slice(8,-1);if(B==="Object"&&p.constructor&&(B=p.constructor.name),B==="Map"||B==="Set")return Array.from(p);if(B==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(B))return l(p,A)}}n(u,"_unsupportedIterableToArray");function l(p,A){(A==null||A>p.length)&&(A=p.length);for(var B=0,de=new Array(A);B<A;B++)de[B]=p[B];return de}n(l,"_arrayLikeToArray");var c=Da(),d={fg:"#FFF",bg:"#000",newline:!1,escapeXML:!1,stream:!1,colors:m()};function m(){var p={0:"#000",1:"#A00",2:"#0A0",3:"#A50",4:"#00A",5:"#A0A",6:"#0AA",7:"#AAA",8:"#555",9:"#F55",10:"#5F5",11:"#FF5",12:"#55F",13:"#F5F",14:"#5FF",15:"#FFF"};return F(0,5).forEach(function(A){F(0,5).forEach(function(B){F(0,5).forEach(function(de){return h(A,B,de,p)})})}),F(0,23).forEach(function(A){var B=A+232,de=g(A*10+8);p[B]="#"+de+de+de}),p}n(m,"getDefaultColors");function h(p,A,B,de){var pe=16+p*36+A*6+B,Lt=p>0?p*40+55:0,qt=A>0?A*40+55:0,Ut=B>0?B*40+55:0;de[pe]=re([Lt,qt,Ut])}n(h,"setStyleColor");function g(p){for(var A=p.toString(16);A.length<2;)A="0"+A;return A}n(g,"toHexString");function re(p){var A=[],B=i(p),de;try{for(B.s();!(de=B.n()).done;){var pe=de.value;A.push(g(pe))}}catch(Lt){B.e(Lt)}finally{B.f()}return"#"+A.join("")}n(re,"toColorHexString");function ne(p,A,B,de){var pe;return A==="text"?pe=Ve(B,de):A==="display"?pe=J(p,B,de):A==="xterm256Foreground"?pe=Nt(p,de.colors[B]):A==="xterm256Background"?pe=Bt(p,de.colors[B]):A==="rgb"&&(pe=le(p,B)),pe}n(ne,"generateOutput");function le(p,A){A=A.substring(2).slice(0,-1);var B=+A.substr(0,2),de=A.substring(5).split(";"),pe=de.map(function(Lt){return("0"+Number(Lt).toString(16)).substr(-2)}).join("");return we(p,(B===38?"color:#":"background-color:#")+pe)}n(le,"handleRgb");function J(p,A,B){A=parseInt(A,10);var de={"-1":n(function(){return"<br/>"},"_"),0:n(function(){return p.length&&ce(p)},"_"),1:n(function(){return ve(p,"b")},"_"),3:n(function(){return ve(p,"i")},"_"),4:n(function(){return ve(p,"u")},"_"),8:n(function(){return we(p,"display:none")},"_"),9:n(function(){return ve(p,"strike")},"_"),22:n(function(){return we(p,"font-weight:normal;text-decoration:none;font-style:normal")},"_"),23:n(function(){return Ft(p,"i")},"_"),24:n(function(){return Ft(p,"u")},"_"),39:n(function(){return Nt(p,B.fg)},"_"),49:n(function(){return Bt(p,B.bg)},"_"),53:n(function(){return we(p,"text-decoration:overline")},"_")},pe;return de[A]?pe=de[A]():4<A&&A<7?pe=ve(p,"blink"):29<A&&A<38?pe=Nt(p,B.colors[A-30]):39<A&&A<48?pe=Bt(p,B.colors[A-40]):89<A&&A<98?pe=Nt(p,B.colors[8+(A-90)]):99<A&&A<108&&(pe=Bt(p,B.colors[8+(A-100)])),pe}n(J,"handleDisplay");function ce(p){var A=p.slice(0);return p.length=0,A.reverse().map(function(B){return"</"+B+">"}).join("")}n(ce,"resetStyles");function F(p,A){for(var B=[],de=p;de<=A;de++)B.push(de);return B}n(F,"range");function se(p){return function(A){return(p===null||A.category!==p)&&p!=="all"}}n(se,"notCategory");function he(p){p=parseInt(p,10);var A=null;return p===0?A="all":p===1?A="bold":2<p&&p<5?A="underline":4<p&&p<7?A="blink":p===8?A="hide":p===9?A="strike":29<p&&p<38||p===39||89<p&&p<98?A="foreground-color":(39<p&&p<48||p===49||99<p&&p<108)&&(A="background-color"),A}n(he,"categoryForCode");function Ve(p,A){return A.escapeXML?c.encodeXML(p):p}n(Ve,"pushText");function ve(p,A,B){return B||(B=""),p.push(A),"<".concat(A).concat(B?' style="'.concat(B,'"'):"",">")}n(ve,"pushTag");function we(p,A){return ve(p,"span",A)}n(we,"pushStyle");function Nt(p,A){return ve(p,"span","color:"+A)}n(Nt,"pushForegroundColor");function Bt(p,A){return ve(p,"span","background-color:"+A)}n(Bt,"pushBackgroundColor");function Ft(p,A){var B;if(p.slice(-1)[0]===A&&(B=p.pop()),B)return"</"+A+">"}n(Ft,"closeTag");function jt(p,A,B){var de=!1,pe=3;function Lt(){return""}n(Lt,"remove");function qt(Kr,Xr){return B("xterm256Foreground",Xr),""}n(qt,"removeXterm256Foreground");function Ut(Kr,Xr){return B("xterm256Background",Xr),""}n(Ut,"removeXterm256Background");function Mt(Kr){return A.newline?B("display",-1):B("text",Kr),""}n(Mt,"newline");function Gt(Kr,Xr){de=!0,Xr.trim().length===0&&(Xr="0"),Xr=Xr.trimRight(";").split(";");var Bn=i(Xr),zn;try{for(Bn.s();!(zn=Bn.n()).done;){var is=zn.value;B("display",is)}}catch(ys){Bn.e(ys)}finally{Bn.f()}return""}n(Gt,"ansiMess");function lr(Kr){return B("text",Kr),""}n(lr,"realText");function zr(Kr){return B("rgb",Kr),""}n(zr,"rgb");var Qr=[{pattern:/^\x08+/,sub:Lt},{pattern:/^\x1b\[[012]?K/,sub:Lt},{pattern:/^\x1b\[\(B/,sub:Lt},{pattern:/^\x1b\[[34]8;2;\d+;\d+;\d+m/,sub:zr},{pattern:/^\x1b\[38;5;(\d+)m/,sub:qt},{pattern:/^\x1b\[48;5;(\d+)m/,sub:Ut},{pattern:/^\n/,sub:Mt},{pattern:/^\r+\n/,sub:Mt},{pattern:/^\r/,sub:Mt},{pattern:/^\x1b\[((?:\d{1,3};?)+|)m/,sub:Gt},{pattern:/^\x1b\[\d?J/,sub:Lt},{pattern:/^\x1b\[\d{0,3};\d{0,3}f/,sub:Lt},{pattern:/^\x1b\[?[\d;]{0,3}/,sub:Lt},{pattern:/^(([^\x1b\x08\r\n])+)/,sub:lr}];function Vt(Kr,Xr){Xr>pe&&de||(de=!1,p=p.replace(Kr.pattern,Kr.sub))}n(Vt,"process");var Jr=[],Zr=p,Tn=Zr.length;e:for(;Tn>0;){for(var Gn=0,Hn=0,So=Qr.length;Hn<So;Gn=++Hn){var as=Qr[Gn];if(Vt(as,Gn),p.length!==Tn){Tn=p.length;continue e}}if(p.length===Tn)break;Jr.push(0),Tn=p.length}return Jr}n(jt,"tokenize");function qe(p,A,B){return A!=="text"&&(p=p.filter(se(he(B))),p.push({token:A,data:B,category:he(B)})),p}n(qe,"updateStickyStack");var Dt=function(){function p(A){r(this,p),A=A||{},A.colors&&(A.colors=Object.assign({},d.colors,A.colors)),this.options=Object.assign({},d,A),this.stack=[],this.stickyStack=[]}return n(p,"Filter"),a(p,[{key:"toHtml",value:n(function(A){var B=this;A=typeof A=="string"?[A]:A;var de=this.stack,pe=this.options,Lt=[];return this.stickyStack.forEach(function(qt){var Ut=ne(de,qt.token,qt.data,pe);Ut&&Lt.push(Ut)}),jt(A.join(""),pe,function(qt,Ut){var Mt=ne(de,qt,Ut,pe);Mt&&Lt.push(Mt),pe.stream&&(B.stickyStack=qe(B.stickyStack,qt,Ut))}),de.length&&Lt.push(ce(de)),Lt.join("")},"toHtml")}]),p}();e.exports=Dt}),Za=q((t,e)=>{(function(r,o){typeof t=="object"&&typeof e<"u"?e.exports=o():typeof define=="function"&&define.amd?define(o):(r=typeof globalThis<"u"?globalThis:r||self).BrowserDetector=o()})(t,function(){function r(c,d){for(var m=0;m<d.length;m++){var h=d[m];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(c,(g=h.key,re=void 0,typeof(re=function(ne,le){if(typeof ne!="object"||ne===null)return ne;var J=ne[Symbol.toPrimitive];if(J!==void 0){var ce=J.call(ne,le);if(typeof ce!="object")return ce;throw new TypeError("@@toPrimitive must return a primitive value.")}return(le==="string"?String:Number)(ne)}(g,"string"))=="symbol"?re:String(re)),h)}var g,re}n(r,"e");var o={chrome:"Google Chrome",brave:"Brave",crios:"Google Chrome",edge:"Microsoft Edge",edg:"Microsoft Edge",edgios:"Microsoft Edge",fennec:"Mozilla Firefox",jsdom:"JsDOM",mozilla:"Mozilla Firefox",fxios:"Mozilla Firefox",msie:"Microsoft Internet Explorer",opera:"Opera",opios:"Opera",opr:"Opera",opt:"Opera",rv:"Microsoft Internet Explorer",safari:"Safari",samsungbrowser:"Samsung Browser",electron:"Electron"},a={android:"Android",androidTablet:"Android Tablet",cros:"Chrome OS",fennec:"Android Tablet",ipad:"IPad",iphone:"IPhone",jsdom:"JsDOM",linux:"Linux",mac:"Macintosh",tablet:"Android Tablet",win:"Windows","windows phone":"Windows Phone",xbox:"Microsoft Xbox"},i=n(function(c){var d=new RegExp("^-?\\d+(?:.\\d{0,".concat(arguments.length>1&&arguments[1]!==void 0?arguments[1]:-1,"})?")),m=Number(c).toString().match(d);return m?m[0]:null},"n"),u=n(function(){return typeof window<"u"?window.navigator:null},"i"),l=function(){function c(g){var re;(function(ne,le){if(!(ne instanceof le))throw new TypeError("Cannot call a class as a function")})(this,c),this.userAgent=g||((re=u())===null||re===void 0?void 0:re.userAgent)||null}n(c,"t");var d,m,h;return d=c,m=[{key:"parseUserAgent",value:n(function(g){var re,ne,le,J={},ce=g||this.userAgent||"",F=ce.toLowerCase().replace(/\s\s+/g," "),se=/(edge)\/([\w.]+)/.exec(F)||/(edg)[/]([\w.]+)/.exec(F)||/(opr)[/]([\w.]+)/.exec(F)||/(opt)[/]([\w.]+)/.exec(F)||/(fxios)[/]([\w.]+)/.exec(F)||/(edgios)[/]([\w.]+)/.exec(F)||/(jsdom)[/]([\w.]+)/.exec(F)||/(samsungbrowser)[/]([\w.]+)/.exec(F)||/(electron)[/]([\w.]+)/.exec(F)||/(chrome)[/]([\w.]+)/.exec(F)||/(crios)[/]([\w.]+)/.exec(F)||/(opios)[/]([\w.]+)/.exec(F)||/(version)(applewebkit)[/]([\w.]+).*(safari)[/]([\w.]+)/.exec(F)||/(webkit)[/]([\w.]+).*(version)[/]([\w.]+).*(safari)[/]([\w.]+)/.exec(F)||/(applewebkit)[/]([\w.]+).*(safari)[/]([\w.]+)/.exec(F)||/(webkit)[/]([\w.]+)/.exec(F)||/(opera)(?:.*version|)[/]([\w.]+)/.exec(F)||/(msie) ([\w.]+)/.exec(F)||/(fennec)[/]([\w.]+)/.exec(F)||F.indexOf("trident")>=0&&/(rv)(?::| )([\w.]+)/.exec(F)||F.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(F)||[],he=/(ipad)/.exec(F)||/(ipod)/.exec(F)||/(iphone)/.exec(F)||/(jsdom)/.exec(F)||/(windows phone)/.exec(F)||/(xbox)/.exec(F)||/(win)/.exec(F)||/(tablet)/.exec(F)||/(android)/.test(F)&&/(mobile)/.test(F)===!1&&["androidTablet"]||/(android)/.exec(F)||/(mac)/.exec(F)||/(linux)/.exec(F)||/(cros)/.exec(F)||[],Ve=se[5]||se[3]||se[1]||null,ve=he[0]||null,we=se[4]||se[2]||null,Nt=u();Ve==="chrome"&&typeof(Nt==null||(re=Nt.brave)===null||re===void 0?void 0:re.isBrave)=="function"&&(Ve="brave"),Ve&&(J[Ve]=!0),ve&&(J[ve]=!0);var Bt=!!(J.tablet||J.android||J.androidTablet),Ft=!!(J.ipad||J.tablet||J.androidTablet),jt=!!(J.android||J.androidTablet||J.tablet||J.ipad||J.ipod||J.iphone||J["windows phone"]),qe=!!(J.cros||J.mac||J.linux||J.win),Dt=!!(J.brave||J.chrome||J.crios||J.opr||J.safari||J.edg||J.electron),p=!!(J.msie||J.rv);return{name:(ne=o[Ve])!==null&&ne!==void 0?ne:null,platform:(le=a[ve])!==null&&le!==void 0?le:null,userAgent:ce,version:we,shortVersion:we?i(parseFloat(we),2):null,isAndroid:Bt,isTablet:Ft,isMobile:jt,isDesktop:qe,isWebkit:Dt,isIE:p}},"value")},{key:"getBrowserInfo",value:n(function(){var g=this.parseUserAgent();return{name:g.name,platform:g.platform,userAgent:g.userAgent,version:g.version,shortVersion:g.shortVersion}},"value")}],h=[{key:"VERSION",get:n(function(){return"3.4.0"},"get")}],m&&r(d.prototype,m),h&&r(d,h),Object.defineProperty(d,"prototype",{writable:!1}),c}();return l})}),Ht={};_e(Ht,{global:()=>E$1});var E$1=(()=>{let t;return typeof window<"u"?t=window:typeof globalThis<"u"?t=globalThis:typeof global<"u"?t=global:typeof self<"u"?t=self:t={},t})(),ge={};_e(ge,{ARGTYPES_INFO_REQUEST:()=>fo,ARGTYPES_INFO_RESPONSE:()=>nt,CHANNEL_CREATED:()=>cl,CHANNEL_WS_DISCONNECT:()=>Wt,CONFIG_ERROR:()=>$t,CREATE_NEW_STORYFILE_REQUEST:()=>pl,CREATE_NEW_STORYFILE_RESPONSE:()=>dl,CURRENT_STORY_WAS_SET:()=>rt,DOCS_PREPARED:()=>Yt,DOCS_RENDERED:()=>pr,FILE_COMPONENT_SEARCH_REQUEST:()=>ul,FILE_COMPONENT_SEARCH_RESPONSE:()=>fl,FORCE_REMOUNT:()=>Kt,FORCE_RE_RENDER:()=>dr,GLOBALS_UPDATED:()=>Ce,NAVIGATE_URL:()=>yl,PLAY_FUNCTION_THREW_EXCEPTION:()=>Xt,PRELOAD_ENTRIES:()=>Qt,PREVIEW_BUILDER_PROGRESS:()=>ml,PREVIEW_KEYDOWN:()=>Zt,REGISTER_SUBSCRIPTION:()=>hl,REQUEST_WHATS_NEW_DATA:()=>wl,RESET_STORY_ARGS:()=>ur,RESULT_WHATS_NEW_DATA:()=>_l,SAVE_STORY_REQUEST:()=>Ol,SAVE_STORY_RESPONSE:()=>Il,SELECT_STORY:()=>gl,SET_CONFIG:()=>Sl,SET_CURRENT_STORY:()=>eo,SET_FILTER:()=>bl,SET_GLOBALS:()=>ro,SET_INDEX:()=>Tl,SET_STORIES:()=>El,SET_WHATS_NEW_CACHE:()=>Cl,SHARED_STATE_CHANGED:()=>Rl,SHARED_STATE_SET:()=>Al,STORIES_COLLAPSE_ALL:()=>xl,STORIES_EXPAND_ALL:()=>vl,STORY_ARGS_UPDATED:()=>to,STORY_CHANGED:()=>oo,STORY_ERRORED:()=>no,STORY_FINISHED:()=>ot,STORY_INDEX_INVALIDATED:()=>so,STORY_MISSING:()=>tt,STORY_PREPARED:()=>io,STORY_RENDERED:()=>We,STORY_RENDER_PHASE_CHANGED:()=>Pe,STORY_SPECIFIED:()=>ao,STORY_THREW_EXCEPTION:()=>lo,STORY_UNCHANGED:()=>co,TELEMETRY_ERROR:()=>uo,TESTING_MODULE_CANCEL_TEST_RUN_REQUEST:()=>Ll,TESTING_MODULE_CANCEL_TEST_RUN_RESPONSE:()=>jl,TESTING_MODULE_CRASH_REPORT:()=>Fl,TESTING_MODULE_PROGRESS_REPORT:()=>Dl,TESTING_MODULE_RUN_ALL_REQUEST:()=>kl,TESTING_MODULE_RUN_REQUEST:()=>Nl,TOGGLE_WHATS_NEW_NOTIFICATIONS:()=>Pl,UNHANDLED_ERRORS_WHILE_PLAYING:()=>Jt,UPDATE_GLOBALS:()=>fr,UPDATE_QUERY_PARAMS:()=>po,UPDATE_STORY_ARGS:()=>yr,default:()=>ll});var zt=(t=>(t.CHANNEL_WS_DISCONNECT="channelWSDisconnect",t.CHANNEL_CREATED="channelCreated",t.CONFIG_ERROR="configError",t.STORY_INDEX_INVALIDATED="storyIndexInvalidated",t.STORY_SPECIFIED="storySpecified",t.SET_CONFIG="setConfig",t.SET_STORIES="setStories",t.SET_INDEX="setIndex",t.SET_CURRENT_STORY="setCurrentStory",t.CURRENT_STORY_WAS_SET="currentStoryWasSet",t.FORCE_RE_RENDER="forceReRender",t.FORCE_REMOUNT="forceRemount",t.PRELOAD_ENTRIES="preloadStories",t.STORY_PREPARED="storyPrepared",t.DOCS_PREPARED="docsPrepared",t.STORY_CHANGED="storyChanged",t.STORY_UNCHANGED="storyUnchanged",t.STORY_RENDERED="storyRendered",t.STORY_FINISHED="storyFinished",t.STORY_MISSING="storyMissing",t.STORY_ERRORED="storyErrored",t.STORY_THREW_EXCEPTION="storyThrewException",t.STORY_RENDER_PHASE_CHANGED="storyRenderPhaseChanged",t.PLAY_FUNCTION_THREW_EXCEPTION="playFunctionThrewException",t.UNHANDLED_ERRORS_WHILE_PLAYING="unhandledErrorsWhilePlaying",t.UPDATE_STORY_ARGS="updateStoryArgs",t.STORY_ARGS_UPDATED="storyArgsUpdated",t.RESET_STORY_ARGS="resetStoryArgs",t.SET_FILTER="setFilter",t.SET_GLOBALS="setGlobals",t.UPDATE_GLOBALS="updateGlobals",t.GLOBALS_UPDATED="globalsUpdated",t.REGISTER_SUBSCRIPTION="registerSubscription",t.PREVIEW_KEYDOWN="previewKeydown",t.PREVIEW_BUILDER_PROGRESS="preview_builder_progress",t.SELECT_STORY="selectStory",t.STORIES_COLLAPSE_ALL="storiesCollapseAll",t.STORIES_EXPAND_ALL="storiesExpandAll",t.DOCS_RENDERED="docsRendered",t.SHARED_STATE_CHANGED="sharedStateChanged",t.SHARED_STATE_SET="sharedStateSet",t.NAVIGATE_URL="navigateUrl",t.UPDATE_QUERY_PARAMS="updateQueryParams",t.REQUEST_WHATS_NEW_DATA="requestWhatsNewData",t.RESULT_WHATS_NEW_DATA="resultWhatsNewData",t.SET_WHATS_NEW_CACHE="setWhatsNewCache",t.TOGGLE_WHATS_NEW_NOTIFICATIONS="toggleWhatsNewNotifications",t.TELEMETRY_ERROR="telemetryError",t.FILE_COMPONENT_SEARCH_REQUEST="fileComponentSearchRequest",t.FILE_COMPONENT_SEARCH_RESPONSE="fileComponentSearchResponse",t.SAVE_STORY_REQUEST="saveStoryRequest",t.SAVE_STORY_RESPONSE="saveStoryResponse",t.ARGTYPES_INFO_REQUEST="argtypesInfoRequest",t.ARGTYPES_INFO_RESPONSE="argtypesInfoResponse",t.CREATE_NEW_STORYFILE_REQUEST="createNewStoryfileRequest",t.CREATE_NEW_STORYFILE_RESPONSE="createNewStoryfileResponse",t.TESTING_MODULE_CRASH_REPORT="testingModuleCrashReport",t.TESTING_MODULE_PROGRESS_REPORT="testingModuleProgressReport",t.TESTING_MODULE_RUN_REQUEST="testingModuleRunRequest",t.TESTING_MODULE_RUN_ALL_REQUEST="testingModuleRunAllRequest",t.TESTING_MODULE_CANCEL_TEST_RUN_REQUEST="testingModuleCancelTestRunRequest",t.TESTING_MODULE_CANCEL_TEST_RUN_RESPONSE="testingModuleCancelTestRunResponse",t))(zt||{}),ll=zt,{CHANNEL_WS_DISCONNECT:Wt,CHANNEL_CREATED:cl,CONFIG_ERROR:$t,CREATE_NEW_STORYFILE_REQUEST:pl,CREATE_NEW_STORYFILE_RESPONSE:dl,CURRENT_STORY_WAS_SET:rt,DOCS_PREPARED:Yt,DOCS_RENDERED:pr,FILE_COMPONENT_SEARCH_REQUEST:ul,FILE_COMPONENT_SEARCH_RESPONSE:fl,FORCE_RE_RENDER:dr,FORCE_REMOUNT:Kt,GLOBALS_UPDATED:Ce,NAVIGATE_URL:yl,PLAY_FUNCTION_THREW_EXCEPTION:Xt,UNHANDLED_ERRORS_WHILE_PLAYING:Jt,PRELOAD_ENTRIES:Qt,PREVIEW_BUILDER_PROGRESS:ml,PREVIEW_KEYDOWN:Zt,REGISTER_SUBSCRIPTION:hl,RESET_STORY_ARGS:ur,SELECT_STORY:gl,SET_CONFIG:Sl,SET_CURRENT_STORY:eo,SET_FILTER:bl,SET_GLOBALS:ro,SET_INDEX:Tl,SET_STORIES:El,SHARED_STATE_CHANGED:Rl,SHARED_STATE_SET:Al,STORIES_COLLAPSE_ALL:xl,STORIES_EXPAND_ALL:vl,STORY_ARGS_UPDATED:to,STORY_CHANGED:oo,STORY_ERRORED:no,STORY_INDEX_INVALIDATED:so,STORY_MISSING:tt,STORY_PREPARED:io,STORY_RENDER_PHASE_CHANGED:Pe,STORY_RENDERED:We,STORY_FINISHED:ot,STORY_SPECIFIED:ao,STORY_THREW_EXCEPTION:lo,STORY_UNCHANGED:co,UPDATE_GLOBALS:fr,UPDATE_QUERY_PARAMS:po,UPDATE_STORY_ARGS:yr,REQUEST_WHATS_NEW_DATA:wl,RESULT_WHATS_NEW_DATA:_l,SET_WHATS_NEW_CACHE:Cl,TOGGLE_WHATS_NEW_NOTIFICATIONS:Pl,TELEMETRY_ERROR:uo,SAVE_STORY_REQUEST:Ol,SAVE_STORY_RESPONSE:Il,ARGTYPES_INFO_REQUEST:fo,ARGTYPES_INFO_RESPONSE:nt,TESTING_MODULE_CRASH_REPORT:Fl,TESTING_MODULE_PROGRESS_REPORT:Dl,TESTING_MODULE_RUN_REQUEST:Nl,TESTING_MODULE_RUN_ALL_REQUEST:kl,TESTING_MODULE_CANCEL_TEST_RUN_REQUEST:Ll,TESTING_MODULE_CANCEL_TEST_RUN_RESPONSE:jl}=zt,yo={"@storybook/global":"__STORYBOOK_MODULE_GLOBAL__","storybook/internal/channels":"__STORYBOOK_MODULE_CHANNELS__","@storybook/channels":"__STORYBOOK_MODULE_CHANNELS__","@storybook/core/channels":"__STORYBOOK_MODULE_CHANNELS__","storybook/internal/client-logger":"__STORYBOOK_MODULE_CLIENT_LOGGER__","@storybook/client-logger":"__STORYBOOK_MODULE_CLIENT_LOGGER__","@storybook/core/client-logger":"__STORYBOOK_MODULE_CLIENT_LOGGER__","storybook/internal/core-events":"__STORYBOOK_MODULE_CORE_EVENTS__","@storybook/core-events":"__STORYBOOK_MODULE_CORE_EVENTS__","@storybook/core/core-events":"__STORYBOOK_MODULE_CORE_EVENTS__","storybook/internal/preview-errors":"__STORYBOOK_MODULE_CORE_EVENTS_PREVIEW_ERRORS__","@storybook/core-events/preview-errors":"__STORYBOOK_MODULE_CORE_EVENTS_PREVIEW_ERRORS__","@storybook/core/preview-errors":"__STORYBOOK_MODULE_CORE_EVENTS_PREVIEW_ERRORS__","storybook/internal/preview-api":"__STORYBOOK_MODULE_PREVIEW_API__","@storybook/preview-api":"__STORYBOOK_MODULE_PREVIEW_API__","@storybook/core/preview-api":"__STORYBOOK_MODULE_PREVIEW_API__","storybook/internal/types":"__STORYBOOK_MODULE_TYPES__","@storybook/types":"__STORYBOOK_MODULE_TYPES__","@storybook/core/types":"__STORYBOOK_MODULE_TYPES__"},cs=Object.keys(yo),br={};_e(br,{Channel:()=>ie,HEARTBEAT_INTERVAL:()=>Po,HEARTBEAT_MAX_LATENCY:()=>Oo,PostMessageTransport:()=>Qe,WebsocketTransport:()=>Ze,createBrowserChannel:()=>kd,default:()=>Nd});function _$1(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];var o=Array.from(typeof t=="string"?[t]:t);o[o.length-1]=o[o.length-1].replace(/\r?\n([\t ]*)$/,"");var a=o.reduce(function(l,c){var d=c.match(/\n([\t ]+|(?!\s).)/g);return d?l.concat(d.map(function(m){var h,g;return(g=(h=m.match(/[\t ]/g))===null||h===void 0?void 0:h.length)!==null&&g!==void 0?g:0})):l},[]);if(a.length){var i=new RegExp(`
[	 ]{`+Math.min.apply(Math,a)+"}","g");o=o.map(function(l){return l.replace(i,`
`)})}o[0]=o[0].replace(/^\r?\n/,"");var u=o[0];return e.forEach(function(l,c){var d=u.match(/(?:^|\n)( *)$/),m=d?d[1]:"",h=l;typeof l=="string"&&l.includes(`
`)&&(h=String(l).split(`
`).map(function(g,re){return re===0?g:""+m+g}).join(`
`)),u+=h+o[c+1]}),u}n(_$1,"dedent");var ps=_$1,mo=new Map,Ml="UNIVERSAL_STORE:",ee={PENDING:"PENDING",RESOLVED:"RESOLVED",REJECTED:"REJECTED"},w=class Ge{constructor(e,r){if(this.debugging=!1,this.listeners=new Map([["*",new Set]]),this.getState=n(()=>(this.debug("getState",{state:this.state}),this.state),"getState"),this.subscribe=n((o,a)=>{let i=typeof o=="function",u=i?"*":o,l=i?o:a;if(this.debug("subscribe",{eventType:u,listener:l}),!l)throw new TypeError(`Missing first subscribe argument, or second if first is the event type, when subscribing to a UniversalStore with id '${this.id}'`);return this.listeners.has(u)||this.listeners.set(u,new Set),this.listeners.get(u).add(l),()=>{var c;this.debug("unsubscribe",{eventType:u,listener:l}),this.listeners.has(u)&&(this.listeners.get(u).delete(l),((c=this.listeners.get(u))==null?void 0:c.size)===0&&this.listeners.delete(u))}},"subscribe"),this.send=n(o=>{if(this.debug("send",{event:o}),this.status!==Ge.Status.READY)throw new TypeError(_$1`Cannot send event before store is ready. You can get the current status with store.status,
        or await store.readyPromise to wait for the store to be ready before sending events.
        ${JSON.stringify({event:o,id:this.id,actor:this.actor,environment:this.environment},null,2)}`);this.emitToListeners(o,{actor:this.actor}),this.emitToChannel(o,{actor:this.actor})},"send"),this.debugging=e.debug??!1,!Ge.isInternalConstructing)throw new TypeError("UniversalStore is not constructable - use UniversalStore.create() instead");if(Ge.isInternalConstructing=!1,this.id=e.id,this.actorId=Date.now().toString(36)+Math.random().toString(36).substring(2),this.actorType=e.leader?Ge.ActorType.LEADER:Ge.ActorType.FOLLOWER,this.state=e.initialState,this.channelEventName=`${Ml}${this.id}`,this.debug("constructor",{options:e,environmentOverrides:r,channelEventName:this.channelEventName}),this.actor.type===Ge.ActorType.LEADER)this.syncing={state:ee.RESOLVED,promise:Promise.resolve()};else{let o,a,i=new Promise((u,l)=>{o=n(()=>{this.syncing.state===ee.PENDING&&(this.syncing.state=ee.RESOLVED,u())},"syncingResolve"),a=n(c=>{this.syncing.state===ee.PENDING&&(this.syncing.state=ee.REJECTED,l(c))},"syncingReject")});this.syncing={state:ee.PENDING,promise:i,resolve:o,reject:a}}this.getState=this.getState.bind(this),this.setState=this.setState.bind(this),this.subscribe=this.subscribe.bind(this),this.onStateChange=this.onStateChange.bind(this),this.send=this.send.bind(this),this.emitToChannel=this.emitToChannel.bind(this),this.prepareThis=this.prepareThis.bind(this),this.emitToListeners=this.emitToListeners.bind(this),this.handleChannelEvents=this.handleChannelEvents.bind(this),this.debug=this.debug.bind(this),this.channel=(r==null?void 0:r.channel)??Ge.preparation.channel,this.environment=(r==null?void 0:r.environment)??Ge.preparation.environment,this.channel&&this.environment?this.prepareThis({channel:this.channel,environment:this.environment}):Ge.preparation.promise.then(this.prepareThis)}static setupPreparationPromise(){let e,r,o=new Promise((a,i)=>{e=n(u=>{a(u)},"resolveRef"),r=n((...u)=>{i(u)},"rejectRef")});Ge.preparation={resolve:e,reject:r,promise:o}}get actor(){return Object.freeze({id:this.actorId,type:this.actorType,environment:this.environment??Ge.Environment.UNKNOWN})}get status(){var e;if(!this.channel||!this.environment)return Ge.Status.UNPREPARED;switch((e=this.syncing)==null?void 0:e.state){case ee.PENDING:case void 0:return Ge.Status.SYNCING;case ee.REJECTED:return Ge.Status.ERROR;case ee.RESOLVED:default:return Ge.Status.READY}}untilReady(){var e;return Promise.all([Ge.preparation.promise,(e=this.syncing)==null?void 0:e.promise])}static create(e){if(!e||typeof(e==null?void 0:e.id)!="string")throw new TypeError("id is required and must be a string, when creating a UniversalStore");e.debug&&console.debug(_$1`[UniversalStore]
        create`,{options:e});let r=mo.get(e.id);if(r)return console.warn(_$1`UniversalStore with id "${e.id}" already exists in this environment, re-using existing.
        You should reuse the existing instance instead of trying to create a new one.`),r;Ge.isInternalConstructing=!0;let o=new Ge(e);return mo.set(e.id,o),o}static __prepare(e,r){Ge.preparation.channel=e,Ge.preparation.environment=r,Ge.preparation.resolve({channel:e,environment:r})}setState(e){let r=this.state,o=typeof e=="function"?e(r):e;if(this.debug("setState",{newState:o,previousState:r,updater:e}),this.status!==Ge.Status.READY)throw new TypeError(_$1`Cannot set state before store is ready. You can get the current status with store.status,
        or await store.readyPromise to wait for the store to be ready before sending events.
        ${JSON.stringify({newState:o,id:this.id,actor:this.actor,environment:this.environment},null,2)}`);this.state=o;let a={type:Ge.InternalEventType.SET_STATE,payload:{state:o,previousState:r}};this.emitToChannel(a,{actor:this.actor}),this.emitToListeners(a,{actor:this.actor})}onStateChange(e){return this.debug("onStateChange",{listener:e}),this.subscribe(Ge.InternalEventType.SET_STATE,({payload:r},o)=>{e(r.state,r.previousState,o)})}emitToChannel(e,r){var o;this.debug("emitToChannel",{event:e,eventInfo:r,channel:this.channel}),(o=this.channel)==null||o.emit(this.channelEventName,{event:e,eventInfo:r})}prepareThis({channel:e,environment:r}){this.channel=e,this.environment=r,this.debug("prepared",{channel:e,environment:r}),this.channel.on(this.channelEventName,this.handleChannelEvents),this.actor.type===Ge.ActorType.LEADER?this.emitToChannel({type:Ge.InternalEventType.LEADER_CREATED},{actor:this.actor}):(this.emitToChannel({type:Ge.InternalEventType.FOLLOWER_CREATED},{actor:this.actor}),this.emitToChannel({type:Ge.InternalEventType.EXISTING_STATE_REQUEST},{actor:this.actor}),setTimeout(()=>{this.syncing.reject(new TypeError(`No existing state found for follower with id: '${this.id}'. Make sure a leader with the same id exists before creating a follower.`))},1e3))}emitToListeners(e,r){let o=this.listeners.get(e.type),a=this.listeners.get("*");this.debug("emitToListeners",{event:e,eventInfo:r,eventTypeListeners:o,everythingListeners:a}),[...o??[],...a??[]].forEach(i=>i(e,r))}handleChannelEvents(e){var a,i,u,l,c;let{event:r,eventInfo:o}=e;if([o.actor.id,(a=o.forwardingActor)==null?void 0:a.id].includes(this.actor.id)){this.debug("handleChannelEvents: Ignoring event from self",{channelEvent:e});return}else if(((i=this.syncing)==null?void 0:i.state)===ee.PENDING&&r.type!==Ge.InternalEventType.EXISTING_STATE_RESPONSE){this.debug("handleChannelEvents: Ignoring event while syncing",{channelEvent:e});return}if(this.debug("handleChannelEvents",{channelEvent:e}),this.actor.type===Ge.ActorType.LEADER){let d=!0;switch(r.type){case Ge.InternalEventType.EXISTING_STATE_REQUEST:d=!1;let m={type:Ge.InternalEventType.EXISTING_STATE_RESPONSE,payload:this.state};this.debug("handleChannelEvents: responding to existing state request",{responseEvent:m}),this.emitToChannel(m,{actor:this.actor});break;case Ge.InternalEventType.LEADER_CREATED:d=!1,this.syncing.state=ee.REJECTED,this.debug("handleChannelEvents: erroring due to second leader being created",{event:r}),console.error(_$1`Detected multiple UniversalStore leaders created with the same id "${this.id}".
            Only one leader can exists at a time, your stores are now in an invalid state.
            Leaders detected:
            this: ${JSON.stringify(this.actor,null,2)}
            other: ${JSON.stringify(o.actor,null,2)}`);break}d&&(this.debug("handleChannelEvents: forwarding event",{channelEvent:e}),this.emitToChannel(r,{actor:o.actor,forwardingActor:this.actor}))}if(this.actor.type===Ge.ActorType.FOLLOWER)switch(r.type){case Ge.InternalEventType.EXISTING_STATE_RESPONSE:if(this.debug("handleChannelEvents: Setting state from leader's existing state response",{event:r}),((u=this.syncing)==null?void 0:u.state)!==ee.PENDING)break;(c=(l=this.syncing).resolve)==null||c.call(l);let d={type:Ge.InternalEventType.SET_STATE,payload:{state:r.payload,previousState:this.state}};this.state=r.payload,this.emitToListeners(d,o);break}switch(r.type){case Ge.InternalEventType.SET_STATE:this.debug("handleChannelEvents: Setting state",{event:r}),this.state=r.payload.state;break}this.emitToListeners(r,{actor:o.actor})}debug(e,r){this.debugging&&console.debug(_$1`[UniversalStore::${this.id}::${this.environment??Ge.Environment.UNKNOWN}]
        ${e}`,JSON.stringify({data:r,actor:this.actor,state:this.state,status:this.status},null,2))}static __reset(){Ge.preparation.reject(new Error("reset")),Ge.setupPreparationPromise(),Ge.isInternalConstructing=!1}};n(w,"UniversalStore"),w.ActorType={LEADER:"LEADER",FOLLOWER:"FOLLOWER"},w.Environment={SERVER:"SERVER",MANAGER:"MANAGER",PREVIEW:"PREVIEW",UNKNOWN:"UNKNOWN",MOCK:"MOCK"},w.InternalEventType={EXISTING_STATE_REQUEST:"__EXISTING_STATE_REQUEST",EXISTING_STATE_RESPONSE:"__EXISTING_STATE_RESPONSE",SET_STATE:"__SET_STATE",LEADER_CREATED:"__LEADER_CREATED",FOLLOWER_CREATED:"__FOLLOWER_CREATED"},w.Status={UNPREPARED:"UNPREPARED",SYNCING:"SYNCING",READY:"READY",ERROR:"ERROR"},w.isInternalConstructing=!1,w.setupPreparationPromise();var Q=w,Ul=n(t=>t.transports!==void 0,"isMulti"),Gl=n(()=>Math.random().toString(16).slice(2),"generateRandomId"),ho=class{constructor(e={}){this.sender=Gl(),this.events={},this.data={},this.transports=[],this.isAsync=e.async||!1,Ul(e)?(this.transports=e.transports||[],this.transports.forEach(r=>{r.setHandler(o=>this.handleEvent(o))})):this.transports=e.transport?[e.transport]:[],this.transports.forEach(r=>{r.setHandler(o=>this.handleEvent(o))})}get hasTransport(){return this.transports.length>0}addListener(e,r){this.events[e]=this.events[e]||[],this.events[e].push(r)}emit(e,...r){let o={type:e,args:r,from:this.sender},a={};r.length>=1&&r[0]&&r[0].options&&(a=r[0].options);let i=n(()=>{this.transports.forEach(u=>{u.send(o,a)}),this.handleEvent(o)},"handler");this.isAsync?setImmediate(i):i()}last(e){return this.data[e]}eventNames(){return Object.keys(this.events)}listenerCount(e){let r=this.listeners(e);return r?r.length:0}listeners(e){return this.events[e]||void 0}once(e,r){let o=this.onceListener(e,r);this.addListener(e,o)}removeAllListeners(e){e?this.events[e]&&delete this.events[e]:this.events={}}removeListener(e,r){let o=this.listeners(e);o&&(this.events[e]=o.filter(a=>a!==r))}on(e,r){this.addListener(e,r)}off(e,r){this.removeListener(e,r)}handleEvent(e){let r=this.listeners(e.type);r&&r.length&&r.forEach(o=>{o.apply(e,e.args)}),this.data[e.type]=e.args}onceListener(e,r){let o=n((...a)=>(this.removeListener(e,o),r(...a)),"onceListener");return o}};n(ho,"Channel");var ie=ho,mr={};_e(mr,{deprecate:()=>ae,logger:()=>I$1,once:()=>j$1,pretty:()=>X});var{LOGLEVEL:ql}=E$1,Se={trace:1,debug:2,info:3,warn:4,error:5,silent:10},Bl=ql,$e=Se[Bl]||Se.info,I$1={trace:n((t,...e)=>{$e<=Se.trace&&console.trace(t,...e)},"trace"),debug:n((t,...e)=>{$e<=Se.debug&&console.debug(t,...e)},"debug"),info:n((t,...e)=>{$e<=Se.info&&console.info(t,...e)},"info"),warn:n((t,...e)=>{$e<=Se.warn&&console.warn(t,...e)},"warn"),error:n((t,...e)=>{$e<=Se.error&&console.error(t,...e)},"error"),log:n((t,...e)=>{$e<Se.silent&&console.log(t,...e)},"log")},go=new Set,j$1=n(t=>(e,...r)=>{if(!go.has(e))return go.add(e),I$1[t](e,...r)},"once");j$1.clear=()=>go.clear();j$1.trace=j$1("trace");j$1.debug=j$1("debug");j$1.info=j$1("info");j$1.warn=j$1("warn");j$1.error=j$1("error");j$1.log=j$1("log");var ae=j$1("warn"),X=n(t=>(...e)=>{let r=[];if(e.length){let o=/<span\s+style=(['"])([^'"]*)\1\s*>/gi,a=/<\/span>/gi,i;for(r.push(e[0].replace(o,"%c").replace(a,"%c"));i=o.exec(e[0]);)r.push(i[2]),r.push("");for(let u=1;u<e.length;u++)r.push(e[u])}I$1[t].apply(I$1,r)},"pretty");X.trace=X("trace");X.debug=X("debug");X.info=X("info");X.warn=X("warn");X.error=X("error");var Vl=Object.create,ds=Object.defineProperty,Hl=Object.getOwnPropertyDescriptor,us=Object.getOwnPropertyNames,zl=Object.getPrototypeOf,Wl=Object.prototype.hasOwnProperty,Z=n((t,e)=>n(function(){return e||(0,t[us(t)[0]])((e={exports:{}}).exports,e),e.exports},"__require"),"__commonJS"),$l=n((t,e,r,o)=>{if(e&&typeof e=="object"||typeof e=="function")for(let a of us(e))!Wl.call(t,a)&&a!==r&&ds(t,a,{get:n(()=>e[a],"get"),enumerable:!(o=Hl(e,a))||o.enumerable});return t},"__copyProps"),st=n((t,e,r)=>(r=t!=null?Vl(zl(t)):{},$l(e||!t||!t.__esModule?ds(r,"default",{value:t,enumerable:!0}):r,t)),"__toESM"),Yl=["bubbles","cancelBubble","cancelable","composed","currentTarget","defaultPrevented","eventPhase","isTrusted","returnValue","srcElement","target","timeStamp","type"],Kl=["detail"];function fs(t){let e=Yl.filter(r=>t[r]!==void 0).reduce((r,o)=>({...r,[o]:t[o]}),{});return t instanceof CustomEvent&&Kl.filter(r=>t[r]!==void 0).forEach(r=>{e[r]=t[r]}),e}n(fs,"extractEventHiddenProperties");var Ps=ue(it()),Ts=Z({"node_modules/has-symbols/shams.js"(t,e){e.exports=n(function(){if(typeof Symbol!="function"||typeof Object.getOwnPropertySymbols!="function")return!1;if(typeof Symbol.iterator=="symbol")return!0;var r={},o=Symbol("test"),a=Object(o);if(typeof o=="string"||Object.prototype.toString.call(o)!=="[object Symbol]"||Object.prototype.toString.call(a)!=="[object Symbol]")return!1;var i=42;r[o]=i;for(o in r)return!1;if(typeof Object.keys=="function"&&Object.keys(r).length!==0||typeof Object.getOwnPropertyNames=="function"&&Object.getOwnPropertyNames(r).length!==0)return!1;var u=Object.getOwnPropertySymbols(r);if(u.length!==1||u[0]!==o||!Object.prototype.propertyIsEnumerable.call(r,o))return!1;if(typeof Object.getOwnPropertyDescriptor=="function"){var l=Object.getOwnPropertyDescriptor(r,o);if(l.value!==i||l.enumerable!==!0)return!1}return!0},"hasSymbols")}}),Es=Z({"node_modules/has-symbols/index.js"(t,e){var r=typeof Symbol<"u"&&Symbol,o=Ts();e.exports=n(function(){return typeof r!="function"||typeof Symbol!="function"||typeof r("foo")!="symbol"||typeof Symbol("bar")!="symbol"?!1:o()},"hasNativeSymbols")}}),Xl=Z({"node_modules/function-bind/implementation.js"(t,e){var r="Function.prototype.bind called on incompatible ",o=Array.prototype.slice,a=Object.prototype.toString,i="[object Function]";e.exports=n(function(u){var l=this;if(typeof l!="function"||a.call(l)!==i)throw new TypeError(r+l);for(var c=o.call(arguments,1),d,m=n(function(){if(this instanceof d){var le=l.apply(this,c.concat(o.call(arguments)));return Object(le)===le?le:this}else return l.apply(u,c.concat(o.call(arguments)))},"binder"),h=Math.max(0,l.length-c.length),g=[],re=0;re<h;re++)g.push("$"+re);if(d=Function("binder","return function ("+g.join(",")+"){ return binder.apply(this,arguments); }")(m),l.prototype){var ne=n(function(){},"Empty2");ne.prototype=l.prototype,d.prototype=new ne,ne.prototype=null}return d},"bind")}}),To=Z({"node_modules/function-bind/index.js"(t,e){var r=Xl();e.exports=Function.prototype.bind||r}}),Jl=Z({"node_modules/has/src/index.js"(t,e){var r=To();e.exports=r.call(Function.call,Object.prototype.hasOwnProperty)}}),Rs=Z({"node_modules/get-intrinsic/index.js"(t,e){var r,o=SyntaxError,a=Function,i=TypeError,u=n(function(qe){try{return a('"use strict"; return ('+qe+").constructor;")()}catch{}},"getEvalledConstructor"),l=Object.getOwnPropertyDescriptor;if(l)try{l({},"")}catch{l=null}var c=n(function(){throw new i},"throwTypeError"),d=l?function(){try{return arguments.callee,c}catch{try{return l(arguments,"callee").get}catch{return c}}}():c,m=Es()(),h=Object.getPrototypeOf||function(qe){return qe.__proto__},g={},re=typeof Uint8Array>"u"?r:h(Uint8Array),ne={"%AggregateError%":typeof AggregateError>"u"?r:AggregateError,"%Array%":Array,"%ArrayBuffer%":typeof ArrayBuffer>"u"?r:ArrayBuffer,"%ArrayIteratorPrototype%":m?h([][Symbol.iterator]()):r,"%AsyncFromSyncIteratorPrototype%":r,"%AsyncFunction%":g,"%AsyncGenerator%":g,"%AsyncGeneratorFunction%":g,"%AsyncIteratorPrototype%":g,"%Atomics%":typeof Atomics>"u"?r:Atomics,"%BigInt%":typeof BigInt>"u"?r:BigInt,"%Boolean%":Boolean,"%DataView%":typeof DataView>"u"?r:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":typeof Float32Array>"u"?r:Float32Array,"%Float64Array%":typeof Float64Array>"u"?r:Float64Array,"%FinalizationRegistry%":typeof FinalizationRegistry>"u"?r:FinalizationRegistry,"%Function%":a,"%GeneratorFunction%":g,"%Int8Array%":typeof Int8Array>"u"?r:Int8Array,"%Int16Array%":typeof Int16Array>"u"?r:Int16Array,"%Int32Array%":typeof Int32Array>"u"?r:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":m?h(h([][Symbol.iterator]())):r,"%JSON%":typeof JSON=="object"?JSON:r,"%Map%":typeof Map>"u"?r:Map,"%MapIteratorPrototype%":typeof Map>"u"||!m?r:h(new Map()[Symbol.iterator]()),"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":typeof Promise>"u"?r:Promise,"%Proxy%":typeof Proxy>"u"?r:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":typeof Reflect>"u"?r:Reflect,"%RegExp%":RegExp,"%Set%":typeof Set>"u"?r:Set,"%SetIteratorPrototype%":typeof Set>"u"||!m?r:h(new Set()[Symbol.iterator]()),"%SharedArrayBuffer%":typeof SharedArrayBuffer>"u"?r:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":m?h(""[Symbol.iterator]()):r,"%Symbol%":m?Symbol:r,"%SyntaxError%":o,"%ThrowTypeError%":d,"%TypedArray%":re,"%TypeError%":i,"%Uint8Array%":typeof Uint8Array>"u"?r:Uint8Array,"%Uint8ClampedArray%":typeof Uint8ClampedArray>"u"?r:Uint8ClampedArray,"%Uint16Array%":typeof Uint16Array>"u"?r:Uint16Array,"%Uint32Array%":typeof Uint32Array>"u"?r:Uint32Array,"%URIError%":URIError,"%WeakMap%":typeof WeakMap>"u"?r:WeakMap,"%WeakRef%":typeof WeakRef>"u"?r:WeakRef,"%WeakSet%":typeof WeakSet>"u"?r:WeakSet},le=n(function qe(Dt){var p;if(Dt==="%AsyncFunction%")p=u("async function () {}");else if(Dt==="%GeneratorFunction%")p=u("function* () {}");else if(Dt==="%AsyncGeneratorFunction%")p=u("async function* () {}");else if(Dt==="%AsyncGenerator%"){var A=qe("%AsyncGeneratorFunction%");A&&(p=A.prototype)}else if(Dt==="%AsyncIteratorPrototype%"){var B=qe("%AsyncGenerator%");B&&(p=h(B.prototype))}return ne[Dt]=p,p},"doEval2"),J={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},ce=To(),F=Jl(),se=ce.call(Function.call,Array.prototype.concat),he=ce.call(Function.apply,Array.prototype.splice),Ve=ce.call(Function.call,String.prototype.replace),ve=ce.call(Function.call,String.prototype.slice),we=ce.call(Function.call,RegExp.prototype.exec),Nt=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,Bt=/\\(\\)?/g,Ft=n(function(qe){var Dt=ve(qe,0,1),p=ve(qe,-1);if(Dt==="%"&&p!=="%")throw new o("invalid intrinsic syntax, expected closing `%`");if(p==="%"&&Dt!=="%")throw new o("invalid intrinsic syntax, expected opening `%`");var A=[];return Ve(qe,Nt,function(B,de,pe,Lt){A[A.length]=pe?Ve(Lt,Bt,"$1"):de||B}),A},"stringToPath3"),jt=n(function(qe,Dt){var p=qe,A;if(F(J,p)&&(A=J[p],p="%"+A[0]+"%"),F(ne,p)){var B=ne[p];if(B===g&&(B=le(p)),typeof B>"u"&&!Dt)throw new i("intrinsic "+qe+" exists, but is not available. Please file an issue!");return{alias:A,name:p,value:B}}throw new o("intrinsic "+qe+" does not exist!")},"getBaseIntrinsic2");e.exports=n(function(qe,Dt){if(typeof qe!="string"||qe.length===0)throw new i("intrinsic name must be a non-empty string");if(arguments.length>1&&typeof Dt!="boolean")throw new i('"allowMissing" argument must be a boolean');if(we(/^%?[^%]*%?$/,qe)===null)throw new o("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var p=Ft(qe),A=p.length>0?p[0]:"",B=jt("%"+A+"%",Dt),de=B.name,pe=B.value,Lt=!1,qt=B.alias;qt&&(A=qt[0],he(p,se([0,1],qt)));for(var Ut=1,Mt=!0;Ut<p.length;Ut+=1){var Gt=p[Ut],lr=ve(Gt,0,1),zr=ve(Gt,-1);if((lr==='"'||lr==="'"||lr==="`"||zr==='"'||zr==="'"||zr==="`")&&lr!==zr)throw new o("property names with quotes must have matching quotes");if((Gt==="constructor"||!Mt)&&(Lt=!0),A+="."+Gt,de="%"+A+"%",F(ne,de))pe=ne[de];else if(pe!=null){if(!(Gt in pe)){if(!Dt)throw new i("base intrinsic for "+qe+" exists, but the property is not available.");return}if(l&&Ut+1>=p.length){var Qr=l(pe,Gt);Mt=!!Qr,Mt&&"get"in Qr&&!("originalValue"in Qr.get)?pe=Qr.get:pe=pe[Gt]}else Mt=F(pe,Gt),pe=pe[Gt];Mt&&!Lt&&(ne[de]=pe)}}return pe},"GetIntrinsic")}}),Ql=Z({"node_modules/call-bind/index.js"(t,e){var r=To(),o=Rs(),a=o("%Function.prototype.apply%"),i=o("%Function.prototype.call%"),u=o("%Reflect.apply%",!0)||r.call(i,a),l=o("%Object.getOwnPropertyDescriptor%",!0),c=o("%Object.defineProperty%",!0),d=o("%Math.max%");if(c)try{c({},"a",{value:1})}catch{c=null}e.exports=n(function(h){var g=u(r,i,arguments);if(l&&c){var re=l(g,"length");re.configurable&&c(g,"length",{value:1+d(0,h.length-(arguments.length-1))})}return g},"callBind");var m=n(function(){return u(r,a,arguments)},"applyBind2");c?c(e.exports,"apply",{value:m}):e.exports.apply=m}}),Zl=Z({"node_modules/call-bind/callBound.js"(t,e){var r=Rs(),o=Ql(),a=o(r("String.prototype.indexOf"));e.exports=n(function(i,u){var l=r(i,!!u);return typeof l=="function"&&a(i,".prototype.")>-1?o(l):l},"callBoundIntrinsic")}}),ec=Z({"node_modules/has-tostringtag/shams.js"(t,e){var r=Ts();e.exports=n(function(){return r()&&!!Symbol.toStringTag},"hasToStringTagShams")}}),rc=Z({"node_modules/is-regex/index.js"(t,e){var r=Zl(),o=ec()(),a,i,u,l;o&&(a=r("Object.prototype.hasOwnProperty"),i=r("RegExp.prototype.exec"),u={},c=n(function(){throw u},"throwRegexMarker"),l={toString:c,valueOf:c},typeof Symbol.toPrimitive=="symbol"&&(l[Symbol.toPrimitive]=c));var c,d=r("Object.prototype.toString"),m=Object.getOwnPropertyDescriptor,h="[object RegExp]";e.exports=n(o?function(g){if(!g||typeof g!="object")return!1;var re=m(g,"lastIndex"),ne=re&&a(re,"value");if(!ne)return!1;try{i(g,l)}catch(le){return le===u}}:function(g){return!g||typeof g!="object"&&typeof g!="function"?!1:d(g)===h},"isRegex")}}),tc=Z({"node_modules/is-function/index.js"(t,e){e.exports=o;var r=Object.prototype.toString;function o(a){if(!a)return!1;var i=r.call(a);return i==="[object Function]"||typeof a=="function"&&i!=="[object RegExp]"||typeof window<"u"&&(a===window.setTimeout||a===window.alert||a===window.confirm||a===window.prompt)}n(o,"isFunction3")}}),oc=Z({"node_modules/is-symbol/index.js"(t,e){var r=Object.prototype.toString,o=Es()();o?(a=Symbol.prototype.toString,i=/^Symbol\(.*\)$/,u=n(function(l){return typeof l.valueOf()!="symbol"?!1:i.test(a.call(l))},"isRealSymbolObject"),e.exports=n(function(l){if(typeof l=="symbol")return!0;if(r.call(l)!=="[object Symbol]")return!1;try{return u(l)}catch{return!1}},"isSymbol3")):e.exports=n(function(l){return!1},"isSymbol3");var a,i,u}}),nc=st(rc()),sc=st(tc()),ic=st(oc());function ac(t){return t!=null&&typeof t=="object"&&Array.isArray(t)===!1}n(ac,"isObject");var lc=typeof global=="object"&&global&&global.Object===Object&&global,cc=lc,pc=typeof self=="object"&&self&&self.Object===Object&&self,dc=cc||pc||Function("return this")(),Eo=dc,uc=Eo.Symbol,Ye=uc,As=Object.prototype,fc=As.hasOwnProperty,yc=As.toString,hr=Ye?Ye.toStringTag:void 0;function mc(t){var e=fc.call(t,hr),r=t[hr];try{t[hr]=void 0;var o=!0}catch{}var a=yc.call(t);return o&&(e?t[hr]=r:delete t[hr]),a}n(mc,"getRawTag");var hc=mc,gc=Object.prototype,Sc=gc.toString;function bc(t){return Sc.call(t)}n(bc,"objectToString");var Tc=bc,Ec="[object Null]",Rc="[object Undefined]",ms=Ye?Ye.toStringTag:void 0;function Ac(t){return t==null?t===void 0?Rc:Ec:ms&&ms in Object(t)?hc(t):Tc(t)}n(Ac,"baseGetTag");var xs=Ac;function xc(t){return t!=null&&typeof t=="object"}n(xc,"isObjectLike");var vc=xc,wc="[object Symbol]";function _c(t){return typeof t=="symbol"||vc(t)&&xs(t)==wc}n(_c,"isSymbol");var Ro=_c;function Cc(t,e){for(var r=-1,o=t==null?0:t.length,a=Array(o);++r<o;)a[r]=e(t[r],r,t);return a}n(Cc,"arrayMap");var Pc=Cc,Oc=Array.isArray,Ao=Oc,hs=Ye?Ye.prototype:void 0,gs=hs?hs.toString:void 0;function vs(t){if(typeof t=="string")return t;if(Ao(t))return Pc(t,vs)+"";if(Ro(t))return gs?gs.call(t):"";var e=t+"";return e=="0"&&1/t==-1/0?"-0":e}n(vs,"baseToString");var Fc=vs;function Dc(t){var e=typeof t;return t!=null&&(e=="object"||e=="function")}n(Dc,"isObject2");var ws=Dc,Nc="[object AsyncFunction]",kc="[object Function]",Lc="[object GeneratorFunction]",jc="[object Proxy]";function Mc(t){if(!ws(t))return!1;var e=xs(t);return e==kc||e==Lc||e==Nc||e==jc}n(Mc,"isFunction");var Uc=Mc,Gc=Eo["__core-js_shared__"],bo=Gc,Ss=function(){var t=/[^.]+$/.exec(bo&&bo.keys&&bo.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();function qc(t){return!!Ss&&Ss in t}n(qc,"isMasked");var Bc=qc,Vc=Function.prototype,Hc=Vc.toString;function zc(t){if(t!=null){try{return Hc.call(t)}catch{}try{return t+""}catch{}}return""}n(zc,"toSource");var Wc=zc,$c=/[\\^$.*+?()[\]{}|]/g,Yc=/^\[object .+?Constructor\]$/,Kc=Function.prototype,Xc=Object.prototype,Jc=Kc.toString,Qc=Xc.hasOwnProperty,Zc=RegExp("^"+Jc.call(Qc).replace($c,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function ep(t){if(!ws(t)||Bc(t))return!1;var e=Uc(t)?Zc:Yc;return e.test(Wc(t))}n(ep,"baseIsNative");var rp=ep;function tp(t,e){return t==null?void 0:t[e]}n(tp,"getValue");var op=tp;function np(t,e){var r=op(t,e);return rp(r)?r:void 0}n(np,"getNative");var _s=np;function sp(t,e){return t===e||t!==t&&e!==e}n(sp,"eq");var ip=sp,ap=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,lp=/^\w*$/;function cp(t,e){if(Ao(t))return!1;var r=typeof t;return r=="number"||r=="symbol"||r=="boolean"||t==null||Ro(t)?!0:lp.test(t)||!ap.test(t)||e!=null&&t in Object(e)}n(cp,"isKey");var pp=cp,dp=_s(Object,"create"),gr=dp;function up(){this.__data__=gr?gr(null):{},this.size=0}n(up,"hashClear");var fp=up;function yp(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}n(yp,"hashDelete");var mp=yp,hp="__lodash_hash_undefined__",gp=Object.prototype,Sp=gp.hasOwnProperty;function bp(t){var e=this.__data__;if(gr){var r=e[t];return r===hp?void 0:r}return Sp.call(e,t)?e[t]:void 0}n(bp,"hashGet");var Tp=bp,Ep=Object.prototype,Rp=Ep.hasOwnProperty;function Ap(t){var e=this.__data__;return gr?e[t]!==void 0:Rp.call(e,t)}n(Ap,"hashHas");var xp=Ap,vp="__lodash_hash_undefined__";function wp(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=gr&&e===void 0?vp:e,this}n(wp,"hashSet");var _p=wp;function Ke(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var o=t[e];this.set(o[0],o[1])}}n(Ke,"Hash");Ke.prototype.clear=fp;Ke.prototype.delete=mp;Ke.prototype.get=Tp;Ke.prototype.has=xp;Ke.prototype.set=_p;var bs=Ke;function Cp(){this.__data__=[],this.size=0}n(Cp,"listCacheClear");var Pp=Cp;function Op(t,e){for(var r=t.length;r--;)if(ip(t[r][0],e))return r;return-1}n(Op,"assocIndexOf");var lt=Op,Ip=Array.prototype,Fp=Ip.splice;function Dp(t){var e=this.__data__,r=lt(e,t);if(r<0)return!1;var o=e.length-1;return r==o?e.pop():Fp.call(e,r,1),--this.size,!0}n(Dp,"listCacheDelete");var Np=Dp;function kp(t){var e=this.__data__,r=lt(e,t);return r<0?void 0:e[r][1]}n(kp,"listCacheGet");var Lp=kp;function jp(t){return lt(this.__data__,t)>-1}n(jp,"listCacheHas");var Mp=jp;function Up(t,e){var r=this.__data__,o=lt(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this}n(Up,"listCacheSet");var Gp=Up;function Xe(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var o=t[e];this.set(o[0],o[1])}}n(Xe,"ListCache");Xe.prototype.clear=Pp;Xe.prototype.delete=Np;Xe.prototype.get=Lp;Xe.prototype.has=Mp;Xe.prototype.set=Gp;var qp=Xe,Bp=_s(Eo,"Map"),Vp=Bp;function Hp(){this.size=0,this.__data__={hash:new bs,map:new(Vp||qp),string:new bs}}n(Hp,"mapCacheClear");var zp=Hp;function Wp(t){var e=typeof t;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?t!=="__proto__":t===null}n(Wp,"isKeyable");var $p=Wp;function Yp(t,e){var r=t.__data__;return $p(e)?r[typeof e=="string"?"string":"hash"]:r.map}n(Yp,"getMapData");var ct=Yp;function Kp(t){var e=ct(this,t).delete(t);return this.size-=e?1:0,e}n(Kp,"mapCacheDelete");var Xp=Kp;function Jp(t){return ct(this,t).get(t)}n(Jp,"mapCacheGet");var Qp=Jp;function Zp(t){return ct(this,t).has(t)}n(Zp,"mapCacheHas");var ed=Zp;function rd(t,e){var r=ct(this,t),o=r.size;return r.set(t,e),this.size+=r.size==o?0:1,this}n(rd,"mapCacheSet");var td=rd;function Je(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var o=t[e];this.set(o[0],o[1])}}n(Je,"MapCache");Je.prototype.clear=zp;Je.prototype.delete=Xp;Je.prototype.get=Qp;Je.prototype.has=ed;Je.prototype.set=td;var Cs=Je,od="Expected a function";function xo(t,e){if(typeof t!="function"||e!=null&&typeof e!="function")throw new TypeError(od);var r=n(function(){var o=arguments,a=e?e.apply(this,o):o[0],i=r.cache;if(i.has(a))return i.get(a);var u=t.apply(this,o);return r.cache=i.set(a,u)||i,u},"memoized");return r.cache=new(xo.Cache||Cs),r}n(xo,"memoize");xo.Cache=Cs;var nd=xo,sd=500;function id(t){var e=nd(t,function(o){return r.size===sd&&r.clear(),o}),r=e.cache;return e}n(id,"memoizeCapped");var ad=id,ld=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,cd=/\\(\\)?/g,pd=ad(function(t){var e=[];return t.charCodeAt(0)===46&&e.push(""),t.replace(ld,function(r,o,a,i){e.push(a?i.replace(cd,"$1"):o||r)}),e}),dd=pd;function ud(t){return t==null?"":Fc(t)}n(ud,"toString");var fd=ud;function yd(t,e){return Ao(t)?t:pp(t,e)?[t]:dd(fd(t))}n(yd,"castPath");var md=yd;function gd(t){if(typeof t=="string"||Ro(t))return t;var e=t+"";return e=="0"&&1/t==-1/0?"-0":e}n(gd,"toKey");var Sd=gd;function bd(t,e){e=md(e,t);for(var r=0,o=e.length;t!=null&&r<o;)t=t[Sd(e[r++])];return r&&r==o?t:void 0}n(bd,"baseGet");var Td=bd;function Ed(t,e,r){var o=t==null?void 0:Td(t,e);return o===void 0?r:o}n(Ed,"get");var Rd=Ed,at=ac,Ad=n(t=>{let e=null,r=!1,o=!1,a=!1,i="";if(t.indexOf("//")>=0||t.indexOf("/*")>=0)for(let u=0;u<t.length;u+=1)!e&&!r&&!o&&!a?t[u]==='"'||t[u]==="'"||t[u]==="`"?e=t[u]:t[u]==="/"&&t[u+1]==="*"?r=!0:t[u]==="/"&&t[u+1]==="/"?o=!0:t[u]==="/"&&t[u+1]!=="/"&&(a=!0):(e&&(t[u]===e&&t[u-1]!=="\\"||t[u]===`
`&&e!=="`")&&(e=null),a&&(t[u]==="/"&&t[u-1]!=="\\"||t[u]===`
`)&&(a=!1),r&&t[u-1]==="/"&&t[u-2]==="*"&&(r=!1),o&&t[u]===`
`&&(o=!1)),!r&&!o&&(i+=t[u]);else i=t;return i},"removeCodeComments"),xd=(0,Ps.default)(1e4)(t=>Ad(t).replace(/\n\s*/g,"").trim()),vd=n(function(t,e){let r=e.slice(0,e.indexOf("{")),o=e.slice(e.indexOf("{"));if(r.includes("=>")||r.includes("function"))return e;let a=r;return a=a.replace(t,"function"),a+o},"convertShorthandMethods2"),wd=/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z$/,Sr=n(t=>t.match(/^[\[\{\"\}].*[\]\}\"]$/),"isJSON");function Os(t){if(!at(t))return t;let e=t,r=!1;return typeof Event<"u"&&t instanceof Event&&(e=fs(e),r=!0),e=Object.keys(e).reduce((o,a)=>{try{e[a]&&e[a].toJSON,o[a]=e[a]}catch{r=!0}return o},{}),r?e:t}n(Os,"convertUnconventionalData");var _d=n(function(t){let e,r,o,a;return n(function(i,u){try{if(i==="")return a=[],e=new Map([[u,"[]"]]),r=new Map,o=[],u;let l=r.get(this)||this;for(;o.length&&l!==o[0];)o.shift(),a.pop();if(typeof u=="boolean")return u;if(u===void 0)return t.allowUndefined?"_undefined_":void 0;if(u===null)return null;if(typeof u=="number")return u===-1/0?"_-Infinity_":u===1/0?"_Infinity_":Number.isNaN(u)?"_NaN_":u;if(typeof u=="bigint")return`_bigint_${u.toString()}`;if(typeof u=="string")return wd.test(u)?t.allowDate?`_date_${u}`:void 0:u;if((0,nc.default)(u))return t.allowRegExp?`_regexp_${u.flags}|${u.source}`:void 0;if((0,sc.default)(u)){if(!t.allowFunction)return;let{name:d}=u,m=u.toString();return m.match(/(\[native code\]|WEBPACK_IMPORTED_MODULE|__webpack_exports__|__webpack_require__)/)?`_function_${d}|${(()=>{}).toString()}`:`_function_${d}|${xd(vd(i,m))}`}if((0,ic.default)(u)){if(!t.allowSymbol)return;let d=Symbol.keyFor(u);return d!==void 0?`_gsymbol_${d}`:`_symbol_${u.toString().slice(7,-1)}`}if(o.length>=t.maxDepth)return Array.isArray(u)?`[Array(${u.length})]`:"[Object]";if(u===this)return`_duplicate_${JSON.stringify(a)}`;if(u instanceof Error&&t.allowError)return{__isConvertedError__:!0,errorProperties:{...u.cause?{cause:u.cause}:{},...u,name:u.name,message:u.message,stack:u.stack,"_constructor-name_":u.constructor.name}};if(u.constructor&&u.constructor.name&&u.constructor.name!=="Object"&&!Array.isArray(u)&&!t.allowClass)return;let c=e.get(u);if(!c){let d=Array.isArray(u)?u:Os(u);if(u.constructor&&u.constructor.name&&u.constructor.name!=="Object"&&!Array.isArray(u)&&t.allowClass)try{Object.assign(d,{"_constructor-name_":u.constructor.name})}catch{}return a.push(i),o.unshift(d),e.set(u,JSON.stringify(a)),u!==d&&r.set(u,d),d}return`_duplicate_${c}`}catch{return}},"replace")},"replacer2"),Cd=n(function reviver(options){let refs=[],root;return n(function revive(key,value){if(key===""&&(root=value,refs.forEach(({target:t,container:e,replacement:r})=>{let o=Sr(r)?JSON.parse(r):r.split(".");o.length===0?e[t]=root:e[t]=Rd(root,o)})),key==="_constructor-name_")return value;if(at(value)&&value.__isConvertedError__){let{message:t,...e}=value.errorProperties,r=new Error(t);return Object.assign(r,e),r}if(at(value)&&value["_constructor-name_"]&&options.allowFunction){let t=value["_constructor-name_"];if(t!=="Object"){let e=new Function(`return function ${t.replace(/[^a-zA-Z0-9$_]+/g,"")}(){}`)();Object.setPrototypeOf(value,new e)}return delete value["_constructor-name_"],value}if(typeof value=="string"&&value.startsWith("_function_")&&options.allowFunction){let[,name,source]=value.match(/_function_([^|]*)\|(.*)/)||[],sourceSanitized=source.replace(/[(\(\))|\\| |\]|`]*$/,"");if(!options.lazyEval)return eval(`(${sourceSanitized})`);let result=n((...args)=>{let f=eval(`(${sourceSanitized})`);return f(...args)},"result");return Object.defineProperty(result,"toString",{value:n(()=>sourceSanitized,"value")}),Object.defineProperty(result,"name",{value:name}),result}if(typeof value=="string"&&value.startsWith("_regexp_")&&options.allowRegExp){let[,t,e]=value.match(/_regexp_([^|]*)\|(.*)/)||[];return new RegExp(e,t)}return typeof value=="string"&&value.startsWith("_date_")&&options.allowDate?new Date(value.replace("_date_","")):typeof value=="string"&&value.startsWith("_duplicate_")?(refs.push({target:key,container:this,replacement:value.replace(/^_duplicate_/,"")}),null):typeof value=="string"&&value.startsWith("_symbol_")&&options.allowSymbol?Symbol(value.replace("_symbol_","")):typeof value=="string"&&value.startsWith("_gsymbol_")&&options.allowSymbol?Symbol.for(value.replace("_gsymbol_","")):typeof value=="string"&&value==="_-Infinity_"?-1/0:typeof value=="string"&&value==="_Infinity_"?1/0:typeof value=="string"&&value==="_NaN_"?NaN:typeof value=="string"&&value.startsWith("_bigint_")&&typeof BigInt=="function"?BigInt(value.replace("_bigint_","")):value},"revive")},"reviver"),Is={maxDepth:10,space:void 0,allowFunction:!0,allowRegExp:!0,allowDate:!0,allowClass:!0,allowError:!0,allowUndefined:!0,allowSymbol:!0,lazyEval:!0},pt=n((t,e={})=>{let r={...Is,...e};return JSON.stringify(Os(t),_d(r),e.space)},"stringify"),Pd=n(()=>{let t=new Map;return n(function e(r){at(r)&&Object.entries(r).forEach(([o,a])=>{a==="_undefined_"?r[o]=void 0:t.get(a)||(t.set(a,!0),e(a))}),Array.isArray(r)&&r.forEach((o,a)=>{o==="_undefined_"?(t.set(o,!0),r[a]=void 0):t.get(o)||(t.set(o,!0),e(o))})},"mutateUndefined")},"mutator"),dt=n((t,e={})=>{let r={...Is,...e},o=JSON.parse(t,Cd(r));return Pd()(o),o},"parse"),vo="Invariant failed";function fe(t,e){if(!t)throw new Error(vo)}n(fe,"invariant");var Fs=n(t=>{let e=Array.from(document.querySelectorAll("iframe[data-is-storybook]")),[r,...o]=e.filter(i=>{var c,d;try{return((c=i.contentWindow)==null?void 0:c.location.origin)===t.source.location.origin&&((d=i.contentWindow)==null?void 0:d.location.pathname)===t.source.location.pathname}catch{}try{return i.contentWindow===t.source}catch{}let u=i.getAttribute("src"),l;try{if(!u)return!1;({origin:l}=new URL(u,document.location.toString()))}catch{return!1}return l===t.origin}),a=r==null?void 0:r.getAttribute("src");if(a&&o.length===0){let{protocol:i,host:u,pathname:l}=new URL(a,document.location.toString());return`${i}//${u}${l}`}return o.length>0&&I$1.error("found multiple candidates for event source"),null},"getEventSourceUrl"),{document:wo,location:_o}=E$1,Ds="storybook-channel",Id={allowFunction:!1,maxDepth:25},Co=class{constructor(e){if(this.config=e,this.connected=!1,this.buffer=[],typeof(E$1==null?void 0:E$1.addEventListener)=="function"&&E$1.addEventListener("message",this.handleEvent.bind(this),!1),e.page!=="manager"&&e.page!=="preview")throw new Error(`postmsg-channel: "config.page" cannot be "${e.page}"`)}setHandler(e){this.handler=(...r)=>{e.apply(this,r),!this.connected&&this.getLocalFrame().length&&(this.flush(),this.connected=!0)}}send(e,r){let{target:o,allowRegExp:a,allowFunction:i,allowSymbol:u,allowDate:l,allowError:c,allowUndefined:d,allowClass:m,maxDepth:h,space:g,lazyEval:re}=r||{},ne=Object.fromEntries(Object.entries({allowRegExp:a,allowFunction:i,allowSymbol:u,allowDate:l,allowError:c,allowUndefined:d,allowClass:m,maxDepth:h,space:g,lazyEval:re}).filter(([se,he])=>typeof he<"u")),le={...Id,...E$1.CHANNEL_OPTIONS||{},...ne},J=this.getFrames(o),ce=new URLSearchParams((_o==null?void 0:_o.search)||""),F=pt({key:Ds,event:e,refId:ce.get("refId")},le);return J.length?(this.buffer.length&&this.flush(),J.forEach(se=>{try{se.postMessage(F,"*")}catch{I$1.error("sending over postmessage fail")}}),Promise.resolve(null)):new Promise((se,he)=>{this.buffer.push({event:e,resolve:se,reject:he})})}flush(){let{buffer:e}=this;this.buffer=[],e.forEach(r=>{this.send(r.event).then(r.resolve).catch(r.reject)})}getFrames(e){if(this.config.page==="manager"){let r=Array.from(wo.querySelectorAll("iframe[data-is-storybook][data-is-loaded]")).flatMap(o=>{try{return o.contentWindow&&o.dataset.isStorybook!==void 0&&o.id===e?[o.contentWindow]:[]}catch{return[]}});return r!=null&&r.length?r:this.getCurrentFrames()}return E$1&&E$1.parent&&E$1.parent!==E$1.self?[E$1.parent]:[]}getCurrentFrames(){return this.config.page==="manager"?Array.from(wo.querySelectorAll('[data-is-storybook="true"]')).flatMap(e=>e.contentWindow?[e.contentWindow]:[]):E$1&&E$1.parent?[E$1.parent]:[]}getLocalFrame(){return this.config.page==="manager"?Array.from(wo.querySelectorAll("#storybook-preview-iframe")).flatMap(e=>e.contentWindow?[e.contentWindow]:[]):E$1&&E$1.parent?[E$1.parent]:[]}handleEvent(e){try{let{data:r}=e,{key:o,event:a,refId:i}=typeof r=="string"&&Sr(r)?dt(r,E$1.CHANNEL_OPTIONS||{}):r;if(o===Ds){let u=this.config.page==="manager"?'<span style="color: #37D5D3; background: black"> manager </span>':'<span style="color: #1EA7FD; background: black"> preview </span>',l=Object.values(ge).includes(a.type)?`<span style="color: #FF4785">${a.type}</span>`:`<span style="color: #FFAE00">${a.type}</span>`;if(i&&(a.refId=i),a.source=this.config.page==="preview"?e.origin:Fs(e),!a.source){X.error(`${u} received ${l} but was unable to determine the source of the event`);return}let c=`${u} received ${l} (${r.length})`;X.debug(_o.origin!==a.source?c:`${c} <span style="color: gray">(on ${_o.origin} from ${a.source})</span>`,...a.args),fe(this.handler,"ChannelHandler should be set"),this.handler(a)}}catch(r){I$1.error(r)}}};n(Co,"PostMessageTransport");var Qe=Co,{WebSocket:Fd}=E$1,Po=15e3,Oo=5e3,Io=class{constructor({url:e,onError:r,page:o}){this.buffer=[],this.isReady=!1,this.isClosed=!1,this.pingTimeout=0,this.socket=new Fd(e),this.socket.onopen=()=>{this.isReady=!0,this.heartbeat(),this.flush()},this.socket.onmessage=({data:a})=>{let i=typeof a=="string"&&Sr(a)?dt(a):a;fe(this.handler),this.handler(i),i.type==="ping"&&(this.heartbeat(),this.send({type:"pong"}))},this.socket.onerror=a=>{r&&r(a)},this.socket.onclose=a=>{fe(this.handler),this.handler({type:Wt,args:[{reason:a.reason,code:a.code}],from:o||"preview"}),this.isClosed=!0,clearTimeout(this.pingTimeout)}}heartbeat(){clearTimeout(this.pingTimeout),this.pingTimeout=setTimeout(()=>{this.socket.close(3008,"timeout")},Po+Oo)}setHandler(e){this.handler=e}send(e){this.isClosed||(this.isReady?this.sendNow(e):this.sendLater(e))}sendLater(e){this.buffer.push(e)}sendNow(e){let r=pt(e,{maxDepth:15,allowFunction:!1,...E$1.CHANNEL_OPTIONS});this.socket.send(r)}flush(){let{buffer:e}=this;this.buffer=[],e.forEach(r=>this.send(r))}};n(Io,"WebsocketTransport");var Ze=Io,{CONFIG_TYPE:Dd}=E$1,Nd=ie;function kd({page:t,extraTransports:e=[]}){let r=[new Qe({page:t}),...e];if(Dd==="DEVELOPMENT"){let a=window.location.protocol==="http:"?"ws":"wss",{hostname:i,port:u}=window.location,l=`${a}://${i}:${u}/storybook-server-channel`;r.push(new Ze({url:l,onError:n(()=>{},"onError"),page:t}))}let o=new ie({transports:r});return Q.__prepare(o,t==="manager"?Q.Environment.MANAGER:Q.Environment.PREVIEW),o}n(kd,"createBrowserChannel");var Tr={};_e(Tr,{Addon_TypesEnum:()=>Ns});var Ns=(t=>(t.TAB="tab",t.PANEL="panel",t.TOOL="tool",t.TOOLEXTRA="toolextra",t.PREVIEW="preview",t.experimental_PAGE="page",t.experimental_SIDEBAR_BOTTOM="sidebar-bottom",t.experimental_SIDEBAR_TOP="sidebar-top",t.experimental_TEST_PROVIDER="test-provider",t))(Ns||{}),Yr={};_e(Yr,{DocsContext:()=>me,HooksContext:()=>be,Preview:()=>Me,PreviewWeb:()=>Wr,PreviewWithSelection:()=>Ue,ReporterAPI:()=>Ee,StoryStore:()=>Le,UrlStore:()=>Be,WebView:()=>He,addons:()=>te$1,applyHooks:()=>ft,combineArgs:()=>tr,combineParameters:()=>Y,composeConfigs:()=>ke,composeStepRunners:()=>Ct,composeStories:()=>qi,composeStory:()=>Pn,createPlaywrightTest:()=>Bi,decorateStory:()=>xn,defaultDecorateStory:()=>vt,definePreview:()=>ks,experimental_MockUniversalStore:()=>gt,experimental_UniversalStore:()=>Q,experimental_useUniversalStore:()=>Si,filterArgTypes:()=>Mr,getCsfFactoryAnnotations:()=>Pt,inferControls:()=>ir,makeDecorator:()=>$s,mockChannel:()=>ut,normalizeProjectAnnotations:()=>Ne,normalizeStory:()=>De,prepareMeta:()=>wt,prepareStory:()=>sr,sanitizeStoryContextUpdate:()=>vn,setDefaultProjectAnnotations:()=>Ui,setProjectAnnotations:()=>Gi,simulateDOMContentLoaded:()=>$r,simulatePageLoad:()=>ss,sortStoriesV7:()=>Ki,useArgs:()=>zs,useCallback:()=>er,useChannel:()=>Vs,useEffect:()=>Er,useGlobals:()=>Ws,useMemo:()=>Ms,useParameter:()=>Hs,useReducer:()=>Bs,useRef:()=>Gs,useState:()=>mt,useStoryContext:()=>Rr,userOrAutoTitle:()=>Wi,userOrAutoTitleFromSpecifier:()=>Fn});function ut(){let t={setHandler:n(()=>{},"setHandler"),send:n(()=>{},"send")};return new ie({transport:t})}n(ut,"mockChannel");var No=class{constructor(){this.getChannel=n(()=>{if(!this.channel){let e=ut();return this.setChannel(e),e}return this.channel},"getChannel"),this.ready=n(()=>this.promise,"ready"),this.hasChannel=n(()=>!!this.channel,"hasChannel"),this.setChannel=n(e=>{this.channel=e,this.resolve()},"setChannel"),this.promise=new Promise(e=>{this.resolve=()=>e(this.getChannel())})}};n(No,"AddonStore");var Do=No,Fo="__STORYBOOK_ADDONS_PREVIEW";function Ld(){return E$1[Fo]||(E$1[Fo]=new Do),E$1[Fo]}n(Ld,"getAddonsStore");var te$1=Ld();function ks(t){return t}n(ks,"definePreview");var Mo=class{constructor(){this.hookListsMap=void 0,this.mountedDecorators=void 0,this.prevMountedDecorators=void 0,this.currentHooks=void 0,this.nextHookIndex=void 0,this.currentPhase=void 0,this.currentEffects=void 0,this.prevEffects=void 0,this.currentDecoratorName=void 0,this.hasUpdates=void 0,this.currentContext=void 0,this.renderListener=n(e=>{var r;e===((r=this.currentContext)==null?void 0:r.id)&&(this.triggerEffects(),this.currentContext=null,this.removeRenderListeners())},"renderListener"),this.init()}init(){this.hookListsMap=new WeakMap,this.mountedDecorators=new Set,this.prevMountedDecorators=new Set,this.currentHooks=[],this.nextHookIndex=0,this.currentPhase="NONE",this.currentEffects=[],this.prevEffects=[],this.currentDecoratorName=null,this.hasUpdates=!1,this.currentContext=null}clean(){this.prevEffects.forEach(e=>{e.destroy&&e.destroy()}),this.init(),this.removeRenderListeners()}getNextHook(){let e=this.currentHooks[this.nextHookIndex];return this.nextHookIndex+=1,e}triggerEffects(){this.prevEffects.forEach(e=>{!this.currentEffects.includes(e)&&e.destroy&&e.destroy()}),this.currentEffects.forEach(e=>{this.prevEffects.includes(e)||(e.destroy=e.create())}),this.prevEffects=this.currentEffects,this.currentEffects=[]}addRenderListeners(){this.removeRenderListeners(),te$1.getChannel().on(We,this.renderListener)}removeRenderListeners(){te$1.getChannel().removeListener(We,this.renderListener)}};n(Mo,"HooksContext");var be=Mo;function Ls(t){let e=n((...r)=>{let{hooks:o}=typeof r[0]=="function"?r[1]:r[0],a=o.currentPhase,i=o.currentHooks,u=o.nextHookIndex,l=o.currentDecoratorName;o.currentDecoratorName=t.name,o.prevMountedDecorators.has(t)?(o.currentPhase="UPDATE",o.currentHooks=o.hookListsMap.get(t)||[]):(o.currentPhase="MOUNT",o.currentHooks=[],o.hookListsMap.set(t,o.currentHooks),o.prevMountedDecorators.add(t)),o.nextHookIndex=0;let c=E$1.STORYBOOK_HOOKS_CONTEXT;E$1.STORYBOOK_HOOKS_CONTEXT=o;let d=t(...r);if(E$1.STORYBOOK_HOOKS_CONTEXT=c,o.currentPhase==="UPDATE"&&o.getNextHook()!=null)throw new Error("Rendered fewer hooks than expected. This may be caused by an accidental early return statement.");return o.currentPhase=a,o.currentHooks=i,o.nextHookIndex=u,o.currentDecoratorName=l,d},"hookified");return e.originalFn=t,e}n(Ls,"hookify");var ko=0,jd=25,ft=n(t=>(e,r)=>{let o=t(Ls(e),r.map(a=>Ls(a)));return a=>{let{hooks:i}=a;i.prevMountedDecorators??(i.prevMountedDecorators=new Set),i.mountedDecorators=new Set([e,...r]),i.currentContext=a,i.hasUpdates=!1;let u=o(a);for(ko=1;i.hasUpdates;)if(i.hasUpdates=!1,i.currentEffects=[],u=o(a),ko+=1,ko>jd)throw new Error("Too many re-renders. Storybook limits the number of renders to prevent an infinite loop.");return i.addRenderListeners(),u}},"applyHooks"),Md=n((t,e)=>t.length===e.length&&t.every((r,o)=>r===e[o]),"areDepsEqual"),Lo=n(()=>new Error("Storybook preview hooks can only be called inside decorators and story functions."),"invalidHooksError");function js(){return E$1.STORYBOOK_HOOKS_CONTEXT||null}n(js,"getHooksContextOrNull");function jo(){let t=js();if(t==null)throw Lo();return t}n(jo,"getHooksContextOrThrow");function Ud(t,e,r){let o=jo();if(o.currentPhase==="MOUNT"){r!=null&&!Array.isArray(r)&&I$1.warn(`${t} received a final argument that is not an array (instead, received ${r}). When specified, the final argument must be an array.`);let a={name:t,deps:r};return o.currentHooks.push(a),e(a),a}if(o.currentPhase==="UPDATE"){let a=o.getNextHook();if(a==null)throw new Error("Rendered more hooks than during the previous render.");return a.name!==t&&I$1.warn(`Storybook has detected a change in the order of Hooks${o.currentDecoratorName?` called by ${o.currentDecoratorName}`:""}. This will lead to bugs and errors if not fixed.`),r!=null&&a.deps==null&&I$1.warn(`${t} received a final argument during this render, but not during the previous render. Even though the final argument is optional, its type cannot change between renders.`),r!=null&&a.deps!=null&&r.length!==a.deps.length&&I$1.warn(`The final argument passed to ${t} changed size between renders. The order and size of this array must remain constant.
Previous: ${a.deps}
Incoming: ${r}`),(r==null||a.deps==null||!Md(r,a.deps))&&(e(a),a.deps=r),a}throw Lo()}n(Ud,"useHook");function yt(t,e,r){let{memoizedState:o}=Ud(t,a=>{a.memoizedState=e()},r);return o}n(yt,"useMemoLike");function Ms(t,e){return yt("useMemo",t,e)}n(Ms,"useMemo");function er(t,e){return yt("useCallback",()=>t,e)}n(er,"useCallback");function Us(t,e){return yt(t,()=>({current:e}),[])}n(Us,"useRefLike");function Gs(t){return Us("useRef",t)}n(Gs,"useRef");function Gd(){let t=js();if(t!=null&&t.currentPhase!=="NONE")t.hasUpdates=!0;else try{te$1.getChannel().emit(dr)}catch{I$1.warn("State updates of Storybook preview hooks work only in browser")}}n(Gd,"triggerUpdate");function qs(t,e){let r=Us(t,typeof e=="function"?e():e),o=n(a=>{r.current=typeof a=="function"?a(r.current):a,Gd()},"setState");return[r.current,o]}n(qs,"useStateLike");function mt(t){return qs("useState",t)}n(mt,"useState");function Bs(t,e,r){let o=r!=null?()=>r(e):e,[a,i]=qs("useReducer",o);return[a,n(u=>i(l=>t(l,u)),"dispatch")]}n(Bs,"useReducer");function Er(t,e){let r=jo(),o=yt("useEffect",()=>({create:t}),e);r.currentEffects.includes(o)||r.currentEffects.push(o)}n(Er,"useEffect");function Vs(t,e=[]){let r=te$1.getChannel();return Er(()=>(Object.entries(t).forEach(([o,a])=>r.on(o,a)),()=>{Object.entries(t).forEach(([o,a])=>r.removeListener(o,a))}),[...Object.keys(t),...e]),er(r.emit.bind(r),[r])}n(Vs,"useChannel");function Rr(){let{currentContext:t}=jo();if(t==null)throw Lo();return t}n(Rr,"useStoryContext");function Hs(t,e){let{parameters:r}=Rr();if(t)return r[t]??e}n(Hs,"useParameter");function zs(){let t=te$1.getChannel(),{id:e,args:r}=Rr(),o=er(i=>t.emit(yr,{storyId:e,updatedArgs:i}),[t,e]),a=er(i=>t.emit(ur,{storyId:e,argNames:i}),[t,e]);return[r,o,a]}n(zs,"useArgs");function Ws(){let t=te$1.getChannel(),{globals:e}=Rr(),r=er(o=>t.emit(fr,{globals:o}),[t]);return[e,r]}n(Ws,"useGlobals");var $s=n(({name:t,parameterName:e,wrapper:r,skipIfNoParametersOrOptions:o=!1})=>{let a=n(i=>(u,l)=>{let c=l.parameters&&l.parameters[e];return c&&c.disable||o&&!i&&!c?u(l):r(u,l,{options:i,parameters:c})},"decorator");return(...i)=>typeof i[0]=="function"?a()(...i):(...u)=>{if(u.length>1)return i.length>1?a(i)(...u):a(...i)(...u);throw new Error(`Passing stories directly into ${t}() is not allowed,
        instead use addDecorator(${t}) and pass options with the '${e}' parameter`)}},"makeDecorator");function Uo(t,e){let r={},o=Object.entries(t);for(let a=0;a<o.length;a++){let[i,u]=o[a];e(u,i)||(r[i]=u)}return r}n(Uo,"omitBy");function Go(t,e){let r={};for(let o=0;o<e.length;o++){let a=e[o];Object.prototype.hasOwnProperty.call(t,a)&&(r[a]=t[a])}return r}n(Go,"pick");function qo(t,e){let r={},o=Object.entries(t);for(let a=0;a<o.length;a++){let[i,u]=o[a];e(u,i)&&(r[i]=u)}return r}n(qo,"pickBy");function $$1(t){if(typeof t!="object"||t==null)return!1;if(Object.getPrototypeOf(t)===null)return!0;if(t.toString()!=="[object Object]")return!1;let e=t;for(;Object.getPrototypeOf(e)!==null;)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e}n($$1,"isPlainObject");function oe(t,e){let r={},o=Object.keys(t);for(let a=0;a<o.length;a++){let i=o[a],u=t[i];r[i]=e(u,i,t)}return r}n(oe,"mapValues");var Ys="[object RegExp]",Ks="[object String]",Xs="[object Number]",Js="[object Boolean]",Bo="[object Arguments]",Qs="[object Symbol]",Zs="[object Date]",ei="[object Map]",ri="[object Set]",ti="[object Array]",oi="[object Function]",ni="[object ArrayBuffer]",ht="[object Object]",si="[object Error]",ii="[object DataView]",ai="[object Uint8Array]",li="[object Uint8ClampedArray]",ci="[object Uint16Array]",pi="[object Uint32Array]",di="[object BigUint64Array]",ui="[object Int8Array]",fi="[object Int16Array]",yi="[object Int32Array]",mi="[object BigInt64Array]",hi="[object Float32Array]",gi="[object Float64Array]";function Vo(t){return Object.getOwnPropertySymbols(t).filter(e=>Object.prototype.propertyIsEnumerable.call(t,e))}n(Vo,"getSymbols");function Ho(t){return t==null?t===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(t)}n(Ho,"getTag");function Ar(t,e){if(typeof t==typeof e)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":return t===e;case"number":return t===e||Object.is(t,e);case"function":return t===e;case"object":return ye(t,e)}return ye(t,e)}n(Ar,"isEqual");function ye(t,e,r){if(Object.is(t,e))return!0;let o=Ho(t),a=Ho(e);if(o===Bo&&(o=ht),a===Bo&&(a=ht),o!==a)return!1;switch(o){case Ks:return t.toString()===e.toString();case Xs:{let l=t.valueOf(),c=e.valueOf();return l===c||Number.isNaN(l)&&Number.isNaN(c)}case Js:case Zs:case Qs:return Object.is(t.valueOf(),e.valueOf());case Ys:return t.source===e.source&&t.flags===e.flags;case oi:return t===e}r=r??new Map;let i=r.get(t),u=r.get(e);if(i!=null&&u!=null)return i===e;r.set(t,e),r.set(e,t);try{switch(o){case ei:{if(t.size!==e.size)return!1;for(let[l,c]of t.entries())if(!e.has(l)||!ye(c,e.get(l),r))return!1;return!0}case ri:{if(t.size!==e.size)return!1;let l=Array.from(t.values()),c=Array.from(e.values());for(let d=0;d<l.length;d++){let m=l[d],h=c.findIndex(g=>ye(m,g,r));if(h===-1)return!1;c.splice(h,1)}return!0}case ti:case ai:case li:case ci:case pi:case di:case ui:case fi:case yi:case mi:case hi:case gi:{if(typeof Buffer<"u"&&Buffer.isBuffer(t)!==Buffer.isBuffer(e)||t.length!==e.length)return!1;for(let l=0;l<t.length;l++)if(!ye(t[l],e[l],r))return!1;return!0}case ni:return t.byteLength!==e.byteLength?!1:ye(new Uint8Array(t),new Uint8Array(e),r);case ii:return t.byteLength!==e.byteLength||t.byteOffset!==e.byteOffset?!1:ye(t.buffer,e.buffer,r);case si:return t.name===e.name&&t.message===e.message;case ht:{if(!(ye(t.constructor,e.constructor,r)||$$1(t)&&$$1(e)))return!1;let l=[...Object.keys(t),...Vo(t)],c=[...Object.keys(e),...Vo(e)];if(l.length!==c.length)return!1;for(let d=0;d<l.length;d++){let m=l[d],h=t[m];if(!Object.prototype.hasOwnProperty.call(e,m))return!1;let g=e[m];if(!ye(h,g,r))return!1}return!0}default:return!1}}finally{r.delete(t),r.delete(e)}}n(ye,"areObjectsEqual");var Si=n((t,e)=>{let[r,o]=mt(e?e(t.getState()):t.getState());return Er(()=>t.onStateChange((a,i)=>{if(!e){o(a);return}let u=e(a),l=e(i);!Ar(u,l)&&o(u)}),[t,o,e]),[r,t.setState]},"useUniversalStore"),St=class Jn extends Q{constructor(e,r){Q.isInternalConstructing=!0,super({...e,leader:!0},{channel:new ie({}),environment:Q.Environment.MOCK}),Q.isInternalConstructing=!1,typeof(r==null?void 0:r.fn)=="function"&&(this.testUtils=r,this.getState=r.fn(this.getState),this.setState=r.fn(this.setState),this.subscribe=r.fn(this.subscribe),this.onStateChange=r.fn(this.onStateChange),this.send=r.fn(this.send))}static create(e,r){return new Jn(e,r)}unsubscribeAll(){var r,o;if(!this.testUtils)throw new Error(ps`Cannot call unsubscribeAll on a store that does not have testUtils.
        Please provide testUtils as the second argument when creating the store.`);let e=n(a=>{try{a.value()}catch{}},"callReturnedUnsubscribeFn");(r=this.subscribe.mock)==null||r.results.forEach(e),(o=this.onStateChange.mock)==null||o.results.forEach(e)}};n(St,"MockUniversalStore");var gt=St,kr={};_e(kr,{CalledExtractOnStoreError:()=>vr,CalledPreviewMethodBeforeInitializationError:()=>V,Category:()=>Ti,EmptyIndexError:()=>Pr,ImplicitActionsDuringRendering:()=>zo,MdxFileWithNoCsfReferencesError:()=>Cr,MissingRenderToCanvasError:()=>wr,MissingStoryAfterHmrError:()=>xr,MissingStoryFromCsfFileError:()=>Ir,MountMustBeDestructuredError:()=>Oe,NextJsSharpError:()=>Wo,NextjsRouterMocksNotAvailable:()=>$o,NoRenderFunctionError:()=>Dr,NoStoryMatchError:()=>Or,NoStoryMountedError:()=>Nr,StoryIndexFetchError:()=>_r,StoryStoreAccessedBeforeInitializationError:()=>Fr,UnknownArgTypesError:()=>Yo,UnsupportedViewportDimensionError:()=>Ko});function bi({code:t,category:e}){let r=String(t).padStart(4,"0");return`SB_${e}_${r}`}n(bi,"parseErrorCode");var bt=class Zn extends Error{constructor(e){super(Zn.getFullMessage(e)),this.data={},this.fromStorybook=!0,this.category=e.category,this.documentation=e.documentation??!1,this.code=e.code}get fullErrorCode(){return bi({code:this.code,category:this.category})}get name(){let e=this.constructor.name;return`${this.fullErrorCode} (${e})`}static getFullMessage({documentation:e,code:r,category:o,message:a}){let i;return e===!0?i=`https://storybook.js.org/error/${bi({code:r,category:o})}`:typeof e=="string"?i=e:Array.isArray(e)&&(i=`
${e.map(u=>`	- ${u}`).join(`
`)}`),`${a}${i!=null?`

More info: ${i}
`:""}`}};n(bt,"StorybookError");var G=bt,Ti=(t=>(t.BLOCKS="BLOCKS",t.DOCS_TOOLS="DOCS-TOOLS",t.PREVIEW_CLIENT_LOGGER="PREVIEW_CLIENT-LOGGER",t.PREVIEW_CHANNELS="PREVIEW_CHANNELS",t.PREVIEW_CORE_EVENTS="PREVIEW_CORE-EVENTS",t.PREVIEW_INSTRUMENTER="PREVIEW_INSTRUMENTER",t.PREVIEW_API="PREVIEW_API",t.PREVIEW_REACT_DOM_SHIM="PREVIEW_REACT-DOM-SHIM",t.PREVIEW_ROUTER="PREVIEW_ROUTER",t.PREVIEW_THEMING="PREVIEW_THEMING",t.RENDERER_HTML="RENDERER_HTML",t.RENDERER_PREACT="RENDERER_PREACT",t.RENDERER_REACT="RENDERER_REACT",t.RENDERER_SERVER="RENDERER_SERVER",t.RENDERER_SVELTE="RENDERER_SVELTE",t.RENDERER_VUE="RENDERER_VUE",t.RENDERER_VUE3="RENDERER_VUE3",t.RENDERER_WEB_COMPONENTS="RENDERER_WEB-COMPONENTS",t.FRAMEWORK_NEXTJS="FRAMEWORK_NEXTJS",t.ADDON_VITEST="ADDON_VITEST",t))(Ti||{}),Xo=class extends G{constructor(e){super({category:"PREVIEW_API",code:1,message:_$1`
        Couldn't find story matching id '${e.storyId}' after HMR.
        - Did you just rename a story?
        - Did you remove it from your CSF file?
        - Are you sure a story with the id '${e.storyId}' exists?
        - Please check the values in the stories field of your main.js config and see if they would match your CSF File.
        - Also check the browser console and terminal for potential error messages.`}),this.data=e}};n(Xo,"MissingStoryAfterHmrError");var xr=Xo,Jo=class extends G{constructor(e){super({category:"PREVIEW_API",code:2,documentation:"https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#using-implicit-actions-during-rendering-is-deprecated-for-example-in-the-play-function",message:_$1`
        We detected that you use an implicit action arg while ${e.phase} of your story.  
        ${e.deprecated?`
This is deprecated and won't work in Storybook 8 anymore.
`:""}
        Please provide an explicit spy to your args like this:
          import { fn } from '@storybook/test';
          ... 
          args: {
           ${e.name}: fn()
          }`}),this.data=e}};n(Jo,"ImplicitActionsDuringRendering");var zo=Jo,Qo=class extends G{constructor(){super({category:"PREVIEW_API",code:3,message:_$1`
        Cannot call \`storyStore.extract()\` without calling \`storyStore.cacheAllCsfFiles()\` first.

        You probably meant to call \`await preview.extract()\` which does the above for you.`})}};n(Qo,"CalledExtractOnStoreError");var vr=Qo,Zo=class extends G{constructor(){super({category:"PREVIEW_API",code:4,message:_$1`
        Expected your framework's preset to export a \`renderToCanvas\` field.

        Perhaps it needs to be upgraded for Storybook 7.0?`,documentation:"https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#mainjs-framework-field"})}};n(Zo,"MissingRenderToCanvasError");var wr=Zo,en=class extends G{constructor(e){super({category:"PREVIEW_API",code:5,message:_$1`
        Called \`Preview.${e.methodName}()\` before initialization.
        
        The preview needs to load the story index before most methods can be called. If you want
        to call \`${e.methodName}\`, try \`await preview.initializationPromise;\` first.
        
        If you didn't call the above code, then likely it was called by an addon that needs to
        do the above.`}),this.data=e}};n(en,"CalledPreviewMethodBeforeInitializationError");var V=en,rn=class extends G{constructor(e){super({category:"PREVIEW_API",code:6,message:_$1`
        Error fetching \`/index.json\`:
        
        ${e.text}

        If you are in development, this likely indicates a problem with your Storybook process,
        check the terminal for errors.

        If you are in a deployed Storybook, there may have been an issue deploying the full Storybook
        build.`}),this.data=e}};n(rn,"StoryIndexFetchError");var _r=rn,tn=class extends G{constructor(e){super({category:"PREVIEW_API",code:7,message:_$1`
        Tried to render docs entry ${e.storyId} but it is a MDX file that has no CSF
        references, or autodocs for a CSF file that some doesn't refer to itself.
        
        This likely is an internal error in Storybook's indexing, or you've attached the
        \`attached-mdx\` tag to an MDX file that is not attached.`}),this.data=e}};n(tn,"MdxFileWithNoCsfReferencesError");var Cr=tn,on=class extends G{constructor(){super({category:"PREVIEW_API",code:8,message:_$1`
        Couldn't find any stories in your Storybook.

        - Please check your stories field of your main.js config: does it match correctly?
        - Also check the browser console and terminal for error messages.`})}};n(on,"EmptyIndexError");var Pr=on,nn=class extends G{constructor(e){super({category:"PREVIEW_API",code:9,message:_$1`
        Couldn't find story matching '${e.storySpecifier}'.

        - Are you sure a story with that id exists?
        - Please check your stories field of your main.js config.
        - Also check the browser console and terminal for error messages.`}),this.data=e}};n(nn,"NoStoryMatchError");var Or=nn,sn=class extends G{constructor(e){super({category:"PREVIEW_API",code:10,message:_$1`
        Couldn't find story matching id '${e.storyId}' after importing a CSF file.

        The file was indexed as if the story was there, but then after importing the file in the browser
        we didn't find the story. Possible reasons:
        - You are using a custom story indexer that is misbehaving.
        - You have a custom file loader that is removing or renaming exports.

        Please check your browser console and terminal for errors that may explain the issue.`}),this.data=e}};n(sn,"MissingStoryFromCsfFileError");var Ir=sn,an=class extends G{constructor(){super({category:"PREVIEW_API",code:11,message:_$1`
        Cannot access the Story Store until the index is ready.

        It is not recommended to use methods directly on the Story Store anyway, in Storybook 9 we will
        remove access to the store entirely`})}};n(an,"StoryStoreAccessedBeforeInitializationError");var Fr=an,ln=class extends G{constructor(e){super({category:"PREVIEW_API",code:12,message:_$1`
      Incorrect use of mount in the play function.
      
      To use mount in the play function, you must satisfy the following two requirements: 
      
      1. You *must* destructure the mount property from the \`context\` (the argument passed to your play function). 
         This makes sure that Storybook does not start rendering the story before the play function begins.
      
      2. Your Storybook framework or builder must be configured to transpile to ES2017 or newer. 
         This is because destructuring statements and async/await usages are otherwise transpiled away, 
         which prevents Storybook from recognizing your usage of \`mount\`.
      
      Note that Angular is not supported. As async/await is transpiled to support the zone.js polyfill. 
      
      More info: https://storybook.js.org/docs/writing-tests/interaction-testing#run-code-before-the-component-gets-rendered
      
      Received the following play function:
      ${e.playFunction}`}),this.data=e}};n(ln,"MountMustBeDestructuredError");var Oe=ln,cn=class extends G{constructor(e){super({category:"PREVIEW_API",code:14,message:_$1`
        No render function available for storyId '${e.id}'
      `}),this.data=e}};n(cn,"NoRenderFunctionError");var Dr=cn,pn=class extends G{constructor(){super({category:"PREVIEW_API",code:15,message:_$1`
        No component is mounted in your story.
        
        This usually occurs when you destructure mount in the play function, but forget to call it.
        
        For example:

        async play({ mount, canvasElement }) {
          // 👈 mount should be called: await mount(); 
          const canvas = within(canvasElement);
          const button = await canvas.findByRole('button');
          await userEvent.click(button);
        };

        Make sure to either remove it or call mount in your play function.
      `})}};n(pn,"NoStoryMountedError");var Nr=pn,dn=class extends G{constructor(){super({category:"FRAMEWORK_NEXTJS",code:1,documentation:"https://storybook.js.org/docs/get-started/nextjs#faq",message:_$1`
      You are importing avif images, but you don't have sharp installed.

      You have to install sharp in order to use image optimization features in Next.js.
      `})}};n(dn,"NextJsSharpError");var Wo=dn,un=class extends G{constructor(e){super({category:"FRAMEWORK_NEXTJS",code:2,message:_$1`
        Tried to access router mocks from "${e.importType}" but they were not created yet. You might be running code in an unsupported environment.
      `}),this.data=e}};n(un,"NextjsRouterMocksNotAvailable");var $o=un,fn=class extends G{constructor(e){super({category:"DOCS-TOOLS",code:1,documentation:"https://github.com/storybookjs/storybook/issues/26606",message:_$1`
        There was a failure when generating detailed ArgTypes in ${e.language} for:
        ${JSON.stringify(e.type,null,2)} 
        
        Storybook will fall back to use a generic type description instead.

        This type is either not supported or it is a bug in the docgen generation in Storybook.
        If you think this is a bug, please detail it as much as possible in the Github issue.
      `}),this.data=e}};n(fn,"UnknownArgTypesError");var Yo=fn,yn=class extends G{constructor(e){super({category:"ADDON_VITEST",code:1,message:_$1`
        Encountered an unsupported value "${e.value}" when setting the viewport ${e.dimension} dimension.
        
        The Storybook plugin only supports values in the following units:
        - px, vh, vw, em, rem and %.
        
        You can either change the viewport for this story to use one of the supported units or skip the test by adding '!test' to the story's tags per https://storybook.js.org/docs/writing-stories/tags
      `}),this.data=e}};n(yn,"UnsupportedViewportDimensionError");var Ko=yn,Ot=ue(it()),rr=Symbol("incompatible"),mn=n((t,e)=>{let r=e.type;if(t==null||!r||e.mapping)return t;switch(r.name){case"string":return String(t);case"enum":return t;case"number":return Number(t);case"boolean":return String(t)==="true";case"array":return!r.value||!Array.isArray(t)?rr:t.reduce((o,a,i)=>{let u=mn(a,{type:r.value});return u!==rr&&(o[i]=u),o},new Array(t.length));case"object":return typeof t=="string"||typeof t=="number"?t:!r.value||typeof t!="object"?rr:Object.entries(t).reduce((o,[a,i])=>{let u=mn(i,{type:r.value[a]});return u===rr?o:Object.assign(o,{[a]:u})},{});default:return rr}},"map"),Ei=n((t,e)=>Object.entries(t).reduce((r,[o,a])=>{if(!e[o])return r;let i=mn(a,e[o]);return i===rr?r:Object.assign(r,{[o]:i})},{}),"mapArgsToTypes"),tr=n((t,e)=>Array.isArray(t)&&Array.isArray(e)?e.reduce((r,o,a)=>(r[a]=tr(t[a],e[a]),r),[...t]).filter(r=>r!==void 0):!$$1(t)||!$$1(e)?e:Object.keys({...t,...e}).reduce((r,o)=>{if(o in e){let a=tr(t[o],e[o]);a!==void 0&&(r[o]=a)}else r[o]=t[o];return r},{}),"combineArgs"),Ri=n((t,e)=>Object.entries(e).reduce((r,[o,{options:a}])=>{function i(){return o in t&&(r[o]=t[o]),r}if(n(i,"allowArg"),!a)return i();if(!Array.isArray(a))return j$1.error(_$1`
        Invalid argType: '${o}.options' should be an array.

        More info: https://storybook.js.org/docs/api/arg-types
      `),i();if(a.some(h=>h&&["object","function"].includes(typeof h)))return j$1.error(_$1`
        Invalid argType: '${o}.options' should only contain primitives. Use a 'mapping' for complex values.

        More info: https://storybook.js.org/docs/writing-stories/args#mapping-to-complex-arg-values
      `),i();let u=Array.isArray(t[o]),l=u&&t[o].findIndex(h=>!a.includes(h)),c=u&&l===-1;if(t[o]===void 0||a.includes(t[o])||c)return i();let d=u?`${o}[${l}]`:o,m=a.map(h=>typeof h=="string"?`'${h}'`:String(h)).join(", ");return j$1.warn(`Received illegal value for '${d}'. Supported options: ${m}`),r},{}),"validateOptions"),Ie=Symbol("Deeply equal"),or=n((t,e)=>{if(typeof t!=typeof e)return e;if(Ar(t,e))return Ie;if(Array.isArray(t)&&Array.isArray(e)){let r=e.reduce((o,a,i)=>{let u=or(t[i],a);return u!==Ie&&(o[i]=u),o},new Array(e.length));return e.length>=t.length?r:r.concat(new Array(t.length-e.length).fill(void 0))}return $$1(t)&&$$1(e)?Object.keys({...t,...e}).reduce((r,o)=>{let a=or(t==null?void 0:t[o],e==null?void 0:e[o]);return a===Ie?r:Object.assign(r,{[o]:a})},{}):e},"deepDiff"),hn="UNTARGETED";function Ai({args:t,argTypes:e}){let r={};return Object.entries(t).forEach(([o,a])=>{let{target:i=hn}=e[o]||{};r[i]=r[i]||{},r[i][o]=a}),r}n(Ai,"groupArgsByTarget");function qd(t){return Object.keys(t).forEach(e=>t[e]===void 0&&delete t[e]),t}n(qd,"deleteUndefined");var gn=class{constructor(){this.initialArgsByStoryId={},this.argsByStoryId={}}get(e){if(!(e in this.argsByStoryId))throw new Error(`No args known for ${e} -- has it been rendered yet?`);return this.argsByStoryId[e]}setInitial(e){if(!this.initialArgsByStoryId[e.id])this.initialArgsByStoryId[e.id]=e.initialArgs,this.argsByStoryId[e.id]=e.initialArgs;else if(this.initialArgsByStoryId[e.id]!==e.initialArgs){let r=or(this.initialArgsByStoryId[e.id],this.argsByStoryId[e.id]);this.initialArgsByStoryId[e.id]=e.initialArgs,this.argsByStoryId[e.id]=e.initialArgs,r!==Ie&&this.updateFromDelta(e,r)}}updateFromDelta(e,r){let o=Ri(r,e.argTypes);this.argsByStoryId[e.id]=tr(this.argsByStoryId[e.id],o)}updateFromPersisted(e,r){let o=Ei(r,e.argTypes);return this.updateFromDelta(e,o)}update(e,r){if(!(e in this.argsByStoryId))throw new Error(`No args known for ${e} -- has it been rendered yet?`);this.argsByStoryId[e]=qd({...this.argsByStoryId[e],...r})}};n(gn,"ArgsStore");var Tt=gn,Et=n((t={})=>Object.entries(t).reduce((e,[r,{defaultValue:o}])=>(typeof o<"u"&&(e[r]=o),e),{}),"getValuesFromArgTypes"),Sn=class{constructor({globals:e={},globalTypes:r={}}){this.set({globals:e,globalTypes:r})}set({globals:e={},globalTypes:r={}}){let o=this.initialGlobals&&or(this.initialGlobals,this.globals);this.allowedGlobalNames=new Set([...Object.keys(e),...Object.keys(r)]);let a=Et(r);this.initialGlobals={...a,...e},this.globals=this.initialGlobals,o&&o!==Ie&&this.updateFromPersisted(o)}filterAllowedGlobals(e){return Object.entries(e).reduce((r,[o,a])=>(this.allowedGlobalNames.has(o)?r[o]=a:I$1.warn(`Attempted to set a global (${o}) that is not defined in initial globals or globalTypes`),r),{})}updateFromPersisted(e){let r=this.filterAllowedGlobals(e);this.globals={...this.globals,...r}}get(){return this.globals}update(e){this.globals={...this.globals,...this.filterAllowedGlobals(e)}}};n(Sn,"GlobalsStore");var Rt=Sn,xi=ue(it()),Bd=(0,xi.default)(1)(t=>Object.values(t).reduce((e,r)=>(e[r.importPath]=e[r.importPath]||r,e),{})),bn=class{constructor({entries:e}={v:5,entries:{}}){this.entries=e}entryFromSpecifier(e){let r=Object.values(this.entries);if(e==="*")return r[0];if(typeof e=="string")return this.entries[e]?this.entries[e]:r.find(i=>i.id.startsWith(e));let{name:o,title:a}=e;return r.find(i=>i.name===o&&i.title===a)}storyIdToEntry(e){let r=this.entries[e];if(!r)throw new xr({storyId:e});return r}importPathToEntry(e){return Bd(this.entries)[e]}};n(bn,"StoryIndexStore");var At=bn,Vd=n(t=>typeof t=="string"?{name:t}:t,"normalizeType"),Hd=n(t=>typeof t=="string"?{type:t}:t,"normalizeControl"),zd=n((t,e)=>{let{type:r,control:o,...a}=t,i={name:e,...a};return r&&(i.type=Vd(r)),o?i.control=Hd(o):o===!1&&(i.control={disable:!0}),i},"normalizeInputType"),Fe=n(t=>oe(t,zd),"normalizeInputTypes");function vi(t){return t.replace(/_/g," ").replace(/-/g," ").replace(/\./g," ").replace(/([^\n])([A-Z])([a-z])/g,(e,r,o,a)=>`${r} ${o}${a}`).replace(/([a-z])([A-Z])/g,(e,r,o)=>`${r} ${o}`).replace(/([a-z])([0-9])/gi,(e,r,o)=>`${r} ${o}`).replace(/([0-9])([a-z])/gi,(e,r,o)=>`${r} ${o}`).replace(/(\s|^)(\w)/g,(e,r,o)=>`${r}${o.toUpperCase()}`).replace(/ +/g," ").trim()}n(vi,"toStartCaseStr");var En=ue(wi()),_i=n(t=>t.map(e=>typeof e<"u").filter(Boolean).length,"count"),Wd=n((t,e)=>{let{exists:r,eq:o,neq:a,truthy:i}=t;if(_i([r,o,a,i])>1)throw new Error(`Invalid conditional test ${JSON.stringify({exists:r,eq:o,neq:a})}`);if(typeof o<"u")return(0,En.isEqual)(e,o);if(typeof a<"u")return!(0,En.isEqual)(e,a);if(typeof r<"u"){let u=typeof e<"u";return r?u:!u}return typeof i>"u"||i?!!e:!e},"testValue"),Rn=n((t,e,r)=>{if(!t.if)return!0;let{arg:o,global:a}=t.if;if(_i([o,a])!==1)throw new Error(`Invalid conditional value ${JSON.stringify({arg:o,global:a})}`);let i=o?e[o]:r[a];return Wd(t.if,i)},"includeConditionalArg");function nr(t){return t!=null&&typeof t=="object"&&"_tag"in t&&(t==null?void 0:t._tag)==="Story"}n(nr,"isStory");var An=n(t=>t.toLowerCase().replace(/[ ’–—―′¿'`~!@#$%^&*()_|+\-=?;:'",.<>\{\}\[\]\\\/]/gi,"-").replace(/-+/g,"-").replace(/^-+/,"").replace(/-+$/,""),"sanitize"),Ci=n((t,e)=>{let r=An(t);if(r==="")throw new Error(`Invalid ${e} '${t}', must include alphanumeric characters`);return r},"sanitizeSafe"),Oi=n((t,e)=>`${Ci(t,"kind")}${e?`--${Ci(e,"name")}`:""}`,"toId"),Ii=n(t=>vi(t),"storyNameFromExport");function Pi(t,e){return Array.isArray(e)?e.includes(t):t.match(e)}n(Pi,"matches");function Lr(t,{includeStories:e,excludeStories:r}){return t!=="__esModule"&&(!e||Pi(t,e))&&(!r||!Pi(t,r))}n(Lr,"isExportStory");var Fi=n((...t)=>{let e=t.reduce((r,o)=>(o.startsWith("!")?r.delete(o.slice(1)):r.add(o),r),new Set);return Array.from(e)},"combineTags"),k=n(t=>Array.isArray(t)?t:t?[t]:[],"normalizeArrays"),$d=_$1`
CSF .story annotations deprecated; annotate story functions directly:
- StoryFn.story.name => StoryFn.storyName
- StoryFn.story.(parameters|decorators) => StoryFn.(parameters|decorators)
See https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#hoisted-csf-annotations for details and codemod.
`;function De(t,e,r){let o=e,a=typeof e=="function"?e:null,{story:i}=o;i&&(I$1.debug("deprecated story",i),ae($d));let u=Ii(t),l=typeof o!="function"&&o.name||o.storyName||(i==null?void 0:i.name)||u,c=[...k(o.decorators),...k(i==null?void 0:i.decorators)],d={...i==null?void 0:i.parameters,...o.parameters},m={...i==null?void 0:i.args,...o.args},h={...i==null?void 0:i.argTypes,...o.argTypes},g=[...k(o.loaders),...k(i==null?void 0:i.loaders)],re=[...k(o.beforeEach),...k(i==null?void 0:i.beforeEach)],ne=[...k(o.experimental_afterEach),...k(i==null?void 0:i.experimental_afterEach)],{render:le,play:J,tags:ce=[],globals:F={}}=o,se=d.__id||Oi(r.id,u);return{moduleExport:e,id:se,name:l,tags:ce,decorators:c,parameters:d,args:m,argTypes:Fe(h),loaders:g,beforeEach:re,experimental_afterEach:ne,globals:F,...le&&{render:le},...a&&{userStoryFn:a},...J&&{play:J}}}n(De,"normalizeStory");function jr(t,e=t.title,r){let{id:o,argTypes:a}=t;return{id:An(o||e),...t,title:e,...a&&{argTypes:Fe(a)},parameters:{fileName:r,...t.parameters}}}n(jr,"normalizeComponentAnnotations");var Yd=n(t=>{let{globals:e,globalTypes:r}=t;(e||r)&&I$1.error("Global args/argTypes can only be set globally",JSON.stringify({globals:e,globalTypes:r}))},"checkGlobals"),Kd=n(t=>{let{options:e}=t;e!=null&&e.storySort&&I$1.error("The storySort option parameter can only be set globally")},"checkStorySort"),xt=n(t=>{t&&(Yd(t),Kd(t))},"checkDisallowedParameters");function Di(t,e,r){let{default:o,__namedExportsOrder:a,...i}=t,u=Object.values(i)[0];if(nr(u)){let d=jr(u.meta.input,r,e);xt(d.parameters);let m={meta:d,stories:{},moduleExports:t};return Object.keys(i).forEach(h=>{if(Lr(h,d)){let g=De(h,i[h].input,d);xt(g.parameters),m.stories[g.id]=g}}),m.projectAnnotations=u.meta.preview.composed,m}let l=jr(o,r,e);xt(l.parameters);let c={meta:l,stories:{},moduleExports:t};return Object.keys(i).forEach(d=>{if(Lr(d,l)){let m=De(d,i[d],l);xt(m.parameters),c.stories[m.id]=m}}),c}n(Di,"processCSFFile");function ki(t){return t!=null&&Xd(t).includes("mount")}n(ki,"mountDestructured");function Xd(t){let e=t.toString().match(/[^(]*\(([^)]*)/);if(!e)return[];let r=Ni(e[1]);if(!r.length)return[];let o=r[0];return o.startsWith("{")&&o.endsWith("}")?Ni(o.slice(1,-1).replace(/\s/g,"")).map(a=>a.replace(/:.*|=.*/g,"")):[]}n(Xd,"getUsedProps");function Ni(t){let e=[],r=[],o=0;for(let i=0;i<t.length;i++)if(t[i]==="{"||t[i]==="[")r.push(t[i]==="{"?"}":"]");else if(t[i]===r[r.length-1])r.pop();else if(!r.length&&t[i]===","){let u=t.substring(o,i).trim();u&&e.push(u),o=i+1}let a=t.substring(o).trim();return a&&e.push(a),e}n(Ni,"splitByComma");function xn(t,e,r){let o=r(t);return a=>e(o,a)}n(xn,"decorateStory");function vn({componentId:t,title:e,kind:r,id:o,name:a,story:i,parameters:u,initialArgs:l,argTypes:c,...d}={}){return d}n(vn,"sanitizeStoryContextUpdate");function vt(t,e){let r={},o=n(i=>u=>{if(!r.value)throw new Error("Decorated function called without init");return r.value={...r.value,...vn(u)},i(r.value)},"bindWithContext"),a=e.reduce((i,u)=>xn(i,u,o),t);return i=>(r.value=i,a(i))}n(vt,"defaultDecorateStory");var Y=n((...t)=>{let e={},r=t.filter(Boolean),o=r.reduce((a,i)=>(Object.entries(i).forEach(([u,l])=>{let c=a[u];Array.isArray(l)||typeof c>"u"?a[u]=l:$$1(l)&&$$1(c)?e[u]=!0:typeof l<"u"&&(a[u]=l)}),a),{});return Object.keys(e).forEach(a=>{let i=r.filter(Boolean).map(u=>u[a]).filter(u=>typeof u<"u");i.every(u=>$$1(u))?o[a]=Y(...i):o[a]=i[i.length-1]}),o},"combineParameters");function sr(t,e,r){let{moduleExport:o,id:a,name:i}=t||{},u=Li(t,e,r),l=n(async ve=>{let we={};for(let Nt of[..."__STORYBOOK_TEST_LOADERS__"in E$1&&Array.isArray(E$1.__STORYBOOK_TEST_LOADERS__)?[E$1.__STORYBOOK_TEST_LOADERS__]:[],k(r.loaders),k(e.loaders),k(t.loaders)]){if(ve.abortSignal.aborted)return we;let Bt=await Promise.all(Nt.map(Ft=>Ft(ve)));Object.assign(we,...Bt)}return we},"applyLoaders"),c=n(async ve=>{let we=new Array;for(let Nt of[...k(r.beforeEach),...k(e.beforeEach),...k(t.beforeEach)]){if(ve.abortSignal.aborted)return we;let Bt=await Nt(ve);Bt&&we.push(Bt)}return we},"applyBeforeEach"),d=n(async ve=>{let we=[...k(r.experimental_afterEach),...k(e.experimental_afterEach),...k(t.experimental_afterEach)].reverse();for(let Nt of we){if(ve.abortSignal.aborted)return;await Nt(ve)}},"applyAfterEach"),m=n(ve=>ve.originalStoryFn(ve.args,ve),"undecoratedStoryFn"),{applyDecorators:h=vt,runStep:g}=r,re=[...k(t==null?void 0:t.decorators),...k(e==null?void 0:e.decorators),...k(r==null?void 0:r.decorators)],ne=(t==null?void 0:t.userStoryFn)||(t==null?void 0:t.render)||e.render||r.render,le=ft(h)(m,re),J=n(ve=>le(ve),"unboundStoryFn"),ce=(t==null?void 0:t.play)??(e==null?void 0:e.play),F=ki(ce);if(!ne&&!F)throw new Dr({id:a});let se=n(ve=>async()=>(await ve.renderToCanvas(),ve.canvas),"defaultMount"),he=t.mount??e.mount??r.mount??se,Ve=r.testingLibraryRender;return{storyGlobals:{},...u,moduleExport:o,id:a,name:i,story:i,originalStoryFn:ne,undecoratedStoryFn:m,unboundStoryFn:J,applyLoaders:l,applyBeforeEach:c,applyAfterEach:d,playFunction:ce,runStep:g,mount:he,testingLibraryRender:Ve,renderToCanvas:r.renderToCanvas,usesMount:F}}n(sr,"prepareStory");function wt(t,e,r){return{...Li(void 0,t,e),moduleExport:r}}n(wt,"prepareMeta");function Li(t,e,r){var ce;let o=["dev","test"],a=((ce=E$1.DOCS_OPTIONS)==null?void 0:ce.autodocs)===!0?["autodocs"]:[],i=Fi(...o,...a,...r.tags??[],...e.tags??[],...(t==null?void 0:t.tags)??[]),u=Y(r.parameters,e.parameters,t==null?void 0:t.parameters),{argTypesEnhancers:l=[],argsEnhancers:c=[]}=r,d=Y(r.argTypes,e.argTypes,t==null?void 0:t.argTypes);if(t){let F=(t==null?void 0:t.userStoryFn)||(t==null?void 0:t.render)||e.render||r.render;u.__isArgsStory=F&&F.length>0}let m={...r.args,...e.args,...t==null?void 0:t.args},h={...e.globals,...t==null?void 0:t.globals},g={componentId:e.id,title:e.title,kind:e.title,id:(t==null?void 0:t.id)||e.id,name:(t==null?void 0:t.name)||"__meta",story:(t==null?void 0:t.name)||"__meta",component:e.component,subcomponents:e.subcomponents,tags:i,parameters:u,initialArgs:m,argTypes:d,storyGlobals:h};g.argTypes=l.reduce((F,se)=>se({...g,argTypes:F}),g.argTypes);let re={...m};g.initialArgs=c.reduce((F,se)=>({...F,...se({...g,initialArgs:F})}),re);let{name:ne,story:le,...J}=g;return J}n(Li,"preparePartialAnnotations");function _t(t){var i;let{args:e}=t,r={...t,allArgs:void 0,argsByTarget:void 0};if((i=E$1.FEATURES)!=null&&i.argTypeTargetsV7){let u=Ai(t);r={...t,allArgs:t.args,argsByTarget:u,args:u[hn]||{}}}let o=Object.entries(r.args).reduce((u,[l,c])=>{var m;if(!((m=r.argTypes[l])!=null&&m.mapping))return u[l]=c,u;let d=n(h=>{let g=r.argTypes[l].mapping;return g&&h in g?g[h]:h},"mappingFn");return u[l]=Array.isArray(c)?c.map(d):d(c),u},{}),a=Object.entries(o).reduce((u,[l,c])=>{let d=r.argTypes[l]||{};return Rn(d,o,r.globals)&&(u[l]=c),u},{});return{...r,unmappedArgs:e,args:a}}n(_t,"prepareContext");var wn=n((t,e,r)=>{let o=typeof t;switch(o){case"boolean":case"string":case"number":case"function":case"symbol":return{name:o}}return t?r.has(t)?(I$1.warn(_$1`
        We've detected a cycle in arg '${e}'. Args should be JSON-serializable.

        Consider using the mapping feature or fully custom args:
        - Mapping: https://storybook.js.org/docs/writing-stories/args#mapping-to-complex-arg-values
        - Custom args: https://storybook.js.org/docs/essentials/controls#fully-custom-args
      `),{name:"other",value:"cyclic object"}):(r.add(t),Array.isArray(t)?{name:"array",value:t.length>0?wn(t[0],e,new Set(r)):{name:"other",value:"unknown"}}:{name:"object",value:oe(t,a=>wn(a,e,new Set(r)))}):{name:"object",value:{}}},"inferType"),_n=n(t=>{let{id:e,argTypes:r={},initialArgs:o={}}=t,a=oe(o,(u,l)=>({name:l,type:wn(u,`${e}.${l}`,new Set)})),i=oe(r,(u,l)=>({name:l}));return Y(a,i,r)},"inferArgTypes");_n.secondPass=!0;var ji=n((t,e)=>Array.isArray(e)?e.includes(t):t.match(e),"matches"),Mr=n((t,e,r)=>!e&&!r?t:t&&qo(t,(o,a)=>{let i=o.name||a.toString();return!!(!e||ji(i,e))&&(!r||!ji(i,r))}),"filterArgTypes"),Jd=n((t,e,r)=>{let{type:o,options:a}=t;if(o){if(r.color&&r.color.test(e)){let i=o.name;if(i==="string")return{control:{type:"color"}};i!=="enum"&&I$1.warn(`Addon controls: Control of type color only supports string, received "${i}" instead`)}if(r.date&&r.date.test(e))return{control:{type:"date"}};switch(o.name){case"array":return{control:{type:"object"}};case"boolean":return{control:{type:"boolean"}};case"string":return{control:{type:"text"}};case"number":return{control:{type:"number"}};case"enum":{let{value:i}=o;return{control:{type:(i==null?void 0:i.length)<=5?"radio":"select"},options:i}}case"function":case"symbol":return null;default:return{control:{type:a?"select":"object"}}}}},"inferControl"),ir=n(t=>{let{argTypes:e,parameters:{__isArgsStory:r,controls:{include:o=null,exclude:a=null,matchers:i={}}={}}}=t;if(!r)return e;let u=Mr(e,o,a),l=oe(u,(c,d)=>(c==null?void 0:c.type)&&Jd(c,d.toString(),i));return Y(l,u)},"inferControls");ir.secondPass=!0;function Ne({argTypes:t,globalTypes:e,argTypesEnhancers:r,decorators:o,loaders:a,beforeEach:i,experimental_afterEach:u,globals:l,initialGlobals:c,...d}){return l&&Object.keys(l).length>0&&ae(_$1`
      The preview.js 'globals' field is deprecated and will be removed in Storybook 9.0.
      Please use 'initialGlobals' instead. Learn more:

      https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#previewjs-globals-renamed-to-initialglobals
    `),{...t&&{argTypes:Fe(t)},...e&&{globalTypes:Fe(e)},decorators:k(o),loaders:k(a),beforeEach:k(i),experimental_afterEach:k(u),argTypesEnhancers:[...r||[],_n,ir],initialGlobals:Y(c,l),...d}}n(Ne,"normalizeProjectAnnotations");var Mi=n(t=>async()=>{let e=[];for(let r of t){let o=await r();o&&e.unshift(o)}return async()=>{for(let r of e)await r()}},"composeBeforeAllHooks");function Ct(t){return async(e,r,o)=>{await t.reduceRight((a,i)=>async()=>i(e,a,o),async()=>r(o))()}}n(Ct,"composeStepRunners");function Gr(t,e){return t.map(r=>{var o;return((o=r.default)==null?void 0:o[e])??r[e]}).filter(Boolean)}n(Gr,"getField");function Te(t,e,r={}){return Gr(t,e).reduce((o,a)=>{let i=k(a);return r.reverseFileOrder?[...i,...o]:[...o,...i]},[])}n(Te,"getArrayField");function Ur(t,e){return Object.assign({},...Gr(t,e))}n(Ur,"getObjectField");function ar(t,e){return Gr(t,e).pop()}n(ar,"getSingletonField");function ke(t){var a;let e=Te(t,"argTypesEnhancers"),r=Gr(t,"runStep"),o=Te(t,"beforeAll");return{parameters:Y(...Gr(t,"parameters")),decorators:Te(t,"decorators",{reverseFileOrder:!(((a=E$1.FEATURES)==null?void 0:a.legacyDecoratorFileOrder)??!1)}),args:Ur(t,"args"),argsEnhancers:Te(t,"argsEnhancers"),argTypes:Ur(t,"argTypes"),argTypesEnhancers:[...e.filter(i=>!i.secondPass),...e.filter(i=>i.secondPass)],globals:Ur(t,"globals"),initialGlobals:Ur(t,"initialGlobals"),globalTypes:Ur(t,"globalTypes"),loaders:Te(t,"loaders"),beforeAll:Mi(o),beforeEach:Te(t,"beforeEach"),experimental_afterEach:Te(t,"experimental_afterEach"),render:ar(t,"render"),renderToCanvas:ar(t,"renderToCanvas"),renderToDOM:ar(t,"renderToDOM"),applyDecorators:ar(t,"applyDecorators"),runStep:Ct(r),tags:Te(t,"tags"),mount:ar(t,"mount"),testingLibraryRender:ar(t,"testingLibraryRender")}}n(ke,"composeConfigs");var Cn=class{constructor(){this.reports=[]}async addReport(e){this.reports.push(e)}};n(Cn,"ReporterAPI");var Ee=Cn;function Pt(t,e,r){return nr(t)?{story:t.input,meta:t.meta.input,preview:t.meta.preview.composed}:{story:t,meta:e,preview:r}}n(Pt,"getCsfFactoryAnnotations");function Ui(t){globalThis.defaultProjectAnnotations=t}n(Ui,"setDefaultProjectAnnotations");var Qd="ComposedStory",Zd="Unnamed Story";function eu(t){return t?ke([t]):{}}n(eu,"extractAnnotation");function Gi(t){let e=Array.isArray(t)?t:[t];return globalThis.globalProjectAnnotations=ke([globalThis.defaultProjectAnnotations??{},ke(e.map(eu))]),globalThis.globalProjectAnnotations??{}}n(Gi,"setProjectAnnotations");var Re=[];function Pn(t,e,r,o,a){var ce;if(t===void 0)throw new Error("Expected a story but received undefined.");e.title=e.title??Qd;let i=jr(e),u=a||t.storyName||((ce=t.story)==null?void 0:ce.name)||t.name||Zd,l=De(u,t,i),c=Ne(ke([o??globalThis.globalProjectAnnotations??{},r??{}])),d=sr(l,i,c),m={...Et(c.globalTypes),...c.initialGlobals,...d.storyGlobals},h=new Ee,g=n(()=>{let F=_t({hooks:new be,globals:m,args:{...d.initialArgs},viewMode:"story",reporting:h,loaded:{},abortSignal:new AbortController().signal,step:n((se,he)=>d.runStep(se,he,F),"step"),canvasElement:null,canvas:{},globalTypes:c.globalTypes,...d,context:null,mount:null});return F.parameters.__isPortableStory=!0,F.context=F,d.renderToCanvas&&(F.renderToCanvas=async()=>{var he;let se=await((he=d.renderToCanvas)==null?void 0:he.call(d,{componentId:d.componentId,title:d.title,id:d.id,name:d.name,tags:d.tags,showMain:n(()=>{},"showMain"),showError:n(Ve=>{throw new Error(`${Ve.title}
${Ve.description}`)},"showError"),showException:n(Ve=>{throw Ve},"showException"),forceRemount:!0,storyContext:F,storyFn:n(()=>d.unboundStoryFn(F),"storyFn"),unboundStoryFn:d.unboundStoryFn},F.canvasElement));se&&Re.push(se)}),F.mount=d.mount(F),F},"initializeContext"),re,ne=n(async F=>{var he;let se=g();return se.canvasElement??(se.canvasElement=(he=globalThis==null?void 0:globalThis.document)==null?void 0:he.body),re&&(se.loaded=re.loaded),Object.assign(se,F),d.playFunction(se)},"play"),le=n(F=>{let se=g();return Object.assign(se,F),tu(d,se)},"run"),J=d.playFunction?ne:void 0;return Object.assign(n(function(F){let se=g();return re&&(se.loaded=re.loaded),se.args={...se.initialArgs,...F},d.unboundStoryFn(se)},"storyFn"),{id:d.id,storyName:u,load:n(async()=>{for(let se of[...Re].reverse())await se();Re.length=0;let F=g();F.loaded=await d.applyLoaders(F),Re.push(...(await d.applyBeforeEach(F)).filter(Boolean)),re=F},"load"),globals:m,args:d.initialArgs,parameters:d.parameters,argTypes:d.argTypes,play:J,run:le,reporting:h,tags:d.tags})}n(Pn,"composeStory");var ru=n((t,e,r,o)=>Pn(t,e,r,{},o),"defaultComposeStory");function qi(t,e,r=ru){let{default:o,__esModule:a,__namedExportsOrder:i,...u}=t,l=o;return Object.entries(u).reduce((c,[d,m])=>{let{story:h,meta:g}=Pt(m);return!l&&g&&(l=g),Lr(d,l)?Object.assign(c,{[d]:r(h,l,e,d)}):c},{})}n(qi,"composeStories");function Bi(t){return t.extend({mount:n(async({mount:e,page:r},o)=>{await o(async(a,...i)=>{if(!("__pw_type"in a)||"__pw_type"in a&&a.__pw_type!=="jsx")throw new Error(_$1`
              Portable stories in Playwright CT only work when referencing JSX elements.
              Please use JSX format for your components such as:

              instead of:
              await mount(MyComponent, { props: { foo: 'bar' } })

              do:
              await mount(<MyComponent foo="bar"/>)

              More info: https://storybook.js.org/docs/api/portable-stories-playwright
            `);await r.evaluate(async l=>{var d,m,h;let c=await((d=globalThis.__pwUnwrapObject)==null?void 0:d.call(globalThis,l));return(h=(m="__pw_type"in c?c.type:c)==null?void 0:m.load)==null?void 0:h.call(m)},a);let u=await e(a,...i);return await r.evaluate(async l=>{var h,g;let c=await((h=globalThis.__pwUnwrapObject)==null?void 0:h.call(globalThis,l)),d="__pw_type"in c?c.type:c,m=document.querySelector("#root");return(g=d==null?void 0:d.play)==null?void 0:g.call(d,{canvasElement:m})},a),u})},"mount")})}n(Bi,"createPlaywrightTest");async function tu(t,e){var a,i;for(let u of[...Re].reverse())await u();if(Re.length=0,!e.canvasElement){let u=document.createElement("div");(i=(a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body)==null||i.appendChild(u),e.canvasElement=u,Re.push(()=>{var l,c,d,m;(c=(l=globalThis==null?void 0:globalThis.document)==null?void 0:l.body)!=null&&c.contains(u)&&((m=(d=globalThis==null?void 0:globalThis.document)==null?void 0:d.body)==null||m.removeChild(u))})}if(e.loaded=await t.applyLoaders(e),e.abortSignal.aborted)return;Re.push(...(await t.applyBeforeEach(e)).filter(Boolean));let r=t.playFunction,o=t.usesMount;o||await e.mount(),!e.abortSignal.aborted&&(r&&(o||(e.mount=async()=>{throw new Oe({playFunction:r.toString()})}),await r(e)),await t.applyAfterEach(e))}n(tu,"runStory");function Vi(t,e){return Uo(Go(t,e),r=>r===void 0)}n(Vi,"picky");var Hi=1e3,ou=1e4,On=class{constructor(e,r,o){this.importFn=r,this.getStoriesJsonData=n(()=>{let u=this.getSetStoriesPayload(),l=["fileName","docsOnly","framework","__id","__isArgsStory"];return{v:3,stories:oe(u.stories,c=>{let{importPath:d}=this.storyIndex.entries[c.id];return{...Vi(c,["id","name","title"]),importPath:d,kind:c.title,story:c.name,parameters:{...Vi(c.parameters,l),fileName:d}}})}},"getStoriesJsonData"),this.storyIndex=new At(e),this.projectAnnotations=Ne(o);let{initialGlobals:a,globalTypes:i}=this.projectAnnotations;this.args=new Tt,this.userGlobals=new Rt({globals:a,globalTypes:i}),this.hooks={},this.cleanupCallbacks={},this.processCSFFileWithCache=(0,Ot.default)(Hi)(Di),this.prepareMetaWithCache=(0,Ot.default)(Hi)(wt),this.prepareStoryWithCache=(0,Ot.default)(ou)(sr)}setProjectAnnotations(e){this.projectAnnotations=Ne(e);let{initialGlobals:r,globalTypes:o}=e;this.userGlobals.set({globals:r,globalTypes:o})}async onStoriesChanged({importFn:e,storyIndex:r}){e&&(this.importFn=e),r&&(this.storyIndex.entries=r.entries),this.cachedCSFFiles&&await this.cacheAllCSFFiles()}async storyIdToEntry(e){return this.storyIndex.storyIdToEntry(e)}async loadCSFFileByStoryId(e){let{importPath:r,title:o}=this.storyIndex.storyIdToEntry(e),a=await this.importFn(r);return this.processCSFFileWithCache(a,r,o)}async loadAllCSFFiles(){let e={};return Object.entries(this.storyIndex.entries).forEach(([r,{importPath:o}])=>{e[o]=r}),(await Promise.all(Object.entries(e).map(async([r,o])=>({importPath:r,csfFile:await this.loadCSFFileByStoryId(o)})))).reduce((r,{importPath:o,csfFile:a})=>(r[o]=a,r),{})}async cacheAllCSFFiles(){this.cachedCSFFiles=await this.loadAllCSFFiles()}preparedMetaFromCSFFile({csfFile:e}){let r=e.meta;return this.prepareMetaWithCache(r,this.projectAnnotations,e.moduleExports.default)}async loadStory({storyId:e}){let r=await this.loadCSFFileByStoryId(e);return this.storyFromCSFFile({storyId:e,csfFile:r})}storyFromCSFFile({storyId:e,csfFile:r}){let o=r.stories[e];if(!o)throw new Ir({storyId:e});let a=r.meta,i=this.prepareStoryWithCache(o,a,r.projectAnnotations??this.projectAnnotations);return this.args.setInitial(i),this.hooks[i.id]=this.hooks[i.id]||new be,i}componentStoriesFromCSFFile({csfFile:e}){return Object.keys(this.storyIndex.entries).filter(r=>!!e.stories[r]).map(r=>this.storyFromCSFFile({storyId:r,csfFile:e}))}async loadEntry(e){let r=await this.storyIdToEntry(e),o=r.type==="docs"?r.storiesImports:[],[a,...i]=await Promise.all([this.importFn(r.importPath),...o.map(u=>{let l=this.storyIndex.importPathToEntry(u);return this.loadCSFFileByStoryId(l.id)})]);return{entryExports:a,csfFiles:i}}getStoryContext(e,{forceInitialArgs:r=!1}={}){let o=this.userGlobals.get(),{initialGlobals:a}=this.userGlobals,i=new Ee;return _t({...e,args:r?e.initialArgs:this.args.get(e.id),initialGlobals:a,globalTypes:this.projectAnnotations.globalTypes,userGlobals:o,reporting:i,globals:{...o,...e.storyGlobals},hooks:this.hooks[e.id]})}addCleanupCallbacks(e,r){this.cleanupCallbacks[e.id]=r}async cleanupStory(e){this.hooks[e.id].clean();let r=this.cleanupCallbacks[e.id];if(r)for(let o of[...r].reverse())await o();delete this.cleanupCallbacks[e.id]}extract(e={includeDocsOnly:!1}){let{cachedCSFFiles:r}=this;if(!r)throw new vr;return Object.entries(this.storyIndex.entries).reduce((o,[a,{type:i,importPath:u}])=>{if(i==="docs")return o;let l=r[u],c=this.storyFromCSFFile({storyId:a,csfFile:l});return!e.includeDocsOnly&&c.parameters.docsOnly||(o[a]=Object.entries(c).reduce((d,[m,h])=>m==="moduleExport"||typeof h=="function"?d:Array.isArray(h)?Object.assign(d,{[m]:h.slice().sort()}):Object.assign(d,{[m]:h}),{args:c.initialArgs,globals:{...this.userGlobals.initialGlobals,...this.userGlobals.globals,...c.storyGlobals}})),o},{})}getSetStoriesPayload(){let e=this.extract({includeDocsOnly:!0}),r=Object.values(e).reduce((o,{title:a})=>(o[a]={},o),{});return{v:2,globals:this.userGlobals.get(),globalParameters:{},kindParameters:r,stories:e}}raw(){return ae("StoryStore.raw() is deprecated and will be removed in 9.0, please use extract() instead"),Object.values(this.extract()).map(({id:e})=>this.fromId(e)).filter(Boolean)}fromId(e){if(ae("StoryStore.fromId() is deprecated and will be removed in 9.0, please use loadStory() instead"),!this.cachedCSFFiles)throw new Error("Cannot call fromId/raw() unless you call cacheAllCSFFiles() first.");let r;try{({importPath:r}=this.storyIndex.storyIdToEntry(e))}catch{return null}let o=this.cachedCSFFiles[r],a=this.storyFromCSFFile({storyId:e,csfFile:o});return{...a,storyFn:n(i=>{let u={...this.getStoryContext(a),abortSignal:new AbortController().signal,canvasElement:null,loaded:{},step:n((l,c)=>a.runStep(l,c,u),"step"),context:null,mount:null,canvas:{},viewMode:"story"};return a.unboundStoryFn({...u,...i})},"storyFn")}}};n(On,"StoryStore");var Le=On;function In(t){return t.startsWith("\\\\?\\")?t:t.replace(/\\/g,"/")}n(In,"slash");var nu=n(t=>{if(t.length===0)return t;let e=t[t.length-1],r=e==null?void 0:e.replace(/(?:[.](?:story|stories))?([.][^.]+)$/i,"");if(t.length===1)return[r];let o=t[t.length-2];return r&&o&&r.toLowerCase()===o.toLowerCase()?[...t.slice(0,-2),r]:r&&(/^(story|stories)([.][^.]+)$/i.test(e)||/^index$/i.test(r))?t.slice(0,-1):[...t.slice(0,-1),r]},"sanitize");function zi(t){return t.flatMap(e=>e.split("/")).filter(Boolean).join("/")}n(zi,"pathJoin");var Fn=n((t,e,r)=>{let{directory:o,importPathMatcher:a,titlePrefix:i=""}=e||{};typeof t=="number"&&j$1.warn(_$1`
      CSF Auto-title received a numeric fileName. This typically happens when
      webpack is mis-configured in production mode. To force webpack to produce
      filenames, set optimization.moduleIds = "named" in your webpack config.
    `);let u=In(String(t));if(a.exec(u)){if(!r){let l=u.replace(o,""),c=zi([i,l]).split("/");return c=nu(c),c.join("/")}return i?zi([i,r]):r}},"userOrAutoTitleFromSpecifier"),Wi=n((t,e,r)=>{for(let o=0;o<e.length;o+=1){let a=Fn(t,e[o],r);if(a)return a}return r||void 0},"userOrAutoTitle"),$i=/\s*\/\s*/,Yi=n((t={})=>(e,r)=>{if(e.title===r.title&&!t.includeNames)return 0;let o=t.method||"configure",a=t.order||[],i=e.title.trim().split($i),u=r.title.trim().split($i);t.includeNames&&(i.push(e.name),u.push(r.name));let l=0;for(;i[l]||u[l];){if(!i[l])return-1;if(!u[l])return 1;let c=i[l],d=u[l];if(c!==d){let h=a.indexOf(c),g=a.indexOf(d),re=a.indexOf("*");return h!==-1||g!==-1?(h===-1&&(re!==-1?h=re:h=a.length),g===-1&&(re!==-1?g=re:g=a.length),h-g):o==="configure"?0:c.localeCompare(d,t.locales?t.locales:void 0,{numeric:!0,sensitivity:"accent"})}let m=a.indexOf(c);m===-1&&(m=a.indexOf("*")),a=m!==-1&&Array.isArray(a[m+1])?a[m+1]:[],l+=1}return 0},"storySort"),su=n((t,e,r)=>{if(e){let o;typeof e=="function"?o=e:o=Yi(e),t.sort(o)}else t.sort((o,a)=>r.indexOf(o.importPath)-r.indexOf(a.importPath));return t},"sortStoriesCommon"),Ki=n((t,e,r)=>{try{return su(t,e,r)}catch(o){throw new Error(_$1`
    Error sorting stories with sort parameter ${e}:

    > ${o.message}
    
    Are you using a V6-style sort function in V7 mode?

    More info: https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#v7-style-story-sort
  `)}},"sortStoriesV7"),Ae=new Error("prepareAborted"),{AbortController:Xi}=globalThis;function Ji(t){try{let{name:e="Error",message:r=String(t),stack:o}=t;return{name:e,message:r,stack:o}}catch{return{name:"Error",message:String(t)}}}n(Ji,"serializeError");var Dn=class{constructor(e,r,o,a,i,u,l={autoplay:!0,forceInitialArgs:!1},c){this.channel=e,this.store=r,this.renderToScreen=o,this.callbacks=a,this.id=i,this.viewMode=u,this.renderOptions=l,this.type="story",this.notYetRendered=!0,this.rerenderEnqueued=!1,this.disableKeyListeners=!1,this.teardownRender=n(()=>{},"teardownRender"),this.torndown=!1,this.abortController=new Xi,c&&(this.story=c,this.phase="preparing")}async runPhase(e,r,o){this.phase=r,this.channel.emit(Pe,{newPhase:this.phase,storyId:this.id}),o&&(await o(),this.checkIfAborted(e))}checkIfAborted(e){return e.aborted?(this.phase="aborted",this.channel.emit(Pe,{newPhase:this.phase,storyId:this.id}),!0):!1}async prepare(){if(await this.runPhase(this.abortController.signal,"preparing",async()=>{this.story=await this.store.loadStory({storyId:this.id})}),this.abortController.signal.aborted)throw await this.store.cleanupStory(this.story),Ae}isEqual(e){return!!(this.id===e.id&&this.story&&this.story===e.story)}isPreparing(){return["preparing"].includes(this.phase)}isPending(){return["loading","beforeEach","rendering","playing","afterEach"].includes(this.phase)}async renderToElement(e){return this.canvasElement=e,this.render({initial:!0,forceRemount:!0})}storyContext(){if(!this.story)throw new Error("Cannot call storyContext before preparing");let{forceInitialArgs:e}=this.renderOptions;return this.store.getStoryContext(this.story,{forceInitialArgs:e})}async render({initial:e=!1,forceRemount:r=!1}={}){var se,he,Ve,ve;let{canvasElement:o}=this;if(!this.story)throw new Error("cannot render when not prepared");let a=this.story;if(!o)throw new Error("cannot render when canvasElement is unset");let{id:i,componentId:u,title:l,name:c,tags:d,applyLoaders:m,applyBeforeEach:h,applyAfterEach:g,unboundStoryFn:re,playFunction:ne,runStep:le}=a;r&&!e&&(this.cancelRender(),this.abortController=new Xi);let J=this.abortController.signal,ce=!1,F=a.usesMount;try{let we={...this.storyContext(),viewMode:this.viewMode,abortSignal:J,canvasElement:o,loaded:{},step:n((B,de)=>le(B,de,we),"step"),context:null,canvas:{},renderToCanvas:n(async()=>{let B=await this.renderToScreen(Nt,o);this.teardownRender=B||(()=>{}),ce=!0},"renderToCanvas"),mount:n(async(...B)=>{var pe,Lt;(Lt=(pe=this.callbacks).showStoryDuringRender)==null||Lt.call(pe);let de=null;return await this.runPhase(J,"rendering",async()=>{de=await a.mount(we)(...B)}),F&&await this.runPhase(J,"playing"),de},"mount")};we.context=we;let Nt={componentId:u,title:l,kind:l,id:i,name:c,story:c,tags:d,...this.callbacks,showError:n(B=>(this.phase="errored",this.callbacks.showError(B)),"showError"),showException:n(B=>(this.phase="errored",this.callbacks.showException(B)),"showException"),forceRemount:r||this.notYetRendered,storyContext:we,storyFn:n(()=>re(we),"storyFn"),unboundStoryFn:re};if(await this.runPhase(J,"loading",async()=>{we.loaded=await m(we)}),J.aborted)return;let Bt=await h(we);if(this.store.addCleanupCallbacks(a,Bt),this.checkIfAborted(J)||(!ce&&!F&&await we.mount(),this.notYetRendered=!1,J.aborted))return;let Ft=((he=(se=this.story.parameters)==null?void 0:se.test)==null?void 0:he.dangerouslyIgnoreUnhandledErrors)===!0,jt=new Set,qe=n(B=>jt.add("error"in B?B.error:B.reason),"onError");if(this.renderOptions.autoplay&&r&&ne&&this.phase!=="errored"){window.addEventListener("error",qe),window.addEventListener("unhandledrejection",qe),this.disableKeyListeners=!0;try{if(F?await ne(we):(we.mount=async()=>{throw new Oe({playFunction:ne.toString()})},await this.runPhase(J,"playing",async()=>ne(we))),!ce)throw new Nr;this.checkIfAborted(J),!Ft&&jt.size>0?await this.runPhase(J,"errored"):await this.runPhase(J,"played")}catch(B){if((ve=(Ve=this.callbacks).showStoryDuringRender)==null||ve.call(Ve),await this.runPhase(J,"errored",async()=>{this.channel.emit(Xt,Ji(B))}),this.story.parameters.throwPlayFunctionExceptions!==!1)throw B;console.error(B)}if(!Ft&&jt.size>0&&this.channel.emit(Jt,Array.from(jt).map(Ji)),this.disableKeyListeners=!1,window.removeEventListener("unhandledrejection",qe),window.removeEventListener("error",qe),J.aborted)return}await this.runPhase(J,"completed",async()=>this.channel.emit(We,i)),this.phase!=="errored"&&await this.runPhase(J,"afterEach",async()=>{await g(we)});let Dt=!Ft&&jt.size>0,p=we.reporting.reports.some(B=>B.status==="failed"),A=Dt||p;await this.runPhase(J,"finished",async()=>this.channel.emit(ot,{storyId:i,status:A?"error":"success",reporters:we.reporting.reports}))}catch(we){this.phase="errored",this.callbacks.showException(we),await this.runPhase(J,"finished",async()=>this.channel.emit(ot,{storyId:i,status:"error",reporters:[]}))}this.rerenderEnqueued&&(this.rerenderEnqueued=!1,this.render())}async rerender(){if(this.isPending()&&this.phase!=="playing")this.rerenderEnqueued=!0;else return this.render()}async remount(){return await this.teardown(),this.render({forceRemount:!0})}cancelRender(){var e;(e=this.abortController)==null||e.abort()}async teardown(){this.torndown=!0,this.cancelRender(),this.story&&await this.store.cleanupStory(this.story);for(let e=0;e<3;e+=1){if(!this.isPending()){await this.teardownRender();return}await new Promise(r=>setTimeout(r,0))}window.location.reload(),await new Promise(()=>{})}};n(Dn,"StoryRender");var je=Dn,{fetch:iu}=E$1,au="./index.json",Nn=class{constructor(e,r,o=te$1.getChannel(),a=!0){this.importFn=e,this.getProjectAnnotations=r,this.channel=o,this.storyRenders=[],this.storeInitializationPromise=new Promise((i,u)=>{this.resolveStoreInitializationPromise=i,this.rejectStoreInitializationPromise=u}),a&&this.initialize()}get storyStore(){return new Proxy({},{get:n((e,r)=>{if(this.storyStoreValue)return ae("Accessing the Story Store is deprecated and will be removed in 9.0"),this.storyStoreValue[r];throw new Fr},"get")})}async initialize(){this.setupListeners();try{let e=await this.getProjectAnnotationsOrRenderError();await this.runBeforeAllHook(e),await this.initializeWithProjectAnnotations(e)}catch(e){this.rejectStoreInitializationPromise(e)}}ready(){return this.storeInitializationPromise}setupListeners(){this.channel.on(so,this.onStoryIndexChanged.bind(this)),this.channel.on(fr,this.onUpdateGlobals.bind(this)),this.channel.on(yr,this.onUpdateArgs.bind(this)),this.channel.on(fo,this.onRequestArgTypesInfo.bind(this)),this.channel.on(ur,this.onResetArgs.bind(this)),this.channel.on(dr,this.onForceReRender.bind(this)),this.channel.on(Kt,this.onForceRemount.bind(this))}async getProjectAnnotationsOrRenderError(){try{let e=await this.getProjectAnnotations();if(this.renderToCanvas=e.renderToCanvas,!this.renderToCanvas)throw new wr;return e}catch(e){throw this.renderPreviewEntryError("Error reading preview.js:",e),e}}async initializeWithProjectAnnotations(e){this.projectAnnotationsBeforeInitialization=e;try{let r=await this.getStoryIndexFromServer();return this.initializeWithStoryIndex(r)}catch(r){throw this.renderPreviewEntryError("Error loading story index:",r),r}}async runBeforeAllHook(e){var r,o;try{await((r=this.beforeAllCleanup)==null?void 0:r.call(this)),this.beforeAllCleanup=await((o=e.beforeAll)==null?void 0:o.call(e))}catch(a){throw this.renderPreviewEntryError("Error in beforeAll hook:",a),a}}async getStoryIndexFromServer(){let e=await iu(au);if(e.status===200)return e.json();throw new _r({text:await e.text()})}initializeWithStoryIndex(e){if(!this.projectAnnotationsBeforeInitialization)throw new Error("Cannot call initializeWithStoryIndex until project annotations resolve");this.storyStoreValue=new Le(e,this.importFn,this.projectAnnotationsBeforeInitialization),delete this.projectAnnotationsBeforeInitialization,this.setInitialGlobals(),this.resolveStoreInitializationPromise()}async setInitialGlobals(){this.emitGlobals()}emitGlobals(){if(!this.storyStoreValue)throw new V({methodName:"emitGlobals"});let e={globals:this.storyStoreValue.userGlobals.get()||{},globalTypes:this.storyStoreValue.projectAnnotations.globalTypes||{}};this.channel.emit(ro,e)}async onGetProjectAnnotationsChanged({getProjectAnnotations:e}){delete this.previewEntryError,this.getProjectAnnotations=e;let r=await this.getProjectAnnotationsOrRenderError();if(await this.runBeforeAllHook(r),!this.storyStoreValue){await this.initializeWithProjectAnnotations(r);return}this.storyStoreValue.setProjectAnnotations(r),this.emitGlobals()}async onStoryIndexChanged(){if(delete this.previewEntryError,!(!this.storyStoreValue&&!this.projectAnnotationsBeforeInitialization))try{let e=await this.getStoryIndexFromServer();if(this.projectAnnotationsBeforeInitialization){this.initializeWithStoryIndex(e);return}await this.onStoriesChanged({storyIndex:e})}catch(e){throw this.renderPreviewEntryError("Error loading story index:",e),e}}async onStoriesChanged({importFn:e,storyIndex:r}){if(!this.storyStoreValue)throw new V({methodName:"onStoriesChanged"});await this.storyStoreValue.onStoriesChanged({importFn:e,storyIndex:r})}async onUpdateGlobals({globals:e,currentStory:r}){if(this.storyStoreValue||await this.storeInitializationPromise,!this.storyStoreValue)throw new V({methodName:"onUpdateGlobals"});if(this.storyStoreValue.userGlobals.update(e),r){let{initialGlobals:o,storyGlobals:a,userGlobals:i,globals:u}=this.storyStoreValue.getStoryContext(r);this.channel.emit(Ce,{initialGlobals:o,userGlobals:i,storyGlobals:a,globals:u})}else{let{initialGlobals:o,globals:a}=this.storyStoreValue.userGlobals;this.channel.emit(Ce,{initialGlobals:o,userGlobals:a,storyGlobals:{},globals:a})}await Promise.all(this.storyRenders.map(o=>o.rerender()))}async onUpdateArgs({storyId:e,updatedArgs:r}){if(!this.storyStoreValue)throw new V({methodName:"onUpdateArgs"});this.storyStoreValue.args.update(e,r),await Promise.all(this.storyRenders.filter(o=>o.id===e&&!o.renderOptions.forceInitialArgs).map(o=>o.story&&o.story.usesMount?o.remount():o.rerender())),this.channel.emit(to,{storyId:e,args:this.storyStoreValue.args.get(e)})}async onRequestArgTypesInfo({id:e,payload:r}){var o;try{await this.storeInitializationPromise;let a=await((o=this.storyStoreValue)==null?void 0:o.loadStory(r));this.channel.emit(nt,{id:e,success:!0,payload:{argTypes:(a==null?void 0:a.argTypes)||{}},error:null})}catch(a){this.channel.emit(nt,{id:e,success:!1,error:a==null?void 0:a.message})}}async onResetArgs({storyId:e,argNames:r}){var i;if(!this.storyStoreValue)throw new V({methodName:"onResetArgs"});let o=((i=this.storyRenders.find(u=>u.id===e))==null?void 0:i.story)||await this.storyStoreValue.loadStory({storyId:e}),a=(r||[...new Set([...Object.keys(o.initialArgs),...Object.keys(this.storyStoreValue.args.get(e))])]).reduce((u,l)=>(u[l]=o.initialArgs[l],u),{});await this.onUpdateArgs({storyId:e,updatedArgs:a})}async onForceReRender(){await Promise.all(this.storyRenders.map(e=>e.rerender()))}async onForceRemount({storyId:e}){await Promise.all(this.storyRenders.filter(r=>r.id===e).map(r=>r.remount()))}renderStoryToElement(e,r,o,a){if(!this.renderToCanvas||!this.storyStoreValue)throw new V({methodName:"renderStoryToElement"});let i=new je(this.channel,this.storyStoreValue,this.renderToCanvas,o,e.id,"docs",a,e);return i.renderToElement(r),this.storyRenders.push(i),async()=>{await this.teardownRender(i)}}async teardownRender(e,{viewModeChanged:r}={}){var o;this.storyRenders=this.storyRenders.filter(a=>a!==e),await((o=e==null?void 0:e.teardown)==null?void 0:o.call(e,{viewModeChanged:r}))}async loadStory({storyId:e}){if(!this.storyStoreValue)throw new V({methodName:"loadStory"});return this.storyStoreValue.loadStory({storyId:e})}getStoryContext(e,{forceInitialArgs:r=!1}={}){if(!this.storyStoreValue)throw new V({methodName:"getStoryContext"});return this.storyStoreValue.getStoryContext(e,{forceInitialArgs:r})}async extract(e){if(!this.storyStoreValue)throw new V({methodName:"extract"});if(this.previewEntryError)throw this.previewEntryError;return await this.storyStoreValue.cacheAllCSFFiles(),this.storyStoreValue.extract(e)}renderPreviewEntryError(e,r){this.previewEntryError=r,I$1.error(e),I$1.error(r),this.channel.emit($t,r)}};n(Nn,"Preview");var Me=Nn,kn=class{constructor(e,r,o,a){this.channel=e,this.store=r,this.renderStoryToElement=o,this.storyIdByName=n(i=>{let u=this.nameToStoryId.get(i);if(u)return u;throw new Error(`No story found with that name: ${i}`)},"storyIdByName"),this.componentStories=n(()=>this.componentStoriesValue,"componentStories"),this.componentStoriesFromCSFFile=n(i=>this.store.componentStoriesFromCSFFile({csfFile:i}),"componentStoriesFromCSFFile"),this.storyById=n(i=>{if(!i){if(!this.primaryStory)throw new Error("No primary story defined for docs entry. Did you forget to use `<Meta>`?");return this.primaryStory}let u=this.storyIdToCSFFile.get(i);if(!u)throw new Error(`Called \`storyById\` for story that was never loaded: ${i}`);return this.store.storyFromCSFFile({storyId:i,csfFile:u})},"storyById"),this.getStoryContext=n(i=>({...this.store.getStoryContext(i),loaded:{},viewMode:"docs"}),"getStoryContext"),this.loadStory=n(i=>this.store.loadStory({storyId:i}),"loadStory"),this.componentStoriesValue=[],this.storyIdToCSFFile=new Map,this.exportToStory=new Map,this.exportsToCSFFile=new Map,this.nameToStoryId=new Map,this.attachedCSFFiles=new Set,a.forEach((i,u)=>{this.referenceCSFFile(i)})}referenceCSFFile(e){this.exportsToCSFFile.set(e.moduleExports,e),this.exportsToCSFFile.set(e.moduleExports.default,e),this.store.componentStoriesFromCSFFile({csfFile:e}).forEach(r=>{let o=e.stories[r.id];this.storyIdToCSFFile.set(o.id,e),this.exportToStory.set(o.moduleExport,r)})}attachCSFFile(e){if(!this.exportsToCSFFile.has(e.moduleExports))throw new Error("Cannot attach a CSF file that has not been referenced");this.attachedCSFFiles.has(e)||(this.attachedCSFFiles.add(e),this.store.componentStoriesFromCSFFile({csfFile:e}).forEach(r=>{this.nameToStoryId.set(r.name,r.id),this.componentStoriesValue.push(r),this.primaryStory||(this.primaryStory=r)}))}referenceMeta(e,r){let o=this.resolveModuleExport(e);if(o.type!=="meta")throw new Error("<Meta of={} /> must reference a CSF file module export or meta export. Did you mistakenly reference your component instead of your CSF file?");r&&this.attachCSFFile(o.csfFile)}get projectAnnotations(){let{projectAnnotations:e}=this.store;if(!e)throw new Error("Can't get projectAnnotations from DocsContext before they are initialized");return e}resolveAttachedModuleExportType(e){if(e==="story"){if(!this.primaryStory)throw new Error("No primary story attached to this docs file, did you forget to use <Meta of={} />?");return{type:"story",story:this.primaryStory}}if(this.attachedCSFFiles.size===0)throw new Error("No CSF file attached to this docs file, did you forget to use <Meta of={} />?");let r=Array.from(this.attachedCSFFiles)[0];if(e==="meta")return{type:"meta",csfFile:r};let{component:o}=r.meta;if(!o)throw new Error("Attached CSF file does not defined a component, did you forget to export one?");return{type:"component",component:o}}resolveModuleExport(e){let r=this.exportsToCSFFile.get(e);if(r)return{type:"meta",csfFile:r};let o=this.exportToStory.get(nr(e)?e.input:e);return o?{type:"story",story:o}:{type:"component",component:e}}resolveOf(e,r=[]){let o;if(["component","meta","story"].includes(e)){let a=e;o=this.resolveAttachedModuleExportType(a)}else o=this.resolveModuleExport(e);if(r.length&&!r.includes(o.type)){let a=o.type==="component"?"component or unknown":o.type;throw new Error(_$1`Invalid value passed to the 'of' prop. The value was resolved to a '${a}' type but the only types for this block are: ${r.join(", ")}.
        - Did you pass a component to the 'of' prop when the block only supports a story or a meta?
        - ... or vice versa?
        - Did you pass a story, CSF file or meta to the 'of' prop that is not indexed, ie. is not targeted by the 'stories' globs in the main configuration?`)}switch(o.type){case"component":return{...o,projectAnnotations:this.projectAnnotations};case"meta":return{...o,preparedMeta:this.store.preparedMetaFromCSFFile({csfFile:o.csfFile})};case"story":default:return o}}};n(kn,"DocsContext");var me=kn,Ln=class{constructor(e,r,o,a){this.channel=e,this.store=r,this.entry=o,this.callbacks=a,this.type="docs",this.subtype="csf",this.torndown=!1,this.disableKeyListeners=!1,this.preparing=!1,this.id=o.id}isPreparing(){return this.preparing}async prepare(){this.preparing=!0;let{entryExports:e,csfFiles:r=[]}=await this.store.loadEntry(this.id);if(this.torndown)throw Ae;let{importPath:o,title:a}=this.entry,i=this.store.processCSFFileWithCache(e,o,a),u=Object.keys(i.stories)[0];this.story=this.store.storyFromCSFFile({storyId:u,csfFile:i}),this.csfFiles=[i,...r],this.preparing=!1}isEqual(e){return!!(this.id===e.id&&this.story&&this.story===e.story)}docsContext(e){if(!this.csfFiles)throw new Error("Cannot render docs before preparing");let r=new me(this.channel,this.store,e,this.csfFiles);return this.csfFiles.forEach(o=>r.attachCSFFile(o)),r}async renderToElement(e,r){if(!this.story||!this.csfFiles)throw new Error("Cannot render docs before preparing");let o=this.docsContext(r),{docs:a}=this.story.parameters||{};if(!a)throw new Error("Cannot render a story in viewMode=docs if `@storybook/addon-docs` is not installed");let i=await a.renderer(),{render:u}=i,l=n(async()=>{try{await u(o,a,e),this.channel.emit(pr,this.id)}catch(c){this.callbacks.showException(c)}},"renderDocs");return this.rerender=async()=>l(),this.teardownRender=async({viewModeChanged:c})=>{!c||!e||i.unmount(e)},l()}async teardown({viewModeChanged:e}={}){var r;(r=this.teardownRender)==null||r.call(this,{viewModeChanged:e}),this.torndown=!0}};n(Ln,"CsfDocsRender");var qr=Ln,jn=class{constructor(e,r,o,a){this.channel=e,this.store=r,this.entry=o,this.callbacks=a,this.type="docs",this.subtype="mdx",this.torndown=!1,this.disableKeyListeners=!1,this.preparing=!1,this.id=o.id}isPreparing(){return this.preparing}async prepare(){this.preparing=!0;let{entryExports:e,csfFiles:r=[]}=await this.store.loadEntry(this.id);if(this.torndown)throw Ae;this.csfFiles=r,this.exports=e,this.preparing=!1}isEqual(e){return!!(this.id===e.id&&this.exports&&this.exports===e.exports)}docsContext(e){if(!this.csfFiles)throw new Error("Cannot render docs before preparing");return new me(this.channel,this.store,e,this.csfFiles)}async renderToElement(e,r){if(!this.exports||!this.csfFiles||!this.store.projectAnnotations)throw new Error("Cannot render docs before preparing");let o=this.docsContext(r),{docs:a}=this.store.projectAnnotations.parameters||{};if(!a)throw new Error("Cannot render a story in viewMode=docs if `@storybook/addon-docs` is not installed");let i={...a,page:this.exports.default},u=await a.renderer(),{render:l}=u,c=n(async()=>{try{await l(o,i,e),this.channel.emit(pr,this.id)}catch(d){this.callbacks.showException(d)}},"renderDocs");return this.rerender=async()=>c(),this.teardownRender=async({viewModeChanged:d}={})=>{!d||!e||(u.unmount(e),this.torndown=!0)},c()}async teardown({viewModeChanged:e}={}){var r;(r=this.teardownRender)==null||r.call(this,{viewModeChanged:e}),this.torndown=!0}};n(jn,"MdxDocsRender");var Br=jn,lu=globalThis;function cu(t){let e=t.composedPath&&t.composedPath()[0]||t.target;return/input|textarea/i.test(e.tagName)||e.getAttribute("contenteditable")!==null}n(cu,"focusInInput");var Qi="attached-mdx",pu="unattached-mdx";function du({tags:t}){return(t==null?void 0:t.includes(pu))||(t==null?void 0:t.includes(Qi))}n(du,"isMdxEntry");function Mn(t){return t.type==="story"}n(Mn,"isStoryRender");function uu(t){return t.type==="docs"}n(uu,"isDocsRender");function fu(t){return uu(t)&&t.subtype==="csf"}n(fu,"isCsfDocsRender");var Un=class extends Me{constructor(e,r,o,a){super(e,r,void 0,!1),this.importFn=e,this.getProjectAnnotations=r,this.selectionStore=o,this.view=a,this.initialize()}setupListeners(){super.setupListeners(),lu.onkeydown=this.onKeydown.bind(this),this.channel.on(eo,this.onSetCurrentStory.bind(this)),this.channel.on(po,this.onUpdateQueryParams.bind(this)),this.channel.on(Qt,this.onPreloadStories.bind(this))}async setInitialGlobals(){if(!this.storyStoreValue)throw new V({methodName:"setInitialGlobals"});let{globals:e}=this.selectionStore.selectionSpecifier||{};e&&this.storyStoreValue.userGlobals.updateFromPersisted(e),this.emitGlobals()}async initializeWithStoryIndex(e){return await super.initializeWithStoryIndex(e),this.selectSpecifiedStory()}async selectSpecifiedStory(){if(!this.storyStoreValue)throw new V({methodName:"selectSpecifiedStory"});if(this.selectionStore.selection){await this.renderSelection();return}if(!this.selectionStore.selectionSpecifier){this.renderMissingStory();return}let{storySpecifier:e,args:r}=this.selectionStore.selectionSpecifier,o=this.storyStoreValue.storyIndex.entryFromSpecifier(e);if(!o){e==="*"?this.renderStoryLoadingException(e,new Pr):this.renderStoryLoadingException(e,new Or({storySpecifier:e.toString()}));return}let{id:a,type:i}=o;this.selectionStore.setSelection({storyId:a,viewMode:i}),this.channel.emit(ao,this.selectionStore.selection),this.channel.emit(rt,this.selectionStore.selection),await this.renderSelection({persistedArgs:r})}async onGetProjectAnnotationsChanged({getProjectAnnotations:e}){await super.onGetProjectAnnotationsChanged({getProjectAnnotations:e}),this.selectionStore.selection&&this.renderSelection()}async onStoriesChanged({importFn:e,storyIndex:r}){await super.onStoriesChanged({importFn:e,storyIndex:r}),this.selectionStore.selection?await this.renderSelection():await this.selectSpecifiedStory()}onKeydown(e){if(!this.storyRenders.find(r=>r.disableKeyListeners)&&!cu(e)){let{altKey:r,ctrlKey:o,metaKey:a,shiftKey:i,key:u,code:l,keyCode:c}=e;this.channel.emit(Zt,{event:{altKey:r,ctrlKey:o,metaKey:a,shiftKey:i,key:u,code:l,keyCode:c}})}}async onSetCurrentStory(e){this.selectionStore.setSelection({viewMode:"story",...e}),await this.storeInitializationPromise,this.channel.emit(rt,this.selectionStore.selection),this.renderSelection()}onUpdateQueryParams(e){this.selectionStore.setQueryParams(e)}async onUpdateGlobals({globals:e}){var o,a;let r=this.currentRender instanceof je&&this.currentRender.story||void 0;super.onUpdateGlobals({globals:e,currentStory:r}),(this.currentRender instanceof Br||this.currentRender instanceof qr)&&await((a=(o=this.currentRender).rerender)==null?void 0:a.call(o))}async onUpdateArgs({storyId:e,updatedArgs:r}){super.onUpdateArgs({storyId:e,updatedArgs:r})}async onPreloadStories({ids:e}){await this.storeInitializationPromise,this.storyStoreValue&&await Promise.allSettled(e.map(r=>{var o;return(o=this.storyStoreValue)==null?void 0:o.loadEntry(r)}))}async renderSelection({persistedArgs:e}={}){var g,re,ne,le;let{renderToCanvas:r}=this;if(!this.storyStoreValue||!r)throw new V({methodName:"renderSelection"});let{selection:o}=this.selectionStore;if(!o)throw new Error("Cannot call renderSelection as no selection was made");let{storyId:a}=o,i;try{i=await this.storyStoreValue.storyIdToEntry(a)}catch(J){this.currentRender&&await this.teardownRender(this.currentRender),this.renderStoryLoadingException(a,J);return}let u=((g=this.currentSelection)==null?void 0:g.storyId)!==a,l=((re=this.currentRender)==null?void 0:re.type)!==i.type;i.type==="story"?this.view.showPreparingStory({immediate:l}):this.view.showPreparingDocs({immediate:l}),(ne=this.currentRender)!=null&&ne.isPreparing()&&await this.teardownRender(this.currentRender);let c;i.type==="story"?c=new je(this.channel,this.storyStoreValue,r,this.mainStoryCallbacks(a),a,"story"):du(i)?c=new Br(this.channel,this.storyStoreValue,i,this.mainStoryCallbacks(a)):c=new qr(this.channel,this.storyStoreValue,i,this.mainStoryCallbacks(a));let d=this.currentSelection;this.currentSelection=o;let m=this.currentRender;this.currentRender=c;try{await c.prepare()}catch(J){m&&await this.teardownRender(m),J!==Ae&&this.renderStoryLoadingException(a,J);return}let h=!u&&m&&!c.isEqual(m);if(e&&Mn(c)&&(fe(!!c.story),this.storyStoreValue.args.updateFromPersisted(c.story,e)),m&&!m.torndown&&!u&&!h&&!l){this.currentRender=m,this.channel.emit(co,a),this.view.showMain();return}if(m&&await this.teardownRender(m,{viewModeChanged:l}),d&&(u||l)&&this.channel.emit(oo,a),Mn(c)){fe(!!c.story);let{parameters:J,initialArgs:ce,argTypes:F,unmappedArgs:se,initialGlobals:he,userGlobals:Ve,storyGlobals:ve,globals:we}=this.storyStoreValue.getStoryContext(c.story);this.channel.emit(io,{id:a,parameters:J,initialArgs:ce,argTypes:F,args:se}),this.channel.emit(Ce,{userGlobals:Ve,storyGlobals:ve,globals:we,initialGlobals:he})}else{let{parameters:J}=this.storyStoreValue.projectAnnotations,{initialGlobals:ce,globals:F}=this.storyStoreValue.userGlobals;if(this.channel.emit(Ce,{globals:F,initialGlobals:ce,storyGlobals:{},userGlobals:F}),fu(c)||((le=c.entry.tags)==null?void 0:le.includes(Qi))){if(!c.csfFiles)throw new Cr({storyId:a});({parameters:J}=this.storyStoreValue.preparedMetaFromCSFFile({csfFile:c.csfFiles[0]}))}this.channel.emit(Yt,{id:a,parameters:J})}Mn(c)?(fe(!!c.story),this.storyRenders.push(c),this.currentRender.renderToElement(this.view.prepareForStory(c.story))):this.currentRender.renderToElement(this.view.prepareForDocs(),this.renderStoryToElement.bind(this))}async teardownRender(e,{viewModeChanged:r=!1}={}){var o;this.storyRenders=this.storyRenders.filter(a=>a!==e),await((o=e==null?void 0:e.teardown)==null?void 0:o.call(e,{viewModeChanged:r}))}mainStoryCallbacks(e){return{showStoryDuringRender:n(()=>this.view.showStoryDuringRender(),"showStoryDuringRender"),showMain:n(()=>this.view.showMain(),"showMain"),showError:n(r=>this.renderError(e,r),"showError"),showException:n(r=>this.renderException(e,r),"showException")}}renderPreviewEntryError(e,r){super.renderPreviewEntryError(e,r),this.view.showErrorDisplay(r)}renderMissingStory(){this.view.showNoPreview(),this.channel.emit(tt)}renderStoryLoadingException(e,r){I$1.error(r),this.view.showErrorDisplay(r),this.channel.emit(tt,e)}renderException(e,r){let{name:o="Error",message:a=String(r),stack:i}=r;this.channel.emit(lo,{name:o,message:a,stack:i}),this.channel.emit(Pe,{newPhase:"errored",storyId:e}),this.view.showErrorDisplay(r),I$1.error(`Error rendering story '${e}':`),I$1.error(r)}renderError(e,{title:r,description:o}){I$1.error(`Error rendering story ${r}: ${o}`),this.channel.emit(no,{title:r,description:o}),this.channel.emit(Pe,{newPhase:"errored",storyId:e}),this.view.showErrorDisplay({message:r,stack:o})}};n(Un,"PreviewWithSelection");var Ue=Un,Hr=ue(kt()),da=ue(kt()),pa=/^[a-zA-Z0-9 _-]*$/,ua=/^-?[0-9]+(\.[0-9]+)?$/,Uu=/^#([a-f0-9]{3,4}|[a-f0-9]{6}|[a-f0-9]{8})$/i,fa=/^(rgba?|hsla?)\(([0-9]{1,3}),\s?([0-9]{1,3})%?,\s?([0-9]{1,3})%?,?\s?([0-9](\.[0-9]{1,2})?)?\)$/i,Wn=n((t="",e)=>t===null||t===""||!pa.test(t)?!1:e==null||e instanceof Date||typeof e=="number"||typeof e=="boolean"?!0:typeof e=="string"?pa.test(e)||ua.test(e)||Uu.test(e)||fa.test(e):Array.isArray(e)?e.every(r=>Wn(t,r)):$$1(e)?Object.entries(e).every(([r,o])=>Wn(r,o)):!1,"validateArgs"),Gu={delimiter:";",nesting:!0,arrayRepeat:!0,arrayRepeatSyntax:"bracket",nestingSyntax:"js",valueDeserializer(t){if(t.startsWith("!")){if(t==="!undefined")return;if(t==="!null")return null;if(t==="!true")return!0;if(t==="!false")return!1;if(t.startsWith("!date(")&&t.endsWith(")"))return new Date(t.replaceAll(" ","+").slice(6,-1));if(t.startsWith("!hex(")&&t.endsWith(")"))return`#${t.slice(5,-1)}`;let e=t.slice(1).match(fa);if(e)return t.startsWith("!rgba")||t.startsWith("!RGBA")?`${e[1]}(${e[2]}, ${e[3]}, ${e[4]}, ${e[5]})`:t.startsWith("!hsla")||t.startsWith("!HSLA")?`${e[1]}(${e[2]}, ${e[3]}%, ${e[4]}%, ${e[5]})`:t.startsWith("!rgb")||t.startsWith("!RGB")?`${e[1]}(${e[2]}, ${e[3]}, ${e[4]})`:`${e[1]}(${e[2]}, ${e[3]}%, ${e[4]}%)`}return ua.test(t)?Number(t):t}},$n=n(t=>{let e=t.split(";").map(r=>r.replace("=","~").replace(":","="));return Object.entries((0,da.parse)(e.join(";"),Gu)).reduce((r,[o,a])=>Wn(o,a)?Object.assign(r,{[o]:a}):(j$1.warn(_$1`
      Omitted potentially unsafe URL args.

      More info: https://storybook.js.org/docs/writing-stories/args#setting-args-through-the-url
    `),r),{})},"parseArgsParam"),{history:ya,document:xe}=E$1;function qu(t){let e=(t||"").match(/^\/story\/(.+)/);if(!e)throw new Error(`Invalid path '${t}',  must start with '/story/'`);return e[1]}n(qu,"pathToId");var ma=n(({selection:t,extraParams:e})=>{let r=xe==null?void 0:xe.location.search.slice(1),{path:o,selectedKind:a,selectedStory:i,...u}=(0,Hr.parse)(r);return`?${(0,Hr.stringify)({...u,...e,...t&&{id:t.storyId,viewMode:t.viewMode}})}`},"getQueryString"),Bu=n(t=>{if(!t)return;let e=ma({selection:t}),{hash:r=""}=xe.location;xe.title=t.storyId,ya.replaceState({},"",`${xe.location.pathname}${e}${r}`)},"setPath"),Vu=n(t=>t!=null&&typeof t=="object"&&Array.isArray(t)===!1,"isObject"),Vr=n(t=>{if(t!==void 0){if(typeof t=="string")return t;if(Array.isArray(t))return Vr(t[0]);if(Vu(t))return Vr(Object.values(t).filter(Boolean))}},"getFirstString"),Hu=n(()=>{if(typeof xe<"u"){let t=xe.location.search.slice(1),e=(0,Hr.parse)(t),r=typeof e.args=="string"?$n(e.args):void 0,o=typeof e.globals=="string"?$n(e.globals):void 0,a=Vr(e.viewMode);(typeof a!="string"||!a.match(/docs|story/))&&(a="story");let i=Vr(e.path),u=i?qu(i):Vr(e.id);if(u)return{storySpecifier:u,args:r,globals:o,viewMode:a}}return null},"getSelectionSpecifierFromPath"),Yn=class{constructor(){this.selectionSpecifier=Hu()}setSelection(e){this.selection=e,Bu(this.selection)}setQueryParams(e){let r=ma({extraParams:e}),{hash:o=""}=xe.location;ya.replaceState({},"",`${xe.location.pathname}${r}${o}`)}};n(Yn,"UrlStore");var Be=Yn,$a=ue(Ha()),Ya=ue(kt()),{document:z$1}=E$1,za=100,Ka=(t=>(t.MAIN="MAIN",t.NOPREVIEW="NOPREVIEW",t.PREPARING_STORY="PREPARING_STORY",t.PREPARING_DOCS="PREPARING_DOCS",t.ERROR="ERROR",t))(Ka||{}),rs={PREPARING_STORY:"sb-show-preparing-story",PREPARING_DOCS:"sb-show-preparing-docs",MAIN:"sb-show-main",NOPREVIEW:"sb-show-nopreview",ERROR:"sb-show-errordisplay"},ts={centered:"sb-main-centered",fullscreen:"sb-main-fullscreen",padded:"sb-main-padded"},Wa=new $a.default({escapeXML:!0}),os=class{constructor(){if(this.testing=!1,typeof z$1<"u"){let{__SPECIAL_TEST_PARAMETER__:e}=(0,Ya.parse)(z$1.location.search.slice(1));switch(e){case"preparing-story":{this.showPreparingStory(),this.testing=!0;break}case"preparing-docs":{this.showPreparingDocs(),this.testing=!0;break}}}}prepareForStory(e){return this.showStory(),this.applyLayout(e.parameters.layout),z$1.documentElement.scrollTop=0,z$1.documentElement.scrollLeft=0,this.storyRoot()}storyRoot(){return z$1.getElementById("storybook-root")}prepareForDocs(){return this.showMain(),this.showDocs(),this.applyLayout("fullscreen"),z$1.documentElement.scrollTop=0,z$1.documentElement.scrollLeft=0,this.docsRoot()}docsRoot(){return z$1.getElementById("storybook-docs")}applyLayout(e="padded"){if(e==="none"){z$1.body.classList.remove(this.currentLayoutClass),this.currentLayoutClass=null;return}this.checkIfLayoutExists(e);let r=ts[e];z$1.body.classList.remove(this.currentLayoutClass),z$1.body.classList.add(r),this.currentLayoutClass=r}checkIfLayoutExists(e){ts[e]||I$1.warn(_$1`
          The desired layout: ${e} is not a valid option.
          The possible options are: ${Object.keys(ts).join(", ")}, none.
        `)}showMode(e){clearTimeout(this.preparingTimeout),Object.keys(Ka).forEach(r=>{r===e?z$1.body.classList.add(rs[r]):z$1.body.classList.remove(rs[r])})}showErrorDisplay({message:e="",stack:r=""}){let o=e,a=r,i=e.split(`
`);i.length>1&&([o]=i,a=i.slice(1).join(`
`).replace(/^\n/,"")),z$1.getElementById("error-message").innerHTML=Wa.toHtml(o),z$1.getElementById("error-stack").innerHTML=Wa.toHtml(a),this.showMode("ERROR")}showNoPreview(){var e,r;this.testing||(this.showMode("NOPREVIEW"),(e=this.storyRoot())==null||e.setAttribute("hidden","true"),(r=this.docsRoot())==null||r.setAttribute("hidden","true"))}showPreparingStory({immediate:e=!1}={}){clearTimeout(this.preparingTimeout),e?this.showMode("PREPARING_STORY"):this.preparingTimeout=setTimeout(()=>this.showMode("PREPARING_STORY"),za)}showPreparingDocs({immediate:e=!1}={}){clearTimeout(this.preparingTimeout),e?this.showMode("PREPARING_DOCS"):this.preparingTimeout=setTimeout(()=>this.showMode("PREPARING_DOCS"),za)}showMain(){this.showMode("MAIN")}showDocs(){this.storyRoot().setAttribute("hidden","true"),this.docsRoot().removeAttribute("hidden")}showStory(){this.docsRoot().setAttribute("hidden","true"),this.storyRoot().removeAttribute("hidden")}showStoryDuringRender(){z$1.body.classList.add(rs.MAIN)}};n(os,"WebView");var He=os,ns=class extends Ue{constructor(e,r){super(e,r,new Be,new He),this.importFn=e,this.getProjectAnnotations=r,E$1.__STORYBOOK_PREVIEW__=this}};n(ns,"PreviewWeb");var Wr=ns,{document:ze}=E$1,wf=["application/javascript","application/ecmascript","application/x-ecmascript","application/x-javascript","text/ecmascript","text/javascript","text/javascript1.0","text/javascript1.1","text/javascript1.2","text/javascript1.3","text/javascript1.4","text/javascript1.5","text/jscript","text/livescript","text/x-ecmascript","text/x-javascript","module"],_f="script",Xa="scripts-root";function $r(){let t=ze.createEvent("Event");t.initEvent("DOMContentLoaded",!0,!0),ze.dispatchEvent(t)}n($r,"simulateDOMContentLoaded");function Cf(t,e,r){let o=ze.createElement("script");o.type=t.type==="module"?"module":"text/javascript",t.src?(o.onload=e,o.onerror=e,o.src=t.src):o.textContent=t.innerText,r?r.appendChild(o):ze.head.appendChild(o),t.parentNode.removeChild(t),t.src||e()}n(Cf,"insertScript");function Ja(t,e,r=0){t[r](()=>{r++,r===t.length?e():Ja(t,e,r)})}n(Ja,"insertScriptsSequentially");function ss(t){let e=ze.getElementById(Xa);e?e.innerHTML="":(e=ze.createElement("div"),e.id=Xa,ze.body.appendChild(e));let r=Array.from(t.querySelectorAll(_f));if(r.length){let o=[];r.forEach(a=>{let i=a.getAttribute("type");(!i||wf.includes(i))&&o.push(u=>Cf(a,u,e))}),o.length&&Ja(o,$r,void 0)}else $r()}n(ss,"simulatePageLoad");var Qa={"@storybook/global":Ht,"storybook/internal/channels":br,"@storybook/channels":br,"@storybook/core/channels":br,"storybook/internal/client-logger":mr,"@storybook/client-logger":mr,"@storybook/core/client-logger":mr,"storybook/internal/core-events":ge,"@storybook/core-events":ge,"@storybook/core/core-events":ge,"storybook/internal/preview-errors":kr,"@storybook/core-events/preview-errors":kr,"@storybook/core/preview-errors":kr,"storybook/internal/preview-api":Yr,"@storybook/preview-api":Yr,"@storybook/core/preview-api":Yr,"storybook/internal/types":Tr,"@storybook/types":Tr,"@storybook/core/types":Tr},el=ue(Za()),ls;function Pf(){var t;return ls||(ls=new el.default((t=E$1.navigator)==null?void 0:t.userAgent).getBrowserInfo()),ls}n(Pf,"getBrowserInfo");function rl(t){return t.browserInfo=Pf(),t}n(rl,"prepareForTelemetry");function Of(t){let e=t.error||t;e.fromStorybook&&E$1.sendTelemetryError(e)}n(Of,"errorListener");function If({reason:t}){t.fromStorybook&&E$1.sendTelemetryError(t)}n(If,"unhandledRejectionListener");function Ff(){cs.forEach(t=>{E$1[yo[t]]=Qa[t]}),E$1.sendTelemetryError=t=>{E$1.__STORYBOOK_ADDONS_CHANNEL__.emit(uo,rl(t))},E$1.addEventListener("error",Of),E$1.addEventListener("unhandledrejection",If)}n(Ff,"setup");Ff();const{createBrowserChannel}=__STORYBOOK_MODULE_CHANNELS__,{addons}=__STORYBOOK_MODULE_PREVIEW_API__,channel=createBrowserChannel({page:"preview"});addons.setChannel(channel);window.__STORYBOOK_ADDONS_CHANNEL__=channel;window.CONFIG_TYPE==="DEVELOPMENT"&&(window.__STORYBOOK_SERVER_CHANNEL__=channel);var b=Object.create,f=Object.defineProperty,v=Object.getOwnPropertyDescriptor,P=Object.getOwnPropertyNames,O=Object.getPrototypeOf,_=Object.prototype.hasOwnProperty,s=(t,e)=>f(t,"name",{value:e,configurable:!0}),$=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),j=(t,e,r,o)=>{if(e&&typeof e=="object"||typeof e=="function")for(let a of P(e))!_.call(t,a)&&a!==r&&f(t,a,{get:()=>e[a],enumerable:!(o=v(e,a))||o.enumerable});return t},C=(t,e,r)=>(r=t!=null?b(O(t)):{},j(f(r,"default",{value:t,enumerable:!0}),t)),T=$(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isEqual=function(){var e=Object.prototype.toString,r=Object.getPrototypeOf,o=Object.getOwnPropertySymbols?function(a){return Object.keys(a).concat(Object.getOwnPropertySymbols(a))}:Object.keys;return function(a,i){return s(function u(l,c,d){var m,h,g,re=e.call(l),ne=e.call(c);if(l===c)return!0;if(l==null||c==null)return!1;if(d.indexOf(l)>-1&&d.indexOf(c)>-1)return!0;if(d.push(l,c),re!=ne||(m=o(l),h=o(c),m.length!=h.length||m.some(function(le){return!u(l[le],c[le],d)})))return!1;switch(re.slice(8,-1)){case"Symbol":return l.valueOf()==c.valueOf();case"Date":case"Number":return+l==+c||+l!=+l&&+c!=+c;case"RegExp":case"Function":case"String":case"Boolean":return""+l==""+c;case"Set":case"Map":m=l.entries(),h=c.entries();do if(!u((g=m.next()).value,h.next().value,d))return!1;while(!g.done);return!0;case"ArrayBuffer":l=new Uint8Array(l),c=new Uint8Array(c);case"DataView":l=new Uint8Array(l.buffer),c=new Uint8Array(c.buffer);case"Float32Array":case"Float64Array":case"Int8Array":case"Int16Array":case"Int32Array":case"Uint8Array":case"Uint16Array":case"Uint32Array":case"Uint8ClampedArray":case"Arguments":case"Array":if(l.length!=c.length)return!1;for(g=0;g<l.length;g++)if((g in l||g in c)&&(g in l!=g in c||!u(l[g],c[g],d)))return!1;return!0;case"Object":return u(r(l),r(c),d);default:return!1}},"n")(a,i,[])}}()});function R(t){return t.replace(/_/g," ").replace(/-/g," ").replace(/\./g," ").replace(/([^\n])([A-Z])([a-z])/g,(e,r,o,a)=>`${r} ${o}${a}`).replace(/([a-z])([A-Z])/g,(e,r,o)=>`${r} ${o}`).replace(/([a-z])([0-9])/gi,(e,r,o)=>`${r} ${o}`).replace(/([0-9])([a-z])/gi,(e,r,o)=>`${r} ${o}`).replace(/(\s|^)(\w)/g,(e,r,o)=>`${r}${o.toUpperCase()}`).replace(/ +/g," ").trim()}s(R,"toStartCaseStr");var y=C(T()),x=s(t=>t.map(e=>typeof e<"u").filter(Boolean).length,"count"),E=s((t,e)=>{let{exists:r,eq:o,neq:a,truthy:i}=t;if(x([r,o,a,i])>1)throw new Error(`Invalid conditional test ${JSON.stringify({exists:r,eq:o,neq:a})}`);if(typeof o<"u")return(0,y.isEqual)(e,o);if(typeof a<"u")return!(0,y.isEqual)(e,a);if(typeof r<"u"){let u=typeof e<"u";return r?u:!u}return typeof i>"u"||i?!!e:!e},"testValue"),z=s((t,e,r)=>{if(!t.if)return!0;let{arg:o,global:a}=t.if;if(x([o,a])!==1)throw new Error(`Invalid conditional value ${JSON.stringify({arg:o,global:a})}`);let i=o?e[o]:r[a];return E(t.if,i)},"includeConditionalArg");const{composeConfigs:M,normalizeProjectAnnotations:N}=__STORYBOOK_MODULE_PREVIEW_API__;function L(t){let e,r={_tag:"Preview",input:t,get composed(){if(e)return e;let{addons:o,...a}=t;return e=N(M([...o??[],a])),e},meta(o){return I(o,this)}};return globalThis.globalProjectAnnotations=r.composed,r}s(L,"__definePreview");function W(t){return t!=null&&typeof t=="object"&&"_tag"in t&&(t==null?void 0:t._tag)==="Preview"}s(W,"isPreview");function H(t){return t!=null&&typeof t=="object"&&"_tag"in t&&(t==null?void 0:t._tag)==="Meta"}s(H,"isMeta");function I(t,e){return{_tag:"Meta",input:t,preview:e,get composed(){throw new Error("Not implemented")},story(r){return U(r,this)}}}s(I,"defineMeta");function U(t,e){return{_tag:"Story",input:t,meta:e,get composed(){throw new Error("Not implemented")}}}s(U,"defineStory");function K(t){return t!=null&&typeof t=="object"&&"_tag"in t&&(t==null?void 0:t._tag)==="Story"}s(K,"isStory");var D=s(t=>t.toLowerCase().replace(/[ ’–—―′¿'`~!@#$%^&*()_|+\-=?;:'",.<>\{\}\[\]\\\/]/gi,"-").replace(/-+/g,"-").replace(/^-+/,"").replace(/-+$/,""),"sanitize");function S(t,e){return Array.isArray(e)?e.includes(t):t.match(e)}s(S,"matches");function te(t,{includeStories:e,excludeStories:r}){return t!=="__esModule"&&(!e||S(t,e))&&(!r||!S(t,r))}s(te,"isExportStory");const importers={"./components/ui/button.stories.tsx":()=>__vitePreload(()=>import("./button.stories-DM3IL4Sj.js"),__vite__mapDeps([0,1,2]),import.meta.url)};async function importFn(t){return await importers[t]()}Ff();const{composeConfigs,PreviewWeb}=__STORYBOOK_MODULE_PREVIEW_API__,getProjectAnnotations=async(t=[])=>{const e=await __vitePreload(()=>import("./preview-Bt2nRSM_.js"),__vite__mapDeps([3,1,4]),import.meta.url);if(W(e.default))return e.default.composed;const r=await Promise.all([t[0]??__vitePreload(()=>import("./entry-preview-Mp3O3HVQ.js"),__vite__mapDeps([5,6,1]),import.meta.url),t[1]??__vitePreload(()=>import("./entry-preview-docs-BqSPhk5G.js"),__vite__mapDeps([7,6,8,1]),import.meta.url),t[2]??__vitePreload(()=>import("./preview-DD_OYowb.js"),[],import.meta.url),t[3]??__vitePreload(()=>import("./preview-DtE0tX9M.js"),[],import.meta.url),t[4]??__vitePreload(()=>import("./preview-B8lJiyuQ.js"),__vite__mapDeps([9,10]),import.meta.url),t[5]??__vitePreload(()=>import("./preview-CvbIS5ZJ.js"),[],import.meta.url),t[6]??__vitePreload(()=>import("./preview-BBWR9nbA.js"),[],import.meta.url),t[7]??__vitePreload(()=>import("./preview-BWzBA1C2.js"),__vite__mapDeps([11,10]),import.meta.url),t[8]??__vitePreload(()=>import("./preview-DGUiP6tS.js"),[],import.meta.url),t[9]??__vitePreload(()=>import("./preview-N1j9pG9L.js"),__vite__mapDeps([12,13]),import.meta.url),t[10]??__vitePreload(()=>import("./preview-DHQbi4pV.js"),[],import.meta.url)]);return composeConfigs([...r,e])};window.__STORYBOOK_PREVIEW__=window.__STORYBOOK_PREVIEW__||new PreviewWeb(importFn,getProjectAnnotations);window.__STORYBOOK_STORY_STORE__=window.__STORYBOOK_STORY_STORE__||window.__STORYBOOK_PREVIEW__.storyStore;export{D,__vitePreload as _,z};
