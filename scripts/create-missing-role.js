const path = require('path');
// Detectar si existe el build transpilado
let AppDataSource;
const backendPath = path.join(process.cwd(), 'backend');
const distOrmconfig = path.join(backendPath, 'dist', 'ormconfig.js');
if (require('fs').existsSync(distOrmconfig)) {
    // Usar build JS
    ({ AppDataSource } = require(distOrmconfig));
}
else {
    // Usar fuente TS
    ({ AppDataSource } = require(path.join(backendPath, 'ormconfig')));
}
async function createMissingRole() {
    try {
        await AppDataSource.initialize();
        console.log('✅ Conexión a la base de datos establecida');
        const queryRunner = AppDataSource.createQueryRunner();
        // Verificar si el rol ya existe
        const existingRole = await queryRunner.query('SELECT * FROM roles WHERE name = $1', ['ROLE_CLIENTES']);
        if (existingRole.length > 0) {
            console.log('✅ ROLE_CLIENTES ya existe');
        }
        else {
            // Crear el rol
            await queryRunner.query('INSERT INTO roles (name) VALUES ($1)', ['ROLE_CLIENTES']);
            console.log('✅ ROLE_CLIENTES creado exitosamente');
        }
        await queryRunner.release();
        await AppDataSource.destroy();
    }
    catch (error) {
        console.error('❌ Error:', error.message);
        process.exit(1);
    }
}
createMissingRole();
