import { AppDataSource } from '../ormconfig';
import { Permission } from '../src/entities/permission.entity';
import { Role } from '../src/entities/role.entity';

async function createDepartmentalRoles() {
  try {
    // Inicializar conexión a la base de datos
    await AppDataSource.initialize();
    console.log('✅ Conexión a la base de datos establecida');

    const permissionRepository = AppDataSource.getRepository(Permission);
    const roleRepository = AppDataSource.getRepository(Role);

    // Definir roles y sus permisos
    const roleDefinitions = [
      // === ROLES DE ADMINISTRACIÓN ===
      {
        name: 'ROLE_ADMINISTRACION_MANAGER',
        permissions: [
          // Permisos completos de administración
          'admin:viaticos:create', 'admin:viaticos:read', 'admin:viaticos:update', 'admin:viaticos:approve',
          'admin:tiempo_extra:create', 'admin:tiempo_extra:read', 'admin:tiempo_extra:approve',
          'admin:paqueteria:create', 'admin:paqueteria:read', 'admin:paqueteria:track',
          'admin:fiscal:create', 'admin:fiscal:read', 'admin:fiscal:cancel',
          'admin:contratos:create', 'admin:contratos:read', 'admin:contratos:update',
          'admin:facturas:create', 'admin:facturas:read', 'admin:facturas:validate',
          'admin:credito:create', 'admin:credito:read', 'admin:credito:approve',
          'admin:morosos:read', 'admin:morosos:update', 'admin:morosos:liberar',
          'admin:reportes:read', 'admin:reportes:export',
          // Permisos generales
          'dashboard:read', 'profile:read', 'profile:update'
        ]
      },
      {
        name: 'ROLE_ADMINISTRACION_USER',
        permissions: [
          // Permisos limitados de administración
          'admin:viaticos:create', 'admin:viaticos:read', 'admin:viaticos:update',
          'admin:tiempo_extra:create', 'admin:tiempo_extra:read',
          'admin:paqueteria:create', 'admin:paqueteria:read', 'admin:paqueteria:track',
          'admin:fiscal:read', 'admin:contratos:read', 'admin:facturas:read',
          'admin:credito:read', 'admin:morosos:read', 'admin:reportes:read',
          // Permisos generales
          'dashboard:read', 'profile:read', 'profile:update'
        ]
      },

      // === ROLES DE VENTAS ===
      {
        name: 'ROLE_VENTAS_MANAGER',
        permissions: [
          // Permisos completos de ventas
          'ventas:clientes:create', 'ventas:clientes:read', 'ventas:clientes:update', 'ventas:clientes:delete',
          'ventas:cotizaciones:create', 'ventas:cotizaciones:read', 'ventas:cotizaciones:update', 'ventas:cotizaciones:delete',
          'ventas:proyectos:create', 'ventas:proyectos:read', 'ventas:proyectos:update', 'ventas:proyectos:delete',
          'ventas:asignacion:read', 'ventas:asignacion:update',
          'ventas:fichas:create', 'ventas:fichas:read', 'ventas:fichas:update',
          'ventas:existencias:read', 'ventas:reportes:read', 'ventas:reportes:export',
          // Permisos de viáticos (pueden solicitar)
          'admin:viaticos:create', 'admin:viaticos:read', 'admin:viaticos:update',
          // Permisos generales
          'dashboard:read', 'profile:read', 'profile:update'
        ]
      },
      {
        name: 'ROLE_VENTAS_USER',
        permissions: [
          // Permisos limitados de ventas
          'ventas:clientes:read', 'ventas:clientes:update',
          'ventas:cotizaciones:create', 'ventas:cotizaciones:read', 'ventas:cotizaciones:update',
          'ventas:proyectos:read', 'ventas:proyectos:update',
          'ventas:asignacion:read', 'ventas:fichas:read',
          'ventas:existencias:read', 'ventas:reportes:read',
          // Permisos de viáticos (pueden solicitar)
          'admin:viaticos:create', 'admin:viaticos:read', 'admin:viaticos:update',
          // Permisos generales
          'dashboard:read', 'profile:read', 'profile:update'
        ]
      },

      // === ROLES DE ALMACÉN ===
      {
        name: 'ROLE_ALMACEN_MANAGER',
        permissions: [
          // Permisos completos de almacén
          'almacen:inventario:create', 'almacen:inventario:read', 'almacen:inventario:update', 'almacen:inventario:delete',
          'almacen:entradas:create', 'almacen:entradas:read',
          'almacen:salidas:create', 'almacen:salidas:read',
          'almacen:prestamos:create', 'almacen:prestamos:read', 'almacen:prestamos:return',
          'almacen:categorias:create', 'almacen:categorias:read', 'almacen:categorias:update',
          'almacen:reportes:read', 'almacen:reportes:export',
          // Permisos de viáticos y tiempo extra
          'admin:viaticos:create', 'admin:viaticos:read', 'admin:viaticos:update',
          'admin:tiempo_extra:create', 'admin:tiempo_extra:read',
          // Permisos generales
          'dashboard:read', 'profile:read', 'profile:update'
        ]
      },
      {
        name: 'ROLE_ALMACEN_USER',
        permissions: [
          // Permisos limitados de almacén
          'almacen:inventario:read', 'almacen:inventario:update',
          'almacen:entradas:create', 'almacen:entradas:read',
          'almacen:salidas:create', 'almacen:salidas:read',
          'almacen:prestamos:create', 'almacen:prestamos:read', 'almacen:prestamos:return',
          'almacen:categorias:read', 'almacen:reportes:read',
          // Permisos de viáticos y tiempo extra
          'admin:viaticos:create', 'admin:viaticos:read', 'admin:viaticos:update',
          'admin:tiempo_extra:create', 'admin:tiempo_extra:read',
          // Permisos generales
          'dashboard:read', 'profile:read', 'profile:update'
        ]
      },

      // === ROLES DE LOGÍSTICA ===
      {
        name: 'ROLE_LOGISTICA_MANAGER',
        permissions: [
          // Permisos completos de logística
          'logistica:calendario:read', 'logistica:calendario:update',
          'logistica:ingresos:create', 'logistica:ingresos:read',
          'logistica:proyectos:create', 'logistica:proyectos:read', 'logistica:proyectos:update',
          'logistica:paqueteria:create', 'logistica:paqueteria:read',
          'logistica:reportes:read', 'logistica:reportes:export',
          // Permisos de viáticos
          'admin:viaticos:create', 'admin:viaticos:read', 'admin:viaticos:update',
          'admin:paqueteria:create', 'admin:paqueteria:read', 'admin:paqueteria:track',
          // Permisos generales
          'dashboard:read', 'profile:read', 'profile:update'
        ]
      },
      {
        name: 'ROLE_LOGISTICA_USER',
        permissions: [
          // Permisos limitados de logística
          'logistica:calendario:read', 'logistica:ingresos:create', 'logistica:ingresos:read',
          'logistica:proyectos:read', 'logistica:proyectos:update',
          'logistica:paqueteria:create', 'logistica:paqueteria:read',
          'logistica:reportes:read',
          // Permisos de viáticos
          'admin:viaticos:create', 'admin:viaticos:read', 'admin:viaticos:update',
          'admin:paqueteria:create', 'admin:paqueteria:read', 'admin:paqueteria:track',
          // Permisos generales
          'dashboard:read', 'profile:read', 'profile:update'
        ]
      },

      // === ROLES DE CALIDAD ===
      {
        name: 'ROLE_CALIDAD_MANAGER',
        permissions: [
          // Permisos completos de calidad
          'calidad:incidencias:create', 'calidad:incidencias:read', 'calidad:incidencias:update',
          'calidad:procedimientos:create', 'calidad:procedimientos:read', 'calidad:procedimientos:update',
          'calidad:questionpro:read', 'calidad:questionpro:create',
          'calidad:reportes:read', 'calidad:reportes:export',
          // Permisos generales
          'dashboard:read', 'profile:read', 'profile:update'
        ]
      },
      {
        name: 'ROLE_CALIDAD_USER',
        permissions: [
          // Permisos limitados de calidad
          'calidad:incidencias:create', 'calidad:incidencias:read', 'calidad:incidencias:update',
          'calidad:procedimientos:read', 'calidad:questionpro:read',
          'calidad:reportes:read',
          // Permisos generales
          'dashboard:read', 'profile:read', 'profile:update'
        ]
      },

      // === ROLES DE METROLOGÍA ===
      {
        name: 'ROLE_METROLOGIA_MANAGER',
        permissions: [
          // Permisos completos de metrología
          'metrologia:autorizaciones:create', 'metrologia:autorizaciones:read', 'metrologia:autorizaciones:update',
          'metrologia:entregas:create', 'metrologia:entregas:read',
          'metrologia:patrones:create', 'metrologia:patrones:read', 'metrologia:patrones:update',
          'metrologia:rendimiento:read', 'metrologia:retroalimentacion:create', 'metrologia:retroalimentacion:read',
          'metrologia:tareas:create', 'metrologia:tareas:read', 'metrologia:tareas:update',
          'metrologia:capacitaciones:create', 'metrologia:capacitaciones:read', 'metrologia:capacitaciones:update',
          // Permisos de viáticos
          'admin:viaticos:create', 'admin:viaticos:read', 'admin:viaticos:update',
          // Permisos generales
          'dashboard:read', 'profile:read', 'profile:update'
        ]
      },
      {
        name: 'ROLE_METROLOGIA_USER',
        permissions: [
          // Permisos limitados de metrología
          'metrologia:autorizaciones:read', 'metrologia:entregas:create', 'metrologia:entregas:read',
          'metrologia:patrones:read', 'metrologia:rendimiento:read',
          'metrologia:retroalimentacion:create', 'metrologia:retroalimentacion:read',
          'metrologia:tareas:read', 'metrologia:tareas:update',
          'metrologia:capacitaciones:read',
          // Permisos de viáticos
          'admin:viaticos:create', 'admin:viaticos:read', 'admin:viaticos:update',
          // Permisos generales
          'dashboard:read', 'profile:read', 'profile:update'
        ]
      },

      // === ROLES DE RECURSOS HUMANOS ===
      {
        name: 'ROLE_RH_MANAGER',
        permissions: [
          // Permisos completos de RH
          'rh:capacitacion:create', 'rh:capacitacion:read', 'rh:capacitacion:update',
          'rh:rfp:create', 'rh:rfp:read', 'rh:rfp:update',
          'rh:expediente_contractual:create', 'rh:expediente_contractual:read', 'rh:expediente_contractual:update',
          'rh:expediente_laboral:create', 'rh:expediente_laboral:read', 'rh:expediente_laboral:update',
          'rh:rsp:create', 'rh:rsp:read', 'rh:rsp:update',
          // Permisos generales
          'dashboard:read', 'profile:read', 'profile:update'
        ]
      },
      {
        name: 'ROLE_RH_USER',
        permissions: [
          // Permisos limitados de RH
          'rh:capacitacion:read', 'rh:rfp:read', 'rh:expediente_contractual:read',
          'rh:expediente_laboral:read', 'rh:rsp:read',
          // Permisos generales
          'dashboard:read', 'profile:read', 'profile:update'
        ]
      },

      // === ROLES DE SERVICIO ===
      {
        name: 'ROLE_SERVICIO_MANAGER',
        permissions: [
          // Permisos de servicio (basados en los requerimientos)
          'ventas:proyectos:read', 'ventas:proyectos:update', // Pueden ver y actualizar proyectos
          'metrologia:entregas:create', 'metrologia:entregas:read', // Pueden gestionar entregas
          'metrologia:tareas:create', 'metrologia:tareas:read', 'metrologia:tareas:update',
          // Permisos de viáticos y tiempo extra
          'admin:viaticos:create', 'admin:viaticos:read', 'admin:viaticos:update',
          'admin:tiempo_extra:create', 'admin:tiempo_extra:read',
          // Permisos generales
          'dashboard:read', 'profile:read', 'profile:update'
        ]
      },
      {
        name: 'ROLE_SERVICIO_USER',
        permissions: [
          // Permisos limitados de servicio
          'ventas:proyectos:read', 'metrologia:entregas:read',
          'metrologia:tareas:read', 'metrologia:tareas:update',
          // Permisos de viáticos y tiempo extra
          'admin:viaticos:create', 'admin:viaticos:read', 'admin:viaticos:update',
          'admin:tiempo_extra:create', 'admin:tiempo_extra:read',
          // Permisos generales
          'dashboard:read', 'profile:read', 'profile:update'
        ]
      },

      // === ROLES DE INFORMES ===
      {
        name: 'ROLE_INFORMES_MANAGER',
        permissions: [
          // Permisos completos de informes
          'informes:asignar_folio:create', 'informes:asignar_folio:read',
          'informes:historial:read', 'informes:status:read', 'informes:status:update',
          'informes:estadisticos:read', 'informes:estadisticos:export',
          // Permisos generales
          'dashboard:read', 'profile:read', 'profile:update'
        ]
      },
      {
        name: 'ROLE_INFORMES_USER',
        permissions: [
          // Permisos limitados de informes
          'informes:asignar_folio:read', 'informes:historial:read',
          'informes:status:read', 'informes:estadisticos:read',
          // Permisos generales
          'dashboard:read', 'profile:read', 'profile:update'
        ]
      }
    ];

    console.log(`📝 Creando ${roleDefinitions.length} roles departamentales...`);

    let createdRoles = 0;
    let skippedRoles = 0;
    let totalPermissionsAssigned = 0;

    for (const roleDef of roleDefinitions) {
      // Verificar si el rol ya existe
      let role = await roleRepository.findOne({
        where: { name: roleDef.name },
        relations: ['permissions']
      });

      if (role) {
        console.log(`⏭️  Rol '${roleDef.name}' ya existe, omitiendo creación...`);
        skippedRoles++;
      } else {
        // Crear el rol
        role = roleRepository.create({ name: roleDef.name });
        await roleRepository.save(role);
        console.log(`✅ Rol '${roleDef.name}' creado exitosamente`);
        createdRoles++;
      }

      // Buscar los permisos
      const permissions = await permissionRepository.find({
        where: roleDef.permissions.map(permName => ({ name: permName }))
      });

      if (permissions.length !== roleDef.permissions.length) {
        const foundPermNames = permissions.map(p => p.name);
        const missingPerms = roleDef.permissions.filter(p => !foundPermNames.includes(p));
        console.log(`⚠️  Advertencia: Faltan permisos para '${roleDef.name}': ${missingPerms.join(', ')}`);
      }

      // Asignar permisos al rol (evitar duplicados)
      const currentPermissionIds = role.permissions?.map(p => p.id) || [];
      const newPermissions = permissions.filter(p => !currentPermissionIds.includes(p.id));
      
      if (newPermissions.length > 0) {
        role.permissions = [...(role.permissions || []), ...newPermissions];
        await roleRepository.save(role);
        console.log(`   📋 ${newPermissions.length} permisos asignados a '${roleDef.name}'`);
        totalPermissionsAssigned += newPermissions.length;
      } else {
        console.log(`   📋 Rol '${roleDef.name}' ya tenía todos los permisos asignados`);
      }
    }

    console.log(`\n🎉 Proceso completado:`);
    console.log(`   ✅ ${createdRoles} roles creados`);
    console.log(`   ⏭️  ${skippedRoles} roles ya existían`);
    console.log(`   📋 ${totalPermissionsAssigned} permisos asignados`);
    console.log(`   📊 Total procesados: ${roleDefinitions.length} roles`);

  } catch (error) {
    console.error('❌ Error al crear roles departamentales:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('🔌 Conexión a la base de datos cerrada');
    }
  }
}

// Ejecutar el script
createDepartmentalRoles(); 