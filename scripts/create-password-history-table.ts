const path = require('path');

// Detectar si existe el build transpilado
let AppDataSource;
const backendPath = path.join(process.cwd(), 'backend');
const distOrmconfig = path.join(backendPath, 'dist', 'ormconfig.js');
if (require('fs').existsSync(distOrmconfig)) {
  // Usar build JS
  ({ AppDataSource } = require(distOrmconfig));
} else {
  // Usar fuente TS
  ({ AppDataSource } = require(path.join(backendPath, 'ormconfig')));
}

async function createPasswordHistoryTable() {
  try {
    await AppDataSource.initialize();
    console.log('✅ Conexión a la base de datos establecida');

    const queryRunner = AppDataSource.createQueryRunner();

    // Verificar si la tabla ya existe
    const tableExists = await queryRunner.hasTable('password_history');
    
    if (!tableExists) {
      console.log('📋 Creando tabla password_history...');
      
      await queryRunner.query(`
        CREATE TABLE password_history (
          id SERIAL PRIMARY KEY,
          user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          password_hash VARCHAR(255) NOT NULL,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          created_by INTEGER REFERENCES users(id),
          ip_address INET,
          user_agent TEXT
        );
      `);

      // Crear índices para optimización
      await queryRunner.query(`
        CREATE INDEX idx_password_history_user_id ON password_history(user_id);
        CREATE INDEX idx_password_history_created_at ON password_history(created_at);
      `);

      console.log('✅ Tabla password_history creada exitosamente');
    } else {
      console.log('ℹ️ La tabla password_history ya existe');
    }

    await queryRunner.release();
    await AppDataSource.destroy();
    console.log('🔚 Proceso completado');

  } catch (error) {
    console.error('❌ Error creando tabla password_history:', error);
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
    }
    process.exit(1);
  }
}

createPasswordHistoryTable(); 