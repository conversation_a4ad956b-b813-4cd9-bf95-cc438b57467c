#!/bin/bash

# Script para extraer solo las tablas de la aplicación desde el esquema completo
set -e

echo "=== Extrayendo esquema de aplicación ==="

# Archivo de entrada
INPUT_FILE="comintec_schema_core.sql"
OUTPUT_FILE="comintec_schema_app_only.sql"

if [[ ! -f "$INPUT_FILE" ]]; then
  echo "Error: $INPUT_FILE no encontrado"
  exit 1
fi

# Crear archivo de salida con encabezado
cat > "$OUTPUT_FILE" << 'EOF'
-- =============================
-- COMINTEC - Esquema de Aplicación (Solo tablas públicas)
-- =============================
-- Extraído del esquema completo de Supabase
-- Incluye solo: tablas del esquema public de la aplicación
-- =============================

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

EOF

# Extraer solo las secciones del esquema public
echo "Extrayendo tablas del esquema public..."

# Buscar desde la primera tabla public hasta antes de los constraints de auth
START_LINE=$(grep -n "CREATE TABLE public\." "$INPUT_FILE" | head -1 | cut -d: -f1)
END_LINE=$(grep -n "-- Name.*auth.*Owner" "$INPUT_FILE" | grep "CONSTRAINT\|FK\|INDEX" | head -1 | cut -d: -f1)

if [[ -z "$START_LINE" ]]; then
  echo "Error: No se encontraron tablas del esquema public"
  exit 1
fi

if [[ -z "$END_LINE" ]]; then
  # Buscar constraints de auth como alternativa
  END_LINE=$(grep -n "ALTER TABLE ONLY auth\." "$INPUT_FILE" | head -1 | cut -d: -f1)
fi

if [[ -z "$END_LINE" ]]; then
  # Como última opción, usar el final del archivo
  END_LINE=$(wc -l < "$INPUT_FILE")
fi

# Ajustar END_LINE para no incluir la línea de auth
((END_LINE--))

echo "Extrayendo líneas $START_LINE a $END_LINE..."

# Extraer las líneas relevantes
sed -n "${START_LINE},${END_LINE}p" "$INPUT_FILE" >> "$OUTPUT_FILE"

# Agregar un comentario final
echo "" >> "$OUTPUT_FILE"
echo "-- Fin del esquema de aplicación COMINTEC" >> "$OUTPUT_FILE"

# Mostrar estadísticas
TOTAL_LINES=$(wc -l < "$INPUT_FILE")
APP_LINES=$(wc -l < "$OUTPUT_FILE")
TABLES_COUNT=$(grep -c "CREATE TABLE public\." "$OUTPUT_FILE")

echo ""
echo "=== Resultados ==="
echo "Archivo original: $TOTAL_LINES líneas"
echo "Archivo filtrado: $APP_LINES líneas"
echo "Tablas extraídas: $TABLES_COUNT"
echo "Reducción: $(( (TOTAL_LINES - APP_LINES) * 100 / TOTAL_LINES ))%"
echo ""
echo "Archivo generado: $OUTPUT_FILE"

# Mostrar las primeras tablas para verificación
echo ""
echo "=== Primeras tablas extraídas ==="
grep "CREATE TABLE public\." "$OUTPUT_FILE" | head -5 