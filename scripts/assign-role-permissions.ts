const path = require('path');
const fs = require('fs');
const yaml = require('js-yaml');

// Detectar si existe el build transpilado
let AppDataSource;
const backendPath = path.join(process.cwd(), 'backend');
const distOrmconfig = path.join(backendPath, 'dist', 'ormconfig.js');
if (fs.existsSync(distOrmconfig)) {
  // Usar build JS
  ({ AppDataSource } = require(distOrmconfig));
} else {
  // Usar fuente TS
  ({ AppDataSource } = require(path.join(backendPath, 'ormconfig')));
}

async function main() {
  // 1. Leer el YAML
  const yamlPath = path.join(__dirname, 'role-permissions-map.yaml');
  const mapping = yaml.load(fs.readFileSync(yamlPath, 'utf8')) as Record<string, string[]>;

  // 2. In<PERSON><PERSON>zar cone<PERSON>
  await AppDataSource.initialize();
  console.log('✅ Conexión a la base de datos establecida');

  for (const [roleName, desiredPerms] of Object.entries(mapping)) {
    // Buscar el rol usando SQL directo
    const roleQuery = 'SELECT id, name FROM roles WHERE name = $1';
    const roleResult = await AppDataSource.query(roleQuery, [roleName]);
    
    if (!roleResult || roleResult.length === 0) {
      console.warn(`⚠️ Rol no encontrado: ${roleName}`);
      continue;
    }
    
    const role = roleResult[0];

    // Permisos actuales usando SQL directo
    const currentPermsQuery = `
      SELECT p.name 
      FROM role_permissions rp 
      JOIN permissions p ON rp.permission_id = p.id 
      WHERE rp.role_id = $1
    `;
    const currentPerms = await AppDataSource.query(currentPermsQuery, [role.id]);
    const currentPermNames = currentPerms.map((p: any) => p.name);

    // Permisos a agregar y quitar
    const toAdd = desiredPerms.filter((p: string) => !currentPermNames.includes(p));
    const toRemove = currentPermNames.filter((p: string) => !desiredPerms.includes(p));

    // Mostrar diferencias
    console.log(`\n=== ${roleName} ===`);
    console.log('Permisos actuales:', currentPermNames.length);
    console.log('Permisos deseados:', desiredPerms.length);
    if (toAdd.length) console.log('➕ A agregar:', toAdd);
    if (toRemove.length) console.log('➖ A quitar:', toRemove);
    if (!toAdd.length && !toRemove.length) {
      console.log('✅ Sin cambios necesarios');
      continue;
    }

    // Agregar permisos
    for (const permName of toAdd) {
      const permQuery = 'SELECT id, name FROM permissions WHERE name = $1';
      const permResult = await AppDataSource.query(permQuery, [permName]);
      
      if (permResult && permResult.length > 0) {
        const perm = permResult[0];
        await AppDataSource.query(
          'INSERT INTO role_permissions (role_id, permission_id) VALUES ($1, $2) ON CONFLICT DO NOTHING',
          [role.id, perm.id]
        );
        console.log(`  ✔️ Asignado: ${permName}`);
      } else {
        console.warn(`  ⚠️ Permiso no encontrado: ${permName}`);
      }
    }

    // Quitar permisos
    for (const permName of toRemove) {
      const permQuery = 'SELECT id, name FROM permissions WHERE name = $1';
      const permResult = await AppDataSource.query(permQuery, [permName]);
      
      if (permResult && permResult.length > 0) {
        const perm = permResult[0];
        await AppDataSource.query(
          'DELETE FROM role_permissions WHERE role_id = $1 AND permission_id = $2',
          [role.id, perm.id]
        );
        console.log(`  ❌ Removido: ${permName}`);
      }
    }
  }

  await AppDataSource.destroy();
  console.log('\n🎉 Asignación de permisos completada.');
}

main().catch((e: any) => {
  console.error('Error en la asignación masiva de permisos:', e);
  process.exit(1);
}); 