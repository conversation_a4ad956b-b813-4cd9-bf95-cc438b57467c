#!/bin/bash

# Crear directorio para los archivos de configuración
mkdir -p config_package

# 1. Archivos de entorno (crear ejemplos si no existen)
if [ -f "frontend/.env.local" ]; then
    cp frontend/.env.local config_package/
    echo "⚠️  ADVERTENCIA: .env.local contiene credenciales sensibles"
    echo "   - Revisa el archivo antes de compartirlo"
fi

# 2. Script para instalar dependencias (opcional)
echo '#!/bin/bash
echo "Instalando dependencias..."
cd frontend && npm install
cd ..
' > config_package/install_deps.sh
chmod +x config_package/install_deps.sh

# 3. Crear README con instrucciones
echo '# Paquete de Configuración

## Instrucciones

1. Copia los archivos a sus ubicaciones correspondientes:
   ```bash
   cp .env.local frontend/
   ```

2. Instala las dependencias:
   ```bash
   chmod +x install_deps.sh
   ./install_deps.sh
   ```

## Notas de Seguridad

- No compartas los archivos .env que contengan credenciales reales
- Usa canales seguros para compartir credenciales
- Considera usar un gestor de contraseñas para compartir credenciales
' > config_package/README.md

# Crear archivo comprimido
tar -czf config_package.tar.gz config_package/

echo "✅ Paquete creado: config_package.tar.gz"
echo "📝 Revisa el contenido antes de compartir:"
ls -la config_package/
