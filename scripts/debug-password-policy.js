const { PasswordPolicyConstraint } = require('./backend/src/validators/password-policy.validator');

const validator = new PasswordPolicyConstraint();

const testPasswords = [
  'MySecurePass123!',
  'AnotherValid1@',
  'StrongPassword9#',
  'ComplexPass1$',
  'ValidTest123!'
];

console.log('🔍 Depurando PasswordPolicyConstraint\n');

testPasswords.forEach(password => {
  const mockArgs = {
    value: password,
    constraints: [],
    targetName: 'User',
    property: 'password',
    object: {}
  };
  
  try {
    const result = validator.validate(password, mockArgs);
    const errors = validator.getDetailedErrors ? validator.getDetailedErrors(password) : ['método no disponible'];
    
    console.log(`📝 Contraseña: "${password}"`);
    console.log(`✅ Válida: ${result}`);
    console.log(`❌ Errores: ${errors.length ? errors.join(', ') : 'Ning<PERSON>'}`);
    console.log('---');
  } catch (error) {
    console.log(`📝 Contraseña: "${password}"`);
    console.log(`❌ Error: ${error.message}`);
    console.log('---');
  }
}); 