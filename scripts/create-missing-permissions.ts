const path = require('path');
const fs = require('fs');
const yaml = require('js-yaml');

// Detectar si existe el build transpilado
let AppDataSource;
const backendPath = path.join(process.cwd(), 'backend');
const distOrmconfig = path.join(backendPath, 'dist', 'ormconfig.js');
if (fs.existsSync(distOrmconfig)) {
  // Usar build JS
  ({ AppDataSource } = require(distOrmconfig));
} else {
  // Usar fuente TS
  ({ AppDataSource } = require(path.join(backendPath, 'ormconfig')));
}

async function createMissingPermissions() {
  try {
    // 1. Leer el YAML para extraer todos los permisos únicos
    const yamlPath = path.join(__dirname, 'role-permissions-map.yaml');
    const mapping = yaml.load(fs.readFileSync(yamlPath, 'utf8')) as Record<string, string[]>;

    // Extraer todos los permisos únicos del mapeo
    const allPermissions = new Set<string>();
    Object.values(mapping).forEach(permissions => {
      permissions.forEach(permission => allPermissions.add(permission));
    });

    console.log(`📋 Total de permisos únicos en el mapeo: ${allPermissions.size}`);

    // 2. Inicializar conexión
    await AppDataSource.initialize();
    console.log('✅ Conexión a la base de datos establecida');

    const queryRunner = AppDataSource.createQueryRunner();

    // 3. Verificar qué permisos ya existen
    const existingPermissionsQuery = `SELECT name FROM permissions`;
    const existingPermissions = await queryRunner.query(existingPermissionsQuery);
    const existingNames = new Set(existingPermissions.map((p: any) => p.name));

    console.log(`✅ Permisos existentes en BD: ${existingNames.size}`);

    // 4. Determinar permisos faltantes
    const missingPermissions = Array.from(allPermissions).filter(
      permission => !existingNames.has(permission)
    );

    console.log(`🔍 Permisos faltantes: ${missingPermissions.length}`);

    if (missingPermissions.length === 0) {
      console.log('✅ Todos los permisos ya existen en la base de datos');
      await AppDataSource.destroy();
      return;
    }

    // 5. Crear permisos faltantes
    console.log('\n📝 Creando permisos faltantes:');
    let created = 0;

    for (const permissionName of missingPermissions) {
      try {
        // Parsear el formato module:resource:action
        const parts = permissionName.split(':');
        if (parts.length !== 3) {
          console.warn(`⚠️  Formato inválido para permiso: ${permissionName} (esperado: module:resource:action)`);
          continue;
        }

        const [module, resource, action] = parts;

        // Insertar el nuevo permiso usando estructura real de la tabla
        // action debe contener "resource:action" según el esquema actual
        const insertQuery = `
          INSERT INTO permissions (name, module, action, description)
          VALUES ($1, $2, $3, $4)
        `;
        
        const fullAction = `${resource}:${action}`;
        const description = `${action.charAt(0).toUpperCase() + action.slice(1)} ${resource} in ${module} module`;
        
        await queryRunner.query(insertQuery, [
          permissionName,
          module,
          fullAction,
          description
        ]);

        created++;
        console.log(`  ✅ ${permissionName} (module: ${module}, action: ${fullAction})`);
      } catch (error: any) {
        console.error(`  ❌ Error creando ${permissionName}:`, error.message);
      }
    }

    console.log(`\n🎉 ¡Completado! Creados ${created} de ${missingPermissions.length} permisos faltantes`);

    await queryRunner.release();
    await AppDataSource.destroy();

  } catch (error: any) {
    console.error('❌ Error en createMissingPermissions:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
}

// Ejecutar el script
createMissingPermissions(); 