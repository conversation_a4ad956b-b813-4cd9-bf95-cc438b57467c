#!/bin/bash

# Script para limpiar archivos obsoletos del proyecto
# Ejecutar con --dry-run primero para revisar

set -e

# Colores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

DRY_RUN=true
if [[ "$1" == "--force" ]]; then
  DRY_RUN=false
fi

echo "=== Limpieza de archivos obsoletos ==="
echo ""

# Función para eliminar archivo
remove_file() {
  local file=$1
  local reason=$2
  
  if [[ -f "$file" ]]; then
    if [[ "$DRY_RUN" == "true" ]]; then
      echo -e "${YELLOW}[DRY-RUN]${NC} Eliminaría: $file"
      echo -e "  Razón: $reason"
    else
      echo -e "${RED}[ELIMINANDO]${NC} $file"
      rm -f "$file"
    fi
  fi
}

# Scripts obsoletos del backend (reemplazados por el nuevo sistema)
echo "📁 Scripts obsoletos del backend:"
remove_file "backend/scripts/add-sistemas-permission.ts" "Reemplazado por sistema de permisos YAML"
remove_file "backend/scripts/add-users-read-permission.ts" "Reemplazado por sistema de permisos YAML"
remove_file "backend/scripts/assign-sistemas-permissions.ts" "Reemplazado por initialize-roles.ts"
remove_file "backend/scripts/create-departmental-permissions.ts" "Reemplazado por permissions.yaml"
remove_file "backend/scripts/create-departmental-roles.ts" "Reemplazado por initialize-roles.ts"
remove_file "backend/scripts/create-missing-permissions.ts" "Reemplazado por initialize-permissions.ts"
remove_file "backend/scripts/create-test-users.ts" "Mantener para desarrollo"
remove_file "backend/scripts/update-role-name-length.ts" "Migración ya aplicada"
remove_file "backend/scripts/reset-admin-password.ts" "Mantener para emergencias"

# Scripts duplicados en src/scripts
echo ""
echo "📁 Scripts duplicados en src/scripts:"
remove_file "backend/src/scripts/initialize-permissions.ts" "Usar backend/scripts/initialize-permissions.ts"
remove_file "backend/src/scripts/initialize-roles.ts" "Usar backend/scripts/initialize-roles.ts"
remove_file "backend/src/scripts/migrate-roles.ts" "Migración completada"
remove_file "backend/src/scripts/check-admin-role.ts" "Script de verificación temporal"
remove_file "backend/src/scripts/check-admin-user.ts" "Script de verificación temporal"
remove_file "backend/src/scripts/check-roles.ts" "Script de verificación temporal"
remove_file "backend/src/scripts/assign-backup-permissions.ts" "Permisos ya en sistema nuevo"

# Archivos de migración SQL aplicados
echo ""
echo "📁 Scripts SQL ya aplicados:"
remove_file "backend/scripts/fix-function-security.sql" "Ya aplicado en BD"
remove_file "backend/scripts/fix-timezone-columns.sql" "Ya aplicado en BD"

# Archivos temporales y obsoletos
echo ""
echo "📁 Archivos temporales:"
remove_file "cursor.deb" "Instalador descargado"
remove_file "backend/update-env-supabase.sh" "Script temporal"
remove_file "backend/update-env-with-real-credentials.sh" "Script temporal con credenciales"
remove_file "backend/update-test-schema.sql" "Schema test temporal"

# Controllers y archivos backup
echo ""
echo "📁 Archivos backup obsoletos:"
remove_file "backend/src/controllers/user.controller.backup.ts" "Backup antiguo"
remove_file "backend/src/controllers/user.controller.updated.ts" "Versión temporal"

# Archivos de tipos duplicados
echo ""
echo "📁 Archivos de tipos duplicados:"
if [[ -d "backend/src/types" && -d "backend/src/@types" ]]; then
  echo -e "${YELLOW}Detectado:${NC} Directorios de tipos duplicados"
  if [[ "$DRY_RUN" == "false" ]]; then
    # Mover contenido de types a @types si es necesario
    echo -e "${RED}[FUSIONANDO]${NC} types → @types"
  fi
fi

# Generar resumen
echo ""
echo "=== Resumen ==="
if [[ "$DRY_RUN" == "true" ]]; then
  echo -e "${YELLOW}Modo DRY-RUN${NC}: No se eliminó nada. Usa --force para ejecutar."
  echo ""
  echo "💡 Recomendaciones adicionales:"
  echo "  1. Actualizar esquema BD: scripts/update-db-schema.sh"
  echo "  2. Consolidar tests: npm run test:cleanup"
  echo "  3. Verificar imports: npm run lint:fix"
else
  echo -e "${GREEN}Limpieza completada${NC}"
fi 