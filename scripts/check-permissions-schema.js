const path = require('path');
// Detectar si existe el build transpilado
let AppDataSource;
const backendPath = path.join(process.cwd(), 'backend');
const distOrmconfig = path.join(backendPath, 'dist', 'ormconfig.js');
if (require('fs').existsSync(distOrmconfig)) {
    // Usar build JS
    ({ AppDataSource } = require(distOrmconfig));
}
else {
    // Usar fuente TS
    ({ AppDataSource } = require(path.join(backendPath, 'ormconfig')));
}
async function checkPermissionsSchema() {
    try {
        await AppDataSource.initialize();
        console.log('✅ Conexión a la base de datos establecida');
        const queryRunner = AppDataSource.createQueryRunner();
        // Obtener la estructura de la tabla permissions
        const schemaQuery = `
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'permissions' 
      ORDER BY ordinal_position;
    `;
        const columns = await queryRunner.query(schemaQuery);
        console.log('\n📋 Estructura actual de la tabla permissions:');
        console.log('='.repeat(60));
        columns.forEach((col) => {
            console.log(`${col.column_name.padEnd(20)} | ${col.data_type.padEnd(15)} | ${col.is_nullable.padEnd(8)} | ${col.column_default || 'NULL'}`);
        });
        // También verificar algunos permisos existentes para ver el formato
        const sampleQuery = `SELECT * FROM permissions LIMIT 5`;
        const samplePerms = await queryRunner.query(sampleQuery);
        console.log('\n📋 Muestra de permisos existentes:');
        console.log('='.repeat(60));
        samplePerms.forEach((perm) => {
            console.log(JSON.stringify(perm, null, 2));
        });
        await queryRunner.release();
        await AppDataSource.destroy();
    }
    catch (error) {
        console.error('❌ Error:', error.message);
        process.exit(1);
    }
}
checkPermissionsSchema();
