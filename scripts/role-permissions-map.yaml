# Mapeo de roles a permisos para COMINTEC
# Usando los nombres REALES de permisos que existen en la BD

# =============================================
# ROLE_ADMIN - Acceso total al sistema
# =============================================
ROLE_ADMIN:
  # SISTEMAS - Gestión completa
  - sistemas:users:create
  - sistemas:users:read
  - sistemas:users:update
  - sistemas:users:delete
  - sistemas:users:list
  - sistemas:roles:create
  - sistemas:roles:read
  - sistemas:roles:update
  - sistemas:roles:delete
  - sistemas:roles:list
  - sistemas:roles:assign
  - sistemas:permissions:create
  - sistemas:permissions:read
  - sistemas:permissions:update
  - sistemas:permissions:list
  - sistemas:backups:create
  - sistemas:backups:read
  - sistemas:audit_logs:read
  - sistemas:notifications:create
  - sistemas:notifications:read
  
  # ALMACEN - Gestión completa
  - almacen:stock:read
  - almacen:stock:update
  - almacen:stock:create
  - almacen:products:create
  - almacen:products:read
  - almacen:products:update
  - almacen:entradas:create
  - almacen:entradas:read
  - almacen:salidas:create
  - almacen:salidas:read
  - almacen:qr:read
  - almacen:qr:write
  
  # VENTAS - Gestión completa
  - ventas:clients:create
  - ventas:clients:read
  - ventas:clients:update
  - ventas:quotes:create
  - ventas:quotes:read
  - ventas:quotes:update
  - ventas:orders:create
  - ventas:orders:read
  - ventas:invoices:read
  
  # INFORMES
  - informes:folios:assign
  - informes:estadisticos:read
  - informes:historial:read
  
  # ADMIN
  - admin:budgets:read
  - admin:budgets:approve
  - admin:facturas:read
  - admin:morosos:read

# =============================================
# ROLE_SISTEMAS - Administrador de sistemas
# =============================================
ROLE_SISTEMAS:
  - sistemas:users:create
  - sistemas:users:read
  - sistemas:users:update
  - sistemas:users:list
  - sistemas:roles:read
  - sistemas:roles:list
  - sistemas:permissions:read
  - sistemas:permissions:list
  - sistemas:backups:create
  - sistemas:backups:read
  - sistemas:audit_logs:read
  - sistemas:notifications:create
  - sistemas:notifications:read

# =============================================
# ROLE_ALMACEN - Gestión de inventarios
# =============================================
ROLE_ALMACEN:
  - almacen:stock:read
  - almacen:stock:update
  - almacen:products:read
  - almacen:products:list
  - almacen:entradas:create
  - almacen:entradas:read
  - almacen:salidas:create
  - almacen:salidas:read
  - almacen:inventory:read
  - almacen:qr:read
  - almacen:qr:write
  - almacen:qr:generate
  - almacen:qr:scan
  - almacen:movements:read
  - almacen:categories:read

# =============================================
# ROLE_VENTAS - Gestión de ventas
# =============================================
ROLE_VENTAS:
  - ventas:clients:create
  - ventas:clients:read
  - ventas:clients:update
  - ventas:clients:list
  - ventas:quotes:create
  - ventas:quotes:read
  - ventas:quotes:update
  - ventas:orders:create
  - ventas:orders:read
  - ventas:invoices:read
  - ventas:dashboard:read
  - almacen:stock:read
  - almacen:products:read

# =============================================
# ROLE_LOGISTICA - Gestión logística
# =============================================
ROLE_LOGISTICA:
  - logistica:services:create
  - logistica:services:read
  - logistica:services:update
  - logistica:services:schedule
  - logistica:calendar:read
  - logistica:calendar:update
  - logistica:routes:read
  - logistica:vehicles:read
  - logistica:drivers:read
  - logistica:shipments:create
  - logistica:shipments:read
  - logistica:estadisticos:read
  - almacen:stock:read

# =============================================
# ROLE_CALIDAD - Gestión de calidad
# =============================================
ROLE_CALIDAD:
  - quality:incidencias:create
  - quality:incidencias:read
  - quality:incidencias:update
  - quality:procedures:create
  - quality:procedures:read
  - quality:procedures:update
  - quality:audits:create
  - quality:audits:read
  - quality:non_conformities:create
  - quality:non_conformities:read
  - quality:corrective_actions:create
  - quality:corrective_actions:read
  - quality:reports:generate
  - quality:reports:view

# =============================================
# ROLE_RH - Recursos Humanos
# =============================================
ROLE_RH:
  - rh:employees:create
  - rh:employees:read
  - rh:employees:update
  - rh:employees:list
  - rh:training:create
  - rh:training:read
  - rh:training:update
  - rh:training:schedule
  - rh:evaluations:create
  - rh:evaluations:read
  - rh:files:create
  - rh:files:read
  - rh:contracts:create
  - rh:contracts:read
  - rh:benefits:read
  - rh:vacations:read

# =============================================
# ROLE_METROLOGIA - Metrología
# =============================================
ROLE_METROLOGIA:
  - metrologia:calibrations:create
  - metrologia:calibrations:read
  - metrologia:calibrations:update
  - metrologia:calibrations:schedule
  - metrologia:certificates:create
  - metrologia:certificates:read
  - metrologia:equipment:create
  - metrologia:equipment:read
  - metrologia:equipment:update
  - metrologia:standards:read
  - metrologia:capacitaciones:create
  - metrologia:capacitaciones:read
  - metrologia:procedures:read
  - metrologia:reports:generate

# =============================================
# ROLE_COMPRAS - Gestión de compras
# =============================================
ROLE_COMPRAS:
  - compras:requests:create
  - compras:requests:read
  - compras:requests:update
  - compras:requests:list
  - compras:orders:create
  - compras:orders:read
  - compras:orders:update
  - compras:suppliers:create
  - compras:suppliers:read
  - compras:suppliers:list
  - compras:quotes:create
  - compras:quotes:read
  - compras:budgets:read
  - compras:reports:view
  - almacen:suppliers:read

# =============================================
# ROLE_INFORMES - Gestión de informes
# =============================================
ROLE_INFORMES:
  - informes:folios:assign
  - informes:folios:create
  - informes:folios:read
  - informes:estadisticos:read
  - informes:estadisticos:generate
  - informes:historial:read
  - informes:reports:create
  - informes:reports:read
  - informes:dashboards:read

# =============================================
# ROLE_ADMINISTRACION - Gestión administrativa
# =============================================
ROLE_ADMINISTRACION:
  # Contratos REPSE
  - admin:contratos:create
  - admin:contratos:read
  - admin:contratos:update
  - admin:contratos:list
  - admin:contratos:approve
  - admin:contratos:renew

  # Asignación Fiscal
  - admin:fiscal:create
  - admin:fiscal:read
  - admin:fiscal:update
  - admin:fiscal:list
  - admin:fiscal:assign

  # Facturas
  - admin:facturas:create
  - admin:facturas:read
  - admin:facturas:update
  - admin:facturas:list
  - admin:facturas:approve
  - admin:facturas:validate

  # Crédito
  - admin:credito:create
  - admin:credito:read
  - admin:credito:update
  - admin:credito:list
  - admin:credito:approve
  - admin:credito:analyze

  # Morosos
  - admin:morosos:read
  - admin:morosos:update
  - admin:morosos:list
  - admin:morosos:approve
  - admin:morosos:liberate

  # Gráficos
  - admin:graficos:read
  - admin:graficos:export
  - admin:graficos:configure

  # Viáticos
  - admin:viaticos:create
  - admin:viaticos:read
  - admin:viaticos:update
  - admin:viaticos:list
  - admin:viaticos:approve

  # Tiempo Extra
  - admin:tiempo_extra:create
  - admin:tiempo_extra:read
  - admin:tiempo_extra:update
  - admin:tiempo_extra:list
  - admin:tiempo_extra:approve

  # Paquetería
  - admin:paqueteria:create
  - admin:paqueteria:read
  - admin:paqueteria:update
  - admin:paqueteria:list
  - admin:paqueteria:track

# =============================================
# ROLE_CLIENTES - Portal de clientes
# =============================================
ROLE_CLIENTES:
  - clients:profile:read
  - clients:profile:update
  - clients:services:read
  - clients:services:request
  - clients:quotes:read
  - clients:quotes:request
  - clients:orders:read
  - clients:orders:track
  - clients:invoices:read
  - clients:invoices:download
  - clients:support:create
  - clients:support:read
  - clients:documents:read
  - clients:documents:download
  - clients:notifications:read

# =============================================
# ROLE_SERVICIO - Servicio técnico
# =============================================
ROLE_SERVICIO:
  - logistica:services:create
  - logistica:services:read
  - logistica:services:update
  - logistica:services:schedule
  - logistica:services:complete
  - logistica:calendar:read
  - logistica:calendar:update
  - metrologia:calibrations:read
  - metrologia:calibrations:update
  - metrologia:equipment:read
  - metrologia:maintenance:create
  - metrologia:maintenance:read
  - quality:procedures:read

# =============================================
# ROLE_TECNICO - Técnico especializado
# =============================================
ROLE_TECNICO:
  - metrologia:calibrations:create
  - metrologia:calibrations:read
  - metrologia:calibrations:update
  - metrologia:calibrations:complete
  - metrologia:certificates:read
  - metrologia:equipment:read
  - metrologia:equipment:update
  - metrologia:maintenance:read
  - metrologia:maintenance:complete
  - quality:procedures:read
  - logistica:services:read
  - logistica:services:update

# =============================================
# ROLE_MARKETING - Marketing y promoción
# =============================================
ROLE_MARKETING:
  - ventas:clients:read
  - ventas:clients:list
  - ventas:dashboard:read
  - ventas:reports:view
  - informes:estadisticos:read
  - informes:reports:read
  - admin:graficos:read
  - admin:graficos:configure

# =============================================
# ROLES MANAGER - Gerentes de área
# =============================================

# ROLE_ADMINISTRACION_MANAGER
ROLE_ADMINISTRACION_MANAGER:
  - admin:budgets:create
  - admin:budgets:read
  - admin:budgets:update
  - admin:budgets:approve
  - admin:budgets:monitor
  - admin:facturas:read
  - admin:facturas:create
  - admin:facturas:approve
  - admin:contratos_repse:read
  - admin:contratos_repse:approve
  - admin:solicitudes:read
  - admin:solicitudes:approve
  - admin:morosos:read
  - admin:viaticos:approve
  - informes:estadisticos:read

# ROLE_ALMACEN_MANAGER
ROLE_ALMACEN_MANAGER:
  - almacen:stock:read
  - almacen:stock:update
  - almacen:stock:create
  - almacen:products:create
  - almacen:products:read
  - almacen:products:update
  - almacen:entradas:create
  - almacen:entradas:read
  - almacen:entradas:approve
  - almacen:salidas:create
  - almacen:salidas:read
  - almacen:salidas:approve
  - almacen:inventory:read
  - almacen:inventory:audit
  - almacen:reports:generate
  - almacen:movements:approve

# ROLE_VENTAS_MANAGER
ROLE_VENTAS_MANAGER:
  - ventas:clients:create
  - ventas:clients:read
  - ventas:clients:update
  - ventas:clients:approve
  - ventas:quotes:create
  - ventas:quotes:read
  - ventas:quotes:update
  - ventas:quotes:approve
  - ventas:orders:create
  - ventas:orders:read
  - ventas:orders:approve
  - ventas:invoices:read
  - ventas:contracts:read
  - ventas:contracts:approve
  - ventas:reports:generate
  - ventas:dashboard:configure

# ROLE_LOGISTICA_MANAGER
ROLE_LOGISTICA_MANAGER:
  - logistica:services:create
  - logistica:services:read
  - logistica:services:update
  - logistica:services:approve
  - logistica:calendar:read
  - logistica:calendar:schedule
  - logistica:routes:create
  - logistica:routes:read
  - logistica:routes:optimize
  - logistica:vehicles:read
  - logistica:vehicles:maintain
  - logistica:drivers:read
  - logistica:drivers:assign
  - logistica:shipments:read
  - logistica:estadisticos:read

# ROLE_CALIDAD_MANAGER
ROLE_CALIDAD_MANAGER:
  - quality:incidencias:create
  - quality:incidencias:read
  - quality:incidencias:update
  - quality:incidencias:approve
  - quality:procedures:create
  - quality:procedures:read
  - quality:procedures:update
  - quality:procedures:approve
  - quality:audits:create
  - quality:audits:read
  - quality:audits:schedule
  - quality:audits:approve
  - quality:non_conformities:read
  - quality:corrective_actions:approve
  - quality:reports:generate

# ROLE_METROLOGIA_MANAGER
ROLE_METROLOGIA_MANAGER:
  - metrologia:calibrations:create
  - metrologia:calibrations:read
  - metrologia:calibrations:update
  - metrologia:calibrations:schedule
  - metrologia:calibrations:approve
  - metrologia:certificates:create
  - metrologia:certificates:read
  - metrologia:certificates:approve
  - metrologia:equipment:create
  - metrologia:equipment:read
  - metrologia:equipment:update
  - metrologia:equipment:approve
  - metrologia:standards:read
  - metrologia:standards:manage
  - metrologia:capacitaciones:create
  - metrologia:capacitaciones:read
  - metrologia:capacitaciones:schedule
  - metrologia:procedures:read
  - metrologia:procedures:approve
  - metrologia:reports:generate
  - metrologia:reports:approve

# ROLE_RH_MANAGER
ROLE_RH_MANAGER:
  - rh:employees:create
  - rh:employees:read
  - rh:employees:update
  - rh:employees:list
  - rh:employees:approve
  - rh:training:create
  - rh:training:read
  - rh:training:update
  - rh:training:schedule
  - rh:training:approve
  - rh:evaluations:create
  - rh:evaluations:read
  - rh:evaluations:approve
  - rh:files:create
  - rh:files:read
  - rh:files:approve
  - rh:contracts:create
  - rh:contracts:read
  - rh:contracts:approve
  - rh:benefits:read
  - rh:benefits:manage
  - rh:vacations:read
  - rh:vacations:approve
  - rh:reports:generate

# ROLE_SERVICIO_MANAGER
ROLE_SERVICIO_MANAGER:
  - logistica:services:create
  - logistica:services:read
  - logistica:services:update
  - logistica:services:schedule
  - logistica:services:complete
  - logistica:services:approve
  - logistica:calendar:read
  - logistica:calendar:update
  - logistica:calendar:schedule
  - metrologia:calibrations:read
  - metrologia:calibrations:update
  - metrologia:calibrations:schedule
  - metrologia:equipment:read
  - metrologia:equipment:assign
  - metrologia:maintenance:create
  - metrologia:maintenance:read
  - metrologia:maintenance:approve
  - quality:procedures:read
  - quality:procedures:approve
  - logistica:services:assign_technician
  - logistica:reports:generate

# ROLE_INFORMES_MANAGER
ROLE_INFORMES_MANAGER:
  - informes:folios:assign
  - informes:folios:create
  - informes:folios:read
  - informes:folios:approve
  - informes:estadisticos:read
  - informes:estadisticos:generate
  - informes:estadisticos:configure
  - informes:historial:read
  - informes:historial:audit
  - informes:reports:create
  - informes:reports:read
  - informes:reports:approve
  - informes:dashboards:read
  - informes:dashboards:configure
  - informes:access_control:manage

# =============================================
# ROLES USER - Usuarios operativos
# =============================================

# ROLE_ADMINISTRACION_USER
ROLE_ADMINISTRACION_USER:
  - admin:facturas:read
  - admin:solicitudes:read
  - admin:morosos:read
  - admin:viaticos:read
  - admin:paqueteria:read

# ROLE_ALMACEN_USER
ROLE_ALMACEN_USER:
  - almacen:stock:read
  - almacen:products:read
  - almacen:entradas:create
  - almacen:entradas:read
  - almacen:salidas:create
  - almacen:salidas:read
  - almacen:qr:read
  - almacen:qr:scan
  - almacen:movements:read

# ROLE_VENTAS_USER
ROLE_VENTAS_USER:
  - ventas:clients:read
  - ventas:clients:list
  - ventas:quotes:create
  - ventas:quotes:read
  - ventas:orders:create
  - ventas:orders:read
  - ventas:invoices:read
  - almacen:stock:read
  - almacen:products:read

# ROLE_LOGISTICA_USER
ROLE_LOGISTICA_USER:
  - logistica:services:read
  - logistica:services:update
  - logistica:calendar:read
  - logistica:routes:read
  - logistica:shipments:read
  - logistica:shipments:track

# ROLE_CALIDAD_USER
ROLE_CALIDAD_USER:
  - quality:incidencias:create
  - quality:incidencias:read
  - quality:procedures:read
  - quality:non_conformities:create
  - quality:non_conformities:read
  - quality:corrective_actions:create
  - quality:corrective_actions:read

# ROLE_METROLOGIA_USER
ROLE_METROLOGIA_USER:
  - metrologia:calibrations:read
  - metrologia:calibrations:update
  - metrologia:certificates:read
  - metrologia:equipment:read
  - metrologia:procedures:read

# ROLE_RH_USER
ROLE_RH_USER:
  - rh:employees:read
  - rh:training:read
  - rh:evaluations:read
  - rh:files:read
  - rh:contracts:read
  - rh:benefits:read
  - rh:vacations:read

# ROLE_SERVICIO_USER
ROLE_SERVICIO_USER:
  - logistica:services:read
  - logistica:services:update
  - logistica:calendar:read
  - metrologia:calibrations:read
  - metrologia:equipment:read

# ROLE_INFORMES_USER
ROLE_INFORMES_USER:
  - informes:folios:read
  - informes:estadisticos:read
  - informes:historial:read
  - informes:reports:read

# =============================================
# ROLES LEGACY - Para compatibilidad
# =============================================

# ROLE_ENGINEER
ROLE_ENGINEER:
  - metrologia:calibrations:create
  - metrologia:calibrations:read
  - metrologia:equipment:read
  - metrologia:equipment:update
  - quality:procedures:read
  - logistica:services:read

# ROLE_SALES
ROLE_SALES:
  - ventas:clients:read
  - ventas:quotes:create
  - ventas:quotes:read
  - ventas:orders:create
  - ventas:orders:read
  - almacen:stock:read

# ROLE_WAREHOUSE
ROLE_WAREHOUSE:
  - almacen:stock:read
  - almacen:stock:update
  - almacen:products:read
  - almacen:entradas:create
  - almacen:salidas:create
  - almacen:qr:read
  - almacen:qr:scan

# ROLE_FINANCE
ROLE_FINANCE:
  - admin:budgets:read
  - admin:facturas:read
  - admin:morosos:read
  - informes:estadisticos:read

# ROLE_EXAMPLE, ROLE_FULLADMIN - Sin permisos específicos por ser de prueba 