#!/bin/bash

# Configuración de Supabase
export SUPABASE_URL=${SUPABASE_URL:-"https://awlxzhrubqkryrenunun.supabase.co"}
export SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY:-"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF3bHh6aHJ1YnFrcnlyZW51bnVuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA0NzAzOTIsImV4cCI6MjA2NjA0NjM5Mn0.0LCZRkXz88rQwh_kEqA7wysZpj2GQ-NX5qPEYaJN-po"}
export SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY:-"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF3bHh6aHJ1YnFrcnlyZW51bnVuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDQ3MDM5MiwiZXhwIjoyMDY2MDQ2MzkyfQ.lryuBGnXRCv1jEyZlG4B0VTXTuN8l47uC6HiHr4swmU"}
export JWT_SECRET="test-secret"
export NEXT_PUBLIC_API_URL="http://localhost:3000"
export NEXT_PUBLIC_SUPABASE_URL=${SUPABASE_URL}
export NEXT_PUBLIC_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}

# Función para limpiar procesos
cleanup() {
  echo "🧹 Limpiando procesos..."
  pkill -f "node.*backend"
  pkill -f "node.*frontend"
  exit 0
}

# Capturar señal de interrupción
trap cleanup SIGINT

echo "🚀 Iniciando tests E2E..."

# Construir el proyecto
echo "📦 Construyendo el proyecto..."
npm run build

# Ejecutar los tests
echo "🧪 Ejecutando tests..."
npm run test:e2e

# Capturar código de salida
EXIT_CODE=$?

# Limpiar procesos
cleanup

# Salir con el código de los tests
exit $EXIT_CODE 