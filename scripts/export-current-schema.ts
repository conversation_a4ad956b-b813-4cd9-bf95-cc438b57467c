const path = require('path');
const fs = require('fs');

// Detectar si existe el build transpilado
let AppDataSource;
const backendPath = path.join(process.cwd(), 'backend');
const distOrmconfig = path.join(backendPath, 'dist', 'ormconfig.js');
if (fs.existsSync(distOrmconfig)) {
  // Usar build JS
  ({ AppDataSource } = require(distOrmconfig));
} else {
  // Usar fuente TS
  ({ AppDataSource } = require(path.join(backendPath, 'ormconfig')));
}

async function exportCurrentSchema() {
  try {
    await AppDataSource.initialize();
    console.log('✅ Conexión a la base de datos establecida');

    const queryRunner = AppDataSource.createQueryRunner();

    // 1. Exportar estructura de todas las tablas principales
    const tables = ['users', 'roles', 'permissions', 'user_roles', 'role_permissions', 'notifications', 'audit_logs'];
    
    console.log('\n📋 Generando comintec_schema_core.sql actualizado...');
    
    let coreSchemaSQL = `-- Core Tables for Comintec Application
-- Generated on ${new Date().toISOString()}
-- Updated with complete permissions and roles structure

`;

    for (const tableName of tables) {
      console.log(`  📄 Exportando tabla: ${tableName}`);
      
      // Obtener definición de la tabla
      const tableDefQuery = `
        SELECT 
          column_name,
          data_type,
          character_maximum_length,
          is_nullable,
          column_default
        FROM information_schema.columns 
        WHERE table_name = $1 
        ORDER BY ordinal_position;
      `;
      
      const columns = await queryRunner.query(tableDefQuery, [tableName]);
      
      // Obtener constraints
      const constraintsQuery = `
        SELECT 
          tc.constraint_name,
          tc.constraint_type,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        LEFT JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
        WHERE tc.table_name = $1;
      `;
      
      const constraints = await queryRunner.query(constraintsQuery, [tableName]);
      
      // Generar CREATE TABLE
      coreSchemaSQL += `\n-- ${tableName.charAt(0).toUpperCase() + tableName.slice(1)} table\n`;
      coreSchemaSQL += `CREATE TABLE IF NOT EXISTS ${tableName} (\n`;
      
      const columnDefs = columns.map((col: any) => {
        let def = `    ${col.column_name} `;
        
        // Tipo de dato
        if (col.data_type === 'character varying' && col.character_maximum_length) {
          def += `VARCHAR(${col.character_maximum_length})`;
        } else if (col.data_type === 'bigint') {
          def += 'SERIAL PRIMARY KEY';
        } else if (col.data_type === 'integer') {
          def += 'SERIAL PRIMARY KEY';
        } else if (col.data_type === 'timestamp with time zone') {
          def += 'TIMESTAMP';
        } else {
          def += col.data_type.toUpperCase();
        }
        
        // Nullable
        if (col.is_nullable === 'NO' && !col.column_default?.includes('nextval')) {
          def += ' NOT NULL';
        }
        
        // Default
        if (col.column_default && !col.column_default.includes('nextval')) {
          if (col.column_default === 'CURRENT_TIMESTAMP') {
            def += ' DEFAULT CURRENT_TIMESTAMP';
          } else if (col.column_default.includes('true')) {
            def += ' DEFAULT true';
          } else if (col.column_default.includes('false')) {
            def += ' DEFAULT false';
          } else {
            def += ` DEFAULT ${col.column_default}`;
          }
        }
        
        return def;
      });
      
      coreSchemaSQL += columnDefs.join(',\n') + '\n';
      coreSchemaSQL += `);\n\n`;
    }

    // 2. Agregar datos iniciales críticos (roles y permisos)
    console.log('  📄 Exportando datos iniciales...');
    
    const roles = await queryRunner.query('SELECT * FROM roles ORDER BY id');
    const permissions = await queryRunner.query('SELECT * FROM permissions ORDER BY id');
    
    coreSchemaSQL += `-- Initial Roles Data\n`;
    roles.forEach((role: any) => {
      coreSchemaSQL += `INSERT INTO roles (name) VALUES ('${role.name}') ON CONFLICT (name) DO NOTHING;\n`;
    });
    
    coreSchemaSQL += `\n-- Initial Permissions Data\n`;
    permissions.forEach((perm: any) => {
      const description = perm.description?.replace(/'/g, "''") || '';
      coreSchemaSQL += `INSERT INTO permissions (name, module, action, description) VALUES ('${perm.name}', '${perm.module}', '${perm.action}', '${description}') ON CONFLICT (name) DO NOTHING;\n`;
    });

    // 3. Generar índices actualizados
    console.log('\n📋 Generando comintec_schema_data_indexes.sql actualizado...');
    
    let indexesSQL = `-- Performance Indexes for Comintec Application
-- Generated on ${new Date().toISOString()}
-- Optimized for current schema structure

`;

    // Índices básicos para performance
    const indexQueries = [
      `-- Users table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_active ON users (email, active) WHERE active = true;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_status_active ON users (status, active) WHERE active = true;`,
      
      `-- Junction table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_roles_user_id ON user_roles (user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_roles_role_id ON user_roles (role_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_role_permissions_role_id ON role_permissions (role_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_role_permissions_permission_id ON role_permissions (permission_id);`,
      
      `-- Permissions indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_permissions_module_action ON permissions (module, action);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_permissions_name_unique ON permissions (name);`,
      
      `-- Notifications indexes (if table exists)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_target_user ON notifications (target_user_id) WHERE target_user_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_status ON notifications (status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_created_at ON notifications (created_at DESC);`,
      
      `-- Audit logs indexes (if table exists)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_user_id ON audit_logs (user_id) WHERE user_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_target_entity ON audit_logs (target_entity, target_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_created_at ON audit_logs (created_at DESC);`,
      
      `-- Update statistics for query planner
ANALYZE users;
ANALYZE roles;
ANALYZE permissions;
ANALYZE user_roles;
ANALYZE role_permissions;`
    ];
    
    indexesSQL += indexQueries.join('\n\n') + '\n';

    // 4. Escribir archivos
    const coreSchemaPath = path.join(backendPath, 'comintec_schema_core.sql');
    const indexesPath = path.join(backendPath, 'comintec_schema_data_indexes.sql');
    
    fs.writeFileSync(coreSchemaPath, coreSchemaSQL);
    fs.writeFileSync(indexesPath, indexesSQL);
    
    console.log('\n✅ Archivos SQL actualizados:');
    console.log(`  📄 ${coreSchemaPath}`);
    console.log(`  📄 ${indexesPath}`);
    
    console.log('\n📊 Estadísticas actuales:');
    console.log(`  🔑 Roles: ${roles.length}`);
    console.log(`  🔐 Permisos: ${permissions.length}`);
    
    await queryRunner.release();
    await AppDataSource.destroy();
    
    console.log('\n🎉 Exportación del esquema completada exitosamente');

  } catch (error: any) {
    console.error('❌ Error en exportCurrentSchema:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
}

// Ejecutar el script
exportCurrentSchema(); 