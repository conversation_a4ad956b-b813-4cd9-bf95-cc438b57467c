#!/bin/bash

# Script para generar y actualizar esquemas de BD
# Requiere conexión a Supabase/PostgreSQL

set -e

# Colores
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== Actualización de Esquema de BD ===${NC}"
echo ""

# Verificar que estamos en el directorio correcto
if [[ ! -f "backend/package.json" ]]; then
  echo "Error: Ejecutar desde la raíz del proyecto"
  exit 1
fi

# Solicitar credenciales si no están en env
if [[ -z "$DATABASE_URL" ]]; then
  echo -e "${YELLOW}DATABASE_URL no encontrada${NC}"
  echo "Ingrese la URL de conexión a PostgreSQL:"
  read -r DATABASE_URL
  export DATABASE_URL
fi

# Extraer componentes de la URL
if [[ "$DATABASE_URL" =~ ^postgres(ql)?://([^:]+):([^@]+)@([^:]+):([^/]+)/(.+)$ ]]; then
  DB_USER="${BASH_REMATCH[2]}"
  DB_PASS="${BASH_REMATCH[3]}"
  DB_HOST="${BASH_REMATCH[4]}"
  DB_PORT="${BASH_REMATCH[5]}"
  DB_NAME="${BASH_REMATCH[6]}"
else
  echo "Error: URL de base de datos inválida"
  exit 1
fi

# Función para ejecutar pg_dump
dump_schema() {
  local output_file=$1
  local options=$2
  
  echo -e "${BLUE}Generando: $output_file${NC}"
  
  PGPASSWORD="$DB_PASS" pg_dump \
    -h "$DB_HOST" \
    -p "$DB_PORT" \
    -U "$DB_USER" \
    -d "$DB_NAME" \
    $options \
    --no-owner \
    --no-privileges \
    > "$output_file"
    
  echo -e "${GREEN}✓ Generado${NC}"
}

# 1. Generar esquema estructural (DDL)
echo ""
echo -e "${YELLOW}1. Generando esquema estructural...${NC}"
dump_schema "comintec_schema_core_new.sql" "--schema-only --no-comments"

# 2. Generar índices y constraints
echo ""
echo -e "${YELLOW}2. Generando índices y constraints...${NC}"
PGPASSWORD="$DB_PASS" psql \
  -h "$DB_HOST" \
  -p "$DB_PORT" \
  -U "$DB_USER" \
  -d "$DB_NAME" \
  -t -c "
SELECT 
  'CREATE INDEX ' || i.indexname || ' ON ' || i.tablename || 
  ' (' || pg_get_indexdef(c.oid, 0, true) || ');'
FROM pg_indexes i
JOIN pg_class c ON c.relname = i.indexname
WHERE i.schemaname = 'public'
  AND i.indexname NOT LIKE '%_pkey'
  AND i.indexname NOT LIKE '%_key'
ORDER BY i.tablename, i.indexname;
" > comintec_schema_indexes_new.sql

# 3. Comparar con archivos existentes
echo ""
echo -e "${YELLOW}3. Comparando con esquemas actuales...${NC}"

if [[ -f "comintec_schema_core.sql" ]]; then
  echo "Diferencias en esquema core:"
  diff -u comintec_schema_core.sql comintec_schema_core_new.sql || true
  echo ""
fi

if [[ -f "comintec_schema_data_indexes.sql" ]]; then
  echo "Diferencias en índices:"
  diff -u comintec_schema_data_indexes.sql comintec_schema_indexes_new.sql || true
  echo ""
fi

# 4. Generar lista de tablas y columnas
echo ""
echo -e "${YELLOW}4. Generando documentación de estructura...${NC}"
PGPASSWORD="$DB_PASS" psql \
  -h "$DB_HOST" \
  -p "$DB_PORT" \
  -U "$DB_USER" \
  -d "$DB_NAME" \
  -H -c "
SELECT 
  t.table_name as \"Tabla\",
  c.column_name as \"Columna\",
  c.data_type as \"Tipo\",
  c.is_nullable as \"Nullable\",
  c.column_default as \"Default\"
FROM information_schema.tables t
JOIN information_schema.columns c ON c.table_name = t.table_name
WHERE t.table_schema = 'public' 
  AND t.table_type = 'BASE TABLE'
ORDER BY t.table_name, c.ordinal_position;
" > database_structure.html

# 5. Resumen
echo ""
echo -e "${GREEN}=== Archivos generados ===${NC}"
echo "  - comintec_schema_core_new.sql (estructura DDL)"
echo "  - comintec_schema_indexes_new.sql (índices)"
echo "  - database_structure.html (documentación)"
echo ""
echo -e "${YELLOW}Próximos pasos:${NC}"
echo "  1. Revisar las diferencias mostradas"
echo "  2. Si todo está correcto, reemplazar los archivos:"
echo "     mv comintec_schema_core_new.sql comintec_schema_core.sql"
echo "     mv comintec_schema_indexes_new.sql comintec_schema_data_indexes.sql"
echo "  3. Commitear los cambios" 