#!/bin/bash

# Script de limpieza de archivos temporales y obsoletos
# Uso: ./cleanup-temp.sh [--dry-run|--force]

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Variables
DRY_RUN=true
FORCE=false
DELETED_COUNT=0
DELETED_SIZE=0

# Procesar argumentos
while [[ $# -gt 0 ]]; do
  case $1 in
    --dry-run)
      DRY_RUN=true
      shift
      ;;
    --force)
      DRY_RUN=false
      FORCE=true
      shift
      ;;
    *)
      echo "Uso: $0 [--dry-run|--force]"
      exit 1
      ;;
  esac
done

# Función para calcular tamaño
get_size() {
  if [[ -e "$1" ]]; then
    du -sh "$1" 2>/dev/null | cut -f1 || echo "0"
  else
    echo "0"
  fi
}

# Función para limpiar con confirmación
clean_path() {
  local path=$1
  local description=$2
  
  if [[ -e "$path" ]]; then
    local size=$(get_size "$path")
    
    if [[ "$DRY_RUN" == "true" ]]; then
      echo -e "${YELLOW}[DRY-RUN]${NC} Eliminaría: $path (${size}) - $description"
    else
      echo -e "${RED}[ELIMINANDO]${NC} $path (${size}) - $description"
      rm -rf "$path"
      ((DELETED_COUNT++))
    fi
  fi
}

echo "=== Limpieza de archivos temporales ==="
echo ""

# Limpiar archivos de desarrollo temporal
clean_path "cursor.deb" "Instalador de Cursor descargado"
clean_path "backend/backend.log" "Log de desarrollo backend"
clean_path "frontend/frontend.log" "Log de desarrollo frontend"

# Limpiar builds locales no versionados
clean_path "backend/dist" "Build de producción backend"
clean_path "frontend/.next" "Build de Next.js"
clean_path "frontend/out" "Export estático de Next.js"

# Limpiar node_modules si --force
if [[ "$FORCE" == "true" ]]; then
  clean_path "node_modules" "Dependencias raíz"
  clean_path "backend/node_modules" "Dependencias backend"
  clean_path "frontend/node_modules" "Dependencias frontend"
fi

# Limpiar coverage reports
clean_path "backend/coverage" "Reportes de cobertura backend"
clean_path "frontend/coverage" "Reportes de cobertura frontend"

# Limpiar archivos de respaldo antiguos (más de 30 días)
if [[ -d "backend/backups" ]]; then
  echo ""
  echo "Buscando backups antiguos (>30 días)..."
  find backend/backups -name "*.sql" -type f -mtime +30 | while read -r file; do
    clean_path "$file" "Backup antiguo"
  done
fi

# Limpiar archivos temporales del sistema
find . -name "*.tmp" -o -name "*.temp" -o -name "*.swp" -o -name ".DS_Store" | while read -r file; do
  clean_path "$file" "Archivo temporal del sistema"
done

# Resumen
echo ""
echo "=== Resumen ==="
if [[ "$DRY_RUN" == "true" ]]; then
  echo -e "${YELLOW}Modo DRY-RUN${NC}: No se eliminó nada. Usa --force para ejecutar la limpieza."
else
  echo -e "${GREEN}Limpieza completada${NC}: $DELETED_COUNT elementos eliminados"
fi 