---
type: "always_apply"
---

# Reglas de Uso de Taskmaster

## Estructura de IDs
- Usa IDs jerárquicos: 1, 1.1, 1.2, etc.

## Prioridades
- Asigna prioridad: alta, media, baja

## Dependencias
- Declara dependencias entre tareas cuando aplique

## Estados
- Usa los estados: pending, in-progress, done, deferred, cancelled

## Convenciones
- Títulos claros y descriptivos
- Subtareas con verbo de acción
- Actualiza tareas con `update_subtask` para registrar avances, problemas y decisiones

## Mejores Prácticas
- Expande tareas complejas en subtareas
- Marca tareas como done solo cuando estén realmente terminadas
- Usa comentarios para aclarar decisiones importantes
