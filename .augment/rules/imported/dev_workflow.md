---
type: "always_apply"
---

# Reglas de Flujo de Trabajo de Desarrollo

## **🔄 FLUJO BACKEND-FRONTEND OBLIGATORIO**

### **Regla Principal: Siempre Backend Primero, Luego Frontend**
- **NUNCA** implementar solo backend sin su correspondiente frontend
- **NUNCA** implementar solo frontend sin backend funcional
- Cada funcionalidad debe tener implementación completa en ambas capas

### **Proceso de Implementación por Tarea:**

#### **Fase 1: Backend (Puerto 3000)**
1. **Estructura de Datos:**
   - Base de datos (tablas, índices, constraints)
   - Entidades TypeORM con decoradores completos
   - Migraciones si es necesario

2. **Lógica de Negocio:**
   - DTOs con validaciones class-validator
   - Services con lógica completa
   - Controllers con endpoints REST
   - Routes configuradas

3. **Testing Backend:**
   - Tests unitarios de servicios
   - Tests de integración de endpoints
   - Verificación con Postman/Thunder Client

#### **Fase 2: Frontend (Puerto 3001) - OBLIGATORIO DESPUÉS**
1. **Componentes UI:**
   - Páginas en `app/` con Next.js 15
   - Componentes en `components/` con Radix UI + Tailwind
   - Formularios con React Hook Form + Zod

2. **Estado y Datos:**
   - Queries con TanStack React Query
   - Mutaciones para operaciones CRUD
   - Manejo de estados optimista

3. **Navegación:**
   - Rutas configuradas en el módulo correspondiente
   - Pestañas/secciones según la arquitectura del sistema

### **Arquitectura de Pestañas Frontend:**

#### **Módulo Sistemas (implementado en Tarea 87):**
- **🏠 Dashboard Sistemas** → `/sistemas`
- **👥 Gestión de Usuarios** → `/sistemas/usuarios` (**FALTA IMPLEMENTAR**)
- **🔐 Roles y Permisos** → `/sistemas/roles` (**FALTA IMPLEMENTAR**)
- **💾 Gestión de Backups** → `/sistemas/backups` (**FALTA IMPLEMENTAR**)
- **📋 Logs de Auditoría** → `/sistemas/auditoria` (**FALTA IMPLEMENTAR**)

#### **Módulo Almacén (implementado en Tarea 94):**
- **🏠 Dashboard Almacén** → `/almacen`
- **📦 Stock Actual** → `/almacen/inventario` (**FALTA IMPLEMENTAR**)
- **📊 Categorización** → `/almacen/categorias` (**FALTA IMPLEMENTAR**)
- **📈 Reportes de Inventario** → `/almacen/reportes` (**FALTA IMPLEMENTAR**)

### **Comandos de Desarrollo Coordinado:**

#### **Backend (siempre primero):**
```bash
cd backend && npm run dev  # Puerto 3000
```

#### **Frontend (después del backend):**
```bash
cd frontend && npm run dev  # Puerto 3001
```

#### **Verificación Full-Stack:**
```bash
# Backend: http://localhost:3000/api/health
# Frontend: http://localhost:3001
```

## Comandos Backend
- `cd backend && npm run dev` — Levantar backend en desarrollo
- `cd backend && npm run build && npm start` — Producción
- `cd backend && npm run test` — Ejecutar tests backend
- `cd backend && npm run migration:generate` — Generar migración
- `cd backend && npm run migration:run` — Ejecutar migraciones

## Comandos Frontend
- `cd frontend && npm run dev` — Levantar frontend despues de backend en desarrollo
- `cd frontend && npm run build && npm start` — Producción
- `cd frontend && npm run test` — Ejecutar tests frontend

## Rutas de acceso
- Backend: http://localhost:3000
- Frontend: http://localhost:3001

## Usuarios de base de datos (desarrollo)
- Usuario app: <EMAIL>
- Contraseña app: password
- Rol: ADMIN
- Usuario BD: postgres
- Contraseña BD: appcomintecpasw0rd

## **🎯 PRÓXIMOS PASOS OBLIGATORIOS:**

### **Para Tarea 87 (Sistemas) - PENDIENTE FRONTEND:**
1. **Página de Gestión de Usuarios** (`/sistemas/usuarios`)
   - Lista de usuarios con filtros
   - Modal de creación/edición
   - Cambio de contraseñas
   - Activar/desactivar usuarios

2. **Página de Roles y Permisos** (`/sistemas/roles`)
   - Gestión de roles
   - Asignación de permisos
   - Vista matricial de permisos

### **Para Tarea 94 (Almacén) - PENDIENTE FRONTEND:**
1. **Página de Stock Actual** (`/almacen/inventario`)
   - Vista con categorización dual (GENERAL/ROTATIVO)
   - Indicadores de colores de stock
   - Filtros avanzados
   - Operaciones CRUD de productos

## **Uso de Taskmaster**
- IDs jerárquicos para tareas/subtareas (1, 1.1, 1.2)
- Prioridad y dependencias siempre que aplique
- Estados: pending, in-progress, done, deferred, cancelled
- Títulos claros y descriptivos
- Usa `update_subtask` para registrar hallazgos y decisiones

## Uso de Cursor
- Referencias claras a archivos y rutas
- Documenta reglas en `.cursor/rules/`
- Usa comandos de Taskmaster desde Cursor para automatizar

## Reglas de codificación
- Sigue las reglas de linting y formateo del proyecto
- Mantén convenciones de carpetas y nombres
- Documenta cualquier excepción en este archivo

## **Modificaciones de Base de Datos**
- Toda adición o ajuste de campos en modelos debe reflejarse en la estructura real de la base de datos (migraciones, scripts comintec_schema_core `.sql`, comintec_schema_data_indexes , seeds, etc.).
- Versionar y documentar cualquier cambio estructural.
- Asegurar compatibilidad con el resto del sistema y procesos de importación/exportación.

## **⚡ REGLA CRÍTICA: NO DEJAR TAREAS A MEDIAS**
- **BACKEND SIN FRONTEND = TAREA INCOMPLETA**
- **FRONTEND SIN BACKEND = TAREA IMPOSIBLE**
- Siempre verificar que ambas capas funcionen en conjunto
- Documentar endpoints y su uso en componentes frontend
