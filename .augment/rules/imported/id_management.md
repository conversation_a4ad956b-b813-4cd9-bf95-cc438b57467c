---
type: "always_apply"
---

# ID Management Rules - CRITICAL FOR ALL DEVELOPMENT

## **🚨 MANDATORY: Frontend-Backend ID Type Convention**

### **Golden Rules (NEVER VIOLATE)**
1. **APIs ALWAYS return IDs as strings**: `{"id": "78", "name": "ROL<PERSON>_CALIDAD"}`
2. **Frontend forms/validation ALWAYS use numbers**: `roleIds: [78, 31]`
3. **Backend operations ALWAYS accept numbers**: `createUserWithRoleIds(userData, [78, 31])`
4. **ALWAYS convert at boundary points**: API response → Form, Form → API request

### **Why This Matters**
- **Prevents "undefined" in form selections**
- **Stops validation failures on working data**
- **Ensures type safety across stack**
- **Consistent with PostgreSQL/Supabase behavior**

## **Frontend Implementation (MANDATORY PATTERNS)**

### **Form Schema Pattern**
```typescript
// ✅ ALWAYS: Form schemas expect numbers for ID arrays
const formSchema = z.object({
  name: z.string().min(2),
  roleIds: z.array(z.number()).min(1, 'Required'),      // NUMBERS
  departmentIds: z.array(z.number()).optional(),        // NUMBERS
  projectIds: z.array(z.number()).optional(),           // NUMBERS
});

// ❌ NEVER: Don't use string validation for ID operations
const badSchema = z.object({
  roleIds: z.array(z.string()),  // WRONG - causes validation errors
});
```

### **API Response to Form Conversion**
```typescript
// ✅ MANDATORY: Convert string IDs to numbers when initializing forms
useEffect(() => {
  if (isEditing && entity) {
    form.reset({
      name: entity.name,
      // CRITICAL: Convert API strings to form numbers
      roleIds: entity.roles?.map(role => parseInt(role.id.toString(), 10)) || [],
      departmentIds: entity.departments?.map(dept => parseInt(dept.id.toString(), 10)) || [],
    });
  }
}, [entity, isEditing, form]);

// ❌ NEVER: Direct assignment without conversion
form.reset({
  roleIds: entity.roles?.map(role => role.id) || [],  // WRONG - role.id is string!
});
```

### **Checkbox/Select Rendering Pattern**
```typescript
// ✅ MANDATORY: Convert for each render iteration
{roles.map((role: any) => {
  const roleIdNum = parseInt(role.id.toString(), 10);  // CONVERT HERE
  return (
    <Checkbox
      key={role.id}
      id={`role-${role.id}`}
      checked={field.value.includes(roleIdNum)}          // USE NUMBER
      onCheckedChange={() => handleToggle(roleIdNum)}    // PASS NUMBER
    />
  );
})}

// ❌ NEVER: Use string IDs directly in number array operations
<Checkbox
  checked={field.value.includes(role.id)}              // WRONG - type mismatch
  onCheckedChange={() => handleToggle(role.id)}        // WRONG - string vs number
/>
```

### **Form Submission Pattern**
```typescript
// ✅ MANDATORY: Send numbers to backend for operations
const onSubmit = async (data: FormData) => {
  const requestData = {
    name: data.name,
    roles: data.roleIds,        // Already numbers from form: [78, 31]
    departments: data.departmentIds,  // Already numbers: [5, 12]
  };
  
  await apiService.createEntity(requestData);  // Backend expects numbers
};

// ❌ NEVER: Send mixed types or unconverted data
const badSubmit = async (data: FormData) => {
  const requestData = {
    roles: selectedRoles.map(role => role.id),  // WRONG - strings from API
  };
};
```

## **Backend Implementation (MANDATORY PATTERNS)**

### **Controller Pattern - Accept Numbers**
```typescript
// ✅ MANDATORY: Controllers accept numbers for operations
async createUser(req: Request, res: Response): Promise<void> {
  try {
    const { roles, departments, ...userData } = req.body;
    // roles = [78, 31] (numbers from frontend)
    // departments = [5, 12] (numbers from frontend)
    
    const newUser = await this.userService.createUserWithIds(userData, roles, departments);
    res.status(201).json({ data: newUser });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
}
```

### **Service Pattern - Dual Methods**
```typescript
// ✅ RECOMMENDED: Create both legacy and new methods
export class EntityService {
  // Legacy method (backward compatibility)
  async createEntity(userData: EntityData, roleNames: string[]): Promise<Entity> {
    // Implementation for string names
  }

  // ✅ PREFERRED: New method accepting number IDs
  async createEntityWithIds(userData: EntityData, roleIds: number[]): Promise<Entity> {
    // Validate with SQL
    const roleCheckQuery = `SELECT id, name FROM roles WHERE id = ANY($1)`;
    const existingRoles = await repository.query(roleCheckQuery, [roleIds]);
    
    if (existingRoles.length !== roleIds.length) {
      throw new Error('One or more roles not found');
    }

    // Create entity
    const newEntity = repository.create({ ...userData });
    const savedEntity = await repository.save(newEntity);

    // Link relationships with SQL (avoid TypeORM relations)
    for (const roleId of roleIds) {
      await repository.query(
        'INSERT INTO entity_roles (entity_id, role_id) VALUES ($1, $2)',
        [savedEntity.id, roleId]
      );
    }

    return this.findEntityById(savedEntity.id) as Promise<Entity>;
  }
}
```

### **Database Query Pattern - SQL Direct**
```typescript
// ✅ MANDATORY: Use SQL direct queries for reliability
async findEntityById(id: number): Promise<Entity | null> {
  const entityQuery = `
    SELECT id, name, email, status, area, active,
           created_at, updated_at, created_by, updated_by
    FROM entities WHERE id = $1
  `;
  const entityResult = await repository.query(entityQuery, [id]);
  
  if (!entityResult || entityResult.length === 0) return null;
  
  const entity = entityResult[0];
  
  // Get related entities with SQL JOIN
  const rolesQuery = `
    SELECT r.id, r.name
    FROM entity_roles er
    JOIN roles r ON er.role_id = r.id
    WHERE er.entity_id = $1
    ORDER BY r.name
  `;
  const rolesResult = await repository.query(rolesQuery, [id]);
  entity.roles = rolesResult;  // IDs will be strings from database
  
  return entity;
}

// ❌ NEVER: Use TypeORM relations for complex entities
async badFindEntity(id: number): Promise<Entity | null> {
  return repository.findOne({ 
    where: { id }, 
    relations: ['roles', 'roles.permissions']  // CAUSES schema errors
  });
}
```

## **Error Prevention Checklist**

### **Before Implementing Any Form with IDs**
- [ ] **API endpoint returns string IDs** - verify with curl/Postman
- [ ] **Form schema uses number arrays** - `z.array(z.number())`
- [ ] **Conversion on form load** - `parseInt(id.toString(), 10)`
- [ ] **Conversion on render** - convert in map function
- [ ] **Backend accepts numbers** - create `*WithIds` methods
- [ ] **Backend uses SQL queries** - avoid TypeORM relations
- [ ] **Test with real data** - no "undefined" in UI

### **Debug ID Type Issues**
```typescript
// ✅ Add these logs when debugging ID problems
console.log('[ID DEBUG] API Response ID type:', typeof apiData.id, 'Value:', apiData.id);
console.log('[ID DEBUG] Form value type:', typeof formValue, 'Value:', formValue);
console.log('[ID DEBUG] Conversion result:', parseInt(apiData.id, 10));
console.log('[ID DEBUG] Form validation errors:', form.formState.errors);
```

### **Common Error Patterns to AVOID**
```typescript
// ❌ WRONG: Mixed string/number comparisons
if (formIds.includes(apiId)) { }  // string vs number array

// ❌ WRONG: Direct API response in form
roleIds: user.roles.map(role => role.id)  // API strings in number form

// ❌ WRONG: String validation for operations
const schema = z.object({ ids: z.array(z.string()) })  // Forms need numbers

// ❌ WRONG: TypeORM relations with problematic columns
findOne({ relations: ['roles'] })  // Causes schema errors

// ❌ WRONG: Sending strings for backend operations
{ roles: ["78", "31"] }  // Backend expects [78, 31]
```

## **Testing ID Conversion**

### **Frontend Tests**
```typescript
// ✅ Test ID conversion in forms
describe('EntityFormModal', () => {
  it('converts API string IDs to form numbers', () => {
    const mockEntity = {
      id: "123",
      roles: [{ id: "78", name: "ROLE_TEST" }]
    };
    
    render(<EntityFormModal entity={mockEntity} />);
    
    // Verify form has numeric IDs
    expect(form.getValues('roleIds')).toEqual([78]);
  });
  
  it('sends numeric IDs to backend', async () => {
    const mockSubmit = jest.fn();
    // ... test that request data contains numbers
    expect(mockSubmit).toHaveBeenCalledWith({
      roles: [78, 31]  // Numbers, not strings
    });
  });
});
```

### **Backend Tests**
```typescript
// ✅ Test backend accepts numeric IDs
describe('UserController', () => {
  it('creates user with numeric role IDs', async () => {
    const userData = { name: 'Test', roles: [78, 31] };
    const result = await controller.createUser(userData);
    
    expect(result.roles).toContainEqual(
      expect.objectContaining({ id: "78" })  // Response has strings
    );
  });
});
```

## **Migration Guide for Existing Code**

### **Identify Problem Areas**
1. **Search for TypeORM relations**: `relations: ['roles']`
2. **Find direct ID assignments**: `roleIds: roles.map(r => r.id)`
3. **Look for string schemas**: `z.array(z.string())`
4. **Check API payloads**: ensure sending numbers for operations

### **Fix Pattern**
1. **Replace TypeORM relations** with SQL direct queries
2. **Add ID conversion** in form initialization
3. **Update schemas** to expect numbers
4. **Create `*WithIds` methods** in services
5. **Test with real data** to verify no "undefined" errors

This ensures ALL future development follows the proven ID handling pattern and prevents the bugs we encountered with role selection forms.
