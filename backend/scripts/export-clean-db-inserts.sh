#!/bin/bash

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuración de Supabase
DB_HOST="db.awlxzhrubqkryrenunun.supabase.co"
DB_PORT=5432
DB_USER="postgres"
DB_NAME="postgres"
DB_PASSWORD="appcomintecpassw0rd"

# Directorios
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OUTPUT_DIR="$SCRIPT_DIR/.."

# Archivos de salida
BACKUP_FILE="$OUTPUT_DIR/comintec_db_inserts.sql"

echo -e "${BLUE}Exportando base de datos con formato INSERT (ejecutable en consola)...${NC}"

# Crear archivo con encabezado
cat > $BACKUP_FILE << 'EOF'
-- =============================
-- COMINTEC - Base de Datos Completa
-- Formato: INSERT statements (ejecutable en consola SQL)
-- =============================
-- Generado automáticamente
-- Incluye: estructura + datos en formato INSERT
-- =============================

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

-- Crear extensiones necesarias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

EOF

echo -e "${YELLOW}1. Exportando estructura de tablas...${NC}"

# Exportar solo la estructura (DDL)
PGPASSWORD=$DB_PASSWORD pg_dump \
  --schema=public \
  --schema-only \
  --no-owner \
  --no-privileges \
  --exclude-table=supabase_* \
  --exclude-table=_realtime* \
  -h $DB_HOST \
  -p $DB_PORT \
  -U $DB_USER \
  -d $DB_NAME \
  >> $BACKUP_FILE

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Error al exportar estructura${NC}"
    exit 1
fi

echo -e "${YELLOW}2. Exportando datos con INSERT statements...${NC}"

# Agregar separador para datos
cat >> $BACKUP_FILE << 'EOF'

-- =============================
-- DATOS DE LAS TABLAS (INSERT FORMAT)
-- =============================

EOF

# Exportar datos en formato INSERT
PGPASSWORD=$DB_PASSWORD pg_dump \
  --schema=public \
  --data-only \
  --no-owner \
  --no-privileges \
  --exclude-table=supabase_* \
  --exclude-table=_realtime* \
  --column-inserts \
  --rows-per-insert=1 \
  -h $DB_HOST \
  -p $DB_PORT \
  -U $DB_USER \
  -d $DB_NAME \
  >> $BACKUP_FILE

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Error al exportar datos${NC}"
    exit 1
fi

# Agregar footer
cat >> $BACKUP_FILE << 'EOF'

-- =============================
-- FIN DEL BACKUP
-- =============================
-- Para restaurar:
-- 1. Conectar a la base de datos destino
-- 2. Ejecutar: \i comintec_db_inserts.sql
-- 3. O copiar y pegar secciones en consola SQL
-- =============================

EOF

echo -e "${GREEN}✅ Backup con INSERT statements guardado en: $BACKUP_FILE${NC}"

# Mostrar estadísticas del archivo
echo -e "${BLUE}📊 Estadísticas del backup:${NC}"
echo "   📄 Tamaño: $(du -h $BACKUP_FILE | cut -f1)"
echo "   📝 Líneas: $(wc -l < $BACKUP_FILE)"
echo "   🔍 INSERT statements: $(grep -c "^INSERT INTO" $BACKUP_FILE)"

echo ""
echo -e "${YELLOW}💡 Uso del archivo generado:${NC}"
echo "   • Ejecutable directamente en consola SQL de Supabase"
echo "   • Cada INSERT es independiente y se puede copiar/pegar"
echo "   • Formato legible y editable manualmente"
echo "   • Compatible con cualquier cliente PostgreSQL"
