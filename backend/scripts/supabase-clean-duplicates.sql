-- ============================================================================
-- SCRIPT PARA SUPABASE: Eliminar Permisos Duplicados
-- ============================================================================
-- Copia y pega este script completo en el SQL Editor de Supabase
-- ============================================================================

-- Ver estado inicial
SELECT 'ESTADO INICIAL - Total de permisos:' as info, COUNT(*) as total FROM permissions;
SELECT 'ESTADO INICIAL - Nombres únicos:' as info, COUNT(DISTINCT name) as unique_names FROM permissions;
SELECT 'ESTADO INICIAL - Duplicados:' as info, (COUNT(*) - COUNT(DISTINCT name)) as duplicates FROM permissions;

-- Mostrar permisos duplicados
SELECT 'PERMISOS DUPLICADOS:' as info;
SELECT 
    name,
    COUNT(*) as duplicates,
    string_agg(id::text, ', ' ORDER BY id) as ids
FROM permissions 
GROUP BY name 
HAVING COUNT(*) > 1
ORDER BY name;

-- PASO 1: Eliminar referencias en role_permissions de los duplicados
DELETE FROM role_permissions 
WHERE permission_id IN (
    SELECT p.id 
    FROM permissions p
    WHERE p.id NOT IN (
        -- Mantener solo el primer permiso de cada nombre, priorizando español
        SELECT DISTINCT ON (name) id
        FROM permissions
        ORDER BY name, 
                 CASE 
                     -- Prioridad 1: Descripciones en español
                     WHEN description LIKE '%Crear %' 
                          OR description LIKE '%Ver %' 
                          OR description LIKE '%Editar %'
                          OR description LIKE '%Eliminar %'
                          OR description LIKE '%Listar %'
                          OR description LIKE '%Activar %'
                          OR description LIKE '%Desactivar %'
                          OR description LIKE '%usuarios del sistema%'
                          OR description LIKE '%roles del sistema%'
                          OR description LIKE '%permisos del sistema%'
                          OR description LIKE '%auditoría del sistema%'
                          OR description LIKE '%respaldos del sistema%'
                          THEN 1
                     -- Prioridad 3: Descripciones en inglés
                     WHEN description LIKE '%in % module%' 
                          OR description LIKE '%Create %'
                          OR description LIKE '%Read %'
                          OR description LIKE '%Update %'
                          OR description LIKE '%Delete %'
                          THEN 3
                     -- Prioridad 2: Otros casos
                     ELSE 2
                 END,
                 id ASC -- En caso de empate, mantener el ID más bajo
    )
);

-- PASO 2: Eliminar permisos duplicados
DELETE FROM permissions 
WHERE id NOT IN (
    -- Mantener solo el primer permiso de cada nombre, priorizando español
    SELECT DISTINCT ON (name) id
    FROM permissions
    ORDER BY name, 
             CASE 
                 -- Prioridad 1: Descripciones en español
                 WHEN description LIKE '%Crear %' 
                      OR description LIKE '%Ver %' 
                      OR description LIKE '%Editar %'
                      OR description LIKE '%Eliminar %'
                      OR description LIKE '%Listar %'
                      OR description LIKE '%Activar %'
                      OR description LIKE '%Desactivar %'
                      OR description LIKE '%usuarios del sistema%'
                      OR description LIKE '%roles del sistema%'
                      OR description LIKE '%permisos del sistema%'
                      OR description LIKE '%auditoría del sistema%'
                      OR description LIKE '%respaldos del sistema%'
                      THEN 1
                 -- Prioridad 3: Descripciones en inglés
                 WHEN description LIKE '%in % module%' 
                      OR description LIKE '%Create %'
                      OR description LIKE '%Read %'
                      OR description LIKE '%Update %'
                      OR description LIKE '%Delete %'
                      THEN 3
                 -- Prioridad 2: Otros casos
                 ELSE 2
             END,
             id ASC -- En caso de empate, mantener el ID más bajo
);

-- VERIFICACIÓN FINAL
SELECT '✅ RESULTADO FINAL:' as info;
SELECT 'Total de permisos:' as info, COUNT(*) as total FROM permissions;
SELECT 'Nombres únicos:' as info, COUNT(DISTINCT name) as unique_names FROM permissions;
SELECT 'Duplicados restantes:' as info, (COUNT(*) - COUNT(DISTINCT name)) as should_be_zero FROM permissions;

-- Verificar que no hay duplicados (esta consulta debe estar vacía)
SELECT 'DUPLICADOS RESTANTES (debe estar vacío):' as info;
SELECT name, COUNT(*) as count
FROM permissions 
GROUP BY name 
HAVING COUNT(*) > 1
ORDER BY name;

-- Mostrar resumen por módulo
SELECT 'RESUMEN POR MÓDULO:' as info;
SELECT 
    module, 
    COUNT(*) as count 
FROM permissions 
GROUP BY module 
ORDER BY module;

-- Mostrar algunos ejemplos de permisos mantenidos
SELECT 'EJEMPLOS DE PERMISOS MANTENIDOS:' as info;
SELECT 
    name, 
    description 
FROM permissions 
WHERE name LIKE 'sistemas:%' 
ORDER BY name 
LIMIT 10;

SELECT '🎉 LIMPIEZA COMPLETADA EXITOSAMENTE!' as resultado;
