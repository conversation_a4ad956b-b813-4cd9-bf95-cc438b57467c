#!/bin/bash

# Script para exportar BD completa con IDs reiniciados desde 1
# Mantiene todas las relaciones intactas pero reinicia secuencias

# Colores
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Configuración de Supabase
DB_HOST="db.awlxzhrubqkryrenunun.supabase.co"
DB_PORT=5432
DB_USER="postgres"
DB_NAME="postgres"
DB_PASSWORD="appcomintecpassw0rd"

# Directorios
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OUTPUT_DIR="$SCRIPT_DIR/.."

# Archivos de salida
BACKUP_FILE="$OUTPUT_DIR/comintec_db_clean_ids.sql"
TEMP_FILE="/tmp/comintec_temp_backup.sql"

echo -e "${BLUE}=== Generando backup con IDs reiniciados ===${NC}"
echo -e "${YELLOW}Este proceso:${NC}"
echo "  • Exporta toda la estructura y datos"
echo "  • Reinicia todos los IDs desde 1"
echo "  • Mantiene todas las relaciones FK intactas"
echo "  • Reinicia las secuencias automáticamente"
echo ""

# Paso 1: Exportar estructura
echo -e "${YELLOW}1. Exportando estructura de tablas...${NC}"

cat > $BACKUP_FILE << 'EOF'
-- =============================
-- COMINTEC - Base de Datos Limpia (IDs Reiniciados)
-- =============================
-- IMPORTANTE: Este backup reinicia todos los IDs desde 1
-- Mantiene todas las relaciones FK intactas
-- Ideal para despliegue en producción
-- =============================

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

-- Crear extensiones necesarias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================
-- ESTRUCTURA DE TABLAS
-- =============================

EOF

# Exportar estructura
PGPASSWORD=$DB_PASSWORD pg_dump \
  --schema=public \
  --schema-only \
  --no-owner \
  --no-privileges \
  --exclude-table=supabase_* \
  --exclude-table=_realtime* \
  -h $DB_HOST \
  -p $DB_PORT \
  -U $DB_USER \
  -d $DB_NAME \
  >> $BACKUP_FILE

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Error al exportar estructura${NC}"
    exit 1
fi

# Paso 2: Exportar datos con IDs reiniciados
echo -e "${YELLOW}2. Exportando datos con IDs reiniciados...${NC}"

cat >> $BACKUP_FILE << 'EOF'

-- =============================
-- DATOS CON IDs REINICIADOS
-- =============================
-- Los IDs se reinician desde 1 manteniendo relaciones

-- Deshabilitar triggers temporalmente para inserción limpia
SET session_replication_role = replica;

EOF

# Definir orden de tablas para mantener relaciones FK
TABLES_ORDER=(
    "roles"
    "permissions" 
    "role_permissions"
    "users"
    "user_roles"
    "categories"
    "suppliers"
    "products"
    "clients"
    "sales"
    "sale_items"
    "purchases"
    "purchase_items"
    "inventory_movements"
    "notifications"
    "audit_logs"
    "backups"
)

# Función para procesar cada tabla
process_table() {
    local table=$1
    echo -e "  • Procesando $table..."
    
    # Verificar si la tabla existe
    local exists=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "
        SELECT COUNT(*) FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = '$table'
    " | tr -d ' \n')
    
    if [[ "$exists" == "0" ]]; then
        echo -e "    ${YELLOW}⚠️  Tabla $table no existe, saltando...${NC}"
        return
    fi
    
    # Contar registros
    local count=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM $table" | tr -d ' \n')
    
    cat >> $BACKUP_FILE << EOF

-- Tabla: $table ($count registros)
EOF
    
    if [[ "$count" == "0" ]]; then
        echo "-- (Sin datos)" >> $BACKUP_FILE
        return
    fi
    
    # Obtener columnas de la tabla
    local columns=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "
        SELECT string_agg(column_name, ', ' ORDER BY ordinal_position)
        FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = '$table'
    " | tr -d '\n' | sed 's/^ *//')
    
    # Verificar si tiene columna id
    local has_id=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "
        SELECT COUNT(*) FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = '$table' AND column_name = 'id'
    " | tr -d ' \n')
    
    if [[ "$has_id" == "1" ]]; then
        # Tabla con ID - usar ROW_NUMBER() para reiniciar IDs
        cat >> $BACKUP_FILE << EOF
-- Reiniciando IDs desde 1 para $table
INSERT INTO $table ($columns)
SELECT 
EOF
        
        # Generar SELECT con ROW_NUMBER() para id y otras columnas
        local select_columns=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "
            SELECT string_agg(
                CASE 
                    WHEN column_name = 'id' THEN 'ROW_NUMBER() OVER (ORDER BY id)'
                    ELSE column_name 
                END, 
                ', ' ORDER BY ordinal_position
            )
            FROM information_schema.columns 
            WHERE table_schema = 'public' AND table_name = '$table'
        " | tr -d '\n' | sed 's/^ *//')
        
        echo "  $select_columns" >> $BACKUP_FILE
        echo "FROM $table ORDER BY id;" >> $BACKUP_FILE
        
    else
        # Tabla sin ID - insertar normalmente
        PGPASSWORD=$DB_PASSWORD pg_dump \
          --schema=public \
          --data-only \
          --no-owner \
          --no-privileges \
          --table=$table \
          --column-inserts \
          --rows-per-insert=1 \
          -h $DB_HOST \
          -p $DB_PORT \
          -U $DB_USER \
          -d $DB_NAME \
          >> $BACKUP_FILE
    fi
}

# Procesar todas las tablas en orden
for table in "${TABLES_ORDER[@]}"; do
    process_table "$table"
done

# Procesar tablas adicionales que no estén en el orden definido
echo -e "${YELLOW}3. Verificando tablas adicionales...${NC}"
ADDITIONAL_TABLES=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "
    SELECT tablename 
    FROM pg_tables 
    WHERE schemaname = 'public' 
    AND tablename NOT LIKE 'supabase_%' 
    AND tablename NOT LIKE '_realtime%'
    AND tablename NOT IN ('$(IFS=','; echo "${TABLES_ORDER[*]}" | sed "s/,/','/g")')
" | tr -d ' ')

for table in $ADDITIONAL_TABLES; do
    if [[ -n "$table" ]]; then
        process_table "$table"
    fi
done

# Paso 3: Reiniciar secuencias
echo -e "${YELLOW}4. Reiniciando secuencias...${NC}"

cat >> $BACKUP_FILE << 'EOF'

-- =============================
-- REINICIAR SECUENCIAS
-- =============================
-- Ajustar secuencias al máximo ID actual + 1

EOF

# Obtener todas las secuencias y reiniciarlas
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "
    SELECT 
        'SELECT setval(''' || schemaname || '.' || sequencename || ''', COALESCE((SELECT MAX(id) FROM ' || 
        replace(sequencename, '_id_seq', '') || '), 0) + 1, false);'
    FROM pg_sequences 
    WHERE schemaname = 'public'
    AND sequencename LIKE '%_id_seq'
" | sed '/^$/d' >> $BACKUP_FILE

# Paso 4: Finalizar
cat >> $BACKUP_FILE << 'EOF'

-- Rehabilitar triggers
SET session_replication_role = DEFAULT;

-- =============================
-- FIN DEL BACKUP LIMPIO
-- =============================
-- IDs reiniciados desde 1
-- Relaciones FK mantenidas
-- Secuencias ajustadas automáticamente
-- =============================

EOF

echo ""
echo -e "${GREEN}✅ Backup con IDs reiniciados generado: $BACKUP_FILE${NC}"

# Mostrar estadísticas
echo -e "${BLUE}📊 Estadísticas del backup limpio:${NC}"
echo "   📄 Tamaño: $(du -h $BACKUP_FILE | cut -f1)"
echo "   📝 Líneas: $(wc -l < $BACKUP_FILE)"
echo "   🔍 INSERT statements: $(grep -c "^INSERT INTO" $BACKUP_FILE)"

echo ""
echo -e "${YELLOW}💡 Características del backup:${NC}"
echo "   ✅ IDs reiniciados desde 1"
echo "   ✅ Relaciones FK intactas"
echo "   ✅ Secuencias ajustadas automáticamente"
echo "   ✅ Listo para producción"
echo ""
echo -e "${YELLOW}📋 Para usar:${NC}"
echo "   • Ejecutar en BD limpia: \\i comintec_db_clean_ids.sql"
echo "   • Los IDs empezarán desde 1 en todas las tablas"
