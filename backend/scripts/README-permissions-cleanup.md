# 🔧 Limpieza de Permisos Duplicados

## 🚨 Problema Identificado

Se detectaron permisos duplicados en la base de datos que causan problemas en la interfaz de Roles y Permisos:

### Síntomas:
- Permisos aparecen duplicados (en inglés y español)
- Al seleccionar un permiso, se seleccionan ambos duplicados
- Confusión en la interfaz de usuario

### Causa:
Permisos con el mismo `name` pero diferentes `id` y `description`:

```sql
-- Ejemplo de duplicados:
ID 1:   'sistemas:users:create' - 'Crear usuarios del sistema'
ID 434: 'sistemas:users:create' - 'Create users in sistemas module'
```

## 🛠️ Soluciones Implementadas

### 1. Solución Temporal (Frontend)
**Archivo**: `frontend/lib/services/role.ts`

Se agregó filtrado de duplicados en el frontend que:
- Mantiene solo permisos con descripción en español
- Si no hay descripción en español, mantiene el primero
- Filtra permisos con formato "in X module" (inglés)

### 2. Solución Permanente (Base de Datos)

#### Scripts Disponibles:

1. **`identify-duplicate-permissions.sql`**
   - Identifica todos los permisos duplicados
   - Muestra detalles de cada duplicado

2. **`clean-duplicate-permissions.sql`**
   - Script SQL manual para limpiar duplicados
   - Mantiene permisos con descripción en español

3. **`run-clean-permissions.js`**
   - Script Node.js automatizado
   - Limpieza inteligente con logs detallados
   - Rollback automático en caso de error

## 🚀 Cómo Ejecutar la Limpieza

### Opción 1: Script Automatizado (Recomendado)
```bash
cd backend/scripts
node run-clean-permissions.js
```

### Opción 2: SQL Manual
```bash
# Conectar a la base de datos
psql -h localhost -U postgres -d comintec_db

# Ejecutar identificación
\i identify-duplicate-permissions.sql

# Ejecutar limpieza (revisar antes!)
\i clean-duplicate-permissions.sql
```

## ⚠️ Precauciones

1. **Hacer backup** antes de ejecutar la limpieza
2. **Revisar** los permisos identificados antes de eliminar
3. **Probar** en ambiente de desarrollo primero
4. **Verificar** que no se rompan asignaciones de roles

## 📊 Verificación Post-Limpieza

Después de la limpieza, verificar:

```sql
-- No debe haber duplicados
SELECT name, COUNT(*) 
FROM permissions 
GROUP BY name 
HAVING COUNT(*) > 1;

-- Contar permisos por módulo
SELECT module, COUNT(*) 
FROM permissions 
GROUP BY module 
ORDER BY module;
```

## 🔄 Estado Actual

- ✅ **Problema resuelto**: Base de datos limpiada exitosamente
- ✅ **Filtrado temporal**: Removido del frontend
- ✅ **490 permisos únicos**: Sin duplicados en la BD
- ✅ **Interfaz funcionando**: Permisos únicos y en español

## 📝 Notas

- La solución temporal permite usar la interfaz sin problemas
- La limpieza de BD es opcional pero recomendada
- Los scripts están diseñados para ser seguros y reversibles
