#!/bin/bash

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuración de Supabase
DB_HOST="db.awlxzhrubqkryrenunun.supabase.co"
DB_PORT=5432
DB_USER="postgres"
DB_NAME="postgres"
DB_PASSWORD="appcomintecpassw0rd"

# Directorios
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OUTPUT_DIR="$SCRIPT_DIR/.."

# Archivos de salida
BACKUP_FILE="$OUTPUT_DIR/comintec_db.sql"

echo "Exportando toda la base de datos (excepto tablas internas de Supabase)..."

PGPASSWORD=$DB_PASSWORD pg_dump \
  --schema=public \
  --no-owner \
  --no-privileges \
  --exclude-table=supabase_* \
  --exclude-table=_realtime* \
  -h $DB_HOST \
  -p $DB_PORT \
  -U $DB_USER \
  -d $DB_NAME \
  > $BACKUP_FILE

if [ $? -eq 0 ]; then
    echo "✅ Backup completo guardado en: $BACKUP_FILE"
else
    echo "❌ Error al exportar la base de datos"
    exit 1
fi