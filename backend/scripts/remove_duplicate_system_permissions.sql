-- Script para eliminar permisos duplicados del módulo 'system'
-- Los permisos 'system:*' son duplicados de 'sistemas:*' y no se usan en el frontend
-- Fecha: 2025-07-14
-- Autor: Sistema de limpieza de permisos

-- ============================================================================
-- PASO 1: Verificar qué permisos 'system:*' existen antes de eliminar
-- ============================================================================

SELECT 
    id, 
    name, 
    module, 
    action, 
    description,
    created_at
FROM permissions 
WHERE module = 'system' 
ORDER BY id;

-- ============================================================================
-- PASO 2: Verificar si hay asignaciones de roles a estos permisos
-- ============================================================================

SELECT 
    rp.role_id,
    r.name as role_name,
    p.id as permission_id,
    p.name as permission_name,
    p.module
FROM role_permissions rp
JOIN permissions p ON rp.permission_id = p.id
JOIN roles r ON rp.role_id = r.id
WHERE p.module = 'system'
ORDER BY rp.role_id, p.name;

-- ============================================================================
-- PASO 3: ELIMINAR las asignaciones de roles a permisos 'system:*'
-- ============================================================================

-- Eliminar las relaciones role_permissions para permisos del módulo 'system'
DELETE FROM role_permissions 
WHERE permission_id IN (
    SELECT id FROM permissions WHERE module = 'system'
);

-- Verificar que se eliminaron las asignaciones
SELECT COUNT(*) as remaining_role_permissions 
FROM role_permissions rp
JOIN permissions p ON rp.permission_id = p.id
WHERE p.module = 'system';

-- ============================================================================
-- PASO 4: ELIMINAR los permisos 'system:*'
-- ============================================================================

-- Eliminar los permisos del módulo 'system'
DELETE FROM permissions 
WHERE module = 'system';

-- Verificar que se eliminaron los permisos
SELECT COUNT(*) as remaining_system_permissions 
FROM permissions 
WHERE module = 'system';

-- ============================================================================
-- PASO 5: Verificación final - Mostrar permisos 'sistemas:*' que siguen activos
-- ============================================================================

SELECT 
    id, 
    name, 
    module, 
    action, 
    description
FROM permissions 
WHERE module = 'sistemas' 
ORDER BY name;

-- ============================================================================
-- RESUMEN DE PERMISOS ELIMINADOS
-- ============================================================================

-- Los siguientes permisos fueron eliminados (IDs 912-923):
-- 912: system:dashboard:read
-- 913: system:dashboard:configure  
-- 914: system:settings:read
-- 915: system:settings:update
-- 916: system:maintenance:enable
-- 917: system:maintenance:disable
-- 918: system:backup:create
-- 919: system:backup:restore
-- 920: system:logs:read
-- 921: system:logs:download
-- 922: system:health:check
-- 923: system:health:monitor

-- EQUIVALENCIAS EN 'sistemas:*' (que siguen activos):
-- system:backup:create     → sistemas:backups:create
-- system:backup:restore    → sistemas:backups:restore  
-- system:logs:read         → sistemas:audit_logs:read
-- system:dashboard:*       → (funcionalidad en dashboard general)
-- system:settings:*        → sistemas:system_config:*
-- system:maintenance:*     → (funcionalidad administrativa)
-- system:health:*          → (funcionalidad de monitoreo)

-- ============================================================================
-- INSTRUCCIONES DE USO:
-- ============================================================================
-- 1. Ejecutar este script en la consola de Supabase
-- 2. Revisar los resultados de cada SELECT antes de continuar
-- 3. Si hay asignaciones de roles, considerar migrarlas a permisos 'sistemas:*'
-- 4. Ejecutar las eliminaciones paso a paso
-- 5. Verificar que no se rompió ninguna funcionalidad
