#!/usr/bin/env ts-node
import { DataSource, EntityManager } from 'typeorm';
import * as fs from 'fs';
import * as path from 'path';

// Archivos SQL en orden de ejecución
const SQL_FILES = [
  {
    name: 'Schema Core',
    path: path.resolve(__dirname, '../src/database/comintec_schema_core.sql'),
    validation: async (ds: DataSource) => {
      // Verificar si la tabla users existe
      const result = await ds.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'users'
        );
      `);
      return !result[0].exists;
    }
  },
  {
    name: 'Permissions Data',
    path: path.resolve(__dirname, '../src/database/permissions_data.sql'),
    validation: async (ds: DataSource) => {
      // Verificar si hay permisos
      const result = await ds.query(`
        SELECT COUNT(*) FROM permissions;
      `).catch(() => [{ count: '0' }]);
      return parseInt(result[0].count) === 0;
    }
  },
  {
    name: 'Users Data',
    path: path.resolve(__dirname, '../src/database/users_data.sql'),
    validation: async (ds: DataSource) => {
      // Verificar si hay usuarios
      const result = await ds.query(`
        SELECT COUNT(*) FROM users;
      `).catch(() => [{ count: '0' }]);
      return parseInt(result[0].count) === 0;
    }
  }
];

// Configuración de Supabase
const AppDataSource = new DataSource({
  type: 'postgres',
  host: 'db.awlxzhrubqkryrenunun.supabase.co',
  port: 5432,
  username: 'postgres',
  password: 'appcomintecpassw0rd',
  database: 'postgres',
  ssl: true,
  extra: {
    ssl: {
      rejectUnauthorized: false
    }
  }
});

async function executeSQL(manager: EntityManager | DataSource, sql: string): Promise<void> {
  const statements = sql
    .split(';')
    .map(s => s.trim())
    .filter(s => s.length > 0);

  for (const statement of statements) {
    try {
      await manager.query(statement);
    } catch (error: any) {
      console.error('Error ejecutando SQL:', error.message);
      console.error('Statement:', statement);
      throw error;
    }
  }
}

async function main() {
  try {
    console.log('🔄 Conectando a Supabase...');
    await AppDataSource.initialize();
    console.log('✅ Conexión establecida');

    // Verificar existencia de archivos
    for (const file of SQL_FILES) {
      if (!fs.existsSync(file.path)) {
        console.error(`❌ Archivo no encontrado: ${file.path}`);
        process.exit(1);
      }
    }

    // Ejecutar archivos en orden
    for (const file of SQL_FILES) {
      console.log(`\n📄 Procesando ${file.name}...`);

      // Verificar si necesitamos ejecutar este archivo
      const shouldExecute = await file.validation(AppDataSource);
      
      if (!shouldExecute) {
        console.log(`⏭️ Saltando ${file.name} - Ya está aplicado`);
        continue;
      }

      console.log(`📥 Ejecutando ${path.basename(file.path)}...`);
      const sql = fs.readFileSync(file.path, 'utf8');

      // Ejecutar dentro de una transacción
      await AppDataSource.transaction(async transactionalEntityManager => {
        await executeSQL(transactionalEntityManager, sql);
      });

      console.log(`✅ ${file.name} ejecutado correctamente`);
    }

    console.log('\n✅ Proceso de configuración de base de datos completado.');

  } catch (error: any) {
    console.error('❌ Error durante la ejecución:', error.message);
    process.exit(1);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
    }
  }
}

main(); 