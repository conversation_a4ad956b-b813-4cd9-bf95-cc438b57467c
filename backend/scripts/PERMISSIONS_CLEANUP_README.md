# Limpieza de Permisos Duplicados - system:* vs sistemas:*

## 🎯 **Objetivo**
Eliminar permisos duplicados del módulo `system:*` que no se usan en el frontend y mantener solo los permisos `sistemas:*` que están activamente implementados.

## 🔍 **Problema Identificado**
- **Permisos duplicados:** Existían permisos tanto en `system:*` como en `sistemas:*`
- **Inconsistencia:** El frontend usa `sistemas:*` pero la BD tenía ambos
- **Confusión:** Dos módulos haciendo lo mismo generaba ambigüedad

## 📋 **Permisos Eliminados**

### **system:* (ELIMINADOS)**
```
912: system:dashboard:read
913: system:dashboard:configure  
914: system:settings:read
915: system:settings:update
916: system:maintenance:enable
917: system:maintenance:disable
918: system:backup:create
919: system:backup:restore
920: system:logs:read
921: system:logs:download
922: system:health:check
923: system:health:monitor
```

### **sistemas:* (MANTENIDOS)**
```
sistemas:users:*          - Gestión de usuarios
sistemas:roles:*          - Gestión de roles
sistemas:permissions:*    - Gestión de permisos
sistemas:backups:*        - Gestión de backups
sistemas:audit_logs:*     - Logs de auditoría
sistemas:system_config:*  - Configuración del sistema
sistemas:notifications:*  - Notificaciones del sistema
```

## 🔄 **Equivalencias**

| system:* (ELIMINADO) | sistemas:* (ACTIVO) | Uso en Frontend |
|----------------------|---------------------|-----------------|
| `system:backup:create` | `sistemas:backups:create` | ✅ Usado |
| `system:backup:restore` | `sistemas:backups:restore` | ✅ Usado |
| `system:logs:read` | `sistemas:audit_logs:read` | ✅ Usado |
| `system:settings:*` | `sistemas:system_config:*` | ✅ Usado |
| `system:dashboard:*` | Dashboard general | ❌ No específico |
| `system:maintenance:*` | Funcionalidad admin | ❌ No implementado |
| `system:health:*` | Monitoreo | ❌ No implementado |

## 🛠️ **Scripts Disponibles**

### **1. Script Detallado** (`remove_duplicate_system_permissions.sql`)
- Verificaciones paso a paso
- Muestra qué se va a eliminar antes de hacerlo
- Incluye rollback manual
- Recomendado para producción

### **2. Script Rápido** (`quick_remove_system_permissions.sql`)
- Eliminación directa con transacción
- Verificaciones básicas
- Rollback automático si hay error
- Para desarrollo/testing

## 📝 **Instrucciones de Ejecución**

### **Opción 1: Script Detallado (Recomendado)**
```sql
-- 1. Conectar a Supabase SQL Editor
-- 2. Ejecutar sección por sección del script
-- 3. Revisar resultados antes de continuar
-- 4. Ejecutar eliminaciones solo si todo está correcto
```

### **Opción 2: Script Rápido**
```sql
-- 1. Ejecutar todo el script de una vez
-- 2. Si hay error, automáticamente hace ROLLBACK
-- 3. Verificar resultados al final
```

## ✅ **Verificaciones Post-Limpieza**

### **1. Verificar eliminación**
```sql
SELECT COUNT(*) FROM permissions WHERE module = 'system';
-- Resultado esperado: 0
```

### **2. Verificar permisos activos**
```sql
SELECT COUNT(*) FROM permissions WHERE module = 'sistemas';
-- Resultado esperado: >30 permisos
```

### **3. Verificar frontend**
- Probar acceso a páginas de sistemas
- Verificar que los permisos funcionan correctamente
- No debería haber errores de permisos

## 🔧 **Cambios en Código**

### **Frontend Corregido**
- `sistemas:audit:read` → `sistemas:audit_logs:read`
- `sistemas:backup:*` → `sistemas:backups:*`

### **Backend Actualizado**
- Eliminadas entradas `SYSTEM_*` del enum de permisos
- Mantenidas solo entradas `SISTEMAS_*`

## 🚨 **Precauciones**
- ✅ Hacer backup de la BD antes de ejecutar
- ✅ Probar en desarrollo primero
- ✅ Verificar que no hay roles asignados a permisos `system:*`
- ✅ Confirmar que el frontend funciona después de la limpieza

## 📊 **Resultado Final**
- **Permisos eliminados:** 12 permisos `system:*`
- **Permisos mantenidos:** Todos los `sistemas:*`
- **Funcionalidad:** Sin cambios, solo limpieza
- **Consistencia:** 100% frontend usa `sistemas:*`
