-- Script rápido para eliminar permisos duplicados 'system:*'
-- ADVERTENCIA: Este script elimina directamente sin verificaciones extensas
-- Usar solo si estás seguro de que no hay dependencias críticas

BEGIN;

-- Mostrar permisos que se van a eliminar
SELECT 'Permisos que serán eliminados:' as info;
SELECT id, name, module, action FROM permissions WHERE module = 'system' ORDER BY id;

-- Mostrar asignaciones de roles que serán eliminadas
SELECT 'Asignaciones de roles que serán eliminadas:' as info;
SELECT 
    rp.role_id,
    r.name as role_name,
    p.name as permission_name
FROM role_permissions rp
JOIN permissions p ON rp.permission_id = p.id
JOIN roles r ON rp.role_id = r.id
WHERE p.module = 'system';

-- Eliminar asignaciones de roles primero (para evitar violaciones de FK)
DELETE FROM role_permissions 
WHERE permission_id IN (
    SELECT id FROM permissions WHERE module = 'system'
);

-- Eliminar los permisos
DELETE FROM permissions WHERE module = 'system';

-- Verificar eliminación
SELECT 'Verificación - Permisos system restantes (debería ser 0):' as info;
SELECT COUNT(*) as count FROM permissions WHERE module = 'system';

SELECT 'Verificación - Permisos sistemas activos:' as info;
SELECT COUNT(*) as count FROM permissions WHERE module = 'sistemas';

COMMIT;

-- Si algo sale mal, puedes hacer ROLLBACK; antes del COMMIT;
