#!/bin/bash

# Script para exportar tablas específicas con formato INSERT
# Permite seleccionar qué tablas exportar

# Colores
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Configuración de Supabase
DB_HOST="db.awlxzhrubqkryrenunun.supabase.co"
DB_PORT=5432
DB_USER="postgres"
DB_NAME="postgres"
DB_PASSWORD="appcomintecpassw0rd"

# Directorios
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OUTPUT_DIR="$SCRIPT_DIR/.."

# Función para mostrar ayuda
show_help() {
    echo -e "${BLUE}=== Export Selectivo de Base de Datos ===${NC}"
    echo ""
    echo "Uso: $0 [opciones]"
    echo ""
    echo "Opciones:"
    echo "  --all              Exportar todas las tablas"
    echo "  --core             Solo tablas core (users, roles, permissions)"
    echo "  --data             Solo datos (sin estructura)"
    echo "  --structure        Solo estructura (sin datos)"
    echo "  --tables=tabla1,tabla2  Tablas específicas"
    echo "  --help             Mostrar esta ayuda"
    echo ""
    echo "Ejemplos:"
    echo "  $0 --core"
    echo "  $0 --tables=users,roles,permissions"
    echo "  $0 --all --data"
}

# Valores por defecto
EXPORT_ALL=false
EXPORT_CORE=false
EXPORT_DATA=true
EXPORT_STRUCTURE=true
SPECIFIC_TABLES=""

# Procesar argumentos
while [[ $# -gt 0 ]]; do
    case $1 in
        --all)
            EXPORT_ALL=true
            shift
            ;;
        --core)
            EXPORT_CORE=true
            shift
            ;;
        --data)
            EXPORT_DATA=true
            EXPORT_STRUCTURE=false
            shift
            ;;
        --structure)
            EXPORT_STRUCTURE=true
            EXPORT_DATA=false
            shift
            ;;
        --tables=*)
            SPECIFIC_TABLES="${1#*=}"
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}Opción desconocida: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# Definir tablas según la opción
if [[ "$EXPORT_CORE" == "true" ]]; then
    TABLES="users roles permissions user_roles role_permissions"
elif [[ "$SPECIFIC_TABLES" != "" ]]; then
    TABLES=$(echo $SPECIFIC_TABLES | tr ',' ' ')
elif [[ "$EXPORT_ALL" == "true" ]]; then
    # Obtener todas las tablas del esquema public
    TABLES=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "
        SELECT string_agg(tablename, ' ')
        FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename NOT LIKE 'supabase_%' 
        AND tablename NOT LIKE '_realtime%'
    " | tr -d ' \n')
else
    echo -e "${YELLOW}Selecciona una opción de exportación${NC}"
    show_help
    exit 1
fi

# Generar nombre de archivo
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
if [[ "$EXPORT_CORE" == "true" ]]; then
    BACKUP_FILE="$OUTPUT_DIR/comintec_core_${TIMESTAMP}.sql"
elif [[ "$SPECIFIC_TABLES" != "" ]]; then
    BACKUP_FILE="$OUTPUT_DIR/comintec_selective_${TIMESTAMP}.sql"
else
    BACKUP_FILE="$OUTPUT_DIR/comintec_full_${TIMESTAMP}.sql"
fi

echo -e "${BLUE}=== Exportación Selectiva de Base de Datos ===${NC}"
echo -e "${YELLOW}Tablas a exportar:${NC} $TABLES"
echo -e "${YELLOW}Archivo destino:${NC} $BACKUP_FILE"
echo -e "${YELLOW}Estructura:${NC} $EXPORT_STRUCTURE"
echo -e "${YELLOW}Datos:${NC} $EXPORT_DATA"
echo ""

# Crear archivo con encabezado
cat > $BACKUP_FILE << EOF
-- =============================
-- COMINTEC - Exportación Selectiva
-- Generado: $(date)
-- Tablas: $TABLES
-- =============================

SET statement_timeout = 0;
SET lock_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;

-- Crear extensiones necesarias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

EOF

# Exportar estructura si se solicita
if [[ "$EXPORT_STRUCTURE" == "true" ]]; then
    echo -e "${YELLOW}📋 Exportando estructura...${NC}"
    
    for table in $TABLES; do
        echo -e "  • Estructura de $table"
        
        cat >> $BACKUP_FILE << EOF

-- =============================
-- ESTRUCTURA: $table
-- =============================

EOF
        
        PGPASSWORD=$DB_PASSWORD pg_dump \
          --schema=public \
          --schema-only \
          --no-owner \
          --no-privileges \
          --table=$table \
          -h $DB_HOST \
          -p $DB_PORT \
          -U $DB_USER \
          -d $DB_NAME \
          >> $BACKUP_FILE
    done
fi

# Exportar datos si se solicita
if [[ "$EXPORT_DATA" == "true" ]]; then
    echo -e "${YELLOW}📊 Exportando datos...${NC}"
    
    cat >> $BACKUP_FILE << 'EOF'

-- =============================
-- DATOS DE LAS TABLAS
-- =============================

EOF
    
    for table in $TABLES; do
        echo -e "  • Datos de $table"
        
        # Contar registros
        COUNT=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM $table" | tr -d ' \n')
        
        cat >> $BACKUP_FILE << EOF

-- Tabla: $table ($COUNT registros)
EOF
        
        if [[ "$COUNT" -gt 0 ]]; then
            PGPASSWORD=$DB_PASSWORD pg_dump \
              --schema=public \
              --data-only \
              --no-owner \
              --no-privileges \
              --table=$table \
              --column-inserts \
              --rows-per-insert=1 \
              -h $DB_HOST \
              -p $DB_PORT \
              -U $DB_USER \
              -d $DB_NAME \
              >> $BACKUP_FILE
        else
            echo "-- (Sin datos)" >> $BACKUP_FILE
        fi
    done
fi

# Agregar footer
cat >> $BACKUP_FILE << 'EOF'

-- =============================
-- FIN DE LA EXPORTACIÓN
-- =============================

EOF

echo ""
echo -e "${GREEN}✅ Exportación completada: $BACKUP_FILE${NC}"

# Mostrar estadísticas
echo -e "${BLUE}📊 Estadísticas:${NC}"
echo "   📄 Tamaño: $(du -h $BACKUP_FILE | cut -f1)"
echo "   📝 Líneas: $(wc -l < $BACKUP_FILE)"
if [[ "$EXPORT_DATA" == "true" ]]; then
    echo "   🔍 INSERT statements: $(grep -c "^INSERT INTO" $BACKUP_FILE)"
fi

echo ""
echo -e "${YELLOW}💡 Para usar el archivo:${NC}"
echo "   • Ejecutar en consola SQL: \\i $(basename $BACKUP_FILE)"
echo "   • O copiar/pegar secciones específicas"
