const { Client } = require('pg');

// Configuración de la base de datos (usando las credenciales de Supabase)
const client = new Client({
  host: 'db.awlxzhrubqkryrenunun.supabase.co',
  port: 5432,
  database: 'postgres',
  user: 'postgres',
  password: 'appcomintecpassw0rd',
});

async function testAuditLogs() {
  try {
    await client.connect();
    console.log('✅ Conectado a la base de datos');

    // 1. Verificar si la tabla existe
    const tableExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'audit_logs'
      );
    `);
    console.log('📋 Tabla audit_logs existe:', tableExists.rows[0].exists);

    if (!tableExists.rows[0].exists) {
      console.log('❌ La tabla audit_logs no existe');
      return;
    }

    // 2. Contar total de registros
    const totalCount = await client.query('SELECT COUNT(*) FROM audit_logs');
    console.log('📊 Total de registros en audit_logs:', totalCount.rows[0].count);

    // 3. Obtener los últimos 10 registros
    const recentLogs = await client.query(`
      SELECT 
        al.id,
        al.action,
        al.target_entity,
        al.target_id,
        al.details,
        al.ip_address,
        al.created_at,
        u.name as user_name,
        u.email as user_email
      FROM audit_logs al
      LEFT JOIN users u ON al.user_id = u.id
      ORDER BY al.created_at DESC
      LIMIT 10
    `);

    console.log('\n📝 Últimos 10 registros de auditoría:');
    console.log('='.repeat(80));
    
    if (recentLogs.rows.length === 0) {
      console.log('❌ No hay registros de auditoría');
    } else {
      recentLogs.rows.forEach((log, index) => {
        console.log(`\n${index + 1}. ID: ${log.id}`);
        console.log(`   Usuario: ${log.user_name || log.user_email || 'N/A'}`);
        console.log(`   Acción: ${log.action}`);
        console.log(`   Entidad: ${log.target_entity || 'N/A'}`);
        console.log(`   Target ID: ${log.target_id || 'N/A'}`);
        console.log(`   IP: ${log.ip_address || 'N/A'}`);
        console.log(`   Fecha: ${log.created_at}`);
        console.log(`   Detalles: ${log.details ? JSON.stringify(log.details, null, 2) : 'Sin detalles'}`);
      });
    }

    // 4. Estadísticas por acción
    const actionStats = await client.query(`
      SELECT action, COUNT(*) as count
      FROM audit_logs
      GROUP BY action
      ORDER BY count DESC
    `);

    console.log('\n📈 Estadísticas por acción:');
    console.log('='.repeat(40));
    actionStats.rows.forEach(stat => {
      console.log(`${stat.action}: ${stat.count} registros`);
    });

    // 5. Estadísticas por entidad
    const entityStats = await client.query(`
      SELECT target_entity, COUNT(*) as count
      FROM audit_logs
      WHERE target_entity IS NOT NULL
      GROUP BY target_entity
      ORDER BY count DESC
    `);

    console.log('\n🎯 Estadísticas por entidad:');
    console.log('='.repeat(40));
    entityStats.rows.forEach(stat => {
      console.log(`${stat.target_entity}: ${stat.count} registros`);
    });

    // 6. Registros de las últimas 24 horas
    const recentActivity = await client.query(`
      SELECT COUNT(*) as count
      FROM audit_logs
      WHERE created_at >= NOW() - INTERVAL '24 hours'
    `);

    console.log(`\n⏰ Actividad en las últimas 24 horas: ${recentActivity.rows[0].count} registros`);

    // 7. Verificar estructura de la tabla
    const tableStructure = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'audit_logs'
      ORDER BY ordinal_position
    `);

    console.log('\n🏗️ Estructura de la tabla audit_logs:');
    console.log('='.repeat(50));
    tableStructure.rows.forEach(col => {
      console.log(`${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await client.end();
    console.log('\n✅ Conexión cerrada');
  }
}

// Ejecutar el test
testAuditLogs();
