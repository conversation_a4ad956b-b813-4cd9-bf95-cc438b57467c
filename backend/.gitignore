# Dependencias
/node_modules
/.pnp
.pnp.js

# Testing
/coverage

# Producción
/build
/dist

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# TypeScript
*.tsbuildinfo

# Cache
.cache/
.nyc_output/

# Misc
.DS_Store
Thumbs.db

# Docker
/docker-compose.override.yml

# Base de datos
*.sqlite
*.sqlite3

# Archivos temporales
tmp/
temp/

# Archivos de configuración local
config/local.*


# Archivos de migración de TypeORM
/migrations/**/*.js
!/migrations/.gitkeep

# Archivos de cobertura
.nyc_output/
.nyc_coverage/

# Archivos de depuración
*.pem
*.p12
*.key
*.csr
*.crt

# Archivos de sistema operativo
.DS_Store
Thumbs.db

# Archivos de editor
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Archivos de npm
package-lock.json
yarn.lock

# Archivos de TypeScript
*.tsbuildinfo

# Archivos de Jest
/jest_*
/coverage/

# Archivos de documentación
/docs/

# Archivos de migración
/migrations/*.js
!/migrations/.gitkeep

# Archivos de configuración de TypeORM
ormconfig.json

# Archivos de configuración de Docker
docker-compose.override.yml

# Archivos de configuración de VS Code
.vscode/*
!.vscode/extensions.json

# Archivos de configuración de IntelliJ
.idea/
*.iml

# Archivos de configuración de Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Archivos de configuración de macOS
.DS_Store
.AppleDouble
.LSOverride
Icon

# Archivos de configuración de Linux
*~
.Trash-*

# Archivos de configuración de npm
.npm
.npmrc

# Archivos de configuración de Yarn
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.pnp.*

# Archivos de configuración de pnpm
.pnpm-store/
.pnpm-state.json

# Archivos de configuración de Turbo
.turbo/

# Archivos de configuración de Next.js
.next/
out/

# Archivos de configuración de Nuxt.js
.nuxt/
.nitro/
.cache/
.nuxt-
*.log

# Archivos de configuración de Angular
.angular/
dist/

# Archivos de configuración de Svelte
.svelte-kit/
.svelte/

# Archivos de configuración de Vue
.vuepress/dist/

# Archivos de configuración de Gatsby
.cache/
public

# Archivos de configuración de Gridsome
.cache/
dist/

# Archivos de configuración de Gridsome
.cache/
dist/

# Archivos de configuración de Gridsome
.cache/
dist/
