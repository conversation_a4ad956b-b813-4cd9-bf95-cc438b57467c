# Backend de Comintec

Este es el backend de la aplicación Comintec, desarrollado con Node.js, TypeScript, Express y TypeORM.

## Requisitos Previos

- Node.js (v14 o superior)
- npm o yarn
- PostgreSQL (v12 o superior)
- TypeScript (instalado globalmente o como dependencia de desarrollo)

## Configuración del Entorno

1. Clona el repositorio:
   ```bash
   git clone [url-del-repositorio]
   cd backend
   ```

2. Instala las dependencias:
   ```bash
   npm install
   # o
   yarn
   ```

3. Crea un archivo `.env` basado en `.env.example` y configura las variables de entorno:
   ```bash
   cp .env.example .env
   ```

4. Configura la base de datos PostgreSQL y actualiza las variables de entorno en el archivo `.env`.

## Estructura del Proyecto

```
backend/
├── src/
│   ├── config/           # Configuraciones de la aplicación
│   ├── controllers/      # Controladores de la API
│   ├── dtos/            # Data Transfer Objects
│   ├── entities/         # Entidades de TypeORM
│   ├── exceptions/       # Excepciones personalizadas
│   ├── interfaces/       # Interfaces TypeScript
│   ├── middlewares/      # Middlewares de Express
│   ├── routes/           # Rutas de la API
│   ├── services/         # Lógica de negocio
│   ├── utils/            # Utilidades
│   ├── app.ts            # Configuración de Express
│   └── server.ts         # Punto de entrada de la aplicación
├── tests/               # Pruebas
├── .env.example         # Variables de entorno de ejemplo
├── package.json         # Dependencias y scripts
└── tsconfig.json        # Configuración de TypeScript
```

## Comandos Disponibles

- `npm run dev` - Inicia el servidor en modo desarrollo con recarga automática
- `npm run build` - Compila el código TypeScript a JavaScript
- `npm start` - Inicia el servidor en producción
- `npm run lint` - Ejecuta ESLint para verificar la calidad del código
- `npm test` - Ejecuta las pruebas
- `npm run typeorm` - Ejecuta comandos de TypeORM

## Migraciones

### Crear una nueva migración:
```bash
npm run typeorm migration:generate -- -n NombreDeLaMigracion
```

### Ejecutar migraciones pendientes:
```bash
npm run typeorm migration:run
```

### Revertir la última migración:
```bash
npm run typeorm migration:revert
```

## Variables de Entorno

| Variable        | Descripción                                | Valor por Defecto |
|----------------|-------------------------------------------|-------------------|
| PORT           | Puerto del servidor                       | 3000              |
| NODE_ENV       | Entorno de ejecución                     | development       |
| DB_HOST        | Host de la base de datos                 | localhost         |
| DB_PORT        | Puerto de la base de datos               | 5432              |
| DB_USERNAME    | Usuario de la base de datos              | postgres          |
| DB_PASSWORD    | Contraseña de la base de datos           | postgres          |
| DB_DATABASE    | Nombre de la base de datos               | comintec_db       |
| JWT_SECRET     | Clave secreta para JWT                   | -                 |
| JWT_EXPIRES_IN | Tiempo de expiración del token JWT       | 1h                |
| CORS_ORIGIN    | Orígenes permitidos para CORS            | *                 |

## Documentación de la API

La documentación de la API está disponible en `/api-docs` cuando el servidor está en ejecución en modo desarrollo.

## Despliegue

1. Asegúrate de que todas las pruebas pasen:
   ```bash
   npm test
   ```

2. Compila el código TypeScript:
   ```bash
   npm run build
   ```

3. Configura las variables de entorno en producción.

4. Inicia el servidor en producción:
   ```bash
   npm start
   ```

## Licencia

Este proyecto está bajo la licencia ISC.
