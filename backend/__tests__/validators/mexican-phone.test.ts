import { MexicanPhoneConstraint } from '../../src/validators/mexican-phone.validator';

describe('MexicanPhoneConstraint', () => {
  let validator: MexicanPhoneConstraint;

  beforeEach(() => {
    validator = new MexicanPhoneConstraint();
  });

  describe('Valid Mexican phone numbers', () => {
    const validPhones = [
      // Guadalajara (33)
      '33 1234 5678',
      '3312345678',
      '+52 33 1234 5678',
      '+523312345678',
      '+52 33-1234-5678',
      '+52 (33) 1234-5678',
      
      // Ciudad de México (55)
      '55 9876 5432',
      '5598765432',
      '+52 55 9876 5432',
      '+525598765432',
      
      // Monterrey (81)
      '81 2468 1357',
      '8124681357',
      '+52 81 2468 1357',
      '+528124681357',
      
      // Tijuana (664)
      '************',
      '6641234567',
      '+52 ************',
      '+526641234567',
      
      // <PERSON><PERSON><PERSON> (222)
      '************',
      '2223456789',
      '+52 ************',
      '+522223456789',
      
      // Mérida (999)
      '************',
      '9991112222',
      '+52 ************',
      '+529991112222',
    ];

    test.each(validPhones)('should validate %s as valid', (phone) => {
      const args = { object: {}, property: 'phone' } as any;
      expect(validator.validate(phone, args)).toBe(true);
    });
  });

  describe('Invalid Mexican phone numbers', () => {
    const invalidPhones = [
      // Códigos de área inválidos
      '12 1234 5678',  // 12 no es código de área válido
      '00 1234 5678',  // 00 no es código de área válido
      '************',  // 123 no es código de área válido
      
      // Formatos incorrectos
      '33 123 45',     // Muy corto
      '3 1234 5678',   // Código de área de 1 dígito
      
      // Números internacionales no mexicanos
      '****** 123 4567',   // Estados Unidos
      '+34 666 123 456',   // España
      '+54 11 1234 5678',  // Argentina
      
      // Formatos completamente incorrectos
      'abc def ghij',
      '************',     // Formato estadounidense
      '123',
      '12345678901',      // Muy largo
      '123456789',        // Muy corto
    ];

    test.each(invalidPhones)('should validate %s as invalid', (phone) => {
      const args = { object: {}, property: 'phone' } as any;
      expect(validator.validate(phone, args)).toBe(false);
    });
  });

  describe('Empty and null values', () => {
    it('should accept empty string', () => {
      const args = { object: {}, property: 'phone' } as any;
      expect(validator.validate('', args)).toBe(true);
    });

    it('should accept null', () => {
      const args = { object: {}, property: 'phone' } as any;
      expect(validator.validate(null as any, args)).toBe(true);
    });

    it('should accept undefined', () => {
      const args = { object: {}, property: 'phone' } as any;
      expect(validator.validate(undefined as any, args)).toBe(true);
    });

    it('should reject phone with only spaces', () => {
      const args = { object: {}, property: 'phone' } as any;
      expect(validator.validate('   ', args)).toBe(true); // Se trata como empty después de trim
    });
  });

  describe('Special format cases', () => {
    it('should validate alternative but valid distributions', () => {
      const args = { object: {}, property: 'phone' } as any;
      
      // Estos son formatos válidos con códigos de área de 3 dígitos
      expect(validator.validate('************', args)).toBe(true); // 331 es código válido
      expect(validator.validate('33 123 45678', args)).toBe(true); // 33 es código válido, resto es válido
    });

    it('should reject phones with non-numeric characters after normalization', () => {
      const args = { object: {}, property: 'phone' } as any;
      // Este número tiene paréntesis que se eliminan pero 12 no es código válido
      expect(validator.validate('(*************', args)).toBe(false);
      
      // Test con letras (definitivamente inválido)
      expect(validator.validate('abc def ghij', args)).toBe(false);
    });
  });

  describe('Phone normalization for storage', () => {
    const normalizationCases = [
      // Input -> Expected output
      { input: '33 1234 5678', expected: '+523312345678' },
      { input: '3312345678', expected: '+523312345678' },
      { input: '+52 33 1234 5678', expected: '+523312345678' },
      { input: '+523312345678', expected: '+523312345678' },
      { input: '33-1234-5678', expected: '+523312345678' },
      { input: '(33) 1234-5678', expected: '+523312345678' },
      { input: '33  1234  5678', expected: '+523312345678' },
      
      // Códigos de área de 3 dígitos
      { input: '************', expected: '+526641234567' },
      { input: '+52 ************', expected: '+526641234567' },
      { input: '************', expected: '+526641234567' },
      
      // Casos especiales
      { input: '', expected: '' },
      { input: null, expected: null },
      { input: undefined, expected: undefined },
    ];

    test.each(normalizationCases)('should normalize $input to $expected', ({ input, expected }) => {
      const result = MexicanPhoneConstraint.normalizePhoneForStorage(input as any);
      expect(result).toBe(expected);
    });
  });

  describe('Area code extraction', () => {
    const extractionCases = [
      // 2-digit area codes
      { phone: '3312345678', expected: '33' },
      { phone: '5512345678', expected: '55' },
      { phone: '8112345678', expected: '81' },
      
      // 3-digit area codes
      { phone: '6641234567', expected: '664' },
      { phone: '2223456789', expected: '222' },
      { phone: '9991112222', expected: '999' },
      
      // With country code
      { phone: '+523312345678', expected: '33' },
      { phone: '+526641234567', expected: '664' },
      
      // Invalid cases should return valid area codes if they exist
      { phone: '1212345678', expected: null }, // Invalid area code
      { phone: '0012345678', expected: null }, // Invalid area code
    ];

    test.each(extractionCases)('should extract area code from $phone', ({ phone, expected }) => {
      // Accedemos al método privado a través de una instancia
      const extractedCode = (validator as any).extractAreaCode(phone);
      expect(extractedCode).toBe(expected);
    });
  });

  describe('Error messages', () => {
    it('should provide appropriate error message', () => {
      const args = { object: {}, property: 'phone' } as any;
      const message = validator.defaultMessage(args);
      expect(message).toContain('formato mexicano válido');
      expect(message).toContain('código de área reconocido');
      expect(message).toContain('33 1234 5678');
      expect(message).toContain('+52 33 1234 5678');
    });
  });

  describe('Format validation', () => {
    const formatTests = [
      // Valid formats
      { phone: '3312345678', expected: true },
      { phone: '+523312345678', expected: true },
      
      // Invalid formats
      { phone: '331234567', expected: false },    // 9 digits
      { phone: '33123456789', expected: false },  // 11 digits
      { phone: '+5233123456789', expected: false }, // 11 digits with country code
      { phone: '+1234567890', expected: false },  // Wrong country code
    ];

    test.each(formatTests)('should validate format of $phone correctly', ({ phone, expected }) => {
      const result = (validator as any).isValidFormat(phone);
      expect(result).toBe(expected);
    });
  });
}); 