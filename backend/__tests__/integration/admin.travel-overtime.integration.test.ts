/// <reference types="jest" />
import request from 'supertest';
import { App } from '../../src/app';
import { testDataSource, initializeTestEnvironment, cleanupTestEnvironment } from '../../src/tests/test-setup';
import jwt from 'jsonwebtoken';
import { config } from '../../src/config';

const generateTestToken = (userId: number, roles: string[] = ['ROLE_ADMIN'], departmentId?: number, name: string = 'testuser') => {
  return jwt.sign({ sub: userId, type: 'access', roles, departmentId, name }, config.jwt.secret, { expiresIn: '1h' });
};

describe('Admin Travel/Overtime Requests Integration', () => {
  let app: any;
  let testUserAdminToken: string;
  let testAdminUserId: number;
  let testDepartmentId: number;

  beforeAll(async () => {
    await initializeTestEnvironment();
    app = new App(testDataSource).app;
    const adminUser = await testDataSource.query(
      `SELECT id, name FROM users WHERE email = '<EMAIL>' LIMIT 1`
    );
    if (adminUser.length === 0) throw new Error('Admin user not found');
    testAdminUserId = adminUser[0].id;
    const deptRes = await testDataSource.query(
      `SELECT id FROM departments WHERE name = 'Sistemas' LIMIT 1`
    );
    testDepartmentId = deptRes[0]?.id || 1;
    testUserAdminToken = generateTestToken(testAdminUserId, ['ROLE_ADMIN'], testDepartmentId, adminUser[0].name);
  });

  afterEach(async () => {
    await testDataSource.query("DELETE FROM requests WHERE title LIKE '%Test Travel%' OR title LIKE '%Test Overtime%'");
  });

  afterAll(async () => {
    await cleanupTestEnvironment();
  });

  describe('GET /api/admin/travel-requests', () => {
    beforeEach(async () => {
      await testDataSource.query(`INSERT INTO requests (request_type, title, requester_id, department_id, status, created_at) VALUES ('VIATICOS', 'Test Travel 1', $1, $2, 'PENDING', NOW())`, [testAdminUserId, testDepartmentId]);
      await testDataSource.query(`INSERT INTO requests (request_type, title, requester_id, department_id, status, created_at) VALUES ('VIATICOS', 'Test Travel 2', $1, $2, 'APPROVED', NOW())`, [testAdminUserId, testDepartmentId]);
    });
    it('200: admin should get all travel requests with filters', async () => {
      const response = await request(app)
        .get('/api/admin/travel-requests?status=PENDING')
        .set('Authorization', `Bearer ${testUserAdminToken}`)
        .expect(200);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data.some((r:any) => r.title === 'Test Travel 1')).toBe(true);
      expect(response.body.data.some((r:any) => r.status === 'PENDING')).toBe(true);
    });
  });

  describe('GET /api/admin/overtime-requests', () => {
    beforeEach(async () => {
      await testDataSource.query(`INSERT INTO requests (request_type, title, requester_id, department_id, status, created_at) VALUES ('TIEMPO_EXTRA', 'Test Overtime 1', $1, $2, 'PENDING', NOW())`, [testAdminUserId, testDepartmentId]);
      await testDataSource.query(`INSERT INTO requests (request_type, title, requester_id, department_id, status, created_at) VALUES ('TIEMPO_EXTRA', 'Test Overtime 2', $1, $2, 'APPROVED', NOW())`, [testAdminUserId, testDepartmentId]);
    });
    it('200: admin should get all overtime requests with filters', async () => {
      const response = await request(app)
        .get('/api/admin/overtime-requests?status=APPROVED')
        .set('Authorization', `Bearer ${testUserAdminToken}`)
        .expect(200);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data.some((r:any) => r.title === 'Test Overtime 2')).toBe(true);
      expect(response.body.data.some((r:any) => r.status === 'APPROVED')).toBe(true);
    });
  });
});
