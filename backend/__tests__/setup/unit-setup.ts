/**
 * Setup para tests unitarios
 * Configura mocks y herramientas necesarias para tests aislados (sin BD)
 */

/// <reference types="jest" />

// Mock de TypeORM - Evita conexiones a BD en tests unitarios
jest.mock('typeorm', () => {
  const originalModule = jest.requireActual('typeorm');
  
  // Mock del DataSource
  const mockDataSource = {
    initialize: jest.fn().mockResolvedValue(undefined),
    destroy: jest.fn().mockResolvedValue(undefined),
    isInitialized: true,
    getRepository: jest.fn(),
    createQueryBuilder: jest.fn(),
    transaction: jest.fn(),
    manager: {
      save: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      delete: jest.fn(),
      update: jest.fn(),
      create: jest.fn(),
      getRepository: jest.fn(),
    },
  };

  // Mock del Repository
  const mockRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    findOneBy: jest.fn(),
    save: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    remove: jest.fn(),
    createQueryBuilder: jest.fn(),
    count: jest.fn(),
    metadata: {
      columns: [],
      relations: [],
      tableName: 'mock_table',
    },
  };

  return {
    ...originalModule,
    DataSource: jest.fn().mockImplementation(() => mockDataSource),
    getRepository: jest.fn().mockReturnValue(mockRepository),
    Repository: jest.fn().mockImplementation(() => mockRepository),
    Entity: jest.fn().mockImplementation(() => (target: any) => target),
    Column: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
    PrimaryGeneratedColumn: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
    ManyToOne: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
    OneToMany: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
    JoinColumn: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
    CreateDateColumn: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
    UpdateDateColumn: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
  };
});

// Mock de bcryptjs
jest.mock('bcryptjs', () => ({
  hash: jest.fn().mockResolvedValue('hashed_password'),
  compare: jest.fn().mockResolvedValue(true),
  genSalt: jest.fn().mockResolvedValue('salt'),
}));

// Mock de jsonwebtoken
jest.mock('jsonwebtoken', () => ({
  sign: jest.fn().mockReturnValue('mock_jwt_token'),
  verify: jest.fn().mockReturnValue({ 
    id: 1, 
    email: '<EMAIL>',
    role: 'ADMIN' 
  }),
  decode: jest.fn().mockReturnValue({ 
    id: 1, 
    email: '<EMAIL>' 
  }),
}));

// Mock de variables de entorno para tests
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test_jwt_secret';
process.env.JWT_EXPIRES_IN = '1h';
process.env.DB_HOST = 'localhost';
process.env.DB_PORT = '5432';
process.env.DB_USER = 'test_user';
process.env.DB_PASS = 'test_password';
process.env.DB_NAME = 'test_db';

// Mock de data-source para evitar inicialización
jest.mock('../../src/data-source', () => ({
  AppDataSource: {
    initialize: jest.fn().mockResolvedValue(undefined),
    destroy: jest.fn().mockResolvedValue(undefined),
    isInitialized: true,
    getRepository: jest.fn(),
    manager: {
      save: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      delete: jest.fn(),
      update: jest.fn(),
      create: jest.fn(),
    },
  },
}));

// Mock de config para tests
jest.mock('../../src/config', () => ({
  config: {
    jwt: {
      secret: 'test_jwt_secret',
      expiresIn: '1h',
    },
    server: {
      port: 3001,
      env: 'test',
    },
    database: {
      type: 'postgres',
      host: 'localhost',
      port: 5432,
      username: 'test_user',
      password: 'test_password',
      database: 'test_db',
      synchronize: false,
      logging: false,
    },
  },
}));

// Global mocks para funciones comunes
(global as any).console = {
  ...console,
  // Silenciar logs durante tests a menos que sea error
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: console.error,
};

// Mock de express Request y Response para tests de controllers
export const mockRequest = (overrides = {}): any => ({
  body: {},
  params: {},
  query: {},
  headers: {},
  user: { id: 1, email: '<EMAIL>', role: 'ADMIN' },
  ...overrides,
});

export const mockResponse = (): any => {
  const res: any = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  res.send = jest.fn().mockReturnValue(res);
  res.cookie = jest.fn().mockReturnValue(res);
  res.clearCookie = jest.fn().mockReturnValue(res);
  return res;
};

export const mockNext = (): any => jest.fn();

// Helpers para tests
export const createMockRepository = <T>(entity: any) => ({
  find: jest.fn(),
  findOne: jest.fn(),
  findOneBy: jest.fn(),
  save: jest.fn(),
  create: jest.fn().mockImplementation((data: Partial<T>) => ({ ...data, id: 1 })),
  update: jest.fn(),
  delete: jest.fn(),
  remove: jest.fn(),
  query: jest.fn().mockResolvedValue([]), // Para consultas SQL raw
  createQueryBuilder: jest.fn().mockReturnValue({
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    getMany: jest.fn(),
    getOne: jest.fn(),
    getManyAndCount: jest.fn(),
  }),
  count: jest.fn(),
  metadata: {
    columns: [],
    relations: [],
    tableName: 'mock_table',
  },
});

// Setup global para cada test
beforeEach(() => {
  // Limpiar todos los mocks antes de cada test
  jest.clearAllMocks();
  
  // Reset de variables de entorno específicas para tests
  process.env.NODE_ENV = 'test';
});

// Cleanup después de cada test
afterEach(() => {
  jest.restoreAllMocks();
}); 