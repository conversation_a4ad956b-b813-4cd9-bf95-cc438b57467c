import 'reflect-metadata';
import { DataSource } from 'typeorm';
import { User } from '../../src/entities/user.entity';
import { UserService } from '../../src/services/user.service';
import { CreateUserDto, UpdateUserDto } from '../../src/dtos/user.dto';
import { BadRequestException } from '../../src/exceptions/http.exception';

describe('UserService - Email Validation', () => {
  let dataSource: DataSource;
  let userService: UserService;
  let userRepository: any;

  beforeAll(async () => {
    // Mock DataSource
    userRepository = {
      findOne: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
    };

    dataSource = {
      getRepository: jest.fn().mockReturnValue(userRepository),
    } as any;

    userService = new UserService(dataSource);
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Email uniqueness validation', () => {
    const validUserData: CreateUserDto = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'SecurePass123!',
      phone: '33 1234 5678',
      area: 'Sistemas',
    };

    describe('createUser', () => {
      it('should handle database constraint violation error', async () => {
        userRepository.findOne.mockResolvedValue(null); // Primera verificación pasa
        userRepository.create.mockReturnValue({ id: 1, ...validUserData });
        
        // Simular error de constraint de BD
        const constraintError = new Error('duplicate key value violates unique constraint');
        (constraintError as any).code = '23505';
        (constraintError as any).constraint = 'users_email_key';
        
        userRepository.save.mockRejectedValue(constraintError);

        await expect(userService.createUser(validUserData, 1))
          .rejects.toThrow('El correo electrónico ya está en uso');
      });

      it('should handle other database errors without modification', async () => {
        userRepository.findOne.mockResolvedValue(null);
        userRepository.create.mockReturnValue({ id: 1, ...validUserData });
        
        const otherError = new Error('Connection timeout');
        userRepository.save.mockRejectedValue(otherError);

        await expect(userService.createUser(validUserData, 1))
          .rejects.toThrow('Connection timeout');
      });

      it('should detect duplicate email in pre-check', async () => {
        const existingUser = { id: 2, email: validUserData.email };
        userRepository.findOne.mockResolvedValue(existingUser);

        await expect(userService.createUser(validUserData, 1))
          .rejects.toThrow('El correo electrónico ya está en uso');
          
        // Verificar que no se intentó guardar
        expect(userRepository.save).not.toHaveBeenCalled();
      });
    });

    describe('updateUser', () => {
      it('should handle database constraint violation during update', async () => {
        const existingUser = {
          id: 1,
          name: 'Original Name',
          email: '<EMAIL>',
          phone: '33 1111 1111',
          area: 'Sistemas',
          status: 'activo',
        };

        const updateData: UpdateUserDto = {
          email: '<EMAIL>',
        };

        userRepository.findOne
          .mockResolvedValueOnce(existingUser) // Buscar usuario a actualizar
          .mockResolvedValueOnce(null); // No encontrar email duplicado en pre-check
        
        // Simular error de constraint durante el save
        const constraintError = new Error('duplicate key value violates unique constraint');
        (constraintError as any).code = '23505';
        (constraintError as any).constraint = 'users_email_key';
        
        userRepository.save.mockRejectedValue(constraintError);

        await expect(userService.updateUser(1, updateData, 123))
          .rejects.toThrow('El correo electrónico ya está en uso');
      });

      it('should allow user to update with same email (no change)', async () => {
        const existingUser = {
          id: 1,
          name: 'Test User',
          email: '<EMAIL>',
          phone: '33 1111 1111',
          area: 'Sistemas',
          status: 'activo',
        };

        const updateData: UpdateUserDto = {
          email: '<EMAIL>', // Mismo email
          name: 'Updated Name',
        };

        userRepository.findOne.mockResolvedValue(existingUser);
        userRepository.save.mockResolvedValue({ ...existingUser, ...updateData });

        const result = await userService.updateUser(1, updateData, 123);

        expect(result.name).toBe('Updated Name');
        expect(result.email).toBe('<EMAIL>');
        expect(userRepository.save).toHaveBeenCalled();
      });

      it('should prevent updating to email used by another user', async () => {
        const userToUpdate = { id: 1, email: '<EMAIL>' };
        const userWithTargetEmail = { id: 2, email: '<EMAIL>' };

        userRepository.findOne
          .mockResolvedValueOnce(userToUpdate) // Usuario a actualizar
          .mockResolvedValueOnce(userWithTargetEmail); // Usuario con email objetivo

        const updateData: UpdateUserDto = {
          email: '<EMAIL>',
        };

        await expect(userService.updateUser(1, updateData, 123))
          .rejects.toThrow('El correo electrónico ya está en uso');
          
        expect(userRepository.save).not.toHaveBeenCalled();
      });
    });

    describe('Email format validation scenarios', () => {
      it('should work with various valid email formats', async () => {
        const validEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        for (const email of validEmails) {
          userRepository.findOne.mockResolvedValue(null);
          userRepository.create.mockReturnValue({ id: 1, ...validUserData, email });
          userRepository.save.mockResolvedValue({ id: 1, ...validUserData, email });

          const userData = { ...validUserData, email };
          const result = await userService.createUser(userData, 1);
          
          expect(result.email).toBe(email);
        }
      });
    });

    describe('Case sensitivity handling', () => {
      it('should handle email case sensitivity properly', async () => {
        const existingUser = { id: 1, email: '<EMAIL>' };
        userRepository.findOne.mockResolvedValue(existingUser);

        const userData = { ...validUserData, email: '<EMAIL>' };

        await expect(userService.createUser(userData, 1))
          .rejects.toThrow('El correo electrónico ya está en uso');
      });
    });
  });
}); 