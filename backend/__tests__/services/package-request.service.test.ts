// @ts-nocheck
/// <reference types="jest" />

import { jest, describe, it, expect, beforeEach } from '@jest/globals';

// Mockear la función específica que queremos controlar
const mockExecuteQuery = jest.fn();

jest.mock('../../src/services/db.service', () => ({
  ...jest.requireActual('../../src/services/db.service'), // Importamos el módulo real
  executeQuery: mockExecuteQuery, // Sobreescribimos solo la función que nos interesa
}));

import { PackageRequestService } from '../../src/services/package-request.service';
import { CreatePackageRequestDto, UpdatePackageRequestDto, AdminPaginationQueryDto } from '../../src/dtos/admin.dto';
import { HttpException } from '../../src/exceptions/http.exception';
import { User } from '../../src/entities/user.entity';


describe('PackageRequestService', () => {
  let packageRequestService: PackageRequestService;
  let mockCurrentUser: User;

  beforeEach(() => {
    packageRequestService = new PackageRequestService();
    mockExecuteQuery.mockClear();

    mockCurrentUser = {
      id: 1,
      name: 'testuser',
      email: '<EMAIL>',
      phone: '1234567890',
      password: 'hashedpassword',
      status: 'activo',
      area: 'IT',
      active: true,
      created_at: new Date(),
      updated_at: new Date(),
      created_by: undefined,
      updated_by: undefined,
      roles: [{ id: 1, name: 'ROLE_USER', permissions: [] }],
      refresh_token: 'test-refresh-token'
    };
  });

  describe('createPackageRequest', () => {
    it('debería crear y devolver una solicitud de paquetería', async () => {
      const dto: CreatePackageRequestDto = {
        title: 'Envío Urgente',
        description: 'Materiales para cliente X',
        destinationAddress: 'Calle Falsa 123',
      };
      const mockCreatedRequest = {
        id: 1, ...dto,
        requester_id: mockCurrentUser.id,
        status: 'PENDING',
        request_type: 'PAQUETERIA',
        department_id: undefined, // O el valor que se espera si no se pasa en DTO
        priority: 'MEDIUM'      // Default del servicio
      };
      mockExecuteQuery.mockResolvedValue({ rows: [mockCreatedRequest], rowCount: 1 });

      const result = await packageRequestService.createPackageRequest(dto, mockCurrentUser);

      expect(mockExecuteQuery).toHaveBeenCalledTimes(1);
      expect(mockExecuteQuery.mock.calls[0][0]).toContain('INSERT INTO requests');
      expect(mockExecuteQuery.mock.calls[0][1]).toEqual([
        'PAQUETERIA',
        dto.title,
        dto.description,
        mockCurrentUser.id,
        dto.departmentId, // Será undefined si no se pasa en DTO
        dto.priority || 'MEDIUM',
        'PENDING',
        dto.destinationAddress,
        dto.shippingCompany,
        dto.packageWeight,
        dto.packageDimensions
      ]);
      expect(result).toEqual(mockCreatedRequest);
    });

    it('debería lanzar HttpException si la creación en BD falla', async () => {
      const dto: CreatePackageRequestDto = { title: 'Test', destinationAddress: 'Addr' };
      mockExecuteQuery.mockResolvedValue({ rows: [], rowCount: 0 });

      await expect(packageRequestService.createPackageRequest(dto, mockCurrentUser))
        .rejects.toThrow(new HttpException(500, 'No se pudo crear la solicitud de paquetería'));
    });

    it('debería manejar errores de constraint de FK (ej. department_id no existe)', async () => {
        const dto: CreatePackageRequestDto = { title: 'Test FK', destinationAddress: 'Addr FK', departmentId: 999 };
        mockExecuteQuery.mockRejectedValue({ code: '23503', message: 'fk constraint fail', column: 'department_id' });

        await expect(packageRequestService.createPackageRequest(dto, mockCurrentUser))
            .rejects.toThrow(new HttpException(400, `Error de referencia: Verifique que el departamento con id ${dto.departmentId} existe.`));
    });
  });

  describe('getPackageRequestById', () => {
    it('debería devolver una solicitud si se encuentra', async () => {
      const mockRequest = { id: 1, title: 'Test', request_type: 'PAQUETERIA' };
      mockExecuteQuery.mockResolvedValue({ rows: [mockRequest], rowCount: 1 });

      const result = await packageRequestService.getPackageRequestById(1);
      expect(mockExecuteQuery).toHaveBeenCalledWith(expect.stringContaining('SELECT r.*'), [1], undefined);
      expect(result).toEqual(mockRequest);
    });

    it('debería lanzar HttpException 404 si la solicitud no se encuentra', async () => {
      mockExecuteQuery.mockResolvedValue({ rows: [], rowCount: 0 });
      await expect(packageRequestService.getPackageRequestById(99))
        .rejects.toThrow(new HttpException(404, 'Solicitud de paquetería no encontrada'));
    });
  });

  describe('getAllPackageRequests', () => {
    it('debería devolver una lista paginada de solicitudes', async () => {
        const paginationQuery: AdminPaginationQueryDto = { page: 1, limit: 5 };
        const mockRequests = [{ id: 1, title: 'Req 1' }, { id: 2, title: 'Req 2' }];
        const mockTotal = 2;
        
        // Primer mock para COUNT query
        mockExecuteQuery.mockResolvedValueOnce({ rows: [{ total: String(mockTotal) }], rowCount: 1 });
        // Segundo mock para DATA query
        mockExecuteQuery.mockResolvedValueOnce({ rows: mockRequests, rowCount: mockRequests.length });

        const result = await packageRequestService.getAllPackageRequests(paginationQuery);

        expect(mockExecuteQuery).toHaveBeenCalledTimes(2);
        expect(mockExecuteQuery.mock.calls[0][0]).toContain('COUNT(*) as total');
        expect(mockExecuteQuery.mock.calls[1][0]).toContain('SELECT r.*');
        // Params para la consulta de datos: [limit, offset] si no hay search
        expect(mockExecuteQuery.mock.calls[1][1]).toEqual([5, 0]);

        expect(result.data).toEqual(mockRequests);
        expect(result.total).toBe(mockTotal);
        expect(result.page).toBe(1);
        expect(result.limit).toBe(5);
    });

    it('debería manejar la búsqueda y ordenación', async () => {
        const paginationQuery: AdminPaginationQueryDto = { search: 'urgente', sortBy: 'title', sortOrder: 'ASC' };
        mockExecuteQuery.mockResolvedValueOnce({ rows: [{ total: '0' }], rowCount: 1 });
        mockExecuteQuery.mockResolvedValueOnce({ rows: [], rowCount: 0 });

        await packageRequestService.getAllPackageRequests(paginationQuery);

        expect(mockExecuteQuery.mock.calls[1][0]).toContain('r.title ILIKE $1');
        expect(mockExecuteQuery.mock.calls[1][0]).toContain('ORDER BY r.title ASC');
        expect(mockExecuteQuery.mock.calls[1][1]).toEqual(expect.arrayContaining(['%urgente%']));
    });
  });

  describe('updatePackageRequest', () => {
    it('debería actualizar y devolver la solicitud (llamando a getById después)', async () => {
        const updateDto: UpdatePackageRequestDto = { title: 'Título Actualizado' };
        const requestId = 1;
        const mockUpdatedRequestInDb = {id: requestId, title: 'Título Actualizado'}; // Lo que RETURNING * daría
        const mockFinalRequestObject = { id: requestId, title: 'Título Actualizado', request_type: 'PAQUETERIA', /* otros campos de getById */ };

        mockExecuteQuery
          .mockResolvedValueOnce({ rows: [mockUpdatedRequestInDb], rowCount: 1 }) // Para el UPDATE
          .mockResolvedValueOnce({ rows: [mockFinalRequestObject], rowCount: 1 }); // Para el getPackageRequestById interno

        const result = await packageRequestService.updatePackageRequest(requestId, updateDto);

        expect(mockExecuteQuery.mock.calls[0][0]).toContain('UPDATE requests');
        expect(mockExecuteQuery.mock.calls[0][0]).toContain('title = $1');
        expect(mockExecuteQuery.mock.calls[0][1]).toEqual(['Título Actualizado', requestId]);
        expect(mockExecuteQuery.mock.calls[1][0]).toContain('SELECT r.*'); // de getPackageRequestById
        expect(mockExecuteQuery.mock.calls[1][1]).toEqual([requestId]);
        expect(result).toEqual(mockFinalRequestObject);
    });

     it('debería lanzar 404 si la solicitud a actualizar no existe', async () => {
        mockExecuteQuery.mockResolvedValue({ rows: [], rowCount: 0 });
        await expect(packageRequestService.updatePackageRequest(99, { title: 'No existe'}))
            .rejects.toThrow(new HttpException(404, 'Solicitud de paquetería no encontrada o no se pudo actualizar'));
    });
  });

  describe('deletePackageRequest', () => {
    it('debería eliminar la solicitud', async () => {
        mockExecuteQuery.mockResolvedValue({ rows: [], rowCount: 1 });
        await expect(packageRequestService.deletePackageRequest(1)).resolves.not.toThrow();
        expect(mockExecuteQuery).toHaveBeenCalledWith(expect.stringContaining('DELETE FROM requests'), [1], undefined);
    });
    it('debería lanzar 404 si la solicitud a eliminar no existe', async () => {
        mockExecuteQuery.mockResolvedValue({ rows: [], rowCount: 0 });
        await expect(packageRequestService.deletePackageRequest(99))
            .rejects.toThrow(new HttpException(404, 'Solicitud de paquetería no encontrada'));
    });
  });
});
