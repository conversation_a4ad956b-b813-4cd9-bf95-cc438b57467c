import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { DataSource } from 'typeorm';
import { User } from '../../src/entities/user.entity';
import { PasswordHistory } from '../../src/entities/password-history.entity';
import { UserService } from '../../src/services/user.service';
import { PasswordHistoryService } from '../../src/services/password-history.service';
import { ChangePasswordDto, CreateUserDto } from '../../src/dtos/user.dto';
import { BadRequestException } from '../../src/exceptions/http.exception';
import { PasswordPolicyConstraint } from '../../src/validators/password-policy.validator';
import { ValidationArguments } from 'class-validator';
import { createTestConnection, closeTestConnection } from '../../src/tests/test-setup';

describe('Password Policy and History', () => {
  let testDataSource: DataSource;
  let userService: UserService;
  let passwordHistoryService: PasswordHistoryService;
  let testUser: User;

  beforeAll(async () => {
    testDataSource = await createTestConnection();
    userService = new UserService(testDataSource);
    passwordHistoryService = new PasswordHistoryService();
  });

  afterAll(async () => {
    await closeTestConnection();
  });

  beforeEach(async () => {
    // Crear usuario de prueba
    const createUserDto: CreateUserDto = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'StrongPass123!',
      phone: '33 1234 5678',
      area: 'Test Area'
    };

    testUser = await userService.createUser(createUserDto, 1);
  });

  afterEach(async () => {
    // Limpiar datos de prueba
    if (testUser) {
      await testDataSource.getRepository(PasswordHistory).delete({ userId: testUser.id });
      await testDataSource.getRepository(User).delete({ id: testUser.id });
    }
  });

  describe('Password Policy Validation', () => {
    it('should accept strong passwords that meet all requirements', async () => {
      const validator = new PasswordPolicyConstraint();
      
      const strongPasswords = [
        'SecurePass123!',
        'MyStr0ng#Password',
        'Complex@Pass1',
        'Adv4nced&Secure',
        'Super$tr0ngP@ss'
      ];

      for (const password of strongPasswords) {
        const result = validator.validate(password);
        expect(result).toBe(true);
      }
    });

    it('should reject passwords that do not meet requirements', async () => {
      const validator = new PasswordPolicyConstraint();
      
      const weakPasswords = [
        'short',                    // Too short
        'nouppercasehere123!',      // No uppercase
        'NOLOWERCASEHERE123!',      // No lowercase
        'NoNumbersHere!',           // No numbers
        'NoSpecialChars123',        // No special characters
        'aaabbbccc123!',           // Too many consecutive chars
        'abcd1234!',               // Not enough unique chars
        'passwordAAA111!'          // Forbidden word
      ];

      for (const password of weakPasswords) {
        const result = validator.validate(password);
        expect(result).toBe(false);
      }
    });

    it('should detect forbidden patterns and words', async () => {
      const validator = new PasswordPolicyConstraint();
      
      const forbiddenPasswords = [
        'Password123!',            // Contains 'password'
        'Admin123!',               // Contains 'admin'
        'Username123!',            // Contains 'username'
        'Company123!',             // Contains 'company'
        'aaaa1234!',              // Too many consecutive chars
        '1111Password!',          // Too many consecutive numbers
        'ABCDPassword!',          // Consecutive alphabet
        '123Password!',           // Sequential numbers
        'AbcPassword!'            // Sequential alphabet
      ];

      for (const password of forbiddenPasswords) {
        const result = validator.validate(password);
        expect(result).toBe(false);
      }
    });
  });

  describe('Password History Management', () => {
    it('should prevent reusing recent passwords', async () => {
      const passwords = [
        'OldPassword1!',
        'OldPassword2!',
        'OldPassword3!',
        'NewPassword4!'
      ];

      // Cambiar contraseña varias veces
      for (let i = 0; i < passwords.length - 1; i++) {
        const changeDto: ChangePasswordDto = {
          currentPassword: i === 0 ? 'StrongPass123!' : passwords[i - 1],
          newPassword: passwords[i],
          confirmPassword: passwords[i]
        };

        await userService.changePassword(testUser.id, changeDto, 1);
      }

      // Intentar reutilizar una contraseña anterior
      const reuseDto: ChangePasswordDto = {
        currentPassword: passwords[passwords.length - 2],
        newPassword: 'OldPassword1!', // Contraseña ya usada
        confirmPassword: 'OldPassword1!'
      };

      await expect(userService.changePassword(testUser.id, reuseDto, 1))
        .rejects
        .toThrow('La nueva contraseña no puede ser igual a una de las últimas 5 contraseñas utilizadas');
    });

    it('should allow using passwords after they are out of history limit', async () => {
      const passwords = Array.from({ length: 7 }, (_, i) => `Password${i + 1}!@#`);
      
      // Cambiar contraseña 6 veces (más del límite de 5)
      for (let i = 0; i < passwords.length - 1; i++) {
        const changeDto: ChangePasswordDto = {
          currentPassword: i === 0 ? 'StrongPass123!' : passwords[i - 1],
          newPassword: passwords[i],
          confirmPassword: passwords[i]
        };

        await userService.changePassword(testUser.id, changeDto, 1);
      }

      // Ahora debería poder reutilizar la primera contraseña (fuera del límite de 5)
      const reuseDto: ChangePasswordDto = {
        currentPassword: passwords[passwords.length - 2],
        newPassword: 'Password1!@#', // Primera contraseña, ahora permitida
        confirmPassword: 'Password1!@#'
      };

      await expect(userService.changePassword(testUser.id, reuseDto, 1))
        .resolves
        .not.toThrow();
    });

    it('should track password metadata correctly', async () => {
      const changeDto: ChangePasswordDto = {
        currentPassword: 'StrongPass123!',
        newPassword: 'NewSecurePass456!',
        confirmPassword: 'NewSecurePass456!'
      };

      const result = await userService.changePassword(
        testUser.id, 
        changeDto, 
        1,
        '***********',
        'Test User Agent'
      );

      expect(result).toBeDefined();
      expect(result.strengthAnalysis).toBeDefined();
      expect(result.strengthAnalysis.level).toMatch(/Muy Fuerte|Fuerte/);
      expect(result.strengthAnalysis.score).toBeGreaterThan(70);

      // Verificar que se guardó en el historial con metadatos
      const history = await passwordHistoryService.getPasswordHistory(testUser.id, 5);
      expect(history.length).toBeGreaterThan(0);
      expect(history[0].ipAddress).toBe('***********');
      expect(history[0].userAgent).toBe('Test User Agent');
      expect(history[0].createdBy).toBe(1);
    });

    it('should provide password statistics', async () => {
      // Cambiar contraseña varias veces
      const passwords = ['Pass1!@#', 'Pass2!@#', 'Pass3!@#'];
      
      for (let i = 0; i < passwords.length; i++) {
        const changeDto: ChangePasswordDto = {
          currentPassword: i === 0 ? 'StrongPass123!' : passwords[i - 1],
          newPassword: passwords[i],
          confirmPassword: passwords[i]
        };

        await userService.changePassword(testUser.id, changeDto, 1);
        
        // Esperar un poco para que las fechas sean diferentes
        await new Promise(resolve => setTimeout(resolve, 10));
      }

      const stats = await passwordHistoryService.getPasswordStats(testUser.id);
      
      expect(stats.totalPasswordChanges).toBeGreaterThanOrEqual(passwords.length);
      expect(stats.lastPasswordChange).toBeDefined();
      expect(stats.lastPasswordChange).toBeInstanceOf(Date);
    });
  });

  describe('Password Strength Analysis', () => {
    it('should analyze password strength correctly', async () => {
      const changeDto: ChangePasswordDto = {
        currentPassword: 'StrongPass123!',
        newPassword: 'SuperComplexP@ssw0rd!2024',
        confirmPassword: 'SuperComplexP@ssw0rd!2024'
      };

      const result = await userService.changePassword(testUser.id, changeDto, 1);

      expect(result.strengthAnalysis).toBeDefined();
      expect(result.strengthAnalysis.score).toBeGreaterThan(80);
      expect(result.strengthAnalysis.level).toBe('Muy Fuerte');
      expect(result.strengthAnalysis.suggestions).toBeDefined();
      expect(Array.isArray(result.strengthAnalysis.suggestions)).toBe(true);
    });

    it('should provide improvement suggestions for weak passwords', async () => {
      // Para este test, vamos a mockear temporalmente la validación para permitir una contraseña más débil
      const changeDto: ChangePasswordDto = {
        currentPassword: 'StrongPass123!',
        newPassword: 'BasicPass1!',
        confirmPassword: 'BasicPass1!'
      };

      const result = await userService.changePassword(testUser.id, changeDto, 1);

      expect(result.strengthAnalysis).toBeDefined();
      expect(result.strengthAnalysis.score).toBeLessThan(80);
      expect(result.strengthAnalysis.suggestions.length).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle incorrect current password', async () => {
      const changeDto: ChangePasswordDto = {
        currentPassword: 'WrongPassword!',
        newPassword: 'NewSecurePass456!',
        confirmPassword: 'NewSecurePass456!'
      };

      await expect(userService.changePassword(testUser.id, changeDto, 1))
        .rejects
        .toThrow('La contraseña actual es incorrecta');
    });

    it('should handle password confirmation mismatch', async () => {
      const changeDto: ChangePasswordDto = {
        currentPassword: 'StrongPass123!',
        newPassword: 'NewSecurePass456!',
        confirmPassword: 'DifferentPassword!'
      };

      await expect(userService.changePassword(testUser.id, changeDto, 1))
        .rejects
        .toThrow('Las contraseñas nuevas no coinciden');
    });

    it('should handle same password as current', async () => {
      const changeDto: ChangePasswordDto = {
        currentPassword: 'StrongPass123!',
        newPassword: 'StrongPass123!',
        confirmPassword: 'StrongPass123!'
      };

      await expect(userService.changePassword(testUser.id, changeDto, 1))
        .rejects
        .toThrow('La nueva contraseña debe ser diferente de la actual');
    });
  });
});

describe('PasswordPolicyConstraint', () => {
  let validator: PasswordPolicyConstraint;
  let mockArgs: ValidationArguments;

  beforeEach(() => {
    validator = new PasswordPolicyConstraint();
    mockArgs = {
      value: '',
      constraints: [],
      targetName: 'User',
      property: 'password',
      object: {}
    };
  });

  describe('validate', () => {
    it('should reject passwords shorter than 8 characters', async () => {
      const shortPasswords = ['1234567', 'Abc123!', 'Short1!'];
      
      for (const password of shortPasswords) {
        mockArgs.value = password;
        const result = validator.validate(password, mockArgs);
        expect(result).toBe(false);
      }
    });

    it('should reject passwords without uppercase letters', async () => {
      const passwords = ['password123!', 'mypassword1!', 'test123!@#'];
      
      for (const password of passwords) {
        mockArgs.value = password;
        const result = validator.validate(password, mockArgs);
        expect(result).toBe(false);
      }
    });

    it('should reject passwords without lowercase letters', async () => {
      const passwords = ['PASSWORD123!', 'MYPASSWORD1!', 'TEST123!@#'];
      
      for (const password of passwords) {
        mockArgs.value = password;
        const result = validator.validate(password, mockArgs);
        expect(result).toBe(false);
      }
    });

    it('should reject passwords without numbers', async () => {
      const passwords = ['MyPassword!', 'TestPass!@#', 'SecurePass!!'];
      
      for (const password of passwords) {
        mockArgs.value = password;
        const result = validator.validate(password, mockArgs);
        expect(result).toBe(false);
      }
    });

    it('should reject passwords without special characters', async () => {
      const passwords = ['MyPassword123', 'TestPass123', 'SecurePass123'];
      
      for (const password of passwords) {
        mockArgs.value = password;
        const result = validator.validate(password, mockArgs);
        expect(result).toBe(false);
      }
    });

    it('should accept valid passwords', async () => {
      const validPasswords = [
        'MySecurePass123!',
        'AnotherValid1@',
        'StrongPassword9#',
        'ComplexPass1$',
        'ValidTest123!'
      ];
      
      for (const password of validPasswords) {
        mockArgs.value = password;
        const result = validator.validate(password, mockArgs);
        expect(result).toBe(true);
      }
    });

    it('should accept passwords with edge case special characters', async () => {
      const validPasswords = [
        'TestPass123%',
        'MyPassword1^',
        'SecurePass2&',
        'StrongTest3*',
        'ValidPass4+'
      ];
      
      for (const password of validPasswords) {
        mockArgs.value = password;
        const result = validator.validate(password, mockArgs);
        expect(result).toBe(true);
      }
    });
  });

  describe('defaultMessage', () => {
    it('should return appropriate error message', () => {
      const message = validator.defaultMessage(mockArgs);
      expect(message).toContain('La contraseña debe tener');
      expect(message).toContain('8 caracteres');
      expect(message).toContain('mayúscula');
      expect(message).toContain('minúscula');
      expect(message).toContain('número');
      expect(message).toContain('carácter especial');
    });
  });
}); 