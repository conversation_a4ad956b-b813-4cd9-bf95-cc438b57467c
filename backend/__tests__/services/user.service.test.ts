import { DataSource } from 'typeorm';
import { User } from '../../src/entities/user.entity';
import { UserService } from '../../src/services/user.service';
import { CreateUserDto, UpdateUserDto, ChangePasswordDto } from '../../src/dtos/user.dto';
import { BadRequestException, NotFoundException } from '../../src/exceptions/http.exception';
import { hash, compare } from 'bcryptjs';

describe('UserService', () => {
  let dataSource: DataSource;
  let userService: UserService;
  let userRepository: any;

  beforeAll(async () => {
    // Mock DataSource
    userRepository = {
      findOne: jest.fn(),
      findAndCount: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
      delete: jest.fn(),
      update: jest.fn(),
    };

    dataSource = {
      getRepository: jest.fn().mockReturnValue(userRepository),
    } as any;

    userService = new UserService(dataSource);
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createUser', () => {
    const createUserDto: CreateUserDto = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'SecurePass123!',
      phone: '33 1234 5678',
      area: 'Sistemas',
    };

    it('should create user with valid data', async () => {
      userRepository.findOne.mockResolvedValue(null); // Usuario no existe
      userRepository.create.mockReturnValue({ id: 1, ...createUserDto });
      userRepository.save.mockResolvedValue({ id: 1, ...createUserDto });

      const result = await userService.createUser(createUserDto, 1);

      expect(result.email).toBe(createUserDto.email);
      expect(result.name).toBe(createUserDto.name);
      expect(result.password).toBeUndefined(); // No debe devolver password
      expect(userRepository.save).toHaveBeenCalled();
    });

    it('should throw error if email already exists', async () => {
      userRepository.findOne.mockResolvedValue({ id: 2, email: createUserDto.email });

      await expect(userService.createUser(createUserDto, 1))
        .rejects.toThrow('El correo electrónico ya está en uso');
    });

    it('should set audit fields correctly', async () => {
      userRepository.findOne.mockResolvedValue(null);
      userRepository.create.mockReturnValue({ id: 1, ...createUserDto });
      userRepository.save.mockResolvedValue({ id: 1, ...createUserDto });

      await userService.createUser(createUserDto, 123);

      expect(userRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          created_by: 123,
          updated_by: 123,
          status: 'activo',
        })
      );
    });
  });

  describe('updateUser', () => {
    const updateUserDto: UpdateUserDto = {
      name: 'Updated Name',
      phone: '33 9876 5432',
      area: 'Ventas',
    };

    it('should update user data successfully', async () => {
      const existingUser = {
        id: 1,
        name: 'Old Name',
        email: '<EMAIL>',
        phone: '33 1234 5678',
        area: 'Sistemas',
        status: 'activo',
      };

      userRepository.findOne.mockResolvedValue(existingUser);
      userRepository.save.mockResolvedValue({ ...existingUser, ...updateUserDto });

      const result = await userService.updateUser(1, updateUserDto, 123);

      expect(result.name).toBe(updateUserDto.name);
      expect(result.phone).toBe('+523398765432'); // Formato normalizado por MexicanPhoneConstraint
      expect(result.area).toBe(updateUserDto.area);
      expect(userRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          updated_by: 123,
        })
      );
    });

    it('should not update user with duplicate email', async () => {
      const existingUser = { id: 1, email: '<EMAIL>' };
      const duplicateUser = { id: 2, email: '<EMAIL>' };

      userRepository.findOne
        .mockResolvedValueOnce(existingUser) // Primera llamada para buscar el usuario a actualizar
        .mockResolvedValueOnce(duplicateUser); // Segunda llamada para verificar email duplicado

      await expect(userService.updateUser(1, { email: '<EMAIL>' }, 123))
        .rejects.toThrow('El correo electrónico ya está en uso');
    });

    it('should throw error if user not found', async () => {
      userRepository.findOne.mockResolvedValue(null);

      await expect(userService.updateUser(999, updateUserDto, 123))
        .rejects.toThrow('Usuario no encontrado');
    });
  });

  describe('changePassword', () => {
    const passwordDto: ChangePasswordDto = {
      currentPassword: 'OldPass123!',
      newPassword: 'NewSecurePass456!',
      confirmPassword: 'NewSecurePass456!',
    };

    it('should change password successfully', async () => {
      const user = {
        id: 1,
        password: await hash('OldPass123!', 10),
      };

      userRepository.findOne.mockResolvedValue(user);
      userRepository.save.mockResolvedValue(user);

      await userService.changePassword(1, passwordDto, 123);

      expect(userRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          updated_by: 123,
        })
      );
    });

    it('should throw error for incorrect current password', async () => {
      const user = {
        id: 1,
        password: await hash('DifferentPassword', 10),
      };

      userRepository.findOne.mockResolvedValue(user);

      await expect(userService.changePassword(1, passwordDto, 123))
        .rejects.toThrow('La contraseña actual es incorrecta');
    });

    it('should throw error when passwords do not match', async () => {
      const user = {
        id: 1,
        password: await hash('OldPass123!', 10),
      };

      userRepository.findOne.mockResolvedValue(user);

      const mismatchDto = {
        ...passwordDto,
        confirmPassword: 'DifferentPassword',
      };

      await expect(userService.changePassword(1, mismatchDto, 123))
        .rejects.toThrow('Las contraseñas nuevas no coinciden');
    });

    it('should throw error when new password is same as current', async () => {
      const user = {
        id: 1,
        password: await hash('OldPass123!', 10),
      };

      userRepository.findOne.mockResolvedValue(user);

      const samePasswordDto = {
        currentPassword: 'OldPass123!',
        newPassword: 'OldPass123!',
        confirmPassword: 'OldPass123!',
      };

      await expect(userService.changePassword(1, samePasswordDto, 123))
        .rejects.toThrow('La nueva contraseña debe ser diferente de la actual');
    });
  });

  describe('blockUnblockUser', () => {
    it('should block user successfully', async () => {
      const user = {
        id: 1,
        name: 'Test User',
        email: '<EMAIL>',
        status: 'activo',
      };

      userRepository.findOne.mockResolvedValue(user);
      userRepository.save.mockResolvedValue({ ...user, status: 'bloqueado' });

      const result = await userService.blockUnblockUser(1, true, 123);

      expect(result.status).toBe('bloqueado');
      expect(userRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'bloqueado',
          updated_by: 123,
        })
      );
    });

    it('should unblock user successfully', async () => {
      const user = {
        id: 1,
        name: 'Test User',
        email: '<EMAIL>',
        status: 'bloqueado',
      };

      userRepository.findOne.mockResolvedValue(user);
      userRepository.save.mockResolvedValue({ ...user, status: 'activo' });

      const result = await userService.blockUnblockUser(1, false, 123);

      expect(result.status).toBe('activo');
    });

    it('should prevent self-blocking', async () => {
      const user = { id: 1, status: 'activo' };
      userRepository.findOne.mockResolvedValue(user);

      await expect(userService.blockUnblockUser(1, true, 1))
        .rejects.toThrow('No puedes bloquear tu propia cuenta');
    });

    it('should throw error if user not found', async () => {
      userRepository.findOne.mockResolvedValue(null);

      await expect(userService.blockUnblockUser(999, true, 123))
        .rejects.toThrow('Usuario no encontrado');
    });
  });

  describe('isUserActive', () => {
    it('should return true for active user', async () => {
      const user = { status: 'activo', active: true };
      userRepository.findOne.mockResolvedValue(user);

      const result = await userService.isUserActive(1);

      expect(result).toBe(true);
    });

    it('should return false for blocked user', async () => {
      const user = { status: 'bloqueado', active: true };
      userRepository.findOne.mockResolvedValue(user);

      const result = await userService.isUserActive(1);

      expect(result).toBe(false);
    });

    it('should return false for inactive user', async () => {
      const user = { status: 'activo', active: false };
      userRepository.findOne.mockResolvedValue(user);

      const result = await userService.isUserActive(1);

      expect(result).toBe(false);
    });

    it('should return false for non-existent user', async () => {
      userRepository.findOne.mockResolvedValue(null);

      const result = await userService.isUserActive(999);

      expect(result).toBe(false);
    });
  });

  describe('deleteUser', () => {
    it('should delete user successfully', async () => {
      const currentUser = { id: 123 };
      userRepository.delete.mockResolvedValue({ affected: 1 });

      await userService.deleteUser(1, currentUser as User);

      expect(userRepository.delete).toHaveBeenCalledWith(1);
    });

    it('should prevent self-deletion', async () => {
      const currentUser = { id: 1 };

      await expect(userService.deleteUser(1, currentUser as User))
        .rejects.toThrow('No puedes eliminar tu propia cuenta');
    });

    it('should throw error if user not found', async () => {
      const currentUser = { id: 123 };
      userRepository.delete.mockResolvedValue({ affected: 0 });

      await expect(userService.deleteUser(999, currentUser as User))
        .rejects.toThrow('Usuario no encontrado');
    });
  });
}); 