import { Product } from '../../src/entities/product.entity';

describe('Product Indicators', () => {
  let product: Product;

  beforeEach(() => {
    product = new Product();
    product.id = 1;
    product.codigoItem = 'TEST-001';
    product.nombre = 'Producto de Prueba';
    product.tipoAlmacen = 'GENERAL';
    product.estado = 'DISPONIBLE';
    product.stockMinimo = 5;
  });

  describe('getStockReal', () => {
    it('should calculate real stock correctly (disponible - comprometido)', () => {
      product.stockDisponible = 20;
      product.stockComprometido = 5;
      
      expect(product.getStockReal()).toBe(15);
    });

    it('should never return negative stock', () => {
      product.stockDisponible = 10;
      product.stockComprometido = 15;
      
      expect(product.getStockReal()).toBe(0);
    });

    it('should return full stock when no stock is committed', () => {
      product.stockDisponible = 25;
      product.stockComprometido = 0;
      
      expect(product.getStockReal()).toBe(25);
    });
  });

  describe('getNivelStock', () => {
    it('should return CRITICO when stock is zero', () => {
      product.stockDisponible = 0;
      product.stockComprometido = 0;
      
      expect(product.getNivelStock()).toBe('CRITICO');
    });

    it('should return CRITICO when all stock is committed', () => {
      product.stockDisponible = 10;
      product.stockComprometido = 10;
      
      expect(product.getNivelStock()).toBe('CRITICO');
    });

    it('should return BAJO when stock is at minimum threshold', () => {
      product.stockDisponible = 5; // Equal to stockMinimo
      product.stockComprometido = 0;
      product.stockMinimo = 5;
      
      expect(product.getNivelStock()).toBe('BAJO');
    });

    it('should return BAJO when stock is below minimum threshold', () => {
      product.stockDisponible = 3;
      product.stockComprometido = 0;
      product.stockMinimo = 5;
      
      expect(product.getNivelStock()).toBe('BAJO');
    });

    it('should return NORMAL when stock is between minimum and 10', () => {
      product.stockDisponible = 8;
      product.stockComprometido = 0;
      product.stockMinimo = 5;
      
      expect(product.getNivelStock()).toBe('NORMAL');
    });

    it('should return ALTO when stock is above 10', () => {
      product.stockDisponible = 25;
      product.stockComprometido = 0;
      
      expect(product.getNivelStock()).toBe('ALTO');
    });
  });

  describe('getColorIndicador', () => {
    it('should return red when stock is zero', () => {
      product.stockDisponible = 0;
      product.stockComprometido = 0;
      
      expect(product.getColorIndicador()).toBe('red');
    });

    it('should return red when all stock is committed (stockReal = 0)', () => {
      product.stockDisponible = 5;
      product.stockComprometido = 5;
      
      expect(product.getColorIndicador()).toBe('red');
    });

    it('should return yellow when stock is low (1-10 units)', () => {
      product.stockDisponible = 8;
      product.stockComprometido = 0;
      
      expect(product.getColorIndicador()).toBe('yellow');
    });

    it('should return yellow when stock becomes low after commitments', () => {
      product.stockDisponible = 15;
      product.stockComprometido = 7; // stockReal = 8
      
      expect(product.getColorIndicador()).toBe('yellow');
    });

    it('should return blue when stock is high but has commitments', () => {
      product.stockDisponible = 20;
      product.stockComprometido = 5; // stockReal = 15
      
      expect(product.getColorIndicador()).toBe('blue');
    });

    it('should return green when stock is high and no commitments', () => {
      product.stockDisponible = 25;
      product.stockComprometido = 0;
      
      expect(product.getColorIndicador()).toBe('green');
    });

    it('should prioritize red over blue when stockReal is 0 despite commitments', () => {
      product.stockDisponible = 5;
      product.stockComprometido = 5;
      
      expect(product.getColorIndicador()).toBe('red');
    });

    it('should prioritize yellow over blue when stock is low despite commitments', () => {
      product.stockDisponible = 12;
      product.stockComprometido = 5; // stockReal = 7
      
      expect(product.getColorIndicador()).toBe('yellow');
    });
  });

  describe('isDisponibleParaVenta', () => {
    it('should return true when product is available and has stock', () => {
      product.estado = 'DISPONIBLE';
      product.stockDisponible = 10;
      product.stockComprometido = 0;
      
      expect(product.isDisponibleParaVenta()).toBe(true);
    });

    it('should return false when product is discontinued', () => {
      product.estado = 'DESCONTINUADO';
      product.stockDisponible = 10;
      product.stockComprometido = 0;
      
      expect(product.isDisponibleParaVenta()).toBe(false);
    });

    it('should return false when product is out of stock', () => {
      product.estado = 'AGOTADO';
      product.stockDisponible = 0;
      product.stockComprometido = 0;
      
      expect(product.isDisponibleParaVenta()).toBe(false);
    });

    it('should return false when all stock is committed', () => {
      product.estado = 'DISPONIBLE';
      product.stockDisponible = 10;
      product.stockComprometido = 10;
      
      expect(product.isDisponibleParaVenta()).toBe(false);
    });
  });

  describe('Stock management methods', () => {
    beforeEach(() => {
      product.stockDisponible = 20;
      product.stockComprometido = 5;
    });

    describe('reservarStock', () => {
      it('should reserve stock when sufficient available', () => {
        const result = product.reservarStock(10);
        
        expect(result).toBe(true);
        expect(product.stockComprometido).toBe(15);
      });

      it('should not reserve more stock than available', () => {
        const result = product.reservarStock(20); // More than stockReal (15)
        
        expect(result).toBe(false);
        expect(product.stockComprometido).toBe(5); // Unchanged
      });

      it('should reserve exactly the available stock', () => {
        const result = product.reservarStock(15); // Exactly stockReal
        
        expect(result).toBe(true);
        expect(product.stockComprometido).toBe(20);
      });
    });

    describe('liberarStock', () => {
      it('should release committed stock', () => {
        const result = product.liberarStock(3);
        
        expect(result).toBe(true);
        expect(product.stockComprometido).toBe(2);
      });

      it('should not release more than committed stock', () => {
        const result = product.liberarStock(10); // More than committed (5)
        
        expect(result).toBe(false);
        expect(product.stockComprometido).toBe(5); // Unchanged
      });

      it('should release all committed stock', () => {
        const result = product.liberarStock(5); // All committed stock
        
        expect(result).toBe(true);
        expect(product.stockComprometido).toBe(0);
      });
    });
  });

  describe('Edge cases and business logic', () => {
    it('should handle zero stock scenario correctly', () => {
      product.stockDisponible = 0;
      product.stockComprometido = 0;
      
      expect(product.getStockReal()).toBe(0);
      expect(product.getNivelStock()).toBe('CRITICO');
      expect(product.getColorIndicador()).toBe('red');
      expect(product.isDisponibleParaVenta()).toBe(false);
    });

    it('should handle high stock scenario correctly', () => {
      product.stockDisponible = 50;
      product.stockComprometido = 0;
      
      expect(product.getStockReal()).toBe(50);
      expect(product.getNivelStock()).toBe('ALTO');
      expect(product.getColorIndicador()).toBe('green');
      expect(product.isDisponibleParaVenta()).toBe(true);
    });

    it('should handle boundary conditions (exactly 10 units)', () => {
      product.stockDisponible = 10;
      product.stockComprometido = 0;
      
      expect(product.getStockReal()).toBe(10);
      expect(product.getNivelStock()).toBe('NORMAL');
      expect(product.getColorIndicador()).toBe('yellow');
      expect(product.isDisponibleParaVenta()).toBe(true);
    });
  });
}); 