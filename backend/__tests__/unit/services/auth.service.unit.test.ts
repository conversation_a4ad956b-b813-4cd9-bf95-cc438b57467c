/**
 * Tests unitarios para AuthService
 * Verifican la lógica de autenticación sin conexión a BD
 */

// IMPORTANTE: Los mocks deben estar antes de las importaciones
jest.mock('jsonwebtoken', () => ({
  sign: jest.fn(),
  verify: jest.fn(),
  decode: jest.fn(),
}));

jest.mock('bcryptjs', () => ({
  hash: jest.fn(),
  compare: jest.fn(),
  genSalt: jest.fn(),
}));

import { AuthService } from '../../../src/services/auth.service';
import { mockRequest, mockResponse, createMockRepository } from '../../setup/unit-setup';
import { User } from '../../../src/entities/user.entity';
import { TokenPayload } from '../../../src/interfaces/request.interface';

// Mock del repository
const mockUserRepository = createMockRepository<User>(User);

// Mock del DataSource - CRÍTICO: getRepository debe retornar el mock
const mockDataSource = {
  getRepository: jest.fn().mockReturnValue(mockUserRepository),
  initialize: jest.fn(),
  destroy: jest.fn(),
  isInitialized: true,
};

describe('AuthService - Unit Tests', () => {
  let authService: AuthService;
  const mockJwt = require('jsonwebtoken');
  const mockBcrypt = require('bcryptjs');

  beforeEach(() => {
    // Resetear todos los mocks
    jest.clearAllMocks();
    
    // Configurar mocks específicos ANTES de crear AuthService
    mockJwt.sign.mockReturnValue('mock_jwt_token');
    mockJwt.verify.mockReturnValue({
      sub: 1,
      email: '<EMAIL>',
      type: 'access',
    });
    mockBcrypt.hash.mockResolvedValue('mock_hashed_password');
    mockBcrypt.compare.mockResolvedValue(true);
    
    // Asegurar que getRepository retorna el mockUserRepository
    mockDataSource.getRepository.mockReturnValue(mockUserRepository);
    
    // Crear nueva instancia para cada test
    authService = new AuthService(mockDataSource as any);
    
    // Verificar que se creó correctamente
    console.log('🔍 AuthService userRepository after creation:', (authService as any).userRepository);
    console.log('🔍 Repository has query method:', !!(authService as any).userRepository?.query);
  });

  describe('generateToken', () => {
    it('should generate JWT token for valid payload', () => {
      // Arrange
      const payload: TokenPayload = {
        sub: 1,
        email: '<EMAIL>',
        type: 'access',
      };

      // Act
      const token = authService.generateToken(payload);

      // Assert
      expect(token).toBe('mock_jwt_token');
      expect(mockJwt.sign).toHaveBeenCalledWith(
        payload,
        expect.any(String),
        expect.objectContaining({
          expiresIn: expect.any(String),
        })
      );
    });

    it('should generate token with custom options', () => {
      // Arrange
      const payload: TokenPayload = {
        sub: 1,
        email: '<EMAIL>',
        type: 'refresh',
      };
      const options = { expiresIn: '7d' };

      // Act
      const token = authService.generateToken(payload, options);

      // Assert
      expect(token).toBe('mock_jwt_token');
      expect(mockJwt.sign).toHaveBeenCalledWith(
        payload,
        expect.any(String),
        expect.objectContaining({
          expiresIn: '7d',
        })
      );
    });
  });

  describe('verifyToken', () => {
    it('should successfully verify valid JWT token', () => {
      // Arrange
      const token = 'valid_jwt_token';
      const mockPayload: TokenPayload = {
        sub: 1,
        email: '<EMAIL>',
        type: 'access',
      };

      // Mock jwt.verify to return valid payload
      mockJwt.verify.mockReturnValue(mockPayload);

      // Act
      const result = authService.verifyToken(token);

      // Assert
      expect(result).toEqual(mockPayload);
      expect(mockJwt.verify).toHaveBeenCalledWith(
        token,
        expect.any(String)
      );
    });

    it('should return null for invalid token', () => {
      // Arrange
      const invalidToken = 'invalid_token';

      // Mock jwt.verify to throw error
      mockJwt.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      // Act
      const result = authService.verifyToken(invalidToken);

      // Assert
      expect(result).toBeNull();
    });

    it('should return null for token with missing required fields', () => {
      // Arrange
      const token = 'token_with_missing_fields';
      const incompletePayload = {
        sub: 1,
        // Missing email and type
      };

      mockJwt.verify.mockReturnValue(incompletePayload);

      // Act
      const result = authService.verifyToken(token);

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('hashPassword', () => {
    it('should hash password correctly', async () => {
      // Arrange
      const plainPassword = 'password123';

      // Act
      const hashedPassword = await authService.hashPassword(plainPassword);

      // Assert
      expect(hashedPassword).toBe('mock_hashed_password');
      expect(mockBcrypt.hash).toHaveBeenCalledWith(plainPassword, 10);
    });
  });

  describe('comparePasswords', () => {
    it('should return true for matching passwords', async () => {
      // Arrange
      const plainPassword = 'password123';
      const hashedPassword = 'hashed_password';

      // Act
      const isMatch = await authService.comparePasswords(plainPassword, hashedPassword);

      // Assert
      expect(isMatch).toBe(true);
      expect(mockBcrypt.compare).toHaveBeenCalledWith(plainPassword, hashedPassword);
    });

    it('should return false for non-matching passwords', async () => {
      // Arrange
      const plainPassword = 'password123';
      const hashedPassword = 'different_hash';

      // Mock bcrypt.compare to return false
      mockBcrypt.compare.mockResolvedValue(false);

      // Act
      const isMatch = await authService.comparePasswords(plainPassword, hashedPassword);

      // Assert
      expect(isMatch).toBe(false);
    });
  });

  describe('generateTokens', () => {
    it('should generate both access and refresh tokens', () => {
      // Arrange
      const user = {
        id: 1,
        email: '<EMAIL>',
        name: 'Test User',
      };

      // Act
      const tokens = authService.generateTokens(user as any);

      // Assert
      expect(tokens).toHaveProperty('accessToken');
      expect(tokens).toHaveProperty('refreshToken');
      expect(tokens.accessToken).toBe('mock_jwt_token');
      expect(tokens.refreshToken).toBe('mock_jwt_token');
    });

    it('should throw error if user has no email', () => {
      // Arrange
      const userWithoutEmail = {
        id: 1,
        email: '',
      } as User;

      // Act & Assert
      expect(() => authService.generateTokens(userWithoutEmail)).toThrow(
        'El usuario no tiene un email para generar el token.'
      );
    });
  });

  describe('validateUser', () => {
    it('should validate user with correct credentials', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';

      const mockUser = {
        id: 1,
        email,
        password: 'hashed_password',
        status: 'activo',
        active: true,
        roles: ['ROLE_ADMIN'],
      };

      // Mock repository queries
      mockUserRepository.query
        .mockResolvedValueOnce([mockUser]) // getUserByEmail
        .mockResolvedValueOnce([{ name: 'ROLE_ADMIN' }]) // getUserRoles
        .mockResolvedValueOnce([{ name: 'permission1' }]); // getUserPermissions

      // Act
      const result = await authService.validateUser(email, password);

      // Assert
      expect(result).toBeDefined();
      expect(result!.id).toBe(1);
      expect(result!.email).toBe(email);
      expect(result!.roles).toHaveLength(1);
      expect(mockUserRepository.query).toHaveBeenCalledTimes(3);
    });

    it('should return null for invalid credentials', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'wrong_password';

      mockBcrypt.compare.mockResolvedValue(false);
      mockUserRepository.query.mockResolvedValue([{
        id: 1,
        email,
        password: 'hashed_password',
        status: 'activo',
        active: true,
      }]);

      // Act
      const result = await authService.validateUser(email, password);

      // Assert
      expect(result).toBeNull();
    });

    it('should return null for non-existent user', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';

      mockUserRepository.query.mockResolvedValue([]);

      // Act
      const result = await authService.validateUser(email, password);

      // Assert
      expect(result).toBeNull();
    });

    it('should return null for inactive user', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';

      const mockUser = {
        id: 1,
        email,
        password: 'hashed_password',
        status: 'inactivo',
        active: false,
      };

      mockUserRepository.query.mockResolvedValue([mockUser]);

      // Act
      const result = await authService.validateUser(email, password);

      // Assert
      expect(result).toBeNull();
    });
  });
});

/**
 * NOTA: Estos tests cubren los métodos principales del AuthService.
 * 
 * Los tests unitarios:
 * 1. Prueban SOLO la lógica del servicio
 * 2. Usan mocks para todas las dependencias externas (BD, JWT, bcrypt)
 * 3. No requieren conexión a base de datos
 * 4. Son rápidos y determinísticos
 * 5. Cubren casos exitosos, errores y edge cases
 */ 