import { NotificationService, TriggerNotificationInput } from '../../../src/services/notification.service';
import { AppDataSource } from '../../../src/ormconfig';
import { Notification, NotificationType, NotificationPriority, NotificationStatus } from '../../../src/entities/notification.entity';
import { User } from '../../../src/entities/user.entity';

// Mock global WebSocketService
const mockAlertNewNotificationToUser = jest.fn();
(global as any).getWebSocketService = () => ({
  alertNewNotificationToUser: mockAlertNewNotificationToUser,
});

// Mock AppDataSource
jest.mock('../../../src/ormconfig', () => {
  const actual = jest.requireActual('../../../src/ormconfig');
  return {
    ...actual,
    isInitialized: true,
    getRepository: jest.fn(),
  };
});

const mockSave = jest.fn();
const mockCreate = jest.fn();
const mockFindOne = jest.fn();
const mockFind = jest.fn();
const mockCreateQueryBuilder = jest.fn();

beforeEach(() => {
  jest.clearAllMocks();
  (AppDataSource.getRepository as jest.Mock).mockReturnValue({
    create: mockCreate,
    save: mockSave,
    findOne: mockFindOne,
    find: mockFind,
    createQueryBuilder: mockCreateQueryBuilder,
  });
});

describe('NotificationService - triggerNotification', () => {
  it('debe crear una notificación para un usuario individual', async () => {
    const service = new NotificationService();
    const input: TriggerNotificationInput = {
      title: 'Test',
      message: 'Mensaje',
      userId: 1,
    };
    mockCreate.mockReturnValue({ ...input, id: 123 });
    mockSave.mockResolvedValue({ ...input, id: 123 });

    const result = await service.triggerNotification(input);
    expect(mockCreate).toHaveBeenCalled();
    expect(mockSave).toHaveBeenCalled();
    expect(result).toHaveProperty('id', 123);
    expect(mockAlertNewNotificationToUser).toHaveBeenCalledWith(1, expect.objectContaining({ title: 'Test' }));
  });

  it('debe lanzar error si faltan datos obligatorios', async () => {
    const service = new NotificationService();
    await expect(service.triggerNotification({ message: 'Falta título', userId: 1 } as any)).rejects.toThrow();
    await expect(service.triggerNotification({ title: 'Falta mensaje', userId: 1 } as any)).rejects.toThrow();
    await expect(service.triggerNotification({ title: 'Test', message: 'Sin user ni role' } as any)).rejects.toThrow();
  });

  it('debe crear notificaciones para múltiples usuarios de un rol', async () => {
    const service = new NotificationService();
    // Simular usuarios activos con ese rol
    const fakeUsers = [
      { id: 10, active: true },
      { id: 20, active: true },
    ];
    mockCreateQueryBuilder.mockReturnValue({
      innerJoin: () => ({
        where: () => ({
          andWhere: () => ({
            getMany: async () => fakeUsers,
          }),
        }),
      }),
    });
    mockCreate.mockImplementation((data: any) => ({ ...data, id: Math.floor(Math.random() * 1000) }));
    mockSave.mockImplementation(async (data: any) => data);

    const input: TriggerNotificationInput = {
      title: 'Para rol',
      message: 'Mensaje',
      roleId: 5,
    };
    const result = await service.triggerNotification(input);
    expect(Array.isArray(result)).toBe(true);
    expect(result.length).toBe(2);
    expect(mockAlertNewNotificationToUser).toHaveBeenCalledTimes(2);
  });
}); 