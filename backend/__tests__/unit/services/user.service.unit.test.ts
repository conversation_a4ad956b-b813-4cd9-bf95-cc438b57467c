/**
 * Tests unitarios para UserService
 * Verifican la lógica de gestión de usuarios sin conexión a BD
 */

// CRÍTICO: Mock de PasswordHistoryService ANTES de importar UserService
const mockPasswordHistoryInstance = {
  addToHistory: jest.fn().mockResolvedValue(undefined),
  isPasswordReused: jest.fn().mockResolvedValue(false),
};

// Mock class - constructor siempre devuelve la misma instancia
const MockPasswordHistoryService = jest.fn().mockImplementation(() => {
  return mockPasswordHistoryInstance;
});

jest.mock('../../../src/services/password-history.service', () => ({
  PasswordHistoryService: MockPasswordHistoryService,
}));

// Mock de AuthService
jest.mock('../../../src/services/auth.service', () => ({
  authService: {
    hashPassword: jest.fn().mockResolvedValue('hashed_password'),
  },
}));

// Mock de bcryptjs - IMPORTANTE: configurar diferentes retornos por caso
jest.mock('bcryptjs', () => ({
  compare: jest.fn(),
}));

// Mock de validators
jest.mock('../../../src/validators/mexican-phone.validator', () => ({
  MexicanPhoneConstraint: {
    normalizePhoneForStorage: jest.fn().mockImplementation((phone) => phone),
  },
}));

jest.mock('../../../src/validators/password-policy.validator', () => ({
  PasswordStrengthChecker: {
    calculateStrength: jest.fn().mockReturnValue({
      score: 85,
      level: 'Fuerte',
      suggestions: [],
    }),
  },
}));

// AHORA importar UserService después de los mocks
import { UserService } from '../../../src/services/user.service';
import { createMockRepository } from '../../setup/unit-setup';
import { User } from '../../../src/entities/user.entity';
import { CreateUserDto, UpdateUserDto, ChangePasswordDto } from '../../../src/dtos/user.dto';
import { BadRequestException, NotFoundException } from '../../../src/exceptions/http.exception';

// Mock del repository
const mockUserRepository = createMockRepository<User>(User);

// Mock del DataSource
const mockDataSource = {
  getRepository: jest.fn().mockReturnValue(mockUserRepository),
  initialize: jest.fn(),
  destroy: jest.fn(),
  isInitialized: true,
};

describe('UserService - Unit Tests', () => {
  let userService: UserService;

  beforeEach(() => {
    // Configurar mocks antes de crear UserService
    mockDataSource.getRepository.mockReturnValue(mockUserRepository);
    
    // Limpiar mocks del PasswordHistoryService
    mockPasswordHistoryInstance.addToHistory.mockClear();
    mockPasswordHistoryInstance.isPasswordReused.mockClear();
    
    // Limpiar mock del constructor
    MockPasswordHistoryService.mockClear();
    
    // Crear nueva instancia para cada test
    userService = new UserService(mockDataSource as any);
    
    // RESETEAR mock de bcryptjs para cada test
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createUser', () => {
    it('should create a new user successfully', async () => {
      // Arrange
      const userData: CreateUserDto = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        phone: '5551234567',
        area: 'IT',
      };
      const creatorUserId = 1;

      mockUserRepository.findOne.mockResolvedValue(null); // No existing user
      mockUserRepository.create.mockReturnValue({
        id: 1,
        ...userData,
        password: 'hashed_password',
        status: 'activo',
        created_by: creatorUserId,
        updated_by: creatorUserId,
      } as User);
      mockUserRepository.save.mockResolvedValue({
        id: 1,
        ...userData,
        password: 'hashed_password',
        status: 'activo',
        created_by: creatorUserId,
        updated_by: creatorUserId,
      } as User);

      // Act
      const result = await userService.createUser(userData, creatorUserId);

      // Assert
      expect(result).toBeDefined();
      expect(result.email).toBe(userData.email);
      expect(result.name).toBe(userData.name);
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { email: userData.email },
      });
      expect(mockUserRepository.save).toHaveBeenCalled();
      expect(mockPasswordHistoryInstance.addToHistory).toHaveBeenCalledWith(
        1, // savedUser.id
        'hashed_password', // hashedPassword
        creatorUserId
      );
    });

    it('should throw error if email already exists', async () => {
      // Arrange
      const userData: CreateUserDto = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
      };

      mockUserRepository.findOne.mockResolvedValue({ id: 1, email: userData.email } as User);

      // Act & Assert
      await expect(userService.createUser(userData)).rejects.toThrow(BadRequestException);
      expect(mockUserRepository.save).not.toHaveBeenCalled();
    });
  });

  describe('getUserById', () => {
    it('should return user by id', async () => {
      // Arrange
      const userId = 1;
      const mockUser = { id: userId, name: 'Test User', email: '<EMAIL>' } as User;
      mockUserRepository.findOne.mockResolvedValue(mockUser);

      // Act
      const result = await userService.getUserById(userId);

      // Assert
      expect(result).toEqual(mockUser);
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { id: userId },
        select: ['id', 'name', 'email', 'phone', 'area', 'status', 'active', 'created_at', 'updated_at', 'created_by', 'updated_by']
      });
    });

    it('should return null if user not found', async () => {
      // Arrange
      const userId = 999;
      mockUserRepository.findOne.mockResolvedValue(null);

      // Act
      const result = await userService.getUserById(userId);

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('updateUser', () => {
    it('should update user successfully', async () => {
      // Arrange
      const userId = 1;
      const updateData: UpdateUserDto = {
        name: 'Updated Name',
        email: '<EMAIL>',
      };
      const updaterUserId = 2;

      const existingUser = {
        id: userId,
        name: 'Old Name',
        email: '<EMAIL>',
        password: 'old_password',
      } as User;

      mockUserRepository.findOne.mockResolvedValue(existingUser);
      mockUserRepository.save.mockResolvedValue({
        ...existingUser,
        ...updateData,
      });

      // Act
      const result = await userService.updateUser(userId, updateData, updaterUserId);

      // Assert
      expect(result).toBeDefined();
      expect(result.name).toBe(updateData.name);
      expect(result.email).toBe(updateData.email);
      expect(mockUserRepository.save).toHaveBeenCalled();
    });

    it('should throw error if user not found', async () => {
      // Arrange
      const userId = 999;
      const updateData: UpdateUserDto = { name: 'New Name' };
      mockUserRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(userService.updateUser(userId, updateData)).rejects.toThrow(NotFoundException);
    });
  });

  describe('changePassword', () => {
    it('should change password successfully', async () => {
      // Arrange
      const userId = 1;
      const passwordData: ChangePasswordDto = {
        currentPassword: 'currentPassword',
        newPassword: 'newPassword123',
        confirmPassword: 'newPassword123',
      };

      const existingUser = {
        id: userId,
        password: 'current_hashed_password',
      } as User;

      mockUserRepository.findOne.mockResolvedValue(existingUser);
      mockUserRepository.save.mockResolvedValue(existingUser);
      
      // CRÍTICO: Configurar bcryptjs.compare para retornar valores específicos
      const bcryptMock = require('bcryptjs');
      bcryptMock.compare
        .mockResolvedValueOnce(true)   // currentPassword vs user.password = TRUE (correcto)
        .mockResolvedValueOnce(false); // newPassword vs user.password = FALSE (son diferentes)

      // CRÍTICO: Configurar que no hay reutilización de contraseña
      mockPasswordHistoryInstance.isPasswordReused.mockResolvedValue(false);

      // Act
      const result = await userService.changePassword(userId, passwordData);

      // Assert
      expect(result.success).toBe(true);
      expect(result.strengthAnalysis).toBeDefined();
      expect(result.strengthAnalysis.score).toBe(85);
      expect(mockUserRepository.save).toHaveBeenCalled();
      expect(mockPasswordHistoryInstance.isPasswordReused).toHaveBeenCalledWith(
        userId,
        passwordData.newPassword,
        5
      );
      expect(mockPasswordHistoryInstance.addToHistory).toHaveBeenCalledWith(
        userId,
        'current_hashed_password', // Contraseña actual que va al historial
        undefined, // updaterUserId
        undefined, // ipAddress
        undefined  // userAgent
      );
    });

    it('should throw error if current password is incorrect', async () => {
      // Arrange
      const userId = 1;
      const passwordData: ChangePasswordDto = {
        currentPassword: 'wrongPassword',
        newPassword: 'newPassword123',
        confirmPassword: 'newPassword123',
      };

      const existingUser = {
        id: userId,
        password: 'current_hashed_password',
      } as User;

      mockUserRepository.findOne.mockResolvedValue(existingUser);
      // CRÍTICO: Para este test específico, configurar que retorne false para currentPassword
      const bcryptMock = require('bcryptjs');
      bcryptMock.compare.mockResolvedValueOnce(false);

      // Act & Assert
      await expect(userService.changePassword(userId, passwordData)).rejects.toThrow(BadRequestException);
    });

    it('should throw error if new passwords do not match', async () => {
      // Arrange
      const userId = 1;
      const passwordData: ChangePasswordDto = {
        currentPassword: 'currentPassword',
        newPassword: 'newPassword123',
        confirmPassword: 'differentPassword',
      };

      const existingUser = {
        id: userId,
        password: 'current_hashed_password',
      } as User;

      mockUserRepository.findOne.mockResolvedValue(existingUser);

      // Act & Assert
      await expect(userService.changePassword(userId, passwordData)).rejects.toThrow(BadRequestException);
    });
  });

  describe('blockUnblockUser', () => {
    it('should block user successfully', async () => {
      // Arrange
      const userId = 1;
      const updaterUserId = 2;
      const existingUser = { id: userId, status: 'activo' } as User;

      mockUserRepository.findOne.mockResolvedValue(existingUser);
      mockUserRepository.save.mockResolvedValue({ ...existingUser, status: 'bloqueado' });

      // Act
      const result = await userService.blockUnblockUser(userId, true, updaterUserId);

      // Assert
      expect(result).toBeDefined();
      expect(mockUserRepository.save).toHaveBeenCalled();
    });

    it('should prevent self-blocking', async () => {
      // Arrange
      const userId = 1;
      const updaterUserId = 1; // Same user
      const existingUser = { id: userId, status: 'activo' } as User;

      mockUserRepository.findOne.mockResolvedValue(existingUser);

      // Act & Assert
      await expect(userService.blockUnblockUser(userId, true, updaterUserId))
        .rejects.toThrow(BadRequestException);
    });
  });

  describe('deleteUser', () => {
    it('should delete user successfully', async () => {
      // Arrange
      const userId = 1;
      const currentUser = { id: 2 } as User; // Different user
      mockUserRepository.delete.mockResolvedValue({ affected: 1, raw: {} });

      // Act
      await userService.deleteUser(userId, currentUser);

      // Assert
      expect(mockUserRepository.delete).toHaveBeenCalledWith(userId);
    });

    it('should prevent self-deletion', async () => {
      // Arrange
      const userId = 1;
      const currentUser = { id: 1 } as User; // Same user

      // Act & Assert
      await expect(userService.deleteUser(userId, currentUser))
        .rejects.toThrow(BadRequestException);
    });

    it('should throw error if user not found', async () => {
      // Arrange
      const userId = 999;
      const currentUser = { id: 1 } as User;
      mockUserRepository.delete.mockResolvedValue({ affected: 0, raw: {} });

      // Act & Assert
      await expect(userService.deleteUser(userId, currentUser))
        .rejects.toThrow(NotFoundException);
    });
  });

  describe('isUserActive', () => {
    it('should return true for active user', async () => {
      // Arrange
      const userId = 1;
      const activeUser = { status: 'activo', active: true } as User;
      mockUserRepository.findOne.mockResolvedValue(activeUser);

      // Act
      const result = await userService.isUserActive(userId);

      // Assert
      expect(result).toBe(true);
    });

    it('should return false for inactive user', async () => {
      // Arrange
      const userId = 1;
      const inactiveUser = { status: 'bloqueado', active: true } as User;
      mockUserRepository.findOne.mockResolvedValue(inactiveUser);

      // Act
      const result = await userService.isUserActive(userId);

      // Assert
      expect(result).toBe(false);
    });

    it('should return false if user not found', async () => {
      // Arrange
      const userId = 999;
      mockUserRepository.findOne.mockResolvedValue(null);

      // Act
      const result = await userService.isUserActive(userId);

      // Assert
      expect(result).toBe(false);
    });
  });
});

/**
 * NOTA: Estos tests cubren los métodos principales del UserService:
 * 
 * ✅ createUser - Creación de usuarios con validaciones
 * ✅ getUserById - Obtención de usuario por ID
 * ✅ updateUser - Actualización de usuarios
 * ✅ changePassword - Cambio de contraseñas con validaciones avanzadas
 * ✅ blockUnblockUser - Bloqueo/desbloqueo de usuarios
 * ✅ deleteUser - Eliminación de usuarios con prevención de auto-eliminación
 * ✅ isUserActive - Verificación de estado activo
 * 
 * Los tests incluyen:
 * - Casos exitosos
 * - Validaciones de negocio (email duplicado, auto-bloqueo, auto-eliminación)
 * - Casos de error (usuario no encontrado, contraseñas incorrectas)
 * - Mocking de todas las dependencias externas
 */ 