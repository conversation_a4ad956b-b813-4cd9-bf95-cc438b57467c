/// <reference types="jest" />



import { testDataSource } from '../src/tests/test-setup';
import { User } from '../src/entities/user.entity';
import { Role } from '../src/entities/role.entity';
import { AuthService } from '../src/services/auth.service';




describe('Auth/Login', () => {
  let testUser: User;
  let testRole: Role;
  let testAuthService: AuthService;
  

  beforeAll(async () => {
    // Initialize test data source
    if (!testDataSource.isInitialized) {
      await testDataSource.initialize();
    }

    // Create test role
    const roleRepo = testDataSource.getRepository(Role);
    testRole = roleRepo.create({ name: 'ROLE_TEST' });
    await roleRepo.save(testRole);

    // Create test user
    const userRepo = testDataSource.getRepository(User);
    testUser = userRepo.create({
      name: 'Test User',
      email: '<EMAIL>',
      password: '$2a$10$dXJ3SW6G7P50lGmMkkmwe.20cQQubK3.HZWzG3YB1tlRy.fqvM/BG', // 'password'
      status: 'activo',
      roles: [testRole],
    });
    await userRepo.save(testUser);

    // Create test auth service and controller
    testAuthService = new AuthService(testDataSource);
  });

  afterAll(async () => {
    // Clean up test data
    if (testDataSource.isInitialized) {
      const userRepo = testDataSource.getRepository(User);
      const roleRepo = testDataSource.getRepository(Role);
      
      await userRepo.delete({ email: '<EMAIL>' });
      await roleRepo.delete({ name: 'ROLE_TEST' });
      
      await testDataSource.destroy();
    }
  });

  it('debe permitir login con username y password correctos', async () => {
    // Test the auth service directly
    const user = await testAuthService.validateUser('<EMAIL>', 'password');
    expect(user).toBeTruthy();
    expect(user?.email).toBe('<EMAIL>');
  });

  it('debe rechazar login con password incorrecto', async () => {
    const user = await testAuthService.validateUser('<EMAIL>', 'wrongpass');
    expect(user).toBeNull();
  });

  it('debe rechazar login con usuario inexistente', async () => {
    const user = await testAuthService.validateUser('<EMAIL>', 'password');
    expect(user).toBeNull();
  });
});

describe('Dummy', () => {
  it('should run a dummy test', () => {
    expect(true).toBe(true);
  });
});
