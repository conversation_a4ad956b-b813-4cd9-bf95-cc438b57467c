const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuración
const API_URL = 'http://localhost:3000/api';

async function testBackupSystem() {
  try {
    console.log('🧪 Probando sistema de backup mejorado...');
    
    // 1. Login para obtener token
    console.log('🔐 Haciendo login...');
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      credential: '<EMAIL>',
      password: 'password'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ Login exitoso');
    
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
    
    // 2. Crear backup con formato limpio
    console.log('📦 Creando backup con formato limpio...');
    const backupResponse = await axios.post(`${API_URL}/systems/backups`, {
      name: `Test-Backup-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}`,
      description: 'Backup de prueba con formato limpio INSERT',
      type: 'manual',
      format: 'clean'
    }, { headers });
    
    console.log('✅ Backup creado:', {
      id: backupResponse.data.id,
      name: backupResponse.data.name,
      status: backupResponse.data.status,
      message: backupResponse.data.message
    });
    
    const backupId = backupResponse.data.id;
    
    // 3. Esperar a que el backup se complete (polling)
    console.log('⏳ Esperando a que el backup se complete...');
    let backupCompleted = false;
    let attempts = 0;
    const maxAttempts = 30; // 30 segundos máximo
    
    while (!backupCompleted && attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Esperar 1 segundo
      
      try {
        const statusResponse = await axios.get(`${API_URL}/systems/backups/${backupId}`, { headers });
        const backup = statusResponse.data;
        
        console.log(`📊 Estado del backup: ${backup.status} (intento ${attempts + 1}/${maxAttempts})`);
        
        if (backup.status === 'completed') {
          backupCompleted = true;
          console.log('✅ Backup completado exitosamente');
          console.log('📄 Información del archivo:');
          console.log(`   - Ruta: ${backup.filePath}`);
          console.log(`   - Tamaño: ${(backup.fileSize / 1024 / 1024).toFixed(2)} MB`);
          console.log(`   - Creado: ${new Date(backup.completedAt).toLocaleString()}`);
          
          // 4. Verificar contenido del archivo
          if (backup.filePath && fs.existsSync(backup.filePath)) {
            console.log('🔍 Analizando contenido del backup...');
            const content = fs.readFileSync(backup.filePath, 'utf8');
            
            // Verificar características del formato limpio
            const hasHeader = content.includes('COMINTEC - Base de Datos Completa');
            const hasInsertFormat = content.includes('INSERT FORMAT');
            const hasExtensions = content.includes('CREATE EXTENSION IF NOT EXISTS');
            const insertCount = (content.match(/^INSERT INTO/gm) || []).length;
            const hasFooter = content.includes('FIN DEL BACKUP');
            const hasInstructions = content.includes('Para restaurar:');
            
            console.log('📋 Análisis del formato:');
            console.log(`   ✅ Encabezado COMINTEC: ${hasHeader ? 'Sí' : 'No'}`);
            console.log(`   ✅ Formato INSERT: ${hasInsertFormat ? 'Sí' : 'No'}`);
            console.log(`   ✅ Extensiones incluidas: ${hasExtensions ? 'Sí' : 'No'}`);
            console.log(`   ✅ Statements INSERT: ${insertCount}`);
            console.log(`   ✅ Footer con instrucciones: ${hasFooter && hasInstructions ? 'Sí' : 'No'}`);
            
            // Verificar que no tenga dependencias de Supabase
            const hasSupabaseTables = content.includes('supabase_') || content.includes('_realtime');
            console.log(`   ✅ Sin tablas de Supabase: ${!hasSupabaseTables ? 'Sí' : 'No'}`);
            
            // Mostrar primeras líneas del archivo
            const lines = content.split('\n');
            console.log('\n📝 Primeras 10 líneas del backup:');
            lines.slice(0, 10).forEach((line, index) => {
              console.log(`   ${index + 1}: ${line}`);
            });
            
            // Buscar algunos INSERT statements
            const insertLines = lines.filter(line => line.startsWith('INSERT INTO')).slice(0, 3);
            if (insertLines.length > 0) {
              console.log('\n🔍 Ejemplos de INSERT statements:');
              insertLines.forEach((line, index) => {
                console.log(`   ${index + 1}: ${line.substring(0, 100)}...`);
              });
            }
            
          } else {
            console.log('❌ Archivo de backup no encontrado en el sistema de archivos');
          }
          
        } else if (backup.status === 'failed') {
          console.log('❌ Backup falló:', backup.errorMessage);
          break;
        }
        
      } catch (error) {
        console.log(`⚠️ Error al verificar estado: ${error.message}`);
      }
      
      attempts++;
    }
    
    if (!backupCompleted && attempts >= maxAttempts) {
      console.log('⏰ Timeout: El backup no se completó en el tiempo esperado');
    }
    
    // 5. Obtener estadísticas de backups
    console.log('\n📊 Obteniendo estadísticas de backups...');
    const statsResponse = await axios.get(`${API_URL}/systems/backups/stats`, { headers });
    console.log('📈 Estadísticas:', statsResponse.data);
    
    // 6. Listar todos los backups
    console.log('\n📋 Listando todos los backups...');
    const backupsResponse = await axios.get(`${API_URL}/systems/backups`, { headers });
    const backups = backupsResponse.data;
    
    console.log(`📦 Total de backups: ${backups.length}`);
    backups.slice(0, 5).forEach((backup, index) => {
      console.log(`   ${index + 1}. ${backup.name} - ${backup.status} - ${new Date(backup.createdAt).toLocaleString()}`);
    });
    
    console.log('\n✅ Prueba del sistema de backup completada exitosamente');
    
  } catch (error) {
    console.error('❌ Error en la prueba:', error.message);
    
    if (error.response) {
      console.error('- Status:', error.response.status);
      console.error('- Data:', error.response.data);
    }
    
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 El servidor backend no está corriendo en el puerto 3000');
    }
  }
}

// Ejecutar la prueba
testBackupSystem();
