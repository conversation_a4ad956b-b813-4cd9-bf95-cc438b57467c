#!/bin/bash

# Script para configurar el entorno de pruebas en Supabase
echo "🔧 Configurando entorno de pruebas en Supabase..."

# Cargar variables de entorno desde .env
if [ -f ".env" ]; then
    echo "📄 Cargando variables de entorno desde .env..."
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "❌ Error: Archivo .env no encontrado"
    exit 1
fi

# Verificar que las variables de entorno estén configuradas
if [ -z "$DB_HOST" ] || [ -z "$DB_PASSWORD" ]; then
    echo "❌ Error: Las variables de entorno de la base de datos no están configuradas"
    echo "Variables encontradas:"
    echo "  DB_HOST: $DB_HOST"
    echo "  DB_PASSWORD: ${DB_PASSWORD:0:10}..." # Mostrar solo los primeros 10 caracteres
    exit 1
fi

echo "✅ Variables de entorno cargadas correctamente"
echo "   Host: $DB_HOST"
echo "   Database: $DB_DATABASE"
echo "   Usuario: $DB_USERNAME"

# Instalar psql si no está disponible
if ! command -v psql &> /dev/null; then
    echo "📦 Instalando PostgreSQL client..."
    sudo apt-get update
    sudo apt-get install -y postgresql-client
fi

# Ejecutar el script SQL para crear el esquema de prueba
echo "🗄️  Creando esquema de prueba en Supabase..."
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U $DB_USERNAME -d $DB_DATABASE -f setup-test-schema.sql

if [ $? -eq 0 ]; then
    echo "✅ Esquema de prueba creado exitosamente"
    echo "🚀 Ahora puedes ejecutar los tests con: npm test"
else
    echo "❌ Error al crear el esquema de prueba"
    exit 1
fi

echo ""
echo "📋 Resumen de configuración:"
echo "   - Host: $DB_HOST"
echo "   - Database: $DB_DATABASE"
echo "   - Schema: test_schema"
echo "   - Usuario: $DB_USERNAME"
echo ""
echo "⚠️  IMPORTANTE: Los tests ahora usarán el esquema 'test_schema'"
echo "   para evitar conflictos con los datos de producción" 