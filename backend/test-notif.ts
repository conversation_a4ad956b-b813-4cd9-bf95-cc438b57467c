import { AppDataSource } from './ormconfig';
import { NotificationService } from './src/services/notification.service';

(async () => {
  try {
    console.log('Initializing DS');
    await AppDataSource.initialize();
    console.log('DS initialized');
    const svc = new NotificationService();
    console.time('get');
    const notifs = await svc.getNotifications();
    console.timeEnd('get');
    console.log('Count', notifs.length);
  } catch (err) {
    console.error('Error', err);
  } finally {
    await AppDataSource.destroy();
  }
})(); 