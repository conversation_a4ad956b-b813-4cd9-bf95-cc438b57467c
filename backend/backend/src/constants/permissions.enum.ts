// Auto-generated from scripts/permissions.yaml
export enum PermissionEnum {
  ALMACEN_STOCK_READ = 'almacen:stock:read',
  ALMACEN_STOCK_UPDATE = 'almacen:stock:update',
  ALMACEN_QR_READ = 'almacen:qr:read',
  ALMACEN_QR_WRITE = 'almacen:qr:write',
  COMPRAS_SUPPLIER_CREATE = 'compras:supplier:create',
  COMPRAS_SUPPLIER_READ = 'compras:supplier:read',
  COMPRAS_SUPPLIER_UPDATE = 'compras:supplier:update',
  COMPRAS_SUPPLIER_DELETE = 'compras:supplier:delete',
  COMPRAS_REQUEST_CREATE = 'compras:request:create',
  COMPRAS_REQUEST_READ = 'compras:request:read',
  COMPRAS_REQUEST_UPDATE = 'compras:request:update',
  COMPRAS_REQUEST_DELETE = 'compras:request:delete',
  COMPRAS_REQUEST_APPROVE = 'compras:request:approve',
  QUALITY_QUESTIONPRO_CREATE = 'quality:questionpro:create',
  QUALITY_QUESTIONPRO_READ = 'quality:questionpro:read',
  QUALITY_QUESTIONPRO_UPDATE = 'quality:questionpro:update',
  QUALITY_QUESTIONPRO_DELETE = 'quality:questionpro:delete',
  QUALITY_INCIDENCIA_CREATE = 'quality:incidencia:create',
  QUALITY_INCIDENCIA_READ = 'quality:incidencia:read',
  QUALITY_INCIDENCIA_UPDATE = 'quality:incidencia:update',
  QUALITY_INCIDENCIA_DELETE = 'quality:incidencia:delete',
  QUALITY_INCIDENCIA_APPROVE = 'quality:incidencia:approve',
  RH_TRAINING_MANAGE = 'rh:training:manage',
  RH_FILES_MANAGE = 'rh:files:manage',
  LOGISTICA_SERVICE_CREATE = 'logistica:service:create',
  LOGISTICA_SERVICE_READ = 'logistica:service:read',
  LOGISTICA_SERVICE_UPDATE = 'logistica:service:update',
  LOGISTICA_SERVICE_DELETE = 'logistica:service:delete',
  LOGISTICA_SERVICE_SCHEDULE = 'logistica:service:schedule',
  LOGISTICA_SERVICE_GENERATE_PDF = 'logistica:service:generate_pdf',
}
