{
  "compilerOptions": {
    /* Visit https://aka.ms/tsconfig to read more about this file */

    /* Language and Environment */
    "target": "es2020",
    "module": "commonjs",
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "allowSyntheticDefaultImports": true,

    /* Type Checking */
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": false,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,

    /* Module Resolution */
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "baseUrl": ".",
    "rootDir": ".",
    "paths": {
      "@/*": ["src/*"]
    },

    /* Emit */
    "outDir": "./dist",
    "declaration": true,
    "sourceMap": true,

    /* Advanced */
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true
  },
  "typeRoots": [
    "./node_modules/@types",
    "./src/@types"
  ],
  "include": [
    "src/**/*",
    "src/**/*.d.ts",
    "src/@types/express/index.d.ts"
  ],
  "exclude": [
    "node_modules",
    "**/*.test.ts",
    "__tests__",
    "**/*.backup.ts",
    "**/*.updated.ts",
    "src/scripts"
  ],
  "types": ["jest", "node"]
}
