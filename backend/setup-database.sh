#!/bin/bash

# Script para configurar la base de datos principal con datos e índices
echo "🔧 Configurando base de datos principal..."

# Cargar variables de entorno desde .env
if [ -f ".env" ]; then
    echo "📄 Cargando variables de entorno desde .env..."
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "❌ Error: Archivo .env no encontrado"
    exit 1
fi

# Verificar que las variables de entorno estén configuradas
if [ -z "$DB_HOST" ] || [ -z "$DB_PASSWORD" ]; then
    echo "❌ Error: Las variables de entorno de la base de datos no están configuradas"
    echo "Variables encontradas:"
    echo "  DB_HOST: $DB_HOST"
    echo "  DB_PASSWORD: ${DB_PASSWORD:0:10}..." # Mostrar solo los primeros 10 caracteres
    exit 1
fi

echo "✅ Variables de entorno cargadas correctamente"
echo "   Host: $DB_HOST"
echo "   Database: $DB_DATABASE"
echo "   Usuario: $DB_USERNAME"

# Instalar psql si no está disponible
if ! command -v psql &> /dev/null; then
    echo "📦 Instalando PostgreSQL client..."
    sudo apt-get update
    sudo apt-get install -y postgresql-client
fi

# Ejecutar el script SQL principal para crear datos e índices
echo "🗄️  Ejecutando script principal de datos e índices..."
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U $DB_USERNAME -d $DB_DATABASE -f ../comintec_schema_data_indexes.sql

if [ $? -eq 0 ]; then
    echo "✅ Datos e índices creados exitosamente"
    echo "🚀 La base de datos está lista para usar"
else
    echo "❌ Error al ejecutar el script de datos e índices"
    exit 1
fi

echo ""
echo "📋 Resumen de configuración:"
echo "   - Host: $DB_HOST"
echo "   - Database: $DB_DATABASE"
echo "   - Usuario: $DB_USERNAME"
echo ""
echo "✅ La base de datos principal está configurada con todos los datos necesarios" 