const fs = require('fs');
const path = require('path');

// Función para analizar un backup
function analyzeBackup(filePath) {
  console.log(`🔍 Analizando backup: ${path.basename(filePath)}`);
  
  if (!fs.existsSync(filePath)) {
    console.log('❌ Archivo no encontrado');
    return;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  
  console.log(`📄 Total de líneas: ${lines.length}`);
  console.log(`📦 Tamaño del archivo: ${(fs.statSync(filePath).size / 1024 / 1024).toFixed(2)} MB`);
  
  // Detectar tablas CREATE TABLE
  const createTableMatches = content.match(/CREATE TABLE (?:public\.)?(\w+)/g) || [];
  const createTables = createTableMatches.map(match => {
    const tableMatch = match.match(/CREATE TABLE (?:public\.)?(\w+)/);
    return tableMatch ? tableMatch[1] : null;
  }).filter(Boolean);
  
  // Detectar tablas INSERT INTO
  const insertTableMatches = content.match(/INSERT INTO (?:public\.)?(\w+)/g) || [];
  const insertTables = insertTableMatches.map(match => {
    const tableMatch = match.match(/INSERT INTO (?:public\.)?(\w+)/);
    return tableMatch ? tableMatch[1] : null;
  }).filter(Boolean);
  
  // Combinar y obtener tablas únicas
  const allTables = [...new Set([...createTables, ...insertTables])];
  
  console.log(`\n📋 TABLAS DETECTADAS (${allTables.length} total):`);
  console.log('='.repeat(50));
  
  allTables.sort().forEach((table, index) => {
    const hasCreate = createTables.includes(table);
    const hasInsert = insertTables.includes(table);
    const insertCount = insertTableMatches.filter(match => match.includes(table)).length;
    
    console.log(`${index + 1}. ${table}`);
    console.log(`   - Estructura: ${hasCreate ? '✅' : '❌'}`);
    console.log(`   - Datos: ${hasInsert ? '✅' : '❌'} (${insertCount} registros)`);
  });
  
  // Contar INSERT statements totales
  const totalInserts = (content.match(/^INSERT INTO/gm) || []).length;
  console.log(`\n📊 ESTADÍSTICAS:`);
  console.log(`- Total INSERT statements: ${totalInserts}`);
  console.log(`- Tablas con estructura: ${createTables.length}`);
  console.log(`- Tablas con datos: ${[...new Set(insertTables)].length}`);
  
  // Verificar tablas críticas
  const criticalTables = ['users', 'roles', 'permissions', 'audit_logs'];
  const foundCritical = criticalTables.filter(table => allTables.includes(table));
  
  console.log(`\n🔑 TABLAS CRÍTICAS:`);
  criticalTables.forEach(table => {
    const found = allTables.includes(table);
    console.log(`- ${table}: ${found ? '✅' : '❌'}`);
  });
  
  // Buscar otras tablas importantes
  const importantPatterns = [
    'client', 'customer', 'product', 'order', 'invoice', 'payment',
    'backup', 'config', 'setting', 'log', 'notification', 'session'
  ];
  
  const otherImportant = allTables.filter(table => 
    importantPatterns.some(pattern => 
      table.toLowerCase().includes(pattern)
    )
  );
  
  if (otherImportant.length > 0) {
    console.log(`\n💼 OTRAS TABLAS IMPORTANTES:`);
    otherImportant.forEach(table => {
      console.log(`- ${table}`);
    });
  }
  
  // Verificar si es un backup completo
  const isComplete = allTables.length > 10 && foundCritical.length >= 3;
  console.log(`\n🎯 EVALUACIÓN:`);
  console.log(`- Backup completo: ${isComplete ? '✅' : '❌'}`);
  console.log(`- Tablas críticas: ${foundCritical.length}/4`);
  console.log(`- Total tablas: ${allTables.length}`);
  
  return {
    totalTables: allTables.length,
    tables: allTables,
    criticalTables: foundCritical,
    totalInserts,
    isComplete
  };
}

// Buscar archivos de backup
const backupDir = path.join(__dirname, 'backups');
if (fs.existsSync(backupDir)) {
  const files = fs.readdirSync(backupDir).filter(file => file.endsWith('.sql'));
  
  if (files.length === 0) {
    console.log('❌ No se encontraron archivos de backup en ./backups/');
  } else {
    console.log(`📁 Encontrados ${files.length} archivos de backup:`);
    
    files.forEach((file, index) => {
      console.log(`\n${'='.repeat(60)}`);
      console.log(`BACKUP ${index + 1}/${files.length}`);
      console.log('='.repeat(60));
      
      const filePath = path.join(backupDir, file);
      analyzeBackup(filePath);
    });
  }
} else {
  console.log('❌ Directorio ./backups/ no encontrado');
  
  // Buscar en directorio actual
  const currentFiles = fs.readdirSync('.').filter(file => file.endsWith('.sql'));
  if (currentFiles.length > 0) {
    console.log(`📁 Encontrados ${currentFiles.length} archivos .sql en directorio actual:`);
    currentFiles.forEach(file => {
      console.log(`\n${'='.repeat(60)}`);
      analyzeBackup(file);
    });
  }
}
