module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  testTimeout: 15000, // Reducido para tests unitarios
  displayName: 'Unit Tests',
  
  // Configuración para tests unitarios (sin BD)
  testMatch: [
    '**/__tests__/unit/**/*.test.ts',
    '**/*.unit.test.ts'
  ],
  
  // Excluir tests de integración
  testPathIgnorePatterns: [
    '/node_modules/',
    '/__tests__/integration/',
    '/__tests__/e2e/',
    '/dist/',
    '\\.integration\\.test\\.ts$',
    '\\.e2e\\.test\\.ts$'
  ],
  
  // Configuración TypeScript
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
  
  // Alias de módulos
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@tests/(.*)$': '<rootDir>/__tests__/$1',
  },
  
  // Configuración de coverage para 100%
  collectCoverage: true,
  coverageDirectory: 'coverage/unit',
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/server.ts', // Archivo de entry point
    '!src/app.ts', // Archivo de configuración principal
    '!src/data-source.ts', // Configuración de DB
    '!src/migrations/**',
    '!src/tests/**',
    '!src/@types/**',
    '!src/types/**'
  ],
  
  // Threshold de coverage temporal para debugging
  coverageThreshold: {
    global: {
      branches: 0,
      functions: 0,
      lines: 0,
      statements: 0,
    },
  },
  
  // Setup para mocks
  setupFilesAfterEnv: ['<rootDir>/__tests__/setup/unit-setup.ts'],
  
  // Configuración de mocks
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true,
  
  // Configuración para Jest globals
  globals: {
    'ts-jest': {
      isolatedModules: true,
      useESM: false,
    },
  },
  
  // Configuración para trabajadores
  maxWorkers: '50%',
  
  // Verbose para debugging
  verbose: true,
  
  // Configuración de transformación
  moduleFileExtensions: ['ts', 'js', 'json'],
  
  // Mock de módulos automáticos
  automock: false,
}; 