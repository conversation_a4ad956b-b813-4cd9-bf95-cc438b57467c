{"name": "comintec-backend", "version": "1.0.0", "description": "Backend para el sistema de Comintec", "author": "Equipo de Desarrollo Comintec", "license": "ISC", "main": "dist/server.js", "scripts": {"dev": "nodemon --exec ts-node --files src/server.ts", "build": "tsc", "start": "node dist/src/server.js", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "test": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit", "typeorm": "ts-node ./node_modules/typeorm/cli.js", "seed:admin": "ts-node src/scripts/seed-admin.ts", "test:unit": "NODE_ENV=test jest --config=jest.unit.config.js", "test:watch": "NODE_ENV=test jest --watch", "test:cov": "NODE_ENV=test jest --coverage", "seed:permissions": "ts-node-script --compiler-options '{\"module\":\"commonjs\"}' scripts/initialize-permissions.ts", "seed:roles": "ts-node-script --compiler-options '{\"module\":\"commonjs\"}' scripts/initialize-roles.ts", "seed:all": "npm run seed:permissions && npm run seed:roles", "db:export": "bash ../scripts/update-db-schema.sh", "cleanup:scripts": "rm -rf src/scripts && echo 'Scripts consolidados en backend/scripts/'"}, "engines": {"node": ">=14.0.0"}, "dependencies": {"@aws-sdk/client-s3": "^3.839.0", "@types/bcrypt": "^5.0.2", "@types/socket.io": "^3.0.1", "axios": "^1.10.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "node-cron": "^4.2.0", "pg": "^8.16.2", "reflect-metadata": "^0.2.2", "socket.io": "^4.8.1", "typeorm": "^0.3.25"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/multer": "^1.4.13", "@types/node": "^24.0.3", "@types/pg": "^8.15.4", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.35.0", "babel-jest": "^30.0.4", "eslint": "^8.56.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^6.3.4", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}