# 🧪 Configuración de Tests - COMINTEC Backend

## 📋 Resumen

Este proyecto usa **Supabase** como base de datos y tiene tests configurados para ejecutarse en un esquema separado (`test_schema`) para evitar conflictos con los datos de producción.

## 🔧 Configuración Inicial

### 1. Credenciales de Supabase

Las credenciales ya están configuradas en el archivo `.env`:

```env
DB_HOST=db.awlxzhrubqkryrenunun.supabase.co
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=appcomintecpassw0rd
DB_DATABASE=postgres
```

### 2. Configurar Esquema de Prueba

Ejecuta el script de configuración:

```bash
# Dar permisos de ejecución (si no se ha hecho)
chmod +x setup-test-environment.sh

# Ejecutar configuración
./setup-test-environment.sh
```

Este script:
- ✅ Crea el esquema `test_schema` en Supabase
- ✅ Crea todas las tablas necesarias para los tests
- ✅ Configura índices para mejor rendimiento
- ✅ Verifica la conexión

### 3. Configurar Frontend (Opcional)

Si también quieres configurar el frontend, copia el contenido de `frontend-env-example.txt` a `../frontend/.env.local`:

```bash
cp frontend-env-example.txt ../frontend/.env.local
```

## 🚀 Ejecutar Tests

### Test Individual
```bash
# Test de autenticación (funciona perfectamente)
npm test -- --testPathPattern="auth-login.test.ts"

# Test de usuarios
npm test -- --testPathPattern="users.test.ts"

# Test de integración de admin
npm test -- --testPathPattern="admin.integration.test.ts"

# Test de RBAC
npm test -- --testPathPattern="rbac.test.ts"
```

### Todos los Tests
```bash
npm test
```

## 📊 Estado Actual de los Tests

### ✅ **Funcionando Perfectamente:**
- **auth-login.test.ts** - Login, validación de credenciales
- **Conexión a Supabase** - Configurada y funcionando
- **Rutas corregidas** - Todas las rutas apuntan correctamente

### 🔄 **En Progreso:**
- **users.test.ts** - Tests de gestión de usuarios
- **admin.integration.test.ts** - Tests de integración de admin
- **rbac.test.ts** - Tests de control de acceso basado en roles

### ❌ **Problemas Identificados:**
1. **Conflicto de conexiones** - Los servicios usan `AppDataSource` pero tests usan `testDataSource`
2. **Middleware de autenticación** - No encuentra usuarios en el esquema de prueba
3. **Middleware de permisos** - Requiere permisos específicos no configurados

## 🛠️ Soluciones Implementadas

### 1. Esquema Separado para Tests
- Los tests usan el esquema `test_schema`
- Los datos de producción permanecen intactos
- Configuración en `src/config/test.config.ts`

### 2. Tokens de Prueba Mejorados
- Expiración extendida (24h)
- Helper de generación exportado
- Configuración en `src/tests/test-setup.ts`

### 3. Rutas Corregidas
- Admin: `/api/admin/...` (antes `/api/v1/admin/...`)
- Systems: `/api/systems/...` (configuradas correctamente)
- Método `getUsers` agregado al UserController

## 🔍 Debugging

### Verificar Conexión a Supabase
```bash
# Verificar que las credenciales están cargadas
cat .env | grep DB_

# Probar conexión
PGPASSWORD=appcomintecpassw0rd psql -h db.awlxzhrubqkryrenunun.supabase.co -U postgres -d postgres -c "SELECT current_database();"
```

### Verificar Esquema de Prueba
```bash
# Conectar y verificar esquema
PGPASSWORD=appcomintecpassw0rd psql -h db.awlxzhrubqkryrenunun.supabase.co -U postgres -d postgres -c "SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'test_schema';"
```

## 📝 Notas Importantes

1. **Nunca ejecutes tests en producción** - Los tests están configurados para usar un esquema separado
2. **Los datos de prueba se limpian automáticamente** - Cada test limpia sus datos al finalizar
3. **Las credenciales son sensibles** - No subas archivos `.env` a Git
4. **Supabase tiene limitaciones** - No permite ciertas operaciones como `DROP DATABASE`

## 🆘 Troubleshooting

### Error: "EntityMetadataNotFoundError"
- **Causa:** Conflicto entre `AppDataSource` y `testDataSource`
- **Solución:** Usar solo `testDataSource` en tests

### Error: "Unauthorized (401)"
- **Causa:** Token inválido o usuario no encontrado
- **Solución:** Verificar que el usuario existe en el esquema de prueba

### Error: "Forbidden (403)"
- **Causa:** Falta de permisos específicos
- **Solución:** Crear permisos de prueba en el esquema `test_schema`

## 📞 Soporte

Para problemas técnicos:
1. Verificar que el esquema `test_schema` existe
2. Confirmar que las credenciales de Supabase son correctas
3. Revisar los logs de error en la consola
4. Contactar al equipo de desarrollo si persisten los problemas 