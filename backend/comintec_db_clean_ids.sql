-- =============================
-- COMINTEC - Base de Datos Limpia (IDs Reiniciados)
-- =============================
-- IMPORTANTE: Este backup reinicia todos los IDs desde 1
-- Mantiene todas las relaciones FK intactas
-- Ideal para despliegue en producción
-- =============================

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

-- Crear extensiones necesarias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================
-- ESTRUCTURA DE TABLAS
-- =============================

--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.5 (Ubuntu 17.5-1.pgdg22.04+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: public; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA public;


--
-- Name: notification_priority_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.notification_priority_enum AS ENUM (
    'low',
    'medium',
    'high',
    'urgent'
);


--
-- Name: notification_status_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.notification_status_enum AS ENUM (
    'unread',
    'read',
    'dismissed'
);


--
-- Name: notification_type_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.notification_type_enum AS ENUM (
    'info',
    'success',
    'warning',
    'error',
    'system',
    'task',
    'reminder'
);


--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at := NOW();
    RETURN NEW;
END;
$$;


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: adjuntos_seguimiento; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.adjuntos_seguimiento (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    seguimiento_id uuid,
    archivo_url text NOT NULL,
    nombre_archivo text NOT NULL,
    tipo_archivo text,
    subido_por uuid,
    fecha_subida timestamp without time zone DEFAULT now()
);


--
-- Name: audit_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.audit_logs (
    id bigint NOT NULL,
    user_id bigint,
    action character varying(255) NOT NULL,
    target_entity character varying(100),
    target_id bigint,
    details jsonb,
    ip_address character varying(50),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by integer,
    updated_by integer,
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: audit_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.audit_logs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: audit_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.audit_logs_id_seq OWNED BY public.audit_logs.id;


--
-- Name: backups; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.backups (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    type character varying(50) DEFAULT 'manual'::character varying,
    status character varying(50) DEFAULT 'pending'::character varying,
    file_path text,
    file_size bigint,
    error_message text,
    metadata jsonb,
    scheduled_at timestamp without time zone,
    completed_at timestamp without time zone,
    expires_at timestamp without time zone,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    frequency character varying(20),
    CONSTRAINT backups_status_check CHECK (((status)::text = ANY (ARRAY[('pending'::character varying)::text, ('in_progress'::character varying)::text, ('completed'::character varying)::text, ('failed'::character varying)::text]))),
    CONSTRAINT backups_type_check CHECK (((type)::text = ANY (ARRAY[('manual'::character varying)::text, ('automatic'::character varying)::text, ('scheduled'::character varying)::text])))
);


--
-- Name: backups_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.backups_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: backups_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.backups_id_seq OWNED BY public.backups.id;


--
-- Name: calibrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.calibrations (
    id bigint NOT NULL,
    equipment_name character varying(255) NOT NULL,
    equipment_model character varying(255),
    serial_number character varying(100),
    calibration_date date NOT NULL,
    next_calibration_date date NOT NULL,
    calibrated_by bigint,
    department_id bigint,
    certificate_number character varying(100),
    status character varying(50) DEFAULT 'ACTIVE'::character varying,
    notes text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: calibrations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.calibrations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: calibrations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.calibrations_id_seq OWNED BY public.calibrations.id;


--
-- Name: client_documents; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.client_documents (
    id bigint NOT NULL,
    client_id bigint NOT NULL,
    document_id bigint NOT NULL,
    document_type character varying(50),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: client_documents_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.client_documents_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: client_documents_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.client_documents_id_seq OWNED BY public.client_documents.id;


--
-- Name: clients; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.clients (
    id integer NOT NULL,
    commercial_name character varying(255) NOT NULL,
    legal_name character varying(255),
    rfc character varying(13) NOT NULL,
    tax_regime character varying(50),
    neighborhood character varying(100),
    city character varying(100),
    state character varying(100),
    zip_code character varying(10),
    country character varying(50) DEFAULT 'México'::character varying,
    contact_name character varying(150),
    contact_position character varying(100),
    industry character varying(100),
    company_area character varying(100),
    company_size character varying(50),
    client_type character varying(50) DEFAULT 'Prospecto'::character varying,
    credit_limit integer DEFAULT 0,
    credit_days integer DEFAULT 0,
    preferred_payment character varying(50),
    website character varying(255),
    observations text,
    status character varying(20) DEFAULT 'Activo'::character varying NOT NULL,
    active boolean DEFAULT true NOT NULL,
    is_quick_registration boolean DEFAULT false NOT NULL,
    rfc_validated boolean DEFAULT false NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by_user_id integer NOT NULL,
    assigned_salesperson_id integer,
    contact_phones text[],
    contact_emails text[],
    street character varying
);


--
-- Name: clients_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.clients_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: clients_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.clients_id_seq OWNED BY public.clients.id;


--
-- Name: clients_id_seq1; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.clients_id_seq1
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: clients_id_seq1; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.clients_id_seq1 OWNED BY public.clients.id;


--
-- Name: contracts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.contracts (
    id bigint NOT NULL,
    created_by bigint,
    updated_by bigint,
    contract_number character varying(100),
    customer_id bigint NOT NULL,
    project_id bigint,
    contract_type character varying(50),
    start_date date,
    end_date date,
    total_amount numeric(15,2),
    status character varying(50) DEFAULT 'ACTIVE'::character varying,
    terms_conditions text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: contracts_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.contracts_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: contracts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.contracts_id_seq OWNED BY public.contracts.id;


--
-- Name: credit_request_documents; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.credit_request_documents (
    credit_request_id bigint NOT NULL,
    document_id bigint NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: credit_requests; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.credit_requests (
    id bigint NOT NULL,
    request_number character varying(50),
    customer_id bigint NOT NULL,
    amount numeric(15,2) NOT NULL,
    purpose text,
    requester_id bigint NOT NULL,
    status character varying(50) DEFAULT 'PENDING'::character varying,
    approved_by bigint,
    approval_date date,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: credit_requests_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.credit_requests_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: credit_requests_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.credit_requests_id_seq OWNED BY public.credit_requests.id;


--
-- Name: customers; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.customers (
    id bigint NOT NULL,
    razon_social character varying(255) NOT NULL,
    no_cliente character varying(50) NOT NULL,
    cliente_fiscal character varying(255),
    telefono character varying(20),
    correo character varying(100)
);


--
-- Name: customers_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.customers_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: customers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.customers_id_seq OWNED BY public.customers.id;


--
-- Name: departments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.departments (
    id bigint NOT NULL,
    name character varying(100) NOT NULL,
    description text,
    active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: departments_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.departments_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: departments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.departments_id_seq OWNED BY public.departments.id;


--
-- Name: documents; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.documents (
    id bigint NOT NULL,
    file_type character varying(50),
    file_size bigint,
    filename character varying(255) NOT NULL,
    url character varying(255) NOT NULL,
    uploaded_by bigint,
    department_id bigint,
    tipo_documento character varying(50),
    descripcion text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: documents_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.documents_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: documents_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.documents_id_seq OWNED BY public.documents.id;


--
-- Name: employee_files; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.employee_files (
    id bigint NOT NULL,
    employee_id bigint NOT NULL,
    file_type character varying(50),
    document_id bigint,
    description text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: employee_files_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.employee_files_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: employee_files_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.employee_files_id_seq OWNED BY public.employee_files.id;


--
-- Name: equipment_receipts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.equipment_receipts (
    id bigint NOT NULL,
    receipt_number character varying(50),
    equipment_name character varying(255) NOT NULL,
    equipment_model character varying(255),
    serial_number character varying(100),
    received_by bigint NOT NULL,
    received_from character varying(255),
    receipt_date date NOT NULL,
    condition_description text,
    notes text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: equipment_receipts_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.equipment_receipts_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: equipment_receipts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.equipment_receipts_id_seq OWNED BY public.equipment_receipts.id;


--
-- Name: event_attendees; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.event_attendees (
    id bigint NOT NULL,
    event_id bigint NOT NULL,
    user_id bigint NOT NULL,
    status character varying(50) DEFAULT 'REGISTERED'::character varying,
    attendance boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: event_attendees_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.event_attendees_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: event_attendees_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.event_attendees_id_seq OWNED BY public.event_attendees.id;


--
-- Name: event_documents; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.event_documents (
    id bigint NOT NULL,
    event_id bigint NOT NULL,
    document_id bigint NOT NULL,
    document_type character varying(50),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: event_documents_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.event_documents_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: event_documents_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.event_documents_id_seq OWNED BY public.event_documents.id;


--
-- Name: events; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.events (
    id bigint NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    start_date timestamp with time zone NOT NULL,
    end_date timestamp with time zone NOT NULL,
    location character varying(255),
    organizer_id bigint,
    department_id bigint,
    event_type character varying(50),
    max_attendees integer,
    status character varying(50) DEFAULT 'PLANNED'::character varying,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: events_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.events_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: events_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.events_id_seq OWNED BY public.events.id;


--
-- Name: fiscal_numbers; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.fiscal_numbers (
    id bigint NOT NULL,
    customer_id bigint,
    rfc character varying(13) NOT NULL,
    tax_regime character varying(255),
    fiscal_address text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: fiscal_numbers_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.fiscal_numbers_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: fiscal_numbers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.fiscal_numbers_id_seq OWNED BY public.fiscal_numbers.id;


--
-- Name: inventory; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.inventory (
    id bigint NOT NULL,
    product_id bigint NOT NULL,
    quantity integer NOT NULL,
    location character varying(100) DEFAULT 'GDL'::character varying,
    last_update timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: inventory_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.inventory_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: inventory_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.inventory_id_seq OWNED BY public.inventory.id;


--
-- Name: inventory_movements; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.inventory_movements (
    id bigint NOT NULL,
    product_id bigint NOT NULL,
    tipo_movimiento character varying(50) NOT NULL,
    user_id bigint,
    sale_id bigint,
    cantidad integer NOT NULL,
    motivo character varying(255),
    fecha timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: inventory_movements_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.inventory_movements_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: inventory_movements_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.inventory_movements_id_seq OWNED BY public.inventory_movements.id;


--
-- Name: invoice_documents; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.invoice_documents (
    invoice_id bigint NOT NULL,
    document_id bigint NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: invoices; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.invoices (
    id bigint NOT NULL,
    payment_terms text,
    created_by bigint,
    updated_by bigint,
    quotation_id bigint,
    customer_id bigint NOT NULL,
    invoice_number character varying(50),
    total_amount numeric(15,2) NOT NULL,
    tax_amount numeric(15,2) DEFAULT 0,
    status character varying(50) DEFAULT 'PENDING'::character varying,
    due_date date,
    payment_date date,
    notes text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: invoices_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.invoices_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: invoices_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.invoices_id_seq OWNED BY public.invoices.id;


--
-- Name: links_questionpro; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.links_questionpro (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    descripcion text NOT NULL,
    url text NOT NULL,
    creado_por uuid,
    fecha_creacion timestamp without time zone DEFAULT now()
);


--
-- Name: loans; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.loans (
    id bigint NOT NULL,
    product_id bigint NOT NULL,
    user_id bigint,
    customer_id bigint,
    fecha_prestamo timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    fecha_devolucion timestamp with time zone,
    estado character varying(50),
    observaciones text
);


--
-- Name: loans_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.loans_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: loans_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.loans_id_seq OWNED BY public.loans.id;


--
-- Name: logistics_events; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.logistics_events (
    id bigint NOT NULL,
    event_type character varying(50) NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    location character varying(255),
    scheduled_date timestamp with time zone,
    responsible_id bigint,
    customer_id bigint,
    status character varying(50) DEFAULT 'PLANNED'::character varying,
    priority character varying(20) DEFAULT 'MEDIUM'::character varying,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: logistics_events_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.logistics_events_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: logistics_events_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.logistics_events_id_seq OWNED BY public.logistics_events.id;


--
-- Name: marketing_campaigns; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.marketing_campaigns (
    id bigint NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    campaign_type character varying(50),
    start_date date,
    end_date date,
    budget numeric(15,2),
    status character varying(50) DEFAULT 'PLANNED'::character varying,
    target_audience text,
    created_by bigint,
    department_id bigint,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: marketing_campaigns_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.marketing_campaigns_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: marketing_campaigns_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.marketing_campaigns_id_seq OWNED BY public.marketing_campaigns.id;


--
-- Name: migrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.migrations (
    id integer NOT NULL,
    "timestamp" bigint NOT NULL,
    name character varying NOT NULL
);


--
-- Name: migrations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.migrations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: migrations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.migrations_id_seq OWNED BY public.migrations.id;


--
-- Name: notifications; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.notifications (
    id integer NOT NULL,
    title character varying(200) NOT NULL,
    message text NOT NULL,
    type public.notification_type_enum DEFAULT 'info'::public.notification_type_enum NOT NULL,
    status public.notification_status_enum DEFAULT 'unread'::public.notification_status_enum NOT NULL,
    priority public.notification_priority_enum DEFAULT 'medium'::public.notification_priority_enum NOT NULL,
    user_id integer,
    role_id integer,
    metadata jsonb,
    action_url character varying(100),
    action_label character varying(50),
    read_at timestamp with time zone,
    expires_at timestamp with time zone,
    email_sent boolean DEFAULT false NOT NULL,
    email_sent_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    created_by integer,
    updated_by integer
);


--
-- Name: notifications_backup; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.notifications_backup (
    id bigint,
    title character varying(255),
    message text,
    related_entity_type character varying(50),
    related_entity_id bigint,
    type character varying(50),
    user_id bigint,
    department_id bigint,
    is_read boolean,
    priority character varying(20),
    created_at timestamp with time zone
);


--
-- Name: notifications_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.notifications_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: notifications_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.notifications_id_seq OWNED BY public.notifications.id;


--
-- Name: password_history; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.password_history (
    id integer NOT NULL,
    user_id integer NOT NULL,
    password_hash character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by integer,
    ip_address inet,
    user_agent text
);


--
-- Name: password_history_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.password_history_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: password_history_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.password_history_id_seq OWNED BY public.password_history.id;


--
-- Name: permissions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.permissions (
    id bigint NOT NULL,
    name character varying(100) NOT NULL,
    module character varying(50) NOT NULL,
    action character varying(50) NOT NULL,
    description text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: permissions_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.permissions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.permissions_id_seq OWNED BY public.permissions.id;


--
-- Name: personal_follow_ups; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.personal_follow_ups (
    id bigint NOT NULL,
    employee_id bigint NOT NULL,
    follow_up_type character varying(50),
    description text,
    follow_up_date date,
    next_follow_up_date date,
    status character varying(50) DEFAULT 'PENDING'::character varying,
    responsible_id bigint,
    notes text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: personal_follow_ups_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.personal_follow_ups_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: personal_follow_ups_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.personal_follow_ups_id_seq OWNED BY public.personal_follow_ups.id;


--
-- Name: plant_services; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.plant_services (
    id bigint NOT NULL,
    service_number character varying(50),
    title character varying(255) NOT NULL,
    description text,
    customer_id bigint NOT NULL,
    service_type character varying(50),
    scheduled_date date,
    completed_date date,
    status character varying(50) DEFAULT 'SCHEDULED'::character varying,
    technician_id bigint,
    department_id bigint,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: plant_services_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.plant_services_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: plant_services_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.plant_services_id_seq OWNED BY public.plant_services.id;


--
-- Name: procedimientos_calidad; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.procedimientos_calidad (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    nombre text NOT NULL,
    tipo text NOT NULL,
    area text NOT NULL,
    archivo_url text NOT NULL,
    creado_por uuid,
    fecha_creacion timestamp without time zone DEFAULT now(),
    CONSTRAINT procedimientos_calidad_tipo_check CHECK ((tipo = ANY (ARRAY['tecnico'::text, 'general'::text])))
);


--
-- Name: procedures; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.procedures (
    id bigint NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    procedure_number character varying(100),
    version character varying(20),
    department_id bigint,
    created_by bigint,
    status character varying(50) DEFAULT 'DRAFT'::character varying,
    approval_date date,
    effective_date date,
    review_date date,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: procedures_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.procedures_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: procedures_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.procedures_id_seq OWNED BY public.procedures.id;


--
-- Name: products; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.products (
    id bigint NOT NULL,
    nombre character varying(255) NOT NULL,
    descripcion text,
    codigo_item character varying(100),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    ubicacion character varying(100) DEFAULT 'GDL'::character varying,
    marca character varying(100),
    modelo character varying(100),
    numero_serie character varying(100),
    pedimento character varying(100),
    observaciones text,
    tipo_almacen character varying(20) DEFAULT 'GENERAL'::character varying NOT NULL,
    stock_disponible integer DEFAULT 0 NOT NULL,
    stock_comprometido integer DEFAULT 0 NOT NULL,
    stock_minimo integer DEFAULT 1 NOT NULL,
    estado character varying(20) DEFAULT 'DISPONIBLE'::character varying NOT NULL,
    created_by integer,
    updated_by integer,
    deleted_at timestamp with time zone,
    CONSTRAINT check_estado CHECK (((estado)::text = ANY (ARRAY[('DISPONIBLE'::character varying)::text, ('AGOTADO'::character varying)::text, ('DESCONTINUADO'::character varying)::text]))),
    CONSTRAINT check_stock_comprometido CHECK ((stock_comprometido >= 0)),
    CONSTRAINT check_stock_disponible CHECK ((stock_disponible >= 0)),
    CONSTRAINT check_stock_minimo CHECK ((stock_minimo >= 1)),
    CONSTRAINT check_tipo_almacen CHECK (((tipo_almacen)::text = ANY (ARRAY[('GENERAL'::character varying)::text, ('ROTATIVO'::character varying)::text])))
);


--
-- Name: products_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.products_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: products_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.products_id_seq OWNED BY public.products.id;


--
-- Name: project_documents; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.project_documents (
    id bigint NOT NULL,
    project_id bigint NOT NULL,
    document_id bigint NOT NULL,
    document_type character varying(50),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: project_documents_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.project_documents_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: project_documents_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.project_documents_id_seq OWNED BY public.project_documents.id;


--
-- Name: project_stages; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.project_stages (
    id bigint NOT NULL,
    project_id bigint NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    order_index integer NOT NULL,
    start_date date,
    end_date date,
    status character varying(50) DEFAULT 'PENDING'::character varying,
    progress_percentage integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: project_stages_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.project_stages_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: project_stages_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.project_stages_id_seq OWNED BY public.project_stages.id;


--
-- Name: projects; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.projects (
    id bigint NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    customer_id bigint,
    manager_id bigint,
    start_date date,
    end_date date,
    status character varying(50) DEFAULT 'PLANNED'::character varying,
    priority character varying(20) DEFAULT 'MEDIUM'::character varying,
    budget numeric(15,2),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: projects_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.projects_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: projects_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.projects_id_seq OWNED BY public.projects.id;


--
-- Name: purchase_requests; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.purchase_requests (
    id bigint NOT NULL,
    user_id bigint,
    product_id bigint,
    cantidad integer NOT NULL,
    motivo text,
    fecha timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    estado character varying(50)
);


--
-- Name: purchase_requests_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.purchase_requests_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: purchase_requests_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.purchase_requests_id_seq OWNED BY public.purchase_requests.id;


--
-- Name: quality_issues; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.quality_issues (
    id bigint NOT NULL,
    issue_number character varying(50),
    title character varying(255) NOT NULL,
    description text,
    reporter_id bigint NOT NULL,
    assigned_to bigint,
    department_id bigint,
    severity character varying(20) DEFAULT 'MEDIUM'::character varying,
    status character varying(50) DEFAULT 'OPEN'::character varying,
    category character varying(50),
    resolution text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    resolved_at timestamp with time zone,
    tipo text,
    comentario_cierre text,
    cerrado_por uuid,
    CONSTRAINT quality_issues_tipo_check CHECK ((tipo = ANY (ARRAY['minuta'::text, 'queja'::text, 'no_conformidad'::text, 'llamado'::text, 'accion_correctiva'::text])))
);


--
-- Name: quality_issues_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.quality_issues_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: quality_issues_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.quality_issues_id_seq OWNED BY public.quality_issues.id;


--
-- Name: question_pro_links; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.question_pro_links (
    id bigint NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    link_url character varying(500) NOT NULL,
    created_by bigint,
    department_id bigint,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    descripcion text
);


--
-- Name: question_pro_links_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.question_pro_links_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: question_pro_links_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.question_pro_links_id_seq OWNED BY public.question_pro_links.id;


--
-- Name: quotation_documents; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.quotation_documents (
    id bigint NOT NULL,
    quotation_id bigint NOT NULL,
    document_id bigint NOT NULL,
    document_type character varying(50),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: quotation_documents_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.quotation_documents_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: quotation_documents_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.quotation_documents_id_seq OWNED BY public.quotation_documents.id;


--
-- Name: quotation_items; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.quotation_items (
    id bigint NOT NULL,
    quotation_id bigint NOT NULL,
    product_id bigint,
    description character varying(255) NOT NULL,
    quantity integer NOT NULL,
    unit_price numeric(15,2) NOT NULL,
    total_price numeric(15,2) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: quotation_items_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.quotation_items_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: quotation_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.quotation_items_id_seq OWNED BY public.quotation_items.id;


--
-- Name: quotations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.quotations (
    id bigint NOT NULL,
    customer_id bigint NOT NULL,
    project_id bigint,
    created_by bigint NOT NULL,
    quotation_number character varying(50),
    total_amount numeric(15,2),
    status character varying(50) DEFAULT 'DRAFT'::character varying,
    valid_until date,
    notes text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: quotations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.quotations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: quotations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.quotations_id_seq OWNED BY public.quotations.id;


--
-- Name: report_folios; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.report_folios (
    id bigint NOT NULL,
    folio_number character varying(50),
    title character varying(255) NOT NULL,
    description text,
    report_type character varying(50),
    created_by bigint NOT NULL,
    department_id bigint,
    status character varying(50) DEFAULT 'DRAFT'::character varying,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: report_folios_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.report_folios_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: report_folios_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.report_folios_id_seq OWNED BY public.report_folios.id;


--
-- Name: requests; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.requests (
    id bigint NOT NULL,
    destination_address text,
    shipping_company character varying(100),
    package_weight numeric(10,2),
    package_dimensions character varying(50),
    tracking_number character varying(100),
    request_type character varying(50) NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    requester_id bigint NOT NULL,
    department_id bigint,
    priority character varying(20) DEFAULT 'MEDIUM'::character varying,
    status character varying(50) DEFAULT 'PENDING'::character varying,
    assigned_to bigint,
    due_date date,
    completed_date date,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    category character varying(255)
);


--
-- Name: requests_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.requests_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: requests_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.requests_id_seq OWNED BY public.requests.id;


--
-- Name: role_permissions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.role_permissions (
    role_id bigint NOT NULL,
    permission_id bigint NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: roles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.roles (
    id bigint NOT NULL,
    name character varying(50) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: roles_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.roles_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: roles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.roles_id_seq OWNED BY public.roles.id;


--
-- Name: sales; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.sales (
    id bigint NOT NULL,
    customer_id bigint NOT NULL,
    registered_by bigint NOT NULL,
    responsable_ingeniero bigint,
    lugar_servicio character varying(255),
    tipo_servicio character varying(100),
    requerimientos_entrega text,
    hora_entrega timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: sales_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.sales_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: sales_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.sales_id_seq OWNED BY public.sales.id;


--
-- Name: seguimientos_calidad; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.seguimientos_calidad (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    tipo text NOT NULL,
    descripcion text NOT NULL,
    estado text DEFAULT 'En proceso'::text NOT NULL,
    fecha_creacion timestamp without time zone DEFAULT now(),
    fecha_cierre timestamp without time zone,
    creado_por uuid,
    cerrado_por uuid,
    comentario_cierre text,
    CONSTRAINT seguimientos_calidad_estado_check CHECK ((estado = ANY (ARRAY['En proceso'::text, 'Cerrado'::text]))),
    CONSTRAINT seguimientos_calidad_tipo_check CHECK ((tipo = ANY (ARRAY['minuta'::text, 'queja'::text, 'no_conformidad'::text, 'llamado'::text, 'accion_correctiva'::text])))
);


--
-- Name: system_tickets; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.system_tickets (
    id bigint NOT NULL,
    ticket_number character varying(50),
    title character varying(255) NOT NULL,
    description text,
    reporter_id bigint NOT NULL,
    assigned_to bigint,
    department_id bigint,
    priority character varying(20) DEFAULT 'MEDIUM'::character varying,
    status character varying(50) DEFAULT 'OPEN'::character varying,
    category character varying(50),
    resolution text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    resolved_at timestamp with time zone
);


--
-- Name: system_tickets_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.system_tickets_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: system_tickets_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.system_tickets_id_seq OWNED BY public.system_tickets.id;


--
-- Name: technical_sheets; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.technical_sheets (
    id bigint NOT NULL,
    product_id bigint,
    title character varying(255) NOT NULL,
    description text,
    specifications jsonb,
    created_by bigint,
    department_id bigint,
    version character varying(20),
    status character varying(50) DEFAULT 'DRAFT'::character varying,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: technical_sheets_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.technical_sheets_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: technical_sheets_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.technical_sheets_id_seq OWNED BY public.technical_sheets.id;


--
-- Name: training_documents; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.training_documents (
    id bigint NOT NULL,
    training_id bigint NOT NULL,
    document_id bigint NOT NULL,
    document_type character varying(50),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    name character varying(255),
    description text,
    is_required boolean DEFAULT false,
    is_public boolean DEFAULT true,
    file_path character varying(500),
    file_type character varying(100),
    file_size bigint,
    uploaded_by bigint,
    uploaded_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    version character varying(20),
    download_count integer DEFAULT 0,
    view_count integer DEFAULT 0
);


--
-- Name: training_documents_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.training_documents_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: training_documents_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.training_documents_id_seq OWNED BY public.training_documents.id;


--
-- Name: training_evaluations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.training_evaluations (
    id bigint NOT NULL,
    training_id bigint NOT NULL,
    participant_id bigint NOT NULL,
    evaluator_id bigint,
    score numeric(5,2),
    comments text,
    evaluation_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    evaluation_type character varying(50),
    max_score numeric(5,2) DEFAULT 100.00,
    passing_score numeric(5,2),
    is_passed boolean,
    attempt_number integer DEFAULT 1,
    evaluation_duration_minutes integer,
    questions_answered integer,
    total_questions integer
);


--
-- Name: training_evaluations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.training_evaluations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: training_evaluations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.training_evaluations_id_seq OWNED BY public.training_evaluations.id;


--
-- Name: training_participants; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.training_participants (
    id bigint NOT NULL,
    training_id bigint NOT NULL,
    user_id bigint NOT NULL,
    status character varying(50) DEFAULT 'REGISTERED'::character varying,
    attendance boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    registration_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    completion_date timestamp with time zone,
    score numeric(5,2),
    certificate_issued boolean DEFAULT false,
    certificate_issue_date timestamp with time zone,
    certificate_expiry_date timestamp with time zone,
    comments text,
    feedback_rating integer,
    feedback_comments text
);


--
-- Name: training_participants_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.training_participants_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: training_participants_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.training_participants_id_seq OWNED BY public.training_participants.id;


--
-- Name: trainings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.trainings (
    id bigint NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    trainer_id bigint,
    department_id bigint,
    start_date timestamp with time zone,
    end_date timestamp with time zone,
    location character varying(255),
    max_participants integer,
    status character varying(50) DEFAULT 'PLANNED'::character varying,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    cancelled_at timestamp with time zone,
    completed_at timestamp with time zone,
    instructor_id bigint,
    coordinator_id bigint,
    type character varying(50),
    training_mode character varying(50),
    registration_deadline timestamp with time zone,
    duration_hours integer,
    current_participants integer DEFAULT 0,
    min_score numeric(5,2),
    virtual_meeting_link character varying(500),
    materials_required text,
    prerequisites text,
    learning_objectives text,
    is_mandatory boolean DEFAULT false,
    is_certification_required boolean DEFAULT false,
    certification_validity_months integer,
    cost_per_participant numeric(10,2),
    total_budget numeric(15,2),
    is_notification_enabled boolean DEFAULT true,
    notification_frequency character varying(50),
    reminder_days_before integer,
    next_notification_date timestamp with time zone,
    last_notification_sent timestamp with time zone,
    evaluation_deadline timestamp with time zone,
    certificate_template text,
    notes text
);


--
-- Name: trainings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.trainings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: trainings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.trainings_id_seq OWNED BY public.trainings.id;


--
-- Name: user_departments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.user_departments (
    user_id bigint NOT NULL,
    department_id bigint NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: user_roles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.user_roles (
    user_id bigint NOT NULL,
    role_id bigint NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.users (
    id bigint NOT NULL,
    name character varying(100) NOT NULL,
    email character varying(100) NOT NULL,
    password character varying(255) NOT NULL,
    status character varying(20) DEFAULT 'active'::character varying,
    area character varying(100),
    active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    phone character varying(20),
    created_by integer,
    updated_by integer,
    refresh_token character varying(255)
);


--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.users_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: uso_links_questionpro; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.uso_links_questionpro (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    link_id uuid,
    usuario_id uuid,
    fecha timestamp without time zone DEFAULT now()
);


--
-- Name: v_links_questionpro_usage; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.v_links_questionpro_usage AS
 SELECT lq.id,
    lq.descripcion,
    lq.url,
    lq.fecha_creacion,
    (u.raw_user_meta_data ->> 'full_name'::text) AS creado_por_nombre,
    count(ulq.id) AS uso_count,
    max(ulq.fecha) AS ultimo_uso
   FROM ((public.links_questionpro lq
     LEFT JOIN auth.users u ON ((lq.creado_por = u.id)))
     LEFT JOIN public.uso_links_questionpro ulq ON ((lq.id = ulq.link_id)))
  GROUP BY lq.id, lq.descripcion, lq.url, lq.fecha_creacion, (u.raw_user_meta_data ->> 'full_name'::text);


--
-- Name: v_procedimientos_calidad; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.v_procedimientos_calidad AS
 SELECT pc.id,
    pc.nombre,
    pc.tipo,
    pc.area,
    pc.archivo_url,
    pc.fecha_creacion,
    (u.raw_user_meta_data ->> 'full_name'::text) AS creado_por_nombre,
    u.email AS creado_por_email
   FROM (public.procedimientos_calidad pc
     LEFT JOIN auth.users u ON ((pc.creado_por = u.id)));


--
-- Name: v_seguimientos_calidad; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.v_seguimientos_calidad AS
 SELECT sc.id,
    sc.tipo,
    sc.descripcion,
    sc.estado,
    sc.fecha_creacion,
    sc.fecha_cierre,
    sc.comentario_cierre,
    (u1.raw_user_meta_data ->> 'full_name'::text) AS creado_por_nombre,
    (u2.raw_user_meta_data ->> 'full_name'::text) AS cerrado_por_nombre,
    count(a.id) AS adjuntos_count
   FROM (((public.seguimientos_calidad sc
     LEFT JOIN auth.users u1 ON ((sc.creado_por = u1.id)))
     LEFT JOIN auth.users u2 ON ((sc.cerrado_por = u2.id)))
     LEFT JOIN public.adjuntos_seguimiento a ON ((sc.id = a.seguimiento_id)))
  GROUP BY sc.id, sc.tipo, sc.descripcion, sc.estado, sc.fecha_creacion, sc.fecha_cierre, sc.comentario_cierre, (u1.raw_user_meta_data ->> 'full_name'::text), (u2.raw_user_meta_data ->> 'full_name'::text);


--
-- Name: audit_logs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.audit_logs ALTER COLUMN id SET DEFAULT nextval('public.audit_logs_id_seq'::regclass);


--
-- Name: backups id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.backups ALTER COLUMN id SET DEFAULT nextval('public.backups_id_seq'::regclass);


--
-- Name: calibrations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.calibrations ALTER COLUMN id SET DEFAULT nextval('public.calibrations_id_seq'::regclass);


--
-- Name: client_documents id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.client_documents ALTER COLUMN id SET DEFAULT nextval('public.client_documents_id_seq'::regclass);


--
-- Name: clients id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.clients ALTER COLUMN id SET DEFAULT nextval('public.clients_id_seq1'::regclass);


--
-- Name: contracts id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.contracts ALTER COLUMN id SET DEFAULT nextval('public.contracts_id_seq'::regclass);


--
-- Name: credit_requests id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.credit_requests ALTER COLUMN id SET DEFAULT nextval('public.credit_requests_id_seq'::regclass);


--
-- Name: customers id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customers ALTER COLUMN id SET DEFAULT nextval('public.customers_id_seq'::regclass);


--
-- Name: departments id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.departments ALTER COLUMN id SET DEFAULT nextval('public.departments_id_seq'::regclass);


--
-- Name: documents id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.documents ALTER COLUMN id SET DEFAULT nextval('public.documents_id_seq'::regclass);


--
-- Name: employee_files id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.employee_files ALTER COLUMN id SET DEFAULT nextval('public.employee_files_id_seq'::regclass);


--
-- Name: equipment_receipts id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.equipment_receipts ALTER COLUMN id SET DEFAULT nextval('public.equipment_receipts_id_seq'::regclass);


--
-- Name: event_attendees id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.event_attendees ALTER COLUMN id SET DEFAULT nextval('public.event_attendees_id_seq'::regclass);


--
-- Name: event_documents id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.event_documents ALTER COLUMN id SET DEFAULT nextval('public.event_documents_id_seq'::regclass);


--
-- Name: events id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.events ALTER COLUMN id SET DEFAULT nextval('public.events_id_seq'::regclass);


--
-- Name: fiscal_numbers id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fiscal_numbers ALTER COLUMN id SET DEFAULT nextval('public.fiscal_numbers_id_seq'::regclass);


--
-- Name: inventory id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inventory ALTER COLUMN id SET DEFAULT nextval('public.inventory_id_seq'::regclass);


--
-- Name: inventory_movements id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inventory_movements ALTER COLUMN id SET DEFAULT nextval('public.inventory_movements_id_seq'::regclass);


--
-- Name: invoices id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices ALTER COLUMN id SET DEFAULT nextval('public.invoices_id_seq'::regclass);


--
-- Name: loans id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.loans ALTER COLUMN id SET DEFAULT nextval('public.loans_id_seq'::regclass);


--
-- Name: logistics_events id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.logistics_events ALTER COLUMN id SET DEFAULT nextval('public.logistics_events_id_seq'::regclass);


--
-- Name: marketing_campaigns id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.marketing_campaigns ALTER COLUMN id SET DEFAULT nextval('public.marketing_campaigns_id_seq'::regclass);


--
-- Name: migrations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.migrations ALTER COLUMN id SET DEFAULT nextval('public.migrations_id_seq'::regclass);


--
-- Name: notifications id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notifications ALTER COLUMN id SET DEFAULT nextval('public.notifications_id_seq'::regclass);


--
-- Name: password_history id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.password_history ALTER COLUMN id SET DEFAULT nextval('public.password_history_id_seq'::regclass);


--
-- Name: permissions id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.permissions ALTER COLUMN id SET DEFAULT nextval('public.permissions_id_seq'::regclass);


--
-- Name: personal_follow_ups id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.personal_follow_ups ALTER COLUMN id SET DEFAULT nextval('public.personal_follow_ups_id_seq'::regclass);


--
-- Name: plant_services id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.plant_services ALTER COLUMN id SET DEFAULT nextval('public.plant_services_id_seq'::regclass);


--
-- Name: procedures id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.procedures ALTER COLUMN id SET DEFAULT nextval('public.procedures_id_seq'::regclass);


--
-- Name: products id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.products ALTER COLUMN id SET DEFAULT nextval('public.products_id_seq'::regclass);


--
-- Name: project_documents id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.project_documents ALTER COLUMN id SET DEFAULT nextval('public.project_documents_id_seq'::regclass);


--
-- Name: project_stages id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.project_stages ALTER COLUMN id SET DEFAULT nextval('public.project_stages_id_seq'::regclass);


--
-- Name: projects id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.projects ALTER COLUMN id SET DEFAULT nextval('public.projects_id_seq'::regclass);


--
-- Name: purchase_requests id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.purchase_requests ALTER COLUMN id SET DEFAULT nextval('public.purchase_requests_id_seq'::regclass);


--
-- Name: quality_issues id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quality_issues ALTER COLUMN id SET DEFAULT nextval('public.quality_issues_id_seq'::regclass);


--
-- Name: question_pro_links id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.question_pro_links ALTER COLUMN id SET DEFAULT nextval('public.question_pro_links_id_seq'::regclass);


--
-- Name: quotation_documents id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quotation_documents ALTER COLUMN id SET DEFAULT nextval('public.quotation_documents_id_seq'::regclass);


--
-- Name: quotation_items id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quotation_items ALTER COLUMN id SET DEFAULT nextval('public.quotation_items_id_seq'::regclass);


--
-- Name: quotations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quotations ALTER COLUMN id SET DEFAULT nextval('public.quotations_id_seq'::regclass);


--
-- Name: report_folios id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.report_folios ALTER COLUMN id SET DEFAULT nextval('public.report_folios_id_seq'::regclass);


--
-- Name: requests id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.requests ALTER COLUMN id SET DEFAULT nextval('public.requests_id_seq'::regclass);


--
-- Name: roles id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles ALTER COLUMN id SET DEFAULT nextval('public.roles_id_seq'::regclass);


--
-- Name: sales id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sales ALTER COLUMN id SET DEFAULT nextval('public.sales_id_seq'::regclass);


--
-- Name: system_tickets id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.system_tickets ALTER COLUMN id SET DEFAULT nextval('public.system_tickets_id_seq'::regclass);


--
-- Name: technical_sheets id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.technical_sheets ALTER COLUMN id SET DEFAULT nextval('public.technical_sheets_id_seq'::regclass);


--
-- Name: training_documents id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.training_documents ALTER COLUMN id SET DEFAULT nextval('public.training_documents_id_seq'::regclass);


--
-- Name: training_evaluations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.training_evaluations ALTER COLUMN id SET DEFAULT nextval('public.training_evaluations_id_seq'::regclass);


--
-- Name: training_participants id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.training_participants ALTER COLUMN id SET DEFAULT nextval('public.training_participants_id_seq'::regclass);


--
-- Name: trainings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trainings ALTER COLUMN id SET DEFAULT nextval('public.trainings_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Name: migrations PK_8c82d7f526340ab734260ea46be; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.migrations
    ADD CONSTRAINT "PK_8c82d7f526340ab734260ea46be" PRIMARY KEY (id);


--
-- Name: notifications PK_notifications; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT "PK_notifications" PRIMARY KEY (id);


--
-- Name: adjuntos_seguimiento adjuntos_seguimiento_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.adjuntos_seguimiento
    ADD CONSTRAINT adjuntos_seguimiento_pkey PRIMARY KEY (id);


--
-- Name: audit_logs audit_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.audit_logs
    ADD CONSTRAINT audit_logs_pkey PRIMARY KEY (id);


--
-- Name: backups backups_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.backups
    ADD CONSTRAINT backups_pkey PRIMARY KEY (id);


--
-- Name: calibrations calibrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.calibrations
    ADD CONSTRAINT calibrations_pkey PRIMARY KEY (id);


--
-- Name: client_documents client_documents_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.client_documents
    ADD CONSTRAINT client_documents_pkey PRIMARY KEY (id);


--
-- Name: clients clients_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.clients
    ADD CONSTRAINT clients_pkey PRIMARY KEY (id);


--
-- Name: clients clients_rfc_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.clients
    ADD CONSTRAINT clients_rfc_key UNIQUE (rfc);


--
-- Name: contracts contracts_contract_number_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.contracts
    ADD CONSTRAINT contracts_contract_number_key UNIQUE (contract_number);


--
-- Name: contracts contracts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.contracts
    ADD CONSTRAINT contracts_pkey PRIMARY KEY (id);


--
-- Name: credit_request_documents credit_request_documents_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.credit_request_documents
    ADD CONSTRAINT credit_request_documents_pkey PRIMARY KEY (credit_request_id, document_id);


--
-- Name: credit_requests credit_requests_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.credit_requests
    ADD CONSTRAINT credit_requests_pkey PRIMARY KEY (id);


--
-- Name: credit_requests credit_requests_request_number_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.credit_requests
    ADD CONSTRAINT credit_requests_request_number_key UNIQUE (request_number);


--
-- Name: customers customers_no_cliente_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customers
    ADD CONSTRAINT customers_no_cliente_key UNIQUE (no_cliente);


--
-- Name: customers customers_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customers
    ADD CONSTRAINT customers_pkey PRIMARY KEY (id);


--
-- Name: departments departments_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.departments
    ADD CONSTRAINT departments_name_key UNIQUE (name);


--
-- Name: departments departments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.departments
    ADD CONSTRAINT departments_pkey PRIMARY KEY (id);


--
-- Name: documents documents_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_pkey PRIMARY KEY (id);


--
-- Name: employee_files employee_files_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.employee_files
    ADD CONSTRAINT employee_files_pkey PRIMARY KEY (id);


--
-- Name: equipment_receipts equipment_receipts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.equipment_receipts
    ADD CONSTRAINT equipment_receipts_pkey PRIMARY KEY (id);


--
-- Name: equipment_receipts equipment_receipts_receipt_number_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.equipment_receipts
    ADD CONSTRAINT equipment_receipts_receipt_number_key UNIQUE (receipt_number);


--
-- Name: event_attendees event_attendees_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.event_attendees
    ADD CONSTRAINT event_attendees_pkey PRIMARY KEY (id);


--
-- Name: event_documents event_documents_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.event_documents
    ADD CONSTRAINT event_documents_pkey PRIMARY KEY (id);


--
-- Name: events events_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.events
    ADD CONSTRAINT events_pkey PRIMARY KEY (id);


--
-- Name: fiscal_numbers fiscal_numbers_customer_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fiscal_numbers
    ADD CONSTRAINT fiscal_numbers_customer_id_key UNIQUE (customer_id);


--
-- Name: fiscal_numbers fiscal_numbers_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fiscal_numbers
    ADD CONSTRAINT fiscal_numbers_pkey PRIMARY KEY (id);


--
-- Name: fiscal_numbers fiscal_numbers_rfc_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fiscal_numbers
    ADD CONSTRAINT fiscal_numbers_rfc_key UNIQUE (rfc);


--
-- Name: inventory_movements inventory_movements_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inventory_movements
    ADD CONSTRAINT inventory_movements_pkey PRIMARY KEY (id);


--
-- Name: inventory inventory_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inventory
    ADD CONSTRAINT inventory_pkey PRIMARY KEY (id);


--
-- Name: invoice_documents invoice_documents_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoice_documents
    ADD CONSTRAINT invoice_documents_pkey PRIMARY KEY (invoice_id, document_id);


--
-- Name: invoices invoices_invoice_number_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_invoice_number_key UNIQUE (invoice_number);


--
-- Name: invoices invoices_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_pkey PRIMARY KEY (id);


--
-- Name: links_questionpro links_questionpro_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.links_questionpro
    ADD CONSTRAINT links_questionpro_pkey PRIMARY KEY (id);


--
-- Name: loans loans_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.loans
    ADD CONSTRAINT loans_pkey PRIMARY KEY (id);


--
-- Name: logistics_events logistics_events_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.logistics_events
    ADD CONSTRAINT logistics_events_pkey PRIMARY KEY (id);


--
-- Name: marketing_campaigns marketing_campaigns_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.marketing_campaigns
    ADD CONSTRAINT marketing_campaigns_pkey PRIMARY KEY (id);


--
-- Name: password_history password_history_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.password_history
    ADD CONSTRAINT password_history_pkey PRIMARY KEY (id);


--
-- Name: permissions permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.permissions
    ADD CONSTRAINT permissions_pkey PRIMARY KEY (id);


--
-- Name: personal_follow_ups personal_follow_ups_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.personal_follow_ups
    ADD CONSTRAINT personal_follow_ups_pkey PRIMARY KEY (id);


--
-- Name: plant_services plant_services_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.plant_services
    ADD CONSTRAINT plant_services_pkey PRIMARY KEY (id);


--
-- Name: plant_services plant_services_service_number_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.plant_services
    ADD CONSTRAINT plant_services_service_number_key UNIQUE (service_number);


--
-- Name: procedimientos_calidad procedimientos_calidad_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.procedimientos_calidad
    ADD CONSTRAINT procedimientos_calidad_pkey PRIMARY KEY (id);


--
-- Name: procedures procedures_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.procedures
    ADD CONSTRAINT procedures_pkey PRIMARY KEY (id);


--
-- Name: procedures procedures_procedure_number_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.procedures
    ADD CONSTRAINT procedures_procedure_number_key UNIQUE (procedure_number);


--
-- Name: products products_codigo_item_unique; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.products
    ADD CONSTRAINT products_codigo_item_unique UNIQUE (codigo_item);


--
-- Name: products products_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.products
    ADD CONSTRAINT products_pkey PRIMARY KEY (id);


--
-- Name: products products_sku_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.products
    ADD CONSTRAINT products_sku_key UNIQUE (codigo_item);


--
-- Name: project_documents project_documents_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.project_documents
    ADD CONSTRAINT project_documents_pkey PRIMARY KEY (id);


--
-- Name: project_stages project_stages_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.project_stages
    ADD CONSTRAINT project_stages_pkey PRIMARY KEY (id);


--
-- Name: projects projects_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.projects
    ADD CONSTRAINT projects_name_key UNIQUE (name);


--
-- Name: projects projects_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.projects
    ADD CONSTRAINT projects_pkey PRIMARY KEY (id);


--
-- Name: purchase_requests purchase_requests_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.purchase_requests
    ADD CONSTRAINT purchase_requests_pkey PRIMARY KEY (id);


--
-- Name: quality_issues quality_issues_issue_number_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quality_issues
    ADD CONSTRAINT quality_issues_issue_number_key UNIQUE (issue_number);


--
-- Name: quality_issues quality_issues_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quality_issues
    ADD CONSTRAINT quality_issues_pkey PRIMARY KEY (id);


--
-- Name: question_pro_links question_pro_links_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.question_pro_links
    ADD CONSTRAINT question_pro_links_pkey PRIMARY KEY (id);


--
-- Name: quotation_documents quotation_documents_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quotation_documents
    ADD CONSTRAINT quotation_documents_pkey PRIMARY KEY (id);


--
-- Name: quotation_items quotation_items_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quotation_items
    ADD CONSTRAINT quotation_items_pkey PRIMARY KEY (id);


--
-- Name: quotations quotations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quotations
    ADD CONSTRAINT quotations_pkey PRIMARY KEY (id);


--
-- Name: quotations quotations_quotation_number_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quotations
    ADD CONSTRAINT quotations_quotation_number_key UNIQUE (quotation_number);


--
-- Name: report_folios report_folios_folio_number_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.report_folios
    ADD CONSTRAINT report_folios_folio_number_key UNIQUE (folio_number);


--
-- Name: report_folios report_folios_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.report_folios
    ADD CONSTRAINT report_folios_pkey PRIMARY KEY (id);


--
-- Name: requests requests_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.requests
    ADD CONSTRAINT requests_pkey PRIMARY KEY (id);


--
-- Name: role_permissions role_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.role_permissions
    ADD CONSTRAINT role_permissions_pkey PRIMARY KEY (role_id, permission_id);


--
-- Name: roles roles_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_name_key UNIQUE (name);


--
-- Name: roles roles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_pkey PRIMARY KEY (id);


--
-- Name: sales sales_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sales
    ADD CONSTRAINT sales_pkey PRIMARY KEY (id);


--
-- Name: seguimientos_calidad seguimientos_calidad_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.seguimientos_calidad
    ADD CONSTRAINT seguimientos_calidad_pkey PRIMARY KEY (id);


--
-- Name: system_tickets system_tickets_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.system_tickets
    ADD CONSTRAINT system_tickets_pkey PRIMARY KEY (id);


--
-- Name: system_tickets system_tickets_ticket_number_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.system_tickets
    ADD CONSTRAINT system_tickets_ticket_number_key UNIQUE (ticket_number);


--
-- Name: technical_sheets technical_sheets_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.technical_sheets
    ADD CONSTRAINT technical_sheets_pkey PRIMARY KEY (id);


--
-- Name: training_documents training_documents_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.training_documents
    ADD CONSTRAINT training_documents_pkey PRIMARY KEY (id);


--
-- Name: training_evaluations training_evaluations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.training_evaluations
    ADD CONSTRAINT training_evaluations_pkey PRIMARY KEY (id);


--
-- Name: training_participants training_participants_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.training_participants
    ADD CONSTRAINT training_participants_pkey PRIMARY KEY (id);


--
-- Name: trainings trainings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trainings
    ADD CONSTRAINT trainings_pkey PRIMARY KEY (id);


--
-- Name: user_departments user_departments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_departments
    ADD CONSTRAINT user_departments_pkey PRIMARY KEY (user_id, department_id);


--
-- Name: user_roles user_roles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_pkey PRIMARY KEY (user_id, role_id);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: uso_links_questionpro uso_links_questionpro_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.uso_links_questionpro
    ADD CONSTRAINT uso_links_questionpro_pkey PRIMARY KEY (id);


--
-- Name: IDX_audit_logs_user_created; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_audit_logs_user_created" ON public.audit_logs USING btree (user_id, created_at DESC);


--
-- Name: IDX_notifications_role_status_created; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_notifications_role_status_created" ON public.notifications USING btree (role_id, status, created_at);


--
-- Name: IDX_notifications_type_created; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_notifications_type_created" ON public.notifications USING btree (type, created_at);


--
-- Name: IDX_notifications_user_status_created; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_notifications_user_status_created" ON public.notifications USING btree (user_id, status, created_at);


--
-- Name: IDX_permissions_module; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_permissions_module" ON public.permissions USING btree (module);


--
-- Name: IDX_role_permissions_permission_role; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_role_permissions_permission_role" ON public.role_permissions USING btree (permission_id, role_id);


--
-- Name: IDX_user_roles_role_user; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_user_roles_role_user" ON public.user_roles USING btree (role_id, user_id);


--
-- Name: idx_adjuntos_seguimiento_seguimiento_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_adjuntos_seguimiento_seguimiento_id ON public.adjuntos_seguimiento USING btree (seguimiento_id);


--
-- Name: idx_adjuntos_seguimiento_subido_por; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_adjuntos_seguimiento_subido_por ON public.adjuntos_seguimiento USING btree (subido_por);


--
-- Name: idx_audit_logs_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_audit_logs_created_at ON public.audit_logs USING btree (created_at);


--
-- Name: idx_audit_logs_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_audit_logs_user_id ON public.audit_logs USING btree (user_id);


--
-- Name: idx_backups_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_backups_created_at ON public.backups USING btree (created_at);


--
-- Name: idx_backups_expires_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_backups_expires_at ON public.backups USING btree (expires_at);


--
-- Name: idx_backups_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_backups_status ON public.backups USING btree (status);


--
-- Name: idx_backups_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_backups_type ON public.backups USING btree (type);


--
-- Name: idx_calibrations_equipment_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_calibrations_equipment_name ON public.calibrations USING btree (equipment_name);


--
-- Name: idx_calibrations_next_calibration_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_calibrations_next_calibration_date ON public.calibrations USING btree (next_calibration_date);


--
-- Name: idx_client_documents_client_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_client_documents_client_id ON public.client_documents USING btree (client_id);


--
-- Name: idx_client_documents_document_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_client_documents_document_id ON public.client_documents USING btree (document_id);


--
-- Name: idx_clients_assigned_salesperson; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_clients_assigned_salesperson ON public.clients USING btree (assigned_salesperson_id);


--
-- Name: idx_clients_client_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_clients_client_type ON public.clients USING btree (client_type);


--
-- Name: idx_clients_commercial_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_clients_commercial_name ON public.clients USING btree (commercial_name);


--
-- Name: idx_clients_rfc; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_clients_rfc ON public.clients USING btree (rfc);


--
-- Name: idx_clients_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_clients_status ON public.clients USING btree (status);


--
-- Name: idx_customers_no_cliente; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_customers_no_cliente ON public.customers USING btree (no_cliente);


--
-- Name: idx_customers_razon_social; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_customers_razon_social ON public.customers USING btree (razon_social);


--
-- Name: idx_departments_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_departments_name ON public.departments USING btree (name);


--
-- Name: idx_events_department_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_events_department_id ON public.events USING btree (department_id);


--
-- Name: idx_events_start_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_events_start_date ON public.events USING btree (start_date);


--
-- Name: idx_inventory_location; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_inventory_location ON public.inventory USING btree (location);


--
-- Name: idx_inventory_product_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_inventory_product_id ON public.inventory USING btree (product_id);


--
-- Name: idx_invoices_customer_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_invoices_customer_id ON public.invoices USING btree (customer_id);


--
-- Name: idx_invoices_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_invoices_status ON public.invoices USING btree (status);


--
-- Name: idx_links_questionpro_creado_por; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_links_questionpro_creado_por ON public.links_questionpro USING btree (creado_por);


--
-- Name: idx_password_history_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_password_history_created_at ON public.password_history USING btree (created_at);


--
-- Name: idx_password_history_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_password_history_user_id ON public.password_history USING btree (user_id);


--
-- Name: idx_permissions_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_permissions_name ON public.permissions USING btree (name);


--
-- Name: idx_procedimientos_calidad_area; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_procedimientos_calidad_area ON public.procedimientos_calidad USING btree (area);


--
-- Name: idx_procedimientos_calidad_creado_por; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_procedimientos_calidad_creado_por ON public.procedimientos_calidad USING btree (creado_por);


--
-- Name: idx_procedimientos_calidad_tipo; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_procedimientos_calidad_tipo ON public.procedimientos_calidad USING btree (tipo);


--
-- Name: idx_products_codigo_item; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_products_codigo_item ON public.products USING btree (codigo_item);


--
-- Name: idx_products_estado; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_products_estado ON public.products USING btree (estado);


--
-- Name: idx_products_marca; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_products_marca ON public.products USING btree (marca);


--
-- Name: idx_products_modelo; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_products_modelo ON public.products USING btree (modelo);


--
-- Name: idx_products_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_products_name ON public.products USING btree (nombre);


--
-- Name: idx_products_nombre; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_products_nombre ON public.products USING btree (nombre);


--
-- Name: idx_products_sku; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_products_sku ON public.products USING btree (codigo_item);


--
-- Name: idx_products_stock_disponible; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_products_stock_disponible ON public.products USING btree (stock_disponible);


--
-- Name: idx_products_stock_minimo; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_products_stock_minimo ON public.products USING btree (stock_minimo);


--
-- Name: idx_products_tipo_almacen; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_products_tipo_almacen ON public.products USING btree (tipo_almacen);


--
-- Name: idx_products_ubicacion; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_products_ubicacion ON public.products USING btree (ubicacion);


--
-- Name: idx_projects_customer_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_projects_customer_id ON public.projects USING btree (customer_id);


--
-- Name: idx_projects_manager_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_projects_manager_id ON public.projects USING btree (manager_id);


--
-- Name: idx_projects_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_projects_status ON public.projects USING btree (status);


--
-- Name: idx_quality_issues_severity; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_quality_issues_severity ON public.quality_issues USING btree (severity);


--
-- Name: idx_quality_issues_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_quality_issues_status ON public.quality_issues USING btree (status);


--
-- Name: idx_quotations_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_quotations_created_at ON public.quotations USING btree (created_at);


--
-- Name: idx_quotations_customer_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_quotations_customer_id ON public.quotations USING btree (customer_id);


--
-- Name: idx_quotations_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_quotations_status ON public.quotations USING btree (status);


--
-- Name: idx_roles_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_roles_name ON public.roles USING btree (name);


--
-- Name: idx_sales_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_sales_created_at ON public.sales USING btree (created_at);


--
-- Name: idx_sales_customer_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_sales_customer_id ON public.sales USING btree (customer_id);


--
-- Name: idx_sales_registered_by; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_sales_registered_by ON public.sales USING btree (registered_by);


--
-- Name: idx_seguimientos_calidad_creado_por; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_seguimientos_calidad_creado_por ON public.seguimientos_calidad USING btree (creado_por);


--
-- Name: idx_seguimientos_calidad_estado; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_seguimientos_calidad_estado ON public.seguimientos_calidad USING btree (estado);


--
-- Name: idx_seguimientos_calidad_fecha_creacion; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_seguimientos_calidad_fecha_creacion ON public.seguimientos_calidad USING btree (fecha_creacion);


--
-- Name: idx_seguimientos_calidad_tipo; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_seguimientos_calidad_tipo ON public.seguimientos_calidad USING btree (tipo);


--
-- Name: idx_system_tickets_assigned_to; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_tickets_assigned_to ON public.system_tickets USING btree (assigned_to);


--
-- Name: idx_system_tickets_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_tickets_status ON public.system_tickets USING btree (status);


--
-- Name: idx_trainings_department_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trainings_department_id ON public.trainings USING btree (department_id);


--
-- Name: idx_trainings_start_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trainings_start_date ON public.trainings USING btree (start_date);


--
-- Name: idx_users_email; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_users_email ON public.users USING btree (email);


--
-- Name: idx_users_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_users_name ON public.users USING btree (name);


--
-- Name: idx_users_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_users_status ON public.users USING btree (status);


--
-- Name: idx_uso_links_questionpro_fecha; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_uso_links_questionpro_fecha ON public.uso_links_questionpro USING btree (fecha);


--
-- Name: idx_uso_links_questionpro_link_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_uso_links_questionpro_link_id ON public.uso_links_questionpro USING btree (link_id);


--
-- Name: idx_uso_links_questionpro_usuario_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_uso_links_questionpro_usuario_id ON public.uso_links_questionpro USING btree (usuario_id);


--
-- Name: backups update_backups_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_backups_updated_at BEFORE UPDATE ON public.backups FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: clients update_clients_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON public.clients FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: departments update_departments_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_departments_updated_at BEFORE UPDATE ON public.departments FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: permissions update_permissions_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_permissions_updated_at BEFORE UPDATE ON public.permissions FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: roles update_roles_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON public.roles FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: users update_users_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: audit_logs FK_audit_logs_creator; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.audit_logs
    ADD CONSTRAINT "FK_audit_logs_creator" FOREIGN KEY (created_by) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: audit_logs FK_audit_logs_updater; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.audit_logs
    ADD CONSTRAINT "FK_audit_logs_updater" FOREIGN KEY (updated_by) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: notifications FK_notifications_creator; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT "FK_notifications_creator" FOREIGN KEY (created_by) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: notifications FK_notifications_role; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT "FK_notifications_role" FOREIGN KEY (role_id) REFERENCES public.roles(id) ON DELETE CASCADE;


--
-- Name: notifications FK_notifications_updater; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT "FK_notifications_updater" FOREIGN KEY (updated_by) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: notifications FK_notifications_user; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT "FK_notifications_user" FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: adjuntos_seguimiento adjuntos_seguimiento_seguimiento_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.adjuntos_seguimiento
    ADD CONSTRAINT adjuntos_seguimiento_seguimiento_id_fkey FOREIGN KEY (seguimiento_id) REFERENCES public.seguimientos_calidad(id) ON DELETE CASCADE;


--
-- Name: adjuntos_seguimiento adjuntos_seguimiento_subido_por_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.adjuntos_seguimiento
    ADD CONSTRAINT adjuntos_seguimiento_subido_por_fkey FOREIGN KEY (subido_por) REFERENCES auth.users(id);


--
-- Name: calibrations calibrations_department_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.calibrations
    ADD CONSTRAINT calibrations_department_id_fkey FOREIGN KEY (department_id) REFERENCES public.departments(id);


--
-- Name: client_documents client_documents_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.client_documents
    ADD CONSTRAINT client_documents_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id) ON DELETE CASCADE;


--
-- Name: client_documents client_documents_document_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.client_documents
    ADD CONSTRAINT client_documents_document_id_fkey FOREIGN KEY (document_id) REFERENCES public.documents(id) ON DELETE CASCADE;


--
-- Name: clients clients_assigned_salesperson_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.clients
    ADD CONSTRAINT clients_assigned_salesperson_id_fkey FOREIGN KEY (assigned_salesperson_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: clients clients_created_by_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.clients
    ADD CONSTRAINT clients_created_by_user_id_fkey FOREIGN KEY (created_by_user_id) REFERENCES public.users(id) ON DELETE RESTRICT;


--
-- Name: contracts contracts_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.contracts
    ADD CONSTRAINT contracts_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: contracts contracts_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.contracts
    ADD CONSTRAINT contracts_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customers(id);


--
-- Name: contracts contracts_project_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.contracts
    ADD CONSTRAINT contracts_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);


--
-- Name: contracts contracts_updated_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.contracts
    ADD CONSTRAINT contracts_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.users(id);


--
-- Name: credit_request_documents credit_request_documents_credit_request_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.credit_request_documents
    ADD CONSTRAINT credit_request_documents_credit_request_id_fkey FOREIGN KEY (credit_request_id) REFERENCES public.credit_requests(id) ON DELETE CASCADE;


--
-- Name: credit_request_documents credit_request_documents_document_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.credit_request_documents
    ADD CONSTRAINT credit_request_documents_document_id_fkey FOREIGN KEY (document_id) REFERENCES public.documents(id) ON DELETE CASCADE;


--
-- Name: credit_requests credit_requests_approved_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.credit_requests
    ADD CONSTRAINT credit_requests_approved_by_fkey FOREIGN KEY (approved_by) REFERENCES public.users(id);


--
-- Name: credit_requests credit_requests_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.credit_requests
    ADD CONSTRAINT credit_requests_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customers(id);


--
-- Name: credit_requests credit_requests_requester_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.credit_requests
    ADD CONSTRAINT credit_requests_requester_id_fkey FOREIGN KEY (requester_id) REFERENCES public.users(id);


--
-- Name: documents documents_department_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_department_id_fkey FOREIGN KEY (department_id) REFERENCES public.departments(id);


--
-- Name: documents documents_uploaded_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_uploaded_by_fkey FOREIGN KEY (uploaded_by) REFERENCES public.users(id);


--
-- Name: employee_files employee_files_document_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.employee_files
    ADD CONSTRAINT employee_files_document_id_fkey FOREIGN KEY (document_id) REFERENCES public.documents(id);


--
-- Name: employee_files employee_files_employee_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.employee_files
    ADD CONSTRAINT employee_files_employee_id_fkey FOREIGN KEY (employee_id) REFERENCES public.users(id);


--
-- Name: equipment_receipts equipment_receipts_received_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.equipment_receipts
    ADD CONSTRAINT equipment_receipts_received_by_fkey FOREIGN KEY (received_by) REFERENCES public.users(id);


--
-- Name: event_attendees event_attendees_event_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.event_attendees
    ADD CONSTRAINT event_attendees_event_id_fkey FOREIGN KEY (event_id) REFERENCES public.events(id) ON DELETE CASCADE;


--
-- Name: event_attendees event_attendees_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.event_attendees
    ADD CONSTRAINT event_attendees_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: event_documents event_documents_document_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.event_documents
    ADD CONSTRAINT event_documents_document_id_fkey FOREIGN KEY (document_id) REFERENCES public.documents(id) ON DELETE CASCADE;


--
-- Name: event_documents event_documents_event_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.event_documents
    ADD CONSTRAINT event_documents_event_id_fkey FOREIGN KEY (event_id) REFERENCES public.events(id) ON DELETE CASCADE;


--
-- Name: events events_department_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.events
    ADD CONSTRAINT events_department_id_fkey FOREIGN KEY (department_id) REFERENCES public.departments(id);


--
-- Name: events events_organizer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.events
    ADD CONSTRAINT events_organizer_id_fkey FOREIGN KEY (organizer_id) REFERENCES public.users(id);


--
-- Name: fiscal_numbers fiscal_numbers_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fiscal_numbers
    ADD CONSTRAINT fiscal_numbers_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customers(id) ON DELETE CASCADE;


--
-- Name: role_permissions fk_role_permissions_permission_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.role_permissions
    ADD CONSTRAINT fk_role_permissions_permission_id FOREIGN KEY (permission_id) REFERENCES public.permissions(id) ON DELETE CASCADE;


--
-- Name: role_permissions fk_role_permissions_role_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.role_permissions
    ADD CONSTRAINT fk_role_permissions_role_id FOREIGN KEY (role_id) REFERENCES public.roles(id) ON DELETE CASCADE;


--
-- Name: user_departments fk_user_departments_department; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_departments
    ADD CONSTRAINT fk_user_departments_department FOREIGN KEY (department_id) REFERENCES public.departments(id) ON DELETE CASCADE;


--
-- Name: user_departments fk_user_departments_department_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_departments
    ADD CONSTRAINT fk_user_departments_department_id FOREIGN KEY (department_id) REFERENCES public.departments(id) ON DELETE CASCADE;


--
-- Name: user_departments fk_user_departments_user; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_departments
    ADD CONSTRAINT fk_user_departments_user FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: user_departments fk_user_departments_user_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_departments
    ADD CONSTRAINT fk_user_departments_user_id FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: user_roles fk_user_roles_role; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT fk_user_roles_role FOREIGN KEY (role_id) REFERENCES public.roles(id) ON DELETE CASCADE;


--
-- Name: user_roles fk_user_roles_role_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT fk_user_roles_role_id FOREIGN KEY (role_id) REFERENCES public.roles(id) ON DELETE CASCADE;


--
-- Name: user_roles fk_user_roles_user; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT fk_user_roles_user FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: user_roles fk_user_roles_user_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT fk_user_roles_user_id FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: inventory_movements inventory_movements_product_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inventory_movements
    ADD CONSTRAINT inventory_movements_product_id_fkey FOREIGN KEY (product_id) REFERENCES public.products(id);


--
-- Name: inventory_movements inventory_movements_sale_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inventory_movements
    ADD CONSTRAINT inventory_movements_sale_id_fkey FOREIGN KEY (sale_id) REFERENCES public.sales(id);


--
-- Name: inventory_movements inventory_movements_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inventory_movements
    ADD CONSTRAINT inventory_movements_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: inventory inventory_product_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inventory
    ADD CONSTRAINT inventory_product_id_fkey FOREIGN KEY (product_id) REFERENCES public.products(id);


--
-- Name: invoice_documents invoice_documents_document_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoice_documents
    ADD CONSTRAINT invoice_documents_document_id_fkey FOREIGN KEY (document_id) REFERENCES public.documents(id) ON DELETE CASCADE;


--
-- Name: invoice_documents invoice_documents_invoice_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoice_documents
    ADD CONSTRAINT invoice_documents_invoice_id_fkey FOREIGN KEY (invoice_id) REFERENCES public.invoices(id) ON DELETE CASCADE;


--
-- Name: invoices invoices_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: invoices invoices_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customers(id);


--
-- Name: invoices invoices_quotation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_quotation_id_fkey FOREIGN KEY (quotation_id) REFERENCES public.quotations(id);


--
-- Name: invoices invoices_updated_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.users(id);


--
-- Name: links_questionpro links_questionpro_creado_por_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.links_questionpro
    ADD CONSTRAINT links_questionpro_creado_por_fkey FOREIGN KEY (creado_por) REFERENCES auth.users(id);


--
-- Name: loans loans_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.loans
    ADD CONSTRAINT loans_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customers(id);


--
-- Name: loans loans_product_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.loans
    ADD CONSTRAINT loans_product_id_fkey FOREIGN KEY (product_id) REFERENCES public.products(id);


--
-- Name: loans loans_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.loans
    ADD CONSTRAINT loans_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: logistics_events logistics_events_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.logistics_events
    ADD CONSTRAINT logistics_events_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customers(id);


--
-- Name: logistics_events logistics_events_responsible_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.logistics_events
    ADD CONSTRAINT logistics_events_responsible_id_fkey FOREIGN KEY (responsible_id) REFERENCES public.users(id);


--
-- Name: marketing_campaigns marketing_campaigns_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.marketing_campaigns
    ADD CONSTRAINT marketing_campaigns_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: marketing_campaigns marketing_campaigns_department_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.marketing_campaigns
    ADD CONSTRAINT marketing_campaigns_department_id_fkey FOREIGN KEY (department_id) REFERENCES public.departments(id);


--
-- Name: password_history password_history_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.password_history
    ADD CONSTRAINT password_history_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: password_history password_history_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.password_history
    ADD CONSTRAINT password_history_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: personal_follow_ups personal_follow_ups_employee_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.personal_follow_ups
    ADD CONSTRAINT personal_follow_ups_employee_id_fkey FOREIGN KEY (employee_id) REFERENCES public.users(id);


--
-- Name: personal_follow_ups personal_follow_ups_responsible_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.personal_follow_ups
    ADD CONSTRAINT personal_follow_ups_responsible_id_fkey FOREIGN KEY (responsible_id) REFERENCES public.users(id);


--
-- Name: plant_services plant_services_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.plant_services
    ADD CONSTRAINT plant_services_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customers(id);


--
-- Name: plant_services plant_services_department_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.plant_services
    ADD CONSTRAINT plant_services_department_id_fkey FOREIGN KEY (department_id) REFERENCES public.departments(id);


--
-- Name: plant_services plant_services_technician_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.plant_services
    ADD CONSTRAINT plant_services_technician_id_fkey FOREIGN KEY (technician_id) REFERENCES public.users(id);


--
-- Name: procedimientos_calidad procedimientos_calidad_creado_por_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.procedimientos_calidad
    ADD CONSTRAINT procedimientos_calidad_creado_por_fkey FOREIGN KEY (creado_por) REFERENCES auth.users(id);


--
-- Name: procedures procedures_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.procedures
    ADD CONSTRAINT procedures_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: procedures procedures_department_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.procedures
    ADD CONSTRAINT procedures_department_id_fkey FOREIGN KEY (department_id) REFERENCES public.departments(id);


--
-- Name: project_documents project_documents_document_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.project_documents
    ADD CONSTRAINT project_documents_document_id_fkey FOREIGN KEY (document_id) REFERENCES public.documents(id) ON DELETE CASCADE;


--
-- Name: project_documents project_documents_project_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.project_documents
    ADD CONSTRAINT project_documents_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE;


--
-- Name: project_stages project_stages_project_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.project_stages
    ADD CONSTRAINT project_stages_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE;


--
-- Name: projects projects_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.projects
    ADD CONSTRAINT projects_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customers(id);


--
-- Name: purchase_requests purchase_requests_product_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.purchase_requests
    ADD CONSTRAINT purchase_requests_product_id_fkey FOREIGN KEY (product_id) REFERENCES public.products(id);


--
-- Name: purchase_requests purchase_requests_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.purchase_requests
    ADD CONSTRAINT purchase_requests_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: quality_issues quality_issues_assigned_to_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quality_issues
    ADD CONSTRAINT quality_issues_assigned_to_fkey FOREIGN KEY (assigned_to) REFERENCES public.users(id);


--
-- Name: quality_issues quality_issues_cerrado_por_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quality_issues
    ADD CONSTRAINT quality_issues_cerrado_por_fkey FOREIGN KEY (cerrado_por) REFERENCES auth.users(id);


--
-- Name: quality_issues quality_issues_department_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quality_issues
    ADD CONSTRAINT quality_issues_department_id_fkey FOREIGN KEY (department_id) REFERENCES public.departments(id);


--
-- Name: quality_issues quality_issues_reporter_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quality_issues
    ADD CONSTRAINT quality_issues_reporter_id_fkey FOREIGN KEY (reporter_id) REFERENCES public.users(id);


--
-- Name: question_pro_links question_pro_links_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.question_pro_links
    ADD CONSTRAINT question_pro_links_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: question_pro_links question_pro_links_department_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.question_pro_links
    ADD CONSTRAINT question_pro_links_department_id_fkey FOREIGN KEY (department_id) REFERENCES public.departments(id);


--
-- Name: quotation_documents quotation_documents_document_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quotation_documents
    ADD CONSTRAINT quotation_documents_document_id_fkey FOREIGN KEY (document_id) REFERENCES public.documents(id) ON DELETE CASCADE;


--
-- Name: quotation_documents quotation_documents_quotation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quotation_documents
    ADD CONSTRAINT quotation_documents_quotation_id_fkey FOREIGN KEY (quotation_id) REFERENCES public.quotations(id) ON DELETE CASCADE;


--
-- Name: quotation_items quotation_items_product_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quotation_items
    ADD CONSTRAINT quotation_items_product_id_fkey FOREIGN KEY (product_id) REFERENCES public.products(id);


--
-- Name: quotation_items quotation_items_quotation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quotation_items
    ADD CONSTRAINT quotation_items_quotation_id_fkey FOREIGN KEY (quotation_id) REFERENCES public.quotations(id) ON DELETE CASCADE;


--
-- Name: quotations quotations_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quotations
    ADD CONSTRAINT quotations_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customers(id);


--
-- Name: quotations quotations_project_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quotations
    ADD CONSTRAINT quotations_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);


--
-- Name: report_folios report_folios_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.report_folios
    ADD CONSTRAINT report_folios_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: report_folios report_folios_department_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.report_folios
    ADD CONSTRAINT report_folios_department_id_fkey FOREIGN KEY (department_id) REFERENCES public.departments(id);


--
-- Name: requests requests_assigned_to_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.requests
    ADD CONSTRAINT requests_assigned_to_fkey FOREIGN KEY (assigned_to) REFERENCES public.users(id);


--
-- Name: requests requests_department_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.requests
    ADD CONSTRAINT requests_department_id_fkey FOREIGN KEY (department_id) REFERENCES public.departments(id);


--
-- Name: role_permissions role_permissions_permission_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.role_permissions
    ADD CONSTRAINT role_permissions_permission_id_fkey FOREIGN KEY (permission_id) REFERENCES public.permissions(id) ON DELETE CASCADE;


--
-- Name: role_permissions role_permissions_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.role_permissions
    ADD CONSTRAINT role_permissions_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id) ON DELETE CASCADE;


--
-- Name: sales sales_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sales
    ADD CONSTRAINT sales_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customers(id);


--
-- Name: sales sales_registered_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sales
    ADD CONSTRAINT sales_registered_by_fkey FOREIGN KEY (registered_by) REFERENCES public.users(id);


--
-- Name: sales sales_responsable_ingeniero_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sales
    ADD CONSTRAINT sales_responsable_ingeniero_fkey FOREIGN KEY (responsable_ingeniero) REFERENCES public.users(id);


--
-- Name: seguimientos_calidad seguimientos_calidad_cerrado_por_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.seguimientos_calidad
    ADD CONSTRAINT seguimientos_calidad_cerrado_por_fkey FOREIGN KEY (cerrado_por) REFERENCES auth.users(id);


--
-- Name: seguimientos_calidad seguimientos_calidad_creado_por_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.seguimientos_calidad
    ADD CONSTRAINT seguimientos_calidad_creado_por_fkey FOREIGN KEY (creado_por) REFERENCES auth.users(id);


--
-- Name: system_tickets system_tickets_assigned_to_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.system_tickets
    ADD CONSTRAINT system_tickets_assigned_to_fkey FOREIGN KEY (assigned_to) REFERENCES public.users(id);


--
-- Name: system_tickets system_tickets_department_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.system_tickets
    ADD CONSTRAINT system_tickets_department_id_fkey FOREIGN KEY (department_id) REFERENCES public.departments(id);


--
-- Name: system_tickets system_tickets_reporter_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.system_tickets
    ADD CONSTRAINT system_tickets_reporter_id_fkey FOREIGN KEY (reporter_id) REFERENCES public.users(id);


--
-- Name: technical_sheets technical_sheets_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.technical_sheets
    ADD CONSTRAINT technical_sheets_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: technical_sheets technical_sheets_department_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.technical_sheets
    ADD CONSTRAINT technical_sheets_department_id_fkey FOREIGN KEY (department_id) REFERENCES public.departments(id);


--
-- Name: technical_sheets technical_sheets_product_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.technical_sheets
    ADD CONSTRAINT technical_sheets_product_id_fkey FOREIGN KEY (product_id) REFERENCES public.products(id);


--
-- Name: training_documents training_documents_document_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.training_documents
    ADD CONSTRAINT training_documents_document_id_fkey FOREIGN KEY (document_id) REFERENCES public.documents(id) ON DELETE CASCADE;


--
-- Name: training_documents training_documents_training_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.training_documents
    ADD CONSTRAINT training_documents_training_id_fkey FOREIGN KEY (training_id) REFERENCES public.trainings(id) ON DELETE CASCADE;


--
-- Name: training_documents training_documents_uploaded_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.training_documents
    ADD CONSTRAINT training_documents_uploaded_by_fkey FOREIGN KEY (uploaded_by) REFERENCES public.users(id);


--
-- Name: training_evaluations training_evaluations_evaluator_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.training_evaluations
    ADD CONSTRAINT training_evaluations_evaluator_id_fkey FOREIGN KEY (evaluator_id) REFERENCES public.users(id);


--
-- Name: training_evaluations training_evaluations_participant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.training_evaluations
    ADD CONSTRAINT training_evaluations_participant_id_fkey FOREIGN KEY (participant_id) REFERENCES public.training_participants(id) ON DELETE CASCADE;


--
-- Name: training_evaluations training_evaluations_training_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.training_evaluations
    ADD CONSTRAINT training_evaluations_training_id_fkey FOREIGN KEY (training_id) REFERENCES public.trainings(id) ON DELETE CASCADE;


--
-- Name: training_participants training_participants_training_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.training_participants
    ADD CONSTRAINT training_participants_training_id_fkey FOREIGN KEY (training_id) REFERENCES public.trainings(id) ON DELETE CASCADE;


--
-- Name: training_participants training_participants_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.training_participants
    ADD CONSTRAINT training_participants_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: trainings trainings_coordinator_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trainings
    ADD CONSTRAINT trainings_coordinator_id_fkey FOREIGN KEY (coordinator_id) REFERENCES public.users(id);


--
-- Name: trainings trainings_department_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trainings
    ADD CONSTRAINT trainings_department_id_fkey FOREIGN KEY (department_id) REFERENCES public.departments(id);


--
-- Name: trainings trainings_instructor_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trainings
    ADD CONSTRAINT trainings_instructor_id_fkey FOREIGN KEY (instructor_id) REFERENCES public.users(id);


--
-- Name: uso_links_questionpro uso_links_questionpro_link_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.uso_links_questionpro
    ADD CONSTRAINT uso_links_questionpro_link_id_fkey FOREIGN KEY (link_id) REFERENCES public.links_questionpro(id) ON DELETE CASCADE;


--
-- Name: uso_links_questionpro uso_links_questionpro_usuario_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.uso_links_questionpro
    ADD CONSTRAINT uso_links_questionpro_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES auth.users(id);


--
-- PostgreSQL database dump complete
--


-- =============================
-- DATOS CON IDs REINICIADOS
-- =============================
-- Los IDs se reinician desde 1 manteniendo relaciones

-- Deshabilitar triggers temporalmente para inserción limpia
SET session_replication_role = replica;


-- Tabla: roles (21 registros)
-- Reiniciando IDs desde 1 para roles
INSERT INTO roles (id, name, created_at, updated_at)
SELECT 
  ROW_NUMBER() OVER (ORDER BY id), name, created_at, updated_at
FROM roles ORDER BY id;

-- Tabla: permissions (478 registros)
-- Reiniciando IDs desde 1 para permissions
INSERT INTO permissions (id, name, module, action, description, created_at, updated_at)
SELECT 
  ROW_NUMBER() OVER (ORDER BY id), name, module, action, description, created_at, updated_at
FROM permissions ORDER BY id;

-- Tabla: role_permissions (1062 registros)
--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.5 (Ubuntu 17.5-1.pgdg22.04+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: role_permissions; Type: TABLE DATA; Schema: public; Owner: -
--

INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 1, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 2, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 436, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 437, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 438, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 439, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 440, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 441, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 442, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 443, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 444, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 445, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 446, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 447, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 448, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 449, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 450, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 451, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 452, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 453, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 454, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 455, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 456, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 457, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 458, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 459, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 460, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 461, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 462, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 463, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 464, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 465, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 466, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 467, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 468, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 469, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 470, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 471, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 472, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 473, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 474, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 475, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 476, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 477, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 478, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 479, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 480, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 481, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 482, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 483, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 484, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 485, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 486, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 487, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 488, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 489, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 490, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 491, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 492, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 493, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 494, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 495, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 496, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 497, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 498, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 499, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 500, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 501, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 502, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 503, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 504, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 505, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 506, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 507, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 508, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 509, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 510, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 511, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 512, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 513, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 514, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 515, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 516, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 517, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 518, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 519, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 520, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 521, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 522, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 523, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 524, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 525, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 526, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 527, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 528, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 529, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 530, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 531, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 532, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 533, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 534, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 535, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 536, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 537, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 538, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 539, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 540, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 541, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 542, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 543, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 544, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 545, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 546, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 547, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 548, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 549, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 550, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 551, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 552, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 553, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 554, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 555, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 556, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 557, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 558, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 559, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 560, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 561, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 562, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 563, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 564, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 565, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 566, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 567, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 568, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 569, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 570, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 571, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 572, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 573, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 574, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 575, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 576, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 577, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 578, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 579, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 580, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 581, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 582, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 583, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 584, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 585, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 586, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 587, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 588, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 589, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 590, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 591, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 592, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 593, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 594, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 595, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 596, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 597, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 598, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 599, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 600, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 601, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 602, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 603, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 604, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 605, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 606, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 607, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 608, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 609, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 610, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 611, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 612, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 613, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 614, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 615, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 616, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 617, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 618, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 682, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 619, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 620, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 621, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 622, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 623, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 624, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 625, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 626, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 627, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 628, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 629, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 630, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 631, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 632, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 633, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 634, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 635, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 636, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 637, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 638, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 639, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 640, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 641, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 642, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 643, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 644, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 645, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 646, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 647, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 648, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 649, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 650, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 651, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 652, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 653, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 654, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 655, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 656, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 657, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 658, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 659, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 660, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 661, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 662, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 663, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 664, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 665, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 666, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 667, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 668, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 669, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 670, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 671, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 672, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 673, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 674, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 675, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 676, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 677, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 678, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 679, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 680, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 681, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 683, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 684, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 685, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 686, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 687, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 688, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 689, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 690, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 691, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 692, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 693, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 694, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 695, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 696, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 697, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 698, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 699, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 700, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 701, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 702, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 703, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 704, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 705, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 706, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 707, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 708, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 709, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 710, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 711, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 712, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 713, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 714, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 715, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 716, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 717, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 718, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 719, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 720, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 721, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 722, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 723, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 724, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 725, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 726, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 727, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 728, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 729, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 730, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 731, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 732, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 733, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 734, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 735, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 736, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 737, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 738, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 739, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 740, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 741, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 742, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 743, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 744, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 745, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 746, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 747, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 748, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 749, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 750, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 751, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 752, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 753, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 754, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 755, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 756, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 757, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 758, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 759, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 760, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 761, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 762, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 763, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 764, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 765, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 766, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 767, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 768, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 769, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 770, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 771, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 772, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 773, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 774, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 775, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 776, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 777, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 778, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 779, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 780, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 781, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 782, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 783, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 784, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 785, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 786, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 787, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 788, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 789, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 790, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 791, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 792, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 793, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 794, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 795, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 796, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 797, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 798, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 799, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 800, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 801, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 802, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 803, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 804, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 805, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 806, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 807, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 808, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 809, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 810, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 811, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 812, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 813, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 814, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 815, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 816, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 817, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 818, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 819, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 820, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 821, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 822, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 823, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 824, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 825, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 826, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 827, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 828, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 829, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 830, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 831, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 832, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 833, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 834, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 835, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 836, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 837, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 838, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 839, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 840, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 841, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 842, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 843, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 844, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 845, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 846, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 847, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 848, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 849, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 850, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 851, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 852, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 853, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 854, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 855, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 856, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 857, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 858, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 859, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 860, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 861, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 862, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 863, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 864, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 865, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 866, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 867, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 868, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 869, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 870, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 871, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 872, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 873, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 874, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 875, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 876, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 877, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 878, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 879, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 880, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 881, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 882, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 883, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 884, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 885, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 886, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 887, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 888, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 889, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 890, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 891, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 892, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 893, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 894, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 895, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 896, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 897, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 898, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 899, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 900, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 901, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 902, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 903, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 904, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 905, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 906, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 907, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 908, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 909, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 910, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (110, 911, '2025-07-04 19:17:50.708418+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 729, '2025-07-03 06:49:23.433683+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 724, '2025-07-03 06:49:23.500685+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 727, '2025-07-03 06:49:23.566421+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 725, '2025-07-03 06:49:23.631934+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 751, '2025-07-03 06:49:23.697693+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 754, '2025-07-03 06:49:23.762431+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 752, '2025-07-03 06:49:23.827155+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 753, '2025-07-03 06:49:23.892143+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 733, '2025-07-03 06:49:23.956425+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 731, '2025-07-03 06:49:24.021177+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 732, '2025-07-03 06:49:24.085438+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 734, '2025-07-03 06:49:24.14967+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 735, '2025-07-03 06:49:24.214436+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 739, '2025-07-03 06:49:24.279161+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 740, '2025-07-03 06:49:24.343904+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 736, '2025-07-03 06:49:24.408415+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 737, '2025-07-03 06:49:24.472935+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 761, '2025-07-03 06:49:24.537424+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 747, '2025-07-03 06:49:24.601884+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 764, '2025-07-03 06:49:24.666889+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 762, '2025-07-03 06:49:24.731389+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 763, '2025-07-03 06:49:24.795859+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 741, '2025-07-03 06:49:24.86014+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 742, '2025-07-03 06:49:24.925648+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 743, '2025-07-03 06:49:24.990153+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (33, 745, '2025-07-03 06:49:25.055149+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 444, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 459, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 468, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 461, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 462, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 440, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 443, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 456, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 455, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 452, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 437, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 449, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 442, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 465, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 466, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 2, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 458, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 467, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 451, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 1, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 469, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 436, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 445, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 448, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 453, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 454, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 439, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 438, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 447, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 446, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 457, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 460, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 464, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 441, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 450, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (11, 463, '2025-07-03 06:26:52.096734+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (35, 791, '2025-07-05 05:25:32.286654+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (35, 805, '2025-07-05 05:25:32.356202+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (35, 806, '2025-07-05 05:25:32.423441+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (35, 766, '2025-07-05 05:25:32.491406+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (35, 780, '2025-07-05 05:25:32.559429+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (35, 800, '2025-07-05 05:25:32.626442+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (35, 803, '2025-07-05 05:25:32.693948+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (35, 804, '2025-07-05 05:25:32.760706+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (35, 801, '2025-07-05 05:25:32.832459+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (35, 802, '2025-07-05 05:25:32.899155+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (35, 784, '2025-07-05 05:25:32.965927+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (35, 785, '2025-07-05 05:25:33.032447+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (35, 812, '2025-07-05 05:25:33.099695+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (35, 810, '2025-07-05 05:25:33.166686+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (35, 811, '2025-07-05 05:25:33.233439+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (35, 777, '2025-07-05 05:25:33.300169+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (35, 773, '2025-07-05 05:25:33.366699+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (35, 774, '2025-07-05 05:25:33.433183+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (35, 796, '2025-07-05 05:25:33.499692+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (35, 797, '2025-07-05 05:25:33.566442+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 485, '2025-07-11 04:51:52.454925+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 488, '2025-07-11 04:51:52.550107+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 489, '2025-07-11 04:51:52.62648+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 486, '2025-07-11 04:51:52.703127+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 487, '2025-07-11 04:51:52.779366+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 519, '2025-07-11 04:51:52.856246+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 515, '2025-07-11 04:51:52.936984+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 518, '2025-07-11 04:51:53.014247+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 516, '2025-07-11 04:51:53.092388+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 501, '2025-07-11 04:51:53.168605+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 500, '2025-07-11 04:51:53.245636+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 499, '2025-07-11 04:51:53.321892+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 502, '2025-07-11 04:51:53.397843+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 498, '2025-07-11 04:51:53.473577+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 495, '2025-07-11 04:51:53.549341+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 497, '2025-07-11 04:51:53.625355+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 496, '2025-07-11 04:51:53.702367+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 478, '2025-07-11 04:51:53.778366+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 481, '2025-07-11 04:51:53.85488+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 484, '2025-07-11 04:51:53.931634+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 483, '2025-07-11 04:51:54.008356+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 482, '2025-07-11 04:51:54.084385+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 479, '2025-07-11 04:51:54.173856+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 480, '2025-07-11 04:51:54.250143+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 510, '2025-07-11 04:51:54.326145+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 508, '2025-07-11 04:51:54.412387+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 511, '2025-07-11 04:51:54.488628+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 509, '2025-07-11 04:51:54.56538+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 514, '2025-07-11 04:51:54.642631+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 512, '2025-07-11 04:51:54.718633+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 513, '2025-07-11 04:51:54.803883+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 475, '2025-07-11 04:51:54.879875+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 470, '2025-07-11 04:51:54.955896+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 473, '2025-07-11 04:51:55.031909+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 474, '2025-07-11 04:51:55.108891+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 471, '2025-07-11 04:51:55.186142+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 477, '2025-07-11 04:51:55.262169+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 476, '2025-07-11 04:51:55.33815+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 472, '2025-07-11 04:51:55.414154+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 503, '2025-07-11 04:51:55.493151+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 506, '2025-07-11 04:51:55.569393+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 507, '2025-07-11 04:51:55.645656+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 504, '2025-07-11 04:51:55.721647+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 505, '2025-07-11 04:51:55.798132+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 490, '2025-07-11 04:51:55.874883+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 493, '2025-07-11 04:51:55.952655+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 494, '2025-07-11 04:51:56.030388+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 491, '2025-07-11 04:51:56.106648+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (95, 492, '2025-07-11 04:51:56.182634+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 503, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 472, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 476, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 522, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 482, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 490, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 527, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 491, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 528, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 525, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 517, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 510, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 495, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 529, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 513, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 477, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 520, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 506, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 534, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 511, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 516, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 488, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 509, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 501, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 481, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 502, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 521, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 531, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 514, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 470, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 483, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 487, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 530, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 493, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 519, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 498, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 535, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 494, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 492, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 474, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 473, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 480, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 499, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 475, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 532, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 515, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 500, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 485, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 507, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 484, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 524, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 512, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 504, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 479, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 471, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 489, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 526, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 508, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 523, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 533, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 478, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 505, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 486, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 518, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 496, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (24, 497, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 845, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 849, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 844, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 840, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 862, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 870, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 871, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 876, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 855, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 877, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 881, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 842, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 873, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 859, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 884, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 864, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 874, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 878, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 867, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 857, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 888, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 854, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 858, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 860, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 890, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 861, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 882, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 839, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 843, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 853, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 848, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 847, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 872, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 852, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 850, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 856, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 887, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 866, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 838, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 883, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 889, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 863, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 846, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 865, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 875, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 885, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 891, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 868, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 851, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 841, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 886, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 869, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 879, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (22, 880, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 641, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 639, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 632, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 669, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 670, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 628, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 660, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 658, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 668, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 671, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 666, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 650, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 663, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 648, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 657, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 649, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 634, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 659, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 645, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 667, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 630, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 665, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 664, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 647, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 624, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 626, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 629, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 655, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 633, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 644, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 640, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 662, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 642, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 631, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 636, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 651, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 627, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 646, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 652, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 637, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 638, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 643, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 635, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 653, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 654, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 656, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 625, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (23, 661, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 714, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 686, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 684, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 672, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 674, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 681, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 700, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 706, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 690, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 673, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 718, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 694, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 679, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 704, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 701, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 692, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 722, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 705, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 696, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 689, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 687, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 712, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 695, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 719, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 688, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 721, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 703, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 715, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 720, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 698, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 713, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 707, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 683, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 685, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 693, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 699, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 709, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 675, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 697, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 708, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 717, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 710, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 676, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 691, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 702, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 716, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 711, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 680, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 678, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 677, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (25, 682, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 594, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 617, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 614, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 588, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 582, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 606, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 599, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 601, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 620, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 581, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 593, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 609, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 589, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 600, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 603, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 595, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 579, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 590, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 578, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 596, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 610, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 586, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 621, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 591, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 580, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 607, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 622, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 623, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 585, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 604, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 605, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 587, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 597, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 584, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 602, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 598, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 592, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 612, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 616, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 615, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 613, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 583, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 608, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 619, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 618, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (26, 611, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 763, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 752, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 738, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 745, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 733, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 756, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 736, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 758, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 755, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 739, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 728, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 725, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 759, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 730, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 723, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 737, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 748, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 727, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 741, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 729, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 734, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 724, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 742, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 761, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 764, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 735, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 750, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 744, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 732, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 751, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 762, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 753, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 757, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 749, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 740, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 747, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 746, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 731, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 754, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 743, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 760, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (27, 726, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 813, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 814, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 815, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 816, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 817, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 818, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 819, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 820, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 821, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 822, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 823, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 824, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 825, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 826, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 827, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 828, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 829, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 830, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 831, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 832, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 833, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 834, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 835, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 836, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (28, 837, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 803, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 804, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 775, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 782, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 770, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 789, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 800, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 806, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 781, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 777, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 796, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 812, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 779, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 805, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 774, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 765, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 785, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 786, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 807, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 802, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 801, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 772, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 783, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 773, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 792, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 784, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 798, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 808, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 776, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 787, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 771, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 797, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 780, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 790, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 793, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 791, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 768, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 766, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 810, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 769, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 778, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 794, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 788, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 811, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 799, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 767, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 795, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (29, 809, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 555, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 541, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 550, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 577, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 549, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 559, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 537, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 554, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 563, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 556, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 547, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 566, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 575, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 569, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 536, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 570, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 543, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 540, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 546, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 565, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 576, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 545, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 553, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 560, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 542, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 571, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 551, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 557, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 552, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 564, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 558, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 567, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 562, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 572, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 544, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 561, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 574, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 539, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 568, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 538, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 548, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (30, 573, '2025-07-03 06:28:32.646945+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 599, '2025-07-03 06:33:58.542366+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 600, '2025-07-03 06:33:58.607113+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 603, '2025-07-03 06:33:58.687126+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 601, '2025-07-03 06:33:58.747382+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 605, '2025-07-03 06:33:58.806885+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 606, '2025-07-03 06:33:58.866368+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 609, '2025-07-03 06:33:58.926113+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 608, '2025-07-03 06:33:58.986381+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 607, '2025-07-03 06:33:59.045886+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 616, '2025-07-03 06:33:59.105374+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 617, '2025-07-03 06:33:59.165874+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 585, '2025-07-03 06:33:59.22564+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 582, '2025-07-03 06:33:59.286127+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 579, '2025-07-03 06:33:59.345893+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 580, '2025-07-03 06:33:59.405383+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 614, '2025-07-03 06:33:59.464878+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 610, '2025-07-03 06:33:59.52437+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 611, '2025-07-03 06:33:59.583889+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 612, '2025-07-03 06:33:59.644652+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 586, '2025-07-03 06:33:59.704139+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 590, '2025-07-03 06:33:59.764887+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 592, '2025-07-03 06:33:59.824631+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 587, '2025-07-03 06:33:59.884395+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 588, '2025-07-03 06:33:59.943886+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 598, '2025-07-03 06:34:00.003368+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 597, '2025-07-03 06:34:00.062887+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 594, '2025-07-03 06:34:00.122397+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 595, '2025-07-03 06:34:00.181892+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 623, '2025-07-03 06:34:00.241909+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 621, '2025-07-03 06:34:00.301416+00');
INSERT INTO public.role_permissions (role_id, permission_id, created_at) VALUES (31, 622, '2025-07-03 06:34:00.361151+00');


--
-- PostgreSQL database dump complete
--


-- Tabla: users (23 registros)
-- Reiniciando IDs desde 1 para users
INSERT INTO users (id, name, email, password, status, area, active, created_at, updated_at, phone, created_by, updated_by, refresh_token)
SELECT 
  ROW_NUMBER() OVER (ORDER BY id), name, email, password, status, area, active, created_at, updated_at, phone, created_by, updated_by, refresh_token
FROM users ORDER BY id;

-- Tabla: user_roles (23 registros)
--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.5 (Ubuntu 17.5-1.pgdg22.04+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: user_roles; Type: TABLE DATA; Schema: public; Owner: -
--

INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (180, 11, '2025-07-04 18:45:48.174245+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (181, 22, '2025-07-04 18:45:54.191083+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (183, 22, '2025-07-04 18:48:57.787123+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (184, 23, '2025-07-04 18:48:58.551508+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (185, 24, '2025-07-04 18:49:00.830057+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (186, 25, '2025-07-04 18:49:02.820343+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (187, 26, '2025-07-04 18:49:03.570593+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (188, 27, '2025-07-04 18:49:05.836702+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (189, 28, '2025-07-04 18:49:07.850666+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (190, 29, '2025-07-04 18:49:08.573387+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (191, 30, '2025-07-04 18:49:10.885455+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (193, 93, '2025-07-04 18:49:23.981813+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (194, 94, '2025-07-04 18:49:24.668584+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (195, 95, '2025-07-04 18:49:26.957515+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (196, 96, '2025-07-04 18:49:29.162032+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (197, 31, '2025-07-04 18:49:31.958763+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (198, 33, '2025-07-04 18:49:34.058168+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (199, 39, '2025-07-04 18:49:34.800713+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (200, 35, '2025-07-04 18:49:35.555906+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (201, 97, '2025-07-04 18:49:38.049196+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (2, 22, '2025-07-04 19:15:30.007383+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (2, 110, '2025-07-04 19:17:53.642457+00');
INSERT INTO public.user_roles (user_id, role_id, created_at) VALUES (207, 110, '2025-07-05 05:23:23.140195+00');


--
-- PostgreSQL database dump complete
--


-- Tabla: products (7 registros)
-- Reiniciando IDs desde 1 para products
INSERT INTO products (id, nombre, descripcion, codigo_item, created_at, updated_at, ubicacion, marca, modelo, numero_serie, pedimento, observaciones, tipo_almacen, stock_disponible, stock_comprometido, stock_minimo, estado, created_by, updated_by, deleted_at)
SELECT 
  ROW_NUMBER() OVER (ORDER BY id), nombre, descripcion, codigo_item, created_at, updated_at, ubicacion, marca, modelo, numero_serie, pedimento, observaciones, tipo_almacen, stock_disponible, stock_comprometido, stock_minimo, estado, created_by, updated_by, deleted_at
FROM products ORDER BY id;

-- Tabla: clients (1 registros)
-- Reiniciando IDs desde 1 para clients
INSERT INTO clients (id, commercial_name, legal_name, rfc, tax_regime, neighborhood, city, state, zip_code, country, contact_name, contact_position, industry, company_area, company_size, client_type, credit_limit, credit_days, preferred_payment, website, observations, status, active, is_quick_registration, rfc_validated, created_at, updated_at, created_by_user_id, assigned_salesperson_id, contact_phones, contact_emails, street)
SELECT 
  ROW_NUMBER() OVER (ORDER BY id), commercial_name, legal_name, rfc, tax_regime, neighborhood, city, state, zip_code, country, contact_name, contact_position, industry, company_area, company_size, client_type, credit_limit, credit_days, preferred_payment, website, observations, status, active, is_quick_registration, rfc_validated, created_at, updated_at, created_by_user_id, assigned_salesperson_id, contact_phones, contact_emails, street
FROM clients ORDER BY id;

-- Tabla: sales (0 registros)
-- (Sin datos)

-- Tabla: inventory_movements (0 registros)
-- (Sin datos)

-- Tabla: notifications (0 registros)
-- (Sin datos)

-- Tabla: audit_logs (57 registros)
-- Reiniciando IDs desde 1 para audit_logs
INSERT INTO audit_logs (id, user_id, action, target_entity, target_id, details, ip_address, created_at, created_by, updated_by, updated_at)
SELECT 
  ROW_NUMBER() OVER (ORDER BY id), user_id, action, target_entity, target_id, details, ip_address, created_at, created_by, updated_by, updated_at
FROM audit_logs ORDER BY id;

-- Tabla: backups (2 registros)
-- Reiniciando IDs desde 1 para backups
INSERT INTO backups (id, name, description, type, status, file_path, file_size, error_message, metadata, scheduled_at, completed_at, expires_at, created_at, updated_at, frequency)
SELECT 
  ROW_NUMBER() OVER (ORDER BY id), name, description, type, status, file_path, file_size, error_message, metadata, scheduled_at, completed_at, expires_at, created_at, updated_at, frequency
FROM backups ORDER BY id;

-- Tabla: user_departments (1 registros)
--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.5 (Ubuntu 17.5-1.pgdg22.04+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: user_departments; Type: TABLE DATA; Schema: public; Owner: -
--

INSERT INTO public.user_departments (user_id, department_id, created_at) VALUES (2, 2, '2025-07-02 21:23:40.768454+00');


--
-- PostgreSQL database dump complete
--


-- Tabla: departments (11 registros)
-- Reiniciando IDs desde 1 para departments
INSERT INTO departments (id, name, description, active, created_at, updated_at)
SELECT 
  ROW_NUMBER() OVER (ORDER BY id), name, description, active, created_at, updated_at
FROM departments ORDER BY id;

-- Tabla: calibrations (2 registros)
-- Reiniciando IDs desde 1 para calibrations
INSERT INTO calibrations (id, equipment_name, equipment_model, serial_number, calibration_date, next_calibration_date, calibrated_by, department_id, certificate_number, status, notes, created_at, updated_at)
SELECT 
  ROW_NUMBER() OVER (ORDER BY id), equipment_name, equipment_model, serial_number, calibration_date, next_calibration_date, calibrated_by, department_id, certificate_number, status, notes, created_at, updated_at
FROM calibrations ORDER BY id;

-- Tabla: contracts (0 registros)
-- (Sin datos)

-- Tabla: customers (3 registros)
-- Reiniciando IDs desde 1 para customers
INSERT INTO customers (id, razon_social, no_cliente, cliente_fiscal, telefono, correo)
SELECT 
  ROW_NUMBER() OVER (ORDER BY id), razon_social, no_cliente, cliente_fiscal, telefono, correo
FROM customers ORDER BY id;

-- Tabla: documents (0 registros)
-- (Sin datos)

-- Tabla: credit_requests (0 registros)
-- (Sin datos)

-- Tabla: employee_files (0 registros)
-- (Sin datos)

-- Tabla: events (0 registros)
-- (Sin datos)

-- Tabla: event_documents (0 registros)
-- (Sin datos)

-- Tabla: loans (0 registros)
-- (Sin datos)

-- Tabla: logistics_events (0 registros)
-- (Sin datos)

-- Tabla: equipment_receipts (0 registros)
-- (Sin datos)

-- Tabla: fiscal_numbers (0 registros)
-- (Sin datos)

-- Tabla: inventory (3 registros)
-- Reiniciando IDs desde 1 para inventory
INSERT INTO inventory (id, product_id, quantity, location, last_update)
SELECT 
  ROW_NUMBER() OVER (ORDER BY id), product_id, quantity, location, last_update
FROM inventory ORDER BY id;

-- Tabla: invoice_documents (0 registros)
-- (Sin datos)

-- Tabla: invoices (0 registros)
-- (Sin datos)

-- Tabla: migrations (2 registros)
-- Reiniciando IDs desde 1 para migrations
INSERT INTO migrations (id, timestamp, name)
SELECT 
  ROW_NUMBER() OVER (ORDER BY id), timestamp, name
FROM migrations ORDER BY id;

-- Tabla: notifications_backup (2 registros)
-- Reiniciando IDs desde 1 para notifications_backup
INSERT INTO notifications_backup (id, title, message, related_entity_type, related_entity_id, type, user_id, department_id, is_read, priority, created_at)
SELECT 
  ROW_NUMBER() OVER (ORDER BY id), title, message, related_entity_type, related_entity_id, type, user_id, department_id, is_read, priority, created_at
FROM notifications_backup ORDER BY id;

-- Tabla: personal_follow_ups (0 registros)
-- (Sin datos)

-- Tabla: plant_services (0 registros)
-- (Sin datos)

-- Tabla: procedures (0 registros)
-- (Sin datos)

-- Tabla: project_documents (0 registros)
-- (Sin datos)

-- Tabla: marketing_campaigns (0 registros)
-- (Sin datos)

-- Tabla: password_history (0 registros)
-- (Sin datos)

-- Tabla: purchase_requests (0 registros)
-- (Sin datos)

-- Tabla: quotation_documents (0 registros)
-- (Sin datos)

-- Tabla: project_stages (0 registros)
-- (Sin datos)

-- Tabla: requests (11 registros)
-- Reiniciando IDs desde 1 para requests
INSERT INTO requests (id, destination_address, shipping_company, package_weight, package_dimensions, tracking_number, request_type, title, description, requester_id, department_id, priority, status, assigned_to, due_date, completed_date, created_at, updated_at, category)
SELECT 
  ROW_NUMBER() OVER (ORDER BY id), destination_address, shipping_company, package_weight, package_dimensions, tracking_number, request_type, title, description, requester_id, department_id, priority, status, assigned_to, due_date, completed_date, created_at, updated_at, category
FROM requests ORDER BY id;

-- Tabla: system_tickets (0 registros)
-- (Sin datos)

-- Tabla: quotations (2 registros)
-- Reiniciando IDs desde 1 para quotations
INSERT INTO quotations (id, customer_id, project_id, created_by, quotation_number, total_amount, status, valid_until, notes, created_at, updated_at)
SELECT 
  ROW_NUMBER() OVER (ORDER BY id), customer_id, project_id, created_by, quotation_number, total_amount, status, valid_until, notes, created_at, updated_at
FROM quotations ORDER BY id;

-- Tabla: question_pro_links (0 registros)
-- (Sin datos)

-- Tabla: quotation_items (0 registros)
-- (Sin datos)

-- Tabla: report_folios (0 registros)
-- (Sin datos)

-- Tabla: training_documents (0 registros)
-- (Sin datos)

-- Tabla: trainings (2 registros)
-- Reiniciando IDs desde 1 para trainings
INSERT INTO trainings (id, title, description, trainer_id, department_id, start_date, end_date, location, max_participants, status, created_at, updated_at, cancelled_at, completed_at, instructor_id, coordinator_id, type, training_mode, registration_deadline, duration_hours, current_participants, min_score, virtual_meeting_link, materials_required, prerequisites, learning_objectives, is_mandatory, is_certification_required, certification_validity_months, cost_per_participant, total_budget, is_notification_enabled, notification_frequency, reminder_days_before, next_notification_date, last_notification_sent, evaluation_deadline, certificate_template, notes)
SELECT 
  ROW_NUMBER() OVER (ORDER BY id), title, description, trainer_id, department_id, start_date, end_date, location, max_participants, status, created_at, updated_at, cancelled_at, completed_at, instructor_id, coordinator_id, type, training_mode, registration_deadline, duration_hours, current_participants, min_score, virtual_meeting_link, materials_required, prerequisites, learning_objectives, is_mandatory, is_certification_required, certification_validity_months, cost_per_participant, total_budget, is_notification_enabled, notification_frequency, reminder_days_before, next_notification_date, last_notification_sent, evaluation_deadline, certificate_template, notes
FROM trainings ORDER BY id;

-- Tabla: training_participants (0 registros)
-- (Sin datos)

-- Tabla: training_evaluations (0 registros)
-- (Sin datos)

-- Tabla: projects (2 registros)
-- Reiniciando IDs desde 1 para projects
INSERT INTO projects (id, name, description, customer_id, manager_id, start_date, end_date, status, priority, budget, created_at, updated_at)
SELECT 
  ROW_NUMBER() OVER (ORDER BY id), name, description, customer_id, manager_id, start_date, end_date, status, priority, budget, created_at, updated_at
FROM projects ORDER BY id;

-- Tabla: credit_request_documents (0 registros)
-- (Sin datos)

-- Tabla: event_attendees (0 registros)
-- (Sin datos)

-- Tabla: technical_sheets (0 registros)
-- (Sin datos)

-- Tabla: client_documents (0 registros)
-- (Sin datos)

-- Tabla: procedimientos_calidad (3 registros)
-- Reiniciando IDs desde 1 para procedimientos_calidad
INSERT INTO procedimientos_calidad (id, nombre, tipo, area, archivo_url, creado_por, fecha_creacion)
SELECT 
  ROW_NUMBER() OVER (ORDER BY id), nombre, tipo, area, archivo_url, creado_por, fecha_creacion
FROM procedimientos_calidad ORDER BY id;

-- Tabla: links_questionpro (3 registros)
-- Reiniciando IDs desde 1 para links_questionpro
INSERT INTO links_questionpro (id, descripcion, url, creado_por, fecha_creacion)
SELECT 
  ROW_NUMBER() OVER (ORDER BY id), descripcion, url, creado_por, fecha_creacion
FROM links_questionpro ORDER BY id;

-- Tabla: uso_links_questionpro (0 registros)
-- (Sin datos)

-- Tabla: seguimientos_calidad (0 registros)
-- (Sin datos)

-- Tabla: adjuntos_seguimiento (0 registros)
-- (Sin datos)

-- Tabla: quality_issues (0 registros)
-- (Sin datos)

-- =============================
-- REINICIAR SECUENCIAS
-- =============================
-- Ajustar secuencias al máximo ID actual + 1

 SELECT setval('public.departments_id_seq', COALESCE((SELECT MAX(id) FROM departments), 0) + 1, false);
 SELECT setval('public.roles_id_seq', COALESCE((SELECT MAX(id) FROM roles), 0) + 1, false);
 SELECT setval('public.permissions_id_seq', COALESCE((SELECT MAX(id) FROM permissions), 0) + 1, false);
 SELECT setval('public.users_id_seq', COALESCE((SELECT MAX(id) FROM users), 0) + 1, false);
 SELECT setval('public.notifications_id_seq', COALESCE((SELECT MAX(id) FROM notifications), 0) + 1, false);
 SELECT setval('public.audit_logs_id_seq', COALESCE((SELECT MAX(id) FROM audit_logs), 0) + 1, false);
 SELECT setval('public.backups_id_seq', COALESCE((SELECT MAX(id) FROM backups), 0) + 1, false);
 SELECT setval('public.calibrations_id_seq', COALESCE((SELECT MAX(id) FROM calibrations), 0) + 1, false);
 SELECT setval('public.contracts_id_seq', COALESCE((SELECT MAX(id) FROM contracts), 0) + 1, false);
 SELECT setval('public.credit_requests_id_seq', COALESCE((SELECT MAX(id) FROM credit_requests), 0) + 1, false);
 SELECT setval('public.customers_id_seq', COALESCE((SELECT MAX(id) FROM customers), 0) + 1, false);
 SELECT setval('public.documents_id_seq', COALESCE((SELECT MAX(id) FROM documents), 0) + 1, false);
 SELECT setval('public.employee_files_id_seq', COALESCE((SELECT MAX(id) FROM employee_files), 0) + 1, false);
 SELECT setval('public.equipment_receipts_id_seq', COALESCE((SELECT MAX(id) FROM equipment_receipts), 0) + 1, false);
 SELECT setval('public.event_attendees_id_seq', COALESCE((SELECT MAX(id) FROM event_attendees), 0) + 1, false);
 SELECT setval('public.event_documents_id_seq', COALESCE((SELECT MAX(id) FROM event_documents), 0) + 1, false);
 SELECT setval('public.events_id_seq', COALESCE((SELECT MAX(id) FROM events), 0) + 1, false);
 SELECT setval('public.fiscal_numbers_id_seq', COALESCE((SELECT MAX(id) FROM fiscal_numbers), 0) + 1, false);
 SELECT setval('public.inventory_id_seq', COALESCE((SELECT MAX(id) FROM inventory), 0) + 1, false);
 SELECT setval('public.inventory_movements_id_seq', COALESCE((SELECT MAX(id) FROM inventory_movements), 0) + 1, false);
 SELECT setval('public.invoices_id_seq', COALESCE((SELECT MAX(id) FROM invoices), 0) + 1, false);
 SELECT setval('public.loans_id_seq', COALESCE((SELECT MAX(id) FROM loans), 0) + 1, false);
 SELECT setval('public.logistics_events_id_seq', COALESCE((SELECT MAX(id) FROM logistics_events), 0) + 1, false);
 SELECT setval('public.marketing_campaigns_id_seq', COALESCE((SELECT MAX(id) FROM marketing_campaigns), 0) + 1, false);
 SELECT setval('public.migrations_id_seq', COALESCE((SELECT MAX(id) FROM migrations), 0) + 1, false);
 SELECT setval('public.password_history_id_seq', COALESCE((SELECT MAX(id) FROM password_history), 0) + 1, false);
 SELECT setval('public.personal_follow_ups_id_seq', COALESCE((SELECT MAX(id) FROM personal_follow_ups), 0) + 1, false);
 SELECT setval('public.plant_services_id_seq', COALESCE((SELECT MAX(id) FROM plant_services), 0) + 1, false);
 SELECT setval('public.procedures_id_seq', COALESCE((SELECT MAX(id) FROM procedures), 0) + 1, false);
 SELECT setval('public.products_id_seq', COALESCE((SELECT MAX(id) FROM products), 0) + 1, false);
 SELECT setval('public.project_documents_id_seq', COALESCE((SELECT MAX(id) FROM project_documents), 0) + 1, false);
 SELECT setval('public.project_stages_id_seq', COALESCE((SELECT MAX(id) FROM project_stages), 0) + 1, false);
 SELECT setval('public.projects_id_seq', COALESCE((SELECT MAX(id) FROM projects), 0) + 1, false);
 SELECT setval('public.purchase_requests_id_seq', COALESCE((SELECT MAX(id) FROM purchase_requests), 0) + 1, false);
 SELECT setval('public.quality_issues_id_seq', COALESCE((SELECT MAX(id) FROM quality_issues), 0) + 1, false);
 SELECT setval('public.question_pro_links_id_seq', COALESCE((SELECT MAX(id) FROM question_pro_links), 0) + 1, false);
 SELECT setval('public.quotation_documents_id_seq', COALESCE((SELECT MAX(id) FROM quotation_documents), 0) + 1, false);
 SELECT setval('public.quotation_items_id_seq', COALESCE((SELECT MAX(id) FROM quotation_items), 0) + 1, false);
 SELECT setval('public.quotations_id_seq', COALESCE((SELECT MAX(id) FROM quotations), 0) + 1, false);
 SELECT setval('public.report_folios_id_seq', COALESCE((SELECT MAX(id) FROM report_folios), 0) + 1, false);
 SELECT setval('public.requests_id_seq', COALESCE((SELECT MAX(id) FROM requests), 0) + 1, false);
 SELECT setval('public.sales_id_seq', COALESCE((SELECT MAX(id) FROM sales), 0) + 1, false);
 SELECT setval('public.system_tickets_id_seq', COALESCE((SELECT MAX(id) FROM system_tickets), 0) + 1, false);
 SELECT setval('public.technical_sheets_id_seq', COALESCE((SELECT MAX(id) FROM technical_sheets), 0) + 1, false);
 SELECT setval('public.training_documents_id_seq', COALESCE((SELECT MAX(id) FROM training_documents), 0) + 1, false);
 SELECT setval('public.training_evaluations_id_seq', COALESCE((SELECT MAX(id) FROM training_evaluations), 0) + 1, false);
 SELECT setval('public.training_participants_id_seq', COALESCE((SELECT MAX(id) FROM training_participants), 0) + 1, false);
 SELECT setval('public.trainings_id_seq', COALESCE((SELECT MAX(id) FROM trainings), 0) + 1, false);
 SELECT setval('public.clients_id_seq', COALESCE((SELECT MAX(id) FROM clients), 0) + 1, false);
 SELECT setval('public.client_documents_id_seq', COALESCE((SELECT MAX(id) FROM client_documents), 0) + 1, false);

-- Rehabilitar triggers
SET session_replication_role = DEFAULT;

-- =============================
-- FIN DEL BACKUP LIMPIO
-- =============================
-- IDs reiniciados desde 1
-- Relaciones FK mantenidas
-- Secuencias ajustadas automáticamente
-- =============================

