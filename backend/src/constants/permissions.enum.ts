// Auto-generated from scripts/permissions.yaml
export enum PermissionEnum {
  SISTEMAS_USERS_CREATE = 'sistemas:users:create',
  SISTEMAS_USERS_READ = 'sistemas:users:read',
  SISTEMAS_USERS_UPDATE = 'sistemas:users:update',
  SISTEMAS_USERS_DELETE = 'sistemas:users:delete',
  SISTEMAS_USERS_LIST = 'sistemas:users:list',
  SISTEMAS_USERS_ACTIVATE = 'sistemas:users:activate',
  SISTEMAS_USERS_DEACTIVATE = 'sistemas:users:deactivate',
  SISTEMAS_USERS_RESET_PASSWORD = 'sistemas:users:reset_password',
  SISTEMAS_ROLES_CREATE = 'sistemas:roles:create',
  SISTEMAS_ROLES_READ = 'sistemas:roles:read',
  SISTEMAS_ROLES_UPDATE = 'sistemas:roles:update',
  SISTEMAS_ROLES_DELETE = 'sistemas:roles:delete',
  SISTEMAS_ROLES_LIST = 'sistemas:roles:list',
  SISTEMAS_ROLES_ASSIGN = 'sistemas:roles:assign',
  SISTEMAS_ROLES_UNASSIGN = 'sistemas:roles:unassign',
  SISTEMAS_PERMISSIONS_CREATE = 'sistemas:permissions:create',
  SISTEMAS_PERMISSIONS_READ = 'sistemas:permissions:read',
  SISTEMAS_PERMISSIONS_UPDATE = 'sistemas:permissions:update',
  SISTEMAS_PERMISSIONS_DELETE = 'sistemas:permissions:delete',
  SISTEMAS_PERMISSIONS_LIST = 'sistemas:permissions:list',
  SISTEMAS_PERMISSIONS_ASSIGN = 'sistemas:permissions:assign',
  SISTEMAS_BACKUPS_CREATE = 'sistemas:backups:create',
  SISTEMAS_BACKUPS_READ = 'sistemas:backups:read',
  SISTEMAS_BACKUPS_DOWNLOAD = 'sistemas:backups:download',
  SISTEMAS_BACKUPS_RESTORE = 'sistemas:backups:restore',
  SISTEMAS_BACKUPS_SCHEDULE = 'sistemas:backups:schedule',
  SISTEMAS_AUDIT_LOGS_READ = 'sistemas:audit_logs:read',
  SISTEMAS_AUDIT_LOGS_LIST = 'sistemas:audit_logs:list',
  SISTEMAS_AUDIT_LOGS_EXPORT = 'sistemas:audit_logs:export',
  SISTEMAS_SYSTEM_CONFIG_READ = 'sistemas:system_config:read',
  SISTEMAS_SYSTEM_CONFIG_UPDATE = 'sistemas:system_config:update',
  SISTEMAS_NOTIFICATIONS_CREATE = 'sistemas:notifications:create',
  SISTEMAS_NOTIFICATIONS_READ = 'sistemas:notifications:read',
  SISTEMAS_NOTIFICATIONS_UPDATE = 'sistemas:notifications:update',
  SISTEMAS_NOTIFICATIONS_DELETE = 'sistemas:notifications:delete',
  SISTEMAS_NOTIFICATIONS_BROADCAST = 'sistemas:notifications:broadcast',
  ALMACEN_STOCK_CREATE = 'almacen:stock:create',
  ALMACEN_STOCK_READ = 'almacen:stock:read',
  ALMACEN_STOCK_UPDATE = 'almacen:stock:update',
  ALMACEN_STOCK_DELETE = 'almacen:stock:delete',
  ALMACEN_STOCK_LIST = 'almacen:stock:list',
  ALMACEN_STOCK_ADJUST = 'almacen:stock:adjust',
  ALMACEN_STOCK_TRANSFER = 'almacen:stock:transfer',
  ALMACEN_STOCK_RESERVE = 'almacen:stock:reserve',
  ALMACEN_PRODUCTS_CREATE = 'almacen:products:create',
  ALMACEN_PRODUCTS_READ = 'almacen:products:read',
  ALMACEN_PRODUCTS_UPDATE = 'almacen:products:update',
  ALMACEN_PRODUCTS_DELETE = 'almacen:products:delete',
  ALMACEN_PRODUCTS_LIST = 'almacen:products:list',
  ALMACEN_PRODUCTS_IMPORT = 'almacen:products:import',
  ALMACEN_PRODUCTS_EXPORT = 'almacen:products:export',
  ALMACEN_CATEGORIES_CREATE = 'almacen:categories:create',
  ALMACEN_CATEGORIES_READ = 'almacen:categories:read',
  ALMACEN_CATEGORIES_UPDATE = 'almacen:categories:update',
  ALMACEN_CATEGORIES_DELETE = 'almacen:categories:delete',
  ALMACEN_CATEGORIES_LIST = 'almacen:categories:list',
  ALMACEN_WAREHOUSES_CREATE = 'almacen:warehouses:create',
  ALMACEN_WAREHOUSES_READ = 'almacen:warehouses:read',
  ALMACEN_WAREHOUSES_UPDATE = 'almacen:warehouses:update',
  ALMACEN_WAREHOUSES_DELETE = 'almacen:warehouses:delete',
  ALMACEN_WAREHOUSES_LIST = 'almacen:warehouses:list',
  ALMACEN_MOVEMENTS_CREATE = 'almacen:movements:create',
  ALMACEN_MOVEMENTS_READ = 'almacen:movements:read',
  ALMACEN_MOVEMENTS_LIST = 'almacen:movements:list',
  ALMACEN_MOVEMENTS_APPROVE = 'almacen:movements:approve',
  ALMACEN_INVENTORY_READ = 'almacen:inventory:read',
  ALMACEN_INVENTORY_COUNT = 'almacen:inventory:count',
  ALMACEN_INVENTORY_AUDIT = 'almacen:inventory:audit',
  ALMACEN_INVENTORY_REPORT = 'almacen:inventory:report',
  ALMACEN_SUPPLIERS_CREATE = 'almacen:suppliers:create',
  ALMACEN_SUPPLIERS_READ = 'almacen:suppliers:read',
  ALMACEN_SUPPLIERS_UPDATE = 'almacen:suppliers:update',
  ALMACEN_SUPPLIERS_DELETE = 'almacen:suppliers:delete',
  ALMACEN_SUPPLIERS_LIST = 'almacen:suppliers:list',
  ALMACEN_QR_READ = 'almacen:qr:read',
  ALMACEN_QR_WRITE = 'almacen:qr:write',
  ALMACEN_QR_GENERATE = 'almacen:qr:generate',
  ALMACEN_QR_SCAN = 'almacen:qr:scan',
  ALMACEN_REPORTS_GENERATE = 'almacen:reports:generate',
  ALMACEN_REPORTS_VIEW = 'almacen:reports:view',
  ALMACEN_REPORTS_EXPORT = 'almacen:reports:export',
  ALMACEN_ENTRADAS_CREATE = 'almacen:entradas:create',
  ALMACEN_ENTRADAS_READ = 'almacen:entradas:read',
  ALMACEN_ENTRADAS_UPDATE = 'almacen:entradas:update',
  ALMACEN_ENTRADAS_DELETE = 'almacen:entradas:delete',
  ALMACEN_ENTRADAS_APPROVE = 'almacen:entradas:approve',
  ALMACEN_SALIDAS_CREATE = 'almacen:salidas:create',
  ALMACEN_SALIDAS_READ = 'almacen:salidas:read',
  ALMACEN_SALIDAS_UPDATE = 'almacen:salidas:update',
  ALMACEN_SALIDAS_DELETE = 'almacen:salidas:delete',
  ALMACEN_SALIDAS_APPROVE = 'almacen:salidas:approve',
  ALMACEN_PRESTAMOS_CREATE = 'almacen:prestamos:create',
  ALMACEN_PRESTAMOS_READ = 'almacen:prestamos:read',
  ALMACEN_PRESTAMOS_UPDATE = 'almacen:prestamos:update',
  ALMACEN_PRESTAMOS_DELETE = 'almacen:prestamos:delete',
  ALMACEN_PRESTAMOS_APPROVE = 'almacen:prestamos:approve',
  ALMACEN_PRESTAMOS_RETURN = 'almacen:prestamos:return',
  ALMACEN_VIATICOS_CREATE = 'almacen:viaticos:create',
  ALMACEN_VIATICOS_READ = 'almacen:viaticos:read',
  ALMACEN_VIATICOS_UPDATE = 'almacen:viaticos:update',
  ALMACEN_VIATICOS_DELETE = 'almacen:viaticos:delete',
  ALMACEN_VIATICOS_APPROVE = 'almacen:viaticos:approve',
  COMPRAS_REQUESTS_CREATE = 'compras:requests:create',
  COMPRAS_REQUESTS_READ = 'compras:requests:read',
  COMPRAS_REQUESTS_UPDATE = 'compras:requests:update',
  COMPRAS_REQUESTS_DELETE = 'compras:requests:delete',
  COMPRAS_REQUESTS_LIST = 'compras:requests:list',
  COMPRAS_REQUESTS_APPROVE = 'compras:requests:approve',
  COMPRAS_REQUESTS_REJECT = 'compras:requests:reject',
  COMPRAS_REQUESTS_CONVERT_TO_ORDER = 'compras:requests:convert_to_order',
  COMPRAS_ORDERS_CREATE = 'compras:orders:create',
  COMPRAS_ORDERS_READ = 'compras:orders:read',
  COMPRAS_ORDERS_UPDATE = 'compras:orders:update',
  COMPRAS_ORDERS_DELETE = 'compras:orders:delete',
  COMPRAS_ORDERS_LIST = 'compras:orders:list',
  COMPRAS_ORDERS_APPROVE = 'compras:orders:approve',
  COMPRAS_ORDERS_CANCEL = 'compras:orders:cancel',
  COMPRAS_ORDERS_RECEIVE = 'compras:orders:receive',
  COMPRAS_SUPPLIERS_CREATE = 'compras:suppliers:create',
  COMPRAS_SUPPLIERS_READ = 'compras:suppliers:read',
  COMPRAS_SUPPLIERS_UPDATE = 'compras:suppliers:update',
  COMPRAS_SUPPLIERS_DELETE = 'compras:suppliers:delete',
  COMPRAS_SUPPLIERS_LIST = 'compras:suppliers:list',
  COMPRAS_SUPPLIERS_EVALUATE = 'compras:suppliers:evaluate',
  COMPRAS_SUPPLIERS_APPROVE = 'compras:suppliers:approve',
  COMPRAS_QUOTES_CREATE = 'compras:quotes:create',
  COMPRAS_QUOTES_READ = 'compras:quotes:read',
  COMPRAS_QUOTES_UPDATE = 'compras:quotes:update',
  COMPRAS_QUOTES_DELETE = 'compras:quotes:delete',
  COMPRAS_QUOTES_COMPARE = 'compras:quotes:compare',
  COMPRAS_QUOTES_APPROVE = 'compras:quotes:approve',
  COMPRAS_BUDGETS_CREATE = 'compras:budgets:create',
  COMPRAS_BUDGETS_READ = 'compras:budgets:read',
  COMPRAS_BUDGETS_UPDATE = 'compras:budgets:update',
  COMPRAS_BUDGETS_APPROVE = 'compras:budgets:approve',
  COMPRAS_BUDGETS_MONITOR = 'compras:budgets:monitor',
  COMPRAS_CONTRACTS_CREATE = 'compras:contracts:create',
  COMPRAS_CONTRACTS_READ = 'compras:contracts:read',
  COMPRAS_CONTRACTS_UPDATE = 'compras:contracts:update',
  COMPRAS_CONTRACTS_DELETE = 'compras:contracts:delete',
  COMPRAS_CONTRACTS_APPROVE = 'compras:contracts:approve',
  COMPRAS_REPORTS_GENERATE = 'compras:reports:generate',
  COMPRAS_REPORTS_VIEW = 'compras:reports:view',
  COMPRAS_REPORTS_EXPORT = 'compras:reports:export',
  QUALITY_INCIDENCIAS_CREATE = 'quality:incidencias:create',
  QUALITY_INCIDENCIAS_READ = 'quality:incidencias:read',
  QUALITY_INCIDENCIAS_UPDATE = 'quality:incidencias:update',
  QUALITY_INCIDENCIAS_DELETE = 'quality:incidencias:delete',
  QUALITY_INCIDENCIAS_LIST = 'quality:incidencias:list',
  QUALITY_INCIDENCIAS_APPROVE = 'quality:incidencias:approve',
  QUALITY_INCIDENCIAS_CLOSE = 'quality:incidencias:close',
  QUALITY_INCIDENCIAS_ESCALATE = 'quality:incidencias:escalate',
  QUALITY_PROCEDURES_CREATE = 'quality:procedures:create',
  QUALITY_PROCEDURES_READ = 'quality:procedures:read',
  QUALITY_PROCEDURES_UPDATE = 'quality:procedures:update',
  QUALITY_PROCEDURES_DELETE = 'quality:procedures:delete',
  QUALITY_PROCEDURES_LIST = 'quality:procedures:list',
  QUALITY_PROCEDURES_APPROVE = 'quality:procedures:approve',
  QUALITY_PROCEDURES_PUBLISH = 'quality:procedures:publish',
  QUALITY_QUESTIONPRO_CREATE = 'quality:questionpro:create',
  QUALITY_QUESTIONPRO_READ = 'quality:questionpro:read',
  QUALITY_QUESTIONPRO_UPDATE = 'quality:questionpro:update',
  QUALITY_QUESTIONPRO_DELETE = 'quality:questionpro:delete',
  QUALITY_QUESTIONPRO_LIST = 'quality:questionpro:list',
  QUALITY_QUESTIONPRO_EXPORT = 'quality:questionpro:export',
  QUALITY_AUDITS_CREATE = 'quality:audits:create',
  QUALITY_AUDITS_READ = 'quality:audits:read',
  QUALITY_AUDITS_UPDATE = 'quality:audits:update',
  QUALITY_AUDITS_DELETE = 'quality:audits:delete',
  QUALITY_AUDITS_SCHEDULE = 'quality:audits:schedule',
  QUALITY_AUDITS_APPROVE = 'quality:audits:approve',
  QUALITY_CERTIFICATIONS_CREATE = 'quality:certifications:create',
  QUALITY_CERTIFICATIONS_READ = 'quality:certifications:read',
  QUALITY_CERTIFICATIONS_UPDATE = 'quality:certifications:update',
  QUALITY_CERTIFICATIONS_TRACK = 'quality:certifications:track',
  QUALITY_CERTIFICATIONS_RENEW = 'quality:certifications:renew',
  QUALITY_NON_CONFORMITIES_CREATE = 'quality:non_conformities:create',
  QUALITY_NON_CONFORMITIES_READ = 'quality:non_conformities:read',
  QUALITY_NON_CONFORMITIES_UPDATE = 'quality:non_conformities:update',
  QUALITY_NON_CONFORMITIES_DELETE = 'quality:non_conformities:delete',
  QUALITY_NON_CONFORMITIES_CLOSE = 'quality:non_conformities:close',
  QUALITY_CORRECTIVE_ACTIONS_CREATE = 'quality:corrective_actions:create',
  QUALITY_CORRECTIVE_ACTIONS_READ = 'quality:corrective_actions:read',
  QUALITY_CORRECTIVE_ACTIONS_UPDATE = 'quality:corrective_actions:update',
  QUALITY_CORRECTIVE_ACTIONS_DELETE = 'quality:corrective_actions:delete',
  QUALITY_CORRECTIVE_ACTIONS_APPROVE = 'quality:corrective_actions:approve',
  QUALITY_CORRECTIVE_ACTIONS_CLOSE = 'quality:corrective_actions:close',
  QUALITY_REPORTS_GENERATE = 'quality:reports:generate',
  QUALITY_REPORTS_VIEW = 'quality:reports:view',
  QUALITY_REPORTS_EXPORT = 'quality:reports:export',
  VENTAS_QUOTES_CREATE = 'ventas:quotes:create',
  VENTAS_QUOTES_READ = 'ventas:quotes:read',
  VENTAS_QUOTES_UPDATE = 'ventas:quotes:update',
  VENTAS_QUOTES_DELETE = 'ventas:quotes:delete',
  VENTAS_QUOTES_LIST = 'ventas:quotes:list',
  VENTAS_QUOTES_APPROVE = 'ventas:quotes:approve',
  VENTAS_QUOTES_SEND = 'ventas:quotes:send',
  VENTAS_QUOTES_CONVERT = 'ventas:quotes:convert',
  VENTAS_ORDERS_CREATE = 'ventas:orders:create',
  VENTAS_ORDERS_READ = 'ventas:orders:read',
  VENTAS_ORDERS_UPDATE = 'ventas:orders:update',
  VENTAS_ORDERS_DELETE = 'ventas:orders:delete',
  VENTAS_ORDERS_LIST = 'ventas:orders:list',
  VENTAS_ORDERS_APPROVE = 'ventas:orders:approve',
  VENTAS_ORDERS_CANCEL = 'ventas:orders:cancel',
  VENTAS_CLIENTS_CREATE = 'ventas:clients:create',
  VENTAS_CLIENTS_READ = 'ventas:clients:read',
  VENTAS_CLIENTS_UPDATE = 'ventas:clients:update',
  VENTAS_CLIENTS_DELETE = 'ventas:clients:delete',
  VENTAS_CLIENTS_LIST = 'ventas:clients:list',
  VENTAS_CLIENTS_APPROVE = 'ventas:clients:approve',
  VENTAS_CONTRACTS_CREATE = 'ventas:contracts:create',
  VENTAS_CONTRACTS_READ = 'ventas:contracts:read',
  VENTAS_CONTRACTS_UPDATE = 'ventas:contracts:update',
  VENTAS_CONTRACTS_DELETE = 'ventas:contracts:delete',
  VENTAS_CONTRACTS_APPROVE = 'ventas:contracts:approve',
  VENTAS_CONTRACTS_RENEW = 'ventas:contracts:renew',
  VENTAS_INVOICES_CREATE = 'ventas:invoices:create',
  VENTAS_INVOICES_READ = 'ventas:invoices:read',
  VENTAS_INVOICES_UPDATE = 'ventas:invoices:update',
  VENTAS_INVOICES_DELETE = 'ventas:invoices:delete',
  VENTAS_INVOICES_LIST = 'ventas:invoices:list',
  VENTAS_INVOICES_APPROVE = 'ventas:invoices:approve',
  VENTAS_INVOICES_SEND = 'ventas:invoices:send',
  VENTAS_PAYMENTS_CREATE = 'ventas:payments:create',
  VENTAS_PAYMENTS_READ = 'ventas:payments:read',
  VENTAS_PAYMENTS_UPDATE = 'ventas:payments:update',
  VENTAS_PAYMENTS_TRACK = 'ventas:payments:track',
  VENTAS_PAYMENTS_APPROVE = 'ventas:payments:approve',
  VENTAS_COMMISSIONS_CALCULATE = 'ventas:commissions:calculate',
  VENTAS_COMMISSIONS_READ = 'ventas:commissions:read',
  VENTAS_COMMISSIONS_UPDATE = 'ventas:commissions:update',
  VENTAS_COMMISSIONS_APPROVE = 'ventas:commissions:approve',
  VENTAS_REPORTS_GENERATE = 'ventas:reports:generate',
  VENTAS_REPORTS_VIEW = 'ventas:reports:view',
  VENTAS_REPORTS_EXPORT = 'ventas:reports:export',
  VENTAS_DASHBOARD_READ = 'ventas:dashboard:read',
  VENTAS_DASHBOARD_CONFIGURE = 'ventas:dashboard:configure',
  LOGISTICA_SERVICES_CREATE = 'logistica:services:create',
  LOGISTICA_SERVICES_READ = 'logistica:services:read',
  LOGISTICA_SERVICES_UPDATE = 'logistica:services:update',
  LOGISTICA_SERVICES_DELETE = 'logistica:services:delete',
  LOGISTICA_SERVICES_LIST = 'logistica:services:list',
  LOGISTICA_SERVICES_SCHEDULE = 'logistica:services:schedule',
  LOGISTICA_SERVICES_APPROVE = 'logistica:services:approve',
  LOGISTICA_SERVICES_COMPLETE = 'logistica:services:complete',
  LOGISTICA_SERVICES_CANCEL = 'logistica:services:cancel',
  LOGISTICA_SERVICES_GENERATE_PDF = 'logistica:services:generate_pdf',
  LOGISTICA_CALENDAR_READ = 'logistica:calendar:read',
  LOGISTICA_CALENDAR_UPDATE = 'logistica:calendar:update',
  LOGISTICA_CALENDAR_SCHEDULE = 'logistica:calendar:schedule',
  LOGISTICA_CALENDAR_APPROVE = 'logistica:calendar:approve',
  LOGISTICA_ROUTES_CREATE = 'logistica:routes:create',
  LOGISTICA_ROUTES_READ = 'logistica:routes:read',
  LOGISTICA_ROUTES_UPDATE = 'logistica:routes:update',
  LOGISTICA_ROUTES_DELETE = 'logistica:routes:delete',
  LOGISTICA_ROUTES_OPTIMIZE = 'logistica:routes:optimize',
  LOGISTICA_VEHICLES_CREATE = 'logistica:vehicles:create',
  LOGISTICA_VEHICLES_READ = 'logistica:vehicles:read',
  LOGISTICA_VEHICLES_UPDATE = 'logistica:vehicles:update',
  LOGISTICA_VEHICLES_DELETE = 'logistica:vehicles:delete',
  LOGISTICA_VEHICLES_LIST = 'logistica:vehicles:list',
  LOGISTICA_VEHICLES_MAINTAIN = 'logistica:vehicles:maintain',
  LOGISTICA_DRIVERS_CREATE = 'logistica:drivers:create',
  LOGISTICA_DRIVERS_READ = 'logistica:drivers:read',
  LOGISTICA_DRIVERS_UPDATE = 'logistica:drivers:update',
  LOGISTICA_DRIVERS_DELETE = 'logistica:drivers:delete',
  LOGISTICA_DRIVERS_LIST = 'logistica:drivers:list',
  LOGISTICA_DRIVERS_ASSIGN = 'logistica:drivers:assign',
  LOGISTICA_SHIPMENTS_CREATE = 'logistica:shipments:create',
  LOGISTICA_SHIPMENTS_READ = 'logistica:shipments:read',
  LOGISTICA_SHIPMENTS_UPDATE = 'logistica:shipments:update',
  LOGISTICA_SHIPMENTS_TRACK = 'logistica:shipments:track',
  LOGISTICA_SHIPMENTS_DELIVER = 'logistica:shipments:deliver',
  LOGISTICA_PAQUETERIA_CREATE = 'logistica:paqueteria:create',
  LOGISTICA_PAQUETERIA_READ = 'logistica:paqueteria:read',
  LOGISTICA_PAQUETERIA_UPDATE = 'logistica:paqueteria:update',
  LOGISTICA_PAQUETERIA_DELETE = 'logistica:paqueteria:delete',
  LOGISTICA_PAQUETERIA_TRACK = 'logistica:paqueteria:track',
  LOGISTICA_INGRESOS_CREATE = 'logistica:ingresos:create',
  LOGISTICA_INGRESOS_READ = 'logistica:ingresos:read',
  LOGISTICA_INGRESOS_UPDATE = 'logistica:ingresos:update',
  LOGISTICA_INGRESOS_APPROVE = 'logistica:ingresos:approve',
  LOGISTICA_ESTADISTICOS_READ = 'logistica:estadisticos:read',
  LOGISTICA_ESTADISTICOS_GENERATE = 'logistica:estadisticos:generate',
  LOGISTICA_ESTADISTICOS_EXPORT = 'logistica:estadisticos:export',
  LOGISTICA_REPORTS_GENERATE = 'logistica:reports:generate',
  LOGISTICA_REPORTS_VIEW = 'logistica:reports:view',
  LOGISTICA_REPORTS_EXPORT = 'logistica:reports:export',
  METROLOGIA_CALIBRATIONS_CREATE = 'metrologia:calibrations:create',
  METROLOGIA_CALIBRATIONS_READ = 'metrologia:calibrations:read',
  METROLOGIA_CALIBRATIONS_UPDATE = 'metrologia:calibrations:update',
  METROLOGIA_CALIBRATIONS_DELETE = 'metrologia:calibrations:delete',
  METROLOGIA_CALIBRATIONS_SCHEDULE = 'metrologia:calibrations:schedule',
  METROLOGIA_CALIBRATIONS_APPROVE = 'metrologia:calibrations:approve',
  METROLOGIA_CALIBRATIONS_COMPLETE = 'metrologia:calibrations:complete',
  METROLOGIA_CERTIFICATES_CREATE = 'metrologia:certificates:create',
  METROLOGIA_CERTIFICATES_READ = 'metrologia:certificates:read',
  METROLOGIA_CERTIFICATES_UPDATE = 'metrologia:certificates:update',
  METROLOGIA_CERTIFICATES_DOWNLOAD = 'metrologia:certificates:download',
  METROLOGIA_CERTIFICATES_VALIDATE = 'metrologia:certificates:validate',
  METROLOGIA_EQUIPMENT_CREATE = 'metrologia:equipment:create',
  METROLOGIA_EQUIPMENT_READ = 'metrologia:equipment:read',
  METROLOGIA_EQUIPMENT_UPDATE = 'metrologia:equipment:update',
  METROLOGIA_EQUIPMENT_DELETE = 'metrologia:equipment:delete',
  METROLOGIA_EQUIPMENT_LIST = 'metrologia:equipment:list',
  METROLOGIA_EQUIPMENT_MAINTAIN = 'metrologia:equipment:maintain',
  METROLOGIA_STANDARDS_CREATE = 'metrologia:standards:create',
  METROLOGIA_STANDARDS_READ = 'metrologia:standards:read',
  METROLOGIA_STANDARDS_UPDATE = 'metrologia:standards:update',
  METROLOGIA_STANDARDS_DELETE = 'metrologia:standards:delete',
  METROLOGIA_STANDARDS_VALIDATE = 'metrologia:standards:validate',
  METROLOGIA_PROCEDURES_CREATE = 'metrologia:procedures:create',
  METROLOGIA_PROCEDURES_READ = 'metrologia:procedures:read',
  METROLOGIA_PROCEDURES_UPDATE = 'metrologia:procedures:update',
  METROLOGIA_PROCEDURES_DELETE = 'metrologia:procedures:delete',
  METROLOGIA_PROCEDURES_APPROVE = 'metrologia:procedures:approve',
  METROLOGIA_CAPACITACIONES_CREATE = 'metrologia:capacitaciones:create',
  METROLOGIA_CAPACITACIONES_READ = 'metrologia:capacitaciones:read',
  METROLOGIA_CAPACITACIONES_UPDATE = 'metrologia:capacitaciones:update',
  METROLOGIA_CAPACITACIONES_DELETE = 'metrologia:capacitaciones:delete',
  METROLOGIA_CAPACITACIONES_SCHEDULE = 'metrologia:capacitaciones:schedule',
  METROLOGIA_CAPACITACIONES_COMPLETE = 'metrologia:capacitaciones:complete',
  METROLOGIA_MAINTENANCE_CREATE = 'metrologia:maintenance:create',
  METROLOGIA_MAINTENANCE_READ = 'metrologia:maintenance:read',
  METROLOGIA_MAINTENANCE_UPDATE = 'metrologia:maintenance:update',
  METROLOGIA_MAINTENANCE_SCHEDULE = 'metrologia:maintenance:schedule',
  METROLOGIA_MAINTENANCE_COMPLETE = 'metrologia:maintenance:complete',
  METROLOGIA_REPORTS_GENERATE = 'metrologia:reports:generate',
  METROLOGIA_REPORTS_VIEW = 'metrologia:reports:view',
  METROLOGIA_REPORTS_EXPORT = 'metrologia:reports:export',
  RH_EMPLOYEES_CREATE = 'rh:employees:create',
  RH_EMPLOYEES_READ = 'rh:employees:read',
  RH_EMPLOYEES_UPDATE = 'rh:employees:update',
  RH_EMPLOYEES_DELETE = 'rh:employees:delete',
  RH_EMPLOYEES_LIST = 'rh:employees:list',
  RH_EMPLOYEES_HIRE = 'rh:employees:hire',
  RH_EMPLOYEES_TERMINATE = 'rh:employees:terminate',
  RH_TRAINING_CREATE = 'rh:training:create',
  RH_TRAINING_READ = 'rh:training:read',
  RH_TRAINING_UPDATE = 'rh:training:update',
  RH_TRAINING_DELETE = 'rh:training:delete',
  RH_TRAINING_SCHEDULE = 'rh:training:schedule',
  RH_TRAINING_COMPLETE = 'rh:training:complete',
  RH_TRAINING_CERTIFY = 'rh:training:certify',
  RH_EVALUATIONS_CREATE = 'rh:evaluations:create',
  RH_EVALUATIONS_READ = 'rh:evaluations:read',
  RH_EVALUATIONS_UPDATE = 'rh:evaluations:update',
  RH_EVALUATIONS_DELETE = 'rh:evaluations:delete',
  RH_EVALUATIONS_APPROVE = 'rh:evaluations:approve',
  RH_PAYROLL_CREATE = 'rh:payroll:create',
  RH_PAYROLL_READ = 'rh:payroll:read',
  RH_PAYROLL_UPDATE = 'rh:payroll:update',
  RH_PAYROLL_CALCULATE = 'rh:payroll:calculate',
  RH_PAYROLL_APPROVE = 'rh:payroll:approve',
  RH_PAYROLL_PROCESS = 'rh:payroll:process',
  RH_BENEFITS_CREATE = 'rh:benefits:create',
  RH_BENEFITS_READ = 'rh:benefits:read',
  RH_BENEFITS_UPDATE = 'rh:benefits:update',
  RH_BENEFITS_ASSIGN = 'rh:benefits:assign',
  RH_BENEFITS_CALCULATE = 'rh:benefits:calculate',
  RH_VACATIONS_CREATE = 'rh:vacations:create',
  RH_VACATIONS_READ = 'rh:vacations:read',
  RH_VACATIONS_UPDATE = 'rh:vacations:update',
  RH_VACATIONS_APPROVE = 'rh:vacations:approve',
  RH_VACATIONS_SCHEDULE = 'rh:vacations:schedule',
  RH_FILES_CREATE = 'rh:files:create',
  RH_FILES_READ = 'rh:files:read',
  RH_FILES_UPDATE = 'rh:files:update',
  RH_FILES_DELETE = 'rh:files:delete',
  RH_FILES_DOWNLOAD = 'rh:files:download',
  RH_CONTRACTS_CREATE = 'rh:contracts:create',
  RH_CONTRACTS_READ = 'rh:contracts:read',
  RH_CONTRACTS_UPDATE = 'rh:contracts:update',
  RH_CONTRACTS_DELETE = 'rh:contracts:delete',
  RH_CONTRACTS_APPROVE = 'rh:contracts:approve',
  RH_REPORTS_GENERATE = 'rh:reports:generate',
  RH_REPORTS_VIEW = 'rh:reports:view',
  RH_REPORTS_EXPORT = 'rh:reports:export',
  INFORMES_FOLIOS_CREATE = 'informes:folios:create',
  INFORMES_FOLIOS_READ = 'informes:folios:read',
  INFORMES_FOLIOS_UPDATE = 'informes:folios:update',
  INFORMES_FOLIOS_DELETE = 'informes:folios:delete',
  INFORMES_FOLIOS_ASSIGN = 'informes:folios:assign',
  INFORMES_FOLIOS_TRACK = 'informes:folios:track',
  INFORMES_STATUS_READ = 'informes:status:read',
  INFORMES_STATUS_UPDATE = 'informes:status:update',
  INFORMES_STATUS_TRACK = 'informes:status:track',
  INFORMES_ESTADISTICOS_READ = 'informes:estadisticos:read',
  INFORMES_ESTADISTICOS_GENERATE = 'informes:estadisticos:generate',
  INFORMES_ESTADISTICOS_EXPORT = 'informes:estadisticos:export',
  INFORMES_ESTADISTICOS_CONFIGURE = 'informes:estadisticos:configure',
  INFORMES_HISTORIAL_READ = 'informes:historial:read',
  INFORMES_HISTORIAL_EXPORT = 'informes:historial:export',
  INFORMES_HISTORIAL_AUDIT = 'informes:historial:audit',
  INFORMES_REPORTS_CREATE = 'informes:reports:create',
  INFORMES_REPORTS_READ = 'informes:reports:read',
  INFORMES_REPORTS_UPDATE = 'informes:reports:update',
  INFORMES_REPORTS_DELETE = 'informes:reports:delete',
  INFORMES_REPORTS_SCHEDULE = 'informes:reports:schedule',
  INFORMES_REPORTS_EXPORT = 'informes:reports:export',
  INFORMES_DASHBOARDS_READ = 'informes:dashboards:read',
  INFORMES_DASHBOARDS_CONFIGURE = 'informes:dashboards:configure',
  INFORMES_DASHBOARDS_SHARE = 'informes:dashboards:share',
  ADMIN_CONTRATOS_CREATE = 'admin:contratos:create',
  ADMIN_CONTRATOS_READ = 'admin:contratos:read',
  ADMIN_CONTRATOS_UPDATE = 'admin:contratos:update',
  ADMIN_CONTRATOS_DELETE = 'admin:contratos:delete',
  ADMIN_CONTRATOS_LIST = 'admin:contratos:list',
  ADMIN_CONTRATOS_APPROVE = 'admin:contratos:approve',
  ADMIN_CONTRATOS_RENEW = 'admin:contratos:renew',
  ADMIN_FISCAL_CREATE = 'admin:fiscal:create',
  ADMIN_FISCAL_READ = 'admin:fiscal:read',
  ADMIN_FISCAL_UPDATE = 'admin:fiscal:update',
  ADMIN_FISCAL_DELETE = 'admin:fiscal:delete',
  ADMIN_FISCAL_LIST = 'admin:fiscal:list',
  ADMIN_FISCAL_ASSIGN = 'admin:fiscal:assign',
  ADMIN_FACTURAS_CREATE = 'admin:facturas:create',
  ADMIN_FACTURAS_READ = 'admin:facturas:read',
  ADMIN_FACTURAS_UPDATE = 'admin:facturas:update',
  ADMIN_FACTURAS_DELETE = 'admin:facturas:delete',
  ADMIN_FACTURAS_LIST = 'admin:facturas:list',
  ADMIN_FACTURAS_APPROVE = 'admin:facturas:approve',
  ADMIN_FACTURAS_VALIDATE = 'admin:facturas:validate',
  ADMIN_CREDITO_CREATE = 'admin:credito:create',
  ADMIN_CREDITO_READ = 'admin:credito:read',
  ADMIN_CREDITO_UPDATE = 'admin:credito:update',
  ADMIN_CREDITO_DELETE = 'admin:credito:delete',
  ADMIN_CREDITO_LIST = 'admin:credito:list',
  ADMIN_CREDITO_APPROVE = 'admin:credito:approve',
  ADMIN_CREDITO_ANALYZE = 'admin:credito:analyze',
  ADMIN_MOROSOS_CREATE = 'admin:morosos:create',
  ADMIN_MOROSOS_READ = 'admin:morosos:read',
  ADMIN_MOROSOS_UPDATE = 'admin:morosos:update',
  ADMIN_MOROSOS_DELETE = 'admin:morosos:delete',
  ADMIN_MOROSOS_LIST = 'admin:morosos:list',
  ADMIN_MOROSOS_APPROVE = 'admin:morosos:approve',
  ADMIN_MOROSOS_LIBERATE = 'admin:morosos:liberate',
  ADMIN_GRAFICOS_READ = 'admin:graficos:read',
  ADMIN_GRAFICOS_EXPORT = 'admin:graficos:export',
  ADMIN_GRAFICOS_CONFIGURE = 'admin:graficos:configure',
  ADMIN_VIATICOS_CREATE = 'admin:viaticos:create',
  ADMIN_VIATICOS_READ = 'admin:viaticos:read',
  ADMIN_VIATICOS_UPDATE = 'admin:viaticos:update',
  ADMIN_VIATICOS_DELETE = 'admin:viaticos:delete',
  ADMIN_VIATICOS_LIST = 'admin:viaticos:list',
  ADMIN_VIATICOS_APPROVE = 'admin:viaticos:approve',
  ADMIN_TIEMPO_EXTRA_CREATE = 'admin:tiempo_extra:create',
  ADMIN_TIEMPO_EXTRA_READ = 'admin:tiempo_extra:read',
  ADMIN_TIEMPO_EXTRA_UPDATE = 'admin:tiempo_extra:update',
  ADMIN_TIEMPO_EXTRA_DELETE = 'admin:tiempo_extra:delete',
  ADMIN_TIEMPO_EXTRA_LIST = 'admin:tiempo_extra:list',
  ADMIN_TIEMPO_EXTRA_APPROVE = 'admin:tiempo_extra:approve',
  ADMIN_PAQUETERIA_CREATE = 'admin:paqueteria:create',
  ADMIN_PAQUETERIA_READ = 'admin:paqueteria:read',
  ADMIN_PAQUETERIA_UPDATE = 'admin:paqueteria:update',
  ADMIN_PAQUETERIA_DELETE = 'admin:paqueteria:delete',
  ADMIN_PAQUETERIA_LIST = 'admin:paqueteria:list',
  ADMIN_PAQUETERIA_TRACK = 'admin:paqueteria:track',
  ADMIN_SOLICITUDES_CREATE = 'admin:solicitudes:create',
  ADMIN_SOLICITUDES_READ = 'admin:solicitudes:read',
  ADMIN_SOLICITUDES_UPDATE = 'admin:solicitudes:update',
  ADMIN_SOLICITUDES_DELETE = 'admin:solicitudes:delete',
  ADMIN_SOLICITUDES_APPROVE = 'admin:solicitudes:approve',
  ADMIN_NUM_FISCAL_CREATE = 'admin:num_fiscal:create',
  ADMIN_NUM_FISCAL_READ = 'admin:num_fiscal:read',
  ADMIN_NUM_FISCAL_UPDATE = 'admin:num_fiscal:update',
  ADMIN_NUM_FISCAL_VALIDATE = 'admin:num_fiscal:validate',
  ADMIN_SOLICITUD_CREDITO_CREATE = 'admin:solicitud_credito:create',
  ADMIN_SOLICITUD_CREDITO_READ = 'admin:solicitud_credito:read',
  ADMIN_SOLICITUD_CREDITO_UPDATE = 'admin:solicitud_credito:update',
  ADMIN_SOLICITUD_CREDITO_DELETE = 'admin:solicitud_credito:delete',
  ADMIN_SOLICITUD_CREDITO_APPROVE = 'admin:solicitud_credito:approve',
  ADMIN_SOLICITUD_CREDITO_EVALUATE = 'admin:solicitud_credito:evaluate',
  ADMIN_FINANCES_READ = 'admin:finances:read',
  ADMIN_FINANCES_UPDATE = 'admin:finances:update',
  ADMIN_FINANCES_APPROVE = 'admin:finances:approve',
  ADMIN_FINANCES_REPORT = 'admin:finances:report',
  ADMIN_BUDGETS_CREATE = 'admin:budgets:create',
  ADMIN_BUDGETS_READ = 'admin:budgets:read',
  ADMIN_BUDGETS_UPDATE = 'admin:budgets:update',
  ADMIN_BUDGETS_APPROVE = 'admin:budgets:approve',
  ADMIN_BUDGETS_MONITOR = 'admin:budgets:monitor',
  CLIENTS_PROFILE_READ = 'clients:profile:read',
  CLIENTS_PROFILE_UPDATE = 'clients:profile:update',
  CLIENTS_SERVICES_READ = 'clients:services:read',
  CLIENTS_SERVICES_REQUEST = 'clients:services:request',
  CLIENTS_SERVICES_TRACK = 'clients:services:track',
  CLIENTS_QUOTES_READ = 'clients:quotes:read',
  CLIENTS_QUOTES_REQUEST = 'clients:quotes:request',
  CLIENTS_QUOTES_APPROVE = 'clients:quotes:approve',
  CLIENTS_ORDERS_READ = 'clients:orders:read',
  CLIENTS_ORDERS_TRACK = 'clients:orders:track',
  CLIENTS_INVOICES_READ = 'clients:invoices:read',
  CLIENTS_INVOICES_DOWNLOAD = 'clients:invoices:download',
  CLIENTS_INVOICES_PAY = 'clients:invoices:pay',
  CLIENTS_SUPPORT_CREATE = 'clients:support:create',
  CLIENTS_SUPPORT_READ = 'clients:support:read',
  CLIENTS_SUPPORT_UPDATE = 'clients:support:update',
  CLIENTS_DOCUMENTS_READ = 'clients:documents:read',
  CLIENTS_DOCUMENTS_DOWNLOAD = 'clients:documents:download',
  CLIENTS_NOTIFICATIONS_READ = 'clients:notifications:read',
  CLIENTS_NOTIFICATIONS_MARK_READ = 'clients:notifications:mark_read',
}
