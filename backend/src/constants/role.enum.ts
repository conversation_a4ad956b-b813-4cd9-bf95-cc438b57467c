// /backend/src/constants/role.enum.ts

/**
 * @enum Role
 * @description Define los roles de usuario estandarizados en la aplicación.
 *
 * Utilizar un enum para los roles en lugar de strings directos ofrece
 * varias ventajas:
 * - **Consistencia**: Evita errores tipográficos al usar los mismos valores
 *   en todo el código (ej. `Role.Admin` en lugar de `"ROLE_ADMIN"`).
 * - **Autocompletado**: Los IDEs pueden sugerir los valores del enum,
 *   mejorando la productividad y reduciendo errores.
 * - **Refactorización Segura**: Si un nombre de rol cambia, solo necesita
 *   ser actualizado en un lugar.
 * - **Claridad**: Hace el código más legible y auto-documentado.
 */
export enum Role {
  Admin = 'ROLE_ADMIN',
  Manager = 'ROLE_MANAGER',
  Sales = 'ROLE_SALES',
  Finance = 'ROLE_FINANCE',
  Warehouse = 'ROLE_WAREHOUSE',
  User = 'ROLE_USER',
  Calidad = 'ROLE_CALIDAD',
  Logistica = 'ROLE_LOGISTICA',
  Metrologia = 'ROLE_METROLOGIA',
  RH = 'ROLE_RH',
  Sistemas = 'ROLE_SISTEMAS',
  Informes = 'ROLE_INFORMES',
  FullAdmin = 'ROLE_FULLADMIN',
}