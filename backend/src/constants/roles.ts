import { UserRole } from '../interfaces/user.interface';

export const roles = {
  ADMIN: 'admin' as UserRole,
  USER: 'user' as UserRole,
} as const;

export const roleHierarchy: Record<UserRole, number> = {
  admin: 2,
  user: 1,
};

export const hasPermission = (userRole: UserRole, requiredRole: UserRole): boolean => {
  return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
};

export const ROLES_KEY = 'roles';

export const Roles = (...roles: UserRole[]) => {
  return (target: any, key: string | symbol, descriptor: PropertyDescriptor) => {
    Reflect.defineMetadata(ROLES_KEY, roles, target, key);
    return descriptor;
  };
};
