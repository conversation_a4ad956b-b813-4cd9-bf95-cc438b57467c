import { IsEnum, IsString, IsOptional, IsNumber, IsDateString, IsBoolean, IsArray, ValidateNested, IsObject, Min, Max, IsUrl } from 'class-validator';
import { Type } from 'class-transformer';
import { RequestType, RequestStatus, ApprovalLevel } from '../entities/internal-request.entity';

// DTOs específicos para cada tipo de solicitud
export class ViaticosDataDto {
  @IsString()
  @IsOptional()
  destination?: string;

  @IsString()
  @IsOptional()
  purpose?: string;

  @IsNumber()
  @Min(0)
  @IsOptional()
  transportationCost?: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  accommodationCost?: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  mealsCost?: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  otherExpenses?: number;

  @IsString()
  @IsOptional()
  justification?: string;

  @IsArray()
  @IsOptional()
  @IsUrl({}, { each: true })
  receipts?: string[];
}

export class TiempoExtraDataDto {
  @IsDateString()
  @IsOptional()
  workDate?: string;

  @IsNumber()
  @Min(0.5)
  @Max(24)
  @IsOptional()
  hoursRequested?: number;

  @IsString()
  @IsOptional()
  reason?: string;

  @IsString()
  @IsOptional()
  project?: string;

  @IsString()
  @IsOptional()
  taskDescription?: string;

  @IsBoolean()
  @IsOptional()
  isWeekend?: boolean;

  @IsBoolean()
  @IsOptional()
  isHoliday?: boolean;

  @IsNumber()
  @Min(0)
  @IsOptional()
  hourlyRate?: number;
}

export class PaqueteriaDataDto {
  @IsString()
  @IsOptional()
  senderName?: string;

  @IsString()
  @IsOptional()
  senderAddress?: string;

  @IsString()
  @IsOptional()
  senderPhone?: string;

  @IsString()
  @IsOptional()
  recipientName?: string;

  @IsString()
  @IsOptional()
  recipientAddress?: string;

  @IsString()
  @IsOptional()
  recipientPhone?: string;

  @IsString()
  @IsOptional()
  packageDescription?: string;

  @IsNumber()
  @Min(0)
  @IsOptional()
  weight?: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  dimensions?: number;

  @IsString()
  @IsOptional()
  trackingNumber?: string;

  @IsString()
  @IsOptional()
  courierService?: string;

  @IsString()
  @IsOptional()
  specialInstructions?: string;

  @IsBoolean()
  @IsOptional()
  isFragile?: boolean;

  @IsBoolean()
  @IsOptional()
  requiresSignature?: boolean;
}

// DTOs principales
export class CreateInternalRequestDto {
  @IsEnum(RequestType)
  requestType: RequestType;

  @IsString()
  title: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsNumber()
  @Min(0)
  @IsOptional()
  estimatedCost?: number;

  @IsDateString()
  @IsOptional()
  startDate?: string;

  @IsDateString()
  @IsOptional()
  endDate?: string;

  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => Object)
  requestData?: ViaticosDataDto | TiempoExtraDataDto | PaqueteriaDataDto;

  @IsBoolean()
  @IsOptional()
  isUrgent?: boolean;

  @IsString()
  @IsOptional()
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';

  @IsArray()
  @IsOptional()
  @IsUrl({}, { each: true })
  attachments?: string[];
}

export class UpdateInternalRequestDto {
  @IsString()
  @IsOptional()
  title?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsNumber()
  @Min(0)
  @IsOptional()
  estimatedCost?: number;

  @IsDateString()
  @IsOptional()
  startDate?: string;

  @IsDateString()
  @IsOptional()
  endDate?: string;

  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => Object)
  requestData?: ViaticosDataDto | TiempoExtraDataDto | PaqueteriaDataDto;

  @IsBoolean()
  @IsOptional()
  isUrgent?: boolean;

  @IsString()
  @IsOptional()
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';

  @IsArray()
  @IsOptional()
  @IsUrl({}, { each: true })
  attachments?: string[];
}

export class SubmitInternalRequestDto {
  @IsString()
  @IsOptional()
  comment?: string;
}

export class ApproveInternalRequestDto {
  @IsNumber()
  @Min(0)
  @IsOptional()
  approvedAmount?: number;

  @IsString()
  @IsOptional()
  comment?: string;
}

export class RejectInternalRequestDto {
  @IsString()
  rejectionReason: string;

  @IsString()
  @IsOptional()
  comment?: string;
}

export class RequestInfoInternalRequestDto {
  @IsString()
  comment: string;
}

export class InternalRequestFilterDto {
  @IsEnum(RequestType)
  @IsOptional()
  requestType?: RequestType;

  @IsEnum(RequestStatus)
  @IsOptional()
  status?: RequestStatus;

  @IsNumber()
  @IsOptional()
  requesterId?: number;

  @IsNumber()
  @IsOptional()
  approverId?: number;

  @IsDateString()
  @IsOptional()
  startDate?: string;

  @IsDateString()
  @IsOptional()
  endDate?: string;

  @IsBoolean()
  @IsOptional()
  isUrgent?: boolean;

  @IsString()
  @IsOptional()
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';

  @IsString()
  @IsOptional()
  search?: string;

  @IsNumber()
  @IsOptional()
  page?: number;

  @IsNumber()
  @IsOptional()
  limit?: number;

  @IsString()
  @IsOptional()
  sortBy?: 'createdAt' | 'updatedAt' | 'title' | 'status' | 'priority' | 'estimatedCost';

  @IsString()
  @IsOptional()
  sortOrder?: 'ASC' | 'DESC';
}

export class InternalRequestResponseDto {
  id: number;
  requestType: RequestType;
  status: RequestStatus;
  title: string;
  description?: string;
  estimatedCost?: number;
  approvedAmount?: number;
  startDate?: Date;
  endDate?: Date;
  requestData?: Record<string, any>;
  approvalHistory?: Array<{
    level: ApprovalLevel;
    approverId: number;
    approverName: string;
    action: 'APPROVED' | 'REJECTED' | 'REQUEST_INFO';
    comment?: string;
    timestamp: Date;
  }>;
  rejectionReason?: string;
  attachments?: string;
  currentApprovalLevel: ApprovalLevel;
  isUrgent: boolean;
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  requesterId: number;
  approverId?: number;
  createdById?: number;
  updatedById?: number;
  createdAt: Date;
  updatedAt: Date;
  submittedAt?: Date;
  approvedAt?: Date;
  rejectedAt?: Date;
  
  // Datos relacionados
  requester?: {
    id: number;
    name: string;
    email: string;
  };
  approver?: {
    id: number;
    name: string;
    email: string;
  };
  createdBy?: {
    id: number;
    name: string;
    email: string;
  };
  updatedBy?: {
    id: number;
    name: string;
    email: string;
  };
}