import { IsEnum, IsInt, IsNotEmpty, IsOptional, IsPositive, IsString, MaxLength, MinLength } from 'class-validator';
import { MovementType, MovementStatus } from '../entities/inventory-movement.entity';

export class CreateInventoryMovementDto {
  @IsEnum(MovementType, { message: 'Tipo de movimiento inválido' })
  @IsNotEmpty({ message: 'El tipo de movimiento es requerido' })
  tipo: MovementType;

  @IsInt({ message: 'La cantidad debe ser un número entero' })
  @IsPositive({ message: 'La cantidad debe ser mayor a 0' })
  cantidad: number;

  @IsInt({ message: 'El ID del producto debe ser un número entero' })
  @IsPositive({ message: 'El ID del producto debe ser válido' })
  productoId: number;

  @IsOptional()
  @IsString({ message: 'El motivo debe ser una cadena de texto' })
  @MaxLength(500, { message: 'El motivo no puede exceder 500 caracteres' })
  motivo?: string;

  @IsOptional()
  @IsString({ message: 'Las observaciones deben ser una cadena de texto' })
  @MaxLength(1000, { message: 'Las observaciones no pueden exceder 1000 caracteres' })
  observaciones?: string;

  @IsOptional()
  @IsString({ message: 'Los datos QR deben ser una cadena de texto' })
  qrDataScanned?: string;
}

export class UpdateInventoryMovementDto {
  @IsOptional()
  @IsEnum(MovementStatus, { message: 'Estado de movimiento inválido' })
  estado?: MovementStatus;

  @IsOptional()
  @IsInt({ message: 'La cantidad debe ser un número entero' })
  @IsPositive({ message: 'La cantidad debe ser mayor a 0' })
  cantidad?: number;

  @IsOptional()
  @IsString({ message: 'El motivo debe ser una cadena de texto' })
  @MaxLength(500, { message: 'El motivo no puede exceder 500 caracteres' })
  motivo?: string;

  @IsOptional()
  @IsString({ message: 'Las observaciones deben ser una cadena de texto' })
  @MaxLength(1000, { message: 'Las observaciones no pueden exceder 1000 caracteres' })
  observaciones?: string;
}

export class ConfirmInventoryMovementDto {
  @IsOptional()
  @IsString({ message: 'Las observaciones deben ser una cadena de texto' })
  @MaxLength(1000, { message: 'Las observaciones no pueden exceder 1000 caracteres' })
  observaciones?: string;
}

export class InventoryMovementFilterDto {
  @IsOptional()
  @IsEnum(MovementType, { message: 'Tipo de movimiento inválido' })
  tipo?: MovementType;

  @IsOptional()
  @IsEnum(MovementStatus, { message: 'Estado de movimiento inválido' })
  estado?: MovementStatus;

  @IsOptional()
  @IsInt({ message: 'El ID del producto debe ser un número entero' })
  productoId?: number;

  @IsOptional()
  @IsInt({ message: 'El ID del usuario debe ser un número entero' })
  usuarioId?: number;

  @IsOptional()
  @IsString({ message: 'El folio debe ser una cadena de texto' })
  @MaxLength(50, { message: 'El folio no puede exceder 50 caracteres' })
  folio?: string;

  @IsOptional()
  @IsString({ message: 'La búsqueda debe ser una cadena de texto' })
  @MaxLength(100, { message: 'La búsqueda no puede exceder 100 caracteres' })
  search?: string;

  @IsOptional()
  @IsString({ message: 'La fecha desde debe ser una cadena de texto' })
  fechaDesde?: string;

  @IsOptional()
  @IsString({ message: 'La fecha hasta debe ser una cadena de texto' })
  fechaHasta?: string;

  @IsOptional()
  @IsInt({ message: 'La página debe ser un número entero' })
  @IsPositive({ message: 'La página debe ser mayor a 0' })
  page?: number = 1;

  @IsOptional()
  @IsInt({ message: 'El límite debe ser un número entero' })
  @IsPositive({ message: 'El límite debe ser mayor a 0' })
  limit?: number = 20;

  @IsOptional()
  @IsString({ message: 'El campo de ordenamiento debe ser una cadena de texto' })
  sortBy?: string = 'fechaMovimiento';

  @IsOptional()
  @IsString({ message: 'El orden debe ser una cadena de texto' })
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}

export class QRScanDataDto {
  @IsString({ message: 'Los datos QR deben ser una cadena de texto' })
  @IsNotEmpty({ message: 'Los datos QR son requeridos' })
  @MinLength(1, { message: 'Los datos QR no pueden estar vacíos' })
  qrData: string;

  @IsOptional()
  @IsEnum(MovementType, { message: 'Tipo de movimiento inválido' })
  tipo?: MovementType;
}

export class InventoryMovementResponseDto {
  id: number;
  folio: string;
  tipo: MovementType;
  estado: MovementStatus;
  cantidad: number;
  motivo?: string;
  observaciones?: string;
  fechaMovimiento: Date;
  fechaConfirmacion?: Date;
  producto?: {
    id: number;
    codigoItem: string;
    nombre: string;
    marca?: string;
    modelo?: string;
  } | null;
  usuario?: {
    id: number;
    name: string;
    email: string;
  } | null;
  confirmadoPor?: {
    id: number;
    name: string;
    email: string;
  } | null;
  voucherId?: number;
  voucherPath?: string;
  qrDataScanned?: string;
}

export class InventoryMovementStatsDto {
  totalMovements: number;
  pendingMovements: number;
  confirmedMovements: number;
  cancelledMovements: number;
  todayMovements: number;
  thisWeekMovements: number;
  thisMonthMovements: number;
}

export class BulkInventoryMovementDto {
  @IsNotEmpty({ message: 'Se requiere al menos un movimiento' })
  movements: CreateInventoryMovementDto[];
}