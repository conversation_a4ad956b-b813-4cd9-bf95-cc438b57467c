export type UserRole = 'admin' | 'user';

export interface IUser {
  id?: number;
  name: string;
  email: string;
  password: string;
  refreshToken?: string | null;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IUserResponse {
  id: number;
  name: string;
  email: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IUserWithTokens extends IUserResponse {
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

export interface ILoginResponse {
  user: IUserResponse;
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

export interface IRefreshTokenResponse {
  accessToken: string;
  refreshToken: string;
}
