import { Request } from 'express';
import { User } from '../entities/user.entity';
import { AdminController } from '../controllers/admin.controller';

export interface TokenPayload {
  sub: number;
  email: string;
  type: 'access' | 'refresh';
}

export interface RequestWithUser extends Request {
  user?: User;
  adminController?: AdminController;
}

export interface AuthenticatedRequest extends Request {
  user?: User;
}

export interface UserJwtPayload {
  id: number;
  email: string;
}

export interface PaginationOptions {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}
