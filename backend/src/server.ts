// /backend/src/server.ts
import 'reflect-metadata';
import { DataSource } from 'typeorm';
import { App } from './app';
import { config } from './config/config';
import { AppDataSource } from './data-source';
import { initializeWebSocketService } from './services/websocket.service';
import { NotificationService } from './services/notification.service';

/**
 * @class Server
 * @description Orquesta el arranque de la aplicación.
 *
 * Responsabilidades:
 * - Establecer y gestionar la conexión con la base de datos.
 * - Instanciar y arrancar el servidor Express.
 * - Inicializar el servidor WebSocket para notificaciones en tiempo real.
 * - Manejar el cierre limpio (`graceful shutdown`).
 */
class Server {
  private app: App | undefined;
  private dataSource: DataSource;

  constructor() {
    this.dataSource = AppDataSource;
  }

  /**
   * @description Inicia la aplicación: conecta a la DB y levanta el servidor.
   */
  public async start(): Promise<void> {
    try {
      // 1. Conectar a la base de datos
      console.log('🔌 Conectando a la base de datos...');
      await this.dataSource.initialize();
      console.log('✅ Conexión a la base de datos establecida correctamente.');

      // 2. Crear instancias de servicios clave DESPUÉS de inicializar la BD
      console.log('🔧 Inicializando servicios...');
      const notificationService = new NotificationService();

      // 3. Iniciar el servidor Express con todos los servicios
      this.app = new App(this.dataSource, { notificationService });
      const server = this.app.listen(config.server.port, () => {
        console.log(`🚀 Servidor HTTP ejecutándose en http://localhost:${config.server.port}`);
        console.log(`   Entorno: ${config.server.env}`);
      });

      // 4. Inicializar el servidor WebSocket
      console.log('🔌 Iniciando servidor WebSocket...');
      initializeWebSocketService(server, notificationService);
      console.log('✅ Servidor WebSocket inicializado correctamente.');

      // 5. Configurar el cierre limpio (Graceful Shutdown)
      this.setupGracefulShutdown(server);

    } catch (error) {
      console.error('❌ Error fatal al iniciar el servidor:', error);
      process.exit(1);
    }
  }

  /**
   * @description Configura el manejo de señales para un cierre limpio.
   * @param server El servidor HTTP a cerrar.
   */
  private setupGracefulShutdown(server: import('http').Server): void {
    const gracefulShutdown = async (signal: string) => {
      console.log(`\n🔴 Recibida señal de apagado (${signal}). Cerrando conexiones...`);
      
      server.close(async () => {
        console.log('✅ Servidor HTTP cerrado.');
        console.log('✅ Servidor WebSocket cerrado.');
        
        if (this.dataSource.isInitialized) {
          await this.dataSource.destroy();
          console.log('✅ Conexión a la base de datos cerrada.');
        }
        
        process.exit(0);
      });
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  }
}

// Iniciar la aplicación
const server = new Server();
server.start();
