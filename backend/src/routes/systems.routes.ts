import { Router } from 'express';
import { DataSource } from 'typeorm';
import { UserController } from '../modules/systems/controllers/user.controller';
import { RoleController } from '../modules/systems/controllers/role.controller';
import { PermissionController } from '../modules/systems/controllers/permission.controller';
import { AuditController } from '../modules/systems/controllers/audit.controller';
import { BackupController } from '../modules/systems/controllers/backup.controller';
import { createAuthMiddleware } from '../modules/systems/middleware/auth.middleware';
import { permissionMiddleware } from '../modules/systems/middleware/permission.middleware';
import { AppDataSource } from '../data-source';

function createSystemsRoutes(dataSource?: DataSource): Router {
  const router = Router();
  
  // Usar el dataSource apropiado (test o producción)
  const currentDataSource = dataSource || AppDataSource;
  const authMiddleware = createAuthMiddleware(currentDataSource);
  
  // Crear instancias de controladores con el dataSource correcto
  const userController = new UserController(currentDataSource);
  const roleController = new RoleController();
  const permissionController = new PermissionController();
  const auditController = new AuditController();
  const backupController = new BackupController();

  // --- Rutas de Usuarios ---
  router.get('/users', authMiddleware, permissionMiddleware(['sistemas:users:read']), userController.getUsers.bind(userController));
  router.get('/users/:id', authMiddleware, permissionMiddleware(['sistemas:users:read']), userController.getUser.bind(userController));
  router.post('/users', authMiddleware, permissionMiddleware(['sistemas:users:create']), userController.createUser.bind(userController));
  router.put('/users/:id', authMiddleware, permissionMiddleware(['sistemas:users:update']), userController.updateUser.bind(userController));
  router.delete('/users/:id', authMiddleware, permissionMiddleware(['sistemas:users:delete']), userController.deleteUser.bind(userController));
  router.post('/users/:id/activate', authMiddleware, permissionMiddleware(['sistemas:users:update']), userController.activateUser.bind(userController));
  router.post('/users/:id/deactivate', authMiddleware, permissionMiddleware(['sistemas:users:update']), userController.deactivateUser.bind(userController));
  router.post('/users/assign-roles', authMiddleware, permissionMiddleware(['sistemas:users:update']), userController.assignRoles.bind(userController));
  router.get('/users/areas', authMiddleware, permissionMiddleware(['sistemas:users:read']), userController.getAreas.bind(userController));
  router.put('/users/:id/status', authMiddleware, permissionMiddleware(['sistemas:users:update']), userController.updateUserStatus.bind(userController));
  router.put('/users/:id/admin-change-password', authMiddleware, permissionMiddleware(['sistemas:users:update']), userController.adminChangePassword.bind(userController));

  // --- Rutas de Roles ---
  router.get('/roles', authMiddleware, permissionMiddleware(['sistemas:roles:read']), roleController.getRoles.bind(roleController));
  router.get('/roles/:id', authMiddleware, permissionMiddleware(['sistemas:roles:read']), roleController.getRole.bind(roleController));
  router.post('/roles', authMiddleware, permissionMiddleware(['sistemas:roles:create']), roleController.createRole.bind(roleController));
  router.put('/roles/:id', authMiddleware, permissionMiddleware(['sistemas:roles:update']), roleController.updateRole.bind(roleController));
  router.delete('/roles/:id', authMiddleware, permissionMiddleware(['sistemas:roles:delete']), roleController.deleteRole.bind(roleController));
  router.post('/roles/assign-permissions', authMiddleware, permissionMiddleware(['sistemas:roles:update']), roleController.assignPermissionsToRole.bind(roleController));

  // --- Rutas de Permisos ---
  router.get('/permissions', authMiddleware, permissionMiddleware(['sistemas:permissions:read']), permissionController.getPermissions.bind(permissionController));

  // --- Rutas de Auditoría ---
  router.get('/audit', authMiddleware, permissionMiddleware(['sistemas:audit:read']), auditController.getAuditLogs.bind(auditController));

  // --- Rutas de Backup ---
  router.get('/backups', authMiddleware, permissionMiddleware(['sistemas:backup:create']), backupController.getAllBackups.bind(backupController));
  router.get('/backups/stats', authMiddleware, permissionMiddleware(['sistemas:backup:create']), backupController.getBackupStats.bind(backupController));
  router.post('/backups', authMiddleware, permissionMiddleware(['sistemas:backup:create']), backupController.createBackup.bind(backupController));
  router.get('/backups/:id', authMiddleware, permissionMiddleware(['sistemas:backup:download']), backupController.getBackupById.bind(backupController));
  router.delete('/backups/:id', authMiddleware, permissionMiddleware(['sistemas:backup:restore']), backupController.deleteBackup.bind(backupController));
  router.get('/backups/:id/download', authMiddleware, permissionMiddleware(['sistemas:backup:download']), backupController.downloadBackup.bind(backupController));
  router.post('/backups/:id/restore', authMiddleware, permissionMiddleware(['sistemas:backup:restore']), backupController.restoreBackup.bind(backupController));
  router.post('/backups/schedule', authMiddleware, permissionMiddleware(['sistemas:backup:create']), backupController.scheduleBackup.bind(backupController));
  router.post('/backups/cleanup', authMiddleware, permissionMiddleware(['sistemas:backup:create']), backupController.cleanupExpiredBackups.bind(backupController));

  // --- Rutas de Backup Upload ---
  router.post('/backups/upload', authMiddleware, permissionMiddleware(['sistemas:backup:create']), backupController.uploadMiddleware, backupController.uploadBackup.bind(backupController));
  router.post('/backups/execute-uploaded', authMiddleware, permissionMiddleware(['sistemas:backup:restore']), backupController.executeUploadedBackup.bind(backupController));

  return router;
}

// Export default para uso normal
const router = createSystemsRoutes();

export default router;
export { createSystemsRoutes };