import { Router } from 'express';
import { DataSource } from 'typeorm';
import { InventoryMovementController } from '../controllers/inventory-movement.controller';
import { NotificationService } from '../services/notification.service';
import { createAuthMiddleware } from '../middlewares/auth.middleware';
import { validationMiddleware } from '../middlewares/validation.middleware';
import { 
  CreateInventoryMovementDto, 
  UpdateInventoryMovementDto, 
  ConfirmInventoryMovementDto,
  QRScanDataDto,
  BulkInventoryMovementDto
} from '../dto/inventory-movement.dto';

export function createInventoryMovementRoutes(dataSource: DataSource, notificationService: NotificationService): Router {
  const router = Router();
  const movementController = new InventoryMovementController(dataSource, notificationService);
  const authMiddleware = createAuthMiddleware(dataSource);

// Aplicar middleware de autenticación a todas las rutas
router.use(authMiddleware);

// Rutas principales de movimientos
router.post('/', 
  validationMiddleware(CreateInventoryMovementDto), 
  movementController.createMovement.bind(movementController)
);

router.get('/', 
  movementController.getMovements.bind(movementController)
);

router.get('/stats/overview', 
  movementController.getMovementStats.bind(movementController)
);

// Rutas de movimientos específicos
router.get('/:id', 
  movementController.getMovementById.bind(movementController)
);

router.put('/:id', 
  validationMiddleware(UpdateInventoryMovementDto), 
  movementController.updateMovement.bind(movementController)
);

router.post('/:id/confirm', 
  validationMiddleware(ConfirmInventoryMovementDto), 
  movementController.confirmMovement.bind(movementController)
);

router.post('/:id/cancel', 
  movementController.cancelMovement.bind(movementController)
);

// Rutas de QR
router.post('/qr/scan', 
  validationMiddleware(QRScanDataDto), 
  movementController.processQRData.bind(movementController)
);

router.post('/qr/create', 
  validationMiddleware(CreateInventoryMovementDto), 
  movementController.createMovementFromQR.bind(movementController)
);

// Rutas de filtros específicos
router.get('/type/:tipo', 
  movementController.getMovementsByType.bind(movementController)
);

router.get('/status/:estado', 
  movementController.getMovementsByStatus.bind(movementController)
);

router.get('/product/:productoId', 
  movementController.getMovementsByProduct.bind(movementController)
);

router.get('/user/me', 
  movementController.getMyMovements.bind(movementController)
);

router.get('/pending/list', 
  movementController.getPendingMovements.bind(movementController)
);

router.get('/recent/list', 
  movementController.getRecentMovements.bind(movementController)
);

// Rutas de búsqueda
router.get('/search/folio/:folio', 
  movementController.searchByFolio.bind(movementController)
);

router.get('/summary/date-range', 
  movementController.getMovementsSummary.bind(movementController)
);

// Rutas de operaciones masivas
router.post('/bulk', 
  validationMiddleware(BulkInventoryMovementDto), 
  movementController.createBulkMovements.bind(movementController)
);

  return router;
}