import { Router } from 'express';
import { DataSource } from 'typeorm';
import { ProductController } from '../controllers/product.controller';
import { createAuthMiddleware } from '../middlewares/auth.middleware';
import { NotificationService } from '../services/notification.service';

export function createProductRoutes(dataSource: DataSource, notificationService: NotificationService): Router {
  const router = Router();
  const productController = new ProductController(dataSource, notificationService);
  const auth = createAuthMiddleware(dataSource);

  // =============================
  // RUTAS PRODUCTOS (ALMACÉN)
  // =============================

  /**
   * Crear nuevo producto
   * POST /api/products
   */
  router.post('/', auth, (req, res) => 
    productController.createProduct(req, res)
  );

  /**
   * Obtener todos los productos con filtros y paginación
   * GET /api/products
   */
  router.get('/', (req, res) => 
    productController.getAllProducts(req, res)
  );

  /**
   * Obtener productos con stock bajo
   * GET /api/products/low-stock
   */
  router.get('/low-stock', (req, res) => 
    productController.getLowStockProducts(req, res)
  );

  /**
   * Obtener estadísticas de inventario
   * GET /api/products/stats
   */
  router.get('/stats', (req, res) => 
    productController.getInventoryStats(req, res)
  );

  /**
   * Obtener ubicaciones/bodegas disponibles
   * GET /api/products/locations
   */
  router.get('/locations', (req, res) => 
    productController.getAvailableLocations(req, res)
  );

  /**
   * Obtener producto por código
   * GET /api/products/by-code/:code
   */
  router.get('/by-code/:code', (req, res) => 
    productController.getProductByCode(req, res)
  );

  /**
   * Obtener producto por ID
   * GET /api/products/:id
   */
  router.get('/:id', (req, res) => 
    productController.getProductById(req, res)
  );

  /**
   * Actualizar producto
   * PUT /api/products/:id
   */
  router.put('/:id', auth, (req, res) => 
    productController.updateProduct(req, res)
  );

  /**
   * Eliminar producto
   * DELETE /api/products/:id
   */
  router.delete('/:id', auth, (req, res) => 
    productController.deleteProduct(req, res)
  );

  /**
   * Reservar stock de un producto
   * POST /api/products/:id/reserve
   */
  router.post('/:id/reserve', auth, (req, res) => 
    productController.reserveStock(req, res)
  );

  /**
   * Ajustar stock (entrada/salida de inventario)
   * POST /api/products/:id/adjust-stock
   */
  router.post('/:id/adjust-stock', auth, (req, res) => 
    productController.adjustStock(req, res)
  );

  return router;
} 