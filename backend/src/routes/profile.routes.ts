import { Router } from 'express';
import { DataSource } from 'typeorm';
import { createAuthMiddleware } from '../middlewares/auth.middleware';
import { validationMiddleware, ValidationSource } from '../middlewares/validation.middleware';
import { ProfileController } from '../controllers/profile.controller';
import { UpdateProfileDto, UpdatePasswordDto } from '../dtos/profile.dto';

export function createProfileRoutes(dataSource: DataSource): Router {
  const router = Router();
  const profileController = new ProfileController(dataSource);
  const auth = createAuthMiddleware(dataSource);

  // Aplicar middleware de autenticación a todas las rutas
  router.use(auth);

  // Obtener perfil del usuario autenticado
  router.get('/', profileController.getProfile);

  // Actualizar perfil del usuario autenticado
  router.patch(
    '/',
    validationMiddleware(UpdateProfileDto, ValidationSource.Body, true),
    profileController.updateProfile
  );

  // Actualizar contraseña del usuario autenticado
  router.patch(
    '/password',
    validationMiddleware(UpdatePasswordDto),
    profileController.updatePassword
  );

  return router;
}
