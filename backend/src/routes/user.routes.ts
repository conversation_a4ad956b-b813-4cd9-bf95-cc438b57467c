import { Router } from 'express';
import { DataSource } from 'typeorm';
import { UserController } from '../controllers/user.controller';
import { ValidationSource } from '../middlewares/validation.middleware';
import { validationMiddleware } from '../middlewares/validation.middleware';
import { CreateUserDto, UpdateUserDto, ChangePasswordDto } from '../dtos/user.dto';
import { Role } from '../constants/role.enum';
import { createAuthMiddleware } from '../middlewares/auth.middleware';

export function createUserRoutes(dataSource: DataSource): Router {
  const router = Router();
  const userController = new UserController(dataSource); 
  const auth = createAuthMiddleware(dataSource);
  const adminOnly = createAuthMiddleware(dataSource, [Role.Admin]);

  // Obtener todos los usuarios (solo administradores)
  router.get(
    '/',
    adminOnly,
    userController.getAllUsers.bind(userController)
  );

  // Obtener el perfil del usuario actual
  router.get('/profile', auth, userController.getProfile.bind(userController));

  // Obtener un usuario por ID (solo administradores)
  router.get(
    '/:id',
    adminOnly,
    userController.getUserById.bind(userController)
  );

  // Crear un nuevo usuario (solo administradores)
  router.post(
    '/',
    adminOnly,
    validationMiddleware(CreateUserDto),
    userController.createUser.bind(userController)
  );

  // Actualizar un usuario (administradores o el propio usuario)
  router.put(
    '/:id',
    auth,
    validationMiddleware(UpdateUserDto, ValidationSource.Body, true),
    userController.updateUser.bind(userController)
  );

  // Cambiar contraseña (propio usuario o administradores)
  router.put(
    '/:id/change-password',
    auth,
    validationMiddleware(ChangePasswordDto),
    userController.changePassword.bind(userController)
  );

  // Bloquear usuario (solo administradores)
  router.put(
    '/:id/block',
    adminOnly,
    userController.blockUser.bind(userController)
  );

  // Desbloquear usuario (solo administradores)
  router.put(
    '/:id/unblock',
    adminOnly,
    userController.unblockUser.bind(userController)
  );

  // Inactivar usuario (solo administradores)
  router.put(
    '/:id/inactivate',
    adminOnly,
    userController.inactivateUser.bind(userController)
  );

  // Eliminar un usuario (solo administradores)
  router.delete(
    '/:id',
    adminOnly,
    userController.deleteUser.bind(userController)
  );

  return router;
}
