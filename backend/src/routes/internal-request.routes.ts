import { Router } from 'express';
import { DataSource } from 'typeorm';
import { InternalRequestController } from '../controllers/internal-request.controller';
import { NotificationService } from '../services/notification.service';
import { createAuthMiddleware } from '../middlewares/auth.middleware';
import { validationMiddleware } from '../middlewares/validation.middleware';
import {
  CreateInternalRequestDto,
  UpdateInternalRequestDto,
  SubmitInternalRequestDto,
  ApproveInternalRequestDto,
  RejectInternalRequestDto,
  RequestInfoInternalRequestDto
} from '../dto/internal-request.dto';

export function createInternalRequestRoutes(dataSource: DataSource, notificationService: NotificationService): Router {
  const router = Router();
  const requestController = new InternalRequestController(dataSource, notificationService);
  const authMiddleware = createAuthMiddleware(dataSource);

  // Aplicar middleware de autenticación a todas las rutas
  router.use(authMiddleware);

  // Rutas principales de solicitudes
  router.post('/',
    validationMiddleware(CreateInternalRequestDto),
    requestController.createRequest.bind(requestController)
  );

  router.get('/',
    requestController.getRequests.bind(requestController)
  );

  router.get('/stats/overview',
    requestController.getRequestStats.bind(requestController)
  );

  // Rutas de solicitudes específicas
  router.get('/:id',
    requestController.getRequestById.bind(requestController)
  );

  router.put('/:id',
    validationMiddleware(UpdateInternalRequestDto),
    requestController.updateRequest.bind(requestController)
  );

  // Rutas de workflow
  router.post('/:id/submit',
    validationMiddleware(SubmitInternalRequestDto),
    requestController.submitRequest.bind(requestController)
  );

  router.post('/:id/approve',
    validationMiddleware(ApproveInternalRequestDto),
    requestController.approveRequest.bind(requestController)
  );

  router.post('/:id/reject',
    validationMiddleware(RejectInternalRequestDto),
    requestController.rejectRequest.bind(requestController)
  );

  router.post('/:id/request-info',
    validationMiddleware(RequestInfoInternalRequestDto),
    requestController.requestInfo.bind(requestController)
  );

  router.post('/:id/cancel',
    requestController.cancelRequest.bind(requestController)
  );

  // Rutas de filtros específicos
  router.get('/type/:type',
    requestController.getRequestsByType.bind(requestController)
  );

  router.get('/status/:status',
    requestController.getRequestsByStatus.bind(requestController)
  );

  // Rutas de usuario autenticado
  router.get('/my-requests',
    requestController.getMyRequests.bind(requestController)
  );

  router.get('/pending-approval',
    requestController.getPendingApprovalRequests.bind(requestController)
  );

  return router;
}