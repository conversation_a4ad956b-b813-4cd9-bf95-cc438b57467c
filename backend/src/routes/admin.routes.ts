import { Router } from 'express';
import { DataSource } from 'typeorm';
import { AdminController } from '../controllers/admin.controller';
import { createAuthMiddleware } from '../middlewares/auth.middleware';
import { RequestWithUser } from '../interfaces/request.interface';
import { Role } from '../constants/role.enum';
import multer from 'multer';

const storage = multer.memoryStorage();
const upload = multer({
    storage: storage,
    limits: { fileSize: 10 * 1024 * 1024 } // Limite de 10MB por archivo
});

// Extender la interfaz Request para incluir adminController
interface RequestWithAdminController extends RequestWithUser {
  adminController?: AdminController;
  testDataSource?: DataSource;
}

export function createAdminRoutes(dataSource: DataSource): Router {
  const router = Router();

  // Crear funciones de autenticación con diferentes roles
  const auth = createAuthMiddleware(dataSource);
  const adminOnly = createAuthMiddleware(dataSource, [Role.Admin]);
  const adminManager = createAuthMiddleware(dataSource, [Role.Admin, Role.Manager]);
  const adminFinance = createAuthMiddleware(dataSource, [Role.Admin, Role.Finance]);
  const adminSales = createAuthMiddleware(dataSource, [Role.Admin, Role.Sales]);
  const warehouseUsers = createAuthMiddleware(dataSource, [Role.Admin, Role.Manager, Role.User, Role.Warehouse]);
  const warehouseManagers = createAuthMiddleware(dataSource, [Role.Admin, Role.Manager, Role.Warehouse]);

  // Middleware para configurar el controlador con el data source correcto
  const configureController = (req: RequestWithAdminController, _res: any, next: any) => {
    // Si hay un data source de prueba disponible, usarlo
    if (req.testDataSource) {
      req.adminController = new AdminController(req.testDataSource);
    } else {
      req.adminController = new AdminController(dataSource);
    }
    next();
  };

  // Aplicar el middleware de configuración a todas las rutas
  router.use(configureController);

  // === Solicitudes de Paquetería ===
  router.post(
      '/package-requests',
      warehouseUsers, // Usuarios pueden crear
      (req: RequestWithAdminController, res, next) => req.adminController?.createPackageRequest(req, res, next)
  );
  router.get(
      '/package-requests',
      warehouseManagers,
      (req: RequestWithAdminController, res, next) => req.adminController?.getAllPackageRequests(req, res, next)
  );
  router.get(
      '/package-requests/:id',
      warehouseUsers, // Usuario creador también debería poder ver
      (req: RequestWithAdminController, res, next) => req.adminController?.getPackageRequestById(req, res, next)
  );
  router.put(
      '/package-requests/:id',
      warehouseManagers,
      (req: RequestWithAdminController, res, next) => req.adminController?.updatePackageRequest(req, res, next)
  );
  router.delete(
      '/package-requests/:id',
      warehouseManagers,
      (req: RequestWithAdminController, res, next) => req.adminController?.deletePackageRequest(req, res, next)
  );

  // === Asignación de Números Fiscales ===
  router.post(
      '/fiscal-numbers',
      adminFinance,
      (req: RequestWithAdminController, res, next) => req.adminController?.assignFiscalNumber(req, res, next)
  );
  router.get(
      '/fiscal-numbers',
      adminFinance,
      (req: RequestWithAdminController, res, next) => req.adminController?.getFiscalNumbers(req, res, next)
  );
  router.get(
      '/fiscal-numbers/:id',
      adminFinance,
      (req: RequestWithAdminController, res, next) => req.adminController?.getFiscalNumberByIdController(req, res, next)
  );
  router.delete(
      '/fiscal-numbers/:id',
      adminFinance,
      (req: RequestWithAdminController, res, next) => req.adminController?.deleteFiscalNumber(req, res, next)
  );

  // === Contratos Repse ===
  router.post(
      '/contracts-repse',
      adminSales,
      (req: RequestWithAdminController, res, next) => req.adminController?.createContractRepse(req, res, next)
  );
  router.get(
      '/contracts-repse',
      adminSales,
      (req: RequestWithAdminController, res, next) => req.adminController?.getAllContractsRepse(req, res, next)
  );
  router.get(
      '/contracts-repse/:id',
      adminSales,
      (req: RequestWithAdminController, res, next) => req.adminController?.getContractRepseById(req, res, next)
  );
  router.put(
      '/contracts-repse/:id',
      adminSales,
      (req: RequestWithAdminController, res, next) => req.adminController?.updateContractRepse(req, res, next)
  );
  router.delete(
      '/contracts-repse/:id',
      adminSales,
      (req: RequestWithAdminController, res, next) => req.adminController?.deleteContractRepse(req, res, next)
  );

  // === Facturas ===
  const invoiceViewers = createAuthMiddleware(dataSource, [Role.Admin, Role.Manager, Role.Finance, Role.Sales]);
  
  router.post(
      '/invoices',
      adminFinance,
      (req: RequestWithAdminController, res, next) => req.adminController?.createInvoice(req, res, next)
  );
  router.post(
      '/invoices/:invoiceId/documents',
      adminFinance,
      upload.single('invoiceFile'),
      (req: RequestWithAdminController, res, next) => req.adminController?.uploadInvoiceFile(req, res, next)
  );
  router.get(
      '/invoices',
      invoiceViewers,
      (req: RequestWithAdminController, res, next) => req.adminController?.getAllInvoices(req, res, next)
  );
  router.get(
      '/invoices/:id',
      invoiceViewers,
      (req: RequestWithAdminController, res, next) => req.adminController?.getInvoiceById(req, res, next)
  );
  router.put(
      '/invoices/:id',
      adminFinance,
      (req: RequestWithAdminController, res, next) => req.adminController?.updateInvoice(req, res, next)
  );
  router.patch(
      '/invoices/:id/payment-status',
      adminFinance,
      (req: RequestWithAdminController, res, next) => req.adminController?.updateInvoicePaymentStatus(req, res, next)
  );
  router.delete(
      '/invoices/:id',
      adminOnly,
      (req: RequestWithAdminController, res, next) => req.adminController?.deleteInvoice(req, res, next)
  );
  router.delete(
      '/invoices/:invoiceId/documents/:documentId',
      adminFinance,
      (req: RequestWithAdminController, res, next) => req.adminController?.deleteInvoiceFile(req, res, next)
  );

  // === Solicitudes de Crédito ===
  const creditRequestUsers = createAuthMiddleware(dataSource, [Role.Admin, Role.Manager, Role.Sales, Role.Finance]);
  const creditRequestManagers = createAuthMiddleware(dataSource, [Role.Admin, Role.Manager, Role.Finance]);
  
  router.post(
      '/credit-requests',
      creditRequestUsers,
      (req: RequestWithAdminController, res, next) => req.adminController?.createCreditRequest(req, res, next)
  );
  router.post(
      '/credit-requests/:creditRequestId/documents',
      creditRequestUsers,
      upload.single('creditRequestFile'),
      (req: RequestWithAdminController, res, next) => req.adminController?.uploadCreditRequestFile(req, res, next)
  );
  router.get(
      '/credit-requests',
      creditRequestUsers,
      (req: RequestWithAdminController, res, next) => req.adminController?.getAllCreditRequests(req, res, next)
  );
  router.get(
      '/credit-requests/:id',
      creditRequestUsers,
      (req: RequestWithAdminController, res, next) => req.adminController?.getCreditRequestById(req, res, next)
  );
  router.patch(
      '/credit-requests/:id/status',
      creditRequestManagers,
      (req: RequestWithAdminController, res, next) => req.adminController?.updateCreditRequestStatus(req, res, next)
  );
  router.get(
      '/credit-requests/:creditRequestId/certificate',
      creditRequestManagers,
      (req: RequestWithAdminController, res, next) => req.adminController?.generateCreditCertificate(req, res, next)
  );
  router.delete(
      '/credit-requests/:id',
      adminManager,
      (req: RequestWithAdminController, res, next) => req.adminController?.deleteCreditRequest(req, res, next)
  );

  // === "Mis Solicitudes" ===
  router.get(
      '/me/requests', // Ruta más específica para el usuario actual
      auth,
      (req: RequestWithAdminController, res, next) => req.adminController?.getMyRequests(req, res, next)
  );

  // === Dashboard / Gráficos ===
  router.get(
      '/dashboard/graphs',
      adminManager,
      (req: RequestWithAdminController, res, next) => req.adminController?.getAdminDashboardData(req, res, next)
  );
  router.get(
      '/dashboard/graphs/download',
      adminManager,
      (req: RequestWithAdminController, res, next) => req.adminController?.downloadAdminDashboardExcel(req, res, next)
  );

  // === Solicitudes de Viáticos ===
  router.post(
    '/travel-requests',
    warehouseUsers, // Usuarios pueden crear
    (req: RequestWithAdminController, res, next) => req.adminController?.createTravelRequest(req, res, next)
  );
  // === Solicitudes de Tiempo Extra ===
  router.post(
    '/overtime-requests',
    warehouseUsers, // Usuarios pueden crear
    (req: RequestWithAdminController, res, next) => req.adminController?.createOvertimeRequest(req, res, next)
  );

  // === Aprobación de Solicitudes de Viáticos ===
  router.patch(
    '/travel-requests/:id/status',
    adminManager,
    (req: RequestWithAdminController, res, next) => req.adminController?.updateTravelRequestStatus(req, res, next)
  );

  // === Aprobación de Solicitudes de Tiempo Extra ===
  router.patch(
    '/overtime-requests/:id/status',
    adminManager,
    (req: RequestWithAdminController, res, next) => req.adminController?.updateOvertimeRequestStatus(req, res, next)
  );

  // === Edición personal de Solicitudes de Viáticos ===
  router.put(
    '/travel-requests/:id',
    auth,
    (req: RequestWithAdminController, res, next) => req.adminController?.updateTravelRequest(req, res, next)
  );

  // === Edición personal de Solicitudes de Tiempo Extra ===
  router.put(
    '/overtime-requests/:id',
    auth,
    (req: RequestWithAdminController, res, next) => req.adminController?.updateOvertimeRequest(req, res, next)
  );

  // === Borrado personal de Solicitudes de Viáticos ===
  router.delete(
    '/travel-requests/:id',
    auth,
    (req: RequestWithAdminController, res, next) => req.adminController?.deleteTravelRequest(req, res, next)
  );

  // === Borrado personal de Solicitudes de Tiempo Extra ===
  router.delete(
    '/overtime-requests/:id',
    auth,
    (req: RequestWithAdminController, res, next) => req.adminController?.deleteOvertimeRequest(req, res, next)
  );

  // === Consultas personales de Solicitudes de Viáticos ===
  router.get(
    '/travel-requests/mine',
    auth,
    (req: RequestWithAdminController, res, next) => req.adminController?.getMyTravelRequests(req, res, next)
  );
  router.get(
    '/travel-requests/:id',
    auth,
    (req: RequestWithAdminController, res, next) => req.adminController?.getMyTravelRequestById(req, res, next)
  );

  // === Consultas personales de Solicitudes de Tiempo Extra ===
  router.get(
    '/overtime-requests/mine',
    auth,
    (req: RequestWithAdminController, res, next) => req.adminController?.getMyOvertimeRequests(req, res, next)
  );
  router.get(
    '/overtime-requests/:id',
    auth,
    (req: RequestWithAdminController, res, next) => req.adminController?.getMyOvertimeRequestById(req, res, next)
  );

  // === Admin/global listing of travel requests with advanced filters ===
  router.get(
    '/travel-requests',
    adminManager,
    (req: RequestWithAdminController, res, next) => req.adminController?.getAllTravelRequests(req, res, next)
  );

  // === Admin/global listing of overtime requests with advanced filters ===
  router.get(
    '/overtime-requests',
    adminManager,
    (req: RequestWithAdminController, res, next) => req.adminController?.getAllOvertimeRequests(req, res, next)
  );

  return router;
}
