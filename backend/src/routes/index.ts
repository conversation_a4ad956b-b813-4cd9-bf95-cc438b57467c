// /backend/src/routes/index.ts
import { Router } from 'express';
import { DataSource } from 'typeorm';
import * as fs from 'fs';
import * as path from 'path';
import { createAuthRoutes } from './auth.routes';
import { createSystemsRoutes } from './systems.routes';
import { createNotificationRoutes } from './notification.routes';
import { createClientRoutes } from './client.routes';
import { createUserRoutes } from './user.routes';
import { createProfileRoutes } from './profile.routes';
import { createAdminRoutes } from './admin.routes';
import { createProductRoutes } from './product.routes';
import { createRfcValidationRoutes } from './rfc-validation.routes';
import { createInventoryMovementRoutes } from './inventory-movement.routes';
import { createInternalRequestRoutes } from './internal-request.routes';
import { NotificationService } from '../services/notification.service';

/**
 * @description Enrutador principal que carga dinámicamente todas las rutas de la API.
 *
 * Este módulo escanea el directorio actual (`/routes`) en busca de todos los
 * archivos que terminan en `.routes.ts` (o `.routes.js` en producción) y los
 * registra automáticamente en el enrutador principal.
 *
 * Beneficios:
 * - **Escalabilidad**: No es necesario importar manualmente cada nuevo archivo de rutas.
 *   Simplemente se crea el archivo y se registra automáticamente.
 * - **Mantenibilidad**: Reduce el código repetitivo y el riesgo de olvidar registrar una ruta.
 * - **Organización**: Mantiene el archivo de enrutador principal limpio y enfocado
 *   en su única responsabilidad: agregar las rutas a la aplicación.
 */

/**
 * @function createRouter
 * @description Factory para crear el router principal de la aplicación con todas las rutas configuradas.
 * 
 * @param dataSource - Instancia de DataSource de TypeORM para la conexión a la base de datos
 * @param services - Servicios necesarios para las rutas (como NotificationService)
 * @returns Router configurado con todas las rutas de la aplicación
 */
export function createRouter(dataSource: DataSource, services: { notificationService: NotificationService }): Router {
  const router = Router();
  const { notificationService } = services;

  console.log('\n🔧 Configurando rutas de la API...');
  
  // Lista de rutas disponibles en el directorio actual
  const routeFiles = fs.readdirSync(__dirname)
    .filter(file => file.endsWith('.routes.ts') || file.endsWith('.routes.js'))
    .map(file => path.basename(file, path.extname(file)))
    .filter(file => file !== 'index'); // Excluir este archivo

  for (const routeFile of routeFiles) {
    const routeName = routeFile.replace('.routes', '');
    
    // Manejar rutas especiales que usan factories
    if (routeName === 'auth') {
      const authRouter = createAuthRoutes();
      router.use(`/${routeName}`, authRouter);
      console.log(`✅ Ruta cargada: /api/${routeName}`);
      continue;
    }
    
    if (routeName === 'systems') {
      const systemsRouter = createSystemsRoutes(dataSource);
      router.use(`/${routeName}`, systemsRouter);
      console.log(`✅ Ruta cargada: /api/${routeName}`);
      continue;
    }
    
    if (routeName === 'client') {
      const clientRouter = createClientRoutes(dataSource);
      router.use(`/${routeName}s`, clientRouter); // Usar plural para consistencia
      console.log(`✅ Ruta cargada: /api/clients`);
      continue;
    }

    if (routeName === 'user') {
      const userRouter = createUserRoutes(dataSource);
      router.use(`/${routeName}`, userRouter);
      console.log(`✅ Ruta cargada: /api/user`);
      continue;
    }

    if (routeName === 'profile') {
      const profileRouter = createProfileRoutes(dataSource);
      router.use(`/${routeName}`, profileRouter);
      console.log(`✅ Ruta cargada: /api/profile`);
      continue;
    }

    if (routeName === 'admin') {
      const adminRouter = createAdminRoutes(dataSource);
      router.use(`/${routeName}`, adminRouter);
      console.log(`✅ Ruta cargada: /api/admin`);
      continue;
    }

    if (routeName === 'product') {
      const productRouter = createProductRoutes(dataSource, notificationService);
      router.use(`/${routeName}`, productRouter);
      console.log(`✅ Ruta cargada: /api/product`);
      continue;
    }

    if (routeName === 'notification') {
      const notificationRouter = createNotificationRoutes(notificationService, dataSource);
      router.use(`/${routeName}s`, notificationRouter); // Usar la ruta base 'notifications'
      console.log(`✅ Ruta cargada: /api/notifications`);
      continue;
    }

    if (routeName === 'rfc-validation') {
      const rfcValidationRouter = createRfcValidationRoutes(dataSource);
      router.use('/rfc', rfcValidationRouter); // Usar ruta más corta
      console.log(`✅ Ruta cargada: /api/rfc`);
      continue;
    }

    if (routeName === 'inventory-movement') {
      const inventoryMovementRouter = createInventoryMovementRoutes(dataSource, notificationService);
      router.use('/inventory-movements', inventoryMovementRouter);
      console.log(`✅ Ruta cargada: /api/inventory-movements`);
      continue;
    }

    if (routeName === 'internal-request') {
      const internalRequestRouter = createInternalRequestRoutes(dataSource, notificationService);
      router.use('/internal-requests', internalRequestRouter);
      console.log(`✅ Ruta cargada: /api/internal-requests`);
      continue;
    }

    // Para otras rutas que no tienen factory, usar importación dinámica (legacy)
    try {
      const routeModule = require(`./${routeFile}`);
      const routeRouter = routeModule.default || routeModule;
      
      if (routeRouter) {
        router.use(`/${routeName}`, routeRouter);
        console.log(`✅ Ruta cargada: /api/${routeName}`);
      }
    } catch (error) {
      console.warn(`⚠️ No se pudo cargar la ruta: ${routeName}`, error);
    }
  }

  console.log('✅ Todas las rutas configuradas correctamente.\n');
  return router;
}

// Export default para uso normal
// const router = createRouter(); // Ya no se puede crear sin dependencias
