import { Router } from 'express';
import { DataSource } from 'typeorm';
import { AuthController } from '../controllers/auth.controller';
import { AuthService } from '../services/auth.service';
import { validationMiddleware } from '../middlewares/validation.middleware';
import { LoginDto } from '../dtos/auth.dto';
// authMiddleware import removido - ya no se usa /me
import { AppDataSource } from '../data-source';

function createAuthRoutes(dataSource?: DataSource): Router {
  const router = Router();
  
  // Usar el dataSource apropiado (test o producción)
  const currentDataSource = dataSource || AppDataSource;
  const authService = new AuthService(currentDataSource);
  const controller = new AuthController(authService);

  // Ruta para iniciar sesión
  router.post('/login', validationMiddleware(LoginDto), controller.login.bind(controller));
  router.post('/refresh-token', controller.refreshToken.bind(controller));
  router.post('/logout', controller.logout.bind(controller));

  // Ruta para obtener información del usuario actual - ELIMINADA: no se usa en frontend

  return router;
}

// Export default para uso normal
const router = createAuthRoutes();

export default router;
export { createAuthRoutes };
