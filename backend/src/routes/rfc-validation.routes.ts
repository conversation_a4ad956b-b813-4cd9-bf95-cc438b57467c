import { Router } from 'express';
import { DataSource } from 'typeorm';
import { RfcValidationController } from '../controllers/rfc-validation.controller';
import { createAuthMiddleware } from '../middlewares/auth.middleware';

export function createRfcValidationRoutes(dataSource: DataSource): Router {
  const router = Router();
  const rfcValidationController = new RfcValidationController();
  const auth = createAuthMiddleware(dataSource);

  // Proteger todas las rutas de validación de RFC.
  router.use(auth);

  // --- Definición de Rutas para Validación de RFC ---

  // Validar RFC con el SAT
  router.get(
    '/validate/:rfc',
    rfcValidationController.validateRfc
  );

  // Obtener información fiscal completa de un RFC
  router.get(
    '/info/:rfc',
    rfcValidationController.getFiscalInfo
  );

  return router;
} 