import { Router, Request, Response } from 'express';
// import { AppDataSource } from '../../ormconfig';
// import { Notification } from '../entities/notification.entity';
import { createAuthMiddleware } from '../middlewares/auth.middleware';
import { NotificationController } from '../controllers/notification.controller';
import { AuthenticatedRequest } from '../interfaces/request.interface';
import { NotificationService } from '../services/notification.service';
import { DataSource } from 'typeorm';

export function createNotificationRoutes(
  notificationService: NotificationService,
  dataSource: DataSource
): Router {
  const router = Router();
  const notificationController = new NotificationController(notificationService);

  // Aplicar middleware de autenticación
  const authMw = createAuthMiddleware(dataSource);
  router.use(authMw); // ← USANDO FACTORY CON DATASOURCE INYECTADO

  // --- Rutas de Notificaciones ---

  // GET /api/notifications/stats -- usa lógica optimizada del controlador
  router.get(
    '/stats', 
    (req: Request, res: Response) => 
      notificationController.getNotificationStats(req as AuthenticatedRequest, res)
  );

  // GET /
  router.get('/', (req: Request, res: Response) =>
    notificationController.getUserNotifications(req as AuthenticatedRequest, res)
  );

  // Endpoint de prueba que se eliminará al final
  /*
  router.get('/db-test', async (_req, res) => {
    try {
      if (!AppDataSource.isInitialized) {
        return res.status(503).json({
          success: false,
          message: 'Database connection not ready',
        });
      }
      const notificationRepo = AppDataSource.getRepository(Notification);
      const count = await notificationRepo.count();
      return res.json({ 
        success: true, 
        message: 'Repository access working',
        notificationCount: count,
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: 'Database error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });
  */

  // Marcar duplicados como leídos
  router.post('/mark-duplicates-read', authMw, notificationController.markDuplicatesAsRead.bind(notificationController));

  // Eliminar notificación por ID (solo el usuario dueño puede borrar)
  router.delete('/:id', authMw, (req: Request, res: Response) =>
    notificationController.deleteNotification(req as AuthenticatedRequest, res)
  );

  return router;
} 