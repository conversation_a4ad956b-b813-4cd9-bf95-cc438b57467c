// /backend/src/routes/client.routes.ts
import { Router } from 'express';
import { DataSource } from 'typeorm';
import { ClientController } from '../controllers/client.controller';
import { createAuthMiddleware } from '../middlewares/auth.middleware';
import { validationMiddleware, ValidationSource } from '../middlewares/validation.middleware';
import { CreateClientDto, UpdateClientDto, QuickClientRegistrationDto } from '../dtos/client.dto';
import { Role } from '../constants/role.enum';
import { QuotationController } from '../controllers/quotation.controller';

export function createClientRoutes(dataSource: DataSource): Router {
  const router = Router();
  const controller = new ClientController(dataSource);
  const auth = createAuthMiddleware(dataSource);
  const quotationController = new QuotationController(dataSource);

  // Proteger todas las rutas de clientes.
  router.use(auth);

  // --- Definición de Rutas para Clientes ---

  // Obtener todos los clientes
  router.get(
    '/',
    controller.getClients.bind(controller)
  );

  // Buscar clientes con filtros avanzados
  router.get(
    '/search',
    controller.searchClients.bind(controller)
  );

  // Obtener clientes por vendedor asignado
  router.get(
    '/by-salesperson/:salespersonId',
    controller.getClientsBySalesperson.bind(controller)
  );

  // Validar RFC con el SAT
  router.get(
    '/validate-rfc/:rfc',
    controller.validateRfc.bind(controller)
  );

  // Actualizar estado de validación de RFC
  router.put(
    '/:id/rfc-validation',
    validationMiddleware({ isValid: 'boolean' }, ValidationSource.Body),
    controller.updateRfcValidationStatus.bind(controller)
  );

  // Obtener un cliente por ID
  router.get(
    '/:id',
    controller.getClientById.bind(controller)
  );

  // Crear un nuevo cliente (registro completo)
  // Solo roles específicos como Ventas o Admin pueden crear clientes.
  router.post(
    '/',
    createAuthMiddleware(dataSource, [Role.Admin, Role.Sales]),
    validationMiddleware(CreateClientDto),
    controller.createClient.bind(controller)
  );

  // Crear un nuevo cliente (registro rápido)
  router.post(
    '/quick',
    createAuthMiddleware(dataSource, [Role.Admin, Role.Sales]),
    validationMiddleware(QuickClientRegistrationDto),
    controller.createQuickClient.bind(controller)
  );

  // Actualizar un cliente
  router.put(
    '/:id',
    createAuthMiddleware(dataSource, [Role.Admin, Role.Sales]),
    validationMiddleware(UpdateClientDto, ValidationSource.Body, true),
    controller.updateClient.bind(controller)
  );

  // Eliminar un cliente
  router.delete(
    '/:id',
    createAuthMiddleware(dataSource, [Role.Admin]), // Solo los administradores pueden eliminar clientes
    controller.deleteClient.bind(controller)
  );

  router.get('/:id/quotations', quotationController.getQuotationsByClientId.bind(quotationController));

  // Obtener historial de compras por cliente
  router.get('/:id/purchases', controller.getClientPurchases.bind(controller));

  // Obtener productos más comprados por cliente
  router.get('/:id/top-products', controller.getClientTopProducts.bind(controller));

  // Documentos asociados a cliente
  router.get('/:id/documents', controller.getClientDocuments.bind(controller));
  router.post('/:id/documents', controller.uploadClientDocument.bind(controller));
  router.delete('/:id/documents/:docId', controller.deleteClientDocument.bind(controller));

  // KPIs específicos de cliente
  router.get('/:id/kpis', controller.getClientKpis.bind(controller));

  return router;
}