import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateInternalRequestsTable1700000000000 implements MigrationInterface {
  name = 'CreateInternalRequestsTable1700000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Crear enum para tipos de solicitud
    await queryRunner.query(`
      CREATE TYPE "public"."request_type_enum" AS ENUM('VIATICOS', 'TIEMPO_EXTRA', 'PAQUETERIA')
    `);

    // Crear enum para estados de solicitud
    await queryRunner.query(`
      CREATE TYPE "public"."request_status_enum" AS ENUM('DRAFT', 'SUBMITTED', 'PENDING_APPROVAL', 'APPROVED', 'REJECTED', 'CANCELLED')
    `);

    // Crear enum para niveles de aprobación
    await queryRunner.query(`
      CREATE TYPE "public"."approval_level_enum" AS ENUM('MANAGER', 'DIRECTOR', 'ADMIN')
    `);

    // Crear tabla de solicitudes internas
    await queryRunner.query(`
      CREATE TABLE "internal_requests" (
        "id" SERIAL NOT NULL,
        "request_type" "public"."request_type_enum" NOT NULL,
        "status" "public"."request_status_enum" NOT NULL DEFAULT 'DRAFT',
        "title" character varying(255) NOT NULL,
        "description" text,
        "estimated_cost" decimal(10,2),
        "approved_amount" decimal(10,2),
        "start_date" date,
        "end_date" date,
        "request_data" jsonb,
        "approval_history" jsonb,
        "rejection_reason" character varying(500),
        "attachments" character varying(255),
        "current_approval_level" "public"."approval_level_enum" NOT NULL DEFAULT 'MANAGER',
        "is_urgent" boolean NOT NULL DEFAULT false,
        "priority" character varying(100),
        "requester_id" integer NOT NULL,
        "approver_id" integer,
        "created_by" integer,
        "updated_by" integer,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "submitted_at" TIMESTAMP WITH TIME ZONE,
        "approved_at" TIMESTAMP WITH TIME ZONE,
        "rejected_at" TIMESTAMP WITH TIME ZONE,
        CONSTRAINT "PK_internal_requests" PRIMARY KEY ("id")
      )
    `);

    // Agregar índices
    await queryRunner.query(`
      CREATE INDEX "IDX_internal_requests_type_status" ON "internal_requests" ("request_type", "status")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_internal_requests_requester_status" ON "internal_requests" ("requester_id", "status")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_internal_requests_approver_status" ON "internal_requests" ("approver_id", "status")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_internal_requests_created_at" ON "internal_requests" ("created_at")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_internal_requests_urgent" ON "internal_requests" ("is_urgent", "status")
    `);

    // Agregar foreign keys
    await queryRunner.query(`
      ALTER TABLE "internal_requests" 
      ADD CONSTRAINT "FK_internal_requests_requester" 
      FOREIGN KEY ("requester_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "internal_requests" 
      ADD CONSTRAINT "FK_internal_requests_approver" 
      FOREIGN KEY ("approver_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "internal_requests" 
      ADD CONSTRAINT "FK_internal_requests_created_by" 
      FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "internal_requests" 
      ADD CONSTRAINT "FK_internal_requests_updated_by" 
      FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Eliminar foreign keys
    await queryRunner.query(`
      ALTER TABLE "internal_requests" DROP CONSTRAINT "FK_internal_requests_updated_by"
    `);

    await queryRunner.query(`
      ALTER TABLE "internal_requests" DROP CONSTRAINT "FK_internal_requests_created_by"
    `);

    await queryRunner.query(`
      ALTER TABLE "internal_requests" DROP CONSTRAINT "FK_internal_requests_approver"
    `);

    await queryRunner.query(`
      ALTER TABLE "internal_requests" DROP CONSTRAINT "FK_internal_requests_requester"
    `);

    // Eliminar índices
    await queryRunner.query(`
      DROP INDEX "IDX_internal_requests_urgent"
    `);

    await queryRunner.query(`
      DROP INDEX "IDX_internal_requests_created_at"
    `);

    await queryRunner.query(`
      DROP INDEX "IDX_internal_requests_approver_status"
    `);

    await queryRunner.query(`
      DROP INDEX "IDX_internal_requests_requester_status"
    `);

    await queryRunner.query(`
      DROP INDEX "IDX_internal_requests_type_status"
    `);

    // Eliminar tabla
    await queryRunner.query(`
      DROP TABLE "internal_requests"
    `);

    // Eliminar enums
    await queryRunner.query(`
      DROP TYPE "public"."approval_level_enum"
    `);

    await queryRunner.query(`
      DROP TYPE "public"."request_status_enum"
    `);

    await queryRunner.query(`
      DROP TYPE "public"."request_type_enum"
    `);
  }
}