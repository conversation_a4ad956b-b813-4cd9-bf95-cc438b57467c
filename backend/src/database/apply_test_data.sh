#!/bin/bash

# Configuración de la base de datos
DB_NAME="${DB_NAME:-comintec_app}"
DB_USER="${DB_USER:-postgres}"
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"

echo "🗄️ Aplicando datos de prueba a la base de datos..."

# Función para ejecutar archivos SQL
execute_sql() {
    local file=$1
    echo "📋 Ejecutando $file..."
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$file"
    
    if [ $? -eq 0 ]; then
        echo "✅ $file ejecutado correctamente"
    else
        echo "❌ Error ejecutando $file"
        exit 1
    fi
}

# Ejecutar archivos en orden
execute_sql test_users.sql
execute_sql test_roles.sql
execute_sql test_user_roles.sql
execute_sql test_role_permissions.sql

echo "✅ Datos de prueba aplicados correctamente!" 