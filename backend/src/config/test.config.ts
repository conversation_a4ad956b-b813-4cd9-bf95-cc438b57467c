import { DataSource, DataSourceOptions } from 'typeorm';

// ¡ATENCIÓN! Esta configuración usa la misma base de datos de Supabase 
// para tests. Los datos de test se identifican por patrones específicos.
export const testDataSourceOptions: DataSourceOptions = {
  type: 'postgres',
  host: process.env.DB_HOST || 'db.awlxzhrubqkryrenunun.supabase.co',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'appcomintecpassw0rd',
  database: process.env.DB_DATABASE || 'postgres',
  entities: [__dirname + '/../entities/*.{ts,js}'],
  migrations: [__dirname + '/../database/migrations/**/*.{ts,js}'],
  synchronize: false, // Disable synchronize to avoid schema conflicts with existing data
  dropSchema: false,  // Do not drop schema, handled in test-setup
  logging: false,
  // Use the default public schema instead of test_schema
  // schema: 'public', // Default schema, can be omitted
};

export const testDataSource = new DataSource(testDataSourceOptions);

export default testDataSource;
