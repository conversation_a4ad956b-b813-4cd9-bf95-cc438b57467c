// /backend/src/config/config.ts
import path from 'path';
import dotenv from 'dotenv';

// Carga las variables de entorno desde el archivo .env
dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

/**
 * @description Configuración centralizada de la aplicación.
 *
 * Este objeto `config` exporta todas las variables de entorno y otros valores
 * de configuración que la aplicación necesita para funcionar. Al centralizar la
 * configuración en este archivo, se facilita la gestión de diferentes entornos
 * (desarrollo, producción, pruebas) y se evita la dispersión de `process.env`
 * por todo el código.
 *
 * Beneficios:
 * - **Centralización**: Un único lugar para gestionar la configuración.
 * - **Seguridad**: Evita exponer claves secretas directamente en el código.
 * - **Flexibilidad**: Facilita el cambio de valores para diferentes entornos.
 * - **Mantenibilidad**: Simplifica la actualización de la configuración.
 */
export const config = {
  // Configuración del servidor
  server: {
    port: Number(process.env.PORT) || 3000,
    env: process.env.NODE_ENV || 'development',
  },

  // Configuración de la base de datos
  database: {
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: Number(process.env.DB_PORT) || 5432,
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_DATABASE || 'postgres',
    synchronize: process.env.DB_SYNCHRONIZE === 'true' || false,
    logging: process.env.DB_LOGGING === 'true' || false,
  },

  // Configuración de JWT (JSON Web Token)
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '1h',
  },

  // Otras configuraciones
  // Ejemplo: API keys, configuraciones de servicios externos, etc.
  api: {
    // key: process.env.API_KEY,
  },
};