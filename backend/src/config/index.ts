import dotenv from 'dotenv';

// Cargar variables de entorno desde .env
dotenv.config();

// Validar variables de entorno requeridas
const requiredEnvVars = [
  'PORT',
  'NODE_ENV',
  'DB_HOST',
  'DB_PORT',
  'DB_USERNAME',
  'DB_PASSWORD',
  'DB_DATABASE',
  'JWT_SECRET',
  'JWT_EXPIRES_IN'
];

requiredEnvVars.forEach(envVar => {
  if (!process.env[envVar]) {
    throw new Error(`La variable de entorno ${envVar} no está definida`);
  }
});

// Configuración de la aplicación
export const config = {
  // Configuración del servidor
  port: parseInt(process.env.PORT || '3000', 10),
  nodeEnv: process.env.NODE_ENV || 'development',
  isProduction: process.env.NODE_ENV === 'production',
  isDevelopment: process.env.NODE_ENV === 'development',
  
  // Configuración de la base de datos
  database: {
    host: process.env.DB_HOST!,
    port: parseInt(process.env.DB_PORT!, 10),
    username: process.env.DB_USERNAME!,
    password: process.env.DB_PASSWORD!,
    database: process.env.DB_DATABASE!,
    synchronize: false, // Siempre false para no modificar el esquema existente
    logging: process.env.NODE_ENV === 'development',
  },
  
  // Configuración de JWT
  jwt: {
    secret: process.env.JWT_SECRET!,
    expiresIn: process.env.JWT_EXPIRES_IN!,
  },
  
  // Otras configuraciones
  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:3001',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
  },

  // Nombres de Roles Clave (para consistencia)
  roles: {
    ROLE_ADMIN: 'ROLE_ADMIN',
    ROLE_MANAGER: 'ROLE_MANAGER', // Rol genérico de manager
    ROLE_USER: 'ROLE_USER',    // Rol base de usuario
    // Departamentos como roles (ejemplos, pueden ser más granulares o combinados con _MANAGER)
    ROLE_VENTAS: 'ROLE_SALES',
    ROLE_FINANZAS: 'ROLE_FINANCE',
    ROLE_ALMACEN: 'ROLE_WAREHOUSE',
    ROLE_SISTEMAS: 'ROLE_SISTEMAS', // Para el depto de sistemas
    ROLE_MANAGER_ADMINISTRACION: 'ROLE_MANAGER_ADMINISTRACION', // Manager del depto de Admin
    // ... otros roles que se definan en la tabla 'roles' de la BD
  }
};
