// /backend/src/controllers/client.controller.ts
import { Request, Response } from 'express';
import { DataSource } from "typeorm";
import { ClientService } from '../services/client.service';
import { AuditService } from '../modules/systems/services/audit.service';

/**
 * @class ClientController
 * @description Controlador para gestionar las peticiones HTTP relacionadas con clientes.
 */
export class ClientController {
  private clientService: ClientService;
  private auditService: AuditService;

  constructor(dataSource: DataSource) {
    this.clientService = new ClientService(dataSource);
    this.auditService = new AuditService(dataSource);
  }

  /**
   * @description Obtiene todos los clientes.
   */
  async getClients(req: Request, res: Response): Promise<void> {
    try {
      const filters = req.query;
      const result = await this.clientService.getClients(filters);
      console.log("[CLIENTES][LIST] Filtros:", filters, "Resultado:", result);
      res.json({ data: result.clients, total: result.total, page: result.page, limit: result.limit, totalPages: result.totalPages });
    } catch (error: any) {
      console.error("[CLIENTES][LIST] Error:", error);
      res.status(500).json({ message: error.message || "Error interno al listar clientes" });
    }
  }

  /**
   * @description Obtiene un cliente por su ID.
   */
  async getClientById(req: Request, res: Response): Promise<void> {
    const clientId = parseInt(req.params.id, 10);
    if (isNaN(clientId)) {
      res.status(400).json({ message: 'ID de cliente inválido' });
      return;
    }
    try {
      const client = await this.clientService.getClientById(clientId);
      res.json({ data: client });
    } catch (error: any) {
      console.error('[CLIENTES][DETAIL] Error:', error);
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * @description Crea un nuevo cliente.
   */
  async createClient(req: Request, res: Response): Promise<void> {
    try {
      console.log('[DEBUG][CLIENTES][CREATE] Body recibido:', req.body);
      // Obtén el userId del usuario autenticado (middleware)
      const userId = req.user?.id;
      console.log('[DEBUG][CLIENTES][CREATE] userId extraído de req.user:', userId);
      if (!userId) {
        res.status(401).json({ message: 'No autenticado: userId no disponible en req.user' });
        return;
      }
      const client = await this.clientService.createClient(req.body, userId);
      // Auditoría
      await this.auditService.log({
        userId,
        action: 'create_client',
        targetEntity: 'clients',
        targetId: client.id,
        details: client,
        ipAddress: req.ip,
      });
      console.log('[DEBUG][CLIENTES][CREATE] Resultado:', client);
      res.status(201).json({ data: client });
    } catch (error: any) {
      console.error("[CLIENTES][CREATE] Error:", error);
      if (error.message && error.message.includes("duplicate key")) {
        res.status(409).json({ message: "Ya existe un cliente con esos datos" });
        return;
      }
      res.status(500).json({ message: error.message || "Error interno al crear cliente" });
    }
  }

  /**
   * @description Actualiza un cliente existente.
   */
  async updateClient(req: Request, res: Response): Promise<void> {
    try {
      console.log('[DEBUG][CLIENTES][UPDATE] Body recibido:', req.body);
      const id = parseInt(req.params.id, 10);
      const client = await this.clientService.updateClient(id, req.body);
      // Auditoría
      await this.auditService.log({
        userId: req.user?.id,
        action: 'update_client',
        targetEntity: 'clients',
        targetId: client.id,
        details: req.body,
        ipAddress: req.ip,
      });
      console.log('[DEBUG][CLIENTES][UPDATE] Resultado:', client);
      if (!client) {
        res.status(404).json({ message: "Cliente no encontrado" });
        return;
      }
      res.json({ data: client });
    } catch (error: any) {
      console.error("[CLIENTES][UPDATE] Error:", error);
      res.status(500).json({ message: error.message || "Error interno al actualizar cliente" });
    }
  }

  /**
   * @description Elimina un cliente.
   */
  async deleteClient(req: Request, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id, 10);
      const deleted: boolean = await this.clientService.deleteClient(id);
      // Auditoría
      await this.auditService.log({
        userId: req.user?.id,
        action: 'delete_client',
        targetEntity: 'clients',
        targetId: id,
        details: {},
        ipAddress: req.ip,
      });
      if (!deleted) {
        res.status(404).json({ message: "Cliente no encontrado" });
        return;
      }
      res.json({ message: "Cliente eliminado correctamente" });
    } catch (error: any) {
      console.error("[CLIENTES][DELETE] Error:", error);
      res.status(500).json({ message: error.message || "Error interno al eliminar cliente" });
    }
  }

  /**
   * @description Valida un RFC con el servicio externo del SAT.
   */
  async validateRfc(req: Request, res: Response): Promise<void> {
    try {
      const { rfc } = req.params;
      const validationResult = await this.clientService.validateRfcWithSat(rfc);
      if (validationResult.valid) {
        res.status(200).json({ 
          data: { 
            valid: true, 
            message: 'RFC válido en el SAT.',
            details: validationResult.data
          } 
        });
      } else {
        res.status(200).json({ 
          data: { 
            valid: false, 
            message: 'RFC no encontrado o inválido en el SAT.'
          } 
        });
      }
    } catch (error: any) {
      console.error("[CLIENTES][RFC] Error:", error);
      res.status(500).json({ message: error.message || "Error al validar RFC" });
    }
  }

  /**
   * @description Actualiza el estado de validación del RFC de un cliente.
   */
  async updateRfcValidationStatus(req: Request, res: Response): Promise<void> {
    try {
      const clientId = parseInt(req.params.id, 10);
      const { isValid } = req.body;
      const updatedClient = await this.clientService.updateRfcValidationStatus(clientId, isValid);
      res.status(200).json({ data: updatedClient });
    } catch (error: any) {
      console.error("[CLIENTES][RFC][UPDATE] Error:", error);
      res.status(500).json({ message: error.message || "Error al actualizar validación de RFC" });
    }
  }

  /**
   * @description Busca clientes con filtros avanzados.
   */
  async searchClients(req: Request, res: Response): Promise<void> {
    try {
      const filters = req.query;
      // Conversión de filtros numéricos y de fechas si aplica
      const parsedFilters = {
        ...filters,
        creditLimitMin: filters.creditLimitMin ? parseInt(filters.creditLimitMin as string, 10) : undefined,
        creditLimitMax: filters.creditLimitMax ? parseInt(filters.creditLimitMax as string, 10) : undefined,
        lastPurchaseStart: filters.lastPurchaseStart ? new Date(filters.lastPurchaseStart as string) : undefined,
        lastPurchaseEnd: filters.lastPurchaseEnd ? new Date(filters.lastPurchaseEnd as string) : undefined,
      };
      const clients = await this.clientService.searchClients(parsedFilters);
      res.status(200).json({ 
        data: clients,
        total: clients.length,
        page: 1,
        limit: clients.length,
        totalPages: 1
      });
    } catch (error: any) {
      console.error("[CLIENTES][SEARCH] Error:", error);
      res.status(500).json({ message: error.message || "Error al buscar clientes" });
    }
  }

  /**
   * @description Obtiene todos los clientes asignados a un vendedor.
   */
  async getClientsBySalesperson(req: Request, res: Response): Promise<void> {
    try {
      const salespersonId = parseInt(req.params.salespersonId, 10);
      const clients = await this.clientService.getClientsBySalesperson(salespersonId);
      res.json({ data: clients });
    } catch (error: any) {
      res.status(500).json({ message: error.message || "Error interno al obtener clientes por vendedor" });
    }
  }

  /**
   * @description Crea un cliente de forma rápida (sin validaciones extensas).
   */
  async createQuickClient(req: Request, res: Response): Promise<void> {
    try {
      console.log('[DEBUG][CLIENTES][QUICK][CREATE] Body recibido:', req.body);
      // Aquí deberías obtener el userId real del request (ejemplo: req.user.id)
      const userId = req.body.assignedSalespersonId || 1;
      const client = await this.clientService.createQuickClient(req.body, userId);
      console.log('[DEBUG][CLIENTES][QUICK][CREATE] Resultado:', client);
      res.status(201).json({ data: client });
    } catch (error: any) {
      res.status(500).json({ message: error.message || "Error interno al crear cliente rápido" });
    }
  }

  /**
   * @description Obtiene el historial de compras de un cliente.
   */
  async getClientPurchases(req: Request, res: Response): Promise<void> {
    try {
      const clientId = parseInt(req.params.id, 10);
      if (isNaN(clientId)) {
        res.status(400).json({ message: 'ID de cliente inválido' });
        return;
      }
      const purchases = await this.clientService.getClientPurchases(clientId);
      res.status(200).json({ data: purchases, message: 'Historial de compras obtenido correctamente' });
    } catch (error: any) {
      console.error('[CLIENTES][PURCHASES] Error:', error);
      res.status(500).json({ message: error.message || 'Error al obtener historial de compras' });
    }
  }

  /**
   * @description Obtiene los productos más comprados de un cliente.
   */
  async getClientTopProducts(req: Request, res: Response): Promise<void> {
    try {
      const clientId = parseInt(req.params.id, 10);
      if (isNaN(clientId)) {
        res.status(400).json({ message: 'ID de cliente inválido' });
        return;
      }
      const topProducts = await this.clientService.getClientTopProducts(clientId);
      res.status(200).json({ data: topProducts, message: 'Productos más comprados obtenidos correctamente' });
    } catch (error: any) {
      console.error('[CLIENTES][TOP_PRODUCTS] Error:', error);
      res.status(500).json({ message: error.message || 'Error al obtener productos más comprados' });
    }
  }

  /**
   * @description Obtiene los documentos asociados a un cliente.
   */
  async getClientDocuments(req: Request, res: Response): Promise<void> {
    try {
      const clientId = parseInt(req.params.id, 10);
      if (isNaN(clientId)) {
        res.status(400).json({ message: 'ID de cliente inválido' });
        return;
      }
      const documents = await this.clientService.getClientDocuments(clientId);
      res.status(200).json({ data: documents, message: 'Documentos obtenidos correctamente' });
    } catch (error: any) {
      console.error('[CLIENTES][DOCUMENTS] Error:', error);
      res.status(500).json({ message: error.message || 'Error al obtener documentos' });
    }
  }

  /**
   * @description Sube un nuevo documento para un cliente.
   */
  async uploadClientDocument(req: Request, res: Response): Promise<void> {
    try {
      const clientId = parseInt(req.params.id, 10);
      if (isNaN(clientId)) {
        res.status(400).json({ message: 'ID de cliente inválido' });
        return;
      }
      // Suponiendo que el archivo viene en req.file o req.body según el middleware
      const document = await this.clientService.uploadClientDocument(clientId, req);
      res.status(201).json({ data: document, message: 'Documento subido correctamente' });
    } catch (error: any) {
      console.error('[CLIENTES][UPLOAD_DOCUMENT] Error:', error);
      res.status(500).json({ message: error.message || 'Error al subir documento' });
    }
  }

  /**
   * @description Elimina un documento de un cliente.
   */
  async deleteClientDocument(req: Request, res: Response): Promise<void> {
    try {
      const clientId = parseInt(req.params.id, 10);
      const docId = parseInt(req.params.docId, 10);
      if (isNaN(clientId) || isNaN(docId)) {
        res.status(400).json({ message: 'ID de cliente o documento inválido' });
        return;
      }
      await this.clientService.deleteClientDocument(clientId, docId);
      res.status(200).json({ message: 'Documento eliminado correctamente' });
    } catch (error: any) {
      console.error('[CLIENTES][DELETE_DOCUMENT] Error:', error);
      res.status(500).json({ message: error.message || 'Error al eliminar documento' });
    }
  }

  /**
   * @description Obtiene los KPIs de un cliente.
   */
  async getClientKpis(res: Response): Promise<void> {
    try {
      const kpis = await this.clientService.getClientKpis();
      res.status(200).json({ data: kpis, message: 'KPIs obtenidos correctamente' });
    } catch (error: any) {
      console.error('[CLIENTES][KPIS] Error:', error);
      res.status(500).json({ message: error.message || 'Error al obtener KPIs' });
    }
  }
}