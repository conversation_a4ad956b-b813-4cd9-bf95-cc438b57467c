import { Request, Response } from 'express';
import { DataSource } from 'typeorm';
import { ProductService } from '../services/product.service';
import { NotificationService } from '../services/notification.service';
import { 
  CreateProductDto, 
  UpdateProductDto, 
  ProductFilterDto,
  StockMovementDto 
} from '../dtos/product.dto';
import { validateOrReject } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { AuthenticatedRequest } from '../interfaces/request.interface';

export class ProductController {
  private productService: ProductService;

  constructor(dataSource: DataSource, notificationService: NotificationService) {
    this.productService = new ProductService(dataSource, notificationService);
  }

  /**
   * Crear un nuevo producto
   * POST /api/products
   */
  async createProduct(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const createProductDto = plainToClass(CreateProductDto, req.body);
      await validateOrReject(createProductDto);

      const product = await this.productService.createProduct(
        createProductDto, 
        req.user?.id
      );

      res.status(201).json({
        success: true,
        message: 'Producto creado exitosamente',
        data: product
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Datos de entrada inválidos',
          errors: validationErrors
        });
      } else if (error.message.includes('Ya existe un producto')) {
        res.status(409).json({
          success: false,
          message: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Obtener todos los productos con filtros y paginación
   * GET /api/products
   */
  async getAllProducts(req: Request, res: Response): Promise<void> {
    try {
      const filterDto = plainToClass(ProductFilterDto, req.query);
      await validateOrReject(filterDto);

      const result = await this.productService.findAllProducts(filterDto);

      res.status(200).json({
        success: true,
        message: 'Productos obtenidos exitosamente',
        data: result
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Parámetros de búsqueda inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Obtener producto por ID
   * GET /api/products/:id
   */
  async getProductById(req: Request, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: 'ID de producto inválido'
        });
        return;
      }

      const product = await this.productService.findProductById(id);

      res.status(200).json({
        success: true,
        message: 'Producto obtenido exitosamente',
        data: product
      });
    } catch (error: any) {
      if (error.message.includes('no encontrado')) {
        res.status(404).json({
          success: false,
          message: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Obtener producto por código
   * GET /api/products/by-code/:code
   */
  async getProductByCode(req: Request, res: Response): Promise<void> {
    try {
      const codigoItem = req.params.code;

      if (!codigoItem) {
        res.status(400).json({
          success: false,
          message: 'Código de producto requerido'
        });
        return;
      }

      const product = await this.productService.findProductByCode(codigoItem);

      res.status(200).json({
        success: true,
        message: 'Producto obtenido exitosamente',
        data: product
      });
    } catch (error: any) {
      if (error.message.includes('no encontrado')) {
        res.status(404).json({
          success: false,
          message: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Actualizar producto
   * PUT /api/products/:id
   */
  async updateProduct(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: 'ID de producto inválido'
        });
        return;
      }

      const updateProductDto = plainToClass(UpdateProductDto, req.body);
      await validateOrReject(updateProductDto);

      const product = await this.productService.updateProduct(
        id, 
        updateProductDto, 
        req.user?.id
      );

      res.status(200).json({
        success: true,
        message: 'Producto actualizado exitosamente',
        data: product
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Datos de entrada inválidos',
          errors: validationErrors
        });
      } else if (error.message.includes('no encontrado')) {
        res.status(404).json({
          success: false,
          message: error.message
        });
      } else if (error.message.includes('Ya existe un producto')) {
        res.status(409).json({
          success: false,
          message: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Eliminar producto
   * DELETE /api/products/:id
   */
  async deleteProduct(req: Request, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: 'ID de producto inválido'
        });
        return;
      }

      await this.productService.deleteProduct(id);

      res.status(200).json({
        success: true,
        message: 'Producto eliminado exitosamente'
      });
    } catch (error: any) {
      if (error.message.includes('no encontrado')) {
        res.status(404).json({
          success: false,
          message: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Reservar stock de un producto
   * POST /api/products/:id/reserve
   */
  async reserveStock(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const { cantidad } = req.body;
      
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: 'ID de producto inválido'
        });
        return;
      }

      if (!cantidad || cantidad <= 0) {
        res.status(400).json({
          success: false,
          message: 'Cantidad debe ser mayor a 0'
        });
        return;
      }

      const product = await this.productService.reservarStock(
        id, 
        cantidad, 
        req.user?.id
      );

      res.status(200).json({
        success: true,
        message: 'Stock reservado exitosamente',
        data: product
      });
    } catch (error: any) {
      if (error.message.includes('no encontrado')) {
        res.status(404).json({
          success: false,
          message: error.message
        });
      } else if (error.message.includes('Stock insuficiente') || 
                 error.message.includes('no está disponible')) {
        res.status(400).json({
          success: false,
          message: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Ajustar stock (entrada/salida de inventario)
   * POST /api/products/:id/adjust-stock
   */
  async adjustStock(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: 'ID de producto inválido'
        });
        return;
      }

      const stockMovementDto = plainToClass(StockMovementDto, req.body);
      await validateOrReject(stockMovementDto);

      const product = await this.productService.ajustarStock(
        id, 
        stockMovementDto, 
        req.user?.id
      );

      res.status(200).json({
        success: true,
        message: `Stock ${stockMovementDto.tipo.toLowerCase()} registrado exitosamente`,
        data: product
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Datos de movimiento inválidos',
          errors: validationErrors
        });
      } else if (error.message.includes('no encontrado')) {
        res.status(404).json({
          success: false,
          message: error.message
        });
      } else if (error.message.includes('Stock insuficiente')) {
        res.status(400).json({
          success: false,
          message: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Obtener productos con stock bajo
   * GET /api/products/low-stock
   */
  async getLowStockProducts(_req: Request, res: Response): Promise<void> {
    try {
      const products = await this.productService.findProductsWithLowStock();

      res.status(200).json({
        success: true,
        message: 'Productos con stock bajo obtenidos exitosamente',
        data: products
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: 'Error interno del servidor',
        error: error.message
      });
    }
  }

  /**
   * Obtener estadísticas de inventario
   * GET /api/products/stats
   */
  async getInventoryStats(_req: Request, res: Response): Promise<void> {
    try {
      const stats = await this.productService.getInventoryStats();

      res.status(200).json({
        success: true,
        message: 'Estadísticas de inventario obtenidas exitosamente',
        data: stats
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: 'Error interno del servidor',
        error: error.message
      });
    }
  }

  /**
   * Obtener todas las ubicaciones/bodegas disponibles
   * GET /api/products/locations
   */
  async getAvailableLocations(_req: Request, res: Response): Promise<void> {
    try {
      const locations = await this.productService.getAvailableLocations();

      res.status(200).json({
        success: true,
        message: 'Ubicaciones obtenidas exitosamente',
        data: locations
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: 'Error interno del servidor',
        error: error.message
      });
    }
  }
}
