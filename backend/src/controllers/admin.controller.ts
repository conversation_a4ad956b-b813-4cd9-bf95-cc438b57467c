import { Request, Response, NextFunction } from 'express';
import { RequestWithUser } from '../interfaces/request.interface';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';

import {
  CreatePackageRequestDto,
  UpdatePackageRequestDto,
  AssignFiscalNumberDto,
  CreateContractRepseDto,
  UpdateContractRepseDto,
  CreateInvoiceDto,
  UpdateInvoiceDto,
  CreateCreditRequestDto,
  AdminPaginationQueryDto,
  GetAdminDashboardGraphDto,
  CreateTravelRequestDto,
  UpdateTravelRequestDto,
  CreateOvertimeRequestDto,
  UpdateOvertimeRequestDto,
  UpdateTravelRequestStatusDto,
  UpdateOvertimeRequestStatusDto,
} from '../dtos/admin.dto';

import { PackageRequestService }  from '../services/package-request.service';
import { FiscalNumberService } from '../services/fiscal-number.service';
import { ContractRepseService } from '../services/contract-repse.service';
import { InvoiceService } from '../services/invoice.service';
import { CreditRequestService } from '../services/credit-request.service';
import { AdminDashboardService } from '../services/admin-dashboard.service';
import { HttpException } from '../exceptions/http.exception';
import { Role } from '../constants/role.enum';


export class AdminController {
  private packageRequestService: PackageRequestService;
  private fiscalNumberService = new FiscalNumberService();
  private contractRepseService = new ContractRepseService();
  private invoiceService = new InvoiceService();
  private creditRequestService = new CreditRequestService();
  private adminDashboardService = new AdminDashboardService();

  constructor(testDataSource?: any) {
    this.packageRequestService = new PackageRequestService(testDataSource);
  }

  // --- Solicitud de Paquetería ---
  public createPackageRequest = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const dto = plainToInstance(CreatePackageRequestDto, req.body);
      const errors = await validate(dto);
      if (errors.length > 0) {
        return next(new HttpException(400, 'Error de validación DTO', errors));
      }
      if (!req.user || !req.user.email) return next(new HttpException(401, 'Usuario no autenticado o sin email.'));
      const result = await this.packageRequestService.createPackageRequest(dto, req.user as any);
      res.status(201).json({ message: 'Solicitud de paquetería creada', data: result });
    } catch (error) {
      next(error);
    }
  };

  public getPackageRequestById = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      const result = await this.packageRequestService.getPackageRequestById(id);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };

  public getAllPackageRequests = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const paginationQuery = plainToInstance(AdminPaginationQueryDto, req.query);
      const errors = await validate(paginationQuery);
      if (errors.length > 0) {
          return next(new HttpException(400, 'Error de validación en query params', errors));
      }
      const result = await this.packageRequestService.getAllPackageRequests(paginationQuery);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };

  public updatePackageRequest = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      const dto = plainToInstance(UpdatePackageRequestDto, req.body);
      const errors = await validate(dto);
      if (errors.length > 0) {
        return next(new HttpException(400, 'Error de validación DTO', errors));
      }
      if (!req.user) return next(new HttpException(401, 'Usuario no autenticado'));
      const result = await this.packageRequestService.updatePackageRequest(id, dto);
      res.status(200).json({ message: 'Solicitud de paquetería actualizada', data: result });
    } catch (error) {
      next(error);
    }
  };

  public deletePackageRequest = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      if (!req.user) return next(new HttpException(401, 'Usuario no autenticado'));
      await this.packageRequestService.deletePackageRequest(id);
      res.status(204).send();
    } catch (error) {
      next(error);
    }
  };

  // --- Asigna Número Fiscal ---
  public assignFiscalNumber = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const dto = plainToInstance(AssignFiscalNumberDto, req.body);
      const errors = await validate(dto);
      if (errors.length > 0) {
        return next(new HttpException(400, 'Error de validación DTO', errors));
      }
      if (!req.user || !req.user.email) return next(new HttpException(401, 'Usuario no autenticado o sin email.'));
      const result = await this.fiscalNumberService.assignFiscalNumber(dto, req.user as any);
      res.status(201).json({ message: 'Número fiscal asignado', data: result });
    } catch (error) {
      next(error);
    }
  };

  public getFiscalNumbers = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const paginationQuery = plainToInstance(AdminPaginationQueryDto, req.query);
      const errors = await validate(paginationQuery);
      if (errors.length > 0) {
          return next(new HttpException(400, 'Error de validación en query params', errors));
      }
      const result = await this.fiscalNumberService.getAllFiscalNumbers(paginationQuery);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };

  public getFiscalNumberByIdController = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      const result = await this.fiscalNumberService.getFiscalNumberById(id);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };

  public deleteFiscalNumber = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      if (!req.user) return next(new HttpException(401, 'Usuario no autenticado'));
      await this.fiscalNumberService.deleteFiscalNumber(id);
      res.status(204).send();
    } catch (error) {
      next(error);
    }
  };

  // --- Contratos Repse ---
  public createContractRepse = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const dto = plainToInstance(CreateContractRepseDto, req.body);
      const errors = await validate(dto);
      if (errors.length > 0) return next(new HttpException(400, 'Error de validación DTO', errors));
      if (!req.user || !req.user.email) return next(new HttpException(401, 'Usuario no autenticado o sin email.'));
      const result = await this.contractRepseService.createContract(dto, req.user as any);
      res.status(201).json({ message: 'Contrato REPSE creado', data: result });
    } catch (error) {
      next(error);
    }
  };

  public getContractRepseById = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      const result = await this.contractRepseService.getContractById(id);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };

  public getAllContractsRepse = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const paginationQuery = plainToInstance(AdminPaginationQueryDto, req.query);
      const errors = await validate(paginationQuery);
      if (errors.length > 0) return next(new HttpException(400, 'Error de validación en query params', errors));
      const result = await this.contractRepseService.getAllContracts(paginationQuery);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };

  public updateContractRepse = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      const dto = plainToInstance(UpdateContractRepseDto, req.body);
      const errors = await validate(dto);
      if (errors.length > 0) return next(new HttpException(400, 'Error de validación DTO', errors));
      if (!req.user) return next(new HttpException(401, 'Usuario no autenticado'));
      const result = await this.contractRepseService.updateContract(id, dto, req.user as any);
      res.status(200).json({ message: 'Contrato REPSE actualizado', data: result });
    } catch (error) {
      next(error);
    }
  };

  public deleteContractRepse = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      if (!req.user) return next(new HttpException(401, 'Usuario no autenticado'));
      await this.contractRepseService.deleteContract(id);
      res.status(204).send();
    } catch (error) {
      next(error);
    }
  };

  // --- Facturas ---
  public createInvoice = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const dto = plainToInstance(CreateInvoiceDto, req.body);
      const errors = await validate(dto);
      if (errors.length > 0) return next(new HttpException(400, 'Error de validación DTO', errors));
      if (!req.user || !req.user.email) return next(new HttpException(401, 'Usuario no autenticado o sin email.'));
      const result = await this.invoiceService.createInvoice(dto, req.user as any);
      res.status(201).json({ message: 'Factura creada', data: result });
    } catch (error) {
      next(error);
    }
  };

  public getInvoiceById = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      const result = await this.invoiceService.getInvoiceById(id);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };

  public getAllInvoices = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const paginationQuery = plainToInstance(AdminPaginationQueryDto, req.query);
      const errors = await validate(paginationQuery);
      if (errors.length > 0) return next(new HttpException(400, 'Error de validación en query params', errors));
      const result = await this.invoiceService.getAllInvoices(paginationQuery);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };

  public updateInvoice = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      const dto = plainToInstance(UpdateInvoiceDto, req.body);
      const errors = await validate(dto);
      if (errors.length > 0) return next(new HttpException(400, 'Error de validación DTO', errors));
      if (!req.user) return next(new HttpException(401, 'Usuario no autenticado'));
      const result = await this.invoiceService.updateInvoice(id, dto, req.user as any);
      res.status(200).json({ message: 'Factura actualizada', data: result });
    } catch (error) {
      next(error);
    }
  };

  public deleteInvoice = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      if (!req.user) return next(new HttpException(401, 'Usuario no autenticado'));
      await this.invoiceService.deleteInvoice(id);
      res.status(204).send();
    } catch (error) {
      next(error);
    }
  };

  public uploadInvoiceFile = async (_req: Request, res: Response, next: NextFunction) => {
    try {
      // Placeholder implementation
      res.status(200).json({ message: 'uploadInvoiceFile placeholder' });
    } catch (error) {
      next(error);
    }
  };

  public updateInvoicePaymentStatus = async (_req: Request, res: Response, next: NextFunction) => {
    try {
      // Placeholder implementation
      res.status(200).json({ message: 'updateInvoicePaymentStatus placeholder' });
    } catch (error) {
      next(error);
    }
  };

  public deleteInvoiceFile = async (_req: Request, res: Response, next: NextFunction) => {
    try {
      // Placeholder implementation
      res.status(200).json({ message: 'deleteInvoiceFile placeholder' });
    } catch (error) {
      next(error);
    }
  };

  // --- Solicitudes de Crédito ---
  public createCreditRequest = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const dto = plainToInstance(CreateCreditRequestDto, req.body);
      const errors = await validate(dto);
      if (errors.length > 0) return next(new HttpException(400, 'Error de validación DTO', errors));
      if (!req.user || !req.user.email) return next(new HttpException(401, 'Usuario no autenticado o sin email.'));
      const result = await this.creditRequestService.createCreditRequest(dto, req.user as any);
      res.status(201).json({ message: 'Solicitud de crédito creada', data: result });
    } catch (error) {
      next(error);
    }
  };

  public uploadCreditRequestFile = async (_req: Request, res: Response, next: NextFunction) => {
    try {
      // Placeholder implementation
      res.status(200).json({ message: 'uploadCreditRequestFile placeholder' });
    } catch (error) {
      next(error);
    }
  };

  public updateCreditRequestStatus = async (_req: Request, res: Response, next: NextFunction) => {
    try {
      // Placeholder implementation
      res.status(200).json({ message: 'updateCreditRequestStatus placeholder' });
    } catch (error) {
      next(error);
    }
  };

  public generateCreditCertificate = async (_req: Request, res: Response, next: NextFunction) => {
    try {
      // Placeholder implementation
      res.status(200).json({ message: 'generateCreditCertificate placeholder' });
    } catch (error) {
      next(error);
    }
  };

  public getCreditRequestById = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      const result = await this.creditRequestService.getCreditRequestById(id);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };

  public getAllCreditRequests = async (_req: Request, res: Response, next: NextFunction) => {
    try {
      const paginationQuery = plainToInstance(AdminPaginationQueryDto, _req.query);
      const errors = await validate(paginationQuery);
      if (errors.length > 0) return next(new HttpException(400, 'Error de validación en query params', errors));
      const result = await this.creditRequestService.getAllCreditRequests(paginationQuery);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };

  public deleteCreditRequest = async (_req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(_req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      if (!_req.user) return next(new HttpException(401, 'Usuario no autenticado'));
      await this.creditRequestService.deleteCreditRequest(id);
      res.status(204).send();
    } catch (error) {
      next(error);
    }
  };

  // --- Dashboard ---
  public getAdminDashboardData = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const graphQuery = plainToInstance(GetAdminDashboardGraphDto, req.query);
      const errors = await validate(graphQuery);
      if (errors.length > 0) return next(new HttpException(400, 'Error de validación en query params', errors));
      const result = await this.adminDashboardService.getAggregatedData(graphQuery);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };

  public downloadAdminDashboardExcel = async (_req: Request, res: Response, next: NextFunction) => {
    try {
      const graphQuery = plainToInstance(GetAdminDashboardGraphDto, _req.query);
      const errors = await validate(graphQuery);
      if (errors.length > 0) return next(new HttpException(400, 'Error de validación en query params', errors));
      const result = await this.adminDashboardService.generateAggregatedDataExcel(graphQuery);
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', 'attachment; filename=dashboard-report.xlsx');
      res.status(200).send(result);
    } catch (error) {
      next(error);
    }
  };

  // --- Mis Solicitudes ---
  public getMyRequests = async (req: RequestWithUser, _res: Response, next: NextFunction) => {
    try {
      if (!req.user) return next(new HttpException(401, 'Usuario no autenticado'));
      
      // TODO: Implementar lógica para obtener solicitudes del usuario actual
      // Por ahora, devolver un mensaje placeholder
      _res.status(200).json({ 
        message: "Endpoint 'Mis Solicitudes' a implementar",
        data: [],
        total: 0,
        page: 1,
        limit: 10
      });
    } catch (error) {
      next(error);
    }
  };

  // --- Solicitud de Viáticos ---
  public createTravelRequest = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const dto = plainToInstance(CreateTravelRequestDto, req.body);
      const errors = await validate(dto);
      if (errors.length > 0) {
        return next(new HttpException(400, 'Error de validación DTO', errors));
      }
      // Validaciones personalizadas
      const now = new Date();
      const start = new Date(dto.travelStartDate);
      const end = new Date(dto.travelEndDate);
      if (start > end) {
        return next(new HttpException(400, 'La fecha de inicio no puede ser posterior a la fecha de fin.'));
      }
      if (start < now) {
        return next(new HttpException(400, 'No se permiten fechas de viaje en el pasado.'));
      }
      if (dto.requestedAmount <= 0) {
        return next(new HttpException(400, 'El monto solicitado debe ser mayor a cero.'));
      }
      if (!req.user || !req.user.email) return next(new HttpException(401, 'Usuario no autenticado o sin email.'));
      const result = await this.packageRequestService.createTravelRequest(dto, req.user as any);
      res.status(201).json({ message: 'Solicitud de viáticos creada', data: result });
    } catch (error) {
      next(error);
    }
  };

  // --- Solicitud de Tiempo Extra ---
  public createOvertimeRequest = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const dto = plainToInstance(CreateOvertimeRequestDto, req.body);
      const errors = await validate(dto);
      if (errors.length > 0) {
        return next(new HttpException(400, 'Error de validación DTO', errors));
      }
      // Validaciones personalizadas
      const now = new Date();
      const overtimeDate = new Date(dto.overtimeDate);
      if (overtimeDate < now) {
        return next(new HttpException(400, 'No se permiten fechas de tiempo extra en el pasado.'));
      }
      // Validar formato y rango de horas
      const start = dto.startTime;
      const end = dto.endTime;
      const [startHour, startMin] = start.split(':').map(Number);
      const [endHour, endMin] = end.split(':').map(Number);
      const startDate = new Date(overtimeDate);
      startDate.setHours(startHour, startMin, 0, 0);
      const endDate = new Date(overtimeDate);
      endDate.setHours(endHour, endMin, 0, 0);
      if (startDate >= endDate) {
        return next(new HttpException(400, 'La hora de inicio debe ser menor a la hora de fin.'));
      }
      // Máximo 4 horas extra por día
      const diffMs = endDate.getTime() - startDate.getTime();
      const diffHrs = diffMs / (1000 * 60 * 60);
      if (diffHrs > 4) {
        return next(new HttpException(400, 'No se permiten más de 4 horas extra por día.'));
      }
      if (!req.user || !req.user.email) return next(new HttpException(401, 'Usuario no autenticado o sin email.'));
      const result = await this.packageRequestService.createOvertimeRequest(dto, req.user as any);
      res.status(201).json({ message: 'Solicitud de tiempo extra creada', data: result });
    } catch (error) {
      next(error);
    }
  };

  // --- Aprobar/Rechazar Solicitud de Viáticos ---
  public updateTravelRequestStatus = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      const dto = plainToInstance(UpdateTravelRequestStatusDto, req.body);
      const errors = await validate(dto);
      if (errors.length > 0) {
        return next(new HttpException(400, 'Error de validación DTO', errors));
      }
      if (!req.user || !req.user.id) return next(new HttpException(401, 'Usuario no autenticado'));
      // Solo ROLE_ADMIN o ROLE_MANAGER pueden aprobar/rechazar
      if (!req.user.roles?.some(r => r.name === Role.Admin || r.name === Role.Manager)) {
        return next(new HttpException(403, 'No tiene permisos para aprobar/rechazar solicitudes.'));
      }
      const result = await this.packageRequestService.updateTravelRequestStatus(id, {
        ...dto,
        approverId: req.user.id,
      });
      res.status(200).json({ message: 'Estado de solicitud de viáticos actualizado', data: result });
    } catch (error) {
      next(error);
    }
  };

  // --- Aprobar/Rechazar Solicitud de Tiempo Extra ---
  public updateOvertimeRequestStatus = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      const dto = plainToInstance(UpdateOvertimeRequestStatusDto, req.body);
      const errors = await validate(dto);
      if (errors.length > 0) {
        return next(new HttpException(400, 'Error de validación DTO', errors));
      }
      if (!req.user || !req.user.id) return next(new HttpException(401, 'Usuario no autenticado'));
      // Solo ROLE_ADMIN o ROLE_MANAGER pueden aprobar/rechazar
      if (!req.user.roles?.some(r => r.name === Role.Admin || r.name === Role.Manager)) {
        return next(new HttpException(403, 'No tiene permisos para aprobar/rechazar solicitudes.'));
      }
      const result = await this.packageRequestService.updateOvertimeRequestStatus(id, {
        ...dto,
        approverId: req.user.id,
      });
      res.status(200).json({ message: 'Estado de solicitud de tiempo extra actualizado', data: result });
    } catch (error) {
      next(error);
    }
  };

  // --- Editar Solicitud de Viáticos (solo creador y si está pendiente) ---
  public updateTravelRequest = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      const dto = plainToInstance(UpdateTravelRequestDto, req.body);
      const errors = await validate(dto);
      if (errors.length > 0) {
        return next(new HttpException(400, 'Error de validación DTO', errors));
      }
      if (!req.user || !req.user.id) return next(new HttpException(401, 'Usuario no autenticado'));
      // Verificar que el usuario sea el creador y la solicitud esté pendiente
      const request = await this.packageRequestService.getTravelRequestById(id);
      if (!request) return next(new HttpException(404, 'Solicitud no encontrada'));
      if (request.requester_id !== req.user.id) {
        return next(new HttpException(403, 'Solo el usuario creador puede editar esta solicitud.'));
      }
      if (request.status !== 'PENDING') {
        return next(new HttpException(400, 'Solo se puede editar una solicitud pendiente.'));
      }
      const result = await this.packageRequestService.updateTravelRequest(id, dto);
      res.status(200).json({ message: 'Solicitud de viáticos actualizada', data: result });
    } catch (error) {
      next(error);
    }
  };

  // --- Editar Solicitud de Tiempo Extra (solo creador y si está pendiente) ---
  public updateOvertimeRequest = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      const dto = plainToInstance(UpdateOvertimeRequestDto, req.body);
      const errors = await validate(dto);
      if (errors.length > 0) {
        return next(new HttpException(400, 'Error de validación DTO', errors));
      }
      if (!req.user || !req.user.id) return next(new HttpException(401, 'Usuario no autenticado'));
      // Verificar que el usuario sea el creador y la solicitud esté pendiente
      const request = await this.packageRequestService.getOvertimeRequestById(id);
      if (!request) return next(new HttpException(404, 'Solicitud no encontrada'));
      if (request.requester_id !== req.user.id) {
        return next(new HttpException(403, 'Solo el usuario creador puede editar esta solicitud.'));
      }
      if (request.status !== 'PENDING') {
        return next(new HttpException(400, 'Solo se puede editar una solicitud pendiente.'));
      }
      const result = await this.packageRequestService.updateOvertimeRequest(id, dto);
      res.status(200).json({ message: 'Solicitud de tiempo extra actualizada', data: result });
    } catch (error) {
      next(error);
    }
  };

  // --- Borrar Solicitud de Viáticos (solo creador y si está pendiente) ---
  public deleteTravelRequest = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      if (!req.user || !req.user.id) return next(new HttpException(401, 'Usuario no autenticado'));
      const request = await this.packageRequestService.getTravelRequestById(id);
      if (!request) return next(new HttpException(404, 'Solicitud no encontrada'));
      if (request.requester_id !== req.user.id) {
        return next(new HttpException(403, 'Solo el usuario creador puede borrar esta solicitud.'));
      }
      if (request.status !== 'PENDING') {
        return next(new HttpException(400, 'Solo se puede borrar una solicitud pendiente.'));
      }
      await this.packageRequestService.deleteTravelRequest(id);
      res.status(204).send();
    } catch (error) {
      next(error);
    }
  };

  // --- Borrar Solicitud de Tiempo Extra (solo creador y si está pendiente) ---
  public deleteOvertimeRequest = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      if (!req.user || !req.user.id) return next(new HttpException(401, 'Usuario no autenticado'));
      const request = await this.packageRequestService.getOvertimeRequestById(id);
      if (!request) return next(new HttpException(404, 'Solicitud no encontrada'));
      if (request.requester_id !== req.user.id) {
        return next(new HttpException(403, 'Solo el usuario creador puede borrar esta solicitud.'));
      }
      if (request.status !== 'PENDING') {
        return next(new HttpException(400, 'Solo se puede borrar una solicitud pendiente.'));
      }
      await this.packageRequestService.deleteOvertimeRequest(id);
      res.status(204).send();
    } catch (error) {
      next(error);
    }
  };

  // --- Listar solicitudes de viáticos del usuario autenticado ---
  public getMyTravelRequests = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      if (!req.user || !req.user.id) return next(new HttpException(401, 'Usuario no autenticado'));
      const result = await this.packageRequestService.getTravelRequestsByUser(req.user.id);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };

  // --- Consultar detalle de solicitud de viáticos del usuario autenticado ---
  public getMyTravelRequestById = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      if (!req.user || !req.user.id) return next(new HttpException(401, 'Usuario no autenticado'));
      const request = await this.packageRequestService.getTravelRequestById(id);
      if (!request) return next(new HttpException(404, 'Solicitud no encontrada'));
      if (request.requester_id !== req.user.id) {
        return next(new HttpException(403, 'Solo el usuario creador puede consultar esta solicitud.'));
      }
      res.status(200).json(request);
    } catch (error) {
      next(error);
    }
  };

  // --- Listar solicitudes de tiempo extra del usuario autenticado ---
  public getMyOvertimeRequests = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      if (!req.user || !req.user.id) return next(new HttpException(401, 'Usuario no autenticado'));
      const result = await this.packageRequestService.getOvertimeRequestsByUser(req.user.id);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };

  // --- Consultar detalle de solicitud de tiempo extra del usuario autenticado ---
  public getMyOvertimeRequestById = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) return next(new HttpException(400, 'ID inválido'));
      if (!req.user || !req.user.id) return next(new HttpException(401, 'Usuario no autenticado'));
      const request = await this.packageRequestService.getOvertimeRequestById(id);
      if (!request) return next(new HttpException(404, 'Solicitud no encontrada'));
      if (request.requester_id !== req.user.id) {
        return next(new HttpException(403, 'Solo el usuario creador puede consultar esta solicitud.'));
      }
      res.status(200).json(request);
    } catch (error) {
      next(error);
    }
  };

  // --- Admin/global listing of travel requests with advanced filters ---
  public getAllTravelRequests = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      // Only admin/manager roles should reach here (enforced by route middleware)
      const filters = {
        page: req.query.page ? Number(req.query.page) : 1,
        limit: req.query.limit ? Number(req.query.limit) : 10,
        sortBy: req.query.sortBy as string,
        sortOrder: req.query.sortOrder as 'ASC' | 'DESC',
        status: req.query.status as string,
        departmentId: req.query.departmentId ? Number(req.query.departmentId) : undefined,
        requesterId: req.query.requesterId ? Number(req.query.requesterId) : undefined,
        startDate: req.query.startDate as string,
        endDate: req.query.endDate as string,
        search: req.query.search as string,
      };
      const result = await this.packageRequestService.getAllTravelRequests(filters);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };

  // --- Admin/global listing of overtime requests with advanced filters ---
  public getAllOvertimeRequests = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      // Only admin/manager roles should reach here (enforced by route middleware)
      const filters = {
        page: req.query.page ? Number(req.query.page) : 1,
        limit: req.query.limit ? Number(req.query.limit) : 10,
        sortBy: req.query.sortBy as string,
        sortOrder: req.query.sortOrder as 'ASC' | 'DESC',
        status: req.query.status as string,
        departmentId: req.query.departmentId ? Number(req.query.departmentId) : undefined,
        requesterId: req.query.requesterId ? Number(req.query.requesterId) : undefined,
        startDate: req.query.startDate as string,
        endDate: req.query.endDate as string,
        search: req.query.search as string,
      };
      const result = await this.packageRequestService.getAllOvertimeRequests(filters);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };
}
