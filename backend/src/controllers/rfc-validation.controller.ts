import { Request, Response, NextFunction } from 'express';
import { RfcValidationService } from '../services/rfc-validation.service';

/**
 * @class RfcValidationController
 * @description Controlador para gestionar las peticiones HTTP relacionadas con la validación de RFC.
 */
export class RfcValidationController {
  private rfcValidationService = new RfcValidationService();

  /**
   * @description Valida un RFC con el servicio externo del SAT.
   */
  validateRfc = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { rfc } = req.params;
      const validationResult = await this.rfcValidationService.validateRfc(rfc);
      
      if (validationResult.valid) {
        res.status(200).json({ 
          data: { 
            valid: true, 
            message: 'RFC válido en el SAT.',
            details: validationResult.data
          } 
        });
      } else {
        res.status(200).json({ 
          data: { 
            valid: false, 
            message: 'RFC no encontrado o inválido en el SAT.',
            details: validationResult.data
          } 
        });
      }
    } catch (error) {
      next(error);
    }
  };

  /**
   * @description Obtiene información fiscal completa de un RFC.
   */
  getFiscalInfo = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { rfc } = req.params;
      const fiscalInfo = await this.rfcValidationService.getFiscalInfo(rfc);
      
      res.status(200).json({ 
        data: fiscalInfo
      });
    } catch (error) {
      next(error);
    }
  };
} 