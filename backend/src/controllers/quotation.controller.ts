import { Request, Response } from 'express';
import { DataSource } from 'typeorm';
import { QuotationService } from '../services/quotation.service';

export class QuotationController {
  private quotationService: QuotationService;

  constructor(dataSource: DataSource) {
    this.quotationService = new QuotationService(dataSource);
  }

  async getQuotationsByClientId(req: Request, res: Response): Promise<void> {
    try {
      const clientId = parseInt(req.params.id, 10);
      if (isNaN(clientId)) {
        res.status(400).json({ message: 'ID de cliente inválido' });
        return;
      }
      const quotations = await this.quotationService.getQuotationsByClientId(clientId);
      res.json({ data: quotations });
    } catch (error: any) {
      console.error('[QuotationController][getQuotationsByClientId] Error:', error);
      res.status(500).json({ message: 'Error al obtener cotizaciones del cliente' });
    }
  }
} 