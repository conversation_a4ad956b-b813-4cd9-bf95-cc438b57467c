import { Request, Response } from 'express';

export class DashboardController {
  async getStats(_req: Request, res: Response) {
    try {
      // In a real application, you would fetch this data from your database
      const stats = {
        users: 150,
        sales: 12000,
        products: 75,
        pendingOrders: 5,
      };
      res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      res.status(500).json({ success: false, message: 'Error fetching dashboard stats' });
    }
  }
}

export const dashboardController = new DashboardController();