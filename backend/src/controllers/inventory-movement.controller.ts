import { Request, Response } from 'express';
import { DataSource } from 'typeorm';
import { InventoryMovementService } from '../services/inventory-movement.service';
import { NotificationService } from '../services/notification.service';
import { 
  CreateInventoryMovementDto, 
  UpdateInventoryMovementDto, 
  ConfirmInventoryMovementDto,
  InventoryMovementFilterDto,
  QRScanDataDto,
  BulkInventoryMovementDto
} from '../dto/inventory-movement.dto';
import { validateOrReject } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { AuthenticatedRequest } from '../interfaces/request.interface';

export class InventoryMovementController {
  private movementService: InventoryMovementService;

  constructor(dataSource: DataSource, notificationService: NotificationService) {
    this.movementService = new InventoryMovementService(dataSource, notificationService);
  }

  /**
   * Crear un nuevo movimiento de inventario
   * POST /api/inventory-movements
   */
  async createMovement(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const createDto = plainToClass(CreateInventoryMovementDto, req.body);
      await validateOrReject(createDto);

      if (!req.user?.id) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }

      const movement = await this.movementService.createMovement(
        createDto, 
        req.user.id
      );

      res.status(201).json({
        success: true,
        message: 'Movimiento creado exitosamente',
        data: {
          id: movement.id,
          folio: movement.folio,
          tipo: movement.tipo,
          estado: movement.estado,
          cantidad: movement.cantidad,
          productoId: movement.productoId
        }
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Datos de entrada inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Obtener lista de movimientos con filtros y paginación
   * GET /api/inventory-movements
   */
  async getMovements(req: Request, res: Response): Promise<void> {
    try {
      const filterDto = plainToClass(InventoryMovementFilterDto, req.query);
      await validateOrReject(filterDto);

      const result = await this.movementService.getMovements(filterDto);

      res.status(200).json({
        success: true,
        message: 'Movimientos obtenidos exitosamente',
        data: result.data,
        pagination: result.pagination
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Parámetros de búsqueda inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Obtener un movimiento por ID
   * GET /api/inventory-movements/:id
   */
  async getMovementById(req: Request, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: 'ID de movimiento inválido'
        });
        return;
      }

      const movement = await this.movementService.getMovementById(id);

      res.status(200).json({
        success: true,
        message: 'Movimiento obtenido exitosamente',
        data: movement
      });
    } catch (error: any) {
      if (error.message.includes('no encontrado')) {
        res.status(404).json({
          success: false,
          message: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Actualizar un movimiento
   * PUT /api/inventory-movements/:id
   */
  async updateMovement(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: 'ID de movimiento inválido'
        });
        return;
      }

      const updateDto = plainToClass(UpdateInventoryMovementDto, req.body);
      await validateOrReject(updateDto);

      if (!req.user?.id) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }

      const movement = await this.movementService.updateMovement(id, updateDto, req.user.id);

      res.status(200).json({
        success: true,
        message: 'Movimiento actualizado exitosamente',
        data: movement
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Datos de entrada inválidos',
          errors: validationErrors
        });
      } else if (error.message.includes('no encontrado')) {
        res.status(404).json({
          success: false,
          message: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Confirmar un movimiento
   * POST /api/inventory-movements/:id/confirm
   */
  async confirmMovement(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: 'ID de movimiento inválido'
        });
        return;
      }

      const confirmDto = plainToClass(ConfirmInventoryMovementDto, req.body);
      await validateOrReject(confirmDto);

      if (!req.user?.id) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }

      const movement = await this.movementService.confirmMovement(
        id, 
        confirmDto, 
        req.user.id
      );

      res.status(200).json({
        success: true,
        message: 'Movimiento confirmado exitosamente',
        data: {
          id: movement.id,
          folio: movement.folio,
          estado: movement.estado,
          voucherId: movement.voucherId,
          voucherPath: movement.voucherPath
        }
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Datos de entrada inválidos',
          errors: validationErrors
        });
      } else if (error.message.includes('no encontrado')) {
        res.status(404).json({
          success: false,
          message: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Cancelar un movimiento
   * POST /api/inventory-movements/:id/cancel
   */
  async cancelMovement(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: 'ID de movimiento inválido'
        });
        return;
      }

      if (!req.user?.id) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }

      const movement = await this.movementService.cancelMovement(id, req.user.id);

      res.status(200).json({
        success: true,
        message: 'Movimiento cancelado exitosamente',
        data: {
          id: movement.id,
          folio: movement.folio,
          estado: movement.estado
        }
      });
    } catch (error: any) {
      if (error.message.includes('no encontrado')) {
        res.status(404).json({
          success: false,
          message: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Procesar datos QR
   * POST /api/inventory-movements/qr/scan
   */
  async processQRData(req: Request, res: Response): Promise<void> {
    try {
      const qrDataDto = plainToClass(QRScanDataDto, req.body);
      await validateOrReject(qrDataDto);

      const result = await this.movementService.processQRData(qrDataDto);

      if (result.error) {
        res.status(400).json({
          success: false,
          message: result.error,
          data: null
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Datos QR procesados exitosamente',
        data: result
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Datos QR inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Crear movimiento desde QR
   * POST /api/inventory-movements/qr/create
   */
  async createMovementFromQR(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const createDto = plainToClass(CreateInventoryMovementDto, req.body);
      await validateOrReject(createDto);

      if (!req.user?.id) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }

      const movement = await this.movementService.createMovementFromQR(
        createDto, 
        req.user.id
      );

      res.status(201).json({
        success: true,
        message: 'Movimiento creado desde QR exitosamente',
        data: {
          id: movement.id,
          folio: movement.folio,
          tipo: movement.tipo,
          estado: movement.estado,
          cantidad: movement.cantidad,
          productoId: movement.productoId
        }
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Datos de entrada inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Obtener estadísticas de movimientos
   * GET /api/inventory-movements/stats/overview
   */
  async getMovementStats(_req: Request, res: Response): Promise<void> {
    try {
      const stats = await this.movementService.getMovementStats();

      res.status(200).json({
        success: true,
        message: 'Estadísticas obtenidas exitosamente',
        data: stats
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: 'Error interno del servidor',
        error: error.message
      });
    }
  }

  /**
   * Obtener movimientos por tipo
   * GET /api/inventory-movements/type/:tipo
   */
  async getMovementsByType(req: Request, res: Response): Promise<void> {
    try {
      const tipo = req.params.tipo;
      const filterDto = plainToClass(InventoryMovementFilterDto, req.query);
      await validateOrReject(filterDto);

      const result = await this.movementService.getMovementsByType(tipo, filterDto);

      res.status(200).json({
        success: true,
        message: `Movimientos de tipo ${tipo} obtenidos exitosamente`,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Parámetros de búsqueda inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Obtener movimientos por estado
   * GET /api/inventory-movements/status/:estado
   */
  async getMovementsByStatus(req: Request, res: Response): Promise<void> {
    try {
      const estado = req.params.estado;
      const filterDto = plainToClass(InventoryMovementFilterDto, req.query);
      await validateOrReject(filterDto);

      const result = await this.movementService.getMovementsByStatus(estado, filterDto);

      res.status(200).json({
        success: true,
        message: `Movimientos con estado ${estado} obtenidos exitosamente`,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Parámetros de búsqueda inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Obtener movimientos por producto
   * GET /api/inventory-movements/product/:productoId
   */
  async getMovementsByProduct(req: Request, res: Response): Promise<void> {
    try {
      const productoId = parseInt(req.params.productoId);
      
      if (isNaN(productoId)) {
        res.status(400).json({
          success: false,
          message: 'ID de producto inválido'
        });
        return;
      }

      const filterDto = plainToClass(InventoryMovementFilterDto, req.query);
      await validateOrReject(filterDto);

      const result = await this.movementService.getMovementsByProduct(productoId, filterDto);

      res.status(200).json({
        success: true,
        message: `Movimientos del producto ${productoId} obtenidos exitosamente`,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Parámetros de búsqueda inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Obtener mis movimientos
   * GET /api/inventory-movements/user/me
   */
  async getMyMovements(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filterDto = plainToClass(InventoryMovementFilterDto, req.query);
      await validateOrReject(filterDto);

      if (!req.user?.id) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }

      const result = await this.movementService.getMyMovements(filterDto, req.user.id);

      res.status(200).json({
        success: true,
        message: 'Mis movimientos obtenidos exitosamente',
        data: result.data,
        pagination: result.pagination
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Parámetros de búsqueda inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Obtener movimientos pendientes
   * GET /api/inventory-movements/pending/list
   */
  async getPendingMovements(req: Request, res: Response): Promise<void> {
    try {
      const filterDto = plainToClass(InventoryMovementFilterDto, req.query);
      await validateOrReject(filterDto);

      const result = await this.movementService.getPendingMovements(filterDto);

      res.status(200).json({
        success: true,
        message: 'Movimientos pendientes obtenidos exitosamente',
        data: result.data,
        pagination: result.pagination
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Parámetros de búsqueda inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Crear movimientos masivos
   * POST /api/inventory-movements/bulk
   */
  async createBulkMovements(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const bulkDto = plainToClass(BulkInventoryMovementDto, req.body);
      await validateOrReject(bulkDto);

      if (!req.user?.id) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }

      const result = await this.movementService.createBulkMovements(
        bulkDto, 
        req.user.id
      );

      res.status(201).json({
        success: true,
        message: `${result.created} movimientos creados exitosamente`,
        data: {
          created: result.created,
          failed: result.failed,
          errors: result.errors
        }
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Datos de entrada inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Obtener movimientos recientes
   * GET /api/inventory-movements/recent/list
   */
  async getRecentMovements(req: Request, res: Response): Promise<void> {
    try {
      const limit = parseInt(req.query.limit as string) || 10;
      
      if (isNaN(limit) || limit < 1 || limit > 100) {
        res.status(400).json({
          success: false,
          message: 'Límite debe ser un número entre 1 y 100'
        });
        return;
      }

      const movements = await this.movementService.getRecentMovements(limit);

      res.status(200).json({
        success: true,
        message: 'Movimientos recientes obtenidos exitosamente',
        data: movements
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: 'Error interno del servidor',
        error: error.message
      });
    }
  }

  /**
   * Buscar por folio
   * GET /api/inventory-movements/search/folio/:folio
   */
  async searchByFolio(req: Request, res: Response): Promise<void> {
    try {
      const folio = req.params.folio;

      if (!folio) {
        res.status(400).json({
          success: false,
          message: 'Folio requerido'
        });
        return;
      }

      const movements = await this.movementService.searchByFolio(folio);

      res.status(200).json({
        success: true,
        message: `Búsqueda por folio "${folio}" completada`,
        data: movements
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: 'Error interno del servidor',
        error: error.message
      });
    }
  }

  /**
   * Obtener resumen por rango de fechas
   * GET /api/inventory-movements/summary/date-range
   */
  async getMovementsSummary(req: Request, res: Response): Promise<void> {
    try {
      const fechaDesde = req.query.fechaDesde as string;
      const fechaHasta = req.query.fechaHasta as string;

      if (!fechaDesde || !fechaHasta) {
        res.status(400).json({
          success: false,
          message: 'Fechas de inicio y fin requeridas'
        });
        return;
      }

      const summary = await this.movementService.getMovementsSummary(fechaDesde, fechaHasta);

      res.status(200).json({
        success: true,
        message: 'Resumen de movimientos obtenido exitosamente',
        data: summary
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: 'Error interno del servidor',
        error: error.message
      });
    }
  }
}