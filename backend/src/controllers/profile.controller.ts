import { Response, NextFunction } from 'express';
import { DataSource } from 'typeorm';
import { UpdateProfileDto, UpdatePasswordDto } from '../dtos/profile.dto';
import { UserService } from '../services/user.service';
import { AuthService } from '../services/auth.service';
import { successResponse, badRequestResponse } from '../utils/api-response';
import { RequestWithUser } from '../interfaces/request.interface';
import { authService as globalAuthService } from '../services/auth.service';

export class ProfileController {
  private userService: UserService;
  private authService: AuthService;

  constructor(dataSource: DataSource) {
    this.userService = new UserService(dataSource);
    this.authService = globalAuthService; // AuthService no depende de DataSource
  }

  /**
   * Obtiene el perfil del usuario autenticado
   */
  public getProfile = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      if (!req.user) return badRequestResponse(res, 'Usuario no autenticado');
      const userId = req.user.id;
      const user = await this.userService.getUserById(userId);
      if (!user) {
        return badRequestResponse(res, 'Usuario no encontrado');
      }
      successResponse(res, user, 'Perfil obtenido exitosamente');
    } catch (error: unknown) {
      if (error instanceof Error) {
        next(error);
      } else {
        next(new Error('Error desconocido al obtener el perfil'));
      }
    }
  };

  /**
   * Actualiza el perfil del usuario autenticado
   */
  public updateProfile = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      if (!req.user) return badRequestResponse(res, 'Usuario no autenticado');
      const userId = req.user.id;
      const userData: UpdateProfileDto = req.body;

      // Verificar si se está intentando actualizar el correo electrónico
      if (userData.email) {
        const existingUser = await this.userService.getUserByEmail(userData.email);
        if (existingUser && existingUser.id !== userId) {
          return badRequestResponse(res, 'El correo electrónico ya está en uso');
        }
      }

      // Actualizar el perfil
      const updatedUser = await this.userService.updateUser(userId, userData);
      successResponse(res, updatedUser, 'Perfil actualizado exitosamente');
    } catch (error: unknown) {
      if (error instanceof Error) {
        next(error);
      } else {
        next(new Error('Error desconocido al actualizar el perfil'));
      }
    }
  };

  /**
   * Actualiza la contraseña del usuario autenticado
   */
  public updatePassword = async (req: RequestWithUser, res: Response, next: NextFunction) => {
    try {
      if (!req.user) return badRequestResponse(res, 'Usuario no autenticado');
      const userId = req.user.id;
      const { currentPassword, newPassword } = req.body as UpdatePasswordDto;

      if (!currentPassword || !newPassword) {
        return badRequestResponse(res, 'Se requieren tanto la contraseña actual como la nueva');
      }

      // Obtener el usuario con la contraseña
      const user = await this.userService.getUserById(userId);

      if (!user) {
        return badRequestResponse(res, 'Usuario no encontrado');
      }

      // Verificar la contraseña actual
      const isPasswordValid = await this.authService.comparePasswords(currentPassword, user.password);
      if (!isPasswordValid) {
        return badRequestResponse(res, 'La contraseña actual es incorrecta');
      }

      // Actualizar la contraseña
      await this.userService.updateUser(userId, { password: newPassword });
      
      successResponse(res, null, 'Contraseña actualizada exitosamente');
    } catch (error: unknown) {
      if (error instanceof Error) {
        next(error);
      } else {
        next(new Error('Error desconocido al actualizar la contraseña'));
      }
    }
  };
}

