import { Request, Response } from 'express';
import { DataSource } from 'typeorm';
import { InternalRequestService } from '../services/internal-request.service';
import { NotificationService } from '../services/notification.service';
import {
  CreateInternalRequestDto,
  UpdateInternalRequestDto,
  SubmitInternalRequestDto,
  ApproveInternalRequestDto,
  RejectInternalRequestDto,
  RequestInfoInternalRequestDto,
  InternalRequestFilterDto
} from '../dto/internal-request.dto';
import { validateOrReject } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { AuthenticatedRequest } from '../interfaces/request.interface';

export class InternalRequestController {
  private requestService: InternalRequestService;

  constructor(dataSource: DataSource, notificationService: NotificationService) {
    this.requestService = new InternalRequestService(dataSource, notificationService);
  }

  /**
   * Crear una nueva solicitud interna
   * POST /api/internal-requests
   */
  async createRequest(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const createDto = plainToClass(CreateInternalRequestDto, req.body);
      await validateOrReject(createDto);

      if (!req.user?.id) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }

      const request = await this.requestService.createRequest(
        createDto,
        req.user.id,
        req.user.id
      );

      res.status(201).json({
        success: true,
        message: 'Solicitud creada exitosamente',
        data: {
          id: request.id,
          title: request.title,
          requestType: request.requestType,
          status: request.status
        }
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Datos de entrada inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Obtener lista de solicitudes con filtros y paginación
   * GET /api/internal-requests
   */
  async getRequests(req: Request, res: Response): Promise<void> {
    try {
      const filterDto = plainToClass(InternalRequestFilterDto, req.query);
      await validateOrReject(filterDto);

      const result = await this.requestService.getRequests(filterDto);

      res.status(200).json({
        success: true,
        message: 'Solicitudes obtenidas exitosamente',
        data: result.data,
        pagination: result.pagination
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Parámetros de búsqueda inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Obtener una solicitud por ID
   * GET /api/internal-requests/:id
   */
  async getRequestById(req: Request, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: 'ID de solicitud inválido'
        });
        return;
      }

      const request = await this.requestService.getRequestById(id);

      res.status(200).json({
        success: true,
        message: 'Solicitud obtenida exitosamente',
        data: request
      });
    } catch (error: any) {
      if (error.message.includes('no encontrada')) {
        res.status(404).json({
          success: false,
          message: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Actualizar una solicitud
   * PUT /api/internal-requests/:id
   */
  async updateRequest(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: 'ID de solicitud inválido'
        });
        return;
      }

      const updateDto = plainToClass(UpdateInternalRequestDto, req.body);
      await validateOrReject(updateDto);

      if (!req.user?.id) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }

      const request = await this.requestService.updateRequest(id, updateDto, req.user.id);

      res.status(200).json({
        success: true,
        message: 'Solicitud actualizada exitosamente',
        data: request
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Datos de entrada inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Enviar solicitud para aprobación
   * POST /api/internal-requests/:id/submit
   */
  async submitRequest(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: 'ID de solicitud inválido'
        });
        return;
      }

      const submitDto = plainToClass(SubmitInternalRequestDto, req.body);
      await validateOrReject(submitDto);

      if (!req.user?.id) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }

      const request = await this.requestService.submitRequest(id, submitDto, req.user.id);

      res.status(200).json({
        success: true,
        message: 'Solicitud enviada para aprobación exitosamente',
        data: {
          id: request.id,
          status: request.status,
          approverId: request.approverId
        }
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Datos de entrada inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Aprobar una solicitud
   * POST /api/internal-requests/:id/approve
   */
  async approveRequest(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: 'ID de solicitud inválido'
        });
        return;
      }

      const approveDto = plainToClass(ApproveInternalRequestDto, req.body);
      await validateOrReject(approveDto);

      if (!req.user?.id) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }

      const request = await this.requestService.approveRequest(id, approveDto, req.user.id);

      res.status(200).json({
        success: true,
        message: 'Solicitud aprobada exitosamente',
        data: {
          id: request.id,
          status: request.status,
          approvedAmount: request.approvedAmount
        }
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Datos de entrada inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Rechazar una solicitud
   * POST /api/internal-requests/:id/reject
   */
  async rejectRequest(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: 'ID de solicitud inválido'
        });
        return;
      }

      const rejectDto = plainToClass(RejectInternalRequestDto, req.body);
      await validateOrReject(rejectDto);

      if (!req.user?.id) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }

      const request = await this.requestService.rejectRequest(id, rejectDto, req.user.id);

      res.status(200).json({
        success: true,
        message: 'Solicitud rechazada exitosamente',
        data: {
          id: request.id,
          status: request.status,
          rejectionReason: request.rejectionReason
        }
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Datos de entrada inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Solicitar información adicional
   * POST /api/internal-requests/:id/request-info
   */
  async requestInfo(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: 'ID de solicitud inválido'
        });
        return;
      }

      const infoDto = plainToClass(RequestInfoInternalRequestDto, req.body);
      await validateOrReject(infoDto);

      if (!req.user?.id) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }

      const request = await this.requestService.requestInfo(id, infoDto, req.user.id);

      res.status(200).json({
        success: true,
        message: 'Solicitud de información enviada exitosamente',
        data: {
          id: request.id,
          status: request.status
        }
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Datos de entrada inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Cancelar una solicitud
   * POST /api/internal-requests/:id/cancel
   */
  async cancelRequest(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: 'ID de solicitud inválido'
        });
        return;
      }

      if (!req.user?.id) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }

      const request = await this.requestService.cancelRequest(id, req.user.id);

      res.status(200).json({
        success: true,
        message: 'Solicitud cancelada exitosamente',
        data: {
          id: request.id,
          status: request.status
        }
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: 'Error interno del servidor',
        error: error.message
      });
    }
  }

  /**
   * Obtener estadísticas de solicitudes
   * GET /api/internal-requests/stats/overview
   */
  async getRequestStats(_req: Request, res: Response): Promise<void> {
    try {
      const stats = await this.requestService.getRequestStats();

      res.status(200).json({
        success: true,
        message: 'Estadísticas obtenidas exitosamente',
        data: stats
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: 'Error interno del servidor',
        error: error.message
      });
    }
  }

  /**
   * Obtener solicitudes por tipo
   * GET /api/internal-requests/type/:type
   */
  async getRequestsByType(req: Request, res: Response): Promise<void> {
    try {
      const type = req.params.type;
      const filterDto = plainToClass(InternalRequestFilterDto, req.query);
      filterDto.requestType = type as any;
      await validateOrReject(filterDto);

      const result = await this.requestService.getRequests(filterDto);

      res.status(200).json({
        success: true,
        message: `Solicitudes de tipo ${type} obtenidas exitosamente`,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Parámetros de búsqueda inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Obtener solicitudes por estado
   * GET /api/internal-requests/status/:status
   */
  async getRequestsByStatus(req: Request, res: Response): Promise<void> {
    try {
      const status = req.params.status;
      const filterDto = plainToClass(InternalRequestFilterDto, req.query);
      filterDto.status = status as any;
      await validateOrReject(filterDto);

      const result = await this.requestService.getRequests(filterDto);

      res.status(200).json({
        success: true,
        message: `Solicitudes con estado ${status} obtenidas exitosamente`,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Parámetros de búsqueda inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Obtener mis solicitudes (del usuario autenticado)
   * GET /api/internal-requests/my-requests
   */
  async getMyRequests(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user?.id) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }

      const filterDto = plainToClass(InternalRequestFilterDto, req.query);
      filterDto.requesterId = req.user.id;
      await validateOrReject(filterDto);

      const result = await this.requestService.getRequests(filterDto);

      res.status(200).json({
        success: true,
        message: 'Mis solicitudes obtenidas exitosamente',
        data: result.data,
        pagination: result.pagination
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Parámetros de búsqueda inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }

  /**
   * Obtener solicitudes pendientes de aprobación
   * GET /api/internal-requests/pending-approval
   */
  async getPendingApprovalRequests(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user?.id) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }

      const filterDto = plainToClass(InternalRequestFilterDto, req.query);
      filterDto.status = 'PENDING_APPROVAL' as any;
      filterDto.approverId = req.user.id;
      await validateOrReject(filterDto);

      const result = await this.requestService.getRequests(filterDto);

      res.status(200).json({
        success: true,
        message: 'Solicitudes pendientes de aprobación obtenidas exitosamente',
        data: result.data,
        pagination: result.pagination
      });
    } catch (error: any) {
      if (error instanceof Array) {
        const validationErrors = error.map((err: any) => 
          Object.values(err.constraints || {}).join(', ')
        ).join('; ');
        
        res.status(400).json({
          success: false,
          message: 'Parámetros de búsqueda inválidos',
          errors: validationErrors
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error interno del servidor',
          error: error.message
        });
      }
    }
  }
}