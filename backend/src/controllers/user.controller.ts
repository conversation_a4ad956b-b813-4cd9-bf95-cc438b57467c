import { Request, Response } from 'express';
import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { DataSource, ILike } from 'typeorm';
import { CreateUserDto, UpdateUserDto, ChangePasswordDto } from '../dtos/user.dto';
import { NotFoundException, ForbiddenException, BadRequestException } from '../exceptions/http.exception';
import { RequestWithUser } from '../interfaces/request.interface';
import { UserService } from '../services/user.service';
import { ApiResponseBuilder } from '../utils/api-response';
import { PermissionService } from '../services/permission.service';

export class UserController {
  private userService: UserService;

  constructor(dataSource: DataSource) {
    this.userService = new UserService(dataSource);
  }

  async getAllUsers(req: Request, res: Response) {
    try {
      // Parámetros de paginación
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const search = req.query.search as string || '';
      // Filtros de rol
      const role = req.query.role as string | undefined;
      const roleId = req.query.roleId ? Number(req.query.roleId) : undefined;
      // Construir filtros de búsqueda
      let where: any = {};
      if (search) {
        // Buscar por nombre o email (insensible a mayúsculas/minúsculas)
        where = [
          { name: ILike(`%${search}%`) },
          { email: ILike(`%${search}%`) }
        ];
      }
      // Agregar área al filtro si viene en la query
      if (req.query.area) {
        if (Array.isArray(where)) {
          // Si es búsqueda, agregar área a cada condición
          where = where.map(cond => ({ ...cond, area: req.query.area }));
        } else {
          where.area = req.query.area;
        }
      }
      // Pasar filtros de rol al servicio
      const result = await this.userService.getUsers(page, limit, where, { role, roleId });
      // Use ApiResponseBuilder for consistent paginated response
      return new ApiResponseBuilder(res)
        .withPagination(result.data, result.count, page, limit)
        .withMessage('Usuarios obtenidos correctamente')
        .send();
    } catch (error) {
      // Use ApiResponseBuilder for error
      return new ApiResponseBuilder(res)
        .withError(error as Error, 'INTERNAL_ERROR')
        .send();
    }
  }

  async getUserById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const user = await this.userService.getUserById(Number(id));
      
      if (!user) {
        throw new NotFoundException('Usuario no encontrado');
      }
      
      res.json(user);
    } catch (error) {
      throw error;
    }
  }

  async getProfile(req: RequestWithUser, res: Response) {
    try {
      const user = await this.userService.getUserById(Number(req.user?.id));
      
      if (!user) {
        throw new NotFoundException('Usuario no encontrado');
      }
      
      res.json(user);
    } catch (error) {
      throw error;
    }
  }

  async createUser(req: RequestWithUser, res: Response) {
    try {
      const userData = plainToInstance(CreateUserDto, req.body);
      
      // Validar DTO
      const errors = await validate(userData);
      if (errors.length > 0) {
        throw new BadRequestException('Error de validación', errors);
      }
      
      // Crear usuario con auditoría
      const newUser = await this.userService.createUser(userData, req.user?.id);
      
      res.status(201).json(newUser);
    } catch (error) {
      throw error;
    }
  }

  async updateUser(req: RequestWithUser, res: Response) {
    try {
      const { id } = req.params;
      const updateData = plainToInstance(UpdateUserDto, req.body);
      
      // Validar DTO
      const errors = await validate(updateData);
      if (errors.length > 0) {
        throw new BadRequestException('Error de validación', errors);
      }
      
      // Verificar permisos (solo el propio usuario o usuarios con permisos de gestión)
      if (!req.user) {
        throw new ForbiddenException('No autorizado');
      }
      
      // Solo el propio usuario puede actualizar sus datos básicos
      // Los usuarios con permisos de gestión pueden actualizar cualquier usuario
      const isOwner = req.user.id === parseInt(id);
      const canManageUsers = PermissionService.hasAnyPermission(req.user, ['sistemas:users:update', 'sistemas:roles:assign']);
      
      if (!isOwner && !canManageUsers) {
        throw new ForbiddenException('No autorizado');
      }
      
      // Si no tiene permisos de gestión de usuarios, no puede cambiar status
      if (!canManageUsers && updateData.status) {
        throw new ForbiddenException('No autorizado para cambiar el status');
      }
      
      const updatedUser = await this.userService.updateUser(
        Number(id), 
        updateData, 
        req.user.id
      );
      
      return res.status(200).json({ 
        message: 'Usuario actualizado correctamente',
        data: updatedUser 
      });
    } catch (error) {
      throw error;
    }
  }

  async changePassword(req: RequestWithUser, res: Response) {
    try {
      const { id } = req.params;
      const passwordData = plainToInstance(ChangePasswordDto, req.body);
      
      // Validar DTO
      const errors = await validate(passwordData);
      if (errors.length > 0) {
        throw new BadRequestException('Error de validación', errors);
      }
      
      // Verificar permisos (solo el propio usuario o usuarios con permisos de gestión)
      if (!req.user) {
        throw new ForbiddenException('No autorizado');
      }
      
      const isOwner = req.user.id === parseInt(id);
      const canManageUsers = PermissionService.hasAnyPermission(req.user, ['sistemas:users:update', 'sistemas:roles:assign']);
      
      if (!isOwner && !canManageUsers) {
        throw new ForbiddenException('No autorizado');
      }
      
      // Obtener información de la solicitud para auditoría
      const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent') || 'unknown';
      
      const result = await this.userService.changePassword(
        Number(id), 
        passwordData, 
        req.user.id,
        ipAddress,
        userAgent
      );
      
      return res.status(200).json({ 
        message: 'Contraseña actualizada correctamente',
        strengthAnalysis: result.strengthAnalysis,
        metadata: {
          passwordStrength: result.strengthAnalysis.level,
          securityScore: result.strengthAnalysis.score
        }
      });
    } catch (error) {
      throw error;
    }
  }

  async blockUser(req: RequestWithUser, res: Response) {
    try {
      const { id } = req.params;
      // Ya está protegido por adminOnly, no es necesario verificar permisos aquí
      const blockedUser = await this.userService.blockUnblockUser(
        Number(id), 
        true, 
        req.user?.id
      );
      return res.status(200).json({ 
        message: 'Usuario bloqueado correctamente',
        data: blockedUser 
      });
    } catch (error) {
      throw error;
    }
  }

  async unblockUser(req: RequestWithUser, res: Response) {
    try {
      const { id } = req.params;
      // Ya está protegido por adminOnly, no es necesario verificar permisos aquí
      const unblockedUser = await this.userService.blockUnblockUser(
        Number(id), 
        false, 
        req.user?.id
      );
      return res.status(200).json({ 
        message: 'Usuario desbloqueado correctamente',
        data: unblockedUser 
      });
    } catch (error) {
      throw error;
    }
  }

  async deleteUser(req: RequestWithUser, res: Response) {
    try {
      const { id } = req.params;
      
      // Verificar que req.user existe
      if (!req.user) {
        throw new ForbiddenException('No autorizado - usuario no autenticado');
      }
      
      // Verificar permisos de gestión de usuarios
      if (!PermissionService.hasPermission(req.user, 'sistemas:users:delete')) {
        throw new ForbiddenException('No autorizado - requiere permisos de eliminación de usuarios');
      }
      
      await this.userService.deleteUser(Number(id), req.user);
      
      res.status(204).send();
    } catch (error) {
      throw error;
    }
  }

  async inactivateUser(req: RequestWithUser, res: Response) {
    try {
      const { id } = req.params;
      // Ya está protegido por adminOnly, no es necesario verificar permisos aquí
      const updatedUser = await this.userService.updateUser(
        Number(id),
        { status: 'inactivo' },
        req.user?.id
      );
      return res.status(200).json({
        message: 'Usuario inactivado correctamente',
        data: updatedUser
      });
    } catch (error) {
      throw error;
    }
  }
}
