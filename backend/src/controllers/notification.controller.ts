import { Request, Response } from 'express';
import { NotificationService } from '../services/notification.service';
import { CreateNotificationDto, UpdateNotificationDto, NotificationFilterDto, BulkNotificationDto, RoleNotificationDto } from '../dtos/notification.dto';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { AuthenticatedRequest } from '../interfaces/request.interface';
import { getWebSocketService } from '../services/websocket.service';

export class NotificationController {
  constructor(private notificationService: NotificationService) {}

  /**
   * Crear una nueva notificación
   */
  async createNotification(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const dto = plainToClass(CreateNotificationDto, req.body);
      const errors = await validate(dto);

      if (errors.length > 0) {
        res.status(400).json({
          success: false,
          message: 'Datos de entrada inválidos',
          errors: errors.map(err => ({
            property: err.property,
            constraints: err.constraints
          }))
        });
        return;
      }

      // Crear notificación en BD
      const notification = await this.notificationService.createNotification(dto);
      
      // Enviar alerta en tiempo real si el usuario está conectado
      try {
        const wsService = getWebSocketService();
        if (notification.user_id) {
          await wsService.alertNewNotificationToUser(notification.user_id, {
            id: notification.id,
            title: notification.title,
            message: notification.message,
            type: notification.type,
            priority: notification.priority,
            action_url: notification.action_url,
            action_label: notification.action_label,
            created_at: notification.created_at
          });
        }
      } catch (wsError) {
        // No fallar si WebSocket no está disponible
        console.warn('WebSocket no disponible para enviar alerta:', wsError);
      }
      
      res.status(201).json({
        success: true,
        message: 'Notificación creada exitosamente',
        data: notification
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Error interno del servidor',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }
  }

  /**
   * Obtener notificaciones con filtros
   */
  async getNotifications(req: Request, res: Response): Promise<void> {
    console.time('getNotifications');
    console.log('[DEBUG] getNotifications handler start');
    try {
      const filterDto = plainToClass(NotificationFilterDto, req.query);
      const notifications = await this.notificationService.getNotifications(filterDto);
      console.log('[DEBUG] getNotifications service resolved, rows:', notifications.length);
      
      res.status(200).json({
        success: true,
        data: notifications
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Error al obtener notificaciones',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    } finally {
      console.timeEnd('getNotifications');
      console.log('[DEBUG] getNotifications handler end');
    }
  }

  /**
   * Obtener notificaciones del usuario actual (deduplicadas por roles)
   */
  async getUserNotifications(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }

      const filterDto = plainToClass(NotificationFilterDto, req.query);
      // Obtener los IDs de roles del usuario autenticado
      const roleIds = (req.user.roles || []).map((r: any) => typeof r === 'object' ? r.id : r);
      // Usar el nuevo método deduplicado
      const notifications = await this.notificationService.getNotificationsForUserWithRoles(req.user.id, roleIds, filterDto);

      res.status(200).json({
        success: true,
        data: notifications
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Error al obtener notificaciones',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }
  }

  /**
   * Obtener una notificación por ID
   */
  async getNotificationById(req: Request, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id, 10);
      
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: 'ID de notificación inválido'
        });
        return;
      }

      const notification = await this.notificationService.getNotificationById(id);
      
      if (!notification) {
        res.status(404).json({
          success: false,
          message: 'Notificación no encontrada'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: notification
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Error al obtener notificación',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }
  }

  /**
   * Actualizar una notificación
   */
  async updateNotification(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id, 10);
      
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: 'ID de notificación inválido'
        });
        return;
      }

      const dto = plainToClass(UpdateNotificationDto, req.body);
      const errors = await validate(dto);

      if (errors.length > 0) {
        res.status(400).json({
          success: false,
          message: 'Datos de entrada inválidos',
          errors: errors.map(err => ({
            property: err.property,
            constraints: err.constraints
          }))
        });
        return;
      }

      const notification = await this.notificationService.updateNotification(id, dto);
      
      if (!notification) {
        res.status(404).json({
          success: false,
          message: 'Notificación no encontrada'
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Notificación actualizada exitosamente',
        data: notification
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Error al actualizar notificación',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }
  }

  /**
   * Marcar notificación como leída
   */
  async markAsRead(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        res.status(400).json({ success: false, message: 'ID de notificación inválido' });
        return;
      }
      if (!req.user) {
        res.status(401).json({ success: false, message: 'Usuario no autenticado' });
        return;
      }
      // Pasa el userId al servicio
      const notification = await this.notificationService.markAsRead(id, req.user.id);
      if (!notification) {
        res.status(404).json({ success: false, message: 'Notificación no encontrada o no pertenece al usuario' });
        return;
      }
      // Enviar actualización en tiempo real
      try {
        const wsService = getWebSocketService();
        await wsService.notifyNotificationRead(req.user.id, id);
        await wsService.sendUpdatedStats(req.user.id);
      } catch (wsError) {
        console.warn('WebSocket no disponible para actualización:', wsError);
      }

      res.status(200).json({
        success: true,
        message: 'Notificación marcada como leída',
        data: notification
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Error al marcar notificación como leída',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }
  }

  /**
   * Eliminar una notificación
   */
  async deleteNotification(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        res.status(400).json({
          success: false,
          message: 'ID de notificación inválido'
        });
        return;
      }
      if (!req.user) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }
      const deleted = await this.notificationService.deleteNotification(id, req.user.id);
      if (!deleted) {
        res.status(404).json({
          success: false,
          message: 'Notificación no encontrada'
        });
        return;
      }
      res.status(200).json({
        success: true,
        message: 'Notificación eliminada exitosamente'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Error al eliminar notificación',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }
  }

  /**
   * Obtener estadísticas de notificaciones
   */
  async getNotificationStats(req: AuthenticatedRequest, res: Response): Promise<void> {
    console.time('getNotificationStats');
    console.log('[DEBUG] getNotificationStats handler start');
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }

      const stats = await this.notificationService.getNotificationStats(req.user.id);
      console.log('[DEBUG] getNotificationStats service resolved');
      
      res.status(200).json({
        success: true,
        data: stats
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Error al obtener estadísticas',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    } finally {
      console.timeEnd('getNotificationStats');
      console.log('[DEBUG] getNotificationStats handler end');
    }
  }

  /**
   * Crear notificación masiva para múltiples usuarios
   */
  async createBulkNotification(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const dto = plainToClass(BulkNotificationDto, req.body);
      const errors = await validate(dto);

      if (errors.length > 0) {
        res.status(400).json({
          success: false,
          message: 'Datos de entrada inválidos',
          errors: errors.map(err => ({
            property: err.property,
            constraints: err.constraints
          }))
        });
        return;
      }

      // Crear notificaciones en BD
      const notifications = await this.notificationService.sendBulkNotification(dto, dto.userIds);
      
      // Enviar alertas en tiempo real para cada usuario
      try {
        const wsService = getWebSocketService();
        for (const notification of notifications) {
          if (notification.user_id) {
            await wsService.alertNewNotificationToUser(notification.user_id, {
              id: notification.id,
              title: notification.title,
              message: notification.message,
              type: notification.type,
              priority: notification.priority,
              action_url: notification.action_url,
              action_label: notification.action_label,
              created_at: notification.created_at
            });
          }
        }
      } catch (wsError) {
        console.warn('WebSocket no disponible para alertas masivas:', wsError);
      }
      
      res.status(201).json({
        success: true,
        message: `${notifications.length} notificaciones creadas exitosamente`,
        data: notifications
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Error al crear notificaciones masivas',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }
  }

  /**
   * Crear notificación para todos los usuarios de un rol
   */
  async createRoleNotification(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const dto = plainToClass(RoleNotificationDto, req.body);
      const errors = await validate(dto);

      if (errors.length > 0) {
        res.status(400).json({
          success: false,
          message: 'Datos de entrada inválidos',
          errors: errors.map(err => ({
            property: err.property,
            constraints: err.constraints
          }))
        });
        return;
      }

      // Crear notificación en BD
      const notification = await this.notificationService.sendRoleNotification(dto, dto.roleId);
      
      // Enviar alerta en tiempo real al rol
      try {
        const wsService = getWebSocketService();
        await wsService.alertNewNotificationToRole(dto.roleId, {
          id: notification.id,
          title: notification.title,
          message: notification.message,
          type: notification.type,
          priority: notification.priority,
          action_url: notification.action_url,
          action_label: notification.action_label,
          created_at: notification.created_at
        });
      } catch (wsError) {
        console.warn('WebSocket no disponible para alerta de rol:', wsError);
      }
      
      res.status(201).json({
        success: true,
        message: 'Notificación creada para el rol',
        data: notification
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Error al crear notificación por rol',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }
  }

  /**
   * Marcar todas las notificaciones del usuario como leídas
   */
  async markAllAsRead(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }

      await this.notificationService.markAllAsReadForUser(req.user.id);
      
      res.status(200).json({
        success: true,
        message: 'Todas las notificaciones marcadas como leídas'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Error al marcar todas las notificaciones como leídas',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }
  }

  /**
   * Obtener notificaciones no leídas del usuario
   */
  async getUnreadNotifications(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          message: 'Usuario no autenticado'
        });
        return;
      }

      const notifications = await this.notificationService.getUnreadNotificationsForUser(req.user.id);
      
      res.status(200).json({
        success: true,
        data: notifications
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Error al obtener notificaciones no leídas',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }
  }

  /**
   * Limpiar notificaciones expiradas
   */
  async cleanupExpiredNotifications(_req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const deletedCount = await this.notificationService.cleanupExpiredNotifications();
      
      res.status(200).json({
        success: true,
        message: `${deletedCount} notificaciones expiradas eliminadas`,
        data: { deletedCount }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Error al limpiar notificaciones expiradas',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }
  }

  /**
   * Limpiar notificaciones antiguas leídas (más de una semana)
   */
  async cleanupOldReadNotifications(_req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const deletedCount = await this.notificationService.cleanupOldReadNotifications();
      
      res.status(200).json({
        success: true,
        message: `${deletedCount} notificaciones antiguas eliminadas`,
        data: { deletedCount }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Error al limpiar notificaciones antiguas',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }
  }

  /**
   * Marcar todas las notificaciones duplicadas como leídas (por usuario, título y productId)
   */
  async markDuplicatesAsRead(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { title, productId } = req.body;
      if (!title || !productId) {
        res.status(400).json({ success: false, message: 'Faltan parámetros title o productId' });
        return;
      }
      if (!req.user) {
        res.status(401).json({ success: false, message: 'Usuario no autenticado' });
        return;
      }
      await this.notificationService.markDuplicatesAsRead(req.user.id, title, productId);
      res.status(200).json({ success: true });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Error al marcar duplicados como leídos',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }
  }
} 