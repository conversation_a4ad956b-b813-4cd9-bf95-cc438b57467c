import { Request, Response } from 'express';
import { BadRequestException, UnauthorizedException } from '../exceptions/http.exception';
import { AuthService, authService } from '../services/auth.service';
import { auditService } from '../modules/systems/services/audit.service';

export class AuthController {
  constructor(private authService: AuthService) {}

  async login(req: Request, res: Response) {
    const { credential, password } = req.body;
    try {
      const user = await this.authService.validateUser(credential, password);

      if (!user) {
        throw new UnauthorizedException('Credenciales inválidas');
      }

      const { accessToken, refreshToken } = this.authService.generateTokens(user);
      await this.authService.updateRefreshToken(user.id, refreshToken);

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { password: _password, refresh_token: _refreshToken, ...userResponse } = user;

      // Procesar roles y extraer permisos únicos
      const processedUser = this.processUserRolesAndPermissions(userResponse);

      // Registrar auditoría de login exitoso
      await auditService.log({
        userId: user.id,
        action: 'login',
        targetEntity: 'auth',
        details: {
          email: user.email,
          loginTime: new Date().toISOString(),
          userAgent: req.headers['user-agent']
        },
        ipAddress: req.ip,
      });

      return res.json({
        success: true,
        user: processedUser,
        token: accessToken,
        refreshToken: refreshToken,
      });
    } catch (error) {
      // Registrar auditoría de login fallido
      await auditService.log({
        action: 'login_failed',
        targetEntity: 'auth',
        details: {
          credential,
          error: error instanceof Error ? error.message : 'Unknown error',
          attemptTime: new Date().toISOString(),
          userAgent: req.headers['user-agent']
        },
        ipAddress: req.ip,
      });

      if (error instanceof UnauthorizedException) {
        return res.status(401).json({ message: error.message });
      }
      console.error('Error en login:', error);
      return res.status(500).json({ message: 'Error interno en login', error: error instanceof Error ? error.message : error });
    }
  }

  // getCurrentUser ELIMINADO - no se usa en frontend
  
  async refreshToken(req: Request, res: Response) {
    try {
      const { refreshToken } = req.body as { refreshToken?: string };
    
      if (!refreshToken) {
        throw new BadRequestException('Refresh token es requerido');
      }

      const payload = this.authService.verifyToken(refreshToken);
      if (!payload) {
        throw new UnauthorizedException('Token inválido');
      }
      
      const user = await this.authService.getUserById(payload.sub);
      
      if (!user) {
        throw new UnauthorizedException('Usuario no encontrado');
      }
      
      const isValid = await this.authService.validateRefreshToken(user.id, refreshToken);
      if (!isValid) {
        throw new UnauthorizedException('Refresh token inválido');
      }
      
      const { accessToken, refreshToken: newRefreshToken } = this.authService.generateTokens(user);
      await this.authService.updateRefreshToken(user.id, newRefreshToken);
      
      return res.json({
        accessToken,
        refreshToken: newRefreshToken,
        user: {
          ...user,
          roles: user.roles ? user.roles.map((r: any) => r.name) : []
        }
      });
    } catch (error: any) {
      if (error instanceof BadRequestException || error instanceof UnauthorizedException) {
        return res.status(error.statusCode).json({ message: error.message });
      }
      if (error.name === 'TokenExpiredError') {
        return res.status(401).json({ message: 'Refresh token expirado' });
      }
      console.error('Error en refreshToken:', error);
      return res.status(500).json({ message: 'Error interno al refrescar token' });
    }
  }
  
  async logout(req: Request, res: Response) {
    try {
      const { refreshToken } = req.body as { refreshToken?: string };
    
      if (!refreshToken) {
        return res.status(400).json({ message: 'Refresh token es requerido' });
      }

      const payload = this.authService.verifyToken(refreshToken);
      if (payload) {
        await this.authService.updateRefreshToken(payload.sub, null);
      }
      
      return res.json({ message: 'Sesión cerrada correctamente' });
    } catch (error) {
      // Aunque el token sea inválido, el logout puede considerarse exitoso
      return res.json({ message: 'Sesión cerrada (token inválido ignorado)' });
    }
  }

  /**
   * Procesa los roles del usuario y extrae permisos únicos
   */
  private processUserRolesAndPermissions(user: any) {
    const roles = user.roles || [];
    const roleNames = roles.map((role: any) => role.name);
    
    // Extraer todos los permisos únicos de todos los roles
    const allPermissions = new Set<string>();
    roles.forEach((role: any) => {
      if (role.permissions && Array.isArray(role.permissions)) {
        role.permissions.forEach((permission: any) => {
          allPermissions.add(permission.name);
        });
      }
    });

    console.log('✅ Processed user permissions:', Array.from(allPermissions));
    console.log('✅ User roles:', roleNames);

    return {
      ...user,
      roles: roleNames, // Mantener compatibilidad: array de nombres de roles
      permissions: Array.from(allPermissions), // Nuevo: array de permisos únicos
      roleDetails: roles // Opcional: información completa de roles con permisos
    };
  }
}

export const authController = new AuthController(authService);
