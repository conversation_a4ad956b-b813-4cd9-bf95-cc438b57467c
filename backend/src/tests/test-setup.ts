import 'reflect-metadata';
import * as dotenv from 'dotenv';
import * as path from 'path';
import * as jwt from 'jsonwebtoken';

dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

import { testDataSource as _testDataSource } from '../config/test.config';
import runSqlFile from './run-sql-file';
import { config } from '../config/config';

// Re-export the test data source
export const testDataSource = _testDataSource;

// Función para asegurar que la conexión esté inicializada
const ensureConnection = async () => {
  if (!testDataSource.isInitialized) {
    await testDataSource.initialize();
  }
};

// Utility to clean up test data (Supabase-compatible)
async function cleanupTestData() {
  console.log('Cleaning up test data...');
  
  try {
    await ensureConnection(); // Asegurar conexión antes de limpiar
    
    // Limpiar en orden inverso de dependencias para evitar FK violations
    // 1. Primero eliminar registros que dependen de users
    await testDataSource.query('DELETE FROM requests WHERE requester_id IN (SELECT id FROM users WHERE email LIKE \'%test%\')');
    
    // Intentar limpiar password_history si existe
    try {
      await testDataSource.query('DELETE FROM password_history WHERE user_id IN (SELECT id FROM users WHERE email LIKE \'%test%\' AND email NOT LIKE \'%@comintec.com\')');
    } catch (error: any) {
      if (error.code !== '42P01') { // Table does not exist
        console.log('Error cleaning password_history:', error.message);
      }
    }
    
    await testDataSource.query('DELETE FROM user_roles WHERE user_id IN (SELECT id FROM users WHERE email LIKE \'%test%\' AND email NOT LIKE \'%@comintec.com\')');
    
    // 2. Luego eliminar usuarios de test (excepto los reales de comintec.com)
    await testDataSource.query('DELETE FROM users WHERE email LIKE \'%test%\' AND email NOT LIKE \'%@comintec.com\'');
    
    // 3. Limpiar roles y permisos de test
    await testDataSource.query('DELETE FROM role_permissions WHERE role_id IN (SELECT id FROM roles WHERE name LIKE \'%test%\')');
    await testDataSource.query('DELETE FROM roles WHERE name LIKE \'%test%\'');
    await testDataSource.query('DELETE FROM permissions WHERE name LIKE \'%test%\'');
    
    // 4. Limpiar departamentos de test
    await testDataSource.query('DELETE FROM departments WHERE name LIKE \'%test%\'');
    
    // 5. Limpiar productos de test
    try {
      await testDataSource.query('DELETE FROM products WHERE codigo_item LIKE \'%TEST%\'');
    } catch (error: any) {
      if (error.code !== '42703' && error.code !== '42P01') { // Column/table does not exist
        console.log('Error cleaning products:', error.message);
      }
    }
    
    console.log('Test data cleaned successfully');
  } catch (error) {
    console.log('Error cleaning test data:', error);
  }
}

// Export functions needed by other tests
export const initializeTestEnvironment = async () => {
  // Inicializar la conexión primero
  await ensureConnection();

  // Limpiar datos existentes
  await cleanupTestData();

  // Run schema and seed SQL files only if they haven't been run
  try {
    const rootDir = path.resolve(__dirname, '../../../');
    await runSqlFile(path.join(rootDir, 'comintec_schema_core.sql'));
    await runSqlFile(path.join(rootDir, 'comintec_schema_data_indexes.sql'));
  } catch (error: any) {
    console.log('Schema files already applied or error occurred:', error.message);
  }

  // No es necesario inicializar de nuevo, ensureConnection ya lo hizo
};

export const cleanupTestEnvironment = async () => {
  // La limpieza global se hará en afterAll, aquí solo limpiamos datos
  await cleanupTestData();
};

export const closeTestConnection = async () => {
  // Cerrar conexión de base de datos para evitar handles abiertos
  if (testDataSource.isInitialized) {
    await testDataSource.destroy();
  }
};

export const createTestConnection = async () => {
  await ensureConnection();
  return testDataSource;
};

// Global test setup
beforeAll(async () => {
  await initializeTestEnvironment();
});

// Global test teardown
afterAll(async () => {
  // Cerrar conexión de base de datos para evitar handles abiertos
  if (testDataSource.isInitialized) {
    await testDataSource.destroy();
  }
});

// Export test utilities
export const getTestApp = async () => {
  // Import app here to avoid circular dependencies
  const { App } = require('../app');
  const app = new App().app;
  
  // Start the server on a random port
  return new Promise((resolve) => {
    const server = app.listen(0, () => {
      resolve({
        app,
        server,
        port: server.address().port
      });
    });
  });
};

// Helper to close the test app
export const closeTestApp = async (app: any) => {
  if (app && app.server) {
    await new Promise((resolve) => app.server.close(resolve));
  }
};

// Global test types
declare global {
  namespace NodeJS {
    interface Global {
      testApp: any;
    }
  }
}

// Helper para generar un token de prueba
export const generateTestToken = (userId: number, roles: string[] = ['ROLE_ADMIN'], departmentId?: number, name: string = 'testuser') => {
  return jwt.sign({ sub: userId, type: 'access', roles, departmentId, name }, config.jwt.secret, { expiresIn: '24h' }); // Aumentar expiración para pruebas
};

// Helper para crear datos de prueba únicos
export const createTestData = async () => {
  // Usar datos reales de data_indexes.sql en lugar de crear datos falsos
  // Buscar usuarios existentes
  const adminUser = await testDataSource.query(`
    SELECT id, email, name FROM users 
    WHERE email = '<EMAIL>' 
    LIMIT 1
  `);
  
  const regularUser = await testDataSource.query(`
    SELECT id, email, name FROM users 
    WHERE email = '<EMAIL>' 
    LIMIT 1
  `);
  
  // Buscar roles existentes
  const adminRole = await testDataSource.query(`
    SELECT id FROM roles 
    WHERE name = 'ROLE_ADMIN' 
    LIMIT 1
  `);
  
  const userRole = await testDataSource.query(`
    SELECT id FROM roles 
    WHERE name = 'ROLE_USER' 
    LIMIT 1
  `);
  
  // Buscar departamento existente
  const department = await testDataSource.query(`
    SELECT id FROM departments 
    WHERE name = 'Sistemas' 
    LIMIT 1
  `);
  
  return {
    departmentId: department[0]?.id || 1,
    adminRoleId: adminRole[0]?.id,
    userRoleId: userRole[0]?.id,
    testUserId: regularUser[0]?.id,
    adminUserId: adminUser[0]?.id,
    testUserEmail: regularUser[0]?.email || '<EMAIL>',
    adminUserEmail: adminUser[0]?.email || '<EMAIL>',
    // Password real para todos los usuarios: 'password'
    testPassword: 'password'
  };
};
