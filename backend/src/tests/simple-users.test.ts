import { jest, describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import request from 'supertest';
import { App } from '../app';
import { User } from '../entities/user.entity';
import { Role } from '../entities/role.entity';
import { Permission } from '../entities/permission.entity';
import { testDataSource } from './test-setup';

// Crear servicios mock simples para los tests
const mockServices = {
  notificationService: {
    // Mock básico del servicio de notificaciones
    sendNotification: jest.fn(),
    getNotifications: jest.fn(),
  } as any
};

let app: any;

describe('Simple Users API Test', () => {
  let adminToken: string;
  let testUser: User;

  beforeAll(async () => {
    // Crear la app después de inicializar el dataSource
    app = new App(testDataSource, mockServices).app;

    // Inicializar la conexión de prueba
    if (!testDataSource.isInitialized) {
      await testDataSource.initialize();
    }

    // Crear la app después de inicializar el dataSource
    app = new App(testDataSource, mockServices).app;

    // Usar usuario admin real que ya existe
    const loginRes = await request(app)
      .post('/api/auth/login')
      .send({
        credential: '<EMAIL>',
        password: 'password', // Password real del usuario
      });

    if (loginRes.status !== 200) {
      throw new Error(`Login failed: ${loginRes.status} - ${JSON.stringify(loginRes.body)}`);
    }

    adminToken = loginRes.body.token;

    // Buscar un usuario existente para pruebas
    const foundUser = await testDataSource.getRepository(User).findOne({
      where: { email: '<EMAIL>' }
    });

    if (!foundUser) {
      throw new Error('No se encontró usuario <NAME_EMAIL>');
    }

    testUser = foundUser;
  });

  afterAll(async () => {
    // No necesitamos limpiar datos reales
  });

  describe('GET /api/systems/users', () => {
    it('should get paginated users', async () => {
      const res = await request(app)
        .get('/api/systems/users?page=1&limit=10')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(res.status).toBe(200);
      expect(res.body).toHaveProperty('data');
      expect(res.body).toHaveProperty('total');
      expect(res.body.data.length).toBeGreaterThan(0);
    });
  });

  describe('GET /api/systems/users/:id', () => {
    it('should get user by id', async () => {
      const res = await request(app)
        .get(`/api/systems/users/${testUser.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(res.status).toBe(200);
      expect(res.body.data.id).toBe(testUser.id);
      expect(res.body.data.email).toBe('<EMAIL>');
    });
  });
}); 