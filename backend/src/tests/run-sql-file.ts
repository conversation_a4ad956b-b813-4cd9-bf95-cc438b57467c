// Utility to run a SQL file against the test database using pg
import { Client } from 'pg';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';

dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

const runSqlFile = async (filePath: string) => {
  const sql = fs.readFileSync(filePath, 'utf8');
  const client = new Client({
    host: process.env.DB_HOST,
    port: Number(process.env.DB_PORT),
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
  });
  await client.connect();
  try {
    await client.query(sql);
  } finally {
    await client.end();
  }
};

export default runSqlFile;
