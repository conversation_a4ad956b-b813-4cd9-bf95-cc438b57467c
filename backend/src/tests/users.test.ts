import request from 'supertest';
import { App } from '../app';

import { User } from '../entities/user.entity';
import { Role } from '../entities/role.entity';
import { generateTestToken, testDataSource } from './test-setup';

const app = new App().app;

describe('Users API', () => {
  let adminToken: string;
  let testUser: User;
  let testRole: Role;

  beforeAll(async () => {
    // Inicializar la conexión de prueba
    if (!testDataSource.isInitialized) {
      await testDataSource.initialize();
    }

    // Crear rol de prueba
    testRole = await testDataSource.getRepository(Role).save({
      name: 'ROLE_TEST',
      description: 'Test Role',
    });

    // Crear usuario admin de prueba
    const adminUser = await testDataSource.getRepository(User).save({
      name: 'Test Admin',
      email: '<EMAIL>',
      password: 'TestPass123!',
      status: 'activo',
      roles: [testRole],
    });

    // Crear usuario de prueba
    testUser = await testDataSource.getRepository(User).save({
      name: 'Test User',
      email: '<EMAIL>',
      password: 'TestPass123!',
      status: 'activo',
    });

    // Generar token de admin con el ID real del usuario
    adminToken = generateTestToken(adminUser.id, ['ROLE_ADMIN']);
  });

  afterAll(async () => {
    // Limpiar datos de prueba
    if (testDataSource.isInitialized) {
      await testDataSource.getRepository(User).delete({ email: '<EMAIL>' });
      await testDataSource.getRepository(User).delete({ email: '<EMAIL>' });
      await testDataSource.getRepository(User).delete({ email: '<EMAIL>' });
      await testDataSource.getRepository(User).delete({ email: '<EMAIL>' });
      await testDataSource.getRepository(User).delete({ email: '<EMAIL>' });
      await testDataSource.getRepository(Role).delete({ name: 'ROLE_TEST' });
      await testDataSource.destroy();
    }
  });

  describe('GET /api/systems/users', () => {
    it('should get paginated users', async () => {
      const res = await request(app)
        .get('/api/systems/users?page=1&limit=10')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(res.status).toBe(200);
      expect(res.body).toHaveProperty('data');
      expect(res.body).toHaveProperty('total');
      expect(res.body.data.length).toBeGreaterThan(0);
    });

    it('should search users by name or email', async () => {
      const res = await request(app)
        .get('/api/systems/users?search=Test%20User')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(res.status).toBe(200);
      expect(res.body.data.some((u: any) => u.name === 'Test User')).toBe(true);
    });
  });

  describe('GET /api/systems/users/:id', () => {
    it('should get user by id', async () => {
      const res = await request(app)
        .get(`/api/systems/users/${testUser.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(res.status).toBe(200);
      expect(res.body.id).toBe(testUser.id);
      expect(res.body.email).toBe('<EMAIL>');
    });

    it('should return 404 for non-existent user', async () => {
      const res = await request(app)
        .get('/api/systems/users/999999')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(res.status).toBe(404);
    });
  });

  describe('POST /api/systems/users', () => {
    it('should create a new user', async () => {
      const newUser = {
        name: 'New Test User',
        email: '<EMAIL>',
        password: 'NewPass123!',
        status: 'activo',
        roleIds: [testRole.id],
      };

      const res = await request(app)
        .post('/api/systems/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(newUser);

      expect(res.status).toBe(201);
      expect(res.body).toHaveProperty('id');
      expect(res.body.email).toBe('<EMAIL>');
      expect(res.body).not.toHaveProperty('password');
    });

    it('should validate required fields', async () => {
      const res = await request(app)
        .post('/api/systems/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({});

      expect(res.status).toBe(400);
      expect(res.body).toHaveProperty('errors');
    });
  });

  describe('PUT /api/systems/users/:id', () => {
    it('should update user', async () => {
      const updateData = {
        name: 'Updated Test User',
        email: '<EMAIL>',
        status: 'inactivo',
        roleIds: [testRole.id],
      };

      const res = await request(app)
        .put(`/api/systems/users/${testUser.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData);

      expect(res.status).toBe(200);
      expect(res.body.name).toBe('Updated Test User');
      expect(res.body.email).toBe('<EMAIL>');
      expect(res.body.status).toBe('inactivo');
    });

    it('should prevent updating to existing email', async () => {
      const updateData = {
        email: '<EMAIL>', // Already exists
      };

      const res = await request(app)
        .put(`/api/systems/users/${testUser.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData);

      expect(res.status).toBe(400);
    });
  });

  describe('DELETE /api/systems/users/:id', () => {
    it('should delete user', async () => {
      const userToDelete = await testDataSource.getRepository(User).save({
        name: 'User to Delete',
        email: '<EMAIL>',
        password: 'DeleteMe123!',
        status: 'activo',
      });

      const res = await request(app)
        .delete(`/api/systems/users/${userToDelete.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(res.status).toBe(204);

      // Verify deletion
      const deletedUser = await testDataSource.getRepository(User).findOne({
        where: { id: userToDelete.id },
      });
      expect(deletedUser).toBeNull();
    });

    it('should prevent deleting self', async () => {
      const adminUser = await testDataSource.getRepository(User).findOne({
        where: { email: '<EMAIL>' },
      });

      const res = await request(app)
        .delete(`/api/systems/users/${adminUser?.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(res.status).toBe(400);
      expect(res.body.message).toContain('No puedes eliminarte a ti mismo');
    });
  });
});
