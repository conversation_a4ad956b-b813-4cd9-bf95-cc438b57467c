import { jest, describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import request from 'supertest';
import { DataSource } from 'typeorm';
import { createApp } from '../app';
import { testDataSource, createTestData, generateTestToken } from './test-setup';
import { User } from '../entities/user.entity';
import { Role } from '../entities/role.entity';
import { App } from '../app';

const app = createApp();

describe('Role-Based Access Control (RBAC)', () => {
  let dataSource: DataSource;
  let adminToken: string;
  let userToken: string;
  let adminRole: Role;
  let userRole: Role;
  let testUser: User;
  let testAdmin: User;
  let testData: any;

  beforeAll(async () => {
    dataSource = testDataSource;
    if (!dataSource.isInitialized) {
      await dataSource.initialize();
    }
    
    // Limpieza previa - eliminar solo datos de test, no datos reales
    const userRepo = dataSource.getRepository(User);
    const roleRepo = dataSource.getRepository(Role);
    
    // Eliminar requests que dependen de usuarios de test
    await dataSource.query('DELETE FROM requests WHERE requester_id IN (SELECT id FROM users WHERE email IN (\'<EMAIL>\', \'<EMAIL>\', \'<EMAIL>\', \'<EMAIL>\', \'<EMAIL>\', \'<EMAIL>\'))');
    
    // Eliminar user_roles para usuarios de test
    await dataSource.query('DELETE FROM user_roles WHERE user_id IN (SELECT id FROM users WHERE email IN (\'<EMAIL>\', \'<EMAIL>\', \'<EMAIL>\', \'<EMAIL>\', \'<EMAIL>\', \'<EMAIL>\'))');
    
    // Ahora eliminar usuarios de test
    await userRepo.delete({ email: '<EMAIL>' });
    await userRepo.delete({ email: '<EMAIL>' });
    await userRepo.delete({ email: '<EMAIL>' });
    await userRepo.delete({ email: '<EMAIL>' });
    await userRepo.delete({ email: '<EMAIL>' });
    await userRepo.delete({ email: '<EMAIL>' });
    
    // Solo eliminar roles si no existen en datos reales
    await roleRepo.delete({ name: 'ROLE_ADMIN' });
    await roleRepo.delete({ name: 'ROLE_USER' });

    // Create roles
    let adminRoleFound = await roleRepo.findOne({ where: { name: 'ROLE_ADMIN' } });
    if (!adminRoleFound) {
      adminRoleFound = roleRepo.create({ name: 'ROLE_ADMIN' });
      await roleRepo.save(adminRoleFound);
    }
    adminRole = adminRoleFound!;

    let userRoleFound = await roleRepo.findOne({ where: { name: 'ROLE_USER' } });
    if (!userRoleFound) {
      userRoleFound = roleRepo.create({ name: 'ROLE_USER' });
      await roleRepo.save(userRoleFound);
    }
    userRole = userRoleFound!;

    // Create admin user
    let testAdminFound = await userRepo.findOne({ where: { email: '<EMAIL>' }, relations: ['roles'] });
    if (!testAdminFound) {
      testAdminFound = userRepo.create({
        name: 'Admin Sistemas',
        email: '<EMAIL>',
        password: 'Admin123!',
        status: 'activo',
        roles: [adminRole],
      });
      await userRepo.save(testAdminFound);
    }
    testAdmin = testAdminFound!;

    // Create regular test user
    let testUserFound = await userRepo.findOne({ where: { email: '<EMAIL>' }, relations: ['roles'] });
    if (!testUserFound) {
      testUserFound = userRepo.create({
        name: 'Regular User',
        email: '<EMAIL>',
        password: 'UserPass123!',
        status: 'activo',
        roles: [userRole],
      });
      await userRepo.save(testUserFound);
    }
    testUser = testUserFound!;

    // Get tokens using real credentials
    const adminLogin = await request(app)
      .post('/api/auth/login')
      .send({ credential: '<EMAIL>', password: 'Admin123!' });
    adminToken = adminLogin.body.token;

    const userLogin = await request(app)
      .post('/api/auth/login')
      .send({ credential: '<EMAIL>', password: 'UserPass123!' });
    userToken = userLogin.body.token;

    testData = await createTestData();
  });

  afterAll(async () => {
    if (dataSource.isInitialized) {
      const userRepo = dataSource.getRepository(User);
      const roleRepo = dataSource.getRepository(Role);
      
      // Eliminar requests que dependen de usuarios de test
      await dataSource.query('DELETE FROM requests WHERE requester_id IN (SELECT id FROM users WHERE email IN (\'<EMAIL>\', \'<EMAIL>\', \'<EMAIL>\', \'<EMAIL>\', \'<EMAIL>\', \'<EMAIL>\'))');
      
      // Eliminar user_roles para usuarios de test
      await dataSource.query('DELETE FROM user_roles WHERE user_id IN (SELECT id FROM users WHERE email IN (\'<EMAIL>\', \'<EMAIL>\', \'<EMAIL>\', \'<EMAIL>\', \'<EMAIL>\', \'<EMAIL>\'))');
      
      // Ahora eliminar usuarios de test
      await userRepo.delete({ email: '<EMAIL>' });
      await userRepo.delete({ email: '<EMAIL>' });
      await userRepo.delete({ email: '<EMAIL>' });
      await userRepo.delete({ email: '<EMAIL>' });
      await userRepo.delete({ email: '<EMAIL>' });
      await userRepo.delete({ email: '<EMAIL>' });
      await roleRepo.delete({ name: 'ROLE_ADMIN' });
      await roleRepo.delete({ name: 'ROLE_USER' });
      
      // NO destruir la conexión aquí - se hará globalmente
    }
  });

  describe('User Management Access', () => {
    it('should allow admin to list all users', async () => {
      const res = await request(app)
        .get('/api/systems/users')
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(res.status).toBe(200);
      expect(Array.isArray(res.body.data)).toBe(true);
    });

    it('should prevent regular user from listing all users', async () => {
      const res = await request(app)
        .get('/api/systems/users')
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(res.status).toBe(403);
    });
  });

  describe('User Creation', () => {
    it('should allow admin to create new users', async () => {
      const newUser = {
        name: 'New Test User',
        email: '<EMAIL>',
        password: 'NewPass123!',
        status: 'activo',
        roleIds: [userRole.id],
      };

      const res = await request(app)
        .post('/api/systems/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(newUser);
      
      expect(res.status).toBe(201);
      expect(res.body.email).toBe('<EMAIL>');
    });

    it('should prevent regular user from creating users', async () => {
      const newUser = {
        name: 'Unauthorized User',
        email: '<EMAIL>',
        password: 'Pass123!',
        status: 'activo',
      };

      const res = await request(app)
        .post('/api/systems/users')
        .set('Authorization', `Bearer ${userToken}`)
        .send(newUser);
      
      expect(res.status).toBe(403);
    });
  });

  describe('User Update', () => {
    it('should allow admin to update any user', async () => {
      const updateData = { name: 'Updated by Admin' };
      
      const res = await request(app)
        .put(`/api/systems/users/${testUser.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData);
      
      expect(res.status).toBe(200);
      expect(res.body.name).toBe('Updated by Admin');
    });

    it('should allow users to update their own profile', async () => {
      const updateData = { name: 'My Updated Name' };
      
      const res = await request(app)
        .put(`/api/systems/users/${testUser.id}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateData);
      
      expect(res.status).toBe(200);
      expect(res.body.name).toBe('My Updated Name');
    });

    it('should prevent users from updating other users', async () => {
      const updateData = { name: 'Should Not Work' };
      
      const res = await request(app)
        .put(`/api/systems/users/${testAdmin.id}`) // Trying to update admin user
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateData);
      
      expect(res.status).toBe(403);
    });
  });

  describe('User Deletion', () => {
    it('should allow admin to delete users', async () => {
      const userToDelete = await dataSource.getRepository(User).save({
        name: 'To Be Deleted',
        email: '<EMAIL>',
        password: 'DeleteMe123!',
        status: 'activo',
      });

      const res = await request(app)
        .delete(`/api/systems/users/${userToDelete.id}`)
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(res.status).toBe(204);
    });

    it('should prevent users from deleting themselves', async () => {
      const res = await request(app)
        .delete(`/api/systems/users/${testUser.id}`)
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(res.status).toBe(403);
    });

    it('should prevent users from deleting others', async () => {
      const anotherUser = await dataSource.getRepository(User).save({
        name: 'Another User',
        email: '<EMAIL>',
        password: 'Another123!',
        status: 'activo',
      });

      const res = await request(app)
        .delete(`/api/systems/users/${anotherUser.id}`)
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(res.status).toBe(403);
    });
  });

  describe('Role Management', () => {
    it('should allow admin to manage roles', async () => {
      const res = await request(app)
        .get('/api/systems/roles')
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(res.status).toBe(200);
      expect(Array.isArray(res.body)).toBe(true);
    });

    it('should prevent regular users from managing roles', async () => {
      const res = await request(app)
        .get('/api/systems/roles')
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(res.status).toBe(403);
    });
  });

  describe('Super Admin Access', () => {
    it('should allow user with ROLE_FULLADMIN to access any protected route', async () => {
      // Usar el admin user que ya tiene ROLE_FULLADMIN
      const token = generateTestToken(testData.adminUserId, ['ROLE_FULLADMIN']);
      
      const res = await request(global.testApp)
        .get('/api/systems/users')
        .set('Authorization', `Bearer ${token}`);
        
      expect(res.status).toBe(200);
    });
  });
});
