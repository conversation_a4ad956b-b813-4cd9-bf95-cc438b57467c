import request from 'supertest';
import { DataSource } from 'typeorm';
import { createApp } from '../app';
import { testDataSource } from './test-setup';
import { User } from '../entities/user.entity';
import { Role } from '../entities/role.entity';
import { Permission } from '../entities/permission.entity';
import { PermissionService } from '../services/permission.service';

const app = createApp();

describe('Permission-Based Access Control (New System)', () => {
  let dataSource: DataSource;
  let adminToken: string;
  let userWithPermissionsToken: string;
  let userWithoutPermissionsToken: string;
  
  let adminRole: Role;
  let managerRole: Role;
  let basicUserRole: Role;
  
  let userManagementPermission: Permission;
  let roleManagementPermission: Permission;
  
  let testAdmin: User;
  let testManagerUser: User;
  let testBasicUser: User;

  beforeAll(async () => {
    dataSource = testDataSource;
    if (!dataSource.isInitialized) {
      await dataSource.initialize();
    }

    const userRepo = dataSource.getRepository(User);
    const roleRepo = dataSource.getRepository(Role);
    const permissionRepo = dataSource.getRepository(Permission);

    // Limpiar datos previos
    await userRepo.delete({ email: '<EMAIL>' });
    await userRepo.delete({ email: '<EMAIL>' });
    await userRepo.delete({ email: '<EMAIL>' });
    
    // Crear permisos
    userManagementPermission = await permissionRepo.save({
      name: 'users:update',
      module: 'systems',
      action: 'update',
      description: 'Actualizar usuarios'
    });

    roleManagementPermission = await permissionRepo.save({
      name: 'roles:update',
      module: 'systems',
      action: 'update',
      description: 'Actualizar roles'
    });

    // Crear roles
    adminRole = await roleRepo.save({
      name: 'ROLE_ADMIN',
      permissions: [userManagementPermission, roleManagementPermission],
    });

    managerRole = await roleRepo.save({
      name: 'ROLE_MANAGER',
      permissions: [userManagementPermission], // Solo gestión de usuarios
    });

    basicUserRole = await roleRepo.save({
      name: 'ROLE_USER',
      permissions: [], // Sin permisos especiales
    });

    // Crear usuarios de prueba
    testAdmin = await userRepo.save({
      name: 'Test Admin',
      email: '<EMAIL>',
      password: 'Admin123!',
      status: 'activo',
      roles: [adminRole],
    });

    testManagerUser = await userRepo.save({
      name: 'Test Manager',
      email: '<EMAIL>',
      password: 'Manager123!',
      status: 'activo',
      roles: [managerRole],
    });

    testBasicUser = await userRepo.save({
      name: 'Test Basic User',
      email: '<EMAIL>',
      password: 'Basic123!',
      status: 'activo',
      roles: [basicUserRole],
    });

    // Obtener tokens
    const adminLogin = await request(app)
      .post('/api/auth/login')
      .send({ credential: '<EMAIL>', password: 'Admin123!' });
    adminToken = adminLogin.body.token;

    const managerLogin = await request(app)
      .post('/api/auth/login')
      .send({ credential: '<EMAIL>', password: 'Manager123!' });
    userWithPermissionsToken = managerLogin.body.token;

    const basicLogin = await request(app)
      .post('/api/auth/login')
      .send({ credential: '<EMAIL>', password: 'Basic123!' });
    userWithoutPermissionsToken = basicLogin.body.token;
  });

  afterAll(async () => {
    if (dataSource.isInitialized) {
      const userRepo = dataSource.getRepository(User);
      const roleRepo = dataSource.getRepository(Role);
      const permissionRepo = dataSource.getRepository(Permission);
      
      await userRepo.delete({ email: '<EMAIL>' });
      await userRepo.delete({ email: '<EMAIL>' });
      await userRepo.delete({ email: '<EMAIL>' });
      
      await roleRepo.delete({ name: 'ROLE_ADMIN' });
      await roleRepo.delete({ name: 'ROLE_MANAGER' });
      await roleRepo.delete({ name: 'ROLE_USER' });
      
      await permissionRepo.delete({ name: 'users:update' });
      await permissionRepo.delete({ name: 'roles:update' });
      
      await dataSource.destroy();
    }
  });

  describe('PermissionService Unit Tests', () => {
    it('should correctly identify admin users', () => {
      expect(PermissionService.isAdmin(testAdmin)).toBe(true);
      expect(PermissionService.isAdmin(testManagerUser)).toBe(false);
      expect(PermissionService.isAdmin(testBasicUser)).toBe(false);
    });

    it('should correctly check specific permissions', () => {
      // Admin tiene todos los permisos
      expect(PermissionService.hasPermission(testAdmin, 'users:update')).toBe(true);
      expect(PermissionService.hasPermission(testAdmin, 'roles:update')).toBe(true);
      
      // Manager tiene solo users:update
      expect(PermissionService.hasPermission(testManagerUser, 'users:update')).toBe(true);
      expect(PermissionService.hasPermission(testManagerUser, 'roles:update')).toBe(false);
      
      // Basic user no tiene permisos especiales
      expect(PermissionService.hasPermission(testBasicUser, 'users:update')).toBe(false);
      expect(PermissionService.hasPermission(testBasicUser, 'roles:update')).toBe(false);
    });

    it('should correctly get user permissions', () => {
      const adminPermissions = PermissionService.getUserPermissions(testAdmin);
      const managerPermissions = PermissionService.getUserPermissions(testManagerUser);
      const basicPermissions = PermissionService.getUserPermissions(testBasicUser);

      expect(adminPermissions).toContain('users:update');
      expect(adminPermissions).toContain('roles:update');
      
      expect(managerPermissions).toContain('users:update');
      expect(managerPermissions).not.toContain('roles:update');
      
      expect(basicPermissions).toHaveLength(0);
    });
  });

  describe('User Management Access with Permissions', () => {
    it('should allow admin to access user management', async () => {
      const res = await request(app)
        .get('/api/systems/users')
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(res.status).toBe(200);
    });

    it('should allow user with users:update permission to access', async () => {
      const res = await request(app)
        .get('/api/systems/users')
        .set('Authorization', `Bearer ${userWithPermissionsToken}`);
      
      expect(res.status).toBe(200);
    });

    it('should deny access to user without users:update permission', async () => {
      const res = await request(app)
        .get('/api/systems/users')
        .set('Authorization', `Bearer ${userWithoutPermissionsToken}`);
      
      expect(res.status).toBe(403);
    });
  });

  describe('Role Management Access with Permissions', () => {
    it('should allow admin to access role management', async () => {
      const res = await request(app)
        .get('/api/systems/roles')
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(res.status).toBe(200);
    });

    it('should deny access to user without roles:update permission', async () => {
      const res = await request(app)
        .get('/api/systems/roles')
        .set('Authorization', `Bearer ${userWithPermissionsToken}`);
      
      expect(res.status).toBe(403);
    });

    it('should deny access to basic user', async () => {
      const res = await request(app)
        .get('/api/systems/roles')
        .set('Authorization', `Bearer ${userWithoutPermissionsToken}`);
      
      expect(res.status).toBe(403);
    });
  });

  describe('User Operations with Permissions', () => {
    it('should allow user with users:update to create users', async () => {
      const newUser = {
        name: 'Created by Manager',
        email: '<EMAIL>',
        password: 'NewPass123!',
        status: 'activo',
        roleIds: [basicUserRole.id],
      };

      const res = await request(app)
        .post('/api/systems/users')
        .set('Authorization', `Bearer ${userWithPermissionsToken}`)
        .send(newUser);
      
      expect(res.status).toBe(201);
      expect(res.body.email).toBe('<EMAIL>');
    });

    it('should allow user with users:update to update users', async () => {
      const updateData = { name: 'Updated by Manager' };
      
      const res = await request(app)
        .put(`/api/systems/users/${testBasicUser.id}`)
        .set('Authorization', `Bearer ${userWithPermissionsToken}`)
        .send(updateData);
      
      expect(res.status).toBe(200);
    });

    it('should allow user with users:update to delete users', async () => {
      // Crear usuario para eliminar
      const userToDelete = await dataSource.getRepository(User).save({
        name: 'To Be Deleted by Manager',
        email: '<EMAIL>',
        password: 'Delete123!',
        status: 'activo',
      });

      const res = await request(app)
        .delete(`/api/systems/users/${userToDelete.id}`)
        .set('Authorization', `Bearer ${userWithPermissionsToken}`);
      
      expect(res.status).toBe(204);
    });
  });

  describe('Backward Compatibility with ROLE_ADMIN', () => {
    it('should maintain ROLE_ADMIN as super user', async () => {
      // ROLE_ADMIN debe tener acceso a todo, incluso sin permisos específicos asignados
      expect(PermissionService.hasPermission(testAdmin, 'ANY_PERMISSION')).toBe(true);
      expect(PermissionService.hasPermission(testAdmin, 'NON_EXISTENT_PERMISSION')).toBe(true);
    });
  });
}); 