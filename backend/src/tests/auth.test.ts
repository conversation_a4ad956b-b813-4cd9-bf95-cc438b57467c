import request from 'supertest';
import { testDataSource, createTestData, generateTestToken, initializeTestEnvironment, cleanupTestEnvironment } from './test-setup';
import { AuthService } from '../services/auth.service';
import { AuthController } from '../controllers/auth.controller';
import express, { Request, Response } from 'express';

// Import app after test setup
let app: any;
let testAuthService: AuthService;
let testAuthController: AuthController;
let testData: any;

describe('Auth API', () => {
  let adminToken: string;

  beforeAll(async () => {
    // Initialize test environment
    await initializeTestEnvironment();
    
    // Create test services with testDataSource
    testAuthService = new AuthService(testDataSource);
    testAuthController = new AuthController(testAuthService);
    
    // Create unique test data
    testData = await createTestData();
    
    // Generate admin token
    adminToken = generateTestToken(testData.adminUserId || 1, ['ROLE_ADMIN']);

    // Crear app de test y montar rutas de auth manualmente
    app = express();
    app.use(express.json());
    app.post('/api/auth/login', (req: Request, res: Response) => testAuthController.login(req, res));
    app.get('/api/auth/me', (req: Request, res: Response) => testAuthController.getCurrentUser(req, res));
    app.post('/api/auth/refresh-token', (req: Request, res: Response) => testAuthController.refreshToken(req, res));
    app.post('/api/auth/logout', (req: Request, res: Response) => testAuthController.logout(req, res));
  });

  afterAll(async () => {
    await cleanupTestEnvironment();
  });

  describe('AuthService (Direct)', () => {
    it('should validate user with correct credentials', async () => {
      const user = await testAuthService.validateUser(
        testData.adminUserEmail || '<EMAIL>',
        testData.testPassword || 'admin'
      );

      expect(user).toBeTruthy();
      expect(user?.email).toBe(testData.adminUserEmail || '<EMAIL>');
    });

    it('should reject user with incorrect password', async () => {
      const user = await testAuthService.validateUser(
        testData.adminUserEmail || '<EMAIL>',
        'wrongpassword'
      );

      expect(user).toBeNull();
    });

    it('should reject non-existent user', async () => {
      const user = await testAuthService.validateUser(
        '<EMAIL>',
        'password'
      );

      expect(user).toBeNull();
    });
  });

  describe('POST /api/auth/login', () => {
    it('should login with valid credentials', async () => {
      const res = await request(app)
        .post('/api/auth/login')
        .send({
          credential: testData.adminUserEmail || '<EMAIL>',
          password: testData.testPassword || 'admin'
        });

      expect(res.status).toBe(200);
      expect(res.body).toHaveProperty('token');
      expect(res.body).toHaveProperty('user');
      expect(res.body.user.email).toBe(testData.adminUserEmail || '<EMAIL>');
    });

    it('should not login with invalid password', async () => {
      const res = await request(app)
        .post('/api/auth/login')
        .send({
          credential: testData.adminUserEmail || '<EMAIL>',
          password: 'wrongpassword'
        });

      expect(res.status).toBe(401);
      expect(res.body.message).toBe('Credenciales inválidas');
    });
  });

  describe('GET /api/auth/me', () => {
    it('should return current user data', async () => {
      // Simular usuario autenticado en req.user
      app.use((req: Request, _res: Response, next: () => void) => {
        req.user = { sub: testData.adminUserId };
        next();
      });
      const res = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(res.status).toBe(200);
      expect(res.body.email).toBe(testData.adminUserEmail || '<EMAIL>');
    });

    it('should return 401 without token', async () => {
      const res = await request(app).get('/api/auth/me');
      expect(res.status).toBe(401);
    });
  });

  describe('POST /api/auth/refresh-token', () => {
    it('should refresh access token', async () => {
      // First login to get refresh token
      const loginRes = await request(app)
        .post('/api/auth/login')
        .send({
          credential: testData.adminUserEmail || '<EMAIL>',
          password: testData.testPassword || 'admin'
        });

      const refreshToken = loginRes.body.refreshToken;

      const res = await request(app)
        .post('/api/auth/refresh-token')
        .send({ refreshToken });

      expect(res.status).toBe(200);
      expect(res.body).toHaveProperty('accessToken');
      expect(res.body).toHaveProperty('refreshToken');
    }, 30000);

    it('should return 400 without refresh token', async () => {
      const res = await request(app)
        .post('/api/auth/refresh-token')
        .send({});

      expect(res.status).toBe(400);
      expect(res.body.message).toBe('Refresh token es requerido');
    });
  });

  describe('POST /api/auth/logout', () => {
    it('should logout user', async () => {
      // First login to get refresh token
      const loginRes = await request(app)
        .post('/api/auth/login')
        .send({
          credential: testData.adminUserEmail || '<EMAIL>',
          password: testData.testPassword || 'admin'
        });

      const refreshToken = loginRes.body.refreshToken;

      const res = await request(app)
        .post('/api/auth/logout')
        .send({ refreshToken });

      expect(res.status).toBe(200);
      expect(res.body.message).toBe('Sesión cerrada correctamente');
    }, 30000);

    it('should return 400 without refresh token', async () => {
      const res = await request(app)
        .post('/api/auth/logout')
        .send({});

      expect(res.status).toBe(400);
      expect(res.body.message).toBe('Refresh token es requerido');
    });
  });
});
