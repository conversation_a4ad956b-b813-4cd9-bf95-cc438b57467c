import { Pool, QueryResult, QueryResultRow } from 'pg';
import { DataSource } from 'typeorm';
import { config } from '../config';

let pool: Pool | undefined;

export const getDBPool = (): Pool => {
  if (!pool) {
    pool = new Pool({
      user: config.database.username,
      host: config.database.host,
      database: config.database.database,
      password: config.database.password,
      port: config.database.port,
      ssl: config.isProduction ? { rejectUnauthorized: false } : false,
    });

    pool.on('connect', () => {
      if (config.database.logging) {
        console.log('Cliente conectado al pool de PostgreSQL');
      }
    });

    pool.on('acquire', () => {
      if (config.database.logging) {
        // console.log('Cliente adquirido del pool de PostgreSQL');
      }
    });

    pool.on('remove', () => {
      if (config.database.logging) {
        // console.log('Cliente removido del pool de PostgreSQL');
      }
    });

    pool.on('error', (err: Error) => {
      console.error('Error inesperado en el cliente del pool de DB', err);
      // Considerar una estrategia de reconexión o terminación más robusta si es necesario
    });

    if (config.database.logging) {
      console.log('Pool de conexiones a PostgreSQL inicializado.');
    }
  }
  return pool;
};

export const query = async <T extends QueryResultRow = any>(text: string, params?: any[]): Promise<QueryResult<T>> => {
  const start = Date.now();
  const poolToUse = getDBPool(); // Asegura que el pool esté inicializado
  try {
    const res = await poolToUse.query<T>(text, params);
    const duration = Date.now() - start;
    if (config.database.logging) {
      console.log('Executed query:', { text, params: params || [], duration_ms: duration, rowCount: res.rowCount });
    }
    return res;
  } catch (error) {
    console.error('Error ejecutando query:', { text, params: params || [], error });
    throw error; // Re-lanzar el error para que sea manejado por el llamador
  }
};

/**
 * Función helper para ejecutar queries usando el data source correcto
 * Si se proporciona un data source de prueba, lo usa; sino usa el pool principal
 */
export const executeQuery = async <T = any>(
  text: string, 
  params?: any[], 
  testDataSource?: DataSource
): Promise<{ rows: T[]; rowCount: number }> => {
  if (testDataSource) {
    // Usar el data source de prueba
    const result = await testDataSource.query(text, params);
    return { 
      rows: result as T[], 
      rowCount: Array.isArray(result) ? result.length : (result?.rowCount || 0)
    };
  } else {
    // Usar el pool principal
    const result = await query(text, params);
    return { 
      rows: result.rows as T[], 
      rowCount: result.rowCount || 0 
    };
  }
};

export const getClient = async () => {
  const poolToUse = getDBPool();
  const client = await poolToUse.connect();
  // Opcional: Envolver query/release para logging o control adicional
  // const originalQuery = client.query;
  // client.query = (...args: any[]) => {
  //   console.log('Executing query on client:', args[0]);
  //   return originalQuery.apply(client, args);
  // };
  return client;
};

// Es buena práctica cerrar el pool cuando la aplicación se apaga
export const closeDBPool = async () => {
  if (pool) {
    await pool.end();
    if (config.database.logging) {
      console.log('Pool de conexiones a PostgreSQL cerrado.');
    }
    pool = undefined; // Para permitir reinicialización si es necesario (ej. en tests)
  }
};

// Manejar cierre graceful de la aplicación
process.on('SIGINT', async () => {
  console.log('Cerrando pool de DB por SIGINT...');
  await closeDBPool();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Cerrando pool de DB por SIGTERM...');
  await closeDBPool();
  process.exit(0);
});
