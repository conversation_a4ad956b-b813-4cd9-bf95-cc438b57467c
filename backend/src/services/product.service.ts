import { Repository, DataSource } from 'typeorm';
import { Product } from '../entities/product.entity';
import { 
  CreateProductDto, 
  UpdateProductDto, 
  ProductResponseDto, 
  ProductFilterDto,
  StockMovementDto 
} from '../dtos/product.dto';
import { NotificationService } from '../services/notification.service';
import { NotificationType, NotificationPriority } from '../entities/notification.entity';

export class ProductService {
  private productRepository: Repository<Product>;
  private notificationService: NotificationService;

  constructor(dataSource: DataSource, notificationService: NotificationService) {
    this.productRepository = dataSource.getRepository(Product);
    this.notificationService = notificationService;
  }

  /**
   * Crear un nuevo producto
   */
  async createProduct(createProductDto: CreateProductDto, creatorUserId?: number): Promise<ProductResponseDto> {
    try {
      // Verificar si el código ya existe
      const existingProduct = await this.productRepository.findOne({
        where: { codigoItem: createProductDto.codigoItem }
      });

      if (existingProduct) {
        throw new Error(`Ya existe un producto con el código: ${createProductDto.codigoItem}`);
      }

      // Crear nuevo producto
      const product = this.productRepository.create({
        ...createProductDto,
        createdBy: creatorUserId,
        updatedBy: creatorUserId,
      });

      const savedProduct = await this.productRepository.save(product);
      
      return this.transformToResponseDto(savedProduct);
    } catch (error: any) {
      if (error.message.includes('Ya existe un producto')) {
        throw error;
      }
      
      // Manejar errores de constraint de base de datos
      if (error.code === '23505') { // PostgreSQL unique constraint violation
        throw new Error(`Ya existe un producto con el código: ${createProductDto.codigoItem}`);
      }
      
      throw new Error(`Error al crear producto: ${error.message}`);
    }
  }

  /**
   * Obtener todos los productos con filtros y paginación
   */
  async findAllProducts(filters: ProductFilterDto): Promise<{
    products: ProductResponseDto[];
    total: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }> {
    const {
      page = 1,
      limit = 20,
      search,
      tipoAlmacen,
      estado,
      marca,
      modelo,
      ubicacion,
      sortBy = 'nombre',
      sortOrder = 'ASC'
    } = filters;

    const queryBuilder = this.productRepository.createQueryBuilder('product');

    // Aplicar filtros
    if (search) {
      queryBuilder.andWhere(
        '(LOWER(product.nombre) LIKE LOWER(:search) OR LOWER(product.codigoItem) LIKE LOWER(:search) OR LOWER(product.descripcion) LIKE LOWER(:search))',
        { search: `%${search}%` }
      );
    }

    if (tipoAlmacen) {
      queryBuilder.andWhere('product.tipoAlmacen = :tipoAlmacen', { tipoAlmacen });
    }

    if (estado) {
      queryBuilder.andWhere('product.estado = :estado', { estado });
    }

    if (marca) {
      queryBuilder.andWhere('LOWER(product.marca) LIKE LOWER(:marca)', { marca: `%${marca}%` });
    }

    if (modelo) {
      queryBuilder.andWhere('LOWER(product.modelo) LIKE LOWER(:modelo)', { modelo: `%${modelo}%` });
    }

    // Filtro por ubicación/bodega
    if (ubicacion) {
      queryBuilder.andWhere('product.ubicacion = :ubicacion', { ubicacion });
    }

    // Aplicar ordenamiento
    const sortField = `product.${sortBy}`;
    queryBuilder.orderBy(sortField, sortOrder.toUpperCase() as 'ASC' | 'DESC');

    // Aplicar paginación
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Obtener resultados y conteo total
    const [products, total] = await queryBuilder.getManyAndCount();

    const totalPages = Math.ceil(total / limit);

    return {
      products: products.map(product => this.transformToResponseDto(product)),
      total,
      totalPages,
      currentPage: page,
      pageSize: limit,
    };
  }

  /**
   * Obtener producto por ID
   */
  async findProductById(id: number): Promise<ProductResponseDto> {
    const product = await this.productRepository.findOne({
      where: { id }
    });

    if (!product) {
      throw new Error(`Producto con ID ${id} no encontrado`);
    }

    return this.transformToResponseDto(product);
  }

  /**
   * Obtener producto por código
   */
  async findProductByCode(codigoItem: string): Promise<ProductResponseDto> {
    const product = await this.productRepository.findOne({
      where: { codigoItem }
    });

    if (!product) {
      throw new Error(`Producto con código ${codigoItem} no encontrado`);
    }

    return this.transformToResponseDto(product);
  }

  /**
   * Actualizar producto
   */
  async updateProduct(id: number, updateProductDto: UpdateProductDto, updaterUserId?: number): Promise<ProductResponseDto> {
    const product = await this.productRepository.findOne({
      where: { id }
    });

    if (!product) {
      throw new Error(`Producto con ID ${id} no encontrado`);
    }

    // Si se está actualizando el código, verificar que no exista otro producto con ese código
    if (updateProductDto.codigoItem && updateProductDto.codigoItem !== product.codigoItem) {
      const existingProduct = await this.productRepository.findOne({
        where: { codigoItem: updateProductDto.codigoItem }
      });

      if (existingProduct) {
        throw new Error(`Ya existe un producto con el código: ${updateProductDto.codigoItem}`);
      }
    }

    try {
      // Guardar stock anterior
      const stockAnterior = product.stockDisponible;
      // Actualizar campos
      Object.assign(product, updateProductDto);
      product.updatedBy = updaterUserId;

      const savedProduct = await this.productRepository.save(product);

      // --- Notificación de stock bajo/crítico (si el stockDisponible cambió y quedó <= mínimo + umbral) ---
      const UMBRAL_STOCK_BAJO = 2; // Puedes ajustar este valor según la política de la empresa
      if (
        typeof updateProductDto.stockDisponible === 'number' &&
        updateProductDto.stockDisponible !== stockAnterior &&
        savedProduct.stockDisponible <= savedProduct.stockMinimo + UMBRAL_STOCK_BAJO
      ) {
        console.log('[NOTIF][TRACE] Stock actual:', savedProduct.stockDisponible, 'Stock mínimo:', savedProduct.stockMinimo, 'Producto:', savedProduct.nombre);
        const nivel = savedProduct.stockDisponible === 0 ? 'CRITICO' : 'BAJO';
        const title = nivel === 'CRITICO'
          ? `Stock CRÍTICO: ${savedProduct.nombre}`
          : `Stock bajo: ${savedProduct.nombre}`;
        const message = `El producto ${savedProduct.nombre} (${savedProduct.codigoItem}) tiene un stock de ${savedProduct.stockDisponible}, que está por debajo del mínimo configurado (${savedProduct.stockMinimo}).`;
        // Evitar notificaciones duplicadas (opcional: busca notificación no leída para este producto y nivel)
        const existeNotif = await this.notificationService.findUnreadNotificationByProductAndLevel(savedProduct.id, nivel);
        if (!existeNotif) {
          // Notificar a managers y admins
          const almacenManagerId = await this.getRoleIdByName('ROLE_ALMACEN_MANAGER');
          const adminId = await this.getRoleIdByName('ROLE_ADMIN');
          console.log('[NOTIF][DEBUG] almacenManagerId:', almacenManagerId, 'adminId:', adminId);
          const roleIds = [];
          if (almacenManagerId) roleIds.push(almacenManagerId);
          if (adminId) roleIds.push(adminId);
          if (roleIds.length > 0) {
            try {
              await this.notificationService.triggerNotification({
                title,
                message,
                type: nivel === 'CRITICO' ? NotificationType.ERROR : NotificationType.WARNING,
                priority: nivel === 'CRITICO' ? NotificationPriority.HIGH : NotificationPriority.MEDIUM,
                roleId: roleIds as number | number[],
                metadata: { productId: savedProduct.id, stock: savedProduct.stockDisponible, minStock: savedProduct.stockMinimo }
              });
              console.log('[NOTIF][DEBUG] Notificación enviada a roles:', roleIds);
            } catch (err) {
              console.error('[NOTIF][ERROR] Error enviando notificación a roles:', err);
            }
          } else {
            console.warn('[NOTIF][WARN] No se encontró ningún rol válido para notificación');
          }
        } else {
          console.log('[NOTIF][DEBUG] Ya existe notificación no leída para este producto y nivel:', nivel);
        }
      }

      return this.transformToResponseDto(savedProduct);
    } catch (error: any) {
      if (error.code === '23505') {
        throw new Error(`Ya existe un producto con el código: ${updateProductDto.codigoItem}`);
      }
      
      throw new Error(`Error al actualizar producto: ${error.message}`);
    }
  }

  /**
   * Eliminar producto
   */
  async deleteProduct(id: number): Promise<void> {
    const product = await this.productRepository.findOne({
      where: { id }
    });

    if (!product) {
      throw new Error(`Producto con ID ${id} no encontrado`);
    }

    await this.productRepository.remove(product);
  }

  /**
   * Reservar stock de un producto
   */
  async reservarStock(id: number, cantidad: number, reservadoPor?: number): Promise<ProductResponseDto> {
    const product = await this.productRepository.findOne({
      where: { id }
    });

    if (!product) {
      throw new Error(`Producto con ID ${id} no encontrado`);
    }

    const stockDisponibleReal = product.getStockReal();
    
    if (stockDisponibleReal < cantidad) {
      throw new Error(
        `Stock insuficiente. Disponible: ${stockDisponibleReal}, Solicitado: ${cantidad}`
      );
    }

    if (!product.isDisponibleParaVenta()) {
      throw new Error(`El producto no está disponible para venta`);
    }

    // Reservar stock
    product.stockComprometido += cantidad;
    product.updatedBy = reservadoPor;

    const savedProduct = await this.productRepository.save(product);
    
    // --- Notificación de stock bajo/crítico (si el stockDisponible cambió y quedó <= mínimo + umbral) ---
    const UMBRAL_STOCK_BAJO = 2; // Puedes ajustar este valor según la política de la empresa
    if (
      typeof savedProduct.stockDisponible === 'number' &&
      savedProduct.stockDisponible !== stockDisponibleReal &&
      savedProduct.stockDisponible <= savedProduct.stockMinimo + UMBRAL_STOCK_BAJO
    ) {
      console.log('[NOTIF][TRACE] Stock actual:', savedProduct.stockDisponible, 'Stock mínimo:', savedProduct.stockMinimo, 'Producto:', savedProduct.nombre);
      const nivel = savedProduct.stockDisponible === 0 ? 'CRITICO' : 'BAJO';
      const title = nivel === 'CRITICO'
        ? `Stock CRÍTICO: ${savedProduct.nombre}`
        : `Stock bajo: ${savedProduct.nombre}`;
      const message = `El producto ${savedProduct.nombre} (${savedProduct.codigoItem}) tiene un stock de ${savedProduct.stockDisponible}, que está por debajo del mínimo configurado (${savedProduct.stockMinimo}).`;
      // Evitar notificaciones duplicadas (opcional: busca notificación no leída para este producto y nivel)
      const existeNotif = await this.notificationService.findUnreadNotificationByProductAndLevel(savedProduct.id, nivel);
      if (!existeNotif) {
        // Notificar a managers y admins
        const almacenManagerId = await this.getRoleIdByName('ROLE_ALMACEN_MANAGER');
        const adminId = await this.getRoleIdByName('ROLE_ADMIN');
        console.log('[NOTIF][DEBUG] almacenManagerId:', almacenManagerId, 'adminId:', adminId);
        const roleIds = [];
        if (almacenManagerId) roleIds.push(almacenManagerId);
        if (adminId) roleIds.push(adminId);
        if (roleIds.length > 0) {
          try {
            await this.notificationService.triggerNotification({
              title,
              message,
              type: nivel === 'CRITICO' ? NotificationType.ERROR : NotificationType.WARNING,
              priority: nivel === 'CRITICO' ? NotificationPriority.HIGH : NotificationPriority.MEDIUM,
              roleId: roleIds as number | number[],
              metadata: { productId: savedProduct.id, stock: savedProduct.stockDisponible, minStock: savedProduct.stockMinimo }
            });
            console.log('[NOTIF][DEBUG] Notificación enviada a roles:', roleIds);
          } catch (err) {
            console.error('[NOTIF][ERROR] Error enviando notificación a roles:', err);
          }
        } else {
          console.warn('[NOTIF][WARN] No se encontró ningún rol válido para notificación');
        }
      } else {
        console.log('[NOTIF][DEBUG] Ya existe notificación no leída para este producto y nivel:', nivel);
      }
    }
    
    return this.transformToResponseDto(savedProduct);
  }

  /**
   * Liberar stock reservado
   */
  async liberarStock(id: number, cantidad: number, liberadoPor?: number): Promise<ProductResponseDto> {
    const product = await this.productRepository.findOne({
      where: { id }
    });

    if (!product) {
      throw new Error(`Producto con ID ${id} no encontrado`);
    }

    if (product.stockComprometido < cantidad) {
      throw new Error(
        `No se puede liberar más stock del comprometido. Comprometido: ${product.stockComprometido}, Solicitado: ${cantidad}`
      );
    }

    // Liberar stock
    product.stockComprometido -= cantidad;
    product.updatedBy = liberadoPor;

    const savedProduct = await this.productRepository.save(product);
    
    // --- Notificación de stock bajo/crítico (si el stockDisponible cambió y quedó <= mínimo + umbral) ---
    const UMBRAL_STOCK_BAJO = 2; // Puedes ajustar este valor según la política de la empresa
    if (
      typeof savedProduct.stockDisponible === 'number' &&
      savedProduct.stockDisponible !== product.stockDisponible &&
      savedProduct.stockDisponible <= product.stockMinimo + UMBRAL_STOCK_BAJO
    ) {
      console.log('[NOTIF][TRACE] Stock actual:', savedProduct.stockDisponible, 'Stock mínimo:', product.stockMinimo, 'Producto:', savedProduct.nombre);
      const nivel = savedProduct.stockDisponible === 0 ? 'CRITICO' : 'BAJO';
      const title = nivel === 'CRITICO'
        ? `Stock CRÍTICO: ${savedProduct.nombre}`
        : `Stock bajo: ${savedProduct.nombre}`;
      const message = `El producto ${savedProduct.nombre} (${savedProduct.codigoItem}) tiene un stock de ${savedProduct.stockDisponible}, que está por debajo del mínimo configurado (${savedProduct.stockMinimo}).`;
      // Evitar notificaciones duplicadas (opcional: busca notificación no leída para este producto y nivel)
      const existeNotif = await this.notificationService.findUnreadNotificationByProductAndLevel(savedProduct.id, nivel);
      if (!existeNotif) {
        // Notificar a managers y admins
        const almacenManagerId = await this.getRoleIdByName('ROLE_ALMACEN_MANAGER');
        const adminId = await this.getRoleIdByName('ROLE_ADMIN');
        console.log('[NOTIF][DEBUG] almacenManagerId:', almacenManagerId, 'adminId:', adminId);
        const roleIds = [];
        if (almacenManagerId) roleIds.push(almacenManagerId);
        if (adminId) roleIds.push(adminId);
        if (roleIds.length > 0) {
          try {
            await this.notificationService.triggerNotification({
              title,
              message,
              type: nivel === 'CRITICO' ? NotificationType.ERROR : NotificationType.WARNING,
              priority: nivel === 'CRITICO' ? NotificationPriority.HIGH : NotificationPriority.MEDIUM,
              roleId: roleIds as number | number[],
              metadata: { productId: savedProduct.id, stock: savedProduct.stockDisponible, minStock: product.stockMinimo }
            });
            console.log('[NOTIF][DEBUG] Notificación enviada a roles:', roleIds);
          } catch (err) {
            console.error('[NOTIF][ERROR] Error enviando notificación a roles:', err);
          }
        } else {
          console.warn('[NOTIF][WARN] No se encontró ningún rol válido para notificación');
        }
      } else {
        console.log('[NOTIF][DEBUG] Ya existe notificación no leída para este producto y nivel:', nivel);
      }
    }
    
    return this.transformToResponseDto(savedProduct);
  }

  /**
   * Ajustar stock (entrada/salida de inventario)
   */
  async ajustarStock(id: number, stockMovement: StockMovementDto, realizadoPor?: number): Promise<ProductResponseDto> {
    const product = await this.productRepository.findOne({
      where: { id }
    });

    if (!product) {
      throw new Error(`Producto con ID ${id} no encontrado`);
    }

    const { tipo, cantidad } = stockMovement;

    if (tipo === 'ENTRADA') {
      product.stockDisponible += cantidad;
    } else if (tipo === 'SALIDA') {
      if (product.stockDisponible < cantidad) {
        throw new Error(
          `Stock insuficiente para la salida. Disponible: ${product.stockDisponible}, Solicitado: ${cantidad}`
        );
      }
      product.stockDisponible -= cantidad;
    }

    product.updatedBy = realizadoPor;

    const savedProduct = await this.productRepository.save(product);
    
    // --- Notificación de stock bajo/crítico (si el stockDisponible cambió y quedó <= mínimo + umbral) ---
    const UMBRAL_STOCK_BAJO = 2; // Puedes ajustar este valor según la política de la empresa
    if (
      typeof savedProduct.stockDisponible === 'number' &&
      savedProduct.stockDisponible !== product.stockDisponible &&
      savedProduct.stockDisponible <= product.stockMinimo + UMBRAL_STOCK_BAJO
    ) {
      console.log('[NOTIF][TRACE] Stock actual:', savedProduct.stockDisponible, 'Stock mínimo:', product.stockMinimo, 'Producto:', savedProduct.nombre);
      const nivel = savedProduct.stockDisponible === 0 ? 'CRITICO' : 'BAJO';
      const title = nivel === 'CRITICO'
        ? `Stock CRÍTICO: ${savedProduct.nombre}`
        : `Stock bajo: ${savedProduct.nombre}`;
      const message = `El producto ${savedProduct.nombre} (${savedProduct.codigoItem}) tiene un stock de ${savedProduct.stockDisponible}, que está por debajo del mínimo configurado (${savedProduct.stockMinimo}).`;
      // Evitar notificaciones duplicadas (opcional: busca notificación no leída para este producto y nivel)
      const existeNotif = await this.notificationService.findUnreadNotificationByProductAndLevel(savedProduct.id, nivel);
      if (!existeNotif) {
        // Notificar a managers y admins
        const almacenManagerId = await this.getRoleIdByName('ROLE_ALMACEN_MANAGER');
        const adminId = await this.getRoleIdByName('ROLE_ADMIN');
        console.log('[NOTIF][DEBUG] almacenManagerId:', almacenManagerId, 'adminId:', adminId);
        const roleIds = [];
        if (almacenManagerId) roleIds.push(almacenManagerId);
        if (adminId) roleIds.push(adminId);
        if (roleIds.length > 0) {
          try {
            await this.notificationService.triggerNotification({
              title,
              message,
              type: nivel === 'CRITICO' ? NotificationType.ERROR : NotificationType.WARNING,
              priority: nivel === 'CRITICO' ? NotificationPriority.HIGH : NotificationPriority.MEDIUM,
              roleId: roleIds as number | number[],
              metadata: { productId: savedProduct.id, stock: savedProduct.stockDisponible, minStock: product.stockMinimo }
            });
            console.log('[NOTIF][DEBUG] Notificación enviada a roles:', roleIds);
          } catch (err) {
            console.error('[NOTIF][ERROR] Error enviando notificación a roles:', err);
          }
        } else {
          console.warn('[NOTIF][WARN] No se encontró ningún rol válido para notificación');
        }
      } else {
        console.log('[NOTIF][DEBUG] Ya existe notificación no leída para este producto y nivel:', nivel);
      }
    }
    
    return this.transformToResponseDto(savedProduct);
  }

  /**
   * Obtener productos con stock bajo
   */
  async findProductsWithLowStock(): Promise<ProductResponseDto[]> {
    const products = await this.productRepository
      .createQueryBuilder('product')
      .where('product.stockDisponible <= product.stockMinimo')
      .andWhere('product.estado = :estado', { estado: 'DISPONIBLE' })
      .orderBy('product.stockDisponible', 'ASC')
      .getMany();

    return products.map(product => this.transformToResponseDto(product));
  }

  /**
   * Obtener estadísticas de inventario
   */
  async getInventoryStats(): Promise<{
    totalProducts: number;
    totalGeneral: number;
    totalRotativo: number;
    lowStockCount: number;
    outOfStockCount: number;
    averageStockValue: number;
  }> {
    const totalProducts = await this.productRepository.count();
    
    const totalGeneral = await this.productRepository.count({
      where: { tipoAlmacen: 'GENERAL' }
    });
    
    const totalRotativo = await this.productRepository.count({
      where: { tipoAlmacen: 'ROTATIVO' }
    });

    const lowStockProducts = await this.productRepository
      .createQueryBuilder('product')
      .where('(product.stockDisponible - product.stockComprometido) <= product.stockMinimo')
      .getCount();

    const outOfStockProducts = await this.productRepository
      .createQueryBuilder('product')
      .where('(product.stockDisponible - product.stockComprometido) <= 0')
      .getCount();

    return {
      totalProducts,
      totalGeneral,
      totalRotativo,
      lowStockCount: lowStockProducts,
      outOfStockCount: outOfStockProducts,
      averageStockValue: 0, // Se podría calcular si se tiene información de precios
    };
  }

  /**
   * Obtener todas las ubicaciones/bodegas disponibles
   */
  async getAvailableLocations(): Promise<string[]> {
    const locations = await this.productRepository
      .createQueryBuilder('product')
      .select('DISTINCT product.ubicacion', 'ubicacion')
      .where('product.ubicacion IS NOT NULL')
      .getRawMany();

    return locations.map(location => location.ubicacion).sort();
  }

  /**
   * Transformar entidad a DTO de respuesta
   */
  private transformToResponseDto(product: Product): ProductResponseDto {
    return {
      id: product.id,
      codigoItem: product.codigoItem,
      nombre: product.nombre,
      descripcion: product.descripcion,
      marca: product.marca,
      modelo: product.modelo,
      numeroSerie: product.numeroSerie,
      pedimento: product.pedimento,
      observaciones: product.observaciones,
      tipoAlmacen: product.tipoAlmacen,
      stockDisponible: product.stockDisponible,
      stockComprometido: product.stockComprometido,
      stockMinimo: product.stockMinimo,
      stockReal: product.getStockReal(),
      nivelStock: product.getNivelStock(),
      colorIndicador: product.getColorIndicador(),
      estado: product.estado,
      ubicacion: product.ubicacion,
      disponibleParaVenta: product.isDisponibleParaVenta(),
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      createdBy: product.createdBy,
      updatedBy: product.updatedBy,
    };
  }

  // Función utilitaria para obtener el id de un rol por nombre
  private async getRoleIdByName(roleName: string): Promise<number | null> {
    const result = await this.productRepository.query(
      'SELECT id FROM roles WHERE name = $1',
      [roleName]
    );
    return result && result[0] ? parseInt(result[0].id, 10) : null;
  }
} 