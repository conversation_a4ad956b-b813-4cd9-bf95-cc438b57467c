import { DataSource, Repository } from 'typeorm';
import { InventoryMovement, MovementType, MovementStatus } from '../entities/inventory-movement.entity';
import { Product } from '../entities/product.entity';
import { Voucher, VoucherType } from '../entities/voucher.entity';
import { User } from '../entities/user.entity';
import { NotificationService } from './notification.service';
import { NotificationType, NotificationPriority } from '../entities/notification.entity';
import { 
  CreateInventoryMovementDto, 
  UpdateInventoryMovementDto, 
  ConfirmInventoryMovementDto,
  InventoryMovementFilterDto,
  QRScanDataDto,
  InventoryMovementResponseDto,
  InventoryMovementStatsDto,
  BulkInventoryMovementDto
} from '../dto/inventory-movement.dto';

export class InventoryMovementService {
  private movementRepository: Repository<InventoryMovement>;
  private productRepository: Repository<Product>;
  private voucherRepository: Repository<Voucher>;
  private userRepository: Repository<User>;
  private notificationService: NotificationService;

  constructor(private dataSource: DataSource, notificationService: NotificationService) {
    this.movementRepository = this.dataSource.getRepository(InventoryMovement);
    this.productRepository = this.dataSource.getRepository(Product);
    this.voucherRepository = this.dataSource.getRepository(Voucher);
    this.userRepository = this.dataSource.getRepository(User);
    this.notificationService = notificationService;
  }

  /**
   * Crea un nuevo movimiento de inventario
   */
  async createMovement(
    createDto: CreateInventoryMovementDto, 
    userId: number
  ): Promise<InventoryMovement> {
    // Verificar que el producto existe
    const product = await this.productRepository.findOne({ 
      where: { id: createDto.productoId } 
    });
    
    if (!product) {
      throw new Error('Producto no encontrado');
    }

    // Verificar que el usuario existe
    const user = await this.userRepository.findOne({ 
      where: { id: userId } 
    });
    
    if (!user) {
      throw new Error('Usuario no encontrado');
    }

    // Generar folio único
    const folio = await this.generateFolio(createDto.tipo);

    // Crear el movimiento
    const movement = this.movementRepository.create({
      ...createDto,
      folio,
      usuarioId: userId,
      createdBy: userId,
      updatedBy: userId
    });

    // Validar stock para salidas
    if (createDto.tipo === MovementType.OUTPUT || createDto.tipo === MovementType.LOAN) {
      const stockReal = product.getStockReal();
      if (stockReal < createDto.cantidad) {
        throw new Error(
          `Stock insuficiente. Disponible: ${stockReal}, Solicitado: ${createDto.cantidad}`
        );
      }
    }

    // Guardar el movimiento
    const savedMovement = await this.movementRepository.save(movement);

    // Actualizar stock si es una entrada
    if (createDto.tipo === MovementType.INPUT) {
      await this.updateProductStock(product.id, createDto.cantidad, 'add');
    }

    // Enviar notificación en tiempo real
    const movementTypeText = this.getMovementTypeText(createDto.tipo);
    await this.sendMovementNotification(
      `Nuevo movimiento de inventario: ${movementTypeText}`,
      `Se ha creado un movimiento de ${movementTypeText.toLowerCase()} para el producto "${product.nombre}" (${product.codigoItem}) con cantidad ${createDto.cantidad}. Folio: ${folio}`,
      NotificationType.INFO,
      NotificationPriority.MEDIUM,
      {
        movementId: savedMovement.id,
        productId: product.id,
        productName: product.nombre,
        productCode: product.codigoItem,
        movementType: createDto.tipo,
        quantity: createDto.cantidad,
        folio: folio,
        userId: userId
      }
    );

    return savedMovement;
  }

  /**
   * Confirma un movimiento de inventario
   */
  async confirmMovement(
    movementId: number, 
    confirmDto: ConfirmInventoryMovementDto, 
    userId: number
  ): Promise<InventoryMovement> {
    const movement = await this.movementRepository.findOne({ 
      where: { id: movementId },
      relations: ['producto']
    });

    if (!movement) {
      throw new Error('Movimiento no encontrado');
    }

    if (!movement.canBeConfirmed()) {
      throw new Error('El movimiento no puede ser confirmado');
    }

    // Confirmar el movimiento
    movement.confirm(userId);
    
    if (confirmDto.observaciones) {
      movement.observaciones = confirmDto.observaciones;
    }

    movement.updatedBy = userId;

    // Actualizar stock según el tipo de movimiento
    await this.processStockUpdate(movement);

    // Generar voucher si es necesario
    if (movement.tipo !== MovementType.ADJUSTMENT) {
      await this.generateVoucher(movement, userId);
    }

    const savedMovement = await this.movementRepository.save(movement);

    // Enviar notificación en tiempo real de confirmación
    const movementTypeText = this.getMovementTypeText(movement.tipo);
    await this.sendMovementNotification(
      `Movimiento confirmado: ${movementTypeText}`,
      `Se ha confirmado el movimiento de ${movementTypeText.toLowerCase()} para el producto "${movement.producto?.nombre}" (${movement.producto?.codigoItem}) con cantidad ${movement.cantidad}. Folio: ${movement.folio}`,
      NotificationType.SUCCESS,
      NotificationPriority.MEDIUM,
      {
        movementId: savedMovement.id,
        productId: movement.productoId,
        productName: movement.producto?.nombre,
        productCode: movement.producto?.codigoItem,
        movementType: movement.tipo,
        quantity: movement.cantidad,
        folio: movement.folio,
        confirmedBy: userId,
        status: 'CONFIRMED'
      }
    );

    return savedMovement;
  }

  /**
   * Cancela un movimiento de inventario
   */
  async cancelMovement(movementId: number, userId: number): Promise<InventoryMovement> {
    const movement = await this.movementRepository.findOne({ 
      where: { id: movementId },
      relations: ['producto']
    });

    if (!movement) {
      throw new Error('Movimiento no encontrado');
    }

    if (!movement.canBeCancelled()) {
      throw new Error('El movimiento no puede ser cancelado');
    }

    // Cancelar el movimiento
    movement.cancel();
    movement.updatedBy = userId;

    // Revertir stock si ya se había aplicado
    if (movement.estado === MovementStatus.CONFIRMED) {
      await this.revertStockUpdate(movement);
    }

    const savedMovement = await this.movementRepository.save(movement);

    // Enviar notificación en tiempo real de cancelación
    const movementTypeText = this.getMovementTypeText(movement.tipo);
    await this.sendMovementNotification(
      `Movimiento cancelado: ${movementTypeText}`,
      `Se ha cancelado el movimiento de ${movementTypeText.toLowerCase()} para el producto "${movement.producto?.nombre}" (${movement.producto?.codigoItem}) con cantidad ${movement.cantidad}. Folio: ${movement.folio}`,
      NotificationType.WARNING,
      NotificationPriority.MEDIUM,
      {
        movementId: savedMovement.id,
        productId: movement.productoId,
        productName: movement.producto?.nombre,
        productCode: movement.producto?.codigoItem,
        movementType: movement.tipo,
        quantity: movement.cantidad,
        folio: movement.folio,
        cancelledBy: userId,
        status: 'CANCELLED'
      }
    );

    return savedMovement;
  }

  /**
   * Actualiza un movimiento
   */
  async updateMovement(
    movementId: number, 
    updateDto: UpdateInventoryMovementDto, 
    userId: number
  ): Promise<InventoryMovement> {
    const movement = await this.movementRepository.findOne({ 
      where: { id: movementId }
    });

    if (!movement) {
      throw new Error('Movimiento no encontrado');
    }

    // Solo permitir actualizar campos específicos
    if (updateDto.observaciones !== undefined) {
      movement.observaciones = updateDto.observaciones;
    }
    if (updateDto.motivo !== undefined) {
      movement.motivo = updateDto.motivo;
    }

    movement.updatedBy = userId;

    return await this.movementRepository.save(movement);
  }

  /**
   * Obtiene movimientos con filtros y paginación
   */
  async getMovements(filterDto: InventoryMovementFilterDto): Promise<{
    data: InventoryMovementResponseDto[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    const {
      tipo,
      estado,
      productoId,
      usuarioId,
      folio,
      search,
      fechaDesde,
      fechaHasta,
      page = 1,
      limit = 20,
      sortBy = 'fechaMovimiento',
      sortOrder = 'DESC'
    } = filterDto;

    const offset = (page - 1) * limit;

    // Construir query base
    let query = this.movementRepository
      .createQueryBuilder('movement')
      .leftJoinAndSelect('movement.producto', 'producto')
      .leftJoinAndSelect('movement.usuario', 'usuario')
      .leftJoinAndSelect('movement.confirmadoPor', 'confirmadoPor');

    // Aplicar filtros
    if (tipo) {
      query = query.andWhere('movement.tipo = :tipo', { tipo });
    }

    if (estado) {
      query = query.andWhere('movement.estado = :estado', { estado });
    }

    if (productoId) {
      query = query.andWhere('movement.productoId = :productoId', { productoId });
    }

    if (usuarioId) {
      query = query.andWhere('movement.usuarioId = :usuarioId', { usuarioId });
    }

    if (folio) {
      query = query.andWhere('movement.folio ILIKE :folio', { folio: `%${folio}%` });
    }

    if (search) {
      query = query.andWhere(
        '(movement.folio ILIKE :search OR producto.nombre ILIKE :search OR usuario.name ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    if (fechaDesde) {
      query = query.andWhere('movement.fechaMovimiento >= :fechaDesde', { fechaDesde });
    }

    if (fechaHasta) {
      query = query.andWhere('movement.fechaMovimiento <= :fechaHasta', { fechaHasta });
    }

    // Contar total
    const total = await query.getCount();

    // Aplicar ordenamiento y paginación
    query = query
      .orderBy(`movement.${sortBy}`, sortOrder as 'ASC' | 'DESC')
      .skip(offset)
      .take(limit);

    const movements = await query.getMany();

    // Convertir a DTOs de respuesta
    const data = movements.map(movement => ({
      id: movement.id,
      folio: movement.folio,
      tipo: movement.tipo,
      estado: movement.estado,
      cantidad: movement.cantidad,
      motivo: movement.motivo,
      observaciones: movement.observaciones,
      fechaMovimiento: movement.fechaMovimiento,
      fechaConfirmacion: movement.fechaConfirmacion,
      producto: movement.producto ? {
        id: movement.producto.id,
        codigoItem: movement.producto.codigoItem,
        nombre: movement.producto.nombre,
        marca: movement.producto.marca,
        modelo: movement.producto.modelo
      } : null,
      usuario: movement.usuario ? {
        id: movement.usuario.id,
        name: movement.usuario.name,
        email: movement.usuario.email
      } : null,
      confirmadoPor: movement.confirmadoPor ? {
        id: movement.confirmadoPor.id,
        name: movement.confirmadoPor.name,
        email: movement.confirmadoPor.email
      } : null,
      voucherId: movement.voucherId,
      voucherPath: movement.voucherPath,
      qrDataScanned: movement.qrDataScanned
    }));

    return {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Obtiene un movimiento por ID
   */
  async getMovementById(id: number): Promise<InventoryMovementResponseDto> {
    const movement = await this.movementRepository.findOne({
      where: { id },
      relations: ['producto', 'usuario', 'confirmadoPor']
    });

    if (!movement) {
      throw new Error('Movimiento no encontrado');
    }

    return {
      id: movement.id,
      folio: movement.folio,
      tipo: movement.tipo,
      estado: movement.estado,
      cantidad: movement.cantidad,
      motivo: movement.motivo,
      observaciones: movement.observaciones,
      fechaMovimiento: movement.fechaMovimiento,
      fechaConfirmacion: movement.fechaConfirmacion,
      producto: movement.producto ? {
        id: movement.producto.id,
        codigoItem: movement.producto.codigoItem,
        nombre: movement.producto.nombre,
        marca: movement.producto.marca,
        modelo: movement.producto.modelo
      } : null,
      usuario: movement.usuario ? {
        id: movement.usuario.id,
        name: movement.usuario.name,
        email: movement.usuario.email
      } : null,
      confirmadoPor: movement.confirmadoPor ? {
        id: movement.confirmadoPor.id,
        name: movement.confirmadoPor.name,
        email: movement.confirmadoPor.email
      } : null,
      voucherId: movement.voucherId,
      voucherPath: movement.voucherPath,
      qrDataScanned: movement.qrDataScanned
    };
  }

  /**
   * Procesa datos QR y encuentra el producto correspondiente
   */
  async processQRData(qrDataDto: QRScanDataDto): Promise<{
    producto?: Product;
    error?: string;
    message?: string;
    qrData?: any;
  }> {
    try {
      const { qrData } = qrDataDto;

      // Intentar parsear como JSON primero (QR completo)
      let parsedData: any = null;
      let isJsonQR = false;

      try {
        parsedData = JSON.parse(qrData);
        isJsonQR = true;
      } catch (e) {
        // No es JSON, tratar como código simple
        parsedData = null;
      }

      let product: Product | null = null;
      let searchCode: string = '';

      if (isJsonQR && parsedData) {
        // Procesar QR completo con datos JSON
        if (parsedData.type === 'product' && parsedData.product) {
          const productData = parsedData.product;
          
          // Buscar por ID del producto
          if (productData.id) {
            product = await this.productRepository.findOne({
              where: { id: parseInt(productData.id) }
            });
          }
          
          // Si no se encuentra por ID, buscar por código
          if (!product && productData.codigoItem) {
            product = await this.productRepository.findOne({
              where: { codigoItem: productData.codigoItem }
            });
            searchCode = productData.codigoItem;
          }

          // Verificar si los datos del QR coinciden con la base de datos
          if (product) {
            const verificationResult = this.verifyQRDataWithProduct(product, productData);
            if (!verificationResult.matches) {
              return {
                error: `Los datos del QR no coinciden con el producto en la base de datos. ${verificationResult.differences.join(', ')}`,
                qrData: parsedData
              };
            }
          }
        } else {
          return {
            error: 'Formato de QR inválido. Se esperaba un QR de producto.',
            qrData: parsedData
          };
        }
      } else {
        // Procesar código simple (código de producto directo)
        searchCode = qrData.trim();
        
        // Buscar producto por código
        product = await this.productRepository.findOne({
          where: { codigoItem: searchCode }
        });

        // Si no se encuentra por código, intentar buscar por ID
        if (!product && !isNaN(parseInt(searchCode))) {
          product = await this.productRepository.findOne({
            where: { id: parseInt(searchCode) }
          });
        }
      }

      if (!product) {
        const searchType = isJsonQR ? 'datos del QR' : 'código';
        const searchValue = isJsonQR ? (parsedData?.product?.codigoItem || parsedData?.product?.id) : searchCode;
        
        return {
          error: `Producto con ${searchType} "${searchValue}" no encontrado`,
          qrData: isJsonQR ? parsedData : searchCode
        };
      }

      return {
        producto: product,
        message: 'Producto encontrado exitosamente',
        qrData: isJsonQR ? parsedData : searchCode
      };
    } catch (error: any) {
      return {
        error: error.message || 'Error procesando datos QR',
        qrData: qrDataDto.qrData
      };
    }
  }

  /**
   * Verifica que los datos del QR coincidan con el producto en la base de datos
   */
  private verifyQRDataWithProduct(product: Product, qrProductData: any): {
    matches: boolean;
    differences: string[];
  } {
    const differences: string[] = [];
    
    // Verificar campos críticos
    if (qrProductData.codigoItem && qrProductData.codigoItem !== product.codigoItem) {
      differences.push(`Código: QR="${qrProductData.codigoItem}" vs DB="${product.codigoItem}"`);
    }
    
    if (qrProductData.nombre && qrProductData.nombre !== product.nombre) {
      differences.push(`Nombre: QR="${qrProductData.nombre}" vs DB="${product.nombre}"`);
    }
    
    if (qrProductData.tipoAlmacen && qrProductData.tipoAlmacen !== product.tipoAlmacen) {
      differences.push(`Tipo: QR="${qrProductData.tipoAlmacen}" vs DB="${product.tipoAlmacen}"`);
    }
    
    if (qrProductData.estado && qrProductData.estado !== product.estado) {
      differences.push(`Estado: QR="${qrProductData.estado}" vs DB="${product.estado}"`);
    }

    return {
      matches: differences.length === 0,
      differences
    };
  }

  /**
   * Crea un movimiento desde datos QR
   */
  async createMovementFromQR(
    createDto: CreateInventoryMovementDto, 
    userId: number
  ): Promise<InventoryMovement> {
    // Procesar datos QR primero
    const qrResult = await this.processQRData({ qrData: createDto.qrDataScanned || '' });
    
    if (qrResult.error) {
      throw new Error(qrResult.error);
    }

    // Usar el producto encontrado por QR
    if (qrResult.producto) {
      createDto.productoId = qrResult.producto.id;
    }

    return await this.createMovement(createDto, userId);
  }

  /**
   * Obtiene estadísticas de movimientos
   */
  async getMovementStats(): Promise<InventoryMovementStatsDto> {
    const [
      totalMovements,
      pendingMovements,
      confirmedMovements,
      cancelledMovements,
      todayMovements,
      thisWeekMovements,
      thisMonthMovements
    ] = await Promise.all([
      this.movementRepository.count(),
      this.movementRepository.count({ where: { estado: MovementStatus.PENDING } }),
      this.movementRepository.count({ where: { estado: MovementStatus.CONFIRMED } }),
      this.movementRepository.count({ where: { estado: MovementStatus.CANCELLED } }),
      this.movementRepository.count({
        where: {
          fechaMovimiento: new Date()
        }
      }),
      this.movementRepository.count({
        where: {
          fechaMovimiento: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        }
      }),
      this.movementRepository.count({
        where: {
          fechaMovimiento: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        }
      })
    ]);

    return {
      totalMovements,
      pendingMovements,
      confirmedMovements,
      cancelledMovements,
      todayMovements,
      thisWeekMovements,
      thisMonthMovements
    };
  }

  /**
   * Obtiene movimientos por tipo
   */
  async getMovementsByType(tipo: string, filterDto: InventoryMovementFilterDto) {
    return await this.getMovements({ ...filterDto, tipo: tipo as MovementType });
  }

  /**
   * Obtiene movimientos por estado
   */
  async getMovementsByStatus(estado: string, filterDto: InventoryMovementFilterDto) {
    return await this.getMovements({ ...filterDto, estado: estado as MovementStatus });
  }

  /**
   * Obtiene movimientos por producto
   */
  async getMovementsByProduct(productoId: number, filterDto: InventoryMovementFilterDto) {
    return await this.getMovements({ ...filterDto, productoId });
  }

  /**
   * Obtiene mis movimientos
   */
  async getMyMovements(filterDto: InventoryMovementFilterDto, userId: number) {
    return await this.getMovements({ ...filterDto, usuarioId: userId });
  }

  /**
   * Obtiene movimientos pendientes
   */
  async getPendingMovements(filterDto: InventoryMovementFilterDto) {
    return await this.getMovements({ ...filterDto, estado: MovementStatus.PENDING });
  }

  /**
   * Crea movimientos masivos
   */
  async createBulkMovements(
    bulkDto: BulkInventoryMovementDto, 
    userId: number
  ): Promise<{
    created: number;
    failed: number;
    errors: string[];
  }> {
    const results = [];
    const errors = [];

    for (const movementDto of bulkDto.movements) {
      try {
        await this.createMovement(movementDto, userId);
        results.push(movementDto);
      } catch (error: any) {
        errors.push(`${movementDto.productoId}: ${error.message}`);
      }
    }

    const createdCount = results.length;
    const failedCount = errors.length;

    const result = {
      created: createdCount,
      failed: failedCount,
      errors
    };

    // Enviar notificación en tiempo real de movimientos masivos
    if (createdCount > 0) {
      await this.sendMovementNotification(
        `Movimientos masivos procesados`,
        `Se han procesado ${createdCount} movimientos de inventario exitosamente. ${failedCount > 0 ? `${failedCount} movimientos fallaron.` : ''}`,
        failedCount > 0 ? NotificationType.WARNING : NotificationType.SUCCESS,
        NotificationPriority.MEDIUM,
        {
          totalProcessed: bulkDto.movements.length,
          created: createdCount,
          failed: failedCount,
          errors: errors.length > 0 ? errors : undefined,
          userId: userId,
          movementType: bulkDto.movements[0]?.tipo // Tipo del primer movimiento como referencia
        }
      );
    }

    return result;
  }

  /**
   * Obtiene movimientos recientes
   */
  async getRecentMovements(limit: number = 10): Promise<InventoryMovementResponseDto[]> {
    const movements = await this.movementRepository.find({
      relations: ['producto', 'usuario'],
      order: { fechaMovimiento: 'DESC' },
      take: limit
    });

    return movements.map(movement => ({
      id: movement.id,
      folio: movement.folio,
      tipo: movement.tipo,
      estado: movement.estado,
      cantidad: movement.cantidad,
      motivo: movement.motivo,
      observaciones: movement.observaciones,
      fechaMovimiento: movement.fechaMovimiento,
      fechaConfirmacion: movement.fechaConfirmacion,
      producto: movement.producto ? {
        id: movement.producto.id,
        codigoItem: movement.producto.codigoItem,
        nombre: movement.producto.nombre,
        marca: movement.producto.marca,
        modelo: movement.producto.modelo
      } : null,
      usuario: movement.usuario ? {
        id: movement.usuario.id,
        name: movement.usuario.name,
        email: movement.usuario.email
      } : null,
      confirmadoPor: null,
      voucherId: movement.voucherId,
      voucherPath: movement.voucherPath,
      qrDataScanned: movement.qrDataScanned
    }));
  }

  /**
   * Busca por folio
   */
  async searchByFolio(folio: string): Promise<InventoryMovementResponseDto[]> {
    const movements = await this.movementRepository.find({
      where: { folio },
      relations: ['producto', 'usuario', 'confirmadoPor']
    });

    return movements.map(movement => ({
      id: movement.id,
      folio: movement.folio,
      tipo: movement.tipo,
      estado: movement.estado,
      cantidad: movement.cantidad,
      motivo: movement.motivo,
      observaciones: movement.observaciones,
      fechaMovimiento: movement.fechaMovimiento,
      fechaConfirmacion: movement.fechaConfirmacion,
      producto: movement.producto ? {
        id: movement.producto.id,
        codigoItem: movement.producto.codigoItem,
        nombre: movement.producto.nombre,
        marca: movement.producto.marca,
        modelo: movement.producto.modelo
      } : null,
      usuario: movement.usuario ? {
        id: movement.usuario.id,
        name: movement.usuario.name,
        email: movement.usuario.email
      } : null,
      confirmadoPor: movement.confirmadoPor ? {
        id: movement.confirmadoPor.id,
        name: movement.confirmadoPor.name,
        email: movement.confirmadoPor.email
      } : null,
      voucherId: movement.voucherId,
      voucherPath: movement.voucherPath,
      qrDataScanned: movement.qrDataScanned
    }));
  }

  /**
   * Obtiene resumen por rango de fechas
   */
  async getMovementsSummary(fechaDesde: string, fechaHasta: string) {
    const movements = await this.movementRepository
      .createQueryBuilder('movement')
      .leftJoinAndSelect('movement.producto', 'producto')
      .where('movement.fechaMovimiento >= :fechaDesde', { fechaDesde: new Date(fechaDesde) })
      .andWhere('movement.fechaMovimiento <= :fechaHasta', { fechaHasta: new Date(fechaHasta) })
      .getMany();

    const summary = {
      totalMovements: movements.length,
      totalQuantity: movements.reduce((sum, m) => sum + m.cantidad, 0),
      byType: movements.reduce((acc, m) => {
        acc[m.tipo] = (acc[m.tipo] || 0) + m.cantidad;
        return acc;
      }, {} as Record<string, number>),
      byStatus: movements.reduce((acc, m) => {
        acc[m.estado] = (acc[m.estado] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };

    return summary;
  }

  /**
   * Genera un folio único para el movimiento
   */
  private async generateFolio(tipo: MovementType): Promise<string> {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    
    const prefix = tipo === MovementType.INPUT ? 'ENT' : 
                  tipo === MovementType.OUTPUT ? 'SAL' : 
                  tipo === MovementType.LOAN ? 'PRES' : 'AJU';

    // Contar movimientos del día
    const count = await this.movementRepository.count({
      where: {
        fechaMovimiento: today,
        tipo
      }
    });

    const sequence = String(count + 1).padStart(4, '0');
    
    return `${prefix}-${year}${month}${day}-${sequence}`;
  }

  /**
   * Actualiza el stock de un producto
   */
  private async updateProductStock(
    productId: number, 
    cantidad: number, 
    operation: 'add' | 'subtract'
  ): Promise<void> {
    const product = await this.productRepository.findOne({ 
      where: { id: productId } 
    });

    if (!product) {
      throw new Error('Producto no encontrado');
    }

    if (operation === 'add') {
      product.stockDisponible += cantidad;
    } else {
      product.stockDisponible = Math.max(0, product.stockDisponible - cantidad);
    }

    await this.productRepository.save(product);
  }

  /**
   * Procesa la actualización de stock según el tipo de movimiento
   */
  private async processStockUpdate(movement: InventoryMovement): Promise<void> {
    const { tipo, cantidad, productoId } = movement;

    switch (tipo) {
      case MovementType.INPUT:
        await this.updateProductStock(productoId, cantidad, 'add');
        break;
      case MovementType.OUTPUT:
      case MovementType.LOAN:
        await this.updateProductStock(productoId, cantidad, 'subtract');
        break;
      case MovementType.ADJUSTMENT:
        // Los ajustes se manejan de forma especial
        break;
    }
  }

  /**
   * Revierte la actualización de stock
   */
  private async revertStockUpdate(movement: InventoryMovement): Promise<void> {
    const { tipo, cantidad, productoId } = movement;

    switch (tipo) {
      case MovementType.INPUT:
        await this.updateProductStock(productoId, cantidad, 'subtract');
        break;
      case MovementType.OUTPUT:
      case MovementType.LOAN:
        await this.updateProductStock(productoId, cantidad, 'add');
        break;
    }
  }

  /**
   * Genera un voucher para el movimiento
   */
  private async generateVoucher(movement: InventoryMovement, userId: number): Promise<void> {
    const voucherType = this.getVoucherTypeForMovement(movement.tipo);
    
    const voucher = this.voucherRepository.create({
      folio: movement.folio,
      tipo: voucherType,
      titulo: `Vale de ${this.getMovementTypeText(movement.tipo).toLowerCase()}`,
      descripcion: `Vale generado para el movimiento ${movement.folio}`,
      filePath: `/vouchers/${movement.folio}.pdf`,
      fileName: `${movement.folio}.pdf`,
      metadata: {
        movimientoId: movement.id,
        productoId: movement.productoId,
        cantidad: movement.cantidad,
        motivo: movement.motivo,
        observaciones: movement.observaciones
      },
      movimientoId: movement.id,
      generadoPorId: userId,
      createdBy: userId,
      updatedBy: userId
    });

    await this.voucherRepository.save(voucher);
  }

  /**
   * Obtiene el tipo de voucher correspondiente al tipo de movimiento
   */
  private getVoucherTypeForMovement(movementType: MovementType): VoucherType {
    switch (movementType) {
      case MovementType.INPUT:
        return VoucherType.INPUT_VOUCHER;
      case MovementType.OUTPUT:
        return VoucherType.OUTPUT_VOUCHER;
      case MovementType.LOAN:
        return VoucherType.LOAN_VOUCHER;
      case MovementType.RETURN:
        return VoucherType.RETURN_VOUCHER;
      default:
        return VoucherType.INPUT_VOUCHER;
    }
  }

  /**
   * Obtiene los IDs de roles de almacén para notificaciones
   */
  private async getWarehouseRoleIds(): Promise<number[]> {
    const roleIds: number[] = [];
    
    // Obtener ID del rol ROLE_ADMIN
    const adminResult = await this.movementRepository.query(
      'SELECT id FROM roles WHERE name = $1',
      ['ROLE_ADMIN']
    );
    if (adminResult && adminResult[0]) {
      roleIds.push(parseInt(adminResult[0].id, 10));
    }

    // Obtener ID del rol ROLE_ALMACEN_MANAGER
    const almacenManagerResult = await this.movementRepository.query(
      'SELECT id FROM roles WHERE name = $1',
      ['ROLE_ALMACEN_MANAGER']
    );
    if (almacenManagerResult && almacenManagerResult[0]) {
      roleIds.push(parseInt(almacenManagerResult[0].id, 10));
    }

    // Obtener ID del rol ROLE_ALMACEN_USER
    const almacenUserResult = await this.movementRepository.query(
      'SELECT id FROM roles WHERE name = $1',
      ['ROLE_ALMACEN_USER']
    );
    if (almacenUserResult && almacenUserResult[0]) {
      roleIds.push(parseInt(almacenUserResult[0].id, 10));
    }

    return roleIds;
  }

  /**
   * Envía notificación de movimiento de inventario
   */
  private async sendMovementNotification(
    title: string,
    message: string,
    type: NotificationType,
    priority: NotificationPriority,
    metadata?: any
  ): Promise<void> {
    try {
      const roleIds = await this.getWarehouseRoleIds();
      
      if (roleIds.length > 0) {
        await this.notificationService.triggerNotification({
          title,
          message,
          type,
          priority,
          roleId: roleIds,
          metadata
        });
        console.log(`[NOTIF][ALMACEN] Notificación enviada a roles:`, roleIds);
      } else {
        console.warn('[NOTIF][ALMACEN] No se encontraron roles de almacén para notificación');
      }
    } catch (error) {
      console.error('[NOTIF][ALMACEN] Error enviando notificación:', error);
    }
  }

  /**
   * Obtiene el texto legible del tipo de movimiento
   */
  private getMovementTypeText(movementType: MovementType): string {
    switch (movementType) {
      case MovementType.INPUT:
        return 'Entrada';
      case MovementType.OUTPUT:
        return 'Salida';
      case MovementType.LOAN:
        return 'Préstamo';
      case MovementType.RETURN:
        return 'Devolución';
      case MovementType.ADJUSTMENT:
        return 'Ajuste';
      default:
        return 'Desconocido';
    }
  }
}