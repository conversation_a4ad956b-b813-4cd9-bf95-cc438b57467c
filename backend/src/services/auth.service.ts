import { sign, verify, SignOptions, JwtPayload } from 'jsonwebtoken';
import { DataSource, Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { config } from '../config';
import { compare, hash } from 'bcryptjs';
import { TokenPayload } from '../interfaces/request.interface';
import { AppDataSource } from '../data-source';

export class AuthService {
  private userRepository: Repository<User>;

  constructor(dataSource: DataSource) {
    this.userRepository = dataSource.getRepository(User);
  }

  /**
   * Genera un token JWT
   */
  public generateToken(payload: TokenPayload, options: SignOptions = {}): string {
    const secret = config.jwt.secret;
    const expiresIn = options.expiresIn || config.jwt.expiresIn;
    
    return sign(payload, secret, {
      ...options,
      expiresIn,
    } as SignOptions);
  }

  /**
   * Verifica y decodifica un token JWT
   */
  public verifyToken(token: string): TokenPayload | null {
    try {
      const secret = config.jwt.secret;
      const decoded = verify(token, secret) as JwtPayload & TokenPayload;
      
      // Asegurarse de que el token tenga los campos requeridos
      if (!decoded.sub || !decoded.email || !decoded.type) {
        return null;
      }
      
      return {
        sub: decoded.sub,
        email: decoded.email,
        type: decoded.type,
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Valida las credenciales de un usuario
   */
  public async validateUser(credential: string, password: string): Promise<any | null> {
    try {
      console.log('🔍 validateUser called with credential:', credential);
      
      // Primero obtener el usuario básico
      const userQuery = `
        SELECT u.id, u.name, u.email, u.phone, u.status, u.area, u.active, u.password,
               u.created_at, u.updated_at, u.created_by, u.updated_by
        FROM users u
        WHERE u.email = $1 OR u.phone = $1
      `;
      
      const userResult = await this.userRepository.query(userQuery, [credential]);
      
      if (!userResult || userResult.length === 0) {
        console.log('❌ No user found with credential:', credential);
        return null;
      }

      const user = userResult[0];
      console.log('🔍 User found:', {
        id: user.id,
        email: user.email,
        active: user.active,
        status: user.status
      });

      // Verificar que el usuario esté activo
      if (!user.active || user.status !== 'activo') {
        console.log('❌ User is not active:', { active: user.active, status: user.status });
        return null;
      }

      // Verificar la contraseña
      const isPasswordValid = await compare(password, user.password);
      console.log('🔍 Password valid:', isPasswordValid);

      if (!isPasswordValid) {
        console.log('❌ Invalid password for user:', credential);
        return null;
      }

      // Obtener roles del usuario
      const rolesQuery = `
        SELECT r.id, r.name
        FROM user_roles ur
        JOIN roles r ON ur.role_id = r.id
        WHERE ur.user_id = $1
      `;
      
      const rolesResult = await this.userRepository.query(rolesQuery, [user.id]);
      console.log('🔍 User roles found:', rolesResult.length);

      // Obtener permisos de todos los roles del usuario
      const permissionsQuery = `
        SELECT DISTINCT p.id, p.name, p.module, p.action
        FROM user_roles ur
        JOIN roles r ON ur.role_id = r.id
        JOIN role_permissions rp ON r.id = rp.role_id
        JOIN permissions p ON rp.permission_id = p.id
        WHERE ur.user_id = $1
        ORDER BY p.name
      `;
      
      const permissionsResult = await this.userRepository.query(permissionsQuery, [user.id]);
      console.log('🔍 User permissions found:', permissionsResult.length);

      // Construir la estructura de roles con permisos
      const roles = rolesResult.map((role: any) => ({
        id: role.id,
        name: role.name,
        permissions: permissionsResult // Todos los permisos del usuario
      }));

      console.log('✅ User validated successfully');
      console.log('🔍 Final roles structure:', roles.map((r: any) => ({ name: r.name, permissionCount: r.permissions.length })));
      
      return {
        ...user,
        roles: roles
      };
    } catch (error) {
      console.error('❌ Error in validateUser:', error);
      throw error;
    }
  }

  /**
   * Hashea una contraseña
   */
  public async hashPassword(password: string): Promise<string> {
    return hash(password, 10);
  }

  /**
   * Verifica si una contraseña coincide con el hash
   */
  public async comparePasswords(
    plainPassword: string,
    hashedPassword: string
  ): Promise<boolean> {
    return compare(plainPassword, hashedPassword);
  }

  /**
   * Genera tokens de acceso y actualización
   */
  public generateTokens(user: User): { accessToken: string; refreshToken: string } {
    if (!user.email) {
      throw new Error('El usuario no tiene un email para generar el token.');
    }
    const accessToken = this.generateToken(
      {
        sub: user.id,
        email: user.email,
        type: 'access',
      },
      { expiresIn: Number(config.jwt.expiresIn) }
    );

    const refreshToken = this.generateToken(
      {
        sub: user.id,
        email: user.email,
        type: 'refresh',
      },
      { expiresIn: '7d' } // Refresh token expira en 7 días
    );

    return { accessToken, refreshToken };
  }

  /**
   * Actualiza el refresh token en la base de datos
   * TODO: Implementar tabla separada para refresh tokens
   */
  public async updateRefreshToken(userId: number, _refreshToken: string | null): Promise<void> {
    // await this.userRepository.update(userId, { refresh_token: refreshToken || undefined });
    console.log('TODO: Implement refresh token storage for user', userId);
  }

  /**
   * Verifica si un token de actualización es válido
   * TODO: Implementar tabla separada para refresh tokens
   */
  public async validateRefreshToken(userId: number, refreshToken: string): Promise<boolean> {
    // const user = await this.userRepository.findOne({
    //   where: { id: userId, refresh_token: refreshToken },
    //   select: ['id'],
    // });
    console.log('TODO: Implement refresh token validation for user', userId, refreshToken);
    return false;
  }

  /**
   * Obtiene un usuario por su ID
   * WARNING: Este método puede tener problemas con esquema de Supabase si las columnas no existen.
   * Solo se usa en refreshToken. Para /me fue eliminado ya que frontend usa datos del login.
   */
  public async getUserById(id: number): Promise<any | undefined> {
    try {
      // Usar consulta raw para obtener usuario con roles y permisos
      // Removemos DISTINCT para evitar problemas con tipos JSON
      const query = `
        SELECT u.id, u.name, u.email, u.active,
               COALESCE(
                 json_agg(
                   json_build_object(
                     'id', r.id,
                     'name', r.name,
                     'permissions', COALESCE(role_permissions.permissions, '[]'::json)
                   )
                 ) FILTER (WHERE r.id IS NOT NULL),
                 '[]'::json
               ) as roles
        FROM users u
        LEFT JOIN user_roles ur ON u.id = ur.user_id
        LEFT JOIN roles r ON ur.role_id = r.id
        LEFT JOIN (
          SELECT
            r2.id as role_id,
            COALESCE(
              json_agg(
                json_build_object('id', p.id, 'name', p.name, 'module', p.module)
              ) FILTER (WHERE p.id IS NOT NULL),
              '[]'::json
            ) as permissions
          FROM roles r2
          LEFT JOIN role_permissions rp ON r2.id = rp.role_id
          LEFT JOIN permissions p ON rp.permission_id = p.id
          GROUP BY r2.id
        ) role_permissions ON r.id = role_permissions.role_id
        WHERE u.id = $1
        GROUP BY u.id, u.name, u.email, u.active
      `;
      
      const result = await this.userRepository.query(query, [id]);
      
      if (!result || result.length === 0) {
        return undefined;
      }
      
      const user = result[0];
      console.log('✅ getUserById returning user with roles and permissions:', user);
      return user;
    } catch (error) {
      console.error('❌ Error in getUserById:', error);
      return undefined;
    }
  }
}

export const authService = new AuthService(AppDataSource);
