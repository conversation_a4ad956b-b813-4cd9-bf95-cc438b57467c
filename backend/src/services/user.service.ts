import { DataSource, FindOptionsWhere, Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { CreateUserDto, UpdateUserDto, ChangePasswordDto } from '../dtos/user.dto';
import { authService } from './auth.service';
import { BadRequestException, NotFoundException } from '../exceptions/http.exception';
import { compare } from 'bcryptjs';
import { MexicanPhoneConstraint } from '../validators/mexican-phone.validator';
import { PasswordHistoryService } from './password-history.service';
import { PasswordStrengthChecker } from '../validators/password-policy.validator';
import { auditService } from '../modules/systems/services/audit.service';

export class UserService {
  private userRepository: Repository<User>;
  private passwordHistoryService: PasswordHistoryService;

  constructor(dataSource: DataSource) {
    this.userRepository = dataSource.getRepository(User);
    this.passwordHistoryService = new PasswordHistoryService();
  }

  /**
   * Crea un nuevo usuario
   */
  public async createUser(userData: CreateUserDto, creatorUserId?: number): Promise<User> {
    // Verificar si el correo electrónico ya está en uso
    const existingUser = await this.userRepository.findOne({
      where: { email: userData.email }
    });
    if (existingUser) {
      throw new BadRequestException('El correo electrónico ya está en uso');
    }

    // Hashear la contraseña
    const hashedPassword = await authService.hashPassword(userData.password);
    
    // Normalizar teléfono si se proporciona
    const normalizedPhone = userData.phone 
      ? MexicanPhoneConstraint.normalizePhoneForStorage(userData.phone)
      : userData.phone;
    
    // Crear el usuario con auditoría
    const user = this.userRepository.create({
      ...userData,
      password: hashedPassword,
      phone: normalizedPhone,
      status: userData.status || 'activo',
      created_by: creatorUserId,
      updated_by: creatorUserId,
    });
    
    try {
      const savedUser = await this.userRepository.save(user);
      
      // Agregar la primera contraseña al historial
      await this.passwordHistoryService.addToHistory(
        savedUser.id,
        hashedPassword,
        creatorUserId
      );
      
      const { password, refresh_token, ...result } = savedUser;
      return result as User;
    } catch (error: any) {
      // Capturar errores de constraint de base de datos
      if (error.code === '23505' && error.constraint === 'users_email_key') {
        throw new BadRequestException('El correo electrónico ya está en uso');
      }
      throw error;
    }
  }

  /**
   * Obtiene un usuario por su ID
   */
  public async getUserById(id: number): Promise<User | null> {
    return await this.userRepository.findOne({ 
      where: { id },
      select: ['id', 'name', 'email', 'phone', 'area', 'status', 'active', 'created_at', 'updated_at', 'created_by', 'updated_by']
    });
  }

  /**
   * Obtiene un usuario por su correo electrónico (incluye password para autenticación)
   */
  public async getUserByEmail(email: string): Promise<User | null> {
    return await this.userRepository.findOne({ where: { email } });
  }

  /**
   * Obtiene todos los usuarios con paginación y filtro por rol
   */
  public async getUsers(
    page: number = 1,
    limit: number = 10,
    where: FindOptionsWhere<User> | FindOptionsWhere<User>[] = {},
    filters: { role?: string; roleId?: number } = {}
  ): Promise<{ data: User[]; count: number }> {
    // Si no hay filtro de rol, usar findAndCount normal
    if (!filters.role && !filters.roleId) {
      const [users, count] = await this.userRepository.findAndCount({
        where,
        select: ['id', 'name', 'email', 'phone', 'area', 'status', 'active', 'created_at', 'updated_at', 'created_by', 'updated_by'],
        skip: (page - 1) * limit,
        take: limit,
        order: { created_at: 'DESC' },
      });
      let usersWithRoles = users;
      let countWithRoles = count;

      // --- AGREGAR ROLES A CADA USUARIO ---
      for (const user of usersWithRoles) {
        const rolesQuery = `
          SELECT r.id, r.name
          FROM user_roles ur
          JOIN roles r ON ur.role_id = r.id
          WHERE ur.user_id = $1
          ORDER BY r.name
        `;
        const rolesResult = await this.userRepository.query(rolesQuery, [user.id]);
        user.roles = Array.isArray(rolesResult) ? rolesResult : [];
      }

      return { data: usersWithRoles, count: countWithRoles };
    }

    // Si hay filtro de rol, usar query SQL directo
    const params: any[] = [];
    let sql = `SELECT u.id, u.name, u.email, u.phone, u.area, u.status, u.active, u.created_at, u.updated_at, u.created_by, u.updated_by
      FROM users u
      INNER JOIN user_roles ur ON u.id = ur.user_id
      INNER JOIN roles r ON ur.role_id = r.id
      WHERE 1=1`;
    
    // Filtro por rol (nombre o id)
    if (filters.roleId) {
      sql += ` AND r.id = $${params.length + 1}`;
      params.push(filters.roleId);
    } else if (filters.role) {
      sql += ` AND r.name = $${params.length + 1}`;
      params.push(filters.role);
    }
    // Otros filtros (solo los más comunes)
    if ((where as any).area) {
      sql += ` AND u.area = $${params.length + 1}`;
      params.push((where as any).area);
    }
    if ((where as any).status) {
      sql += ` AND u.status = $${params.length + 1}`;
      params.push((where as any).status);
    }
    if ((where as any).active !== undefined) {
      sql += ` AND u.active = $${params.length + 1}`;
      params.push((where as any).active);
    }
    // Paginación
    sql += ` ORDER BY u.created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
    params.push(limit, (page - 1) * limit);
    const users = await this.userRepository.query(sql, params);
    // Total
    let countSql = `SELECT COUNT(DISTINCT u.id) FROM users u INNER JOIN user_roles ur ON u.id = ur.user_id INNER JOIN roles r ON ur.role_id = r.id WHERE 1=1`;
    const countParams: any[] = [];
    if (filters.roleId) {
      countSql += ` AND r.id = $${countParams.length + 1}`;
      countParams.push(filters.roleId);
    } else if (filters.role) {
      countSql += ` AND r.name = $${countParams.length + 1}`;
      countParams.push(filters.role);
    }
    if ((where as any).area) {
      countSql += ` AND u.area = $${countParams.length + 1}`;
      countParams.push((where as any).area);
    }
    if ((where as any).status) {
      countSql += ` AND u.status = $${countParams.length + 1}`;
      countParams.push((where as any).status);
    }
    if ((where as any).active !== undefined) {
      countSql += ` AND u.active = $${countParams.length + 1}`;
      countParams.push((where as any).active);
    }
    const countResult = await this.userRepository.query(countSql, countParams);
    const count = parseInt(countResult[0]?.count || '0', 10);

    // --- AGREGAR ROLES A CADA USUARIO ---
    for (const user of users) {
      const rolesQuery = `
        SELECT r.id, r.name
        FROM user_roles ur
        JOIN roles r ON ur.role_id = r.id
        WHERE ur.user_id = $1
        ORDER BY r.name
      `;
      const rolesResult = await this.userRepository.query(rolesQuery, [user.id]);
      user.roles = Array.isArray(rolesResult) ? rolesResult : [];
    }

    return { data: users, count };
  }

  /**
   * Actualiza un usuario existente con auditoría automática
   */
  public async updateUser(
    id: number,
    userData: UpdateUserDto,
    updaterUserId?: number
  ): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException('Usuario no encontrado');
    }

    // Verificar si el correo electrónico ya está en uso por otro usuario
    if (userData.email && userData.email !== user.email) {
      const existingUser = await this.userRepository.findOne({
        where: { email: userData.email }
      });
      if (existingUser && existingUser.id !== id) {
        throw new BadRequestException('El correo electrónico ya está en uso');
      }
    }

    // Actualizar campos
    if (userData.name !== undefined) user.name = userData.name;
    if (userData.email !== undefined) user.email = userData.email;
    if (userData.phone !== undefined) {
      user.phone = userData.phone 
        ? MexicanPhoneConstraint.normalizePhoneForStorage(userData.phone)
        : userData.phone;
    }
    if (userData.area !== undefined) user.area = userData.area;
    if (userData.status !== undefined) user.status = userData.status;
    
    // Actualizar contraseña si se proporciona
    if (userData.password) {
      // Guardar la contraseña actual en el historial antes de cambiarla
      await this.passwordHistoryService.addToHistory(
        user.id,
        user.password,
        updaterUserId
      );
      
      user.password = await authService.hashPassword(userData.password);
    }

    // Actualizar roles si se reciben roleIds
    if (userData.roleIds && Array.isArray(userData.roleIds)) {
      console.log('[UPDATE USER][ROLES] Recibido roleIds:', userData.roleIds);
      // Validar que los roles existan
      const roleCheckQuery = `SELECT id FROM roles WHERE id = ANY($1)`;
      const existingRoles = await this.userRepository.query(roleCheckQuery, [userData.roleIds]);
      console.log('[UPDATE USER][ROLES] Roles existentes encontrados:', existingRoles.map((r: any) => r.id));
      if (existingRoles.length !== userData.roleIds.length) {
        console.error('[UPDATE USER][ROLES] Error: Uno o más roles no existen. Recibidos:', userData.roleIds, 'Existentes:', existingRoles.map((r: any) => r.id));
        throw new BadRequestException('Uno o más roles no existen');
      }
      // Eliminar roles actuales
      console.log('[UPDATE USER][ROLES] Eliminando roles actuales de user_roles para user_id:', user.id);
      await this.userRepository.query('DELETE FROM user_roles WHERE user_id = $1', [user.id]);
      console.log('[UPDATE USER][ROLES] Roles eliminados. Insertando nuevos roles...');
      // Insertar nuevos roles
      for (const roleId of userData.roleIds) {
        console.log('[UPDATE USER][ROLES] Insertando user_id:', user.id, 'role_id:', roleId);
        await this.userRepository.query(
          'INSERT INTO user_roles (user_id, role_id) VALUES ($1, $2)',
          [user.id, roleId]
        );
      }
      console.log('[UPDATE USER][ROLES] Todos los roles insertados para user_id:', user.id, 'roleIds:', userData.roleIds);
    }

    // Auditoría automática
    user.updated_by = updaterUserId;

    // --- LOGS DE DEPURACIÓN ---
    console.log('[UPDATE USER] Antes de guardar:', JSON.stringify(user));
    try {
      await this.userRepository.save(user);
      console.log('[UPDATE USER] Después de guardar:', JSON.stringify(user));
      const { password, refresh_token, ...result } = user;
      return result as User;
    } catch (error: any) {
      // Capturar errores de constraint de base de datos
      if (error.code === '23505' && error.constraint === 'users_email_key') {
        throw new BadRequestException('El correo electrónico ya está en uso');
      }
      throw error;
    }
  }

  /**
   * Cambia la contraseña de un usuario con validaciones de seguridad avanzadas
   */
  public async changePassword(
    userId: number,
    passwordData: ChangePasswordDto,
    updaterUserId?: number,
    ipAddress?: string,
    userAgent?: string
  ): Promise<{
    success: boolean;
    strengthAnalysis: {
      score: number;
      level: string;
      suggestions: string[];
    };
  }> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('Usuario no encontrado');
    }

    // Verificar contraseña actual
    const isCurrentPasswordValid = await compare(passwordData.currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new BadRequestException('La contraseña actual es incorrecta');
    }

    // Verificar que las nuevas contraseñas coincidan
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      throw new BadRequestException('Las contraseñas nuevas no coinciden');
    }

    // Verificar que la nueva contraseña sea diferente de la actual
    const isSamePassword = await compare(passwordData.newPassword, user.password);
    if (isSamePassword) {
      throw new BadRequestException('La nueva contraseña debe ser diferente de la actual');
    }

    // Verificar historial de contraseñas (no permitir reutilización de las últimas 5)
    const isPasswordReused = await this.passwordHistoryService.isPasswordReused(
      userId,
      passwordData.newPassword,
      5
    );
    if (isPasswordReused) {
      throw new BadRequestException(
        'No puedes reutilizar una de tus últimas 5 contraseñas. Por favor, elige una contraseña diferente.'
      );
    }

    // Analizar la fuerza de la nueva contraseña
    const strengthAnalysis = PasswordStrengthChecker.calculateStrength(passwordData.newPassword);

    // Guardar la contraseña actual en el historial antes de cambiarla
    await this.passwordHistoryService.addToHistory(
      userId,
      user.password, // Contraseña actual que va al historial
      updaterUserId,
      ipAddress,
      userAgent
    );

    // Actualizar contraseña
    const newHashedPassword = await authService.hashPassword(passwordData.newPassword);
    user.password = newHashedPassword;
    user.updated_by = updaterUserId;

    await this.userRepository.save(user);

    // Registrar auditoría de cambio de contraseña
    await auditService.log({
      userId: updaterUserId || userId,
      action: 'change_password',
      targetEntity: 'users',
      targetId: userId,
      details: {
        strengthScore: strengthAnalysis.score,
        strengthLevel: strengthAnalysis.level,
        changedBy: updaterUserId ? 'admin' : 'self',
        userAgent,
      },
      ipAddress,
    });

    return {
      success: true,
      strengthAnalysis,
    };
  }

  /**
   * Bloquea o desbloquea un usuario
   */
  public async blockUnblockUser(
    id: number,
    block: boolean,
    updaterUserId?: number
  ): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException('Usuario no encontrado');
    }

    // Prevenir auto-bloqueo
    if (updaterUserId === id && block) {
      throw new BadRequestException('No puedes bloquear tu propia cuenta');
    }

    user.status = block ? 'bloqueado' : 'activo';
    user.updated_by = updaterUserId;
    
    await this.userRepository.save(user);
    const { password, refresh_token, ...result } = user;
    return result as User;
  }

  /**
   * Elimina un usuario
   */
  public async deleteUser(id: number, currentUser: User): Promise<void> {
    // Evitar que un usuario se elimine a sí mismo
    if (currentUser.id === id) {
      throw new BadRequestException('No puedes eliminar tu propia cuenta');
    }
    
    const result = await this.userRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException('Usuario no encontrado');
    }
  }

  /**
   * Actualiza el token de actualización de un usuario
   */
  public async updateUserRefreshToken(userId: number, refreshToken: string | null): Promise<void> {
    await this.userRepository.update(userId, { refresh_token: refreshToken || undefined });
  }

  /**
   * Verifica si un usuario está activo y no bloqueado
   */
  public async isUserActive(userId: number): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ['status', 'active']
    });
    
    return user ? user.active && user.status === 'activo' : false;
  }
}
