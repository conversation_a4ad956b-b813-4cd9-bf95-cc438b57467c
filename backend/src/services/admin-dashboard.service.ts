import { query } from './db.service';
import { HttpException } from '../exceptions/http.exception';
import { GetAdminDashboardGraphDto } from '../dtos/admin.dto';
// import * as ExcelJS from 'exceljs'; // Descomentar para la generación real de Excel

export class AdminDashboardService {

  constructor() {
    // this.excelGenerationService = new ExcelGenerationService();
  }

  public async getAggregatedData(
    params: GetAdminDashboardGraphDto
  ): Promise<any[]> {
    const { startDate, endDate, groupBy, salespersonId, customerId } = params;
    const queryParams: any[] = [];
    let paramIndex = 1;

    // Base CTEs for quotations and invoices
    let quotationsBaseCte = `
        SELECT
            q.created_by as salesperson_id_q,
            q.customer_id as customer_id_q,
            TO_CHAR(q.created_at, 'YYYY-MM-DD') as date_q,
            SUM(qi.quantity * qi.unit_price) as total_cotizado_val
        FROM quotations q
        JOIN quotation_items qi ON q.id = qi.quotation_id
        WHERE 1=1
    `;
    let invoicesBaseCte = `
        SELECT
            q_inv.created_by as salesperson_id_i,
            i.customer_id as customer_id_i,
            TO_CHAR(i.created_at, 'YYYY-MM-DD') as date_i,
            SUM(i.total_amount) as total_facturado_val,
            SUM(CASE WHEN i.status = 'PAID' THEN i.total_amount ELSE 0 END) as total_pagado_val
        FROM invoices i
        LEFT JOIN quotations q_inv ON i.quotation_id = q_inv.id
        WHERE 1=1
    `;

    // Date filters
    if (startDate) {
        quotationsBaseCte += ` AND q.created_at >= $${paramIndex}`;
        invoicesBaseCte += ` AND i.created_at >= $${paramIndex}`;
        queryParams.push(startDate);
        paramIndex++;
    }
    if (endDate) {
        quotationsBaseCte += ` AND q.created_at <= $${paramIndex}`;
        invoicesBaseCte += ` AND i.created_at <= $${paramIndex}`;
        queryParams.push(endDate);
        paramIndex++;
    }

    // Salesperson filter
    if (salespersonId) {
        quotationsBaseCte += ` AND q.created_by = $${paramIndex}`;
        invoicesBaseCte += ` AND q_inv.created_by = $${paramIndex}`;
        queryParams.push(salespersonId);
        paramIndex++;
    }
    // Customer filter
    if (customerId) {
        quotationsBaseCte += ` AND q.customer_id = $${paramIndex}`;
        invoicesBaseCte += ` AND i.customer_id = $${paramIndex}`;
        queryParams.push(customerId);
        paramIndex++;
    }

    quotationsBaseCte += ` GROUP BY q.created_by, q.customer_id, TO_CHAR(q.created_at, 'YYYY-MM-DD')`;
    invoicesBaseCte += ` GROUP BY q_inv.created_by, i.customer_id, TO_CHAR(i.created_at, 'YYYY-MM-DD')`;

    let finalSelect = "";
    let finalGroupBy = "";
    let groupingKeySelect = "";
    let groupingKeyJoin = "";

    if (groupBy === 'salesperson') {
        groupingKeySelect = "COALESCE(q_data.salesperson_id_q, i_data.salesperson_id_i) as group_id, u.name as group_name";
        groupingKeyJoin = "COALESCE(q_data.salesperson_id_q, i_data.salesperson_id_i) = u.id";
        finalGroupBy = "group_id, u.name";
    } else if (groupBy === 'customer') {
        groupingKeySelect = "COALESCE(q_data.customer_id_q, i_data.customer_id_i) as group_id, c.razon_social as group_name";
        groupingKeyJoin = "COALESCE(q_data.customer_id_q, i_data.customer_id_i) = c.id";
        finalGroupBy = "group_id, c.razon_social";
    } else if (groupBy === 'month') {
        groupingKeySelect = "TO_CHAR(COALESCE(q_data.date_q, i_data.date_i)::date, 'YYYY-MM') as group_name";
        finalGroupBy = "group_name";
    } else if (groupBy === 'year') {
        groupingKeySelect = "TO_CHAR(COALESCE(q_data.date_q, i_data.date_i)::date, 'YYYY') as group_name";
        finalGroupBy = "group_name";
    } else if (groupBy === 'day') { // Default or explicit 'day'
        groupingKeySelect = "COALESCE(q_data.date_q, i_data.date_i) as group_name"; // Date is already YYYY-MM-DD
        finalGroupBy = "group_name";
    } else { // Totales generales
        finalSelect = `
            SUM(COALESCE(q_data.total_cotizado_val, 0)) as total_cotizado,
            SUM(COALESCE(i_data.total_facturado_val, 0)) as total_facturado,
            SUM(COALESCE(i_data.total_pagado_val, 0)) as total_pagado
        `;
         // No GROUP BY clause for grand totals
    }

    if (groupBy) {
        finalSelect = `
            ${groupingKeySelect},
            SUM(COALESCE(q_data.total_cotizado_val, 0)) as total_cotizado,
            SUM(COALESCE(i_data.total_facturado_val, 0)) as total_facturado,
            SUM(COALESCE(i_data.total_pagado_val, 0)) as total_pagado
        `;
    }

    let sql = `
        WITH
        q_data AS (${quotationsBaseCte}),
        i_data AS (${invoicesBaseCte})
        SELECT ${finalSelect}
        FROM q_data
        FULL OUTER JOIN i_data ON
            ${(groupBy === 'salesperson') ? 'q_data.salesperson_id_q = i_data.salesperson_id_i' :
              (groupBy === 'customer') ? 'q_data.customer_id_q = i_data.customer_id_i' :
              (groupBy === 'month' || groupBy === 'year' || groupBy === 'day') ? `TO_CHAR(q_data.date_q::date, '${
                  groupBy === 'month' ? 'YYYY-MM' : groupBy === 'year' ? 'YYYY' : 'YYYY-MM-DD'
              }') = TO_CHAR(i_data.date_i::date, '${
                  groupBy === 'month' ? 'YYYY-MM' : groupBy === 'year' ? 'YYYY' : 'YYYY-MM-DD'
              }')` :
              // Para totales generales, el JOIN puede ser complejo o no necesario si se suman totales de CTEs separadas
              // Esta condición de join para totales es un placeholder y podría ser 1=1 si las CTEs ya son totales.
              // Sin embargo, la estructura actual de q_data e i_data no son totales generales aún.
              // Para un total general, es mejor sumar los resultados de una consulta agrupada o modificar las CTEs.
              // Simplificando: si no hay groupBy, la consulta actual de totales generales es la correcta.
              (groupBy ? '1=1' : '1=1') // Placeholder, el join se refina con las condiciones de grupo
            }
            ${(groupBy === 'salesperson' && groupingKeyJoin) ? `LEFT JOIN users u ON ${groupingKeyJoin}` : ''}
            ${(groupBy === 'customer' && groupingKeyJoin) ? `LEFT JOIN customers c ON ${groupingKeyJoin}` : ''}
    `;

    if (groupBy) {
        sql += ` GROUP BY ${finalGroupBy} ORDER BY group_name`;
    }
    // Si no hay groupBy, las CTEs y el select ya calculan los totales generales.
    // La consulta de totales generales sin groupBy es más simple y se puede hacer directamente:
    if (!groupBy) {
        sql = `
            SELECT
                (SELECT SUM(total_cotizado_val) FROM (${quotationsBaseCte}) AS q_total) AS total_cotizado,
                (SELECT SUM(total_facturado_val) FROM (${invoicesBaseCte}) AS i_fact_total) AS total_facturado,
                (SELECT SUM(total_pagado_val) FROM (${invoicesBaseCte}) AS i_pag_total) AS total_pagado
        `;
    }


    try {
      const result = await query(sql, queryParams);
      return result.rows;
    } catch (error) {
      console.error('Error in getAggregatedData:', error);
      console.error('SQL:', sql);
      console.error('Params:', queryParams);
      if (error instanceof Error) {
        throw new HttpException(500, `Error al obtener datos agregados: ${error.message}`);
      }
      throw new HttpException(500, 'Error al obtener datos agregados: error desconocido');
    }
  }

  public async generateAggregatedDataExcel(params: GetAdminDashboardGraphDto): Promise<Buffer> {
    const data = await this.getAggregatedData(params);

    if (data.length === 0) {
      // Devolver un buffer vacío o un Excel con solo encabezados si se prefiere
      // throw new HttpException(404, "No hay datos para generar el Excel con los filtros seleccionados.");
      console.warn("No hay datos para generar el Excel con los filtros seleccionados.");
    }

    // Simulación: devolver un CSV
    let csvData = "";
    if (data.length > 0) {
        const headers = Object.keys(data[0]).map(header => `"${header.replace(/"/g, '""')}"`).join(',');
        csvData += headers + "\n";
        data.forEach(row => {
        csvData += Object.values(row).map(value => {
            if (value === null || value === undefined) return "";
            const strValue = String(value);
            return `"${strValue.replace(/"/g, '""')}"`;
        }).join(',') + "\n";
        });
    } else {
        // Encabezados por defecto si no hay datos, basado en los campos esperados
        let defaultHeaders = ['group_name', 'total_cotizado', 'total_facturado', 'total_pagado'];
        if (params.groupBy === 'salesperson') defaultHeaders[0] = 'salesperson_name';
        if (params.groupBy === 'customer') defaultHeaders[0] = 'customer_name';
        if (!params.groupBy) defaultHeaders = ['total_cotizado', 'total_facturado', 'total_pagado'];
        csvData += defaultHeaders.map(h => `"${h}"`).join(',') + "\n";
    }

    // console.log("[INFO] Simulación de generación de Excel (CSV):", csvData);
    return Buffer.from(csvData, 'utf-8');
  }
}
