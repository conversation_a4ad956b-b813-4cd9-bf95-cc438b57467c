import { Server as HTTPServer } from 'http';
import { Server as SocketIOServer, Socket } from 'socket.io';
import { verify, JwtPayload } from 'jsonwebtoken';
import { AppDataSource } from '../data-source';
import { NotificationService } from './notification.service';
import { NotificationType, NotificationPriority } from '../entities/notification.entity';
import { User } from '../entities/user.entity';
import { config } from '../config/config';

export interface AuthenticatedSocket extends Socket {
  userId?: number;
  userRoles?: string[];
}

export interface NotificationAlert {
  id: number;
  title: string;
  message: string;
  type: NotificationType;
  priority: NotificationPriority;
  action_url?: string;
  action_label?: string;
  created_at: Date;
}

export class WebSocketService {
  private io: SocketIOServer;
  private notificationService: NotificationService;
  private connectedUsers: Map<number, Set<string>> = new Map(); // userId -> Set<socketId>
  private userSockets: Map<string, AuthenticatedSocket> = new Map(); // socketId -> socket
  private typeSubscriptions: Map<string, Set<string>> = new Map(); // notificationType -> Set<socketId>
  private prioritySubscriptions: Map<string, Set<string>> = new Map(); // priority -> Set<socketId>

  constructor(httpServer: HTTPServer, notificationService: NotificationService) {
    this.io = new SocketIOServer(httpServer, {
      cors: {
        origin: config.server.env === 'development' 
          ? ["http://localhost:3001", "http://localhost:3000"]  
          : process.env.CORS_WHITELIST?.split(',') || [],
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    this.notificationService = notificationService;
    this.setupSocketHandlers();
    this.setupCleanupScheduler();
  }

  private setupSocketHandlers(): void {
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      console.log(`Nueva conexión WebSocket: ${socket.id}`);

      // Manejar autenticación
      socket.on('authenticate', async (data: { token: string; userId: string }) => {
        try {
          const { token, userId } = data;
          
          // Validar token JWT
          if (!token) {
            throw new Error('Token no proporcionado');
          }

          let decoded: JwtPayload;
          try {
            const payload = verify(token, config.jwt.secret);
            if (typeof payload === 'string' || !payload.sub || payload.type !== 'access') {
              throw new Error('Token inválido o de tipo incorrecto');
            }
            decoded = payload;
          } catch (jwtError: any) {
            if (jwtError.name === 'TokenExpiredError') {
              throw new Error('Token expirado');
            }
            throw new Error('Token inválido');
          }

          // Obtener el usuario de la base de datos
          const userIdFromToken = Number(decoded.sub);
          const userIdFromClient = Number(userId);
          
          // Verificar que el userId del token coincida con el enviado
          if (userIdFromToken !== userIdFromClient) {
            throw new Error('Token no corresponde al usuario');
          }

          const userRepository = AppDataSource.getRepository(User);
          const user = await userRepository.findOne({
            where: { id: userIdFromToken, active: true },
            relations: ['roles']
          });

          if (!user) {
            throw new Error('Usuario no encontrado o inactivo');
          }

          if (user.status === 'bloqueado') {
            throw new Error('Usuario bloqueado');
          }

          // Asociar socket con usuario
          socket.userId = userIdFromToken;
          socket.userRoles = user.roles ? user.roles.map(role => role.name) : [];
          this.userSockets.set(socket.id, socket);
          
          // Agregar a mapeo de usuarios conectados
          if (!this.connectedUsers.has(userIdFromToken)) {
            this.connectedUsers.set(userIdFromToken, new Set());
          }
          this.connectedUsers.get(userIdFromToken)!.add(socket.id);

          // Unir a rooms específicos
          await socket.join(`user:${userIdFromToken}`);
          
          // Unir a rooms de roles
          if (socket.userRoles) {
            for (const role of user.roles || []) {
              await socket.join(`role:${role.id}`);
            }
          }
          
          // Solo alertar que hay notificaciones pendientes (no enviarlas todas)
          await this.alertPendingNotifications(socket, userIdFromToken);
          
          socket.emit('authenticated', { 
            success: true, 
            userId: userIdFromToken,
            roles: socket.userRoles 
          });
          
          console.log(`✅ Usuario ${userIdFromToken} (${user.email}) autenticado en socket ${socket.id}`);
          
        } catch (error) {
          console.error('❌ Error en autenticación WebSocket:', error);
          socket.emit('authentication_error', { 
            message: error instanceof Error ? error.message : 'Error de autenticación' 
          });
          socket.disconnect();
        }
      });

      // Manejar suscripción a roles
      socket.on('subscribe_to_role', (data: { roleId: number }) => {
        if (socket.userId) {
          socket.join(`role:${data.roleId}`);
          socket.emit('subscription_confirmed', { 
            type: 'role', 
            target: data.roleId,
            message: `Suscrito al rol ${data.roleId}` 
          });
          console.log(`✅ Usuario ${socket.userId} suscrito al rol ${data.roleId}`);
        }
      });

      // Manejar suscripción por tipo de notificación
      socket.on('subscribe_to_type', (data: { notificationType: NotificationType }) => {
        if (socket.userId) {
          const typeKey = data.notificationType;
          if (!this.typeSubscriptions.has(typeKey)) {
            this.typeSubscriptions.set(typeKey, new Set());
          }
          this.typeSubscriptions.get(typeKey)!.add(socket.id);
          
          socket.emit('subscription_confirmed', { 
            type: 'notification_type', 
            target: typeKey,
            message: `Suscrito a notificaciones de tipo ${typeKey}` 
          });
          console.log(`✅ Usuario ${socket.userId} suscrito a notificaciones tipo ${typeKey}`);
        }
      });

      // Manejar suscripción por prioridad
      socket.on('subscribe_to_priority', (data: { priority: NotificationPriority }) => {
        if (socket.userId) {
          const priorityKey = data.priority;
          if (!this.prioritySubscriptions.has(priorityKey)) {
            this.prioritySubscriptions.set(priorityKey, new Set());
          }
          this.prioritySubscriptions.get(priorityKey)!.add(socket.id);
          
          socket.emit('subscription_confirmed', { 
            type: 'priority', 
            target: priorityKey,
            message: `Suscrito a notificaciones de prioridad ${priorityKey}` 
          });
          console.log(`✅ Usuario ${socket.userId} suscrito a notificaciones prioridad ${priorityKey}`);
        }
      });

      // Manejar desuscripción de tipo
      socket.on('unsubscribe_from_type', (data: { notificationType: NotificationType }) => {
        if (socket.userId) {
          const typeKey = data.notificationType;
          const typeSubscribers = this.typeSubscriptions.get(typeKey);
          if (typeSubscribers) {
            typeSubscribers.delete(socket.id);
            if (typeSubscribers.size === 0) {
              this.typeSubscriptions.delete(typeKey);
            }
          }
          
          socket.emit('unsubscription_confirmed', { 
            type: 'notification_type', 
            target: typeKey,
            message: `Desuscrito de notificaciones tipo ${typeKey}` 
          });
          console.log(`❌ Usuario ${socket.userId} desuscrito de notificaciones tipo ${typeKey}`);
        }
      });

      // Manejar desuscripción de prioridad
      socket.on('unsubscribe_from_priority', (data: { priority: NotificationPriority }) => {
        if (socket.userId) {
          const priorityKey = data.priority;
          const prioritySubscribers = this.prioritySubscriptions.get(priorityKey);
          if (prioritySubscribers) {
            prioritySubscribers.delete(socket.id);
            if (prioritySubscribers.size === 0) {
              this.prioritySubscriptions.delete(priorityKey);
            }
          }
          
          socket.emit('unsubscription_confirmed', { 
            type: 'priority', 
            target: priorityKey,
            message: `Desuscrito de notificaciones prioridad ${priorityKey}` 
          });
          console.log(`❌ Usuario ${socket.userId} desuscrito de notificaciones prioridad ${priorityKey}`);
        }
      });

      // Obtener lista de suscripciones del usuario
      socket.on('get_subscriptions', () => {
        if (socket.userId) {
          const userSubscriptions = {
            userId: socket.userId,
            roles: socket.userRoles || [],
            types: this.getUserTypeSubscriptions(socket.id),
            priorities: this.getUserPrioritySubscriptions(socket.id)
          };
          
          socket.emit('current_subscriptions', userSubscriptions);
          console.log(`📋 Enviando suscripciones a usuario ${socket.userId}`);
        }
      });

      // Manejar marca de notificación como leída desde el cliente
      socket.on('mark_notification_read', async (notificationId: number) => {
        if (socket.userId) {
          try {
            const result = await this.notificationService.markAsRead(notificationId, socket.userId);
            if (result) {
              // Notificar a otros dispositivos del mismo usuario que se leyó
              this.notifyNotificationRead(socket.userId, notificationId);
              // Enviar confirmación SOLO a este socket para feedback inmediato
              socket.emit('notification_read', notificationId);
              // Enviar estadísticas actualizadas
              await this.sendUpdatedStats(socket.userId);
            } // Si no, no hacer nada (no existe o no es del usuario)
          } catch (error) {
            console.error('Error marcando notificación como leída:', error);
          }
        }
      });

      // Manejar desconexión
      socket.on('disconnect', () => {
        this.handleDisconnection(socket);
      });

      // Manejar errores
      socket.on('error', (error) => {
        console.error(`Error en socket ${socket.id}:`, error);
      });
    });
  }

  private async alertPendingNotifications(socket: AuthenticatedSocket, userId: number): Promise<void> {
    try {
      const unreadCount = await this.getUnreadNotificationCount(userId);
      
      if (unreadCount > 0) {
        socket.emit('pending_notifications_alert', {
          count: unreadCount,
          message: `Tienes ${unreadCount} notificación${unreadCount > 1 ? 'es' : ''} sin leer`
        });
      }
    } catch (error) {
      console.error('Error enviando alerta de notificaciones pendientes:', error);
    }
  }

  private async getUnreadNotificationCount(userId: number): Promise<number> {
    try {
      const stats = await this.notificationService.getNotificationStats(userId);
      return stats.unread;
    } catch (error) {
      console.error('Error obteniendo contador de notificaciones:', error);
      return 0;
    }
  }

  private handleDisconnection(socket: AuthenticatedSocket): void {
    console.log(`Socket desconectado: ${socket.id}`);
    
    if (socket.userId) {
      const userConnections = this.connectedUsers.get(socket.userId);
      if (userConnections) {
        userConnections.delete(socket.id);
        if (userConnections.size === 0) {
          this.connectedUsers.delete(socket.userId);
        }
      }
    }
    
    // Limpiar suscripciones por tipo
    this.typeSubscriptions.forEach((subscribers, type) => {
      subscribers.delete(socket.id);
      if (subscribers.size === 0) {
        this.typeSubscriptions.delete(type);
      }
    });
    
    // Limpiar suscripciones por prioridad  
    this.prioritySubscriptions.forEach((subscribers, priority) => {
      subscribers.delete(socket.id);
      if (subscribers.size === 0) {
        this.prioritySubscriptions.delete(priority);
      }
    });
    
    this.userSockets.delete(socket.id);
  }

  private getUserTypeSubscriptions(socketId: string): NotificationType[] {
    const types: NotificationType[] = [];
    this.typeSubscriptions.forEach((subscribers, type) => {
      if (subscribers.has(socketId)) {
        types.push(type as NotificationType);
      }
    });
    return types;
  }

  private getUserPrioritySubscriptions(socketId: string): NotificationPriority[] {
    const priorities: NotificationPriority[] = [];
    this.prioritySubscriptions.forEach((subscribers, priority) => {
      if (subscribers.has(socketId)) {
        priorities.push(priority as NotificationPriority);
      }
    });
    return priorities;
  }

  private formatNotificationAlert(notification: any): NotificationAlert {
    return {
      id: notification.id,
      title: notification.title,
      message: notification.message,
      type: notification.type,
      priority: notification.priority,
      action_url: notification.action_url,
      action_label: notification.action_label,
      created_at: notification.created_at
    };
  }

  /**
   * Configurar limpieza automática semanal de notificaciones antiguas
   */
  private setupCleanupScheduler(): void {
    // Habilitado: limpieza automática semanal de notificaciones leídas > 7 días
    const WEEK_IN_MS = 7 * 24 * 60 * 60 * 1000;
    setInterval(async () => {
      try {
        console.log('🧹 Iniciando limpieza automática de notificaciones antiguas...');
        const deletedCount = await this.cleanupOldNotifications();
        console.log(`✅ Limpieza completada: ${deletedCount} notificaciones eliminadas`);
      } catch (error) {
        console.error('❌ Error en limpieza automática de notificaciones:', error);
      }
    }, WEEK_IN_MS);
    // Ejecutar limpieza inicial al iniciar el servidor
    setTimeout(async () => {
      try {
        const deletedCount = await this.cleanupOldNotifications();
        console.log(`🧹 Limpieza inicial: ${deletedCount} notificaciones antiguas eliminadas`);
      } catch (error) {
        console.error('Error en limpieza inicial:', error);
      }
    }, 30000); // 30 segundos después del inicio
  }

  /**
   * Eliminar notificaciones más antiguas de una semana que ya fueron leídas
   */
  private async cleanupOldNotifications(): Promise<number> {
    try {
      return await this.notificationService.cleanupOldReadNotifications();
    } catch (error) {
      console.error('Error eliminando notificaciones antiguas:', error);
      return 0;
    }
  }

  // Métodos públicos para alertar sobre nuevas notificaciones (estas SE GUARDAN EN BD)

  /**
   * Alertar a un usuario específico de una nueva notificación
   * La notificación ya debe estar guardada en BD
   */
  async alertNewNotificationToUser(userId: number, notification: NotificationAlert): Promise<boolean> {
    try {
      const room = `user:${userId}`;
      this.io.to(room).emit('new_notification_alert', {
        notification: this.formatNotificationAlert(notification),
        timestamp: new Date(),
        message: `Nueva notificación: ${notification.title}`
      });
      
      // También enviar estadísticas actualizadas
      await this.sendUpdatedStats(userId);
      
      console.log(`Alerta de nueva notificación enviada al usuario ${userId}:`, notification.title);
      return true;
    } catch (error) {
      console.error(`Error enviando alerta a usuario ${userId}:`, error);
      return false;
    }
  }

  /**
   * Alertar a todos los usuarios de un rol específico
   */
  async alertNewNotificationToRole(roleId: number, notification: NotificationAlert): Promise<boolean> {
    try {
      const room = `role:${roleId}`;
      // Emitir la notificación como objeto plano, no anidado
      this.io.to(room).emit('new_notification_alert', {
        ...this.formatNotificationAlert(notification),
        timestamp: new Date(),
        message: `Nueva notificación para tu rol: ${notification.title}`
      });
      
      console.log(`Alerta de nueva notificación enviada al rol ${roleId}:`, notification.title);
      return true;
    } catch (error) {
      console.error(`Error enviando alerta a rol ${roleId}:`, error);
      return false;
    }
  }

  /**
   * Alertar broadcast a todos los usuarios conectados
   */
  async alertBroadcastNotification(notification: NotificationAlert): Promise<boolean> {
    try {
      this.io.emit('new_notification_alert', {
        notification: this.formatNotificationAlert(notification),
        timestamp: new Date(),
        message: `Notificación general: ${notification.title}`
      });
      
      console.log('Alerta de notificación broadcast enviada:', notification.title);
      return true;
    } catch (error) {
      console.error('Error enviando alerta broadcast:', error);
      return false;
    }
  }

  /**
   * Notificar cuando una notificación es marcada como leída
   */
  async notifyNotificationRead(userId: number, notificationId: number): Promise<void> {
    try {
      const room = `user:${userId}`;
      this.io.to(room).emit('notification_read', {
        notificationId,
        timestamp: new Date()
      });
    } catch (error) {
      console.error('Error notificando lectura de notificación:', error);
    }
  }

  /**
   * Alertar a suscriptores por tipo de notificación
   */
  async alertNotificationByType(type: NotificationType, notification: NotificationAlert): Promise<boolean> {
    try {
      const subscribers = this.typeSubscriptions.get(type);
      if (subscribers && subscribers.size > 0) {
        subscribers.forEach(socketId => {
          const socket = this.userSockets.get(socketId);
          if (socket) {
            socket.emit('new_notification_alert', {
              notification: this.formatNotificationAlert(notification),
              timestamp: new Date(),
              message: `Nueva notificación de tipo ${type}: ${notification.title}`
            });
          }
        });
        console.log(`🎯 Alerta de notificación tipo ${type} enviada a ${subscribers.size} suscriptores`);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`Error enviando alerta por tipo ${type}:`, error);
      return false;
    }
  }

  /**
   * Alertar a suscriptores por prioridad de notificación
   */
  async alertNotificationByPriority(priority: NotificationPriority, notification: NotificationAlert): Promise<boolean> {
    try {
      const subscribers = this.prioritySubscriptions.get(priority);
      if (subscribers && subscribers.size > 0) {
        subscribers.forEach(socketId => {
          const socket = this.userSockets.get(socketId);
          if (socket) {
            socket.emit('new_notification_alert', {
              notification: this.formatNotificationAlert(notification),
              timestamp: new Date(),
              message: `Nueva notificación de prioridad ${priority}: ${notification.title}`
            });
          }
        });
        console.log(`⚡ Alerta de notificación prioridad ${priority} enviada a ${subscribers.size} suscriptores`);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`Error enviando alerta por prioridad ${priority}:`, error);
      return false;
    }
  }

  /**
   * Enviar estadísticas actualizadas al usuario
   */
  async sendUpdatedStats(userId: number): Promise<void> {
    try {
      const stats = await this.notificationService.getNotificationStats(userId);
      const room = `user:${userId}`;
      
      this.io.to(room).emit('stats_updated', {
        stats,
        timestamp: new Date()
      });
    } catch (error) {
      console.error('Error enviando estadísticas actualizadas:', error);
    }
  }

  /**
   * Obtener usuarios conectados
   */
  getConnectedUsers(): number[] {
    return Array.from(this.connectedUsers.keys());
  }

  /**
   * Verificar si un usuario está conectado
   */
  isUserConnected(userId: number): boolean {
    return this.connectedUsers.has(userId);
  }

  /**
   * Obtener estadísticas de conexiones
   */
  getConnectionStats(): { totalConnections: number; uniqueUsers: number } {
    return {
      totalConnections: this.userSockets.size,
      uniqueUsers: this.connectedUsers.size
    };
  }

  /**
   * Cerrar conexiones de un usuario específico (útil para logout forzado)
   */
  disconnectUser(userId: number): void {
    const userConnections = this.connectedUsers.get(userId);
    if (userConnections) {
      userConnections.forEach(socketId => {
        const socket = this.userSockets.get(socketId);
        if (socket) {
          socket.emit('force_disconnect', { reason: 'User logged out from another session' });
          socket.disconnect();
        }
      });
    }
  }

  /**
   * Ejecutar limpieza manual de notificaciones antiguas
   */
  async manualCleanupOldNotifications(): Promise<number> {
    return await this.cleanupOldNotifications();
  }

  /**
   * Obtener estadísticas detalladas de suscripciones
   */
  getSubscriptionStats(): {
    totalTypeSubscriptions: number;
    totalPrioritySubscriptions: number;
    typeBreakdown: Record<string, number>;
    priorityBreakdown: Record<string, number>;
  } {
    const typeBreakdown: Record<string, number> = {};
    const priorityBreakdown: Record<string, number> = {};

    this.typeSubscriptions.forEach((subscribers, type) => {
      typeBreakdown[type] = subscribers.size;
    });

    this.prioritySubscriptions.forEach((subscribers, priority) => {
      priorityBreakdown[priority] = subscribers.size;
    });

    return {
      totalTypeSubscriptions: Array.from(this.typeSubscriptions.values())
        .reduce((total, subscribers) => total + subscribers.size, 0),
      totalPrioritySubscriptions: Array.from(this.prioritySubscriptions.values())
        .reduce((total, subscribers) => total + subscribers.size, 0),
      typeBreakdown,
      priorityBreakdown
    };
  }

  /**
   * Obtener lista de usuarios suscritos a un tipo específico
   */
  getSubscribersByType(type: NotificationType): number[] {
    const subscribers = this.typeSubscriptions.get(type);
    if (!subscribers) return [];

    const userIds: number[] = [];
    subscribers.forEach(socketId => {
      const socket = this.userSockets.get(socketId);
      if (socket && socket.userId) {
        userIds.push(socket.userId);
      }
    });
    return userIds;
  }

  /**
   * Obtener lista de usuarios suscritos a una prioridad específica
   */
  getSubscribersByPriority(priority: NotificationPriority): number[] {
    const subscribers = this.prioritySubscriptions.get(priority);
    if (!subscribers) return [];

    const userIds: number[] = [];
    subscribers.forEach(socketId => {
      const socket = this.userSockets.get(socketId);
      if (socket && socket.userId) {
        userIds.push(socket.userId);
      }
    });
    return userIds;
  }
}

// Singleton instance
let webSocketServiceInstance: WebSocketService | null = null;

export const initializeWebSocketService = (
  httpServer: HTTPServer, 
  notificationService: NotificationService
): WebSocketService => {
  if (!webSocketServiceInstance) {
    webSocketServiceInstance = new WebSocketService(httpServer, notificationService);
  }
  return webSocketServiceInstance;
};

export const getWebSocketService = (): WebSocketService => {
  if (!webSocketServiceInstance) {
    throw new Error('WebSocket service no inicializado. Llama a initializeWebSocketService primero.');
  }
  return webSocketServiceInstance;
}; 