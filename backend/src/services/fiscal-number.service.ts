import { AssignFiscalNumberDto, AdminPaginationQueryDto, FiscalEntityTypeDto } from '../dtos/admin.dto';
import { query } from './db.service';
import { HttpException } from '../exceptions/http.exception';
import { IUser as User } from '../interfaces/user.interface';

export class FiscalNumberService {
  private readonly tableName = 'fiscal_numbers';

  public async assignFiscalNumber(data: AssignFiscalNumberDto, currentUser: User): Promise<any> {
    const { assignedNumber, entityType, entityId, notes } = data;

    if (entityType === FiscalEntityTypeDto.INVOICE) {
      const invoiceCheck = await query(`SELECT id FROM invoices WHERE id = $1`, [entityId]);
      if (invoiceCheck.rows.length === 0) {
        throw new HttpException(404, `La factura con ID ${entityId} no existe.`);
      }
    } else if (entityType === FiscalEntityTypeDto.CUSTOMER) {
      const customerCheck = await query(`SELECT id FROM customers WHERE id = $1`, [entityId]);
      if (customerCheck.rows.length === 0) {
        throw new HttpException(404, `El cliente con ID ${entityId} no existe.`);
      }
    }

    const sql = `
      INSERT INTO ${this.tableName}
        (assigned_number, entity_type, entity_id, assigned_by_user_id, assignment_date, notes, created_at, updated_at)
      VALUES
        ($1, $2, $3, $4, CURRENT_TIMESTAMP, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *;
    `;
    try {
      const result = await query(sql, [
        assignedNumber,
        entityType,
        entityId,
        currentUser.id,
        notes,
      ]);
      if (result.rows.length === 0) {
        throw new HttpException(500, 'No se pudo asignar el número fiscal.');
      }
      return result.rows[0];
    } catch (error) {
      console.error('Error in assignFiscalNumber:', error);
      if (error instanceof HttpException) throw error;
      if (error && typeof error === 'object' && 'code' in error) {
        const err = error as { code: string };
        if (err.code === '23505') {
          throw new HttpException(409, `El número fiscal '${assignedNumber}' ya ha sido asignado o viola otra restricción de unicidad.`);
        }
        if (err.code === '23503') {
            throw new HttpException(400, `Error de referencia: Verifique los IDs proporcionados (usuario, entidad).`);
        }
      }
      if (error instanceof Error) {
        throw new HttpException(500, `Error al asignar el número fiscal: ${error.message}`);
      }
      throw new HttpException(500, 'Error al asignar el número fiscal: error desconocido.');
    }
  }

  public async getFiscalNumberById(id: number): Promise<any> {
    const sql = `
      SELECT fn.*, u.name as assigned_by_username
      FROM ${this.tableName} fn
      LEFT JOIN users u ON fn.assigned_by_user_id = u.id
      WHERE fn.id = $1;
    `;
    try {
      const result = await query(sql, [id]);
      if (result.rows.length === 0) {
        throw new HttpException(404, 'Número fiscal no encontrado.');
      }
      return result.rows[0];
    } catch (error) {
      console.error('Error in getFiscalNumberById:', error);
      if (error instanceof HttpException) throw error;
      if (error instanceof Error) {
        throw new HttpException(500, `Error al obtener el número fiscal: ${error.message}`);
      }
      throw new HttpException(500, 'Error al obtener el número fiscal: error desconocido.');
    }
  }

  public async getFiscalNumberByEntity(entityType: FiscalEntityTypeDto, entityId: number): Promise<any[]> {
    const sql = `
      SELECT fn.*, u.name as assigned_by_username
      FROM ${this.tableName} fn
      LEFT JOIN users u ON fn.assigned_by_user_id = u.id
      WHERE fn.entity_type = $1 AND fn.entity_id = $2;
    `;
    try {
      const result = await query(sql, [entityType, entityId]);
      return result.rows;
    } catch (error) {
      console.error('Error in getFiscalNumberByEntity:', error);
      if (error instanceof HttpException) throw error;
      if (error instanceof Error) {
        throw new HttpException(500, `Error al obtener el número fiscal por entidad: ${error.message}`);
      }
      throw new HttpException(500, 'Error al obtener el número fiscal por entidad: error desconocido.');
    }
  }

  public async getAllFiscalNumbers(paginationQuery: AdminPaginationQueryDto): Promise<{ data: any[], total: number, page: number, limit: number }> {
    const { page = 1, limit = 10, sortBy = 'assignment_date', sortOrder = 'DESC', search } = paginationQuery;
    const offset = (page - 1) * limit;

    let selectClause = `fn.*, u.name as assigned_by_username`;
    let baseFrom = `FROM ${this.tableName} fn LEFT JOIN users u ON fn.assigned_by_user_id = u.id`;
    let whereClause = " WHERE 1=1 ";

    const params: any[] = [];
    let paramCount = 1;

    if (search) {
      whereClause += ` AND (fn.assigned_number ILIKE $${paramCount} OR fn.notes ILIKE $${paramCount} OR u.name ILIKE $${paramCount} OR fn.entity_type ILIKE $${paramCount} OR CAST(fn.entity_id AS TEXT) ILIKE $${paramCount})`;
      params.push(`%${search}%`);
      paramCount++;
    }

    const countSql = `SELECT COUNT(*) as total ${baseFrom} ${whereClause}`;

    const allowedSortBy = ['assigned_number', 'entity_type', 'entity_id', 'assignment_date', 'assigned_by_username'];
    let safeSortBy = 'fn.assignment_date';
    if (allowedSortBy.includes(sortBy)) {
        safeSortBy = sortBy === 'assigned_by_username' ? 'u.name' : `fn.${sortBy}`;
    }
    const safeSortOrder = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    const dataSql = `
      SELECT ${selectClause}
      ${baseFrom}
      ${whereClause}
      ORDER BY ${safeSortBy} ${safeSortOrder}
      LIMIT $${paramCount++} OFFSET $${paramCount++};
    `;
    const finalDataParams = [...params, limit, offset];
    const finalCountParams = [...params];

    try {
      const totalResult = await query(countSql, finalCountParams);
      const total = parseInt(totalResult.rows[0].total, 10);

      const dataResult = await query(dataSql, finalDataParams);
      return { data: dataResult.rows, total, page, limit };
    } catch (error) {
      console.error('Error in getAllFiscalNumbers:', error);
      if (error instanceof Error) {
        throw new HttpException(500, `Error al obtener los números fiscales: ${error.message}`);
      }
      throw new HttpException(500, 'Error al obtener los números fiscales: error desconocido.');
    }
  }

  public async deleteFiscalNumber(id: number): Promise<void> {
    const sql = `DELETE FROM ${this.tableName} WHERE id = $1;`;
    try {
      const result = await query(sql, [id]);
      if (result.rowCount === 0) {
        throw new HttpException(404, 'Número fiscal no encontrado.');
      }
    } catch (error) {
      console.error('Error in deleteFiscalNumber:', error);
      if (error instanceof HttpException) throw error;
      if (error instanceof Error) {
        throw new HttpException(500, `Error al eliminar el número fiscal: ${error.message}`);
      }
      throw new HttpException(500, 'Error al eliminar el número fiscal: error desconocido.');
    }
  }
}
