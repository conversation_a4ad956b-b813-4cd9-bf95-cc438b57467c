// /backend/src/services/client.service.ts
import { AppDataSource } from '../data-source';
import { Client } from '../entities/client.entity';
import { User } from '../entities/user.entity';
import { CreateClientDto, QuickClientRegistrationDto, UpdateClientDto, ClientStatus } from '../dtos/client.dto';
import { NotFoundException, BadRequestException } from '../exceptions';
import { RfcValidationService } from './rfc-validation.service';
import { DataSource } from "typeorm";

/**
 * @class ClientService
 * @description Servicio que encapsula la lógica de negocio para la gestión de clientes.
 */
export class ClientService {
  private dataSource: DataSource;
  private userRepository = AppDataSource.getRepository(User);
  private rfcValidationService = new RfcValidationService();

  constructor(dataSource: DataSource) {
    this.dataSource = dataSource;
  }

  /**
   * @description Obtiene todos los clientes de la base de datos.
   * @returns {Promise<Client[]>} Un arreglo de clientes.
   */
  async getAllClients(): Promise<Client[]> {
    return AppDataSource.getRepository(Client).find({ 
      relations: ['createdByUser', 'assignedSalesperson'],
      order: { commercialName: 'ASC' }
    });
  }

  /**
   * @description Busca un cliente por su ID.
   * @param {number} id - El ID del cliente a buscar.
   * @returns {Promise<Client>} El cliente encontrado.
   * @throws {NotFoundException} Si el cliente no se encuentra.
   */
  async getClientById(id: number): Promise<Client> {
    const client = await AppDataSource.getRepository(Client).findOne({
      where: { id },
      relations: ['createdByUser', 'assignedSalesperson'],
    });
    if (!client) {
      throw new NotFoundException('Cliente no encontrado.');
    }
    return client;
  }

  /**
   * @description Obtiene clientes filtrados por vendedor asignado.
   * @param {number} salespersonId - ID del vendedor.
   * @returns {Promise<Client[]>} Lista de clientes filtrados.
   */
  async getClientsBySalesperson(salespersonId: number): Promise<Client[]> {
    return AppDataSource.getRepository(Client).find({
      where: { assignedSalesperson: { id: salespersonId } },
      relations: ['createdByUser', 'assignedSalesperson'],
      order: { commercialName: 'ASC' }
    });
  }

  /**
   * @description Crea un nuevo cliente con registro rápido.
   * @param {QuickClientRegistrationDto} clientData - Datos mínimos del cliente.
   * @param {number} userId - ID del usuario que crea el cliente.
   * @returns {Promise<Client>} El cliente recién creado.
   * @throws {NotFoundException} Si el usuario creador no existe.
   */
  async createQuickClient(clientData: QuickClientRegistrationDto, userId: number): Promise<Client> {
    console.log('[DEBUG][SERVICE][createQuickClient] clientData:', clientData);
    const user = await this.userRepository.findOneBy({ id: userId });
    if (!user) {
      throw new NotFoundException('Usuario no encontrado.');
    }
    let assignedSalesperson: User = user;
    if (clientData.assignedSalespersonId && clientData.assignedSalespersonId !== userId) {
      const salesperson = await this.userRepository.findOneBy({ id: clientData.assignedSalespersonId });
      if (!salesperson) {
        throw new NotFoundException('Vendedor asignado no encontrado.');
      }
      assignedSalesperson = salesperson;
    }
    const existingClient = await AppDataSource.getRepository(Client).findOne({
      where: { rfc: clientData.rfc }
    });
    if (existingClient) {
      throw new BadRequestException(`Ya existe un cliente con el RFC ${clientData.rfc}`);
    }
    let rfcValidated = false;
    try {
      const validationResult = await this.rfcValidationService.validateRfc(clientData.rfc);
      rfcValidated = validationResult.valid;
    } catch (error) {
      console.error('Error al validar RFC con SAT:', error);
    }
    const newClient = AppDataSource.getRepository(Client).create({
      commercialName: clientData.commercialName,
      rfc: clientData.rfc,
      isQuickRegistration: true,
      status: ClientStatus.TEMPORARY,
      rfcValidated,
      createdByUser: user,
      assignedSalesperson: assignedSalesperson
    });
    console.log('[DEBUG][SERVICE][createQuickClient] Objeto a guardar:', newClient);
    return AppDataSource.getRepository(Client).save(newClient);
  }

  /**
   * @description Crea un nuevo cliente.
   * @param {CreateClientDto} clientData - Los datos para crear el cliente.
   * @param {number} userId - El ID del usuario que crea el cliente.
   * @returns {Promise<Client>} El cliente recién creado.
   * @throws {NotFoundException} Si el usuario creador no existe.
   */
  async createClient(clientData: CreateClientDto, userId: number): Promise<Client> {
    console.log('[DEBUG][SERVICE][createClient] clientData:', clientData);
    const user = await this.userRepository.findOneBy({ id: userId });
    if (!user) {
      throw new NotFoundException('Usuario no encontrado.');
    }
    // Manejar el vendedor asignado
    let assignedSalesperson: User = user;
    if (clientData.assignedSalespersonId) {
      const salesperson = await this.userRepository.findOneBy({ id: clientData.assignedSalespersonId });
      if (!salesperson) {
        throw new NotFoundException('Vendedor asignado no encontrado.');
      }
      assignedSalesperson = salesperson;
    }
    // Validar que no exista otro cliente con el mismo RFC
    const existingClient = await AppDataSource.getRepository(Client).findOne({
      where: { rfc: clientData.rfc }
    });
    if (existingClient) {
      throw new BadRequestException(`Ya existe un cliente con el RFC ${clientData.rfc}`);
    }
    // Intentar validar el RFC con el SAT (sin bloquear la creación)
    let rfcValidated = false;
    try {
      const validationResult = await this.rfcValidationService.validateRfc(clientData.rfc);
      rfcValidated = validationResult.valid;
    } catch (error) {
      console.error('Error al validar RFC con SAT:', error);
      // Continuamos con la creación aunque falle la validación
    }
    const newClient = AppDataSource.getRepository(Client).create({
      ...clientData,
      isQuickRegistration: clientData.isQuickRegistration || false,
      rfcValidated,
      createdByUser: user,
      assignedSalesperson: assignedSalesperson
    });
    console.log('[DEBUG][SERVICE][createClient] Objeto a guardar:', newClient);
    return AppDataSource.getRepository(Client).save(newClient);
  }

  /**
   * @description Actualiza un cliente existente.
   * @param {number} id - El ID del cliente a actualizar.
   * @param {UpdateClientDto} clientData - Los datos para actualizar.
   * @returns {Promise<Client>} El cliente actualizado.
   * @throws {NotFoundException} Si el cliente no se encuentra.
   */
  async updateClient(id: number, clientData: UpdateClientDto): Promise<Client> {
    console.log('[DEBUG][SERVICE][updateClient] clientData:', clientData);
    const client = await this.getClientById(id);
    // Si se actualiza el vendedor asignado
    if (clientData.assignedSalespersonId) {
      const assignedSalesperson = await this.userRepository.findOneBy({ id: clientData.assignedSalespersonId });
      if (!assignedSalesperson) {
        throw new NotFoundException('Vendedor asignado no encontrado.');
      }
      client.assignedSalesperson = assignedSalesperson;
      delete clientData.assignedSalespersonId; // Evitar duplicación
    }
    // Si se actualiza el RFC, validar que no exista otro cliente con el mismo RFC
    if (clientData.rfc && clientData.rfc !== client.rfc) {
      const existingClient = await AppDataSource.getRepository(Client).findOne({
        where: { rfc: clientData.rfc }
      });
      if (existingClient && existingClient.id !== id) {
        throw new BadRequestException(`Ya existe un cliente con el RFC ${clientData.rfc}`);
      }
      // Intentar validar el nuevo RFC con el SAT
      try {
        const validationResult = await this.rfcValidationService.validateRfc(clientData.rfc);
        clientData.rfcValidated = validationResult.valid;
      } catch (error) {
        console.error('Error al validar RFC con SAT:', error);
        clientData.rfcValidated = false;
      }
    }
    AppDataSource.getRepository(Client).merge(client, clientData);
    console.log('[DEBUG][SERVICE][updateClient] Objeto a guardar:', client);
    return AppDataSource.getRepository(Client).save(client);
  }

  /**
   * @description Elimina un cliente de la base de datos.
   * @param {number} id - El ID del cliente a eliminar.
   * @returns {Promise<boolean>} true si se eliminó el cliente, false si no existía.
   * @throws {NotFoundException} Si el cliente no se encuentra.
   */
  async deleteClient(id: string | number): Promise<boolean> {
    try {
      const repository = this.dataSource.getRepository("Client");
      const deleteQuery = `DELETE FROM clients WHERE id = $1`;
      const result = await repository.query(deleteQuery, [id]);
      if (result && (result.rowCount === 1 || result.affectedRows === 1)) return true;
      if (result && (result.rowCount === 0 || result.affectedRows === 0)) return false;
      const check = await repository.query(`SELECT id FROM clients WHERE id = $1`, [id]);
      if (check.length === 0) return false;
      return true;
    } catch (error: any) {
      if (error.message?.includes("not found")) return false;
      console.error("[CLIENTES][SERVICE][DELETE] Error:", error);
      throw error;
    }
  }

  /**
   * @description Valida un RFC con el servicio externo del SAT.
   * @param {string} rfc - El RFC a validar.
   * @returns {Promise<{ valid: boolean; data?: any }>} Resultado de la validación.
   */
  async validateRfcWithSat(rfc: string): Promise<{ valid: boolean; data?: any }> {
    return this.rfcValidationService.validateRfc(rfc);
  }

  /**
   * @description Actualiza el estado de validación del RFC de un cliente.
   * @param {number} id - El ID del cliente.
   * @param {boolean} isValid - Si el RFC es válido.
   * @returns {Promise<Client>} El cliente actualizado.
   */
  async updateRfcValidationStatus(id: number, isValid: boolean): Promise<Client> {
    const client = await this.getClientById(id);
    client.rfcValidated = isValid;
    
    return AppDataSource.getRepository(Client).save(client);
  }

  /**
   * @description Busca clientes con filtros avanzados.
   * @param {object} filters - Filtros para la búsqueda.
   * @returns {Promise<Client[]>} Clientes filtrados.
   */
  async searchClients(filters: {
    location?: string;
    name?: string;
    status?: string;
    companyArea?: string;
    clientType?: string;
    creditLimitMin?: number;
    creditLimitMax?: number;
    lastPurchaseStart?: Date;
    lastPurchaseEnd?: Date;
  }): Promise<Client[]> {
    const query = AppDataSource.getRepository(Client).createQueryBuilder('client')
      .leftJoinAndSelect('client.createdByUser', 'createdByUser')
      .leftJoinAndSelect('client.assignedSalesperson', 'assignedSalesperson');
    
    if (filters.name) {
      query.andWhere('(client.commercialName ILIKE :name OR client.legalName ILIKE :name OR client.rfc ILIKE :name)', 
        { name: `%${filters.name}%` });
    }
    
    if (filters.location) {
      query.andWhere('(client.city ILIKE :location OR client.state ILIKE :location)', 
        { location: `%${filters.location}%` });
    }
    
    if (filters.status) {
      query.andWhere('client.status = :status', { status: filters.status });
    }
    
    if (filters.companyArea) {
      query.andWhere('client.companyArea = :companyArea', { companyArea: filters.companyArea });
    }
    
    if (filters.clientType) {
      query.andWhere('client.clientType = :clientType', { clientType: filters.clientType });
    }
    
    if (filters.creditLimitMin !== undefined) {
      query.andWhere('client.creditLimit >= :creditLimitMin', { creditLimitMin: filters.creditLimitMin });
    }
    
    if (filters.creditLimitMax !== undefined) {
      query.andWhere('client.creditLimit <= :creditLimitMax', { creditLimitMax: filters.creditLimitMax });
    }
    
    // Nota: lastPurchase requeriría una relación con una tabla de compras/ventas
    // que implementaremos en el futuro
    
    return query.orderBy('client.commercialName', 'ASC').getMany();
  }

  async getClients(filters: any = {}): Promise<any> {
    try {
      // Construir query SQL directo, similar a usuarios
      const repository = this.dataSource.getRepository("Client");
      let query = `SELECT * FROM clients WHERE 1=1`;
      const params: any[] = [];
      if (filters.search) {
        query += ` AND (commercial_name ILIKE $${params.length + 1} OR legal_name ILIKE $${params.length + 1})`;
        params.push(`%${filters.search}%`);
      }
      if (filters.rfc) {
        query += ` AND rfc ILIKE $${params.length + 1}`;
        params.push(`%${filters.rfc}%`);
      }
      if (filters.status) {
        query += ` AND status = $${params.length + 1}`;
        params.push(filters.status);
      }
      // Paginación
      const page = parseInt(filters.page, 10) || 1;
      const limit = parseInt(filters.limit, 10) || 10;
      const offset = (page - 1) * limit;
      query += ` ORDER BY created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
      params.push(limit, offset);
      const clients = await repository.query(query, params);
      // Total
      const countQuery = `SELECT COUNT(*) FROM clients WHERE 1=1` + (params.length > 2 ? query.slice(23, query.indexOf("ORDER BY")) : "");
      const countParams = params.slice(0, params.length - 2);
      const totalResult = await repository.query(countQuery, countParams);
      const total = parseInt(totalResult[0]?.count || "0", 10);
      return {
        clients,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit) || 1,
      };
    } catch (error) {
      console.error("[CLIENTES][SERVICE][LIST] Error:", error);
      throw error;
    }
  }

  async getClient(id: string | number): Promise<any> {
    try {
      const repository = this.dataSource.getRepository("Client");
      const query = `SELECT * FROM clients WHERE id = $1`;
      const result = await repository.query(query, [id]);
      if (!result.length) return null;
      return result[0];
    } catch (error) {
      console.error("[CLIENTES][SERVICE][DETAIL] Error:", error);
      throw error;
    }
  }

  /**
   * Obtiene el historial de compras de un cliente.
   */
  async getClientPurchases(clientId: number): Promise<any[]> {
    const repository = this.dataSource.getRepository('invoices');
    // Consulta directa: facturas asociadas al cliente
    const query = `
      SELECT i.id, i.date, i.total, i.status
      FROM invoices i
      WHERE i.client_id = $1
      ORDER BY i.date DESC
    `;
    const purchases = await repository.query(query, [clientId]);
    // Opcional: obtener items de cada compra si se requiere
    // (puedes expandir esto según la estructura real de la BD)
    return purchases;
  }

  /**
   * Obtiene los productos más comprados de un cliente.
   */
  async getClientTopProducts(clientId: number): Promise<any[]> {
    const repository = this.dataSource.getRepository('invoice_items');
    // Consulta directa: sumar cantidad y total por producto para el cliente
    const query = `
      SELECT ii.product_id as productId, p.name, SUM(ii.quantity) as quantity, SUM(ii.total) as total
      FROM invoice_items ii
      JOIN invoices i ON ii.invoice_id = i.id
      JOIN products p ON ii.product_id = p.id
      WHERE i.client_id = $1
      GROUP BY ii.product_id, p.name
      ORDER BY quantity DESC
      LIMIT 10
    `;
    const topProducts = await repository.query(query, [clientId]);
    return topProducts;
  }

  /**
   * Obtiene los documentos asociados a un cliente.
   */
  async getClientDocuments(clientId: number): Promise<any[]> {
    const repository = this.dataSource.getRepository('client_documents');
    const query = `
      SELECT cd.id as relation_id, d.id, d.filename, d.file_type, d.file_size, d.url, d.tipo_documento, d.descripcion, d.created_at, d.uploaded_by, u.name as uploaded_by_name
      FROM client_documents cd
      JOIN documents d ON cd.document_id = d.id
      LEFT JOIN users u ON d.uploaded_by = u.id
      WHERE cd.client_id = $1
      ORDER BY d.created_at DESC
    `;
    const documents = await repository.query(query, [clientId]);
    return documents;
  }

  /**
   * Sube un nuevo documento para un cliente.
   * req debe contener el archivo (req.file o req.body segun el middleware)
   */
  async uploadClientDocument(clientId: number, req: any): Promise<any> {
    // Aquí deberías manejar la subida real de archivos (ejemplo: multer, formidable, etc.)
    // Por simplicidad, asumimos que req.body tiene filename, url, file_type, file_size, tipo_documento, descripcion
    const { filename, url, file_type, file_size, tipo_documento, descripcion } = req.body;
    const uploaded_by = req.user?.id || null;
    if (!filename || !url) {
      throw new Error('Faltan datos del archivo');
    }
    // 1. Insertar en documents
    const docRepo = this.dataSource.getRepository('documents');
    const insertDocQuery = `
      INSERT INTO documents (filename, url, file_type, file_size, tipo_documento, descripcion, uploaded_by, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
      RETURNING id, filename, url, file_type, file_size, tipo_documento, descripcion, uploaded_by, created_at
    `;
    const docResult = await docRepo.query(insertDocQuery, [filename, url, file_type, file_size, tipo_documento, descripcion, uploaded_by]);
    const document = docResult[0];
    // 2. Insertar en client_documents
    const relRepo = this.dataSource.getRepository('client_documents');
    const insertRelQuery = `
      INSERT INTO client_documents (client_id, document_id, document_type, created_at)
      VALUES ($1, $2, $3, NOW())
      RETURNING id
    `;
    await relRepo.query(insertRelQuery, [clientId, document.id, tipo_documento]);
    return document;
  }

  /**
   * Elimina un documento de un cliente.
   */
  async deleteClientDocument(clientId: number, docId: number): Promise<void> {
    const relRepo = this.dataSource.getRepository('client_documents');
    // 1. Eliminar la relación
    await relRepo.query('DELETE FROM client_documents WHERE client_id = $1 AND document_id = $2', [clientId, docId]);
    // 2. Verificar si el documento está asociado a otro cliente
    const check = await relRepo.query('SELECT COUNT(*) FROM client_documents WHERE document_id = $1', [docId]);
    if (parseInt(check[0].count, 10) === 0) {
      // Si no está asociado a ningún cliente, eliminar el documento
      const docRepo = this.dataSource.getRepository('documents');
      await docRepo.query('DELETE FROM documents WHERE id = $1', [docId]);
    }
  }

  /**
   * Obtiene los KPIs de un cliente.
   */
  async getClientKpis(): Promise<any> {
    // TODO: Implementar lógica real de KPIs
    // Placeholder: retorna objeto simulado
    return {
      totalSpent: 15000,
      purchasesThisYear: 5,
      averagePurchase: 3000
    };
  }
}