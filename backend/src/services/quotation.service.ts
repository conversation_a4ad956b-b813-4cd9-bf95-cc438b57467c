import { DataSource } from 'typeorm';
import { Quotation } from '../entities/quotation.entity';

export class QuotationService {
  private quotationRepository;

  constructor(private dataSource: DataSource) {
    this.quotationRepository = this.dataSource.getRepository(Quotation);
  }

  async getQuotationsByClientId(clientId: number): Promise<Quotation[]> {
    // SQL directo para evitar problemas de relaciones
    const query = `
      SELECT * FROM quotations
      WHERE customer_id = $1
      ORDER BY created_at DESC
    `;
    const result = await this.quotationRepository.query(query, [clientId]);
    return result;
  }
} 