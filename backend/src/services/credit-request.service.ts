import {
    CreateCreditRequestDto,
    UpdateCreditRequestDto,
    AdminPaginationQueryDto
} from '../dtos/admin.dto';
import { query } from './db.service';
import { HttpException } from '../exceptions/http.exception';
import { IUser as User } from '../interfaces/user.interface';
// import { NotificationService } from './notification.service';
// import { FileStorageService } from './file-storage.service';
// import { PdfGenerationService } from './pdf-generation.service';

export class CreditRequestService {
  private readonly tableName = 'credit_requests';
  private readonly documentsTableName = 'documents';
  private readonly creditRequestDocsTableName = 'credit_request_documents';
  // private notificationService: NotificationService;
  // private fileStorageService: FileStorageService;
  // private pdfGenerationService: PdfGenerationService;

  constructor() {
    // this.notificationService = new NotificationService();
    // this.fileStorageService = new FileStorageService();
    // this.pdfGenerationService = new PdfGenerationService();
  }

  public async createCreditRequest(data: CreateCreditRequestDto, currentUser: User): Promise<any> {
    const { requestNumber, customerId, amount, purpose } = data;

    const customerCheck = await query(`SELECT id, razon_social FROM customers WHERE id = $1`, [customerId]);
    if (customerCheck.rows.length === 0) {
      throw new HttpException(404, `El cliente con ID ${customerId} no existe.`);
    }
    // const customerName = customerCheck.rows[0].razon_social;

    try {
      const sql = `
        INSERT INTO ${this.tableName}
          (request_number, customer_id, amount, purpose, requester_id, status, created_at, updated_at)
        VALUES
          ($1, $2, $3, $4, $5, 'PENDING', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING *;
      `;
      const result = await query(sql, [
        requestNumber, customerId, amount, purpose, currentUser.id
      ]);
      const newCreditRequest = result.rows[0];

      return this.getCreditRequestById(newCreditRequest.id);
    } catch (error) {
      console.error('Error in createCreditRequest:', error);
      if (error instanceof HttpException) throw error;
      if (error && typeof error === 'object' && 'code' in error) {
        const err = error as { code: string };
        if (err.code === '23505') {
          throw new HttpException(409, `El número de solicitud '${requestNumber}' ya existe.`);
        }
      }
      if (error instanceof Error) {
        throw new HttpException(500, `Error al crear la solicitud de crédito: ${error.message}`);
      }
      throw new HttpException(500, 'Error al crear la solicitud de crédito: error desconocido.');
    }
  }

  public async uploadCreditRequestDocument(
    creditRequestId: number,
    file: Express.Multer.File,
    documentType: string,
    currentUser: User,
    description?: string
  ): Promise<any> {
    const requestCheck = await query(`SELECT id FROM ${this.tableName} WHERE id = $1`, [creditRequestId]);
    if (requestCheck.rows.length === 0) {
        throw new HttpException(404, `Solicitud de crédito con ID ${creditRequestId} no encontrada.`);
    }

    // const fileUrl = await this.fileStorageService.uploadFile(file, `credit_requests/${creditRequestId}/${file.filename}`);
    const fileUrl = `uploads/credit_requests/${creditRequestId}/${file.filename}`;

    try {
      const docSql = `
        INSERT INTO ${this.documentsTableName}
          (filename, url, uploaded_by, tipo_documento, descripcion, file_type, file_size, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id;
      `;
      const docResult = await query(docSql, [
        file.originalname, fileUrl, currentUser.id, `CREDIT_REQ_${documentType.toUpperCase()}`,
        description || `Documento ${documentType} para solicitud ${creditRequestId}`,
        file.mimetype, file.size
      ]);
      const documentId = docResult.rows[0].id;

      const assocSql = `
        INSERT INTO ${this.creditRequestDocsTableName}
          (credit_request_id, document_id, document_type)
        VALUES ($1, $2, $3);
      `;
      await query(assocSql, [creditRequestId, documentId, documentType.toUpperCase()]);

      return { message: 'Documento subido y asociado exitosamente.', documentId, fileUrl };
    } catch (error) {
      console.error('Error in uploadCreditRequestDocument:', error);
      // await this.fileStorageService.deleteFile(fileUrl);
      if (error instanceof Error) {
        throw new HttpException(500, `Error al subir el documento de la solicitud de crédito: ${error.message}`);
      }
      throw new HttpException(500, 'Error al subir el documento de la solicitud de crédito: error desconocido.');
    }
  }

  public async getCreditRequestById(id: number): Promise<any> {
    const sql = `
      SELECT
        cr.*,
        cust.razon_social as customer_name, cust.correo as customer_email,
        req_user.name as requester_username,
        app_user.name as approver_username,
        (SELECT json_agg(json_build_object(
            'id', d.id, 'filename', d.filename, 'url', d.url,
            'document_type', crd.document_type,
            'file_db_type', d.tipo_documento,
            'uploaded_at', d.created_at,
            'uploaded_by_username', up_user.name
          ))
         FROM ${this.documentsTableName} d
         JOIN ${this.creditRequestDocsTableName} crd ON d.id = crd.document_id
         LEFT JOIN users up_user ON d.uploaded_by = up_user.id
         WHERE crd.credit_request_id = cr.id) as attached_documents
      FROM ${this.tableName} cr
      JOIN customers cust ON cr.customer_id = cust.id
      LEFT JOIN users req_user ON cr.requester_id = req_user.id
      LEFT JOIN users app_user ON cr.approved_by = app_user.id
      WHERE cr.id = $1;
    `;
    try {
      const result = await query(sql, [id]);
      if (result.rows.length === 0) {
        throw new HttpException(404, 'Solicitud de crédito no encontrada.');
      }
      return result.rows[0];
    } catch (error) {
      console.error('Error in getCreditRequestById:', error);
      if (error instanceof HttpException) throw error;
      if (error instanceof Error) {
        throw new HttpException(500, `Error al obtener la solicitud de crédito: ${error.message}`);
      }
      throw new HttpException(500, 'Error al obtener la solicitud de crédito: error desconocido.');
    }
  }

  public async getAllCreditRequests(paginationQuery: AdminPaginationQueryDto): Promise<{ data: any[], total: number, page: number, limit: number }> {
    const { page = 1, limit = 10, sortBy = 'created_at', sortOrder = 'DESC', search } = paginationQuery;
    const offset = (page - 1) * limit;

    let selectClause = `cr.id, cr.request_number, cr.status, cr.amount, cr.approval_date,
                        cust.razon_social as customer_name, req_user.name as requester_username, cr.created_at`;
    let baseFrom = `
      FROM ${this.tableName} cr
      JOIN customers cust ON cr.customer_id = cust.id
      LEFT JOIN users req_user ON cr.requester_id = req_user.id
    `;
    let whereClause = " WHERE 1=1 ";
    const params: any[] = [];
    let paramCount = 1;

    if (search) {
      whereClause += ` AND (cr.request_number ILIKE $${paramCount} OR cust.razon_social ILIKE $${paramCount} OR cr.status ILIKE $${paramCount} OR req_user.name ILIKE $${paramCount})`;
      params.push(`%${search}%`);
      paramCount++;
    }

    const countSql = `SELECT COUNT(cr.id) as total ${baseFrom} ${whereClause}`;

    const allowedSortBy = ['request_number', 'customer_name', 'status', 'amount', 'created_at', 'approval_date'];
    let safeSortBy = 'cr.created_at';
    if (allowedSortBy.includes(sortBy)) {
        safeSortBy = sortBy === 'customer_name' ? 'cust.razon_social' : `cr.${sortBy}`;
    }
    const safeSortOrder = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    const dataSql = `SELECT ${selectClause} ${baseFrom} ${whereClause} ORDER BY ${safeSortBy} ${safeSortOrder} LIMIT $${paramCount++} OFFSET $${paramCount++};`;
    const finalDataParams = [...params, limit, offset];
    const finalCountParams = [...params];

    try {
      const totalResult = await query(countSql, finalCountParams);
      const total = parseInt(totalResult.rows[0].total, 10);
      const dataResult = await query(dataSql, finalDataParams);
      return { data: dataResult.rows, total, page, limit };
    } catch (error) {
      console.error('Error in getAllCreditRequests:', error);
      if (error instanceof Error) {
        throw new HttpException(500, `Error al obtener las solicitudes de crédito: ${error.message}`);
      }
      throw new HttpException(500, 'Error al obtener las solicitudes de crédito: error desconocido.');
    }
  }

  public async updateCreditRequestStatus(id: number, data: UpdateCreditRequestDto, currentUser: User): Promise<any> {
    const { status } = data;

    const currentRequestCheck = await query(`SELECT * FROM ${this.tableName} WHERE id = $1`, [id]);
    if (currentRequestCheck.rows.length === 0) {
        throw new HttpException(404, "Solicitud de crédito no encontrada.");
    }
    const currentRequest = currentRequestCheck.rows[0];

    const fieldsToUpdate: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    if (status) {
      fieldsToUpdate.push(`status = $${paramCount++}`);
      values.push(status);
    }
    if (status === 'APPROVED') {
      fieldsToUpdate.push(`approved_by = $${paramCount++}`);
      values.push(currentUser.id);
      fieldsToUpdate.push(`approval_date = CURRENT_TIMESTAMP`);
    } else if (status === 'REJECTED' || status === 'PENDING') {
        fieldsToUpdate.push(`approved_by = NULL`);
        fieldsToUpdate.push(`approval_date = NULL`);
    }

    if (fieldsToUpdate.length === 0 && status === currentRequest.status) return this.getCreditRequestById(id); // No hay cambios reales

    fieldsToUpdate.push(`updated_at = CURRENT_TIMESTAMP`);

    const sql = `UPDATE ${this.tableName} SET ${fieldsToUpdate.join(', ')} WHERE id = $${paramCount++} RETURNING *;`;
    values.push(id);

    try {
      const result = await query(sql, values);
      if (result.rows.length === 0) {
        throw new HttpException(404, 'Solicitud no encontrada o no se pudo actualizar.');
      }
      const updatedRequest = result.rows[0];

      if (status && status !== currentRequest.status) { // Solo notificar si el estado cambió
        const customerInfo = await query(`SELECT razon_social FROM customers WHERE id = $1`, [updatedRequest.customer_id]);
        const customerName = customerInfo.rows[0].razon_social;
        const requesterInfo = await query(`SELECT id FROM users WHERE id = $1`, [updatedRequest.requester_id]);

        const title = `Solicitud de Crédito ${updatedRequest.request_number} ${status}`;
        const message = `Solicitud de crédito para ${customerName} (N° ${updatedRequest.request_number}) ha sido ${status.toLowerCase()}.`;

        if(requesterInfo.rows.length > 0) {
            await query(
                `INSERT INTO notifications (title, message, type, user_id, priority, related_entity_type, related_entity_id, created_at, is_read)
                 VALUES ($1, $2, 'CREDIT_REQUEST_STATUS', $3, 'MEDIUM', 'CREDIT_REQUEST', $4, CURRENT_TIMESTAMP, false) ON CONFLICT DO NOTHING`,
                [title, message, requesterInfo.rows[0].id, updatedRequest.id]
            );
        }
        const ventasDept = await query(`SELECT id FROM departments WHERE name = 'Ventas' LIMIT 1;`);
        if (ventasDept.rows.length > 0) {
             await query(
                `INSERT INTO notifications (title, message, type, department_id, priority, related_entity_type, related_entity_id, created_at, is_read)
                 VALUES ($1, $2, 'CREDIT_REQUEST_STATUS', $3, 'MEDIUM', 'CREDIT_REQUEST', $4, CURRENT_TIMESTAMP, false) ON CONFLICT DO NOTHING`,
                [title, message, ventasDept.rows[0].id, updatedRequest.id]
            );
        }
        if (status === 'APPROVED') {
            const facturacionDept = await query(`SELECT id FROM departments WHERE name = 'Administración' LIMIT 1;`);
             if (facturacionDept.rows.length > 0) {
                 await query(
                    `INSERT INTO notifications (title, message, type, department_id, priority, related_entity_type, related_entity_id, created_at, is_read)
                     VALUES ($1, $2, 'CREDIT_REQUEST_APPROVED', $3, 'HIGH', 'CREDIT_REQUEST', $4, CURRENT_TIMESTAMP, false) ON CONFLICT DO NOTHING`,
                    [`Crédito Aprobado: ${customerName}`, `Solicitud ${updatedRequest.request_number} aprobada. Monto: ${updatedRequest.amount}`, facturacionDept.rows[0].id, updatedRequest.id]
                );
            }
        }
      }
      return this.getCreditRequestById(updatedRequest.id);
    } catch (error) {
      console.error('Error in updateCreditRequestStatus:', error);
      if (error instanceof HttpException) throw error;
      if (error instanceof Error) {
        throw new HttpException(500, `Error al actualizar estado de solicitud de crédito: ${error.message}`);
      }
      throw new HttpException(500, 'Error al actualizar estado de solicitud de crédito: error desconocido.');
    }
  }

  public async generateCreditCertificatePdf(creditRequestId: number, currentUser: User): Promise<Buffer | null> {
    const creditRequest = await this.getCreditRequestById(creditRequestId);
    if (!creditRequest || creditRequest.status !== 'APPROVED') {
      throw new HttpException(400, 'La solicitud de crédito no está aprobada o no existe para generar constancia.');
    }

    const dataForPdf = {
      requestNumber: creditRequest.request_number,
      customerName: creditRequest.customer_name,
      amountApproved: creditRequest.amount,
      approvalDate: new Date(creditRequest.approval_date).toLocaleDateString('es-MX'),
      approvedBy: creditRequest.approver_name || 'Sistema',
      lineaCreditoOtorgada: creditRequest.amount, // Asumiendo que el monto solicitado es la línea otorgada
      // Faltaría información del socio si es diferente al customerName
    };

    // Simulación de generación de PDF y guardado como documento
    console.log("[INFO] Simulación de generación de PDF para constancia de crédito:", dataForPdf);
    try {
        const certificateFileName = `Constancia_Credito_${creditRequest.request_number}.txt`; // Sería .pdf
        const certificateContent = `CONSTANCIA DE CRÉDITO\n\nNúmero de Solicitud: ${dataForPdf.requestNumber}\nCliente: ${dataForPdf.customerName}\nMonto Aprobado: ${dataForPdf.amountApproved}\nFecha Aprobación: ${dataForPdf.approvalDate}\nAprobado Por: ${dataForPdf.approvedBy}\nLínea de Crédito: ${dataForPdf.lineaCreditoOtorgada}`;
        const certificateUrl = `generated_documents/credit_certificates/${certificateFileName}`;

        const docSql = `
            INSERT INTO ${this.documentsTableName}
            (filename, url, uploaded_by, tipo_documento, descripcion, file_type, file_size, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            RETURNING id;
        `;
        const docResult = await query(docSql, [
            certificateFileName, certificateUrl, currentUser.id, 'CREDIT_CERTIFICATE',
            `Constancia de crédito para solicitud ${creditRequest.request_number}`,
            'application/pdf', // Simulado como PDF
            Buffer.from(certificateContent).length
        ]);
        const documentId = docResult.rows[0].id;

        const assocSql = `
            INSERT INTO ${this.creditRequestDocsTableName}
            (credit_request_id, document_id, document_type)
            VALUES ($1, $2, $3) ON CONFLICT DO NOTHING;
        `; // ON CONFLICT para evitar duplicados si se genera varias veces
        await query(assocSql, [creditRequestId, documentId, 'CERTIFICATE']);
        console.log(`[INFO] Constancia de crédito simulada y registrada como documento ID: ${documentId}`);
        return Buffer.from(certificateContent);
    } catch (error) {
        console.error('Error generando/registrando constancia de crédito simulada:', error);
        throw new HttpException(500, 'Error al generar/registrar la constancia de crédito.');
    }
  }

  public async deleteCreditRequest(id: number): Promise<void> {
    try {
        const docsToDelete = await query(
            `SELECT crd.document_id, d.url as file_url FROM ${this.creditRequestDocsTableName} crd
             JOIN ${this.documentsTableName} d ON crd.document_id = d.id
             WHERE crd.credit_request_id = $1`, [id]
        );
        for (const row of docsToDelete.rows) {
            // if (row.file_url) await this.fileStorageService.deleteFile(row.file_url); // Eliminar archivo físico
            await query(`DELETE FROM ${this.documentsTableName} WHERE id = $1`, [row.document_id]);
        }
        await query(`DELETE FROM ${this.creditRequestDocsTableName} WHERE credit_request_id = $1`, [id]);

        const result = await query(`DELETE FROM ${this.tableName} WHERE id = $1;`, [id]);
        if (result.rowCount === 0) {
            throw new HttpException(404, 'Solicitud de crédito no encontrada.');
        }
    } catch (error) {
        console.error('Error in deleteCreditRequest:', error);
        if (error instanceof Error) {
            throw new HttpException(500, `Error al eliminar la solicitud de crédito: ${error.message}`);
        }
        throw new HttpException(500, 'Error al eliminar la solicitud de crédito: error desconocido.');
    }
  }
}
