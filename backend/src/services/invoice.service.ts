import {
    CreateInvoiceDto,
    UpdateInvoiceDto,
    AdminPaginationQueryDto,
    UpdateInvoicePaymentStatusDto
} from '../dtos/admin.dto';
import { query } from './db.service';
import { HttpException } from '../exceptions/http.exception';
import { IUser as User } from '../interfaces/user.interface';
// import { NotificationService } from './notification.service';
// import { FileStorageService } from './file-storage.service';

export class InvoiceService {
  private readonly tableName = 'invoices';
  private readonly documentsTableName = 'documents';
  private readonly invoiceDocsTableName = 'invoice_documents';
  // private notificationService: NotificationService;
  // private fileStorageService: FileStorageService;

  constructor() {
    // this.notificationService = new NotificationService();
    // this.fileStorageService = new FileStorageService();
  }

  public async createInvoice(data: CreateInvoiceDto, currentUser: User): Promise<any> {
    const {
      quotationId, customerId, invoiceNumber, totalAmount, taxAmount,
      dueDate, status, notes, paymentTerms
    } = data;

    const customerCheck = await query(`SELECT id FROM customers WHERE id = $1`, [customerId]);
    if (customerCheck.rows.length === 0) {
      throw new HttpException(404, `El cliente con ID ${customerId} no existe.`);
    }
    if (quotationId) {
      const quotationCheck = await query(`SELECT id, created_by FROM quotations WHERE id = $1`, [quotationId]);
      if (quotationCheck.rows.length === 0) {
        throw new HttpException(404, `La cotización con ID ${quotationId} no existe.`);
      }
    }

    try {
      const sql = `
        INSERT INTO ${this.tableName}
          (quotation_id, customer_id, invoice_number, total_amount, tax_amount,
           due_date, status, notes, payment_terms, created_by, created_at, updated_at)
        VALUES
          ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING *;
      `;
      const result = await query(sql, [
        quotationId, customerId, invoiceNumber, totalAmount, taxAmount,
        dueDate, status || 'PENDING', notes, paymentTerms, currentUser.id
      ]);

      if (result.rows.length === 0) {
        throw new HttpException(500, 'No se pudo crear la factura.');
      }
      const newInvoice = result.rows[0];

      if (quotationId) {
        const quotationInfo = await query(`SELECT q.created_by, u.email as sales_user_email
                                                  FROM quotations q
                                                  JOIN users u ON q.created_by = u.id
                                                  WHERE q.id = $1`, [quotationId]);
        if (quotationInfo.rows.length > 0 && quotationInfo.rows[0].created_by) {
          const salesUserId = quotationInfo.rows[0].created_by;
          const notificationTitle = `Factura Creada: ${newInvoice.invoice_number}`;
          const notificationMessage = `Se ha creado la factura N° ${newInvoice.invoice_number} (Monto: ${newInvoice.total_amount}, Cliente ID: ${newInvoice.customer_id}).`;

          await query(
            `INSERT INTO notifications (title, message, type, user_id, priority, related_entity_type, related_entity_id, created_at, is_read)
             VALUES ($1, $2, 'INVOICE_CREATED', $3, 'MEDIUM', 'INVOICE', $4, CURRENT_TIMESTAMP, false)`,
            [notificationTitle, notificationMessage, salesUserId, newInvoice.id]
          );
        }
      }

      return this.getInvoiceById(newInvoice.id);
    } catch (error) {
      console.error('Error in createInvoice:', error);
      if (error instanceof HttpException) throw error;
      if (error && typeof error === 'object' && 'code' in error) {
        const err = error as { code: string };
        if (err.code === '23505') {
          throw new HttpException(409, `El número de factura '${invoiceNumber}' ya existe.`);
        }
      }
      if (error instanceof Error) {
        throw new HttpException(500, `Error al crear la factura: ${error.message}`);
      }
      throw new HttpException(500, 'Error al crear la factura: error desconocido.');
    }
  }

  public async uploadInvoiceDocument(
    invoiceId: number,
    file: Express.Multer.File,
    documentType: 'XML' | 'PDF' | 'OTHER',
    currentUser: User,
    description?: string
  ): Promise<any> {
    const invoiceCheck = await query(`SELECT id FROM ${this.tableName} WHERE id = $1`, [invoiceId]);
    if (invoiceCheck.rows.length === 0) {
        throw new HttpException(404, `Factura con ID ${invoiceId} no encontrada.`);
    }

    // Simulación de FileStorageService
    const simulatedFileStoragePath = `uploads/invoices/${invoiceId}`;
    // En un escenario real, aquí se subiría el archivo a S3 o disco y se obtendría la URL/path
    // await this.fileStorageService.upload(file, simulatedFileStoragePath, file.filename);
    const fileUrl = `${simulatedFileStoragePath}/${file.filename}`;

    try {
      const docSql = `
        INSERT INTO ${this.documentsTableName}
          (filename, url, uploaded_by, tipo_documento, descripcion, file_type, file_size, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id;
      `;
      const docResult = await query(docSql, [
        file.originalname,
        fileUrl, // URL o path del archivo guardado
        currentUser.id,
        `INVOICE_${documentType}`,
        description || `Documento ${documentType} para factura ${invoiceId}`,
        file.mimetype,
        file.size
      ]);
      const documentId = docResult.rows[0].id;

      const assocSql = `
        INSERT INTO ${this.invoiceDocsTableName}
          (invoice_id, document_id, document_type)
        VALUES ($1, $2, $3);
      `;
      await query(assocSql, [invoiceId, documentId, documentType]);

      return { message: 'Documento subido y asociado exitosamente.', documentId, fileUrl };
    } catch (error) {
      console.error('Error in uploadInvoiceDocument:', error);
      // Opcional: Rollback de subida de archivo si se implementa
      // await this.fileStorageService.delete(fileUrl);
      if (error instanceof Error) {
        throw new HttpException(500, `Error al subir el documento de la factura: ${error.message}`);
      }
      throw new HttpException(500, 'Error al subir el documento de la factura: error desconocido.');
    }
  }

  public async getInvoiceById(id: number): Promise<any> {
    const sql = `
      SELECT
        i.*,
        cust.razon_social as customer_name,
        q.quotation_number,
        proj.id as project_id,
        proj.name as project_name,
        creator.name as created_by_username,
        updater.name as updated_by_username,
        (SELECT json_agg(json_build_object(
            'id', d.id,
            'filename', d.filename,
            'url', d.url,
            'document_type', idoc.document_type, /* este es el tipo de la asociacion */
            'file_db_type', d.tipo_documento, /* este es el tipo guardado en la tabla documents */
            'uploaded_at', d.created_at,
            'uploaded_by_username', up_user.name
          ))
         FROM ${this.documentsTableName} d
         JOIN ${this.invoiceDocsTableName} idoc ON d.id = idoc.document_id
         LEFT JOIN users up_user ON d.uploaded_by = up_user.id
         WHERE idoc.invoice_id = i.id) as attached_documents
      FROM ${this.tableName} i
      JOIN customers cust ON i.customer_id = cust.id
      LEFT JOIN quotations q ON i.quotation_id = q.id
      LEFT JOIN projects proj ON q.project_id = proj.id
      LEFT JOIN users creator ON i.created_by = creator.id
      LEFT JOIN users updater ON i.updated_by = updater.id
      WHERE i.id = $1;
    `;
    try {
      const result = await query(sql, [id]);
      if (result.rows.length === 0) {
        throw new HttpException(404, 'Factura no encontrada.');
      }
      return result.rows[0];
    } catch (error) {
      console.error('Error in getInvoiceById:', error);
      if (error instanceof HttpException) throw error;
      if (error instanceof Error) {
        throw new HttpException(500, `Error al obtener la factura: ${error.message}`);
      }
      throw new HttpException(500, 'Error al obtener la factura: error desconocido.');
    }
  }

  public async getAllInvoices(paginationQuery: AdminPaginationQueryDto): Promise<{ data: any[], total: number, page: number, limit: number }> {
    const { page = 1, limit = 10, sortBy = 'created_at', sortOrder = 'DESC', search } = paginationQuery;
    const offset = (page - 1) * limit;

    let selectClause = `i.id, i.invoice_number, i.status, i.total_amount, i.due_date, i.payment_date, i.payment_terms,
                        cust.razon_social as customer_name, q.quotation_number, creator.name as created_by_username, i.created_at`;
    let baseFrom = `
      FROM ${this.tableName} i
      JOIN customers cust ON i.customer_id = cust.id
      LEFT JOIN quotations q ON i.quotation_id = q.id
      LEFT JOIN users creator ON i.created_by = creator.id
    `;
    let whereClause = " WHERE 1=1 ";
    const params: any[] = [];
    let paramCount = 1;

    if (search) {
      whereClause += ` AND (i.invoice_number ILIKE $${paramCount} OR cust.razon_social ILIKE $${paramCount} OR i.status ILIKE $${paramCount} OR q.quotation_number ILIKE $${paramCount} OR creator.name ILIKE $${paramCount})`;
      params.push(`%${search}%`);
      paramCount++;
    }

    const countSql = `SELECT COUNT(i.id) as total ${baseFrom} ${whereClause}`;

    const allowedSortBy = ['invoice_number', 'customer_name', 'status', 'total_amount', 'due_date', 'created_at', 'payment_terms'];
    let safeSortBy = 'i.created_at';
    if (allowedSortBy.includes(sortBy)) {
        safeSortBy = sortBy === 'customer_name' ? 'cust.razon_social' : `i.${sortBy}`;
    }
    const safeSortOrder = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    const dataSql = `SELECT ${selectClause} ${baseFrom} ${whereClause} ORDER BY ${safeSortBy} ${safeSortOrder} LIMIT $${paramCount++} OFFSET $${paramCount++};`;
    const finalDataParams = [...params, limit, offset];
    const finalCountParams = [...params];

    try {
      const totalResult = await query(countSql, finalCountParams);
      const total = parseInt(totalResult.rows[0].total, 10);
      const dataResult = await query(dataSql, finalDataParams);
      return { data: dataResult.rows, total, page, limit };
    } catch (error) {
      console.error('Error in getAllInvoices:', error);
      if (error instanceof Error) {
        throw new HttpException(500, `Error al obtener las facturas: ${error.message}`);
      }
      throw new HttpException(500, 'Error al obtener las facturas: error desconocido.');
    }
  }

  public async updateInvoice(id: number, data: UpdateInvoiceDto, currentUser: User): Promise<any> {
    const fieldsToUpdate: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    if (data.customerId) {
      const customerCheck = await query(`SELECT id FROM customers WHERE id = $1`, [data.customerId]);
      if (customerCheck.rows.length === 0) throw new HttpException(404, `Cliente con ID ${data.customerId} no existe.`);
    }
    if (data.quotationId !== undefined) {
      if (data.quotationId !== null) {
        const quotationCheck = await query(`SELECT id FROM quotations WHERE id = $1`, [data.quotationId]);
        if (quotationCheck.rows.length === 0) throw new HttpException(404, `Cotización con ID ${data.quotationId} no existe.`);
      }
    }

    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        fieldsToUpdate.push(`${snakeKey} = $${paramCount++}`);
        values.push(value);
      }
    });

    if (fieldsToUpdate.length === 0) return this.getInvoiceById(id);

    fieldsToUpdate.push(`updated_at = CURRENT_TIMESTAMP`, `updated_by = $${paramCount++}`);
    values.push(currentUser.id);

    const sql = `UPDATE ${this.tableName} SET ${fieldsToUpdate.join(', ')} WHERE id = $${paramCount++} RETURNING id;`;
    values.push(id);

    try {
      const result = await query(sql, values);
      if (result.rows.length === 0) {
        throw new HttpException(404, 'Factura no encontrada o no se pudo actualizar.');
      }

      const updatedInvoice = result.rows[0];

      if (data.status || data.paymentTerms) {
          const currentInvoiceData = await query(`SELECT customer_id, quotation_id FROM invoices WHERE id = $1`, [updatedInvoice.id]);
          await this.handleInvoiceStatusChange(
              query,
              updatedInvoice.id,
              data.status,
              data.paymentTerms,
              currentInvoiceData.rows[0].customer_id,
              currentInvoiceData.rows[0].quotation_id
          );
      }

      return this.getInvoiceById(updatedInvoice.id);
    } catch (error) {
      console.error('Error in updateInvoice:', error);
      if (error instanceof HttpException) throw error;
      if (error instanceof Error) {
        throw new HttpException(500, `Error al actualizar la factura: ${error.message}`);
      }
      throw new HttpException(500, 'Error al actualizar la factura: error desconocido.');
    }
  }

  public async updateInvoicePayment(id: number, data: UpdateInvoicePaymentStatusDto, currentUser: User): Promise<any> {
    // Esta función es un alias más específico para actualizar el estado de pago.
    // Llama a updateInvoice internamente.
    const { status, paymentDate, paymentTerms } = data;
    const updatePayload: UpdateInvoiceDto = { status };
    if (paymentDate !== undefined) updatePayload.paymentDate = paymentDate; // Permitir null para borrar fecha de pago
    if (paymentTerms) updatePayload.paymentTerms = paymentTerms;

    return this.updateInvoice(id, updatePayload, currentUser);
  }

  private async handleInvoiceStatusChange(
    client: any,
    invoiceId: number,
    newStatus?: string,
    paymentTerms?: string,
    customerId?: number,
    quotationId?: number
  ): Promise<void> {
    if (!newStatus && !paymentTerms) return; // No hay cambios relevantes para notificar

    const currentInvoice = (await client.query(`SELECT status, payment_terms, invoice_number FROM invoices WHERE id = $1`, [invoiceId])).rows[0];
    const finalStatus = newStatus || currentInvoice.status;
    const finalPaymentTerms = paymentTerms || currentInvoice.payment_terms;

    // Notificación a Informes para liberación
    if (finalPaymentTerms && (finalPaymentTerms.toUpperCase() === 'CREDIT' || finalPaymentTerms.toUpperCase() === 'PREPAID')) {
        if (finalStatus.toUpperCase() === 'PAID' || finalStatus.toUpperCase() === 'PENDING_RELEASE') {
            const informesDept = await client.query(`SELECT id FROM departments WHERE name = 'Informes' LIMIT 1;`);
            if (informesDept.rows.length > 0) {
                const title = `Factura ${currentInvoice.invoice_number} requiere atención`;
                const message = `Factura ${currentInvoice.invoice_number} (Términos: ${finalPaymentTerms}) ha cambiado a estado '${finalStatus}'. Revisar para liberación.`;
                await client.query(
                    `INSERT INTO notifications (title, message, type, department_id, priority, related_entity_type, related_entity_id, created_at, is_read)
                     VALUES ($1, $2, 'INVOICE_PAYMENT_UPDATE', $3, 'MEDIUM', 'INVOICE', $4, CURRENT_TIMESTAMP, false) ON CONFLICT DO NOTHING`,
                    [title, message, informesDept.rows[0].id, invoiceId]
                );
            }
        }
    }

    // Notificación a Vendedores sobre socios morosos
    if (finalStatus.toUpperCase() === 'OVERDUE' && customerId && quotationId) {
        const quotationInfo = await client.query(`SELECT created_by FROM quotations WHERE id = $1`, [quotationId]);
        if (quotationInfo.rows.length > 0 && quotationInfo.rows[0].created_by) {
            const salesUserId = quotationInfo.rows[0].created_by;
            const customerInfo = await client.query(`SELECT razon_social FROM customers WHERE id = $1`, [customerId]);
            const customerName = customerInfo.rows.length > 0 ? customerInfo.rows[0].razon_social : `ID ${customerId}`;
            const title = `Cliente Moroso: ${customerName}`;
            const message = `Cliente ${customerName} tiene factura ${currentInvoice.invoice_number} en estado moroso.`;
            await client.query(
                `INSERT INTO notifications (title, message, type, user_id, priority, related_entity_type, related_entity_id, created_at, is_read)
                 VALUES ($1, $2, 'CUSTOMER_OVERDUE_INVOICE', $3, 'HIGH', 'INVOICE', $4, CURRENT_TIMESTAMP, false) ON CONFLICT DO NOTHING`,
                [title, message, salesUserId, invoiceId]
            );
        }
    }
  }

  public async deleteInvoiceDocument(invoiceId: number, documentId: number, currentUser: User): Promise<void> {
    const docRes = await query(`SELECT url FROM ${this.documentsTableName} WHERE id = $1 AND uploaded_by = $2`, [documentId, currentUser.id]);
    // O permitir a admin eliminar cualquier documento:
    // const docRes = await query(`SELECT url FROM ${this.documentsTableName} WHERE id = $1`, [documentId]);
    if (docRes.rows.length === 0 && !currentUser.email?.endsWith('@comintec.com')) { // Asumiendo un rol admin por email o un sistema de roles
        throw new HttpException(404, "Documento no encontrado o no tiene permiso para eliminarlo.");
    }
    // const fileUrl = docRes.rows[0]?.url;

    try {
      await query(
        `DELETE FROM ${this.invoiceDocsTableName} WHERE invoice_id = $1 AND document_id = $2`,
        [invoiceId, documentId]
      );
      // No lanzar error si la asociación no existe, podría haber sido borrada.
      // Pero sí eliminar el documento si ya no tiene asociaciones (o según la lógica de negocio)

      // Verificar si el documento está asociado a otras facturas u entidades
      // const otherAssociations = await query(`SELECT COUNT(*) as count FROM ${this.invoiceDocsTableName} WHERE document_id = $1`, [documentId]);
      // if (parseInt(otherAssociations.rows[0].count, 10) === 0) {
         await query(`DELETE FROM ${this.documentsTableName} WHERE id = $1`, [documentId]);
         // if (fileUrl) await this.fileStorageService.deleteFile(fileUrl); // Eliminar archivo físico
      // }

    } catch (error) {
      console.error('Error deleting invoice document:', error);
      if (error instanceof HttpException) throw error;
      if (error instanceof Error) {
        throw new HttpException(500, `Error al eliminar documento de factura: ${error.message}`);
      }
      throw new HttpException(500, 'Error al eliminar documento de factura: error desconocido.');
    }
  }

  public async deleteInvoice(id: number): Promise<void> {
    try {
        const docsToDelete = await query(
            `SELECT document_id, d.url as file_url FROM ${this.invoiceDocsTableName} idoc
             JOIN ${this.documentsTableName} d ON idoc.document_id = d.id
             WHERE idoc.invoice_id = $1`, [id]
        );

        for (const row of docsToDelete.rows) {
            // if (row.file_url) await this.fileStorageService.deleteFile(row.file_url);
            await query(`DELETE FROM ${this.documentsTableName} WHERE id = $1`, [row.document_id]);
        }
        await query(`DELETE FROM ${this.invoiceDocsTableName} WHERE invoice_id = $1`, [id]);

        const result = await query(`DELETE FROM ${this.tableName} WHERE id = $1;`, [id]);
        if (result.rowCount === 0) {
            throw new HttpException(404, 'Factura no encontrada.');
        }
    } catch (error) {
        console.error('Error in deleteInvoice:', error);
        if (error instanceof HttpException) throw error;
        if (error instanceof Error) {
            throw new HttpException(500, `Error al eliminar la factura: ${error.message}`);
        }
        throw new HttpException(500, 'Error al eliminar la factura: error desconocido.');
    }
  }
}
