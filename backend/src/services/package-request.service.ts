import { CreatePackageRequestDto, UpdatePackageRequestDto, AdminPaginationQueryDto } from '../dtos/admin.dto';
import { executeQuery } from './db.service';
import { HttpException } from '../exceptions/http.exception';
import { IUser as User } from '../interfaces/user.interface'; // Renombrado IUser a User
import { DataSource } from 'typeorm';
import { NotificationService } from './notification.service';
import { Role } from '../constants/role.enum';
import { NotificationType, NotificationPriority } from '../entities/notification.entity';

export class PackageRequestService {
  private readonly tableName = 'requests';
  private testDataSource?: DataSource;
  private notificationService: NotificationService;

  constructor(testDataSource?: DataSource) {
    this.testDataSource = testDataSource;
    this.notificationService = new NotificationService();
  }

  public async createPackageRequest(
    data: CreatePackageRequestDto,
    currentUser: User, // Asumiendo que esta es la interfaz del usuario autenticado
  ): Promise<any> {
    const { title, description, departmentId, priority, destinationAddress, shippingCompany, packageWeight, packageDimensions } = data;

    const requesterIdToUse = currentUser.id;
    // Asumimos que 'currentUser' podría tener 'departmentId', pero no está en la interfaz IUser actual.
    // Si no, departmentId debe venir del DTO o tener otra lógica.
    // Por ahora, si departmentId no viene en 'data', se insertará NULL o el default de la BD.
    const departmentIdToUse = departmentId;

    // Usar el esquema público para todos los casos
    const fullTableName = this.tableName;

    const sql = `
      INSERT INTO ${fullTableName}
        (request_type, title, description, requester_id, department_id, priority, status,
         destination_address, shipping_company, package_weight, package_dimensions, created_at, updated_at)
      VALUES
        ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *;
    `;

    try {
      const result = await executeQuery(sql, [
        'PAQUETERIA',
        title,
        description,
        requesterIdToUse,
        departmentIdToUse,
        priority || 'MEDIUM',
        'PENDING',
        destinationAddress,
        shippingCompany,
        packageWeight,
        packageDimensions
      ], this.testDataSource);
      
      if (result.rows.length === 0) {
        throw new HttpException(500, 'No se pudo crear la solicitud de paquetería');
      }
      return result.rows[0];
    } catch (error) {
      console.error('Error in createPackageRequest:', error);
      if (error instanceof HttpException) throw error;
      // Verificar si el error es por constraint de FK o similar (ej. department_id no existe)
      if (error && typeof error === 'object' && 'code' in error) {
        const err = error as { code: string, column: string };
        if (err.code === '23503') { // Foreign key violation
            throw new HttpException(400, `Error de referencia: Verifique que el departamento con id ${departmentIdToUse} existe.`);
        }
        if (err.code === '23502') { // Not null violation
            throw new HttpException(400, `Error: Campo requerido faltante o nulo que no debería serlo. Detalle: ${err.column}`);
        }
      }
      if (error instanceof Error) {
        throw new HttpException(500, `Error al crear la solicitud de paquetería: ${error.message}`);
      }
      throw new HttpException(500, 'Error al crear la solicitud de paquetería: error desconocido.');
    }
  }

  public async getPackageRequestById(id: number): Promise<any> {
    const fullTableName = this.tableName;
    const userTableName = 'users';
    const deptTableName = 'departments';

    const sql = `
      SELECT r.*, u_req.name as requester_name, d.name as department_name, u_assign.name as assigned_to_name
      FROM ${fullTableName} r
      LEFT JOIN ${userTableName} u_req ON r.requester_id = u_req.id
      LEFT JOIN ${deptTableName} d ON r.department_id = d.id
      LEFT JOIN ${userTableName} u_assign ON r.assigned_to = u_assign.id
      WHERE r.id = $1 AND r.request_type = 'PAQUETERIA';
    `;
    try {
      const result = await executeQuery(sql, [id], this.testDataSource);
      if (result.rows.length === 0) {
        throw new HttpException(404, 'Solicitud de paquetería no encontrada');
      }
      return result.rows[0];
    } catch (error) {
      console.error('Error in getPackageRequestById:', error);
      if (error instanceof HttpException) throw error;
      if (error instanceof Error) {
        throw new HttpException(500, `Error al obtener la solicitud de paquetería: ${error.message}`);
      }
      throw new HttpException(500, 'Error al obtener la solicitud de paquetería: error desconocido.');
    }
  }

  public async getAllPackageRequests(
    paginationQuery: AdminPaginationQueryDto
  ): Promise<{ data: any[], total: number, page: number, limit: number }> {
    const { page = 1, limit = 10, sortBy = 'created_at', sortOrder = 'DESC', search } = paginationQuery;
    const offset = (page - 1) * limit;

    const fullTableName = this.tableName;
    const userTableName = 'users';
    const deptTableName = 'departments';

    const selectClause = `r.*, u_req.name as requester_name, d.name as department_name, u_assign.name as assigned_to_name`;
    let fromAndWhereClause = `
      FROM ${fullTableName} r
      LEFT JOIN ${userTableName} u_req ON r.requester_id = u_req.id
      LEFT JOIN ${deptTableName} d ON r.department_id = d.id
      LEFT JOIN ${userTableName} u_assign ON r.assigned_to = u_assign.id
      WHERE r.request_type = 'PAQUETERIA'
    `;

    const params: any[] = [];
    let paramCount = 1;

    if (search) {
      fromAndWhereClause += ` AND (r.title ILIKE $${paramCount} OR r.description ILIKE $${paramCount} OR u_req.name ILIKE $${paramCount} OR d.name ILIKE $${paramCount})`;
      params.push(`%${search}%`);
      paramCount++;
    }

    // Ejemplo de filtro adicional: si se quiere ver solo las del departamento del usuario actual
    // if (currentUser && currentUser.departmentId) {
    //   fromAndWhereClause += ` AND r.department_id = $${paramCount++}`;
    //   params.push(currentUser.departmentId);
    // }

    const countSql = `SELECT COUNT(*) as total ${fromAndWhereClause}`;

    const allowedSortBy = ['title', 'created_at', 'status', 'priority', 'requester_name', 'department_name', 'assigned_to_name'];
    let safeSortBy = allowedSortBy.includes(sortBy) ? `r.${sortBy}` : 'r.created_at'; // Prefijo r. para desambiguar
    if (sortBy === 'requester_name') safeSortBy = 'u_req.name';
    if (sortBy === 'department_name') safeSortBy = 'd.name';
    if (sortBy === 'assigned_to_name') safeSortBy = 'u_assign.name';


    const safeSortOrder = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    const dataSql = `
      SELECT ${selectClause}
      ${fromAndWhereClause}
      ORDER BY ${safeSortBy} ${safeSortOrder}
      LIMIT $${paramCount++} OFFSET $${paramCount++};
    `;
    const finalDataParams = [...params, limit, offset];
    const finalCountParams = [...params];


    try {
      const totalResult = await executeQuery(countSql, finalCountParams, this.testDataSource);
      const total = parseInt(totalResult.rows[0].total, 10);

      const dataResult = await executeQuery(dataSql, finalDataParams, this.testDataSource);
      return { data: dataResult.rows, total, page, limit };
    } catch (error) {
      console.error('Error in getAllPackageRequests:', error);
      if (error instanceof Error) {
        throw new HttpException(500, `Error al obtener las solicitudes de paquetería: ${error.message}`);
      }
      throw new HttpException(500, 'Error al obtener las solicitudes de paquetería: error desconocido.');
    }
  }

  public async updatePackageRequest(id: number, data: UpdatePackageRequestDto): Promise<any> {
    const fieldsToUpdate: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    // Obtener la solicitud actual para verificar permisos o estado si es necesario
    // const currentRequest = await this.getPackageRequestById(id);
    // if (currentRequest.requester_id !== currentUser.id && !currentUser.roles.includes('ROLE_ADMIN')) {
    //   throw new HttpException(403, "No tiene permiso para actualizar esta solicitud");
    // }

    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        fieldsToUpdate.push(`${snakeKey} = $${paramCount++}`);
        values.push(value);
      }
    });

    if (fieldsToUpdate.length === 0) {
      // Devolver la solicitud sin cambios o un error, según se prefiera
      return this.getPackageRequestById(id);
      // throw new HttpException(400, 'No hay datos para actualizar');
    }

    fieldsToUpdate.push(`updated_at = CURRENT_TIMESTAMP`);

    const fullTableName = this.tableName;

    const sql = `
      UPDATE ${fullTableName}
      SET ${fieldsToUpdate.join(', ')}
      WHERE id = $${paramCount++} AND request_type = 'PAQUETERIA'
      RETURNING *;
    `;
    values.push(id);

    try {
      const result = await executeQuery(sql, values, this.testDataSource);
      if (result.rows.length === 0) {
        throw new HttpException(404, 'Solicitud de paquetería no encontrada o no se pudo actualizar');
      }
      // Devolver la solicitud actualizada con nombres de relaciones
      return this.getPackageRequestById(result.rows[0].id);
    } catch (error) {
      console.error('Error in updatePackageRequest:', error);
      if (error instanceof HttpException) throw error;
      if (error instanceof Error) {
        throw new HttpException(500, `Error al actualizar la solicitud de paquetería: ${error.message}`);
      }
      throw new HttpException(500, 'Error al actualizar la solicitud de paquetería: error desconocido.');
    }
  }

  public async deletePackageRequest(id: number): Promise<void> {
    const fullTableName = this.tableName;

    const sql = `
      DELETE FROM ${fullTableName}
      WHERE id = $1 AND request_type = 'PAQUETERIA';
    `;
    try {
      const result = await executeQuery(sql, [id], this.testDataSource);
      if (result.rowCount === 0) {
        throw new HttpException(404, 'Solicitud de paquetería no encontrada');
      }
    } catch (error) {
      console.error('Error in deletePackageRequest:', error);
      if (error instanceof HttpException) throw error;
      if (error instanceof Error) {
        throw new HttpException(500, `Error al eliminar la solicitud de paquetería: ${error.message}`);
      }
      throw new HttpException(500, 'Error al eliminar la solicitud de paquetería: error desconocido.');
    }
  }

  public async createTravelRequest(
    data: any, // CreateTravelRequestDto
    currentUser: User
  ): Promise<any> {
    const {
      title, description, departmentId, priority, travelEndDate, destination, requestedAmount, clientOrProject
    } = data;
    const requesterIdToUse = currentUser.id;
    const departmentIdToUse = departmentId;
    const sql = `
      INSERT INTO requests
        (request_type, title, description, requester_id, department_id, priority, status,
         due_date, destination_address, package_weight, created_at, updated_at, category)
      VALUES
        ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $11)
      RETURNING *;
    `;
    try {
      const result = await executeQuery(sql, [
        'VIATICOS',
        title,
        description,
        requesterIdToUse,
        departmentIdToUse,
        priority || 'MEDIUM',
        'PENDING',
        travelEndDate, // due_date
        destination,
        requestedAmount,
        clientOrProject
      ], this.testDataSource);
      if (result.rows.length === 0) {
        throw new HttpException(500, 'No se pudo crear la solicitud de viáticos');
      }
      const travelRequest = result.rows[0];
      // Notificar a administradores/gerentes usando Role enum
      await this.notificationService.triggerNotification({
        title: 'Nueva solicitud de viáticos',
        message: `${currentUser.name} ha registrado una solicitud de viáticos: ${title}`,
        type: NotificationType.INFO,
        priority: NotificationPriority.MEDIUM,
        roleId: getRoleIds([Role.Admin, Role.Manager]),
        metadata: { requestId: travelRequest.id, requestType: 'VIATICOS' }
      });
      return travelRequest;
    } catch (error) {
      throw new HttpException(500, 'Error al crear la solicitud de viáticos');
    }
  }

  public async createOvertimeRequest(
    data: any, // CreateOvertimeRequestDto
    currentUser: User
  ): Promise<any> {
    const {
      title, description, departmentId, priority, overtimeDate, clientOrProject
    } = data;
    const requesterIdToUse = currentUser.id;
    const departmentIdToUse = departmentId;
    const sql = `
      INSERT INTO requests
        (request_type, title, description, requester_id, department_id, priority, status,
         due_date, created_at, updated_at, category)
      VALUES
        ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $9)
      RETURNING *;
    `;
    try {
      const result = await executeQuery(sql, [
        'TIEMPO_EXTRA',
        title,
        description,
        requesterIdToUse,
        departmentIdToUse,
        priority || 'MEDIUM',
        'PENDING',
        overtimeDate,
        clientOrProject
      ], this.testDataSource);
      if (result.rows.length === 0) {
        throw new HttpException(500, 'No se pudo crear la solicitud de tiempo extra');
      }
      const overtimeRequest = result.rows[0];
      // Notificar a administradores/gerentes usando Role enum
      await this.notificationService.triggerNotification({
        title: 'Nueva solicitud de tiempo extra',
        message: `${currentUser.name} ha registrado una solicitud de tiempo extra: ${title}`,
        type: NotificationType.INFO,
        priority: NotificationPriority.MEDIUM,
        roleId: getRoleIds([Role.Admin, Role.Manager]),
        metadata: { requestId: overtimeRequest.id, requestType: 'TIEMPO_EXTRA' }
      });
      return overtimeRequest;
    } catch (error) {
      throw new HttpException(500, 'Error al crear la solicitud de tiempo extra');
    }
  }

  public async updateTravelRequestStatus(
    id: number,
    data: { status: 'APPROVED' | 'REJECTED'; approverComment?: string; approvalDate?: string; approverId: number }
  ): Promise<any> {
    const sql = `
      UPDATE requests
      SET status = $1,
          approver_comment = $2,
          approved_by = $3,
          approval_date = $4,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $5 AND request_type = 'VIATICOS'
      RETURNING *;
    `;
    const approvalDate = data.approvalDate || new Date().toISOString();
    const result = await executeQuery(sql, [
      data.status,
      data.approverComment || null,
      data.approverId,
      approvalDate,
      id
    ], this.testDataSource);
    if (result.rows.length === 0) {
      throw new HttpException(404, 'Solicitud de viáticos no encontrada');
    }
    const updatedRequest = result.rows[0];
    // Notificar al solicitante
    await this.notificationService.triggerNotification({
      title: `Solicitud de viáticos ${data.status === 'APPROVED' ? 'aprobada' : 'rechazada'}`,
      message: `Tu solicitud de viáticos "${updatedRequest.title}" ha sido ${data.status === 'APPROVED' ? 'aprobada' : 'rechazada'}.`,
      type: data.status === 'APPROVED' ? NotificationType.SUCCESS : NotificationType.ERROR,
      priority: NotificationPriority.MEDIUM,
      userId: updatedRequest.requester_id,
      metadata: { requestId: updatedRequest.id, requestType: 'VIATICOS', status: data.status }
    });
    return updatedRequest;
  }

  public async updateOvertimeRequestStatus(
    id: number,
    data: { status: 'APPROVED' | 'REJECTED'; approverComment?: string; approvalDate?: string; approverId: number }
  ): Promise<any> {
    const sql = `
      UPDATE requests
      SET status = $1,
          approver_comment = $2,
          approved_by = $3,
          approval_date = $4,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $5 AND request_type = 'TIEMPO_EXTRA'
      RETURNING *;
    `;
    const approvalDate = data.approvalDate || new Date().toISOString();
    const result = await executeQuery(sql, [
      data.status,
      data.approverComment || null,
      data.approverId,
      approvalDate,
      id
    ], this.testDataSource);
    if (result.rows.length === 0) {
      throw new HttpException(404, 'Solicitud de tiempo extra no encontrada');
    }
    const updatedRequest = result.rows[0];
    // Notificar al solicitante
    await this.notificationService.triggerNotification({
      title: `Solicitud de tiempo extra ${data.status === 'APPROVED' ? 'aprobada' : 'rechazada'}`,
      message: `Tu solicitud de tiempo extra "${updatedRequest.title}" ha sido ${data.status === 'APPROVED' ? 'aprobada' : 'rechazada'}.`,
      type: data.status === 'APPROVED' ? NotificationType.SUCCESS : NotificationType.ERROR,
      priority: NotificationPriority.MEDIUM,
      userId: updatedRequest.requester_id,
      metadata: { requestId: updatedRequest.id, requestType: 'TIEMPO_EXTRA', status: data.status }
    });
    return updatedRequest;
  }

  public async getTravelRequestById(id: number): Promise<any> {
    const sql = `
      SELECT * FROM requests WHERE id = $1 AND request_type = 'VIATICOS';
    `;
    const result = await executeQuery(sql, [id], this.testDataSource);
    if (result.rows.length === 0) {
      return null;
    }
    return result.rows[0];
  }

  public async updateTravelRequest(id: number, data: any): Promise<any> {
    const fieldsToUpdate: string[] = [];
    const values: any[] = [];
    let paramCount = 1;
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        fieldsToUpdate.push(`${snakeKey} = $${paramCount++}`);
        values.push(value);
      }
    });
    if (fieldsToUpdate.length === 0) {
      return this.getTravelRequestById(id);
    }
    fieldsToUpdate.push(`updated_at = CURRENT_TIMESTAMP`);
    const sql = `
      UPDATE requests
      SET ${fieldsToUpdate.join(', ')}
      WHERE id = $${paramCount++} AND request_type = 'VIATICOS'
      RETURNING *;
    `;
    values.push(id);
    const result = await executeQuery(sql, values, this.testDataSource);
    if (result.rows.length === 0) {
      throw new HttpException(404, 'Solicitud de viáticos no encontrada o no se pudo actualizar');
    }
    return result.rows[0];
  }

  public async getOvertimeRequestById(id: number): Promise<any> {
    const sql = `
      SELECT * FROM requests WHERE id = $1 AND request_type = 'TIEMPO_EXTRA';
    `;
    const result = await executeQuery(sql, [id], this.testDataSource);
    if (result.rows.length === 0) {
      return null;
    }
    return result.rows[0];
  }

  public async updateOvertimeRequest(id: number, data: any): Promise<any> {
    const fieldsToUpdate: string[] = [];
    const values: any[] = [];
    let paramCount = 1;
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        fieldsToUpdate.push(`${snakeKey} = $${paramCount++}`);
        values.push(value);
      }
    });
    if (fieldsToUpdate.length === 0) {
      return this.getOvertimeRequestById(id);
    }
    fieldsToUpdate.push(`updated_at = CURRENT_TIMESTAMP`);
    const sql = `
      UPDATE requests
      SET ${fieldsToUpdate.join(', ')}
      WHERE id = $${paramCount++} AND request_type = 'TIEMPO_EXTRA'
      RETURNING *;
    `;
    values.push(id);
    const result = await executeQuery(sql, values, this.testDataSource);
    if (result.rows.length === 0) {
      throw new HttpException(404, 'Solicitud de tiempo extra no encontrada o no se pudo actualizar');
    }
    return result.rows[0];
  }

  public async deleteTravelRequest(id: number): Promise<void> {
    const sql = `
      DELETE FROM requests WHERE id = $1 AND request_type = 'VIATICOS';
    `;
    const result = await executeQuery(sql, [id], this.testDataSource);
    if (result.rowCount === 0) {
      throw new HttpException(404, 'Solicitud de viáticos no encontrada');
    }
  }

  public async deleteOvertimeRequest(id: number): Promise<void> {
    const sql = `
      DELETE FROM requests WHERE id = $1 AND request_type = 'TIEMPO_EXTRA';
    `;
    const result = await executeQuery(sql, [id], this.testDataSource);
    if (result.rowCount === 0) {
      throw new HttpException(404, 'Solicitud de tiempo extra no encontrada');
    }
  }

  public async getTravelRequestsByUser(userId: number): Promise<any[]> {
    const sql = `
      SELECT * FROM requests WHERE requester_id = $1 AND request_type = 'VIATICOS' ORDER BY created_at DESC;
    `;
    const result = await executeQuery(sql, [userId], this.testDataSource);
    return result.rows;
  }

  public async getOvertimeRequestsByUser(userId: number): Promise<any[]> {
    const sql = `
      SELECT * FROM requests WHERE requester_id = $1 AND request_type = 'TIEMPO_EXTRA' ORDER BY created_at DESC;
    `;
    const result = await executeQuery(sql, [userId], this.testDataSource);
    return result.rows;
  }

  /**
   * List all travel requests with advanced filters for admin/global view
   */
  public async getAllTravelRequests(
    filters: {
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'ASC' | 'DESC';
      status?: string;
      departmentId?: number;
      requesterId?: number;
      startDate?: string;
      endDate?: string;
      search?: string;
    }
  ): Promise<{ data: any[]; total: number; page: number; limit: number }> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'created_at',
      sortOrder = 'DESC',
      status,
      departmentId,
      requesterId,
      startDate,
      endDate,
      search,
    } = filters;
    const offset = (page - 1) * limit;
    let where = `WHERE r.request_type = 'VIATICOS'`;
    const params: any[] = [];
    let paramCount = 1;
    if (status) {
      where += ` AND r.status = $${paramCount++}`;
      params.push(status);
    }
    if (departmentId) {
      where += ` AND r.department_id = $${paramCount++}`;
      params.push(departmentId);
    }
    if (requesterId) {
      where += ` AND r.requester_id = $${paramCount++}`;
      params.push(requesterId);
    }
    if (startDate) {
      where += ` AND r.created_at >= $${paramCount++}`;
      params.push(startDate);
    }
    if (endDate) {
      where += ` AND r.created_at <= $${paramCount++}`;
      params.push(endDate);
    }
    if (search) {
      where += ` AND (r.title ILIKE $${paramCount} OR r.description ILIKE $${paramCount})`;
      params.push(`%${search}%`);
      paramCount++;
    }
    const countSql = `SELECT COUNT(*) as total FROM requests r ${where}`;
    const allowedSortBy = ['title', 'created_at', 'status', 'priority', 'requester_id', 'department_id'];
    let safeSortBy = allowedSortBy.includes(sortBy) ? `r.${sortBy}` : 'r.created_at';
    const safeSortOrder = sortOrder === 'ASC' ? 'ASC' : 'DESC';
    const dataSql = `
      SELECT r.* FROM requests r
      ${where}
      ORDER BY ${safeSortBy} ${safeSortOrder}
      LIMIT $${paramCount++} OFFSET $${paramCount++};
    `;
    const finalDataParams = [...params, limit, offset];
    const finalCountParams = [...params];
    const totalResult = await executeQuery(countSql, finalCountParams, this.testDataSource);
    const total = parseInt(totalResult.rows[0].total, 10);
    const dataResult = await executeQuery(dataSql, finalDataParams, this.testDataSource);
    return { data: dataResult.rows, total, page, limit };
  }

  /**
   * List all overtime requests with advanced filters for admin/global view
   */
  public async getAllOvertimeRequests(
    filters: {
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'ASC' | 'DESC';
      status?: string;
      departmentId?: number;
      requesterId?: number;
      startDate?: string;
      endDate?: string;
      search?: string;
    }
  ): Promise<{ data: any[]; total: number; page: number; limit: number }> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'created_at',
      sortOrder = 'DESC',
      status,
      departmentId,
      requesterId,
      startDate,
      endDate,
      search,
    } = filters;
    const offset = (page - 1) * limit;
    let where = `WHERE r.request_type = 'TIEMPO_EXTRA'`;
    const params: any[] = [];
    let paramCount = 1;
    if (status) {
      where += ` AND r.status = $${paramCount++}`;
      params.push(status);
    }
    if (departmentId) {
      where += ` AND r.department_id = $${paramCount++}`;
      params.push(departmentId);
    }
    if (requesterId) {
      where += ` AND r.requester_id = $${paramCount++}`;
      params.push(requesterId);
    }
    if (startDate) {
      where += ` AND r.created_at >= $${paramCount++}`;
      params.push(startDate);
    }
    if (endDate) {
      where += ` AND r.created_at <= $${paramCount++}`;
      params.push(endDate);
    }
    if (search) {
      where += ` AND (r.title ILIKE $${paramCount} OR r.description ILIKE $${paramCount})`;
      params.push(`%${search}%`);
      paramCount++;
    }
    const countSql = `SELECT COUNT(*) as total FROM requests r ${where}`;
    const allowedSortBy = ['title', 'created_at', 'status', 'priority', 'requester_id', 'department_id'];
    let safeSortBy = allowedSortBy.includes(sortBy) ? `r.${sortBy}` : 'r.created_at';
    const safeSortOrder = sortOrder === 'ASC' ? 'ASC' : 'DESC';
    const dataSql = `
      SELECT r.* FROM requests r
      ${where}
      ORDER BY ${safeSortBy} ${safeSortOrder}
      LIMIT $${paramCount++} OFFSET $${paramCount++};
    `;
    const finalDataParams = [...params, limit, offset];
    const finalCountParams = [...params];
    const totalResult = await executeQuery(countSql, finalCountParams, this.testDataSource);
    const total = parseInt(totalResult.rows[0].total, 10);
    const dataResult = await executeQuery(dataSql, finalDataParams, this.testDataSource);
    return { data: dataResult.rows, total, page, limit };
  }
}

// Utility to map Role enum to numeric role IDs (should be replaced with DB lookup in production)
const ROLE_ID_MAP: Record<string, number> = {
  [Role.Admin]: 1,
  [Role.Manager]: 2,
  // Add other roles as needed
};
function getRoleIds(roles: Role | Role[]): number[] {
  if (Array.isArray(roles)) {
    return roles.map(r => ROLE_ID_MAP[r]).filter(Boolean);
  }
  return [ROLE_ID_MAP[roles]].filter(Boolean);
}
