import { Repository, FindOptionsWhere } from 'typeorm';
import { AppDataSource } from '../data-source';
import { Notification, NotificationType, NotificationStatus, NotificationPriority } from '../entities/notification.entity';
import { User } from '../entities/user.entity';
import { Role } from '../entities/role.entity';

export interface NotificationCreateData {
  title: string;
  message: string;
  type?: NotificationType;
  priority?: NotificationPriority;
  user_id?: number;
  role_id?: number;
  metadata?: any;
  action_url?: string;
  action_label?: string;
  expires_at?: Date;
}

export interface NotificationUpdateData {
  title?: string;
  message?: string;
  type?: NotificationType;
  priority?: NotificationPriority;
  user_id?: number;
  role_id?: number;
  metadata?: any;
  action_url?: string;
  action_label?: string;
  expires_at?: Date;
}

export interface NotificationFilters {
  user_id?: number;
  role_id?: number;
  type?: NotificationType;
  status?: NotificationStatus;
  priority?: NotificationPriority;
  limit?: number;
  offset?: number;
  dateFrom?: Date;
  dateTo?: Date;
}

export interface TriggerNotificationInput {
  title: string;
  message: string;
  type?: NotificationType;
  priority?: NotificationPriority;
  userId?: number;
  roleId?: number | number[];
  metadata?: any;
  actionUrl?: string;
  actionLabel?: string;
  expiresAt?: Date;
}

export class NotificationService {
  private notificationRepository: Repository<Notification>;

  constructor() {
    // Verificar que AppDataSource esté inicializado antes de obtener el repositorio
    if (!AppDataSource.isInitialized) {
      throw new Error('NotificationService: AppDataSource must be initialized before creating service');
    }
    
    try {
      this.notificationRepository = AppDataSource.getRepository(Notification);
    } catch (error) {
      console.error('Error creating notification repository:', error);
      throw new Error('Failed to create notification repository');
    }
  }

  async createNotification(data: NotificationCreateData): Promise<Notification> {
    const notificationData: Partial<Notification> = {
      title: data.title,
      message: data.message,
      type: data.type || NotificationType.INFO,
      priority: data.priority || NotificationPriority.MEDIUM,
      metadata: data.metadata,
      action_url: data.action_url,
      action_label: data.action_label,
      expires_at: data.expires_at,
      status: NotificationStatus.UNREAD
    };

    if (data.user_id) {
      notificationData.user = { id: data.user_id } as User;
    }
    if (data.role_id) {
      notificationData.role = { id: data.role_id } as Role;
    }

    const notification = this.notificationRepository.create(notificationData);
    const savedNotification = await this.notificationRepository.save(notification);
    console.log('[NOTIF][DB] Notificación insertada en BD:', savedNotification);
    return savedNotification;
  }

  async getNotificationById(id: number): Promise<Notification | null> {
    return await this.notificationRepository.findOne({
      where: { id },
      relations: ['user', 'role']
    });
  }

  async getNotifications(filters: NotificationFilters = {}): Promise<Notification[]> {
    const where: FindOptionsWhere<Notification> = {};

    if (filters.user_id) where.user_id = filters.user_id;
    if (filters.role_id) where.role_id = filters.role_id;
    if (filters.type) where.type = filters.type;
    if (filters.status) where.status = filters.status;
    if (filters.priority) where.priority = filters.priority;

    const findOptions: any = {
      where,
      order: { created_at: 'DESC' },
    };

    if (filters.limit) findOptions.take = filters.limit;
    if (filters.offset) findOptions.skip = filters.offset;
    
    return this.notificationRepository.find(findOptions);
  }

  /**
   * Obtiene todas las notificaciones para un usuario, considerando sus roles, deduplicando por notification.id
   */
  async getNotificationsForUserWithRoles(userId: number, roleIds: number[], filters: NotificationFilters = {}): Promise<Notification[]> {
    // Construir condiciones dinámicas
    const params: any[] = [userId];
    let whereSql = '(n.user_id = $1';
    if (roleIds && roleIds.length > 0) {
      params.push(roleIds);
      whereSql += ' OR n.role_id = ANY($2)';
    }
    whereSql += ')';
    let filterIdx = params.length + 1;
    if (filters.status) {
      params.push(filters.status);
      whereSql += ` AND n.status = $${filterIdx}`;
      filterIdx++;
    }
    if (filters.type) {
      params.push(filters.type);
      whereSql += ` AND n.type = $${filterIdx}`;
      filterIdx++;
    }
    if (filters.priority) {
      params.push(filters.priority);
      whereSql += ` AND n.priority = $${filterIdx}`;
      filterIdx++;
    }
    // Consulta deduplicada por id
    const sql = `
      SELECT DISTINCT ON (n.id) n.*
      FROM notifications n
      WHERE ${whereSql}
      ORDER BY n.id DESC, n.created_at DESC
      LIMIT $${filterIdx}
    `;
    params.push(filters.limit || 100);
    const rows = await this.notificationRepository.query(sql, params);
    return rows;
  }

  async updateNotification(id: number, updateData: NotificationUpdateData): Promise<Notification | null> {
    const notification = await this.getNotificationById(id);
    if (!notification) return null;

    // Update fields
    if (updateData.title !== undefined) notification.title = updateData.title;
    if (updateData.message !== undefined) notification.message = updateData.message;
    if (updateData.type !== undefined) notification.type = updateData.type;
    if (updateData.priority !== undefined) notification.priority = updateData.priority;
    if (updateData.user_id !== undefined) notification.user_id = updateData.user_id;
    if (updateData.role_id !== undefined) notification.role_id = updateData.role_id;
    if (updateData.metadata !== undefined) notification.metadata = updateData.metadata;
    if (updateData.action_url !== undefined) notification.action_url = updateData.action_url;
    if (updateData.action_label !== undefined) notification.action_label = updateData.action_label;
    if (updateData.expires_at !== undefined) notification.expires_at = updateData.expires_at;

    return await this.notificationRepository.save(notification);
  }

  async deleteNotification(id: number, userId: number): Promise<boolean> {
    // Solo permite borrar si la notificación pertenece al usuario
    const notif = await this.notificationRepository.findOne({ where: { id, user_id: userId } });
    if (!notif) return false;
    const result = await this.notificationRepository.delete(id);
    return result.affected ? result.affected > 0 : false;
  }

  async markAsRead(id: number, userId: number): Promise<Notification | null> {
    const notification = await this.notificationRepository.findOne({ where: { id, user_id: userId } });
    if (!notification) return null;
    notification.markAsRead();
    return await this.notificationRepository.save(notification);
  }

  async markAllAsReadForUser(userId: number): Promise<void> {
    await this.notificationRepository
      .createQueryBuilder()
      .update(Notification)
      .set({ status: NotificationStatus.READ, read_at: new Date() })
      .where('user_id = :userId', { userId })
      .andWhere('status = :status', { status: NotificationStatus.UNREAD })
      .execute();
  }

  async markAllAsReadForRole(roleId: number): Promise<void> {
    await this.notificationRepository
      .createQueryBuilder()
      .update(Notification)
      .set({ status: NotificationStatus.READ, read_at: new Date() })
      .where('role_id = :roleId', { roleId })
      .andWhere('status = :status', { status: NotificationStatus.UNREAD })
      .execute();
  }

  async getNotificationStats(userId?: number, roleId?: number): Promise<{
    total: number;
    unread: number;
    read: number;
    byType: Record<string, number>;
    byPriority: Record<string, number>;
  }> {
    // Construir filtros dinámicos para la consulta agregada
    const qb = this.notificationRepository.createQueryBuilder('n');

    if (userId) {
      qb.andWhere('n.user_id = :userId', { userId });
    }
    if (roleId) {
      qb.andWhere('n.role_id = :roleId', { roleId });
    }

    // Agregar selecciones agregadas
    qb.select([
      'COUNT(*)::int                AS "total"',
      `SUM(CASE WHEN n.status = '${NotificationStatus.UNREAD}' THEN 1 ELSE 0 END)::int AS "unread"`,
      `SUM(CASE WHEN n.status = '${NotificationStatus.READ}' THEN 1 ELSE 0 END)::int   AS "read"`,
    ]);

    // Ejecutar la consulta principal para total, unread, read
    const rawStats = await qb.getRawOne<{ total: number; unread: number; read: number }>();

    // Consultas adicionales para agrupaciones por tipo y prioridad
    const byTypeQuery = this.notificationRepository
      .createQueryBuilder('n')
      .select(['n.type AS type', 'COUNT(*)::int AS count']);

    if (userId) {
      byTypeQuery.andWhere('n.user_id = :userId', { userId });
    }
    if (roleId) {
      byTypeQuery.andWhere('n.role_id = :roleId', { roleId });
    }

    const byTypeRows = await byTypeQuery
      .groupBy('n.type')
      .getRawMany<{ type: NotificationType; count: number }>();

    const byPriorityQuery = this.notificationRepository
      .createQueryBuilder('n')
      .select(['n.priority AS priority', 'COUNT(*)::int AS count']);

    if (userId) {
      byPriorityQuery.andWhere('n.user_id = :userId', { userId });
    }
    if (roleId) {
      byPriorityQuery.andWhere('n.role_id = :roleId', { roleId });
    }

    const byPriorityRows = await byPriorityQuery
      .groupBy('n.priority')
      .getRawMany<{ priority: NotificationPriority; count: number }>();

    const byType: Record<string, number> = {};
    byTypeRows.forEach(row => {
      byType[row.type] = Number(row.count);
    });

    const byPriority: Record<string, number> = {};
    byPriorityRows.forEach(row => {
      byPriority[row.priority] = Number(row.count);
    });

    return {
      total: rawStats?.total || 0,
      unread: rawStats?.unread || 0,
      read: rawStats?.read || 0,
      byType,
      byPriority,
    };
  }

  async cleanupExpiredNotifications(): Promise<number> {
    const result = await this.notificationRepository
      .createQueryBuilder()
      .delete()
      .from(Notification)
      .where('expires_at IS NOT NULL')
      .andWhere('expires_at < :now', { now: new Date() })
      .execute();

    return result.affected || 0;
  }

  async getUnreadNotificationsForUser(userId: number): Promise<Notification[]> {
    return await this.notificationRepository.find({
      where: {
        user_id: userId,
        status: NotificationStatus.UNREAD
      },
      order: {
        created_at: 'DESC'
      },
      relations: ['user', 'role']
    });
  }

  async sendBulkNotification(data: NotificationCreateData, userIds: number[]): Promise<Notification[]> {
    const notifications: Notification[] = [];

    for (const userId of userIds) {
      const notification = await this.createNotification({
        ...data,
        user_id: userId
      });
      notifications.push(notification);
    }

    return notifications;
  }

  async sendRoleNotification(data: NotificationCreateData, roleId: number): Promise<Notification> {
    return await this.createNotification({
      ...data,
      role_id: roleId
    });
  }

  /**
   * Limpiar notificaciones leídas más antiguas de una semana
   */
  async cleanupOldReadNotifications(): Promise<number> {
    try {
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

      // Solo eliminar notificaciones que ya fueron leídas y son más antiguas de una semana
      const result = await this.notificationRepository
        .createQueryBuilder()
        .delete()
        .where('status = :status', { status: NotificationStatus.READ })
        .andWhere('read_at < :date', { date: oneWeekAgo })
        .execute();

      return result.affected || 0;
    } catch (error) {
      console.error('Error eliminando notificaciones antiguas:', error);
      return 0;
    }
  }

  /**
   * Interfaz central para disparar notificaciones desde cualquier módulo
   * Ahora acepta roleId: number | number[] para soportar múltiples roles
   */
  async triggerNotification(input: TriggerNotificationInput & { roleId?: number | number[] }): Promise<Notification | Notification[]> {
    console.log('[NOTIF][TRACE] triggerNotification llamado con:', input);
    // 1. Validar datos mínimos
    if (!input.title || !input.message) {
      throw new Error('Faltan datos obligatorios para la notificación');
    }
    if (!input.userId && !input.roleId) {
      throw new Error('Se debe especificar userId o roleId');
    }

    // 2. Si es para uno o varios roles, crear notificación individual para cada usuario único
    if (input.roleId && !input.userId) {
      const roleIds = Array.isArray(input.roleId) ? input.roleId : [input.roleId];
      // Obtener usuarios activos con cualquiera de esos roles
      const userRepo = AppDataSource.getRepository(User);
      const userRoleRows = await userRepo
        .createQueryBuilder('u')
        .innerJoin('u.roles', 'r')
        .where('r.id IN (:...roleIds)', { roleIds })
        .andWhere('u.active = true')
        .getMany();
      if (!userRoleRows.length) {
        throw new Error('No hay usuarios activos con esos roles');
      }
      // Filtrar usuarios únicos por ID
      const uniqueUsers = Array.from(new Map(userRoleRows.map(u => [u.id, u])).values());
      // Crear notificación para cada usuario único
      const notifications: Notification[] = [];
      for (const user of uniqueUsers) {
        // Verificar si ya existe una notificación no leída para este usuario, producto y nivel
        let skip = false;
        if (input.metadata && input.metadata.productId) {
          // Buscar notificación no leída para este usuario, producto y título
          const notifExist = await this.notificationRepository.createQueryBuilder('n')
            .where('n.status = :status', { status: NotificationStatus.UNREAD })
            .andWhere('n.user_id = :userId', { userId: user.id })
            .andWhere('n.title = :title', { title: input.title })
            .andWhere('n.metadata::jsonb ->> :key = :value', { key: 'productId', value: input.metadata.productId.toString() })
            .getOne();
          if (notifExist) skip = true;
        }
        if (skip) continue;
        const notif = await this.createNotification({
          title: input.title,
          message: input.message,
          type: input.type,
          priority: input.priority,
          user_id: user.id,
          metadata: input.metadata,
          action_url: input.actionUrl,
          action_label: input.actionLabel,
          expires_at: input.expiresAt,
        });
        notifications.push(notif);
        // Emitir en tiempo real (WebSocket)
        try {
          const wsService = (global as any).getWebSocketService ? (global as any).getWebSocketService() : undefined;
          if (wsService) {
            await wsService.alertNewNotificationToUser(user.id, notif);
          }
        } catch (err) {
          console.warn('No se pudo emitir notificación en tiempo real:', err);
        }
      }
      return notifications;
    }

    // 3. Si es para un usuario individual
    const notification = await this.createNotification({
      title: input.title,
      message: input.message,
      type: input.type,
      priority: input.priority,
      user_id: input.userId,
      metadata: input.metadata,
      action_url: input.actionUrl,
      action_label: input.actionLabel,
      expires_at: input.expiresAt,
    });

    // 4. Emitir en tiempo real (WebSocket)
    try {
      const wsService = (global as any).getWebSocketService ? (global as any).getWebSocketService() : undefined;
      if (wsService && input.userId) {
        await wsService.alertNewNotificationToUser(input.userId, notification);
      }
    } catch (err) {
      // Loguear pero no fallar
      console.warn('No se pudo emitir notificación en tiempo real:', err);
    }

    return notification;
  }

  /**
   * Busca si ya existe una notificación no leída para un producto y nivel de stock (CRITICO o BAJO)
   */
  async findUnreadNotificationByProductAndLevel(productId: number, nivel: 'CRITICO' | 'BAJO'): Promise<Notification | null> {
    // El título contiene 'Stock CRÍTICO' o 'Stock bajo' y metadata.productId coincide
    const titleLike = nivel === 'CRITICO' ? 'Stock CRÍTICO%' : 'Stock bajo%';
    const notif = await this.notificationRepository.createQueryBuilder('n')
      .where('n.status = :status', { status: NotificationStatus.UNREAD })
      .andWhere('n.metadata::jsonb ->> :key = :value', { key: 'productId', value: productId.toString() })
      .andWhere('n.title LIKE :titleLike', { titleLike })
      .orderBy('n.created_at', 'DESC')
      .getOne();
    return notif || null;
  }

  /**
   * Marca como leídas todas las notificaciones duplicadas (por usuario, título y productId)
   */
  async markDuplicatesAsRead(userId: number, title: string, productId: string) {
    await this.notificationRepository
      .createQueryBuilder()
      .update(Notification)
      .set({ status: NotificationStatus.READ, read_at: new Date() })
      .where('user_id = :userId', { userId })
      .andWhere('title = :title', { title })
      .andWhere('metadata::jsonb ->> :key = :value', { key: 'productId', value: productId })
      .andWhere('status = :status', { status: NotificationStatus.UNREAD })
      .execute();
  }
} 