import axios from 'axios';
import { BadRequestException } from '../exceptions';

/**
 * @class RfcValidationService
 * @description Servicio para validar RFC con el SAT y obtener información fiscal.
 */
export class RfcValidationService {
  private apiUrl: string;
  private apiKey: string;

  constructor() {
    this.apiUrl = process.env.RFC_SAT_API_URL || 'https://api.rfc-sat.com/validate';
    this.apiKey = process.env.RFC_SAT_API_KEY || '';
    
    if (!this.apiKey) {
      console.warn('RFC_SAT_API_KEY no está configurada. La validación de RFC no funcionará correctamente.');
    }
  }

  /**
   * @description Valida un RFC con el servicio externo del SAT.
   * @param {string} rfc - El RFC a validar.
   * @returns {Promise<{ valid: boolean; data?: any }>} Resultado de la validación.
   */
  async validateRfc(rfc: string): Promise<{ valid: boolean; data?: any }> {
    try {
      // Validación básica del formato del RFC
      if (!this.isValidRfcFormat(rfc)) {
        return { 
          valid: false,
          data: { message: 'Formato de RFC inválido' }
        };
      }

      if (!this.apiKey) {
        throw new Error('API key para validación de RFC no configurada');
      }

      const response = await axios.get(`${this.apiUrl}/${rfc}`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      // Verificar respuesta según la estructura de la API utilizada
      if (response.data && response.data.valid) {
        return { 
          valid: true, 
          data: response.data 
        };
      }
      
      return { 
        valid: false,
        data: response.data
      };
    } catch (error: any) {
      console.error('Error validando RFC con SAT:', error.message);
      
      // Si el error es de la API (404, 400, etc.), puede contener información útil
      if (error.response) {
        return {
          valid: false,
          data: {
            message: 'Error en la validación del RFC',
            details: error.response.data
          }
        };
      }
      
      // En caso de error de conexión, no fallamos la operación
      return { 
        valid: false,
        data: { message: 'Error de conexión con el servicio de validación' }
      };
    }
  }

  /**
   * @description Obtiene información fiscal completa de un RFC.
   * @param {string} rfc - El RFC a consultar.
   * @returns {Promise<any>} Información fiscal del RFC.
   */
  async getFiscalInfo(rfc: string): Promise<any> {
    try {
      if (!this.apiKey) {
        throw new BadRequestException('API key para validación de RFC no configurada');
      }

      const response = await axios.get(`${this.apiUrl}/info/${rfc}`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data;
    } catch (error: any) {
      console.error('Error obteniendo información fiscal:', error.message);
      
      if (error.response) {
        throw new BadRequestException(
          error.response.data.message || 'Error al obtener información fiscal'
        );
      }
      
      throw new BadRequestException('Error de conexión con el servicio de validación');
    }
  }

  /**
   * @description Valida el formato de un RFC (validación local).
   * @param {string} rfc - El RFC a validar.
   * @returns {boolean} True si el formato es válido.
   */
  private isValidRfcFormat(rfc: string): boolean {
    // Formato para personas físicas (13 caracteres) o morales (12 caracteres)
    const rfcRegex = /^[A-Z&Ñ]{3,4}\d{6}[A-Z\d]{3}$/;
    return rfcRegex.test(rfc);
  }
} 