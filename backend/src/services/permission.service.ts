import { User } from '../entities/user.entity';
import { Role } from '../entities/role.entity';
import { Permission } from '../entities/permission.entity';

export class PermissionService {
  /**
   * Verifica si un usuario tiene un permiso específico
   */
  static hasPermission(user: User, permissionName: string): boolean {
    if (!user || !user.roles) {
      return false;
    }

    // Si el usuario tiene ROLE_ADMIN, permitir todo
    const hasAdminRole = user.roles.some((role: Role) => role.name === 'ROLE_ADMIN');
    if (hasAdminRole) {
      return true;
    }

    // Verificar si alguno de los roles del usuario tiene el permiso específico
    return user.roles.some((role: Role) => {
      if (!role.permissions) return false;
      return role.permissions.some((permission: Permission) => 
        permission.name === permissionName
      );
    });
  }

  /**
   * Verifica si un usuario tiene cualquiera de los permisos especificados
   */
  static hasAnyPermission(user: User, permissionNames: string[]): boolean {
    return permissionNames.some(permission => 
      this.hasPermission(user, permission)
    );
  }

  /**
   * Verifica si un usuario tiene todos los permisos especificados
   */
  static hasAllPermissions(user: User, permissionNames: string[]): boolean {
    return permissionNames.every(permission => 
      this.hasPermission(user, permission)
    );
  }

  /**
   * Verifica si un usuario tiene un rol específico
   */
  static hasRole(user: User, roleName: string): boolean {
    if (!user || !user.roles) {
      return false;
    }

    return user.roles.some((role: Role) => role.name === roleName);
  }

  /**
   * Verifica si un usuario tiene cualquiera de los roles especificados
   */
  static hasAnyRole(user: User, roleNames: string[]): boolean {
    return roleNames.some(roleName => this.hasRole(user, roleName));
  }

  /**
   * Obtiene todos los permisos únicos de un usuario
   */
  static getUserPermissions(user: User): string[] {
    if (!user || !user.roles) {
      return [];
    }

    const allPermissions = new Set<string>();
    
    user.roles.forEach((role: Role) => {
      if (role.permissions) {
        role.permissions.forEach((permission: Permission) => {
          allPermissions.add(permission.name);
        });
      }
    });

    return Array.from(allPermissions);
  }

  /**
   * Verifica si un usuario es administrador (tiene ROLE_ADMIN)
   */
  static isAdmin(user: User): boolean {
    return this.hasRole(user, 'ROLE_ADMIN');
  }

  /**
   * Verifica si un usuario puede acceder a un recurso específico
   * Combina verificación de roles y permisos
   */
  static canAccess(user: User, requiredRoles: string[] = [], requiredPermissions: string[] = []): boolean {
    // Si es admin, permitir acceso completo
    if (this.isAdmin(user)) {
      return true;
    }

    // Verificar roles si se especifican
    const hasRequiredRole = requiredRoles.length === 0 || this.hasAnyRole(user, requiredRoles);
    
    // Verificar permisos si se especifican
    const hasRequiredPermission = requiredPermissions.length === 0 || this.hasAllPermissions(user, requiredPermissions);

    return hasRequiredRole && hasRequiredPermission;
  }
}

export const permissionService = PermissionService; 