/*
 * Central FileService - stub implementation
 * Drivers: Local (default), S3 compatible
 * Real driver is selected via FILE_STORAGE env var (local|s3)
 */

import fs from 'fs/promises'
import path from 'path'
import { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3'
import { Readable } from 'stream'

export interface FileService {
  upload(buffer: Buffer, destination: string): Promise<string>
  download(filePath: string): Promise<Buffer>
  delete(filePath: string): Promise<void>
}

class LocalFileService implements FileService {
  private base = process.env.LOCAL_FILE_BASE || path.resolve(__dirname, '../../../uploads')
  async upload(buffer: Buffer, destination: string) {
    const destPath = path.join(this.base, destination)
    await fs.mkdir(path.dirname(destPath), { recursive: true })
    await fs.writeFile(destPath, buffer)
    return destPath
  }
  async download(filePath: string) {
    return fs.readFile(path.join(this.base, filePath))
  }
  async delete(filePath: string) {
    await fs.unlink(path.join(this.base, filePath))
  }
}

class S3FileService implements FileService {
  private client: S3Client
  private bucket: string

  constructor() {
    this.bucket = process.env.S3_BUCKET || 'comintec-files'
    this.client = new S3Client({
      endpoint: process.env.S3_ENDPOINT, // Para Backblaze B2 u otros S3-compatible
      region: process.env.S3_REGION || 'us-east-1',
      credentials: {
        accessKeyId: process.env.S3_ACCESS_KEY_ID!,
        secretAccessKey: process.env.S3_SECRET_ACCESS_KEY!
      }
    })
  }

  async upload(buffer: Buffer, destination: string): Promise<string> {
    const command = new PutObjectCommand({
      Bucket: this.bucket,
      Key: destination,
      Body: buffer,
      ContentType: this.getContentType(destination)
    })
    await this.client.send(command)
    return `s3://${this.bucket}/${destination}`
  }

  async download(filePath: string): Promise<Buffer> {
    const key = filePath.replace(`s3://${this.bucket}/`, '')
    const command = new GetObjectCommand({
      Bucket: this.bucket,
      Key: key
    })
    const response = await this.client.send(command)
    const stream = response.Body as Readable
    const chunks: Buffer[] = []
    for await (const chunk of stream) {
      chunks.push(chunk)
    }
    return Buffer.concat(chunks)
  }

  async delete(filePath: string): Promise<void> {
    const key = filePath.replace(`s3://${this.bucket}/`, '')
    const command = new DeleteObjectCommand({
      Bucket: this.bucket,
      Key: key
    })
    await this.client.send(command)
  }

  private getContentType(filename: string): string {
    const ext = path.extname(filename).toLowerCase()
    const types: Record<string, string> = {
      '.pdf': 'application/pdf',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    }
    return types[ext] || 'application/octet-stream'
  }
}

// Factory
export function createFileService(): FileService {
  const driver = process.env.FILE_STORAGE || 'local'
  switch (driver) {
    case 's3':
      return new S3FileService()
    case 'local':
    default:
      return new LocalFileService()
  }
} 