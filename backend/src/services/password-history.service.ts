import { Repository } from 'typeorm';
import { AppDataSource } from '../data-source';
import { PasswordHistory } from '../entities/password-history.entity';
import * as bcrypt from 'bcryptjs';

export class PasswordHistoryService {
  private passwordHistoryRepository: Repository<PasswordHistory> | null = null;

  constructor() {
    try {
      if (AppDataSource.isInitialized) {
        this.passwordHistoryRepository = AppDataSource.getRepository(PasswordHistory);
      }
    } catch (error) {
      console.warn('PasswordHistory entity not available, some features will be disabled:', error);
    }
  }

  /**
   * Verificar si una contraseña fue usada anteriormente por el usuario
   */
  async isPasswordReused(userId: number, newPassword: string, lastNPasswords: number = 5): Promise<boolean> {
    try {
      if (!this.passwordHistoryRepository) {
        // Si no está disponible, permitir el cambio (no reused)
        return false;
      }

      // Obtener las últimas N contraseñas del usuario
      const passwordHistory = await this.passwordHistoryRepository.find({
        where: { userId },
        order: { createdAt: 'DESC' },
        take: lastNPasswords,
      });

      // Verificar si la nueva contraseña coincide con alguna anterior
      for (const historyEntry of passwordHistory) {
        const isMatch = await bcrypt.compare(newPassword, historyEntry.passwordHash);
        if (isMatch) {
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error('Error verificando historial de contraseñas:', error);
      return false;
    }
  }

  /**
   * Agregar una nueva contraseña al historial
   */
  async addToHistory(
    userId: number,
    passwordHash: string,
    createdBy?: number,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    try {
      if (!this.passwordHistoryRepository) {
        // Si no está disponible, simplemente retornar sin error
        console.warn('PasswordHistory not available, skipping history tracking');
        return;
      }

      // Crear nueva entrada en el historial
      const historyEntry = this.passwordHistoryRepository.create({
        userId,
        passwordHash,
        createdBy,
        ipAddress,
        userAgent,
      });

      await this.passwordHistoryRepository.save(historyEntry);

      // Limpiar historial antiguo (mantener solo las últimas 10 contraseñas)
      await this.cleanOldPasswords(userId, 10);
    } catch (error) {
      console.error('Error agregando contraseña al historial:', error);
      // En tests o cuando la entidad no está disponible, no lanzar error
      if (this.passwordHistoryRepository) {
        throw new Error('Error interno del servidor al gestionar historial de contraseñas');
      }
    }
  }

  /**
   * Limpiar contraseñas antiguas del historial
   */
  private async cleanOldPasswords(userId: number, keepCount: number): Promise<void> {
    try {
      if (!this.passwordHistoryRepository) {
        return;
      }

      // Obtener todas las contraseñas del usuario ordenadas por fecha
      const allPasswords = await this.passwordHistoryRepository.find({
        where: { userId },
        order: { createdAt: 'DESC' },
      });

      // Si hay más contraseñas de las que queremos mantener, eliminar las más antiguas
      if (allPasswords.length > keepCount) {
        const passwordsToDelete = allPasswords.slice(keepCount);
        const idsToDelete = passwordsToDelete.map(p => p.id);
        
        await this.passwordHistoryRepository.delete(idsToDelete);
      }
    } catch (error) {
      console.error('Error limpiando historial de contraseñas:', error);
      // No lanzar error aquí para no interrumpir el flujo principal
    }
  }

  /**
   * Obtener estadísticas del historial de contraseñas de un usuario
   */
  async getPasswordStats(userId: number): Promise<{
    totalPasswordChanges: number;
    lastPasswordChange?: Date;
    averageDaysBetweenChanges?: number;
  }> {
    try {
      if (!this.passwordHistoryRepository) {
        return { totalPasswordChanges: 0 };
      }

      const passwordHistory = await this.passwordHistoryRepository.find({
        where: { userId },
        order: { createdAt: 'DESC' },
      });

      const totalPasswordChanges = passwordHistory.length;
      const lastPasswordChange = passwordHistory.length > 0 ? passwordHistory[0].createdAt : undefined;

      let averageDaysBetweenChanges: number | undefined;
      if (passwordHistory.length > 1) {
        const totalDays = passwordHistory.reduce((acc, curr, index) => {
          if (index === 0) return acc;
          const previousDate = passwordHistory[index - 1].createdAt;
          const currentDate = curr.createdAt;
          const daysDiff = Math.abs((previousDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24));
          return acc + daysDiff;
        }, 0);
        
        averageDaysBetweenChanges = Math.round(totalDays / (passwordHistory.length - 1));
      }

      return {
        totalPasswordChanges,
        lastPasswordChange,
        averageDaysBetweenChanges,
      };
    } catch (error) {
      console.error('Error obteniendo estadísticas de contraseñas:', error);
      return {
        totalPasswordChanges: 0,
      };
    }
  }

  /**
   * Obtener el historial de cambios de contraseña de un usuario (solo metadatos)
   */
  async getPasswordHistory(userId: number, limit: number = 10): Promise<{
    id: number;
    createdAt: Date;
    createdBy?: number;
    ipAddress?: string;
    userAgent?: string;
  }[]> {
    try {
      if (!this.passwordHistoryRepository) {
        return [];
      }

      const history = await this.passwordHistoryRepository.find({
        where: { userId },
        order: { createdAt: 'DESC' },
        take: limit,
        select: ['id', 'createdAt', 'createdBy', 'ipAddress', 'userAgent'],
      });

      return history;
    } catch (error) {
      console.error('Error obteniendo historial de contraseñas:', error);
      return [];
    }
  }

  /**
   * Limpiar todo el historial de un usuario (usado para eliminación de cuenta)
   */
  async clearUserHistory(userId: number): Promise<void> {
    try {
      if (!this.passwordHistoryRepository) {
        return;
      }

      await this.passwordHistoryRepository.delete({ userId });
    } catch (error) {
      console.error('Error limpiando historial de usuario:', error);
      if (this.passwordHistoryRepository) {
        throw new Error('Error interno del servidor al limpiar historial');
      }
    }
  }
} 