import { DataSource, Repository, In } from 'typeorm';
import { InternalRequest, RequestType, RequestStatus, ApprovalLevel } from '../entities/internal-request.entity';
import { User } from '../entities/user.entity';
import { NotificationService } from './notification.service';
import { NotificationType } from '../entities/notification.entity';
import {
  CreateInternalRequestDto,
  UpdateInternalRequestDto,
  SubmitInternalRequestDto,
  ApproveInternalRequestDto,
  RejectInternalRequestDto,
  RequestInfoInternalRequestDto,
  InternalRequestFilterDto,
  InternalRequestResponseDto
} from '../dto/internal-request.dto';

export class InternalRequestService {
  private requestRepository: Repository<InternalRequest>;
  private userRepository: Repository<User>;

  constructor(
    dataSource: DataSource,
    private notificationService: NotificationService
  ) {
    this.requestRepository = dataSource.getRepository(InternalRequest);
    this.userRepository = dataSource.getRepository(User);
  }

  /**
   * Crear una nueva solicitud interna
   */
  async createRequest(
    createDto: CreateInternalRequestDto,
    requesterId: number,
    creatorId: number
  ): Promise<InternalRequest> {
    const requester = await this.userRepository.findOne({
      where: { id: requesterId }
    });

    if (!requester) {
      throw new Error('Solicitante no encontrado');
    }

    const request = this.requestRepository.create({
      ...createDto,
      requesterId,
      createdById: creatorId,
      updatedById: creatorId,
      status: RequestStatus.DRAFT,
      currentApprovalLevel: this.getInitialApprovalLevel(createDto.requestType),
      attachments: createDto.attachments?.join(',') || undefined
    });

    return await this.requestRepository.save(request);
  }

  /**
   * Obtener solicitudes con filtros y paginación
   */
  async getRequests(filterDto: InternalRequestFilterDto): Promise<{
    data: InternalRequestResponseDto[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    const {
      requestType,
      status,
      requesterId,
      approverId,
      startDate,
      endDate,
      isUrgent,
      priority,
      search,
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'DESC'
    } = filterDto;

    const queryBuilder = this.requestRepository
      .createQueryBuilder('request')
      .leftJoinAndSelect('request.requester', 'requester')
      .leftJoinAndSelect('request.approver', 'approver')
      .leftJoinAndSelect('request.createdBy', 'createdBy')
      .leftJoinAndSelect('request.updatedBy', 'updatedBy');

    // Aplicar filtros
    if (requestType) {
      queryBuilder.andWhere('request.requestType = :requestType', { requestType });
    }

    if (status) {
      queryBuilder.andWhere('request.status = :status', { status });
    }

    if (requesterId) {
      queryBuilder.andWhere('request.requesterId = :requesterId', { requesterId });
    }

    if (approverId) {
      queryBuilder.andWhere('request.approverId = :approverId', { approverId });
    }

    if (startDate && endDate) {
      queryBuilder.andWhere('request.createdAt BETWEEN :startDate AND :endDate', {
        startDate,
        endDate
      });
    }

    if (isUrgent !== undefined) {
      queryBuilder.andWhere('request.isUrgent = :isUrgent', { isUrgent });
    }

    if (priority) {
      queryBuilder.andWhere('request.priority = :priority', { priority });
    }

    if (search) {
      queryBuilder.andWhere(
        '(request.title ILIKE :search OR request.description ILIKE :search OR requester.name ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Contar total
    const total = await queryBuilder.getCount();

    // Aplicar ordenamiento y paginación
    queryBuilder
      .orderBy(`request.${sortBy}`, sortOrder)
      .skip((page - 1) * limit)
      .take(limit);

    const requests = await queryBuilder.getMany();

    // Transformar a DTO de respuesta
    const responseData: InternalRequestResponseDto[] = requests.map(request => ({
      id: request.id,
      requestType: request.requestType,
      status: request.status,
      title: request.title,
      description: request.description,
      estimatedCost: request.estimatedCost,
      approvedAmount: request.approvedAmount,
      startDate: request.startDate,
      endDate: request.endDate,
      requestData: request.requestData,
      approvalHistory: request.approvalHistory,
      rejectionReason: request.rejectionReason,
      attachments: request.attachments,
      currentApprovalLevel: request.currentApprovalLevel,
      isUrgent: request.isUrgent,
      priority: request.priority,
      requesterId: request.requesterId,
      approverId: request.approverId,
      createdById: request.createdById,
      updatedById: request.updatedById,
      createdAt: request.createdAt,
      updatedAt: request.updatedAt,
      submittedAt: request.submittedAt,
      approvedAt: request.approvedAt,
      rejectedAt: request.rejectedAt,
      requester: request.requester ? {
        id: request.requester.id,
        name: request.requester.name,
        email: request.requester.email
      } : undefined,
      approver: request.approver ? {
        id: request.approver.id,
        name: request.approver.name,
        email: request.approver.email
      } : undefined,
      createdBy: request.createdBy ? {
        id: request.createdBy.id,
        name: request.createdBy.name,
        email: request.createdBy.email
      } : undefined,
      updatedBy: request.updatedBy ? {
        id: request.updatedBy.id,
        name: request.updatedBy.name,
        email: request.updatedBy.email
      } : undefined
    }));

    return {
      data: responseData,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Obtener una solicitud por ID
   */
  async getRequestById(id: number): Promise<InternalRequest> {
    const request = await this.requestRepository.findOne({
      where: { id },
      relations: ['requester', 'approver', 'createdBy', 'updatedBy']
    });

    if (!request) {
      throw new Error('Solicitud no encontrada');
    }

    return request;
  }

  /**
   * Actualizar una solicitud
   */
  async updateRequest(
    id: number,
    updateDto: UpdateInternalRequestDto,
    updaterId: number
  ): Promise<InternalRequest> {
    const request = await this.getRequestById(id);

    // Solo se puede editar si está en borrador
    if (request.status !== RequestStatus.DRAFT) {
      throw new Error('Solo se pueden editar solicitudes en borrador');
    }

    // Verificar que el usuario sea el solicitante o un administrador
    if (request.requesterId !== updaterId) {
      const updater = await this.userRepository.findOne({
        where: { id: updaterId },
        relations: ['roles']
      });

      const isAdmin = updater?.roles?.some(role => 
        role.name === 'ROLE_ADMIN' || role.name === 'ROLE_FULLADMIN'
      );

      if (!isAdmin) {
        throw new Error('No tienes permisos para editar esta solicitud');
      }
    }

    // Actualizar campos
    Object.assign(request, {
      ...updateDto,
      updatedById: updaterId,
      attachments: updateDto.attachments?.join(',') || request.attachments
    });

    return await this.requestRepository.save(request);
  }

  /**
   * Enviar una solicitud para aprobación
   */
  async submitRequest(
    id: number,
    submitDto: SubmitInternalRequestDto,
    submitterId: number
  ): Promise<InternalRequest> {
    const request = await this.getRequestById(id);

    if (!request.canBeSubmitted()) {
      throw new Error('La solicitud no puede ser enviada en su estado actual');
    }

    if (request.requesterId !== submitterId) {
      throw new Error('Solo el solicitante puede enviar la solicitud');
    }

    // Determinar el aprobador
    const approver = await this.determineApprover(request);
    if (!approver) {
      throw new Error('No se pudo determinar el aprobador para esta solicitud');
    }

    // Actualizar estado
    request.status = RequestStatus.SUBMITTED;
    request.approverId = approver.id;
    request.submittedAt = new Date();
    request.updatedById = submitterId;

    // Agregar comentario de envío si existe
    if (submitDto.comment) {
      request.addApprovalHistory(
        request.currentApprovalLevel,
        submitterId,
        (await this.userRepository.findOne({ where: { id: submitterId } }))?.name || 'Usuario',
        'APPROVED',
        submitDto.comment
      );
    }

    const savedRequest = await this.requestRepository.save(request);

    // Enviar notificación al aprobador
    await this.notificationService.sendBulkNotification({
      title: 'Nueva solicitud pendiente de aprobación',
      message: `Tienes una nueva solicitud de ${request.requestType} pendiente de aprobación: ${request.title}`,
      type: NotificationType.TASK,
      metadata: {
        requestId: request.id,
        requestType: request.requestType,
        requesterName: request.requester.name
      }
    }, [approver.id]);

    return savedRequest;
  }

  /**
   * Aprobar una solicitud
   */
  async approveRequest(
    id: number,
    approveDto: ApproveInternalRequestDto,
    approverId: number
  ): Promise<InternalRequest> {
    const request = await this.getRequestById(id);

    if (!request.canBeApproved()) {
      throw new Error('La solicitud no puede ser aprobada en su estado actual');
    }

    if (request.approverId !== approverId) {
      throw new Error('No tienes permisos para aprobar esta solicitud');
    }

    // Verificar si necesita aprobación adicional
    const nextLevel = this.getNextApprovalLevel(request.currentApprovalLevel);
    
    if (nextLevel) {
      // Pasar al siguiente nivel de aprobación
      const nextApprover = await this.findApproverForLevel(nextLevel);
      if (nextApprover) {
        request.currentApprovalLevel = nextLevel;
        request.approverId = nextApprover.id;
        request.status = RequestStatus.PENDING_APPROVAL;
        
        // Agregar al historial
        request.addApprovalHistory(
          request.currentApprovalLevel,
          approverId,
          (await this.userRepository.findOne({ where: { id: approverId } }))?.name || 'Usuario',
          'APPROVED',
          approveDto.comment
        );

        const savedRequest = await this.requestRepository.save(request);

        // Notificar al siguiente aprobador
        await this.notificationService.sendBulkNotification({
          title: 'Solicitud requiere aprobación adicional',
          message: `Una solicitud de ${request.requestType} requiere tu aprobación: ${request.title}`,
          type: NotificationType.TASK,
          metadata: {
            requestId: request.id,
            requestType: request.requestType,
            requesterName: request.requester.name
          }
        }, [nextApprover.id]);

        return savedRequest;
      }
    }

    // Aprobación final
    request.status = RequestStatus.APPROVED;
    request.approvedAmount = approveDto.approvedAmount || request.estimatedCost;
    request.approvedAt = new Date();
    request.updatedById = approverId;

    // Agregar al historial
    request.addApprovalHistory(
      request.currentApprovalLevel,
      approverId,
      (await this.userRepository.findOne({ where: { id: approverId } }))?.name || 'Usuario',
      'APPROVED',
      approveDto.comment
    );

    const savedRequest = await this.requestRepository.save(request);

    // Notificar al solicitante
    await this.notificationService.sendBulkNotification({
      title: 'Solicitud aprobada',
      message: `Tu solicitud de ${request.requestType} ha sido aprobada: ${request.title}`,
      type: NotificationType.SUCCESS,
      metadata: {
        requestId: request.id,
        requestType: request.requestType,
        approvedAmount: request.approvedAmount
      }
    }, [request.requesterId]);

    return savedRequest;
  }

  /**
   * Rechazar una solicitud
   */
  async rejectRequest(
    id: number,
    rejectDto: RejectInternalRequestDto,
    rejecterId: number
  ): Promise<InternalRequest> {
    const request = await this.getRequestById(id);

    if (!request.canBeRejected()) {
      throw new Error('La solicitud no puede ser rechazada en su estado actual');
    }

    if (request.approverId !== rejecterId) {
      throw new Error('No tienes permisos para rechazar esta solicitud');
    }

    request.status = RequestStatus.REJECTED;
    request.rejectionReason = rejectDto.rejectionReason;
    request.rejectedAt = new Date();
    request.updatedById = rejecterId;

    // Agregar al historial
    request.addApprovalHistory(
      request.currentApprovalLevel,
      rejecterId,
      (await this.userRepository.findOne({ where: { id: rejecterId } }))?.name || 'Usuario',
      'REJECTED',
      rejectDto.comment
    );

    const savedRequest = await this.requestRepository.save(request);

    // Notificar al solicitante
    await this.notificationService.sendBulkNotification({
      title: 'Solicitud rechazada',
      message: `Tu solicitud de ${request.requestType} ha sido rechazada: ${request.title}`,
      type: NotificationType.ERROR,
      metadata: {
        requestId: request.id,
        requestType: request.requestType,
        rejectionReason: request.rejectionReason
      }
    }, [request.requesterId]);

    return savedRequest;
  }

  /**
   * Solicitar información adicional
   */
  async requestInfo(
    id: number,
    infoDto: RequestInfoInternalRequestDto,
    requesterId: number
  ): Promise<InternalRequest> {
    const request = await this.getRequestById(id);

    if (request.approverId !== requesterId) {
      throw new Error('No tienes permisos para solicitar información de esta solicitud');
    }

    // Agregar al historial
    request.addApprovalHistory(
      request.currentApprovalLevel,
      requesterId,
      (await this.userRepository.findOne({ where: { id: requesterId } }))?.name || 'Usuario',
      'REQUEST_INFO',
      infoDto.comment
    );

    const savedRequest = await this.requestRepository.save(request);

    // Notificar al solicitante
    await this.notificationService.sendBulkNotification({
      title: 'Información adicional requerida',
      message: `Se requiere información adicional para tu solicitud de ${request.requestType}: ${request.title}`,
      type: NotificationType.WARNING,
      metadata: {
        requestId: request.id,
        requestType: request.requestType,
        comment: infoDto.comment
      }
    }, [request.requesterId]);

    return savedRequest;
  }

  /**
   * Cancelar una solicitud
   */
  async cancelRequest(id: number, cancellerId: number): Promise<InternalRequest> {
    const request = await this.getRequestById(id);

    if (!request.canBeCancelled()) {
      throw new Error('La solicitud no puede ser cancelada en su estado actual');
    }

    if (request.requesterId !== cancellerId) {
      throw new Error('Solo el solicitante puede cancelar la solicitud');
    }

    request.status = RequestStatus.CANCELLED;
    request.updatedById = cancellerId;

    const savedRequest = await this.requestRepository.save(request);

    // Notificar al aprobador si existe
    if (request.approverId) {
      await this.notificationService.sendBulkNotification({
        title: 'Solicitud cancelada',
        message: `Una solicitud de ${request.requestType} ha sido cancelada: ${request.title}`,
        type: NotificationType.INFO,
        metadata: {
          requestId: request.id,
          requestType: request.requestType,
          requesterName: request.requester.name
        }
      }, [request.approverId]);
    }

    return savedRequest;
  }

  /**
   * Obtener estadísticas de solicitudes
   */
  async getRequestStats(): Promise<{
    total: number;
    byStatus: Record<RequestStatus, number>;
    byType: Record<RequestType, number>;
    pendingApproval: number;
    urgent: number;
  }> {
    const total = await this.requestRepository.count();

    const byStatus = await this.requestRepository
      .createQueryBuilder('request')
      .select('request.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('request.status')
      .getRawMany();

    const byType = await this.requestRepository
      .createQueryBuilder('request')
      .select('request.requestType', 'type')
      .addSelect('COUNT(*)', 'count')
      .groupBy('request.requestType')
      .getRawMany();

    const pendingApproval = await this.requestRepository.count({
      where: { status: RequestStatus.PENDING_APPROVAL }
    });

    const urgent = await this.requestRepository.count({
      where: { isUrgent: true, status: In([RequestStatus.SUBMITTED, RequestStatus.PENDING_APPROVAL]) }
    });

    return {
      total,
      byStatus: byStatus.reduce((acc, item) => {
        acc[item.status] = parseInt(item.count);
        return acc;
      }, {} as Record<RequestStatus, number>),
      byType: byType.reduce((acc, item) => {
        acc[item.type] = parseInt(item.count);
        return acc;
      }, {} as Record<RequestType, number>),
      pendingApproval,
      urgent
    };
  }

  // Métodos privados de utilidad
  private getInitialApprovalLevel(requestType: RequestType): ApprovalLevel {
    switch (requestType) {
      case RequestType.VIATICOS:
        return ApprovalLevel.MANAGER;
      case RequestType.TIEMPO_EXTRA:
        return ApprovalLevel.MANAGER;
      case RequestType.PAQUETERIA:
        return ApprovalLevel.MANAGER;
      default:
        return ApprovalLevel.MANAGER;
    }
  }

  private getNextApprovalLevel(currentLevel: ApprovalLevel): ApprovalLevel | null {
    switch (currentLevel) {
      case ApprovalLevel.MANAGER:
        return ApprovalLevel.DIRECTOR;
      case ApprovalLevel.DIRECTOR:
        return ApprovalLevel.ADMIN;
      case ApprovalLevel.ADMIN:
        return null; // No hay siguiente nivel
      default:
        return null;
    }
  }

  private async determineApprover(request: InternalRequest): Promise<User | null> {
    // Lógica para determinar el aprobador basado en el tipo de solicitud y nivel
    // Por ahora, buscar usuarios con roles de aprobación
    const approvers = await this.userRepository
      .createQueryBuilder('user')
      .leftJoin('user.roles', 'role')
      .where('role.name IN (:...roles)', {
        roles: ['ROLE_MANAGER', 'ROLE_DIRECTOR', 'ROLE_ADMIN']
      })
      .getMany();

    // Priorizar por nivel de aprobación
    const levelPriorities = {
      [ApprovalLevel.MANAGER]: ['ROLE_MANAGER'],
      [ApprovalLevel.DIRECTOR]: ['ROLE_DIRECTOR'],
      [ApprovalLevel.ADMIN]: ['ROLE_ADMIN', 'ROLE_FULLADMIN']
    };

    const requiredRoles = levelPriorities[request.currentApprovalLevel];
    const approver = approvers.find(user => 
      user.roles?.some(role => requiredRoles.includes(role.name))
    );

    return approver || null;
  }

  private async findApproverForLevel(level: ApprovalLevel): Promise<User | null> {
    const levelRoles = {
      [ApprovalLevel.MANAGER]: ['ROLE_MANAGER'],
      [ApprovalLevel.DIRECTOR]: ['ROLE_DIRECTOR'],
      [ApprovalLevel.ADMIN]: ['ROLE_ADMIN', 'ROLE_FULLADMIN']
    };

    const approver = await this.userRepository
      .createQueryBuilder('user')
      .leftJoin('user.roles', 'role')
      .where('role.name IN (:...roles)', {
        roles: levelRoles[level]
      })
      .getOne();

    return approver;
  }
}