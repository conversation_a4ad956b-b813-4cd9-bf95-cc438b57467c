import { CreateContractRepseDto, UpdateContractRepseDto, AdminPaginationQueryDto } from '../dtos/admin.dto';
import { query } from './db.service';
import { HttpException } from '../exceptions/http.exception';
import { IUser as User } from '../interfaces/user.interface';
// import { NotificationService } from './notification.service'; // Asumimos que se creará o existe

export class ContractRepseService {
  private readonly tableName = 'contracts';
  // private notificationService: NotificationService;

  constructor() {
    // this.notificationService = new NotificationService();
  }

  public async createContract(data: CreateContractRepseDto, currentUser: User): Promise<any> {
    const { contractNumber, customerId, projectId, startDate, endDate, totalAmount, status, termsConditions } = data;

    const customerCheck = await query(`SELECT id FROM customers WHERE id = $1`, [customerId]);
    if (customerCheck.rows.length === 0) {
      throw new HttpException(404, `El cliente con ID ${customerId} no existe.`);
    }
    if (projectId) {
      const projectCheck = await query(`SELECT id FROM projects WHERE id = $1`, [projectId]);
      if (projectCheck.rows.length === 0) {
        throw new HttpException(404, `El proyecto con ID ${projectId} no existe.`);
      }
    }

    const sql = `
      INSERT INTO ${this.tableName}
        (contract_number, customer_id, project_id, contract_type, start_date, end_date,
         total_amount, status, terms_conditions, created_by, created_at, updated_at)
      VALUES
        ($1, $2, $3, 'REPSE', $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *;
    `;
    try {
      const result = await query(sql, [
        contractNumber,
        customerId,
        projectId,
        startDate,
        endDate,
        totalAmount,
        status || 'ACTIVE',
        termsConditions,
        currentUser.id,
      ]);
      if (result.rows.length === 0) {
        throw new HttpException(500, 'No se pudo crear el contrato REPSE.');
      }
      return this.getContractById(result.rows[0].id);
    } catch (error) {
      console.error('Error in createContract:', error);
      if (error instanceof HttpException) throw error;
      if (error && typeof error === 'object' && 'code' in error) {
        const err = error as { code: string };
        if (err.code === '23505') {
          throw new HttpException(409, `El número de contrato '${contractNumber}' ya existe.`);
        }
        if (err.code === '23503') {
            throw new HttpException(400, `Error de referencia: Verifique IDs de cliente o proyecto.`);
        }
      }
      if (error instanceof Error) {
        throw new HttpException(500, `Error al crear el contrato REPSE: ${error.message}`);
      }
      throw new HttpException(500, 'Error al crear el contrato REPSE: error desconocido.');
    }
  }

  public async getContractById(id: number): Promise<any> {
    const sql = `
      SELECT
        c.*,
        cust.razon_social as customer_name,
        p.name as project_name,
        u.name as created_by_username,
        upd_u.name as updated_by_username
      FROM ${this.tableName} c
      JOIN customers cust ON c.customer_id = cust.id
      LEFT JOIN projects p ON c.project_id = p.id
      LEFT JOIN users u ON c.created_by = u.id
      LEFT JOIN users upd_u ON c.updated_by = upd_u.id
      WHERE c.id = $1 AND c.contract_type = 'REPSE';
    `;
    try {
      const result = await query(sql, [id]);
      if (result.rows.length === 0) {
        throw new HttpException(404, 'Contrato REPSE no encontrado.');
      }
      return result.rows[0];
    } catch (error) {
      console.error('Error in getContractById:', error);
      if (error instanceof HttpException) throw error;
      if (error instanceof Error) {
        throw new HttpException(500, `Error al obtener el contrato REPSE: ${error.message}`);
      }
      throw new HttpException(500, 'Error al obtener el contrato REPSE: error desconocido.');
    }
  }

  public async getAllContracts(paginationQuery: AdminPaginationQueryDto): Promise<{ data: any[], total: number, page: number, limit: number }> {
    const { page = 1, limit = 10, sortBy = 'created_at', sortOrder = 'DESC', search } = paginationQuery;
    const offset = (page - 1) * limit;

    let selectClause = `c.*, cust.razon_social as customer_name, p.name as project_name, u.name as created_by_username, upd_u.name as updated_by_username`;
    let baseFrom = `
      FROM ${this.tableName} c
      JOIN customers cust ON c.customer_id = cust.id
      LEFT JOIN projects p ON c.project_id = p.id
      LEFT JOIN users u ON c.created_by = u.id
      LEFT JOIN users upd_u ON c.updated_by = upd_u.id
    `;
    let whereClause = " WHERE c.contract_type = 'REPSE' ";

    const params: any[] = [];
    let paramCount = 1;

    if (search) {
      whereClause += ` AND (c.contract_number ILIKE $${paramCount} OR cust.razon_social ILIKE $${paramCount} OR p.name ILIKE $${paramCount} OR c.status ILIKE $${paramCount})`;
      params.push(`%${search}%`);
      paramCount++;
    }

    const countSql = `SELECT COUNT(c.id) as total ${baseFrom} ${whereClause}`;

    const allowedSortBy = ['contract_number', 'customer_name', 'project_name', 'start_date', 'end_date', 'status', 'created_at'];
    let safeSortBy = 'c.created_at';
    if (allowedSortBy.includes(sortBy)) {
        if (sortBy === 'customer_name') safeSortBy = 'cust.razon_social';
        else if (sortBy === 'project_name') safeSortBy = 'p.name';
        else safeSortBy = `c.${sortBy}`;
    }
    const safeSortOrder = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    const dataSql = `
      SELECT ${selectClause}
      ${baseFrom}
      ${whereClause}
      ORDER BY ${safeSortBy} ${safeSortOrder}
      LIMIT $${paramCount++} OFFSET $${paramCount++};
    `;
    const finalDataParams = [...params, limit, offset];
    const finalCountParams = [...params];

    try {
      const totalResult = await query(countSql, finalCountParams);
      const total = parseInt(totalResult.rows[0].total, 10);

      const dataResult = await query(dataSql, finalDataParams);
      return { data: dataResult.rows, total, page, limit };
    } catch (error) {
      console.error('Error in getAllContracts:', error);
      if (error instanceof Error) {
        throw new HttpException(500, `Error al obtener los contratos REPSE: ${error.message}`);
      }
      throw new HttpException(500, 'Error al obtener los contratos REPSE: error desconocido.');
    }
  }

  public async updateContract(id: number, data: UpdateContractRepseDto, currentUser: User): Promise<any> {
    const fieldsToUpdate: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    if (data.customerId) {
      const customerCheck = await query(`SELECT id FROM customers WHERE id = $1`, [data.customerId]);
      if (customerCheck.rows.length === 0) {
        throw new HttpException(404, `El cliente con ID ${data.customerId} no existe.`);
      }
    }
    if (data.projectId !== undefined) { // projectId puede ser null
      if (data.projectId !== null) {
        const projectCheck = await query(`SELECT id FROM projects WHERE id = $1`, [data.projectId]);
        if (projectCheck.rows.length === 0) {
          throw new HttpException(404, `El proyecto con ID ${data.projectId} no existe.`);
        }
      }
    }


    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        fieldsToUpdate.push(`${snakeKey} = $${paramCount++}`);
        values.push(value);
      }
    });

    if (fieldsToUpdate.length === 0) {
      return this.getContractById(id);
    }

    fieldsToUpdate.push(`updated_at = CURRENT_TIMESTAMP`);
    fieldsToUpdate.push(`updated_by = $${paramCount++}`);
    values.push(currentUser.id);


    const sql = `
      UPDATE ${this.tableName}
      SET ${fieldsToUpdate.join(', ')}
      WHERE id = $${paramCount++} AND contract_type = 'REPSE'
      RETURNING id;
    `;
    values.push(id);

    try {
      const result = await query(sql, values);
      if (result.rows.length === 0) {
        throw new HttpException(404, 'Contrato REPSE no encontrado o no se pudo actualizar.');
      }
      return this.getContractById(result.rows[0].id);
    } catch (error) {
      console.error('Error in updateContract:', error);
      if (error instanceof HttpException) throw error;
      if (error && typeof error === 'object' && 'code' in error) {
        const err = error as { code: string };
        if (err.code === '23505') {
          throw new HttpException(409, `Error de unicidad al actualizar el contrato.`);
        }
      }
      if (error instanceof Error) {
        throw new HttpException(500, `Error al actualizar el contrato REPSE: ${error.message}`);
      }
      throw new HttpException(500, 'Error al actualizar el contrato REPSE: error desconocido.');
    }
  }

  public async deleteContract(id: number): Promise<void> {
    const sql = `DELETE FROM ${this.tableName} WHERE id = $1 AND contract_type = 'REPSE';`;
    try {
      const result = await query(sql, [id]);
      if (result.rowCount === 0) {
        throw new HttpException(404, 'Contrato REPSE no encontrado.');
      }
    } catch (error) {
      console.error('Error in deleteContract:', error);
      if (error instanceof HttpException) throw error;
      if (error instanceof Error) {
        throw new HttpException(500, `Error al eliminar el contrato REPSE: ${error.message}`);
      }
      throw new HttpException(500, 'Error al eliminar el contrato REPSE: error desconocido.');
    }
  }

  public async findContractsNearingExpiry(daysUntilExpiry: number): Promise<any[]> {
    const sql = `
      SELECT
        c.id, c.contract_number, c.end_date, c.customer_id, cust.razon_social as customer_name,
        c.project_id, p.name as project_name,
        COALESCE(p_manager.id, c_creator.id) as user_to_notify_id,
        COALESCE(p_manager.email, c_creator.email) as user_to_notify_email,
        (SELECT id FROM departments WHERE name = 'Ventas' LIMIT 1) as sales_department_id
      FROM ${this.tableName} c
      JOIN customers cust ON c.customer_id = cust.id
      LEFT JOIN projects p ON c.project_id = p.id
      LEFT JOIN users c_creator ON c.created_by = c_creator.id
      LEFT JOIN users p_manager ON p.manager_id = p_manager.id
      WHERE c.contract_type = 'REPSE'
        AND c.status = 'ACTIVE'
        AND c.end_date BETWEEN CURRENT_DATE AND (CURRENT_DATE + MAKE_INTERVAL(days => $1))
        AND NOT EXISTS (
            SELECT 1 FROM notifications n
            WHERE n.type = 'CONTRACT_EXPIRING'
            AND n.related_entity_type = 'CONTRACT'
            AND n.related_entity_id = c.id
            AND n.created_at >= (CURRENT_TIMESTAMP - INTERVAL '7 days')
        );
    `;
    try {
      const result = await query(sql, [daysUntilExpiry]);
      return result.rows;
    } catch (error) {
      console.error('Error finding contracts nearing expiry:', error);
      return [];
    }
  }

  public async processExpiringContractsNotifications(): Promise<void> {
    const daysToCheck = 30;
    const contracts = await this.findContractsNearingExpiry(daysToCheck);

    for (const contract of contracts) {
      const message = `El contrato REPSE N° ${contract.contract_number} con el cliente ${contract.customer_name} está por vencer el ${new Date(contract.end_date).toLocaleDateString()}.`;
      const title = `Alerta: Contrato REPSE por vencer - ${contract.contract_number}`;

      console.log(`[Notification Simulation] For contract ID ${contract.id}: ${title} (User to notify ID: ${contract.user_to_notify_id}, Email: ${contract.user_to_notify_email})`);

      // Insertar en la tabla notifications
      // Se asume que la tabla notifications tiene: title, message, type, user_id (nullable), department_id (nullable), priority, related_entity_type, related_entity_id
      try {
        await query(
           `INSERT INTO notifications (title, message, type, user_id, department_id, priority, related_entity_type, related_entity_id, created_at, is_read)
            VALUES ($1, $2, 'CONTRACT_EXPIRING', $3, $4, 'HIGH', 'CONTRACT', $5, CURRENT_TIMESTAMP, false)`,
           [
             title,
             message,
             contract.user_to_notify_id, // Si existe, se asigna al usuario
             contract.user_to_notify_id ? null : contract.sales_department_id, // Si no hay usuario específico, al departamento de ventas
             contract.id
            ]
        );
      } catch (notificationError) {
        console.error(`Error creating notification for contract ID ${contract.id}:`, notificationError);
      }
    }
  }
}
