import cron from 'node-cron';
import { AppDataSource } from '../data-source';

// Ejecutar todos los días a las 2:00 AM
cron.schedule('0 2 * * *', async () => {
  try {
    const days = 14; // Retención en días
    const result = await AppDataSource.manager.query(
      `DELETE FROM audit_logs WHERE created_at < NOW() - INTERVAL '${days} days' RETURNING id`
    );
    console.log(`[CRON] Limpieza de logs de auditoría: ${result.length} registros eliminados (${new Date().toISOString()})`);
  } catch (error) {
    console.error('[CRON] Error al limpiar logs de auditoría:', error);
  }
}); 