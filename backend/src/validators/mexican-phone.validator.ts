import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  registerDecorator,
  ValidationOptions,
} from 'class-validator';

/**
 * Códigos de área válidos en México
 * Incluye los principales códigos de área de ciudades importantes
 */
const VALID_MEXICAN_AREA_CODES = [
  // Ciudad de México y área metropolitana
  '55', '56',
  // Guadalajara
  '33',
  // Monterrey
  '81', '82',
  // Tijuana
  '664',
  // <PERSON>uebla
  '222',
  // León
  '477',
  // Juárez
  '656',
  // Mérida
  '999',
  // Zapopan
  '33',
  // Nezahualcóyotl
  '55',
  // Chihuahua
  '614',
  // Toluca
  '722',
  // Aguascalientes
  '449',
  // Querétaro
  '442',
  // Morelia
  '443',
  // Hermosillo
  '662',
  // Saltillo
  '844',
  // Mexicali
  '686',
  // <PERSON><PERSON><PERSON><PERSON>
  '667',
  // Acapulco
  '744',
  // Tlalnepantla
  '55',
  // Canc<PERSON>
  '998',
  // Veracruz
  '229',
  // Otros códigos comunes (excluyendo 555 que es reservado/no válido)
  '246', '247', '248', '249', '271', '272', '273', '274', '275', '276', '277', '278',
  '281', '282', '283', '284', '285', '287', '288', '289', '292', '294', '295', '296',
  '297', '331', '351', '352', '353', '354', '355', '356', '357', '358', '359', '371', '372',
  '373', '374', '375', '376', '377', '378', '381', '382', '383', '384', '385', '386',
  '387', '388', '389', '391', '392', '393', '394', '395', '411', '412', '413', '414',
  '415', '417', '418', '421', '423', '424', '426', '427', '428', '429', '431', '432',
  '433', '434', '435', '436', '437', '438', '441', '445', '446', '447', '448', '451',
  '452', '453', '454', '455', '456', '457', '458', '459', '461', '462', '463', '464',
  '465', '466', '467', '468', '469', '471', '472', '473', '474', '475', '476', '478',
  '481', '482', '483', '485', '486', '487', '488', '489', '492', '493', '494', '495',
  '496', '497', '498', '499'
];

@ValidatorConstraint({ name: 'MexicanPhone', async: false })
export class MexicanPhoneConstraint implements ValidatorConstraintInterface {
  validate(phone: string, _args: ValidationArguments): boolean {
    if (!phone || phone.trim() === '') {
      return true; // Allow empty phones since it's optional
    }

    // Normalizar el número eliminando espacios y caracteres especiales
    const normalized = this.normalizePhone(phone);
    
    // Verificar formato básico
    if (!this.isValidFormat(normalized)) {
      return false;
    }
    
    // Extraer código de área y verificar si es válido
    const areaCode = this.extractAreaCode(normalized);
    if (!areaCode || !VALID_MEXICAN_AREA_CODES.includes(areaCode)) {
      return false;
    }
    
    return true;
  }

  defaultMessage(_args: ValidationArguments): string {
    return 'El teléfono debe tener un formato mexicano válido con código de área reconocido (ej: 33 1234 5678, +52 33 1234 5678)';
  }

  /**
   * Normaliza el número de teléfono eliminando espacios y caracteres especiales
   */
  private normalizePhone(phone: string): string {
    return phone.replace(/[\s\-\(\)]/g, '');
  }

  /**
   * Verifica si el formato básico es válido
   */
  private isValidFormat(normalized: string): boolean {
    // Verificar que solo contenga dígitos y +
    if (!/^[\d+]+$/.test(normalized)) {
      return false;
    }
    
    // Con código de país (+52)
    if (normalized.startsWith('+52')) {
      const withoutCountryCode = normalized.substring(3);
      return /^\d{10}$/.test(withoutCountryCode);
    }
    
    // Sin código de país (10 dígitos exactos)
    return /^\d{10}$/.test(normalized);
  }

  /**
   * Extrae el código de área del número normalizado
   */
  private extractAreaCode(normalized: string): string | null {
    let phoneDigits: string;
    
    // Eliminar código de país si existe
    if (normalized.startsWith('+52')) {
      phoneDigits = normalized.substring(3);
    } else {
      phoneDigits = normalized;
    }
    
    // Verificar que tenga 10 dígitos
    if (phoneDigits.length !== 10) {
      return null;
    }
    
    // Extraer código de área (2 o 3 dígitos)
    // Intentar primero con 2 dígitos (más comunes)
    const twoDigitCode = phoneDigits.substring(0, 2);
    if (VALID_MEXICAN_AREA_CODES.includes(twoDigitCode)) {
      return twoDigitCode;
    }
    
    // Intentar con 3 dígitos
    const threeDigitCode = phoneDigits.substring(0, 3);
    if (VALID_MEXICAN_AREA_CODES.includes(threeDigitCode)) {
      return threeDigitCode;
    }
    
    return null;
  }

  /**
   * Método público para normalizar teléfonos antes de guardar
   */
  public static normalizePhoneForStorage(phone: string): string {
    if (!phone) return phone;
    
    const validator = new MexicanPhoneConstraint();
    const normalized = validator.normalizePhone(phone);
    
    // Si tiene código de país, mantenerlo
    if (normalized.startsWith('+52')) {
      return normalized;
    }
    
    // Si no tiene código de país, agregarlo
    return `+52${normalized}`;
  }
}

/**
 * Decorator para validar teléfonos mexicanos
 */
export function IsMexicanPhone(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: MexicanPhoneConstraint,
    });
  };
} 