import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  registerDecorator,
  ValidationOptions,
} from 'class-validator';

export interface PasswordPolicyOptions {
  minLength?: number;
  maxLength?: number;
  requireUppercase?: boolean;
  requireLowercase?: boolean;
  requireNumbers?: boolean;
  requireSpecialChars?: boolean;
  forbiddenPatterns?: RegExp[];
  forbiddenWords?: string[];
  maxConsecutiveChars?: number;
  minUniqueChars?: number;
}

const DEFAULT_PASSWORD_POLICY: PasswordPolicyOptions = {
  minLength: 8,
  maxLength: 128,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  forbiddenPatterns: [
    /(.)\1{2,}/, // No más de 2 caracteres consecutivos iguales
    /123456/, // Secuencias numéricas simples
    /abcdef/, // Secuencias alfabéticas simples
    /qwerty/i, // Patrones de teclado
    /password/i, // Palabra "password"
    /admin/i, // Palabra "admin"
  ],
  forbiddenWords: [
    'comintec',
    'empresa',
    'usuario',
    'contraseña',
    'password',
    'admin',
    'administrador',
  ],
  maxConsecutiveChars: 2,
  minUniqueChars: 6,
};

@ValidatorConstraint({ name: 'passwordPolicy', async: false })
export class PasswordPolicyConstraint implements ValidatorConstraintInterface {
  validate(password: string, args: ValidationArguments): boolean {
    if (!password || typeof password !== 'string') {
      return false;
    }

    const options = { ...DEFAULT_PASSWORD_POLICY, ...(args.constraints[0] || {}) };
    
    return this.validatePasswordPolicy(password, options);
  }

  defaultMessage(args: ValidationArguments): string {
    const options = { ...DEFAULT_PASSWORD_POLICY, ...(args.constraints[0] || {}) };
    const errors = this.getPasswordErrors(args.value, options);
    
    if (errors.length > 0) {
      return `La contraseña no cumple con los requisitos: ${errors.join(', ')}`;
    }
    
    return 'La contraseña no cumple con las políticas de seguridad';
  }

  private validatePasswordPolicy(password: string, options: PasswordPolicyOptions): boolean {
    const errors = this.getPasswordErrors(password, options);
    return errors.length === 0;
  }

  private getPasswordErrors(password: string, options: PasswordPolicyOptions): string[] {
    const errors: string[] = [];

    // Validar longitud
    if (options.minLength && password.length < options.minLength) {
      errors.push(`mínimo ${options.minLength} caracteres`);
    }
    if (options.maxLength && password.length > options.maxLength) {
      errors.push(`máximo ${options.maxLength} caracteres`);
    }

    // Validar requisitos de caracteres
    if (options.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('al menos una letra mayúscula');
    }
    if (options.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('al menos una letra minúscula');
    }
    if (options.requireNumbers && !/\d/.test(password)) {
      errors.push('al menos un número');
    }
    if (options.requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('al menos un carácter especial');
    }

    // Validar patrones prohibidos
    if (options.forbiddenPatterns) {
      for (const pattern of options.forbiddenPatterns) {
        if (pattern.test(password)) {
          errors.push('no debe contener patrones comunes o secuencias');
          break;
        }
      }
    }

    // Validar palabras prohibidas
    if (options.forbiddenWords) {
      const lowerPassword = password.toLowerCase();
      for (const word of options.forbiddenWords) {
        if (lowerPassword.includes(word.toLowerCase())) {
          errors.push('no debe contener palabras comunes o relacionadas con la empresa');
          break;
        }
      }
    }

    // Validar caracteres consecutivos
    if (options.maxConsecutiveChars) {
      const consecutiveRegex = new RegExp(`(.)\\1{${options.maxConsecutiveChars},}`);
      if (consecutiveRegex.test(password)) {
        errors.push(`no debe tener más de ${options.maxConsecutiveChars} caracteres consecutivos iguales`);
      }
    }

    // Validar caracteres únicos
    if (options.minUniqueChars) {
      const uniqueChars = new Set(password.split('')).size;
      if (uniqueChars < options.minUniqueChars) {
        errors.push(`debe tener al menos ${options.minUniqueChars} caracteres únicos`);
      }
    }

    return errors;
  }

  // Método para obtener errores detallados (útil para debugging)
  public getDetailedErrors(password: string, options?: PasswordPolicyOptions): string[] {
    const finalOptions = { ...DEFAULT_PASSWORD_POLICY, ...(options || {}) };
    return this.getPasswordErrors(password, finalOptions);
  }
}

export function IsStrongPassword(
  options?: PasswordPolicyOptions,
  validationOptions?: ValidationOptions,
) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [options],
      validator: PasswordPolicyConstraint,
    });
  };
}

// Utilidad para verificar la fuerza de la contraseña
export class PasswordStrengthChecker {
  static calculateStrength(password: string): {
    score: number; // 0-100
    level: 'muy débil' | 'débil' | 'media' | 'fuerte' | 'muy fuerte';
    suggestions: string[];
  } {
    let score = 0;
    const suggestions: string[] = [];

    // Longitud (máximo 25 puntos)
    if (password.length >= 12) score += 25;
    else if (password.length >= 8) score += 15;
    else if (password.length >= 6) score += 5;
    else suggestions.push('Usar al menos 8 caracteres');

    // Mayúsculas (15 puntos)
    if (/[A-Z]/.test(password)) score += 15;
    else suggestions.push('Incluir letras mayúsculas');

    // Minúsculas (15 puntos)
    if (/[a-z]/.test(password)) score += 15;
    else suggestions.push('Incluir letras minúsculas');

    // Números (15 puntos)
    if (/\d/.test(password)) score += 15;
    else suggestions.push('Incluir números');

    // Caracteres especiales (20 puntos)
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score += 20;
    else suggestions.push('Incluir caracteres especiales');

    // Diversidad de caracteres (10 puntos)
    const uniqueChars = new Set(password.split('')).size;
    if (uniqueChars >= password.length * 0.7) score += 10;
    else suggestions.push('Usar más variedad de caracteres');

    // Determinar nivel
    let level: 'muy débil' | 'débil' | 'media' | 'fuerte' | 'muy fuerte';
    if (score >= 90) level = 'muy fuerte';
    else if (score >= 70) level = 'fuerte';
    else if (score >= 50) level = 'media';
    else if (score >= 30) level = 'débil';
    else level = 'muy débil';

    return { score, level, suggestions };
  }
} 