import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  registerDecorator,
  ValidationOptions,
} from 'class-validator';
import { AppDataSource } from '../data-source';
import { User } from '../entities/user.entity';

@ValidatorConstraint({ name: 'UniqueEmail', async: true })
export class UniqueEmailConstraint implements ValidatorConstraintInterface {
  async validate(email: string, args: ValidationArguments): Promise<boolean> {
    const userRepository = AppDataSource.getRepository(User);
    
    // Obtener el ID del usuario actual si está siendo actualizado
    const currentUserId = args.object ? (args.object as any).id : undefined;
    
    const existingUser = await userRepository.findOne({
      where: { email },
    });
    
    // Si no existe usuario con ese email, es válido
    if (!existingUser) {
      return true;
    }
    
    // Si existe pero es el mismo usuario (actualización), es válido
    if (currentUserId && existingUser.id === currentUserId) {
      return true;
    }
    
    // Si existe y no es el mismo usuario, no es válido
    return false;
  }

  defaultMessage(_args: ValidationArguments): string {
    return 'El correo electrónico ya está en uso por otro usuario';
  }
}

/**
 * Decorator para validar que un email sea único en el sistema
 * @param validationOptions Opciones de validación
 */
export function IsUniqueEmail(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: UniqueEmailConstraint,
    });
  };
} 