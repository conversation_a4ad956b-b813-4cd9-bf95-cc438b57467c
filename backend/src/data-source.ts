import 'reflect-metadata';
import { DataSource } from 'typeorm';
import { config } from './config/config';
import { Client } from './entities/client.entity';
import { User } from './entities/user.entity';
import { Role } from './entities/role.entity';
import { Permission } from './entities/permission.entity';
import { Notification } from './entities/notification.entity';
import { AuditLog } from './entities/audit_log.entity';
import { Backup } from './entities/backup.entity';

export const AppDataSource = new DataSource({
  type: 'postgres',
  host: config.database.host,
  port: config.database.port,
  username: config.database.username,
  password: config.database.password,
  database: config.database.database,
  synchronize: false, // Use migrations in production
  logging: false,
  entities: [Client, User, Role, Permission, Notification, AuditLog, Backup],
  migrations: [__dirname + '/migrations/*.{ts,js}'],
  subscribers: [],
});
