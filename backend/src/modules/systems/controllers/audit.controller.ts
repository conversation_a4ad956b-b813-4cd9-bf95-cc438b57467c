import { Request, Response } from 'express';
import { AppDataSource } from '../../../data-source';
import { AuditLog } from '../../../entities/audit_log.entity';

export class AuditController {
  async getAuditLogs(req: Request, res: Response): Promise<void> {
    try {
      const auditLogRepository = AppDataSource.getRepository(AuditLog);
      // Valores por defecto y conversión segura
      const search = typeof req.query.search === 'string' ? req.query.search : '';
      const startDate = typeof req.query.startDate === 'string' ? req.query.startDate : undefined;
      const endDate = typeof req.query.endDate === 'string' ? req.query.endDate : undefined;
      // (Opcional: paginación)
      // const page = Number(req.query.page) || 1;
      // const limit = Number(req.query.limit) || 20;

      let query = auditLogRepository
        .createQueryBuilder('audit')
        .leftJoinAndSelect('audit.user', 'user')
        .orderBy('audit.created_at', 'DESC');

      // Filtro por búsqueda
      if (search) {
        query = query.where(
          '(audit.action ILIKE :search OR audit.target_entity ILIKE :search OR user.email ILIKE :search)',
          { search: `%${search}%` }
        );
      }

      // Filtro por fecha de inicio
      if (startDate) {
        query = query.andWhere('audit.created_at >= :startDate', { startDate });
      }

      // Filtro por fecha de fin
      if (endDate) {
        query = query.andWhere('audit.created_at <= :endDate', { endDate });
      }

      const auditLogs = await query.getMany();
      res.status(200).json({ data: auditLogs });
    } catch (error: any) {
      console.error('[AuditController][getAuditLogs] Error:', error);
      res.status(500).json({ message: 'Error al obtener logs de auditoría', error: error?.message || 'Error interno' });
    }
  }
} 