import { Request, Response } from 'express';
import { AppDataSource } from '../../../data-source';
import { Permission } from '../../../entities/permission.entity';

export class PermissionController {
  async getPermissions(_req: Request, res: Response): Promise<void> {
    try {
      const permissionRepository = AppDataSource.getRepository(Permission);
      const permissions = await permissionRepository.find();
      res.status(200).json({ data: permissions });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }
}