import { Request, Response } from 'express';
import { DataSource } from 'typeorm';
import { UserService } from '../services/user.service';
import { AuditService } from '../services/audit.service';
import { AppDataSource } from '../../../data-source';

export class UserController {
  private userService: UserService;
  private auditService: AuditService;

  constructor(dataSource: DataSource = AppDataSource) {
    this.userService = new UserService(dataSource);
    this.auditService = new AuditService(dataSource);
  }

  async createUser(req: Request, res: Response): Promise<void> {
    try {
      const { roles, ...userData } = req.body;
      const newUser = await this.userService.createUserWithRoleIds(userData, roles);
      await this.auditService.log({
        userId: req.user?.id,
        action: 'create_user',
        targetEntity: 'users',
        targetId: newUser.id,
        details: newUser,
        ipAddress: req.ip,
      });
      res.status(201).json({ data: newUser });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }

  async getUser(req: Request, res: Response): Promise<void> {
    try {
      const user = await this.userService.findUserById(parseInt(req.params.id, 10));
      if (user) {
        res.status(200).json({ data: user });
      } else {
        res.status(404).json({ message: 'User not found' });
      }
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }

  async getUsers(req: Request, res: Response): Promise<void> {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const search = req.query.search as string;
      const status = req.query.status as string;
      const area = req.query.area as string;

      const { users, total } = await this.userService.getUsers(page, limit, search, status, area);
      
      res.status(200).json({
        data: users,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }

  async updateUser(req: Request, res: Response): Promise<void> {
    console.log('[BACKEND][updateUser] req.body:', req.body);
    try {
      const { roleIds, ...userData } = req.body;
      let user;
      if (roleIds && Array.isArray(roleIds)) {
        user = await this.userService.updateUserWithRoleIds(parseInt(req.params.id, 10), userData, roleIds);
      } else {
        user = await this.userService.updateUser(parseInt(req.params.id, 10), userData);
      }
      if (user) {
        await this.auditService.log({
          userId: req.user?.id,
          action: 'update_user',
          targetEntity: 'users',
          targetId: user.id,
          details: req.body,
          ipAddress: req.ip,
        });
        res.status(200).json({ data: user });
      } else {
        res.status(404).json({ message: 'User not found' });
      }
    } catch (error: any) {
      console.error('[BACKEND][updateUser] Error:', error);
      res.status(500).json({ message: error.message });
    }
  }

  async deleteUser(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      await this.userService.deleteUser(parseInt(id, 10));
      await this.auditService.log({
        userId: req.user?.id,
        action: 'delete_user',
        targetEntity: 'users',
        targetId: parseInt(id, 10),
        details: {},
        ipAddress: req.ip,
      });
      res.status(204).send();
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }

  async activateUser(req: Request, res: Response): Promise<void> {
    try {
      const user = await this.userService.activateUser(parseInt(req.params.id, 10));
      if (user) {
        await this.auditService.log({
          userId: req.user?.id,
          action: 'activate_user',
          targetEntity: 'users',
          targetId: user.id,
          ipAddress: req.ip,
        });
        res.status(200).json({ data: user });
      } else {
        res.status(404).json({ message: 'User not found' });
      }
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }

  async deactivateUser(req: Request, res: Response): Promise<void> {
    try {
      const user = await this.userService.deactivateUser(parseInt(req.params.id, 10));
      if (user) {
        await this.auditService.log({
          userId: req.user?.id,
          action: 'deactivate_user',
          targetEntity: 'users',
          targetId: user.id,
          ipAddress: req.ip,
        });
        res.status(200).json({ data: user });
      } else {
        res.status(404).json({ message: 'User not found' });
      }
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }

  async assignRoles(req: Request, res: Response): Promise<void> {
    try {
      const { userId, roles } = req.body;
      const user = await this.userService.assignRolesToUser(userId, roles);
      await this.auditService.log({
        userId: req.user?.id,
        action: 'assign_roles',
        targetEntity: 'users',
        targetId: user.id,
        details: { roles },
        ipAddress: req.ip,
      });
      res.status(200).json({ data: user });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }

  async getAreas(_req: Request, res: Response): Promise<void> {
    try {
      const areas = await this.userService.getAreas();
      res.status(200).json({ data: areas });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }

  async updateUserStatus(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { status } = req.body;
      const user = await this.userService.updateUserStatus(parseInt(id, 10), status);
      await this.auditService.log({
        userId: req.user?.id,
        action: 'update_user_status',
        targetEntity: 'users',
        targetId: parseInt(id, 10),
        details: { status },
        ipAddress: req.ip,
      });
      res.status(200).json({ data: user });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }

  async adminChangePassword(req: Request, res: Response): Promise<void> {
    try {
      const { userId, newPassword } = req.body;
      const user = await this.userService.adminChangePassword(userId, newPassword);
      await this.auditService.log({
        userId: req.user?.id,
        action: 'change_password',
        targetEntity: 'users',
        targetId: userId,
        details: { changedBy: req.user?.id },
        ipAddress: req.ip,
      });
      res.status(200).json({ data: user });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }
}