import { Request, Response } from 'express';
import { BackupService } from '../services/backup.service';
import { AuditService } from '../services/audit.service';
import { BackupType } from '../../../entities/backup.entity';
import multer from 'multer';
import * as fs from 'fs';
import * as path from 'path';

// Configuración de multer para subida de archivos
const storage = multer.diskStorage({
  destination: (_req, _file, cb) => {
    const uploadDir = path.join(__dirname, '../../../../uploads/backups');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (_req, file, cb) => {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    cb(null, `uploaded-backup-${timestamp}-${file.originalname}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB máximo
  },
  fileFilter: (_req, file, cb) => {
    // Solo permitir archivos .sql
    if (file.mimetype === 'application/sql' ||
        file.originalname.toLowerCase().endsWith('.sql') ||
        file.mimetype === 'text/plain') {
      cb(null, true);
    } else {
      cb(new Error('Solo se permiten archivos .sql'));
    }
  }
});

export class BackupController {
  private get backupService() {
    return new BackupService();
  }
  private get auditService() {
    return new AuditService();
  }

  // Middleware para subida de archivos
  public uploadMiddleware = upload.single('backupFile');

  async createBackup(req: Request, res: Response): Promise<void> {
    try {
      const { name, description, type = BackupType.MANUAL, format = 'clean' } = req.body;

      if (!name) {
        res.status(400).json({ message: 'El nombre del backup es requerido' });
        return;
      }

      console.log(`🔧 Creating backup with format: ${format}`);
      const backup = await this.backupService.createBackup(name, description, type, req.user?.id, req.ip);

      res.status(201).json({
        ...backup,
        format: format,
        message: format === 'clean'
          ? 'Backup limpio creado con formato INSERT (sin dependencias de Supabase)'
          : 'Backup estándar creado'
      });
    } catch (error: any) {
      console.error('❌ Error creating backup:', error);
      res.status(500).json({ message: error.message });
    }
  }

  async getAllBackups(_req: Request, res: Response): Promise<void> {
    try {
      const backups = await this.backupService.getAllBackups();
      res.status(200).json(backups);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }

  async getBackupById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const backup = await this.backupService.getBackupById(parseInt(id));
      
      if (!backup) {
        res.status(404).json({ message: 'Backup no encontrado' });
        return;
      }
      
      res.status(200).json(backup);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }

  async deleteBackup(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      await this.backupService.deleteBackup(parseInt(id));
      
      await this.auditService.log({
        userId: req.user?.id,
        action: 'delete_backup',
        targetEntity: 'backups',
        targetId: parseInt(id),
        details: { backupId: id },
        ipAddress: req.ip,
      });

      res.status(204).send();
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }

  async downloadBackup(req: Request, res: Response): Promise<void> {
    try {
      console.log('🔍 [CONTROLLER] Starting download backup...');
      const { id } = req.params;
      console.log(`Backup ID to download: ${id}`);
      
      const { filePath, fileName } = await this.backupService.downloadBackup(parseInt(id));
      console.log(`✅ [CONTROLLER] Backup service returned: filePath=${filePath}, fileName=${fileName}`);
      
      await this.auditService.log({
        userId: req.user?.id,
        action: 'download_backup',
        targetEntity: 'backups',
        targetId: parseInt(id),
        details: { fileName },
        ipAddress: req.ip,
      });

      console.log('✅ [CONTROLLER] Sending file download response...');
      res.download(filePath, fileName);
    } catch (error: any) {
      console.error('❌ [CONTROLLER] Error in downloadBackup:', error);
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        id: req.params.id
      });
      res.status(500).json({ message: error.message });
    }
  }

  async restoreBackup(_req: Request, res: Response): Promise<void> {
    try {
      const { id } = _req.params;
      
      // Confirmación requerida para restauración
      const { confirm } = _req.body;
      if (!confirm) {
        res.status(400).json({ message: 'Se requiere confirmación para restaurar un backup' });
        return;
      }

      await this.backupService.restoreBackup(parseInt(id));
      
      await this.auditService.log({
        userId: _req.user?.id,
        action: 'restore_backup',
        targetEntity: 'backups',
        targetId: parseInt(id),
        details: { backupId: id },
        ipAddress: _req.ip,
      });

      res.status(200).json({ message: 'Backup restaurado correctamente' });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }

  async scheduleBackup(_req: Request, res: Response): Promise<void> {
    try {
      const { name, description, frequency, cronExpression } = _req.body;
      let finalCron = cronExpression;
      let usedFrequency = frequency;

      // Traducción de frecuencia a expresión cron
      if (frequency && !cronExpression) {
        switch (frequency) {
          case 'daily':
            finalCron = '0 2 * * *';
            break;
          case 'weekly':
            finalCron = '0 2 * * 0';
            break;
          case 'monthly':
            finalCron = '0 2 1 * *';
            break;
          default:
            res.status(400).json({ message: 'Frecuencia no soportada' });
            return;
        }
      }

      if (!name || !finalCron) {
        res.status(400).json({ message: 'Nombre y frecuencia o expresión cron son requeridos' });
        return;
      }

      const backup = await this.backupService.scheduleBackup(name, finalCron, description, usedFrequency);

      await this.auditService.log({
        userId: _req.user?.id,
        action: 'schedule_backup',
        targetEntity: 'backups',
        targetId: backup.id,
        details: { name, frequency: usedFrequency, cronExpression: finalCron },
        ipAddress: _req.ip,
      });

      res.status(201).json({
        ...backup,
        frequency: usedFrequency,
        cronExpression: finalCron
      });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }

  async getBackupStats(_req: Request, res: Response): Promise<void> {
    try {
      const stats = await this.backupService.getBackupStats();
      res.status(200).json(stats);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Subir y validar un archivo de backup local
   */
  async uploadBackup(req: Request, res: Response): Promise<void> {
    try {
      if (!req.file) {
        res.status(400).json({ message: 'No se proporcionó ningún archivo' });
        return;
      }

      console.log('📤 [CONTROLLER] Validating uploaded backup file...');
      const filePath = req.file.path;
      const originalName = req.file.originalname;

      // Validar el archivo de backup
      const validationResult = await this.backupService.validateBackupFile(filePath);

      if (!validationResult.isValid) {
        // Eliminar archivo inválido
        fs.unlinkSync(filePath);
        res.status(400).json({
          message: 'Archivo de backup inválido',
          errors: validationResult.errors,
          details: validationResult.details
        });
        return;
      }

      // Registrar auditoría de subida
      await this.auditService.log({
        userId: req.user?.id,
        action: 'upload_backup',
        targetEntity: 'backups',
        details: {
          fileName: originalName,
          fileSize: req.file.size,
          validationResult: validationResult.details
        },
        ipAddress: req.ip,
      });

      res.status(200).json({
        message: 'Archivo de backup validado correctamente',
        fileName: originalName,
        fileSize: req.file.size,
        filePath: filePath,
        validation: validationResult.details,
        canRestore: true
      });

    } catch (error: any) {
      console.error('❌ Error uploading backup:', error);

      // Limpiar archivo si existe
      if (req.file?.path && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }

      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Ejecutar/restaurar un backup subido previamente
   */
  async executeUploadedBackup(req: Request, res: Response): Promise<void> {
    try {
      const { filePath, confirm } = req.body;

      if (!confirm) {
        res.status(400).json({ message: 'Se requiere confirmación para ejecutar el backup' });
        return;
      }

      if (!filePath || !fs.existsSync(filePath)) {
        res.status(400).json({ message: 'Archivo de backup no encontrado' });
        return;
      }

      console.log('🔄 [CONTROLLER] Executing uploaded backup...');

      // Validar nuevamente antes de ejecutar
      const validationResult = await this.backupService.validateBackupFile(filePath);

      if (!validationResult.isValid) {
        res.status(400).json({
          message: 'El archivo de backup no es válido para ejecutar',
          errors: validationResult.errors
        });
        return;
      }

      // Ejecutar el backup
      await this.backupService.executeBackupFile(filePath);

      // Registrar auditoría de ejecución
      await this.auditService.log({
        userId: req.user?.id,
        action: 'execute_uploaded_backup',
        targetEntity: 'backups',
        details: {
          filePath,
          validationResult: validationResult.details
        },
        ipAddress: req.ip,
      });

      // Limpiar archivo después de ejecutar
      fs.unlinkSync(filePath);

      res.status(200).json({
        message: 'Backup ejecutado correctamente',
        details: 'La base de datos ha sido restaurada desde el archivo subido'
      });

    } catch (error: any) {
      console.error('❌ Error executing uploaded backup:', error);
      res.status(500).json({ message: error.message });
    }
  }

  async cleanupExpiredBackups(_req: Request, res: Response): Promise<void> {
    try {
      const deletedCount = await this.backupService.cleanupExpiredBackups();
      
      await this.auditService.log({
        userId: _req.user?.id,
        action: 'cleanup_expired_backups',
        targetEntity: 'backups',
        targetId: 0,
        details: { deletedCount },
        ipAddress: _req.ip,
      });

      res.status(200).json({ 
        message: `Se eliminaron ${deletedCount} backups expirados`,
        deletedCount 
      });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }
} 