import { Request, Response } from 'express';
import { AppDataSource } from '../../../data-source';
import { Role } from '../../../entities/role.entity';
import { Permission } from '../../../entities/permission.entity';
import { AuditService } from '../services/audit.service';
// import { User } from '../../../entities/user.entity'; // Eliminado porque ya no se usa

export class RoleController {
  private get auditService() {
    return new AuditService();
  }

  async createRole(req: Request, res: Response): Promise<void> {
    try {
      console.log('[RoleController] createRole - Starting creation with data:', req.body);
      
      const roleRepository = AppDataSource.getRepository(Role);
      const permissionRepository = AppDataSource.getRepository(Permission);
      const { name, permissions } = req.body;
      
      console.log('[RoleController] createRole - Creating role with name:', name);
      console.log('[<PERSON><PERSON><PERSON>roller] createRole - Permissions to assign:', permissions);
      
      const role = new Role();
      role.name = name;
      // TODO: Uncomment when description column is added to Supabase
      // if (description) {
      //   role.description = description;
      // }

      if (permissions) {
        console.log('[RoleController] createRole - Looking up permission objects');
        const permissionObjects = await permissionRepository.find({
          where: permissions.map((p: string) => ({ name: p })),
        });
        console.log('[RoleController] createRole - Found permission objects:', permissionObjects.length);
        role.permissions = permissionObjects;
      }

      console.log('[RoleController] createRole - Saving role to database');
      const newRole = await roleRepository.save(role);
      await this.auditService.log({
        userId: req.user?.id,
        action: 'create_role',
        targetEntity: 'roles',
        targetId: newRole.id,
        details: { name: newRole.name, permissions: newRole.permissions?.map(p => p.name) },
        ipAddress: req.ip,
      });
      console.log('[RoleController] createRole - Role saved successfully:', newRole);
      
      res.status(201).json({ data: newRole });
    } catch (error: any) {
      console.error('[RoleController] createRole - Error:', error);
      console.error('[RoleController] createRole - Error stack:', error.stack);
      res.status(500).json({ message: error.message });
    }
  }

  async getRoles(_req: Request, res: Response): Promise<void> {
    try {
      const roleRepository = AppDataSource.getRepository(Role);
      
      // Obtener roles con SQL directo - solo columnas que existen
      const rolesQuery = `
        SELECT r.id, r.name
        FROM roles r
        ORDER BY r.name
      `;
      
      const rolesResult = await roleRepository.query(rolesQuery);
      
      // Obtener permisos para cada rol con SQL directo
      for (const role of rolesResult) {
        const permissionsQuery = `
          SELECT p.id, p.name, p.module, p.action
          FROM role_permissions rp
          JOIN permissions p ON rp.permission_id = p.id
          WHERE rp.role_id = $1
          ORDER BY p.name
        `;
        
        const permissionsResult = await roleRepository.query(permissionsQuery, [role.id]);
        role.permissions = permissionsResult;
      }
      
      res.status(200).json({ data: rolesResult });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }

  async getRole(req: Request, res: Response): Promise<void> {
    try {
      const roleRepository = AppDataSource.getRepository(Role);
      const { id } = req.params;
      const roleId = parseInt(id);
      
      // Obtener rol usando SQL directo
      const roleQuery = `SELECT id, name FROM roles WHERE id = $1`;
      const roleResult = await roleRepository.query(roleQuery, [roleId]);
      
      if (!roleResult || roleResult.length === 0) {
        res.status(404).json({ message: 'Role not found' });
        return;
      }
      
      const role = roleResult[0];
      
      // Obtener permisos del rol usando SQL directo
      const permissionsQuery = `
        SELECT p.id, p.name, p.module, p.action
        FROM role_permissions rp
        JOIN permissions p ON rp.permission_id = p.id
        WHERE rp.role_id = $1
        ORDER BY p.name
      `;
      
      const permissionsResult = await roleRepository.query(permissionsQuery, [roleId]);
      role.permissions = permissionsResult;
      
      res.status(200).json({ data: role });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }

  async updateRole(req: Request, res: Response): Promise<void> {
    try {
      const roleRepository = AppDataSource.getRepository(Role);
      const { id } = req.params;
      const { name } = req.body;
      
      const role = await roleRepository.findOne({ where: { id: parseInt(id) } });
      if (!role) {
        res.status(404).json({ message: 'Role not found' });
        return;
      }
      
      role.name = name;
      // TODO: Uncomment when description column is added to Supabase
      // if (description !== undefined) {
      //   role.description = description;
      // }
      
      const updatedRole = await roleRepository.save(role);

      // Registrar auditoría
      await this.auditService.log({
        userId: req.user?.id,
        action: 'update_role',
        targetEntity: 'roles',
        targetId: updatedRole.id,
        details: { name: updatedRole.name },
        ipAddress: req.ip,
      });

      res.status(200).json({ data: updatedRole });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }

  async deleteRole(req: Request, res: Response): Promise<void> {
    try {
      console.log('[RoleController] deleteRole - Starting deletion for ID:', req.params.id);
      
      const roleRepository = AppDataSource.getRepository(Role);
      const { id } = req.params;
      const roleId = parseInt(id);
      
      console.log('[RoleController] deleteRole - Looking for role with ID:', roleId);
      
      // Verificar que el rol existe usando SQL directo
      const roleQuery = `SELECT id, name FROM roles WHERE id = $1`;
      const roleResult = await roleRepository.query(roleQuery, [roleId]);
      
      if (!roleResult || roleResult.length === 0) {
        console.log('[RoleController] deleteRole - Role not found');
        res.status(404).json({ message: 'Role not found' });
        return;
      }
      
      const role = roleResult[0];
      console.log('[RoleController] deleteRole - Found role:', role.name);
      
      // Verificar si hay usuarios con este rol usando SQL directo
      const usersWithRoleQuery = `
        SELECT COUNT(*) as count 
        FROM user_roles ur 
        WHERE ur.role_id = $1
      `;
      const usersCountResult = await roleRepository.query(usersWithRoleQuery, [roleId]);
      const usersCount = parseInt(usersCountResult[0].count);
      
      console.log('[RoleController] deleteRole - Users with this role:', usersCount);
      
      if (usersCount > 0) {
        console.log('[RoleController] deleteRole - Cannot delete role, users still assigned');
        res.status(400).json({ 
          message: `Cannot delete role. ${usersCount} user(s) still have this role assigned.`,
          usersCount: usersCount
        });
        return;
      }
      
      console.log('[RoleController] deleteRole - Proceeding with deletion');
      
      // Eliminar las relaciones rol-permisos primero
      await roleRepository.query(
        'DELETE FROM role_permissions WHERE role_id = $1',
        [roleId]
      );
      
      // Eliminar el rol
      await roleRepository.query(
        'DELETE FROM roles WHERE id = $1',
        [roleId]
      );
      
      console.log('[RoleController] deleteRole - Role deleted successfully');
      
      await this.auditService.log({
        userId: req.user?.id,
        action: 'delete_role',
        targetEntity: 'roles',
        targetId: roleId,
        details: { deletedRole: role?.name },
        ipAddress: req.ip,
      });
      
      res.status(200).json({ message: 'Role deleted successfully' });
    } catch (error: any) {
      console.error('[RoleController] deleteRole - Error:', error);
      console.error('[RoleController] deleteRole - Error stack:', error.stack);
      res.status(500).json({ message: error.message });
    }
  }

  async assignPermissionsToRole(req: Request, res: Response): Promise<void> {
    try {
      console.log('[RoleController] assignPermissionsToRole - Starting with data:', req.body);
      
      const roleRepository = AppDataSource.getRepository(Role);
      const { roleId, permissionNames } = req.body;
      
      console.log('[RoleController] assignPermissionsToRole - Looking for role ID:', roleId);
      console.log('[RoleController] assignPermissionsToRole - Permission names:', permissionNames);
      
      // Verificar que el rol existe usando SQL directo
      const roleQuery = `SELECT id, name FROM roles WHERE id = $1`;
      const roleResult = await roleRepository.query(roleQuery, [roleId]);
      
      if (!roleResult || roleResult.length === 0) {
        console.log('[RoleController] assignPermissionsToRole - Role not found');
        res.status(404).json({ message: 'Role not found' });
        return;
      }

      const role = roleResult[0];
      console.log('[RoleController] assignPermissionsToRole - Found role:', role.name);
      console.log('[RoleController] assignPermissionsToRole - Looking up permissions');
      
      // Verificar que todos los permisos existen usando SQL directo
      const permissionQuery = `
        SELECT id, name, module, action
        FROM permissions
        WHERE name = ANY($1)
        ORDER BY name
      `;
      
      const permissions = await roleRepository.query(permissionQuery, [permissionNames]);
      console.log('[RoleController] assignPermissionsToRole - Found permissions:', permissions.length);

      if (permissions.length !== permissionNames.length) {
        console.log('[RoleController] assignPermissionsToRole - Permission count mismatch');
        res.status(400).json({ message: 'One or more permissions not found' });
        return;
      }

      console.log('[RoleController] assignPermissionsToRole - Clearing existing permissions');
      // Limpiar permisos existentes del rol
      await roleRepository.query(
        'DELETE FROM role_permissions WHERE role_id = $1',
        [roleId]
      );

      console.log('[RoleController] assignPermissionsToRole - Assigning new permissions');
      // Asignar nuevos permisos usando SQL directo
      for (const permission of permissions) {
        await roleRepository.query(
          'INSERT INTO role_permissions (role_id, permission_id) VALUES ($1, $2)',
          [roleId, permission.id]
        );
      }

      console.log('[RoleController] assignPermissionsToRole - Getting updated role with permissions');
      // Obtener el rol actualizado con sus permisos
      const updatedRoleQuery = `
        SELECT r.id, r.name
        FROM roles r
        WHERE r.id = $1
      `;
      
      const updatedRoleResult = await roleRepository.query(updatedRoleQuery, [roleId]);
      const updatedRole = updatedRoleResult[0];
      
      // Obtener los permisos del rol
      const rolePermissionsQuery = `
        SELECT p.id, p.name, p.module, p.action
        FROM role_permissions rp
        JOIN permissions p ON rp.permission_id = p.id
        WHERE rp.role_id = $1
        ORDER BY p.name
      `;
      
      const rolePermissions = await roleRepository.query(rolePermissionsQuery, [roleId]);
      updatedRole.permissions = rolePermissions;

      console.log('[RoleController] assignPermissionsToRole - Sending response');
      res.status(200).json({ data: updatedRole });
    } catch (error: any) {
      console.error('[RoleController] assignPermissionsToRole - Error:', error);
      console.error('[RoleController] assignPermissionsToRole - Error stack:', error.stack);
      res.status(500).json({ message: error.message });
    }
  }
}