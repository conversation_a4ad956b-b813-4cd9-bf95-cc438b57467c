import { DataSource } from 'typeorm';
import { AppDataSource } from '../../../data-source';
import { AuditLog } from '../../../entities/audit_log.entity';
import { User } from '../../../entities/user.entity';

interface AuditLogData {
  userId?: number;
  action: string;
  targetEntity?: string;
  targetId?: number;
  details?: any;
  ipAddress?: string;
}

export class AuditService {
  private dataSource: DataSource;

  constructor(dataSource: DataSource = AppDataSource) {
    this.dataSource = dataSource;
  }

  private get auditLogRepository() {
    return this.dataSource.getRepository(AuditLog);
  }

  async log(data: AuditLogData): Promise<void> {
    try {
      console.log('[AuditService] log - Starting audit log with data:', data);
      
      const logEntryData: Partial<AuditLog> = {
        action: data.action,
        target_entity: data.targetEntity,
        target_id: data.targetId,
        details: data.details,
        ip_address: data.ipAddress,
      };

      console.log('[AuditService] log - Created log entry data:', logEntryData);

      // Si tenemos userId, buscar el usuario y asignarlo
      if (data.userId) {
        console.log('[AuditService] log - Looking up user with ID:', data.userId);
        const userRepository = this.dataSource.getRepository(User);
        const user = await userRepository.findOne({ where: { id: data.userId } });
        if (user) {
          console.log('[AuditService] log - Found user:', user.email);
          logEntryData.user = user;
        } else {
          console.log('[AuditService] log - User not found for ID:', data.userId);
        }
      }

      console.log('[AuditService] log - Creating audit log entry');
      const logEntry = this.auditLogRepository.create(logEntryData);
      console.log('[AuditService] log - Saving audit log entry');
      await this.auditLogRepository.save(logEntry);
      
      console.log(`✅ Audit logged: ${data.action} for ${data.targetEntity} (ID: ${data.targetId}) by user ${data.userId}`);
    } catch (error) {
      console.error('❌ [AuditService] Error logging audit:', error);
      console.error('❌ [AuditService] Error stack:', (error as Error).stack);
      // No lanzar el error para no interrumpir la operación principal
    }
  }
}

export const auditService = new AuditService();