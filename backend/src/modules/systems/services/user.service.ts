import { DataSource } from 'typeorm';
import { AppDataSource } from '../../../data-source';
import { User } from '../../../entities/user.entity';
import { Role } from '../../../entities/role.entity';
import bcrypt from 'bcrypt';

export class UserService {
  private dataSource: DataSource;

  constructor(dataSource: DataSource = AppDataSource) {
    this.dataSource = dataSource;
  }

  async createUser(userData: Partial<User>, roleNames: string[]): Promise<User> {
    const userRepository = this.dataSource.getRepository(User);
    const roleRepository = this.dataSource.getRepository(Role);

    if (userData.password) {
      const salt = await bcrypt.genSalt(10);
      userData.password = await bcrypt.hash(userData.password, salt);
    }

    const roles = await roleRepository.find({
      where: roleNames.map(name => ({ name })),
    });

    if (roles.length !== roleNames.length) {
      throw new Error('One or more roles not found');
    }

    const newUser = userRepository.create({
      ...userData,
      roles,
    });

    return userRepository.save(newUser);
  }

  async createUserWithRoleIds(userData: Partial<User>, roleIds: number[]): Promise<User> {
    const userRepository = this.dataSource.getRepository(User);

    // Hash password
    if (userData.password) {
      const salt = await bcrypt.genSalt(10);
      userData.password = await bcrypt.hash(userData.password, salt);
    }

    // Verificar que los roles existen usando SQL directo
    const roleCheckQuery = `
      SELECT id, name
      FROM roles
      WHERE id = ANY($1)
    `;
    
    const existingRoles = await userRepository.query(roleCheckQuery, [roleIds]);
    
    if (existingRoles.length !== roleIds.length) {
      throw new Error('One or more roles not found');
    }

    // Crear usuario sin roles primero
    const newUser = userRepository.create({
      ...userData,
      roles: [],
    });

    const savedUser = await userRepository.save(newUser);

    // Asignar roles usando SQL directo
    for (const roleId of roleIds) {
      await userRepository.query(
        'INSERT INTO user_roles (user_id, role_id) VALUES ($1, $2)',
        [savedUser.id, roleId]
      );
    }

    // Obtener el usuario completo con roles
    return this.findUserById(savedUser.id) as Promise<User>;
  }

  async findUserById(id: number): Promise<User | null> {
    const userRepository = this.dataSource.getRepository(User);
    
    // Obtener usuario básico con SQL directo
    const userQuery = `
      SELECT id, name, email, phone, status, area, active,
             created_at, updated_at, created_by, updated_by
      FROM users
      WHERE id = $1
    `;
    
    const userResult = await userRepository.query(userQuery, [id]);
    
    if (!userResult || userResult.length === 0) {
      return null;
    }
    
    const user = userResult[0];
    
    // Obtener roles con SQL directo
    const rolesQuery = `
      SELECT r.id, r.name
      FROM user_roles ur
      JOIN roles r ON ur.role_id = r.id
      WHERE ur.user_id = $1
      ORDER BY r.name
    `;
    
    const rolesResult = await userRepository.query(rolesQuery, [id]);
    user.roles = rolesResult;
    
    return user;
  }

  async findUserByEmail(email: string): Promise<User | null> {
    const userRepository = this.dataSource.getRepository(User);
    
    // Obtener usuario básico con SQL directo
    const userQuery = `
      SELECT id, name, email, phone, status, area, active,
             created_at, updated_at, created_by, updated_by, password
      FROM users
      WHERE email = $1
    `;
    
    const userResult = await userRepository.query(userQuery, [email]);
    
    if (!userResult || userResult.length === 0) {
      return null;
    }
    
    const user = userResult[0];
    
    // Obtener roles con SQL directo
    const rolesQuery = `
      SELECT r.id, r.name
      FROM user_roles ur
      JOIN roles r ON ur.role_id = r.id
      WHERE ur.user_id = $1
      ORDER BY r.name
    `;
    
    const rolesResult = await userRepository.query(rolesQuery, [user.id]);
    user.roles = rolesResult;
    
    // Obtener permisos para cada rol
    for (const role of user.roles) {
      const permissionsQuery = `
        SELECT p.id, p.name, p.module, p.action
        FROM role_permissions rp
        JOIN permissions p ON rp.permission_id = p.id
        WHERE rp.role_id = $1
        ORDER BY p.name
      `;
      
      const permissionsResult = await userRepository.query(permissionsQuery, [role.id]);
      role.permissions = permissionsResult;
    }
    
    return user;
  }

  async getUsers(
    page: number = 1,
    limit: number = 10,
    search?: string,
    status?: string,
    area?: string
  ): Promise<{ users: User[], total: number }> {
    const userRepository = this.dataSource.getRepository(User);
    
    // Construir cláusulas dinámicas
    const whereClauses: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    if (search) {
      whereClauses.push(`(u.name ILIKE $${paramIndex} OR u.email ILIKE $${paramIndex})`);
      params.push(`%${search}%`);
      paramIndex++;
    }
    if (status) {
      whereClauses.push(`u.status = $${paramIndex}`);
      params.push(status);
      paramIndex++;
    }
    if (area) {
      whereClauses.push(`u.area = $${paramIndex}`);
      params.push(area);
      paramIndex++;
    }

    const whereClause = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';

    // Consulta para obtener usuarios con paginación
    const usersQuery = `
      SELECT u.id, u.name, u.email, u.phone, u.status, u.area, u.active,
             u.created_at, u.updated_at, u.created_by, u.updated_by
      FROM users u
      ${whereClause}
      ORDER BY u.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    params.push(limit, (page - 1) * limit);

    // Consulta para contar total
    const countQuery = `
      SELECT COUNT(*) as total
      FROM users u
      ${whereClause}
    `;
    const countParams = params.slice(0, paramIndex - 1);

    // Ejecutar consultas
    const [usersResult, countResult] = await Promise.all([
      userRepository.query(usersQuery, params),
      userRepository.query(countQuery, countParams)
    ]);
    
    const total = parseInt(countResult[0].total);
    
    // Añadir roles a cada usuario
    for (const user of usersResult) {
      const rolesQuery = `
        SELECT r.id, r.name
        FROM user_roles ur
        JOIN roles r ON ur.role_id = r.id
        WHERE ur.user_id = $1
        ORDER BY r.name
      `;
      const rolesResult = await userRepository.query(rolesQuery, [user.id]);
      user.roles = Array.isArray(rolesResult) ? rolesResult : [];
    }
    // Garantizar que todos tengan 'roles'
    for (const user of usersResult) {
      if (!Array.isArray(user.roles)) {
        user.roles = [];
      }
    }
    return { users: usersResult, total };
  }

  async updateUser(id: number, userData: Partial<User>): Promise<User | null> {
    const userRepository = this.dataSource.getRepository(User);
    // Separar roles del resto de datos del usuario
    const { roles, ...userFields } = userData;
    // Actualizar campos básicos del usuario
    await userRepository.update(id, userFields);
    // Si se proporcionaron roles, actualizarlos
    if (roles && Array.isArray(roles)) {
      // Limpiar roles existentes
      await userRepository.query('DELETE FROM user_roles WHERE user_id = $1', [id]);
      // Asignar nuevos roles
      for (const role of roles) {
        const roleId = typeof role === 'number' ? role : role.id;
        await userRepository.query('INSERT INTO user_roles (user_id, role_id) VALUES ($1, $2)', [id, roleId]);
      }
    }
    return this.findUserById(id);
  }

  async updateUserWithRoleIds(id: number, userData: Partial<User>, roleIds: number[]): Promise<User | null> {
    const userRepository = this.dataSource.getRepository(User);
    
    // Verificar que el usuario existe
    const existingUser = await this.findUserById(id);
    if (!existingUser) {
      return null;
    }

    // Hash password si se proporciona
    if (userData.password) {
      const salt = await bcrypt.genSalt(10);
      userData.password = await bcrypt.hash(userData.password, salt);
    }

    // Verificar que los roles existen usando SQL directo
    const roleCheckQuery = `
      SELECT id, name
      FROM roles
      WHERE id = ANY($1)
    `;
    
    const existingRoles = await userRepository.query(roleCheckQuery, [roleIds]);
    
    if (existingRoles.length !== roleIds.length) {
      throw new Error('One or more roles not found');
    }

    // Actualizar campos básicos del usuario usando SQL directo
    if (Object.keys(userData).length > 0) {
      const setClause = Object.keys(userData)
        .map((key, index) => `${key} = $${index + 2}`)
        .join(', ');
      
      const updateUserQuery = `
        UPDATE users 
        SET ${setClause}, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
      `;
      
      const updateParams = [id, ...Object.values(userData)];
      await userRepository.query(updateUserQuery, updateParams);
    }

    // Limpiar roles existentes
    await userRepository.query(
      'DELETE FROM user_roles WHERE user_id = $1',
      [id]
    );

    // Asignar nuevos roles usando SQL directo
    for (const roleId of roleIds) {
      await userRepository.query(
        'INSERT INTO user_roles (user_id, role_id) VALUES ($1, $2)',
        [id, roleId]
      );
    }

    // Obtener el usuario actualizado con roles
    return this.findUserById(id);
  }

  async deleteUser(id: number): Promise<void> {
    const userRepository = this.dataSource.getRepository(User);
    await userRepository.delete(id);
  }

  async activateUser(id: number): Promise<User | null> {
    const userRepository = this.dataSource.getRepository(User);
    const user = await this.findUserById(id);
    if (!user) {
      return null;
    }

    // Sincronizar ambos campos: active y status
    user.active = true;
    user.status = 'activo';
    return userRepository.save(user);
  }

  async deactivateUser(id: number): Promise<User | null> {
    const userRepository = this.dataSource.getRepository(User);
    const user = await this.findUserById(id);
    if (!user) {
      return null;
    }

    // Sincronizar ambos campos: active y status
    user.active = false;
    user.status = 'inactivo';
    return userRepository.save(user);
  }

  async assignRolesToUser(userId: number, roleIds: number[]): Promise<User> {
    const userRepository = this.dataSource.getRepository(User);
    // Verificar que el usuario existe
    const user = await this.findUserById(userId);
    if (!user) {
      throw new Error('User not found');
    }
    // Verificar que los roles existen
    const roleCheckQuery = `SELECT id FROM roles WHERE id = ANY($1)`;
    const existingRoles = await userRepository.query(roleCheckQuery, [roleIds]);
    if (existingRoles.length !== roleIds.length) {
      throw new Error('One or more roles not found');
    }
    // Limpiar roles existentes
    await userRepository.query('DELETE FROM user_roles WHERE user_id = $1', [userId]);
    // Asignar nuevos roles
    for (const roleId of roleIds) {
      await userRepository.query('INSERT INTO user_roles (user_id, role_id) VALUES ($1, $2)', [userId, roleId]);
    }
    // Retornar el usuario actualizado con roles
    return this.findUserById(userId) as Promise<User>;
  }

  async getAreas(): Promise<string[]> {
    const userRepository = this.dataSource.getRepository(User);
    
    // Obtener todas las áreas únicas de los usuarios
    const result = await userRepository
      .createQueryBuilder('user')
      .select('DISTINCT user.area', 'area')
      .where('user.area IS NOT NULL AND user.area != :empty', { empty: '' })
      .orderBy('user.area', 'ASC')
      .getRawMany();
    
    return result.map(row => row.area).filter(area => area);
  }

  async adminChangePassword(userId: number, newPassword: string): Promise<User | null> {
    const userRepository = this.dataSource.getRepository(User);
    
    const user = await this.findUserById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Hash de la nueva contraseña
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Actualizar la contraseña del usuario
    await userRepository.update(userId, { password: hashedPassword });

    return this.findUserById(userId);
  }

  async updateUserStatus(id: number, status: string): Promise<User | null> {
    const userRepository = this.dataSource.getRepository(User);
    // Validar que el status sea uno de los valores permitidos
    const allowedStatuses = ['activo', 'inactivo', 'bloqueado'];
    if (!allowedStatuses.includes(status)) {
      throw new Error(`Invalid status. Allowed values: ${allowedStatuses.join(', ')}`);
    }
    // Actualizar el status y el campo active basado en el status
    const active = status === 'activo';
    await userRepository.update(id, { status, active });
    // Retornar el usuario actualizado
    return this.findUserById(id);
  }
}