import { User } from '../../../entities/user.entity';
import { UserService } from './user.service';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { Role } from '../../../entities/role.entity';

export class AuthService {
  private get userService() {
    return new UserService();
  }

  async validateUser(email: string, pass: string): Promise<any> {
    const user = await this.userService.findUserByEmail(email);
    if (user && (await bcrypt.compare(pass, user.password))) {
      const { password, ...result } = user;
      return result;
    }
    return null;
  }

  async login(user: User) {
    const payload = { email: user.email, sub: user.id, roles: user.roles.map((r: Role) => r.name) };
    const secret = process.env.JWT_SECRET;
    if (!secret) {
      throw new Error('JWT_SECRET is not defined');
    }
    return {
      access_token: jwt.sign(payload, secret, { expiresIn: '60m' }),
    };
  }
}