import { Backup, BackupType, BackupStatus } from '../../../entities/backup.entity';
import { exec } from 'child_process';
import { promisify } from 'util';
import * as fs from 'fs';
import * as path from 'path';
import { config } from '../../../config/config';
import { AppDataSource } from '../../../data-source';
import { auditService } from './audit.service';

const execAsync = promisify(exec);

export class BackupService {
  private get backupRepository() {
    return AppDataSource.getRepository(Backup);
  }

  private get backupDir() {
    return path.join(process.cwd(), 'backups');
  }

  async createBackup(name: string, description?: string, type: BackupType = BackupType.MANUAL, userId?: number, ipAddress?: string): Promise<Backup> {
    // Crear directorio de backups si no existe
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }

    const backup = this.backupRepository.create({
      name,
      description,
      type,
      status: BackupStatus.IN_PROGRESS,
      metadata: {
        database: config.database.database,
        host: config.database.host,
        createdAt: new Date().toISOString()
      }
    });

    const savedBackup = await this.backupRepository.save(backup);

    try {
      // Generar nombre de archivo
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `backup-${savedBackup.id}-${timestamp}.sql`;
      const filePath = path.join(this.backupDir, fileName);

      console.log('🔍 Creating backup with pg_dump...');
      console.log('Database config:', {
        host: config.database.host,
        port: config.database.port,
        username: config.database.username,
        database: config.database.database
      });

      // Crear backup limpio usando el mismo formato que el script .sh
      await this.createCleanBackup(filePath, savedBackup.id);


      // Verificar que el archivo se creó
      if (!fs.existsSync(filePath)) {
        throw new Error('pg_dump no creó el archivo de backup');
      }

      // Obtener tamaño del archivo
      const stats = fs.statSync(filePath);
      const fileSize = stats.size;

      console.log(`✅ Backup created successfully: ${fileName} (${fileSize} bytes)`);

      // Actualizar backup con información del archivo
      savedBackup.status = BackupStatus.COMPLETED;
      savedBackup.filePath = filePath;
      savedBackup.fileSize = fileSize;
      savedBackup.completedAt = new Date();
      savedBackup.expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 días

      const finalBackup = await this.backupRepository.save(savedBackup);

      // Registrar auditoría de backup exitoso
      await auditService.log({
        userId,
        action: 'create_backup',
        targetEntity: 'backups',
        targetId: finalBackup.id,
        details: {
          name: finalBackup.name,
          type: finalBackup.type,
          fileSize: finalBackup.fileSize,
          filePath: finalBackup.filePath,
        },
        ipAddress,
      });

      return finalBackup;
    } catch (error: any) {
      console.error('❌ Error creating backup:', error);
      console.error('Error details:', {
        message: error.message,
        stderr: error.stderr,
        stdout: error.stdout,
        code: error.code
      });

      // Marcar backup como fallido
      savedBackup.status = BackupStatus.FAILED;
      savedBackup.errorMessage = error.message;
      await this.backupRepository.save(savedBackup);

      // Registrar auditoría de backup fallido
      await auditService.log({
        userId,
        action: 'backup_failed',
        targetEntity: 'backups',
        targetId: savedBackup.id,
        details: {
          name: savedBackup.name,
          type: savedBackup.type,
          error: error.message,
        },
        ipAddress,
      });

      throw error;
    }
  }

  async getAllBackups(): Promise<Backup[]> {
    try {
      console.log('🔍 [SERVICE] Getting all backups...');
      const backups = await this.backupRepository.find({
        order: { createdAt: 'DESC' }
      });
      console.log('✅ [SERVICE] Found backups:', backups.length);
      return backups;
    } catch (error: any) {
      console.error('❌ [SERVICE] Error getting all backups:', error);
      console.error('Error details:', {
        message: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  async getBackupById(id: number): Promise<Backup | null> {
    return await this.backupRepository.findOne({ where: { id } });
  }

  async deleteBackup(id: number): Promise<void> {
    try {
      console.log(`🔍 Deleting backup with ID: ${id}`);
      
      const backup = await this.backupRepository.findOne({ where: { id } });
      if (!backup) {
        throw new Error('Backup no encontrado');
      }

      console.log(`Found backup: ${backup.name} (${backup.filePath})`);

      // Eliminar archivo físico si existe
      if (backup.filePath && fs.existsSync(backup.filePath)) {
        console.log(`Deleting file: ${backup.filePath}`);
        fs.unlinkSync(backup.filePath);
        console.log('✅ File deleted successfully');
      } else {
        console.log('No file to delete or file does not exist');
      }

      console.log('Removing backup from database...');
      await this.backupRepository.remove(backup);
      console.log('✅ Backup deleted successfully');
      
    } catch (error: any) {
      console.error('❌ Error deleting backup:', error);
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        id: id
      });
      throw error;
    }
  }

  async downloadBackup(id: number): Promise<{ filePath: string; fileName: string }> {
    try {
      console.log(`🔍 [SERVICE] Downloading backup with ID: ${id}`);
      
      const backup = await this.backupRepository.findOne({ where: { id } });
      if (!backup) {
        throw new Error('Backup no encontrado');
      }

      console.log(`Found backup: ${backup.name} (${backup.filePath})`);

      if (!backup.filePath || !fs.existsSync(backup.filePath)) {
        console.error(`File not found: ${backup.filePath}`);
        throw new Error('Archivo de backup no encontrado');
      }

      const fileName = path.basename(backup.filePath);
      console.log(`✅ [SERVICE] Returning file info: filePath=${backup.filePath}, fileName=${fileName}`);
      
      return { filePath: backup.filePath, fileName };
    } catch (error: any) {
      console.error('❌ [SERVICE] Error downloading backup:', error);
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        id: id
      });
      throw error;
    }
  }

  async restoreBackup(backupId: number): Promise<void> {
    const backup = await this.backupRepository.findOne({ where: { id: backupId } });
    if (!backup) {
      throw new Error('Backup no encontrado');
    }

    if (!backup.filePath || !fs.existsSync(backup.filePath)) {
      throw new Error('Archivo de backup no encontrado');
    }

    if (backup.status !== BackupStatus.COMPLETED) {
      throw new Error('El backup no está completo');
    }

    // Crear backup de seguridad antes de restaurar
    const safetyBackup = await this.createBackup(
      `Pre-restore-backup-${new Date().toISOString()}`,
      'Backup de seguridad antes de restaurar',
      BackupType.AUTOMATIC
    );

    try {
      // Restaurar usando psql
      const restoreCommand = `PGPASSWORD=${config.database.password} psql -h ${config.database.host} -p ${config.database.port} -U ${config.database.username} -d ${config.database.database} -f "${backup.filePath}"`;

      await execAsync(restoreCommand);
    } catch (error: any) {
      // Si falla la restauración, eliminar el backup de seguridad
      await this.deleteBackup(safetyBackup.id);
      throw new Error(`Error al restaurar: ${error.message}`);
    }
  }

  async scheduleBackup(name: string, cronExpression: string, description?: string, frequency?: string): Promise<Backup> {
    // Validar expresión cron básica
    if (!this.isValidCronExpression(cronExpression)) {
      throw new Error('Expresión cron inválida');
    }

    const backup = this.backupRepository.create({
      name,
      description,
      type: BackupType.SCHEDULED,
      status: BackupStatus.PENDING,
      scheduledAt: new Date(),
      frequency: frequency,
      metadata: {
        cronExpression,
        database: config.database.database,
        host: config.database.host
      }
    });

    return await this.backupRepository.save(backup);
  }

  async cleanupExpiredBackups(): Promise<number> {
    const expiredBackups = await this.backupRepository.find({
      where: {
        expiresAt: new Date(),
        status: BackupStatus.COMPLETED
      }
    });

    let deletedCount = 0;
    for (const backup of expiredBackups) {
      try {
        await this.deleteBackup(backup.id);
        deletedCount++;
      } catch (error) {
        console.error(`Error eliminando backup ${backup.id}:`, error);
      }
    }

    return deletedCount;
  }

  async getBackupStats(): Promise<{
    total: number;
    completed: number;
    failed: number;
    pending: number;
    totalSize: number;
  }> {
    try {
      console.log('🔍 [SERVICE] Getting backup stats...');
      const backups = await this.backupRepository.find();
      console.log('Found backups:', backups.length);
      
      // Log individual backup sizes
      backups.forEach(backup => {
        console.log(`Backup ${backup.id}: fileSize = ${backup.fileSize}, status = ${backup.status}`);
      });
      
      const stats = {
        total: backups.length,
        completed: backups.filter(b => b.status === BackupStatus.COMPLETED).length,
        failed: backups.filter(b => b.status === BackupStatus.FAILED).length,
        pending: backups.filter(b => b.status === BackupStatus.PENDING || b.status === BackupStatus.IN_PROGRESS).length,
        totalSize: backups.reduce((sum, b) => sum + (Number(b.fileSize) || 0), 0)
      };

      console.log('📊 [SERVICE] Calculated stats:', stats);
      return stats;
    } catch (error: any) {
      console.error('❌ [SERVICE] Error getting backup stats:', error);
      console.error('Error details:', {
        message: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Crear backup limpio siguiendo el formato del script export-clean-db-inserts.sh
   * Genera un backup sin dependencias de Supabase, con triggers y formato INSERT
   */
  private async createCleanBackup(filePath: string, backupId: number): Promise<void> {
    console.log('🔧 Creating clean backup with INSERT format...');

    // Crear encabezado del archivo
    const header = `-- =============================
-- COMINTEC - Base de Datos Completa
-- Formato: INSERT statements (ejecutable en consola SQL)
-- =============================
-- Backup ID: ${backupId}
-- Generado: ${new Date().toISOString()}
-- Incluye: estructura + datos en formato INSERT
-- =============================

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

-- Crear extensiones necesarias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

`;

    // Escribir encabezado
    fs.writeFileSync(filePath, header);

    console.log('📋 Exporting table structure...');

    // 1. Exportar estructura (DDL) - sin datos
    const structureCommand = `PGPASSWORD=${config.database.password} pg_dump \\
      --schema=public \\
      --schema-only \\
      --no-owner \\
      --no-privileges \\
      --exclude-table=supabase_* \\
      --exclude-table=_realtime* \\
      -h ${config.database.host} \\
      -p ${config.database.port} \\
      -U ${config.database.username} \\
      -d ${config.database.database}`;

    const { stdout: structureOutput, stderr: structureError } = await execAsync(structureCommand);

    if (structureError) {
      console.log('Structure export stderr:', structureError);
    }

    // Agregar estructura al archivo
    fs.appendFileSync(filePath, structureOutput);

    console.log('📊 Exporting data with INSERT statements...');

    // Agregar separador para datos
    const dataSeparator = `

-- =============================
-- DATOS DE LAS TABLAS (INSERT FORMAT)
-- =============================

`;
    fs.appendFileSync(filePath, dataSeparator);

    // 2. Exportar datos en formato INSERT
    const dataCommand = `PGPASSWORD=${config.database.password} pg_dump \\
      --schema=public \\
      --data-only \\
      --no-owner \\
      --no-privileges \\
      --exclude-table=supabase_* \\
      --exclude-table=_realtime* \\
      --column-inserts \\
      --rows-per-insert=1 \\
      -h ${config.database.host} \\
      -p ${config.database.port} \\
      -U ${config.database.username} \\
      -d ${config.database.database}`;

    const { stdout: dataOutput, stderr: dataError } = await execAsync(dataCommand);

    if (dataError) {
      console.log('Data export stderr:', dataError);
    }

    // Agregar datos al archivo
    fs.appendFileSync(filePath, dataOutput);

    // Agregar footer con instrucciones
    const footer = `

-- =============================
-- FIN DEL BACKUP
-- =============================
-- Para restaurar:
-- 1. Conectar a la base de datos destino
-- 2. Ejecutar: \\i ${path.basename(filePath)}
-- 3. O copiar y pegar secciones en consola SQL
-- =============================

`;
    fs.appendFileSync(filePath, footer);

    console.log('✅ Clean backup created successfully');

    // Mostrar estadísticas
    const stats = fs.statSync(filePath);
    const content = fs.readFileSync(filePath, 'utf8');
    const insertCount = (content.match(/^INSERT INTO/gm) || []).length;

    console.log('📊 Backup statistics:');
    console.log(`   📄 Size: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
    console.log(`   📝 Lines: ${content.split('\n').length}`);
    console.log(`   🔍 INSERT statements: ${insertCount}`);
  }

  /**
   * Validar un archivo de backup subido
   * Implementa múltiples capas de seguridad
   */
  async validateBackupFile(filePath: string): Promise<{
    isValid: boolean;
    errors: string[];
    details: any;
  }> {
    const errors: string[] = [];
    const details: any = {};

    try {
      console.log('🔍 [SERVICE] Starting backup file validation...');

      // 1. Verificar que el archivo existe
      if (!fs.existsSync(filePath)) {
        errors.push('El archivo no existe');
        return { isValid: false, errors, details };
      }

      // 2. Verificar tamaño del archivo (no más de 100MB)
      const stats = fs.statSync(filePath);
      const fileSizeMB = stats.size / (1024 * 1024);
      details.fileSize = `${fileSizeMB.toFixed(2)} MB`;

      if (stats.size > 100 * 1024 * 1024) {
        errors.push('El archivo es demasiado grande (máximo 100MB)');
      }

      // 3. Leer contenido del archivo
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      details.totalLines = lines.length;

      // 4. Validaciones de seguridad específicas

      // 4.1 Verificar que es un archivo SQL válido
      const hasSqlHeader = content.includes('PostgreSQL database dump') ||
                          content.includes('COMINTEC - Base de Datos') ||
                          content.includes('SET statement_timeout');

      if (!hasSqlHeader) {
        errors.push('No parece ser un archivo de backup SQL válido');
      } else {
        details.sqlFormat = 'Válido';
      }

      // 4.2 Buscar indicadores de que es un backup de COMINTEC
      const hasComintecIndicators = content.includes('COMINTEC') ||
                                   content.includes('users') ||
                                   content.includes('roles') ||
                                   content.includes('permissions');

      if (!hasComintecIndicators) {
        errors.push('El backup no parece ser de la base de datos COMINTEC');
      } else {
        details.comintecFormat = 'Detectado';
      }

      // 4.3 Verificar que no contiene comandos peligrosos
      const dangerousCommands = [
        'DROP DATABASE',
        'DROP SCHEMA',
        'TRUNCATE',
        'DELETE FROM users WHERE',
        'UPDATE users SET password',
        'GRANT ALL',
        'CREATE USER',
        'ALTER USER',
        'DROP USER'
      ];

      const foundDangerous = dangerousCommands.filter(cmd =>
        content.toUpperCase().includes(cmd.toUpperCase())
      );

      if (foundDangerous.length > 0) {
        errors.push(`Contiene comandos potencialmente peligrosos: ${foundDangerous.join(', ')}`);
        details.dangerousCommands = foundDangerous;
      } else {
        details.securityCheck = 'Aprobado';
      }

      // 4.4 Detectar todas las tablas en el backup
      const createTableMatches = content.match(/CREATE TABLE (?:public\.)?(\w+)/g) || [];
      const insertTableMatches = content.match(/INSERT INTO (?:public\.)?(\w+)/g) || [];

      // Extraer nombres de tablas únicos
      const createTables = createTableMatches.map(match => {
        const tableMatch = match.match(/CREATE TABLE (?:public\.)?(\w+)/);
        return tableMatch ? tableMatch[1] : null;
      }).filter(Boolean);

      const insertTables = insertTableMatches.map(match => {
        const tableMatch = match.match(/INSERT INTO (?:public\.)?(\w+)/);
        return tableMatch ? tableMatch[1] : null;
      }).filter(Boolean);

      // Combinar y obtener tablas únicas
      const allTables = [...new Set([...createTables, ...insertTables])];

      // Tablas críticas que debe tener un backup de COMINTEC
      const criticalTables = ['users', 'roles', 'permissions'];
      const foundCriticalTables = criticalTables.filter(table => allTables.includes(table));

      details.tablesFound = allTables;
      details.totalTables = allTables.length;
      details.criticalTablesFound = foundCriticalTables;
      details.criticalTablesExpected = criticalTables;

      console.log('📋 [VALIDATION] Tables detected:', {
        total: allTables.length,
        tables: allTables,
        critical: foundCriticalTables.length,
        criticalTables: foundCriticalTables
      });

      if (foundCriticalTables.length < 2) {
        errors.push('El backup no contiene las tablas críticas esperadas (users, roles, permissions)');
      }

      // 4.5 Contar INSERT statements
      const insertCount = (content.match(/^INSERT INTO/gm) || []).length;
      details.insertStatements = insertCount;

      if (insertCount === 0) {
        errors.push('El backup no contiene datos (no hay INSERT statements)');
      }

      // 4.6 Verificar que no tiene dependencias de Supabase (buena práctica)
      const hasSupabaseTables = content.includes('supabase_') ||
                               content.includes('_realtime') ||
                               content.includes('auth.users');

      if (hasSupabaseTables) {
        details.warning = 'Contiene referencias a tablas de Supabase';
      } else {
        details.cleanFormat = 'Sin dependencias de Supabase';
      }

      // 4.7 Verificar encoding
      if (!content.includes("SET client_encoding = 'UTF8'")) {
        details.warning = (details.warning || '') + ' Encoding UTF8 no especificado';
      }

      console.log('📊 [SERVICE] Validation results:', {
        isValid: errors.length === 0,
        errorsCount: errors.length,
        details
      });

      return {
        isValid: errors.length === 0,
        errors,
        details
      };

    } catch (error: any) {
      console.error('❌ [SERVICE] Error validating backup file:', error);
      errors.push(`Error al validar archivo: ${error.message}`);
      return { isValid: false, errors, details };
    }
  }

  /**
   * Ejecutar un archivo de backup validado
   */
  async executeBackupFile(filePath: string): Promise<void> {
    console.log('🔄 [SERVICE] Executing backup file...');

    try {
      // Crear backup de seguridad antes de ejecutar
      console.log('💾 Creating safety backup before restore...');
      const safetyBackup = await this.createBackup(
        `Pre-restore-safety-${new Date().toISOString()}`,
        'Backup de seguridad antes de restaurar archivo subido',
        BackupType.AUTOMATIC
      );

      console.log('✅ Safety backup created:', safetyBackup.id);

      // Ejecutar el archivo de backup usando psql
      const executeCommand = `PGPASSWORD=${config.database.password} psql \\
        -h ${config.database.host} \\
        -p ${config.database.port} \\
        -U ${config.database.username} \\
        -d ${config.database.database} \\
        -f "${filePath}"`;

      console.log('🔄 Executing restore command...');
      const { stdout, stderr } = await execAsync(executeCommand);

      if (stderr && !stderr.includes('NOTICE')) {
        console.log('⚠️ Restore stderr:', stderr);
      }

      if (stdout) {
        console.log('📄 Restore stdout:', stdout);
      }

      console.log('✅ Backup file executed successfully');

    } catch (error: any) {
      console.error('❌ [SERVICE] Error executing backup file:', error);
      throw new Error(`Error al ejecutar backup: ${error.message}`);
    }
  }

  private isValidCronExpression(cron: string): boolean {
    // Validación básica de expresión cron (5 campos: minuto hora día mes día_semana)
    const cronRegex = /^(\*|[0-9]{1,2})(\/[0-9]{1,2})?(\s+(\*|[0-9]{1,2})(\/[0-9]{1,2})?){4}$/;
    return cronRegex.test(cron);
  }
}