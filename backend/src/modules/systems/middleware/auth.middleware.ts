import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { AppDataSource } from '../../../data-source';
import { User } from '../../../entities/user.entity';
import { config } from '../../../config/config';

// Función factory para crear el middleware con un dataSource específico
export function createAuthMiddleware(dataSource = AppDataSource) {
  return async (req: Request, res: Response, next: NextFunction) => {
    console.log('=== [AUTH MIDDLEWARE] ===');
    console.log('URL:', req.url);
    console.log('Method:', req.method);
    
    const authHeader = req.headers.authorization;
    console.log('Auth header:', authHeader);

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('❌ No auth header or invalid format');
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const token = authHeader.split(' ')[1];
    console.log('Token extracted:', token ? 'YES' : 'NO');

    try {
      console.log('🔍 Step 1: Checking JWT_SECRET...');
      const secret = config.jwt.secret;
      console.log('JWT_SECRET from config:', secret);
      
      if (!secret) {
        console.log('❌ JWT_SECRET is not defined');
        throw new Error('JWT_SECRET is not defined');
      }
      
      console.log('🔍 Step 2: Verifying JWT token...');
      const decoded: any = jwt.verify(token, secret);
      console.log('Token decoded successfully, user ID:', decoded.sub);
      console.log('Decoded token:', JSON.stringify(decoded, null, 2));
      
      console.log('🔍 Step 3: Getting user repository...');
      const userRepository = dataSource.getRepository(User);
      console.log('User repository obtained');
      
      console.log('🔍 Step 4: Finding user in database with SQL...');
      
      // Usar consulta SQL directa para evitar problemas de columnas
      const userQuery = `
        SELECT u.id, u.name, u.email, u.phone, u.status, u.area, u.active, u.password,
               u.created_at, u.updated_at, u.created_by, u.updated_by
        FROM users u
        WHERE u.id = $1
      `;
      
      const userResult = await userRepository.query(userQuery, [decoded.sub]);
      
      if (!userResult || userResult.length === 0) {
        console.log('❌ User not found in database');
        return res.status(401).json({ message: 'Unauthorized' });
      }

      const user = userResult[0];
      console.log('User found:', {
        id: user.id,
        email: user.email,
        active: user.active,
        status: user.status
      });

      // Obtener roles del usuario con SQL directo
      const rolesQuery = `
        SELECT r.id, r.name
        FROM user_roles ur
        JOIN roles r ON ur.role_id = r.id
        WHERE ur.user_id = $1
      `;
      
      const rolesResult = await userRepository.query(rolesQuery, [user.id]);
      console.log('User roles found:', rolesResult.length);

      // Obtener permisos de todos los roles del usuario con SQL directo
      const permissionsQuery = `
        SELECT DISTINCT p.id, p.name, p.module, p.action
        FROM user_roles ur
        JOIN roles r ON ur.role_id = r.id
        JOIN role_permissions rp ON r.id = rp.role_id
        JOIN permissions p ON rp.permission_id = p.id
        WHERE ur.user_id = $1
        ORDER BY p.name
      `;
      
      const permissionsResult = await userRepository.query(permissionsQuery, [user.id]);
      console.log('User permissions found:', permissionsResult.length);

      // Construir la estructura de roles con permisos
      const roles = rolesResult.map((role: any) => ({
        id: role.id,
        name: role.name,
        permissions: permissionsResult // Todos los permisos del usuario
      }));

      console.log('🔍 Step 5: Attaching user to request...');
      req.user = {
        ...user,
        roles: roles
      };
      console.log('✅ Auth middleware passed, user attached to req');
      console.log('User roles:', roles.map((r: any) => ({ name: r.name, permissionCount: r.permissions.length })));
      
      return next();
    } catch (error) {
      console.log('❌ Auth middleware error:', (error as Error).message);
      console.log('❌ Error stack:', (error as Error).stack);
      return res.status(401).json({ message: 'Unauthorized' });
    }
  };
}

// Export default para uso normal
export const authMiddleware = createAuthMiddleware();