import { Request, Response, NextFunction } from 'express';
import { PermissionService } from '../../../services/permission.service';

export const permissionMiddleware = (requiredPermissions: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const user = req.user;

    // LOG de depuración
    console.log('=== [PERMISSION MIDDLEWARE] ===');
    console.log('User:', JSON.stringify(user, null, 2));
    console.log('Required permissions:', requiredPermissions);
    
    if (!user) {
      console.log('No user found');
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Usar el nuevo servicio de permisos
    const hasAccess = PermissionService.canAccess(user, [], requiredPermissions);
    
    if (hasAccess) {
      console.log('✅ User has required permissions - granting access');
      return next();
    }
    
    console.log('❌ Permission denied - user lacks required permissions');
    const userPermissions = PermissionService.getUserPermissions(user);
    console.log('User permissions:', userPermissions);
    
    return res.status(403).json({ 
      message: 'Forbidden',
      requiredPermissions,
      userPermissions: userPermissions
    });
  };
};