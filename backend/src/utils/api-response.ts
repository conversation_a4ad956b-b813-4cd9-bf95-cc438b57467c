import { Response } from 'express';

interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T | T[];
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    totalPages?: number;
  };
  error?: {
    code: string;
    details?: any;
  };
}

export class ApiResponseBuilder<T = any> {
  private response: Response;
  private statusCode: number;
  private result: ApiResponse<T>;

  constructor(response: Response) {
    this.response = response;
    this.statusCode = 200;
    this.result = {
      success: true,
      message: '',
    };
  }

  withData(data: T): this {
    this.result.data = data as any;
    return this;
  }

  withMessage(message: string): this {
    this.result.message = message;
    return this;
  }

  withMeta(meta: ApiResponse<T>['meta']): this {
    this.result.meta = meta;
    return this;
  }

  withPagination(
    data: T[],
    total: number,
    page: number,
    limit: number,
  ): this {
    this.result.data = data;
    this.result.meta = {
      page: Number(page) || 1,
      limit: Number(limit) || 10,
      total,
      totalPages: Math.ceil(total / (Number(limit) || 10)),
    };
    return this;
  }

  withError(error: Error, code: string = 'INTERNAL_ERROR'): this {
    this.result.success = false;
    this.result.error = {
      code,
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    };
    
    // Mapear códigos de error a códigos de estado HTTP
    const statusMap: Record<string, number> = {
      NOT_FOUND: 404,
      UNAUTHORIZED: 401,
      FORBIDDEN: 403,
      BAD_REQUEST: 400,
      VALIDATION_ERROR: 422,
      INTERNAL_ERROR: 500,
    };

    this.statusCode = statusMap[code] || 500;
    return this;
  }

  withStatusCode(statusCode: number): this {
    this.statusCode = statusCode;
    return this;
  }

  send(): void {
    this.response.status(this.statusCode).json(this.result);
  }
}

// Funciones de ayuda para respuestas comunes
export const successResponse = <T>(
  res: Response,
  data: T,
  message: string = 'Operación exitosa',
  statusCode: number = 200,
) => {
  return new ApiResponseBuilder<T>(res)
    .withData(data)
    .withMessage(message)
    .withStatusCode(statusCode)
    .send();
};

export const errorResponse = (
  res: Response,
  error: Error,
  code: string = 'INTERNAL_ERROR',
  statusCode?: number,
) => {
  const response = new ApiResponseBuilder(res).withError(error, code);
  
  if (statusCode) {
    response.withStatusCode(statusCode);
  }
  
  response.send();
};

export const notFoundResponse = (res: Response, message: string = 'Recurso no encontrado') => {
  return new ApiResponseBuilder(res)
    .withError(new Error(message), 'NOT_FOUND')
    .withStatusCode(404)
    .send();
};

export const unauthorizedResponse = (res: Response, message: string = 'No autorizado') => {
  return new ApiResponseBuilder(res)
    .withError(new Error(message), 'UNAUTHORIZED')
    .withStatusCode(401)
    .send();
};

export const forbiddenResponse = (res: Response, message: string = 'Acceso denegado') => {
  return new ApiResponseBuilder(res)
    .withError(new Error(message), 'FORBIDDEN')
    .withStatusCode(403)
    .send();
};

export const badRequestResponse = (res: Response, message: string = 'Solicitud incorrecta') => {
  return new ApiResponseBuilder(res)
    .withError(new Error(message), 'BAD_REQUEST')
    .withStatusCode(400)
    .send();
};

export const validationErrorResponse = (res: Response, errors: any) => {
  return new ApiResponseBuilder(res)
    .withError(new Error('Error de validación'), 'VALIDATION_ERROR')
    .withStatusCode(422)
    .withData(errors as any)
    .send();
};
