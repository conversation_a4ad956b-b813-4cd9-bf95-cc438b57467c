// /backend/src/entities/client.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';

/**
 * @class Client
 * @description Entidad que representa a un cliente en la base de datos.
 *
 * Almacena toda la información relevante de un cliente, incluyendo datos
 * fiscales, de contacto y su estado. Es una entidad central para los
 * módulos de Ventas, Proyectos y Facturación.
 */
@Entity('clients')
export class Client {
  @PrimaryGeneratedColumn()
  id: number;

  // Datos Generales
  @Column({ type: 'varchar', length: 255, name: 'commercial_name' })
  commercialName: string;

  @Column({ type: 'varchar', length: 255, name: 'legal_name', nullable: true })
  legalName?: string;

  @Column({ type: 'varchar', length: 13, unique: true })
  rfc: string;

  @Column({ type: 'varchar', length: 50, name: 'tax_regime', nullable: true })
  taxRegime?: string;

  // Dirección Fiscal
  @Column({ type: 'varchar', length: 100, nullable: true })
  street?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  neighborhood?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  city?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  state?: string;

  @Column({ type: 'varchar', length: 10, name: 'zip_code', nullable: true })
  zipCode?: string;

  @Column({ type: 'varchar', length: 50, nullable: true, default: 'México' })
  country?: string;

  // Contacto Principal
  @Column({ type: 'varchar', length: 150, name: 'contact_name', nullable: true })
  contactName?: string;

  @Column({ type: 'varchar', length: 100, name: 'contact_position', nullable: true })
  contactPosition?: string;

  @Column("text", { array: true, nullable: true, name: 'contact_phones' })
  contactPhones?: string[];

  // Datos Comerciales
  @Column({ type: 'varchar', length: 100, nullable: true })
  industry?: string;

  @Column({ type: 'varchar', length: 100, name: 'company_area', nullable: true })
  companyArea?: string;

  @Column({ type: 'varchar', length: 50, name: 'company_size', nullable: true })
  companySize?: string;

  @Column({ type: 'varchar', length: 50, name: 'client_type', nullable: true, default: 'Prospecto' })
  clientType?: string;

  @Column({ type: 'integer', name: 'credit_limit', nullable: true, default: 0 })
  creditLimit?: number;

  @Column({ type: 'integer', name: 'credit_days', nullable: true, default: 0 })
  creditDays?: number;

  @Column({ type: 'varchar', length: 50, name: 'preferred_payment', nullable: true })
  preferredPayment?: string;

  // Información Adicional
  @Column({ type: 'varchar', length: 255, name: 'website', nullable: true })
  website?: string;

  @Column({ type: 'text', nullable: true })
  observations?: string;

  @Column({ type: 'varchar', length: 20, default: 'Activo' })
  status: string;

  @Column({ type: 'boolean', default: true })
  active: boolean;

  @Column({ type: 'boolean', name: 'is_quick_registration', default: false })
  isQuickRegistration: boolean;

  @Column({ type: 'boolean', name: 'rfc_validated', default: false })
  rfcValidated: boolean;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp with time zone' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp with time zone' })
  updatedAt: Date;

  // --- Relaciones ---

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'created_by_user_id' })
  createdByUser: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'assigned_salesperson_id' })
  assignedSalesperson?: User;

  @Column("text", { array: true, nullable: true, name: 'contact_emails' })
  contactEmails?: string[];
}