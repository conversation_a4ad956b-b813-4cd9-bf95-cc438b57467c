import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Check,
} from 'typeorm';

@Entity('products')
@Check(`"tipo_almacen" IN ('GENERAL', 'ROTATIVO')`)
@Check(`"estado" IN ('DISPONIBLE', 'AGOTADO', 'DESCONTINUADO')`)
@Check(`"stock_disponible" >= 0 AND "stock_comprometido" >= 0 AND "stock_minimo" >= 0`)
export class Product {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'codigo_item', unique: true, length: 100 })
  codigoItem: string;

  @Column({ name: 'nombre', length: 255 })
  nombre: string;

  @Column({ name: 'descripcion', type: 'text', nullable: true })
  descripcion?: string;

  @Column({ name: 'marca', length: 100, nullable: true })
  marca?: string;

  @Column({ name: 'modelo', length: 100, nullable: true })
  modelo?: string;

  @Column({ name: 'numero_serie', length: 100, nullable: true })
  numeroSerie?: string;

  @Column({ name: 'pedimento', length: 100, nullable: true })
  pedimento?: string;

  @Column({ name: 'observaciones', type: 'text', nullable: true })
  observaciones?: string;

  @Column({ 
    name: 'tipo_almacen', 
    length: 20, 
    default: 'GENERAL',
    type: 'varchar' 
  })
  tipoAlmacen: 'GENERAL' | 'ROTATIVO';

  @Column({ name: 'stock_disponible', type: 'int', default: 0 })
  stockDisponible: number;

  @Column({ name: 'stock_comprometido', type: 'int', default: 0 })
  stockComprometido: number;

  @Column({ name: 'stock_minimo', type: 'int', default: 1 })
  stockMinimo: number;

  @Column({ 
    name: 'estado', 
    length: 20, 
    default: 'DISPONIBLE',
    type: 'varchar' 
  })
  estado: 'DISPONIBLE' | 'AGOTADO' | 'DESCONTINUADO';

  @Column({ name: 'ubicacion', length: 100, default: 'GDL', nullable: true })
  ubicacion?: string;

  // Campos de auditoría
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by', nullable: true })
  createdBy?: number;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy?: number;

  @Column({ name: 'deleted_at', type: 'timestamptz', nullable: true })
  deletedAt?: Date;

  // Métodos de utilidad
  
  /**
   * Calcula el stock total disponible (disponible - comprometido)
   */
  getStockReal(): number {
    return Math.max(0, this.stockDisponible - this.stockComprometido);
  }

  /**
   * Determina el nivel de alerta del stock
   */
  getNivelStock(): 'CRITICO' | 'BAJO' | 'NORMAL' | 'ALTO' {
    const stockReal = this.getStockReal();
    
    if (stockReal === 0) return 'CRITICO';
    if (stockReal <= this.stockMinimo) return 'BAJO';
    if (stockReal <= 10) return 'NORMAL';
    return 'ALTO';
  }

  /**
   * Obtiene el color del indicador visual según el nivel de stock
   */
  getColorIndicador(): 'red' | 'yellow' | 'green' | 'blue' {
    const stockReal = this.getStockReal();
    
    if (stockReal === 0) return 'red'; // Sin stock
    if (stockReal <= 10) return 'yellow'; // Stock bajo
    if (this.stockComprometido > 0) return 'blue'; // Comprometido
    return 'green'; // Stock normal
  }

  /**
   * Verifica si el producto está disponible para venta
   */
  isDisponibleParaVenta(): boolean {
    return this.estado === 'DISPONIBLE' && this.getStockReal() > 0;
  }

  /**
   * Reserva stock comprometido
   */
  reservarStock(cantidad: number): boolean {
    if (this.getStockReal() >= cantidad) {
      this.stockComprometido += cantidad;
      return true;
    }
    return false;
  }

  /**
   * Libera stock comprometido
   */
  liberarStock(cantidad: number): boolean {
    if (this.stockComprometido >= cantidad) {
      this.stockComprometido -= cantidad;
      return true;
    }
    return false;
  }
} 