import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';

@Entity('audit_logs')
export class AuditLog {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ length: 255 })
  action: string;

  @Column({ length: 100, nullable: true })
  target_entity: string;

  @Column({ type: 'bigint', nullable: true })
  target_id: number;

  @Column({ type: 'jsonb', nullable: true })
  details: any;

  @Column({ length: 50, nullable: true })
  ip_address: string;

  @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  updated_at: Date;

  @Column({ name: 'created_by', nullable: true })
  created_by?: number;

  @Column({ name: 'updated_by', nullable: true })
  updated_by?: number;

  // Relaciones adicionales
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;
}