import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
  Check,
} from 'typeorm';
import { User } from './user.entity';
import { Product } from './product.entity';

export enum MovementType {
  INPUT = 'INPUT',
  OUTPUT = 'OUTPUT',
  LOAN = 'LOAN',
  RETURN = 'RETURN',
  ADJUSTMENT = 'ADJUSTMENT'
}

export enum MovementStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  CANCELLED = 'CANCELLED',
  COMPLETED = 'COMPLETED'
}

@Entity('inventory_movements')
@Check(`"tipo" IN ('INPUT', 'OUTPUT', 'LOAN', 'RETURN', 'ADJUSTMENT')`)
@Check(`"estado" IN ('PENDING', 'CONFIRMED', 'CANCELLED', 'COMPLETED')`)
@Check(`"cantidad" > 0`)
export class InventoryMovement {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'folio', unique: true, length: 50 })
  folio: string;

  @Column({ 
    name: 'tipo', 
    type: 'enum', 
    enum: MovementType 
  })
  tipo: MovementType;

  @Column({ 
    name: 'estado', 
    type: 'enum', 
    enum: MovementStatus,
    default: MovementStatus.PENDING
  })
  estado: MovementStatus;

  @Column({ name: 'cantidad', type: 'int' })
  cantidad: number;

  @Column({ name: 'motivo', type: 'text', nullable: true })
  motivo?: string;

  @Column({ name: 'observaciones', type: 'text', nullable: true })
  observaciones?: string;

  @Column({ name: 'qr_data_scanned', type: 'text', nullable: true })
  qrDataScanned?: string;

  @Column({ name: 'voucher_id', nullable: true })
  voucherId?: number;

  @Column({ name: 'voucher_path', length: 255, nullable: true })
  voucherPath?: string;

  @Column({ name: 'fecha_movimiento', type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  fechaMovimiento: Date;

  @Column({ name: 'fecha_confirmacion', type: 'timestamptz', nullable: true })
  fechaConfirmacion?: Date;

  // Relaciones
  @ManyToOne(() => Product, { nullable: false })
  @JoinColumn({ name: 'producto_id' })
  producto: Product;

  @Column({ name: 'producto_id' })
  productoId: number;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'usuario_id' })
  usuario: User;

  @Column({ name: 'usuario_id' })
  usuarioId: number;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'confirmado_por' })
  confirmadoPor?: User;

  @Column({ name: 'confirmado_por', nullable: true })
  confirmadoPorId?: number;

  // Campos de auditoría
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by', nullable: true })
  createdBy?: number;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy?: number;

  // Métodos de utilidad

  /**
   * Genera un folio único basado en el tipo de movimiento
   */
  static generateFolio(tipo: MovementType, sequence: number): string {
    const year = new Date().getFullYear();
    const prefix = tipo === MovementType.INPUT ? 'ENT' : 
                   tipo === MovementType.OUTPUT ? 'SAL' :
                   tipo === MovementType.LOAN ? 'PREST' :
                   tipo === MovementType.RETURN ? 'DEV' : 'AJUST';
    
    return `${prefix}-${year}-${sequence.toString().padStart(4, '0')}`;
  }

  /**
   * Verifica si el movimiento puede ser confirmado
   */
  canBeConfirmed(): boolean {
    return this.estado === MovementStatus.PENDING;
  }

  /**
   * Verifica si el movimiento puede ser cancelado
   */
  canBeCancelled(): boolean {
    return this.estado === MovementStatus.PENDING || this.estado === MovementStatus.CONFIRMED;
  }

  /**
   * Marca el movimiento como confirmado
   */
  confirm(userId: number): void {
    if (!this.canBeConfirmed()) {
      throw new Error('El movimiento no puede ser confirmado');
    }
    
    this.estado = MovementStatus.CONFIRMED;
    this.confirmadoPorId = userId;
    this.fechaConfirmacion = new Date();
  }

  /**
   * Marca el movimiento como completado
   */
  complete(): void {
    if (this.estado !== MovementStatus.CONFIRMED) {
      throw new Error('Solo los movimientos confirmados pueden ser completados');
    }
    
    this.estado = MovementStatus.COMPLETED;
  }

  /**
   * Cancela el movimiento
   */
  cancel(): void {
    if (!this.canBeCancelled()) {
      throw new Error('El movimiento no puede ser cancelado');
    }
    
    this.estado = MovementStatus.CANCELLED;
  }
}