import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { Permission } from './permission.entity';

@Entity('roles')
export class Role {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 40, unique: true })
  name: string;

  // @Column({ length: 255, nullable: true })
  // description?: string; // ← COMENTADO: No existe en Supabase

  // @CreateDateColumn({ name: 'created_at', type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  // created_at: Date;

  // @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  // updated_at: Date;

  // @Column({ name: 'created_by', nullable: true })
  // created_by?: number;

  // @Column({ name: 'updated_by', nullable: true })
  // updated_by?: number;

  // @Column({ name: 'deleted_at', type: 'timestamptz', nullable: true })
  // deleted_at?: Date; // ← COMENTADO: No existe en Supabase

  // Relaciones
  // @ManyToOne(() => User, { nullable: true })
  // @JoinColumn({ name: 'created_by' })
  // creator?: User;

  // @ManyToOne(() => User, { nullable: true })
  // @JoinColumn({ name: 'updated_by' })
  // updater?: User;

  @ManyToMany(() => Permission)
  @JoinTable({
    name: 'role_permissions',
    joinColumn: { name: 'role_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'permission_id', referencedColumnName: 'id' },
  })
  permissions: Permission[];

  // Métodos de utilidad
  // isDeleted(): boolean {
  //   return this.deleted_at !== null;
  // }

  // softDelete(): void {
  //   this.deleted_at = new Date();
  // }

  // restore(): void {
  //   this.deleted_at = undefined;
  // } // ← COMENTADOS: Dependen de deleted_at que no existe
}