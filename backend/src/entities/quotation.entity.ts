import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Client } from './client.entity';
import { User } from './user.entity';

@Entity('quotations')
export class Quotation {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => Client, { nullable: false })
  @JoinColumn({ name: 'customer_id' })
  client: Client;

  @Column({ name: 'project_id', type: 'bigint', nullable: true })
  projectId?: number;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'created_by' })
  createdBy: User;

  @Column({ name: 'quotation_number', type: 'varchar', length: 50, nullable: true })
  quotationNumber?: string;

  @Column({ name: 'total_amount', type: 'numeric', precision: 15, scale: 2, nullable: true })
  totalAmount?: number;

  @Column({ name: 'status', type: 'varchar', length: 50, default: 'DRAFT' })
  status: string;

  @Column({ name: 'valid_until', type: 'date', nullable: true })
  validUntil?: Date;

  @Column({ name: 'notes', type: 'text', nullable: true })
  notes?: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp with time zone' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp with time zone' })
  updatedAt: Date;
} 