import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'typeorm';
import { InventoryMovement } from './inventory-movement.entity';
import { User } from './user.entity';

export enum VoucherType {
  INPUT_VOUCHER = 'INPUT_VOUCHER',
  OUTPUT_VOUCHER = 'OUTPUT_VOUCHER',
  LOAN_VOUCHER = 'LOAN_VOUCHER',
  RETURN_VOUCHER = 'RETURN_VOUCHER'
}

export enum VoucherStatus {
  GENERATED = 'GENERATED',
  SENT = 'SENT',
  DOWNLOADED = 'DOWNLOADED',
  EXPIRED = 'EXPIRED'
}

@Entity('vouchers')
@Check(`"tipo" IN ('INPUT_VOUCHER', 'OUTPUT_VOUCHER', 'LOAN_VOUCHER', 'RETURN_VOUCHER')`)
@Check(`"estado" IN ('GENERATED', 'SENT', 'DOWNLOADED', 'EXPIRED')`)
export class Voucher {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'folio', unique: true, length: 50 })
  folio: string;

  @Column({ 
    name: 'tipo', 
    type: 'enum', 
    enum: VoucherType 
  })
  tipo: VoucherType;

  @Column({ 
    name: 'estado', 
    type: 'enum', 
    enum: VoucherStatus,
    default: VoucherStatus.GENERATED
  })
  estado: VoucherStatus;

  @Column({ name: 'titulo', length: 255 })
  titulo: string;

  @Column({ name: 'descripcion', type: 'text', nullable: true })
  descripcion?: string;

  @Column({ name: 'file_path', length: 500 })
  filePath: string;

  @Column({ name: 'file_name', length: 255 })
  fileName: string;

  @Column({ name: 'file_size', type: 'bigint', nullable: true })
  fileSize?: number;

  @Column({ name: 'mime_type', length: 100, default: 'application/pdf' })
  mimeType: string;

  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @Column({ name: 'fecha_generacion', type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  fechaGeneracion: Date;

  @Column({ name: 'fecha_descarga', type: 'timestamptz', nullable: true })
  fechaDescarga?: Date;

  @Column({ name: 'fecha_expiracion', type: 'timestamptz', nullable: true })
  fechaExpiracion?: Date;

  @Column({ name: 'descargas_count', type: 'int', default: 0 })
  descargasCount: number;

  // Relaciones
  @ManyToOne(() => InventoryMovement, { nullable: true })
  @JoinColumn({ name: 'movimiento_id' })
  movimiento?: InventoryMovement;

  @Column({ name: 'movimiento_id', nullable: true })
  movimientoId?: number;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'generado_por' })
  generadoPor: User;

  @Column({ name: 'generado_por' })
  generadoPorId: number;

  // Campos de auditoría
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by', nullable: true })
  createdBy?: number;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy?: number;

  // Métodos de utilidad

  /**
   * Genera un folio único para el voucher
   */
  static generateFolio(tipo: VoucherType, sequence: number): string {
    const year = new Date().getFullYear();
    const prefix = tipo === VoucherType.INPUT_VOUCHER ? 'VALE-ENT' : 
                   tipo === VoucherType.OUTPUT_VOUCHER ? 'VALE-SAL' :
                   tipo === VoucherType.LOAN_VOUCHER ? 'VALE-PREST' :
                   tipo === VoucherType.RETURN_VOUCHER ? 'VALE-DEV' : 'VALE';
    
    return `${prefix}-${year}-${sequence.toString().padStart(4, '0')}`;
  }

  /**
   * Verifica si el voucher ha expirado
   */
  isExpired(): boolean {
    if (!this.fechaExpiracion) return false;
    return new Date() > this.fechaExpiracion;
  }

  /**
   * Marca el voucher como descargado
   */
  markAsDownloaded(): void {
    this.estado = VoucherStatus.DOWNLOADED;
    this.fechaDescarga = new Date();
    this.descargasCount += 1;
  }

  /**
   * Marca el voucher como enviado
   */
  markAsSent(): void {
    this.estado = VoucherStatus.SENT;
  }

  /**
   * Marca el voucher como expirado
   */
  markAsExpired(): void {
    this.estado = VoucherStatus.EXPIRED;
  }

  /**
   * Obtiene la URL de descarga del voucher
   */
  getDownloadUrl(): string {
    return `/api/vouchers/${this.id}/download`;
  }

  /**
   * Obtiene la URL de vista previa del voucher
   */
  getPreviewUrl(): string {
    return `/api/vouchers/${this.id}/preview`;
  }
}