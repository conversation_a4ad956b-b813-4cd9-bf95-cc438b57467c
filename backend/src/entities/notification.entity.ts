import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Role } from './role.entity';

export enum NotificationType {
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  SYSTEM = 'system',
  TASK = 'task',
  REMINDER = 'reminder'
}

export enum NotificationStatus {
  UNREAD = 'unread',
  READ = 'read',
  DISMISSED = 'dismissed'
}

export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

@Entity('notifications')
@Index('IDX_notifications_user_status_created', ['user_id', 'status', 'created_at'])
@Index('IDX_notifications_role_status_created', ['role_id', 'status', 'created_at'])
@Index('IDX_notifications_type_created', ['type', 'created_at'])
export class Notification {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ length: 200 })
  title: string;

  @Column({ type: 'text' })
  message: string;

  @Column({
    type: 'enum',
    enum: NotificationType,
    default: NotificationType.INFO
  })
  type: NotificationType;

  @Column({
    type: 'enum',
    enum: NotificationStatus,
    default: NotificationStatus.UNREAD
  })
  status: NotificationStatus;

  @Column({
    type: 'enum',
    enum: NotificationPriority,
    default: NotificationPriority.MEDIUM
  })
  priority: NotificationPriority;

  @Column({ name: 'user_id', nullable: true })
  user_id?: number;

  @Column({ name: 'role_id', nullable: true })
  role_id?: number;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: any;

  @Column({ name: 'action_url', length: 100, nullable: true })
  action_url?: string;

  @Column({ name: 'action_label', length: 50, nullable: true })
  action_label?: string;

  @Column({ name: 'read_at', type: 'timestamptz', nullable: true })
  read_at?: Date;

  @Column({ name: 'expires_at', type: 'timestamptz', nullable: true })
  expires_at?: Date;

  @Column({ name: 'email_sent', type: 'boolean', default: false })
  email_sent: boolean;

  @Column({ name: 'email_sent_at', type: 'timestamptz', nullable: true })
  email_sent_at?: Date;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  updated_at: Date;

  @Column({ name: 'created_by', nullable: true })
  created_by?: number;

  @Column({ name: 'updated_by', nullable: true })
  updated_by?: number;

  // Relaciones
  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'user_id' })
  user?: User;

  @ManyToOne(() => Role, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'role_id' })
  role?: Role;

  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  // Métodos de utilidad
  markAsRead(): void {
    this.status = NotificationStatus.READ;
    this.read_at = new Date();
  }

  isExpired(): boolean {
    return this.expires_at ? new Date() > this.expires_at : false;
  }

  canBeDismissed(): boolean {
    return this.status !== NotificationStatus.DISMISSED;
  }
} 