import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index
} from 'typeorm';
import { User } from './user.entity';

export enum RequestType {
  VIATICOS = 'VIATICOS',
  TIEMPO_EXTRA = 'TIEMPO_EXTRA',
  PAQUETERIA = 'PAQUETERIA'
}

export enum RequestStatus {
  DRAFT = 'DRAFT',
  SUBMITTED = 'SUBMITTED',
  PENDING_APPROVAL = 'PENDING_APPROVAL',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELLED = 'CANCELLED'
}

export enum ApprovalLevel {
  MANAGER = 'MANAGER',
  DIRECTOR = 'DIRECTOR',
  ADMIN = 'ADMIN'
}

@Entity('internal_requests')
@Index(['requestType', 'status'])
@Index(['requesterId', 'status'])
@Index(['approverId', 'status'])
export class InternalRequest {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'enum',
    enum: RequestType,
    nullable: false
  })
  requestType: RequestType;

  @Column({
    type: 'enum',
    enum: RequestStatus,
    default: RequestStatus.DRAFT
  })
  status: RequestStatus;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: false
  })
  title: string;

  @Column({
    type: 'text',
    nullable: true
  })
  description: string;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true
  })
  estimatedCost: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true
  })
  approvedAmount: number;

  @Column({
    type: 'date',
    nullable: true
  })
  startDate: Date;

  @Column({
    type: 'date',
    nullable: true
  })
  endDate: Date;

  @Column({
    type: 'jsonb',
    nullable: true
  })
  requestData: Record<string, any>;

  @Column({
    type: 'jsonb',
    nullable: true
  })
  approvalHistory: Array<{
    level: ApprovalLevel;
    approverId: number;
    approverName: string;
    action: 'APPROVED' | 'REJECTED' | 'REQUEST_INFO';
    comment?: string;
    timestamp: Date;
  }>;

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true
  })
  rejectionReason: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true
  })
  attachments?: string; // URLs separadas por comas

  @Column({
    type: 'enum',
    enum: ApprovalLevel,
    default: ApprovalLevel.MANAGER
  })
  currentApprovalLevel: ApprovalLevel;

  @Column({
    type: 'boolean',
    default: false
  })
  isUrgent: boolean;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true
  })
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';

  // Relaciones
  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'requester_id' })
  requester: User;

  @Column({ name: 'requester_id' })
  requesterId: number;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'approver_id' })
  approver: User;

  @Column({ name: 'approver_id', nullable: true })
  approverId: number;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  createdBy: User;

  @Column({ name: 'created_by', nullable: true })
  createdById: number;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updatedBy: User;

  @Column({ name: 'updated_by', nullable: true })
  updatedById: number;

  // Timestamps
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true
  })
  submittedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true
  })
  approvedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true
  })
  rejectedAt: Date;

  // Métodos de utilidad
  canBeSubmitted(): boolean {
    return this.status === RequestStatus.DRAFT;
  }

  canBeApproved(): boolean {
    return this.status === RequestStatus.PENDING_APPROVAL;
  }

  canBeRejected(): boolean {
    return this.status === RequestStatus.PENDING_APPROVAL;
  }

  canBeCancelled(): boolean {
    return [RequestStatus.DRAFT, RequestStatus.SUBMITTED, RequestStatus.PENDING_APPROVAL].includes(this.status);
  }

  isCompleted(): boolean {
    return [RequestStatus.APPROVED, RequestStatus.REJECTED, RequestStatus.CANCELLED].includes(this.status);
  }

  addApprovalHistory(
    level: ApprovalLevel,
    approverId: number,
    approverName: string,
    action: 'APPROVED' | 'REJECTED' | 'REQUEST_INFO',
    comment?: string
  ): void {
    if (!this.approvalHistory) {
      this.approvalHistory = [];
    }

    this.approvalHistory.push({
      level,
      approverId,
      approverName,
      action,
      comment,
      timestamp: new Date()
    });
  }

  getLastApprovalAction(): any {
    if (!this.approvalHistory || this.approvalHistory.length === 0) {
      return null;
    }
    return this.approvalHistory[this.approvalHistory.length - 1];
  }
}