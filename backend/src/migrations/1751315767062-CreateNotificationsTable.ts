import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateNotificationsTable1751315767062 implements MigrationInterface {
    name = 'CreateNotificationsTable1751315767062'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Crear tipos enum
        await queryRunner.query(`
            CREATE TYPE "notification_type_enum" AS ENUM(
                'info', 'success', 'warning', 'error', 'system', 'task', 'reminder'
            )
        `);
        
        await queryRunner.query(`
            CREATE TYPE "notification_status_enum" AS ENUM(
                'unread', 'read', 'dismissed'
            )
        `);
        
        await queryRunner.query(`
            CREATE TYPE "notification_priority_enum" AS ENUM(
                'low', 'medium', 'high', 'urgent'
            )
        `);

        // Crear tabla notifications
        await queryRunner.query(`
            CREATE TABLE "notifications" (
                "id" SERIAL NOT NULL,
                "title" character varying(200) NOT NULL,
                "message" text NOT NULL,
                "type" "notification_type_enum" NOT NULL DEFAULT 'info',
                "status" "notification_status_enum" NOT NULL DEFAULT 'unread',
                "priority" "notification_priority_enum" NOT NULL DEFAULT 'medium',
                "user_id" integer,
                "role_id" integer,
                "metadata" jsonb,
                "action_url" character varying(100),
                "action_label" character varying(50),
                "read_at" TIMESTAMP WITH TIME ZONE,
                "expires_at" TIMESTAMP WITH TIME ZONE,
                "email_sent" boolean NOT NULL DEFAULT false,
                "email_sent_at" TIMESTAMP WITH TIME ZONE,
                "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "created_by" integer,
                "updated_by" integer,
                CONSTRAINT "PK_notifications" PRIMARY KEY ("id")
            )
        `);

        // Crear índices para optimizar consultas
        await queryRunner.query(`
            CREATE INDEX "IDX_notifications_user_status_created" 
            ON "notifications" ("user_id", "status", "created_at")
        `);
        
        await queryRunner.query(`
            CREATE INDEX "IDX_notifications_role_status_created" 
            ON "notifications" ("role_id", "status", "created_at")
        `);
        
        await queryRunner.query(`
            CREATE INDEX "IDX_notifications_type_created" 
            ON "notifications" ("type", "created_at")
        `);

        // Agregar foreign keys
        await queryRunner.query(`
            ALTER TABLE "notifications" 
            ADD CONSTRAINT "FK_notifications_user" 
            FOREIGN KEY ("user_id") 
            REFERENCES "users"("id") 
            ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        
        await queryRunner.query(`
            ALTER TABLE "notifications" 
            ADD CONSTRAINT "FK_notifications_role" 
            FOREIGN KEY ("role_id") 
            REFERENCES "roles"("id") 
            ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        
        await queryRunner.query(`
            ALTER TABLE "notifications" 
            ADD CONSTRAINT "FK_notifications_creator" 
            FOREIGN KEY ("created_by") 
            REFERENCES "users"("id") 
            ON DELETE SET NULL ON UPDATE NO ACTION
        `);
        
        await queryRunner.query(`
            ALTER TABLE "notifications" 
            ADD CONSTRAINT "FK_notifications_updater" 
            FOREIGN KEY ("updated_by") 
            REFERENCES "users"("id") 
            ON DELETE SET NULL ON UPDATE NO ACTION
        `);

        // Constraint para asegurar que se especifique user_id O role_id
        await queryRunner.query(`
            ALTER TABLE "notifications" 
            ADD CONSTRAINT "CHK_notifications_recipient" 
            CHECK (
                (user_id IS NOT NULL AND role_id IS NULL) OR 
                (user_id IS NULL AND role_id IS NOT NULL)
            )
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Eliminar tabla
        await queryRunner.query(`DROP TABLE "notifications"`);
        
        // Eliminar tipos enum
        await queryRunner.query(`DROP TYPE "notification_priority_enum"`);
        await queryRunner.query(`DROP TYPE "notification_status_enum"`);
        await queryRunner.query(`DROP TYPE "notification_type_enum"`);
    }
} 