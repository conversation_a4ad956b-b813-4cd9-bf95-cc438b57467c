import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateInventoryMovementsAndVouchers1751097259 implements MigrationInterface {
  name = 'CreateInventoryMovementsAndVouchers1751097259';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enum types
    await queryRunner.query(`
      CREATE TYPE "public"."inventory_movement_type_enum" AS ENUM('INPUT', 'OUTPUT', 'LOAN', 'RETURN', 'ADJUSTMENT')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."inventory_movement_status_enum" AS ENUM('PENDING', 'CONFIRMED', 'CANCELLED', 'COMPLETED')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."voucher_type_enum" AS ENUM('INPUT_VOUCHER', 'OUTPUT_VOUCHER', 'LOAN_VOUCHER', 'RETURN_VOUCHER')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."voucher_status_enum" AS ENUM('GENERATED', 'SENT', 'DOWNLOADED', 'EXPIRED')
    `);

    // Create inventory_movements table
    await queryRunner.query(`
      CREATE TABLE "inventory_movements" (
        "id" BIGSERIAL NOT NULL,
        "folio" character varying(50) NOT NULL,
        "tipo" "public"."inventory_movement_type_enum" NOT NULL,
        "estado" "public"."inventory_movement_status_enum" NOT NULL DEFAULT 'PENDING',
        "cantidad" integer NOT NULL,
        "motivo" text,
        "observaciones" text,
        "qr_data_scanned" text,
        "voucher_id" integer,
        "voucher_path" character varying(255),
        "fecha_movimiento" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "fecha_confirmacion" TIMESTAMP WITH TIME ZONE,
        "producto_id" integer NOT NULL,
        "usuario_id" integer NOT NULL,
        "confirmado_por" integer,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "created_by" integer,
        "updated_by" integer,
        CONSTRAINT "PK_inventory_movements" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_inventory_movements_folio" UNIQUE ("folio"),
        CONSTRAINT "CHK_inventory_movements_cantidad" CHECK ("cantidad" > 0)
      )
    `);

    // Create vouchers table
    await queryRunner.query(`
      CREATE TABLE "vouchers" (
        "id" BIGSERIAL NOT NULL,
        "folio" character varying(50) NOT NULL,
        "tipo" "public"."voucher_type_enum" NOT NULL,
        "estado" "public"."voucher_status_enum" NOT NULL DEFAULT 'GENERATED',
        "titulo" character varying(255) NOT NULL,
        "descripcion" text,
        "file_path" character varying(500) NOT NULL,
        "file_name" character varying(255) NOT NULL,
        "file_size" bigint,
        "mime_type" character varying(100) NOT NULL DEFAULT 'application/pdf',
        "metadata" jsonb,
        "fecha_generacion" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "fecha_descarga" TIMESTAMP WITH TIME ZONE,
        "fecha_expiracion" TIMESTAMP WITH TIME ZONE,
        "descargas_count" integer NOT NULL DEFAULT '0',
        "movimiento_id" integer,
        "generado_por" integer NOT NULL,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "created_by" integer,
        "updated_by" integer,
        CONSTRAINT "PK_vouchers" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_vouchers_folio" UNIQUE ("folio")
      )
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "inventory_movements" 
      ADD CONSTRAINT "FK_inventory_movements_producto" 
      FOREIGN KEY ("producto_id") REFERENCES "products"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "inventory_movements" 
      ADD CONSTRAINT "FK_inventory_movements_usuario" 
      FOREIGN KEY ("usuario_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "inventory_movements" 
      ADD CONSTRAINT "FK_inventory_movements_confirmado_por" 
      FOREIGN KEY ("confirmado_por") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "inventory_movements" 
      ADD CONSTRAINT "FK_inventory_movements_created_by" 
      FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "inventory_movements" 
      ADD CONSTRAINT "FK_inventory_movements_updated_by" 
      FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "vouchers" 
      ADD CONSTRAINT "FK_vouchers_movimiento" 
      FOREIGN KEY ("movimiento_id") REFERENCES "inventory_movements"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "vouchers" 
      ADD CONSTRAINT "FK_vouchers_generado_por" 
      FOREIGN KEY ("generado_por") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "vouchers" 
      ADD CONSTRAINT "FK_vouchers_created_by" 
      FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "vouchers" 
      ADD CONSTRAINT "FK_vouchers_updated_by" 
      FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    // Create indexes for performance
    await queryRunner.query(`
      CREATE INDEX "IDX_inventory_movements_folio" ON "inventory_movements" ("folio")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_inventory_movements_tipo" ON "inventory_movements" ("tipo")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_inventory_movements_estado" ON "inventory_movements" ("estado")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_inventory_movements_producto_id" ON "inventory_movements" ("producto_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_inventory_movements_usuario_id" ON "inventory_movements" ("usuario_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_inventory_movements_fecha_movimiento" ON "inventory_movements" ("fecha_movimiento")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_inventory_movements_qr_data" ON "inventory_movements" ("qr_data_scanned") WHERE "qr_data_scanned" IS NOT NULL
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_vouchers_folio" ON "vouchers" ("folio")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_vouchers_tipo" ON "vouchers" ("tipo")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_vouchers_estado" ON "vouchers" ("estado")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_vouchers_movimiento_id" ON "vouchers" ("movimiento_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_vouchers_generado_por" ON "vouchers" ("generado_por")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_vouchers_fecha_generacion" ON "vouchers" ("fecha_generacion")
    `);

    // Create sequence for folio generation
    await queryRunner.query(`
      CREATE SEQUENCE IF NOT EXISTS "inventory_movement_folio_seq" 
      START WITH 1 
      INCREMENT BY 1 
      NO MINVALUE 
      NO MAXVALUE 
      CACHE 1
    `);

    await queryRunner.query(`
      CREATE SEQUENCE IF NOT EXISTS "voucher_folio_seq" 
      START WITH 1 
      INCREMENT BY 1 
      NO MINVALUE 
      NO MAXVALUE 
      CACHE 1
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_vouchers_fecha_generacion"`);
    await queryRunner.query(`DROP INDEX "IDX_vouchers_generado_por"`);
    await queryRunner.query(`DROP INDEX "IDX_vouchers_movimiento_id"`);
    await queryRunner.query(`DROP INDEX "IDX_vouchers_estado"`);
    await queryRunner.query(`DROP INDEX "IDX_vouchers_tipo"`);
    await queryRunner.query(`DROP INDEX "IDX_vouchers_folio"`);
    await queryRunner.query(`DROP INDEX "IDX_inventory_movements_qr_data"`);
    await queryRunner.query(`DROP INDEX "IDX_inventory_movements_fecha_movimiento"`);
    await queryRunner.query(`DROP INDEX "IDX_inventory_movements_usuario_id"`);
    await queryRunner.query(`DROP INDEX "IDX_inventory_movements_producto_id"`);
    await queryRunner.query(`DROP INDEX "IDX_inventory_movements_estado"`);
    await queryRunner.query(`DROP INDEX "IDX_inventory_movements_tipo"`);
    await queryRunner.query(`DROP INDEX "IDX_inventory_movements_folio"`);

    // Drop foreign key constraints
    await queryRunner.query(`ALTER TABLE "vouchers" DROP CONSTRAINT "FK_vouchers_updated_by"`);
    await queryRunner.query(`ALTER TABLE "vouchers" DROP CONSTRAINT "FK_vouchers_created_by"`);
    await queryRunner.query(`ALTER TABLE "vouchers" DROP CONSTRAINT "FK_vouchers_generado_por"`);
    await queryRunner.query(`ALTER TABLE "vouchers" DROP CONSTRAINT "FK_vouchers_movimiento"`);
    await queryRunner.query(`ALTER TABLE "inventory_movements" DROP CONSTRAINT "FK_inventory_movements_updated_by"`);
    await queryRunner.query(`ALTER TABLE "inventory_movements" DROP CONSTRAINT "FK_inventory_movements_created_by"`);
    await queryRunner.query(`ALTER TABLE "inventory_movements" DROP CONSTRAINT "FK_inventory_movements_confirmado_por"`);
    await queryRunner.query(`ALTER TABLE "inventory_movements" DROP CONSTRAINT "FK_inventory_movements_usuario"`);
    await queryRunner.query(`ALTER TABLE "inventory_movements" DROP CONSTRAINT "FK_inventory_movements_producto"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE "vouchers"`);
    await queryRunner.query(`DROP TABLE "inventory_movements"`);

    // Drop sequences
    await queryRunner.query(`DROP SEQUENCE IF EXISTS "voucher_folio_seq"`);
    await queryRunner.query(`DROP SEQUENCE IF EXISTS "inventory_movement_folio_seq"`);

    // Drop enum types
    await queryRunner.query(`DROP TYPE "public"."voucher_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."voucher_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."inventory_movement_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."inventory_movement_type_enum"`);
  }
}