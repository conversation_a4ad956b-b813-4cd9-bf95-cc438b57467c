import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateRoleNameLength1751097259 implements MigrationInterface {
    name = 'UpdateRoleNameLength1751097259'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "roles" ALTER COLUMN "name" TYPE character varying(40)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "roles" ALTER COLUMN "name" TYPE character varying(20)`);
    }
}
