import { MigrationInterface, QueryRunner } from "typeorm";

export class OptimizeTablesAndIndexes1751316200000 implements MigrationInterface {
    name = 'OptimizeTablesAndIndexes1751316200000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 1. Optimizar tabla user_roles - añadir índice inverso
        const userRolesIdx = await queryRunner.query(`
            SELECT indexname FROM pg_indexes 
            WHERE tablename = 'user_roles' AND indexname = 'IDX_user_roles_role_user'
        `);
        
        if (userRolesIdx.length === 0) {
            await queryRunner.query(`
                CREATE INDEX "IDX_user_roles_role_user" 
                ON "user_roles" ("role_id", "user_id")
            `);
        }

        // 2. Optimizar tabla role_permissions - añadir índice inverso
        const rolePermsIdx = await queryRunner.query(`
            SELECT indexname FROM pg_indexes 
            WHERE tablename = 'role_permissions' AND indexname = 'IDX_role_permissions_permission_role'
        `);
        
        if (rolePermsIdx.length === 0) {
            await queryRunner.query(`
                CREATE INDEX "IDX_role_permissions_permission_role" 
                ON "role_permissions" ("permission_id", "role_id")
            `);
        }

        // 3. Añadir campos de auditoría a roles si no existen
        const rolesColumns = await queryRunner.query(`
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = 'roles' AND column_name IN ('created_at', 'updated_at', 'created_by', 'updated_by')
        `);
        
        const existingCols = rolesColumns.map((row: any) => row.column_name);
        
        if (!existingCols.includes('created_at')) {
            await queryRunner.query(`
                ALTER TABLE "roles" 
                ADD COLUMN "created_at" TIMESTAMP WITH TIME ZONE DEFAULT now()
            `);
        }
        
        if (!existingCols.includes('updated_at')) {
            await queryRunner.query(`
                ALTER TABLE "roles" 
                ADD COLUMN "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now()
            `);
        }
        
        if (!existingCols.includes('created_by')) {
            await queryRunner.query(`
                ALTER TABLE "roles" 
                ADD COLUMN "created_by" integer
            `);
        }
        
        if (!existingCols.includes('updated_by')) {
            await queryRunner.query(`
                ALTER TABLE "roles" 
                ADD COLUMN "updated_by" integer
            `);
        }
        
        // Añadir FK solo si no existen
        const rolesConstraints = await queryRunner.query(`
            SELECT constraint_name FROM information_schema.table_constraints 
            WHERE table_name = 'roles' AND constraint_name IN ('FK_roles_creator', 'FK_roles_updater')
        `);
        
        const existingConstraints = rolesConstraints.map((row: any) => row.constraint_name);
        
        if (!existingConstraints.includes('FK_roles_creator')) {
            await queryRunner.query(`
                ALTER TABLE "roles" 
                ADD CONSTRAINT "FK_roles_creator" 
                FOREIGN KEY ("created_by") 
                REFERENCES "users"("id") 
                ON DELETE SET NULL
            `);
        }
        
        if (!existingConstraints.includes('FK_roles_updater')) {
            await queryRunner.query(`
                ALTER TABLE "roles" 
                ADD CONSTRAINT "FK_roles_updater" 
                FOREIGN KEY ("updated_by") 
                REFERENCES "users"("id") 
                ON DELETE SET NULL
            `);
        }

        // 4. Añadir campos de auditoría a audit_logs si no existen
        const auditColumns = await queryRunner.query(`
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = 'audit_logs' AND column_name IN ('created_by', 'updated_by', 'updated_at')
        `);
        
        const existingAuditCols = auditColumns.map((row: any) => row.column_name);
        
        if (!existingAuditCols.includes('created_by')) {
            await queryRunner.query(`
                ALTER TABLE "audit_logs"
                ADD COLUMN "created_by" integer
            `);
        }
        
        if (!existingAuditCols.includes('updated_by')) {
            await queryRunner.query(`
                ALTER TABLE "audit_logs"
                ADD COLUMN "updated_by" integer
            `);
        }
        
        if (!existingAuditCols.includes('updated_at')) {
            await queryRunner.query(`
                ALTER TABLE "audit_logs"
                ADD COLUMN "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now()
            `);
        }
        
        // Añadir FK para audit_logs si no existen
        const auditConstraints = await queryRunner.query(`
            SELECT constraint_name FROM information_schema.table_constraints 
            WHERE table_name = 'audit_logs' AND constraint_name IN ('FK_audit_logs_creator', 'FK_audit_logs_updater')
        `);
        
        const existingAuditConstraints = auditConstraints.map((row: any) => row.constraint_name);
        
        if (!existingAuditConstraints.includes('FK_audit_logs_creator')) {
            await queryRunner.query(`
                ALTER TABLE "audit_logs" 
                ADD CONSTRAINT "FK_audit_logs_creator" 
                FOREIGN KEY ("created_by") 
                REFERENCES "users"("id") 
                ON DELETE SET NULL
            `);
        }
        
        if (!existingAuditConstraints.includes('FK_audit_logs_updater')) {
            await queryRunner.query(`
                ALTER TABLE "audit_logs" 
                ADD CONSTRAINT "FK_audit_logs_updater" 
                FOREIGN KEY ("updated_by") 
                REFERENCES "users"("id") 
                ON DELETE SET NULL
            `);
        }

        // 5. Añadir índices básicos si no existen
        const basicIndexes = [
            { table: 'audit_logs', name: 'IDX_audit_logs_user_created', columns: '("user_id", "created_at" DESC)' },
            { table: 'permissions', name: 'IDX_permissions_module', columns: '("module")' }
        ];
        
        for (const idx of basicIndexes) {
            const existingIdx = await queryRunner.query(`
                SELECT indexname FROM pg_indexes 
                WHERE tablename = '${idx.table}' AND indexname = '${idx.name}'
            `);
            
            if (existingIdx.length === 0) {
                await queryRunner.query(`
                    CREATE INDEX "${idx.name}" 
                    ON "${idx.table}" ${idx.columns}
                `);
            }
        }

        // 6. Añadir constraint único para permisos si no existe
        const permissionsConstraint = await queryRunner.query(`
            SELECT constraint_name FROM information_schema.table_constraints 
            WHERE table_name = 'permissions' AND constraint_name = 'UQ_permissions_module_action'
        `);
        
        if (permissionsConstraint.length === 0) {
            await queryRunner.query(`
                ALTER TABLE "permissions" 
                ADD CONSTRAINT "UQ_permissions_module_action" 
                UNIQUE ("module", "action")
            `);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revertir cambios básicos
        
        // Eliminar constraint unique de permisos
        await queryRunner.query(`ALTER TABLE "permissions" DROP CONSTRAINT IF EXISTS "UQ_permissions_module_action"`);
        
        // Eliminar índices básicos
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_permissions_module"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_audit_logs_user_created"`);
        
        // Eliminar FK y columnas de auditoría
        await queryRunner.query(`ALTER TABLE "audit_logs" DROP CONSTRAINT IF EXISTS "FK_audit_logs_updater"`);
        await queryRunner.query(`ALTER TABLE "audit_logs" DROP CONSTRAINT IF EXISTS "FK_audit_logs_creator"`);
        await queryRunner.query(`ALTER TABLE "audit_logs" DROP COLUMN IF EXISTS "updated_by"`);
        await queryRunner.query(`ALTER TABLE "audit_logs" DROP COLUMN IF EXISTS "created_by"`);
        await queryRunner.query(`ALTER TABLE "audit_logs" DROP COLUMN IF EXISTS "updated_at"`);
        
        await queryRunner.query(`ALTER TABLE "roles" DROP CONSTRAINT IF EXISTS "FK_roles_updater"`);
        await queryRunner.query(`ALTER TABLE "roles" DROP CONSTRAINT IF EXISTS "FK_roles_creator"`);
        await queryRunner.query(`ALTER TABLE "roles" DROP COLUMN IF EXISTS "updated_by"`);
        await queryRunner.query(`ALTER TABLE "roles" DROP COLUMN IF EXISTS "created_by"`);
        await queryRunner.query(`ALTER TABLE "roles" DROP COLUMN IF EXISTS "updated_at"`);
        await queryRunner.query(`ALTER TABLE "roles" DROP COLUMN IF EXISTS "created_at"`);
        
        // Eliminar índices de tablas de unión
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_role_permissions_permission_role"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_user_roles_role_user"`);
    }
} 