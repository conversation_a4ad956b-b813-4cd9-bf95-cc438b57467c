import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateClientsTable1751315767063 implements MigrationInterface {
  name = 'UpdateClientsTable1751315767063';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Verificar si las columnas ya existen antes de crearlas
    const columnsToAdd = [
      { name: 'tax_regime', type: 'varchar(50)', nullable: true },
      { name: 'neighborhood', type: 'varchar(100)', nullable: true },
      { name: 'country', type: 'varchar(50)', nullable: true, default: "'México'" },
      { name: 'contact_position', type: 'varchar(100)', nullable: true },
      { name: 'industry', type: 'varchar(100)', nullable: true },
      { name: 'company_area', type: 'varchar(100)', nullable: true },
      { name: 'company_size', type: 'varchar(50)', nullable: true },
      { name: 'client_type', type: 'varchar(50)', nullable: true, default: "'Prospecto'" },
      { name: 'credit_limit', type: 'integer', nullable: true, default: 0 },
      { name: 'credit_days', type: 'integer', nullable: true, default: 0 },
      { name: 'preferred_payment', type: 'varchar(50)', nullable: true },
      { name: 'website', type: 'varchar(255)', nullable: true },
      { name: 'observations', type: 'text', nullable: true },
      { name: 'status', type: 'varchar(20)', nullable: false, default: "'Activo'" },
      { name: 'is_quick_registration', type: 'boolean', nullable: false, default: false },
      { name: 'rfc_validated', type: 'boolean', nullable: false, default: false },
      { name: 'assigned_salesperson_id', type: 'integer', nullable: true }
    ];

    // Verificar y agregar cada columna
    for (const column of columnsToAdd) {
      const columnExists = await queryRunner.hasColumn('clients', column.name);
      if (!columnExists) {
        let query = `ALTER TABLE "clients" ADD COLUMN "${column.name}" ${column.type}`;
        
        if (column.nullable === false) {
          query += ' NOT NULL';
        }
        
        if (column.default !== undefined) {
          query += ` DEFAULT ${column.default}`;
        }
        
        await queryRunner.query(query);
      }
    }

    // Agregar índices para mejorar el rendimiento de búsquedas
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "idx_clients_commercial_name" ON "clients" ("commercial_name");
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "idx_clients_rfc" ON "clients" ("rfc");
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "idx_clients_status" ON "clients" ("status");
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "idx_clients_client_type" ON "clients" ("client_type");
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "idx_clients_assigned_salesperson" ON "clients" ("assigned_salesperson_id");
    `);

    // Agregar restricción de clave foránea para assigned_salesperson_id
    const hasForeignKey = await queryRunner.hasColumn('clients', 'assigned_salesperson_id');
    if (hasForeignKey) {
      await queryRunner.query(`
        DO $$
        BEGIN
          IF NOT EXISTS (
            SELECT 1 FROM pg_constraint WHERE conname = 'fk_clients_assigned_salesperson'
          ) THEN
            ALTER TABLE "clients" 
            ADD CONSTRAINT "fk_clients_assigned_salesperson" 
            FOREIGN KEY ("assigned_salesperson_id") 
            REFERENCES "users"("id") ON DELETE SET NULL;
          END IF;
        END
        $$;
      `);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Eliminar restricción de clave foránea
    await queryRunner.query(`
      DO $$
      BEGIN
        IF EXISTS (
          SELECT 1 FROM pg_constraint WHERE conname = 'fk_clients_assigned_salesperson'
        ) THEN
          ALTER TABLE "clients" DROP CONSTRAINT "fk_clients_assigned_salesperson";
        END IF;
      END
      $$;
    `);

    // Eliminar índices
    await queryRunner.query(`DROP INDEX IF EXISTS "idx_clients_commercial_name"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "idx_clients_rfc"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "idx_clients_status"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "idx_clients_client_type"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "idx_clients_assigned_salesperson"`);

    // Eliminar columnas
    const columnsToRemove = [
      'tax_regime', 'neighborhood', 'country', 'contact_position',
      'industry', 'company_area', 'company_size', 'client_type',
      'credit_limit', 'credit_days', 'preferred_payment', 'website',
      'observations', 'status', 'is_quick_registration', 'rfc_validated',
      'assigned_salesperson_id'
    ];

    for (const column of columnsToRemove) {
      const columnExists = await queryRunner.hasColumn('clients', column);
      if (columnExists) {
        await queryRunner.query(`ALTER TABLE "clients" DROP COLUMN "${column}"`);
      }
    }
  }
} 