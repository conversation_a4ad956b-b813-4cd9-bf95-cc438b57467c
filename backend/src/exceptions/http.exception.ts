export class HttpException extends Error {
  constructor(
    public statusCode: number,
    message: string,
    public errors?: any
  ) {
    super(message);
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }
}

export class BadRequestException extends HttpException {
  constructor(message = 'Solicitud incorrecta', errors?: any) {
    super(400, message, errors);
  }
}

export class UnauthorizedException extends HttpException {
  constructor(message = 'No autorizado') {
    super(401, message);
  }
}

export class ForbiddenException extends HttpException {
  constructor(message = 'Prohibido') {
    super(403, message);
  }
}

export class NotFoundException extends HttpException {
  constructor(message = 'No encontrado') {
    super(404, message);
  }
}

export class ConflictException extends HttpException {
  constructor(message = 'Conflicto') {
    super(409, message);
  }
}

export class InternalServerErrorException extends HttpException {
  constructor(message = 'Error interno del servidor') {
    super(500, message);
  }
}
