// /backend/src/middlewares/validation.middleware.ts
import { Request, Response, NextFunction, RequestHandler } from 'express';
import { validate, ValidationError } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { BadRequestException } from '../exceptions';

/**
 * @enum ValidationSource
 * @description Define las posibles fuentes de datos a validar en una solicitud HTTP.
 */
export enum ValidationSource {
  Body = 'body',
  Query = 'query',
  Params = 'params',
}

/**
 * @function validationMiddleware
 * @description Middleware genérico para validar datos de entrada utilizando `class-validator`.
 *
 * Este middleware valida los datos del `body`, `query` o `params` de una solicitud
 * contra una clase DTO (Data Transfer Object) especificada. Si la validación falla,
 * lanza una `BadRequestException` con los detalles de los errores.
 *
 * @param {any} type - La clase DTO a utilizar para la validación.
 * @param {ValidationSource} [source=ValidationSource.Body] - La fuente de los datos a validar.
 * @param {boolean} [skipMissingProperties=false] - Si es `true`, los campos que falten
 *   en el objeto de entrada no causarán errores de validación.
 *
 * @returns {RequestHandler} Un manejador de middleware de Express.
 */
export const validationMiddleware = (
  type: any,
  source: ValidationSource = ValidationSource.Body,
  skipMissingProperties = false
): RequestHandler => {
  return async (req: Request, _res: Response, next: NextFunction) => {
    const dataToValidate = req[source];

    // Transforma el objeto plano a una instancia de la clase DTO
    const dtoInstance = plainToClass(type, dataToValidate);

    // Realiza la validación
    const errors: ValidationError[] = await validate(dtoInstance, {
      skipMissingProperties,
      whitelist: true, // Elimina propiedades no definidas en el DTO
      forbidNonWhitelisted: true, // Lanza un error si hay propiedades no permitidas
    });

    if (errors.length > 0) {
      // Formatea los mensajes de error para una respuesta clara
      const errorMessages = errors.flatMap((error) =>
        error.constraints ? Object.values(error.constraints) : []
      );
      
      const message = `Error de validación: ${errorMessages.join(', ')}`;
      return next(new BadRequestException(message));
    }

    // Reemplaza el objeto original (body, query, params) con la instancia del DTO validada y transformada.
    // Esto asegura que los controladores reciban datos limpios y con el tipo correcto.
    req[source] = dtoInstance;

    next();
  };
};
