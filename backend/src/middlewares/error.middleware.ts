// /backend/src/middlewares/error.middleware.ts
import { Request, Response, NextFunction } from 'express';
import { HttpException } from '../exceptions';
import { config } from '../config/config';

/**
 * @function errorHandler
 * @description Middleware de manejo de errores global.
 *
 * Este middleware captura todos los errores que ocurren en la aplicación,
 * tanto `HttpException` personalizadas como errores inesperados del sistema.
 *
 * Responsabilidades:
 * - **Registrar el Error**: Imprime en la consola los detalles del error,
 *   incluyendo la pila de llamadas (stack trace) en el entorno de desarrollo
 *   para facilitar la depuración.
 * - **Formatear la Respuesta**: Envía una respuesta JSON estandarizada al cliente
 *   con el código de estado, el mensaje de error y, opcionalmente, la pila de llamadas.
 * - **Ocultar Detalles en Producción**: Evita exponer información sensible (como la
 *   pila de llamadas) en entornos que no sean de desarrollo.
 *
 * @param {HttpException | Error} error - El objeto de error capturado.
 * @param {Request} req - El objeto de solicitud de Express.
 * @param {Response} res - El objeto de respuesta de Express.
 * @param {NextFunction} _next - La función `next` de Express (no se usa aquí).
 */
export const errorHandler = (
  error: HttpException | Error,
  req: Request,
  res: Response,
  _next: NextFunction // _next es necesario para que Express lo reconozca como middleware de error
) => {
  // Determinar el código de estado y el mensaje
  const status = error instanceof HttpException ? error.statusCode : 500;
  const message = error.message || 'Ocurrió un error inesperado en el servidor.';

  // Registrar el error en la consola para depuración
  console.error(`[${req.method}] ${req.path} >> StatusCode:: ${status}, Message:: ${message}`);
  // En desarrollo, mostrar la pila de llamadas completa para facilitar la depuración
  if (config.server.env === 'development') {
    console.error(error.stack);
  }

  // Construir la respuesta de error
  const errorResponse: { status: number; message: string; stack?: string; errors?: any } = {
    status,
    message,
  };

  // Incluir errores de validación si están disponibles
  if (error instanceof HttpException && error.errors) {
    errorResponse.errors = error.errors;
  }

  // Incluir la pila de llamadas en la respuesta solo en desarrollo
  if (config.server.env === 'development' && error.stack) {
    errorResponse.stack = error.stack;
  }

  // Enviar la respuesta de error al cliente
  res.status(status).json(errorResponse);
};
