import {
  IsString,
  IsOptional,
  <PERSON>N<PERSON>ber,
  IsObject,
  MaxLength,
  IsEnum,
  IsDate,
} from 'class-validator';
import { Type } from 'class-transformer';
import { NotificationType, NotificationStatus, NotificationPriority } from '../entities/notification.entity';

/**
 * DTO para crear una nueva notificación
 */
export class CreateNotificationDto {
  @IsString()
  @MaxLength(200)
  title: string;

  @IsString()
  message: string;

  @IsOptional()
  @IsEnum(NotificationType)
  type?: NotificationType;

  @IsOptional()
  @IsEnum(NotificationPriority)
  priority?: NotificationPriority;

  @IsOptional()
  @IsNumber()
  user_id?: number;

  @IsOptional()
  @IsNumber()
  role_id?: number;

  @IsOptional()
  @IsObject()
  metadata?: any;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  action_url?: string;

  @IsOptional()
  @IsString()
  @MaxLength(50)
  action_label?: string;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  expires_at?: Date;
}

/**
 * DTO para actualizar una notificación existente
 */
export class UpdateNotificationDto {
  @IsOptional()
  @IsString()
  @MaxLength(200)
  title?: string;

  @IsOptional()
  @IsString()
  message?: string;

  @IsOptional()
  @IsEnum(NotificationType)
  type?: NotificationType;

  @IsOptional()
  @IsEnum(NotificationPriority)
  priority?: NotificationPriority;

  @IsOptional()
  @IsNumber()
  user_id?: number;

  @IsOptional()
  @IsNumber()
  role_id?: number;

  @IsOptional()
  @IsObject()
  metadata?: any;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  action_url?: string;

  @IsOptional()
  @IsString()
  @MaxLength(50)
  action_label?: string;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  expires_at?: Date;
}

/**
 * DTO para marcar notificaciones como leídas
 */
export class MarkAsReadDto {
  @IsOptional()
  @IsEnum(NotificationStatus)
  status?: NotificationStatus;
}

/**
 * DTO para filtrar notificaciones
 */
export class NotificationFilterDto {
  @IsOptional()
  @IsNumber()
  user_id?: number;

  @IsOptional()
  @IsNumber()
  role_id?: number;

  @IsOptional()
  @IsEnum(NotificationStatus)
  status?: NotificationStatus;

  @IsOptional()
  @IsEnum(NotificationType)
  type?: NotificationType;

  @IsOptional()
  @IsEnum(NotificationPriority)
  priority?: NotificationPriority;

  @IsOptional()
  @IsNumber()
  limit?: number;

  @IsOptional()
  @IsNumber()
  offset?: number;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  dateFrom?: Date;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  dateTo?: Date;
}

/**
 * DTO para estadísticas de notificaciones
 */
export class NotificationStatsDto {
  @IsNumber()
  total: number;

  @IsNumber()
  unread: number;

  @IsNumber()
  read: number;

  @IsObject()
  byType: Record<string, number>;

  @IsObject()
  byPriority: Record<string, number>;
}

/**
 * DTO para operaciones de marcado múltiple
 */
export class BulkMarkAsReadDto {
  @IsNumber({}, { each: true })
  notificationIds: number[];

  @IsEnum(NotificationStatus)
  status: NotificationStatus;
}

/**
 * DTO para notificaciones masivas
 */
export class BulkNotificationDto {
  @IsString()
  @MaxLength(200)
  title: string;

  @IsString()
  message: string;

  @IsOptional()
  @IsEnum(NotificationType)
  type?: NotificationType;

  @IsOptional()
  @IsEnum(NotificationPriority)
  priority?: NotificationPriority;

  @IsNumber({}, { each: true })
  userIds: number[];

  @IsOptional()
  @IsObject()
  metadata?: any;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  action_url?: string;

  @IsOptional()
  @IsString()
  @MaxLength(50)
  action_label?: string;
}

/**
 * DTO para notificaciones por rol
 */
export class RoleNotificationDto {
  @IsString()
  @MaxLength(200)
  title: string;

  @IsString()
  message: string;

  @IsOptional()
  @IsEnum(NotificationType)
  type?: NotificationType;

  @IsOptional()
  @IsEnum(NotificationPriority)
  priority?: NotificationPriority;

  @IsNumber()
  roleId: number;

  @IsOptional()
  @IsObject()
  metadata?: any;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  action_url?: string;

  @IsOptional()
  @IsString()
  @MaxLength(50)
  action_label?: string;
} 