import { IsNotEmpty, IsString } from 'class-validator';

export class LoginDto {
  @IsString({ message: 'La credencial debe ser una cadena de texto' })
  @IsNotEmpty({ message: 'La credencial es requerida' })
  credential!: string;

  @IsString({ message: 'La contraseña debe ser una cadena de texto' })
  @IsNotEmpty({ message: 'La contraseña es requerida' })
  password!: string;
}

export class RefreshTokenDto {
  @IsString({ message: 'El refresh token debe ser una cadena de texto' })
  @IsNotEmpty({ message: 'El refresh token es requerido' })
  refreshToken!: string;
}

export class LogoutDto extends RefreshTokenDto {}

export class AuthResponseDto {
  user!: {
    id: number;
    email: string;
    name: string;
    role: string;
  };
  tokens!: {
    accessToken: string;
    refreshToken: string;
  };
}

export class RefreshTokenResponseDto {
  accessToken!: string;
  refreshToken!: string;
}
