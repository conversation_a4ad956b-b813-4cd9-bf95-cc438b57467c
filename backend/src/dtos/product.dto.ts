import { 
  IsString, 
  IsOptional, 
  IsInt, 
  IsIn, 
  Min, 
  <PERSON><PERSON>ength, 
  IsNotEmpty,
} from 'class-validator';
import { Type, Expose } from 'class-transformer';

export class CreateProductDto {
  @IsString({ message: 'El código del producto debe ser una cadena de texto' })
  @IsNotEmpty({ message: 'El código del producto es obligatorio' })
  @MaxLength(100, { message: 'El código del producto no puede exceder 100 caracteres' })
  codigoItem: string;

  @IsString({ message: 'El nombre del producto debe ser una cadena de texto' })
  @IsNotEmpty({ message: 'El nombre del producto es obligatorio' })
  @MaxLength(255, { message: 'El nombre del producto no puede exceder 255 caracteres' })
  nombre: string;

  @IsString({ message: 'La descripción debe ser una cadena de texto' })
  @IsOptional()
  descripcion?: string;

  @IsString({ message: 'La marca debe ser una cadena de texto' })
  @IsOptional()
  @MaxLength(100, { message: 'La marca no puede exceder 100 caracteres' })
  marca?: string;

  @IsString({ message: 'El modelo debe ser una cadena de texto' })
  @IsOptional()
  @MaxLength(100, { message: 'El modelo no puede exceder 100 caracteres' })
  modelo?: string;

  @IsString({ message: 'El número de serie debe ser una cadena de texto' })
  @IsOptional()
  @MaxLength(100, { message: 'El número de serie no puede exceder 100 caracteres' })
  numeroSerie?: string;

  @IsString({ message: 'El pedimento debe ser una cadena de texto' })
  @IsOptional()
  @MaxLength(100, { message: 'El pedimento no puede exceder 100 caracteres' })
  pedimento?: string;

  @IsString({ message: 'Las observaciones deben ser una cadena de texto' })
  @IsOptional()
  observaciones?: string;

  @IsString({ message: 'El tipo de almacén debe ser una cadena de texto' })
  @IsIn(['GENERAL', 'ROTATIVO'], { message: 'El tipo de almacén debe ser GENERAL o ROTATIVO' })
  @IsOptional()
  tipoAlmacen?: 'GENERAL' | 'ROTATIVO' = 'GENERAL';

  @IsInt({ message: 'El stock disponible debe ser un número entero' })
  @Min(0, { message: 'El stock disponible no puede ser negativo' })
  @IsOptional()
  @Type(() => Number)
  stockDisponible?: number = 0;

  @IsInt({ message: 'El stock comprometido debe ser un número entero' })
  @Min(0, { message: 'El stock comprometido no puede ser negativo' })
  @IsOptional()
  @Type(() => Number)
  stockComprometido?: number = 0;

  @IsInt({ message: 'El stock mínimo debe ser un número entero' })
  @Min(1, { message: 'El stock mínimo debe ser al menos 1' })
  @IsOptional()
  @Type(() => Number)
  stockMinimo?: number = 1;

  @IsString({ message: 'El estado debe ser una cadena de texto' })
  @IsIn(['DISPONIBLE', 'AGOTADO', 'DESCONTINUADO'], { 
    message: 'El estado debe ser DISPONIBLE, AGOTADO o DESCONTINUADO' 
  })
  @IsOptional()
  estado?: 'DISPONIBLE' | 'AGOTADO' | 'DESCONTINUADO' = 'DISPONIBLE';

  @IsString({ message: 'La ubicación debe ser una cadena de texto' })
  @IsOptional()
  @MaxLength(100, { message: 'La ubicación no puede exceder 100 caracteres' })
  ubicacion?: string = 'GDL';
}

export class UpdateProductDto {
  @IsString({ message: 'El código del producto debe ser una cadena de texto' })
  @IsOptional()
  @MaxLength(100, { message: 'El código del producto no puede exceder 100 caracteres' })
  codigoItem?: string;

  @IsString({ message: 'El nombre del producto debe ser una cadena de texto' })
  @IsOptional()
  @MaxLength(255, { message: 'El nombre del producto no puede exceder 255 caracteres' })
  nombre?: string;

  @IsString({ message: 'La descripción debe ser una cadena de texto' })
  @IsOptional()
  descripcion?: string;

  @IsString({ message: 'La marca debe ser una cadena de texto' })
  @IsOptional()
  @MaxLength(100, { message: 'La marca no puede exceder 100 caracteres' })
  marca?: string;

  @IsString({ message: 'El modelo debe ser una cadena de texto' })
  @IsOptional()
  @MaxLength(100, { message: 'El modelo no puede exceder 100 caracteres' })
  modelo?: string;

  @IsString({ message: 'El número de serie debe ser una cadena de texto' })
  @IsOptional()
  @MaxLength(100, { message: 'El número de serie no puede exceder 100 caracteres' })
  numeroSerie?: string;

  @IsString({ message: 'El pedimento debe ser una cadena de texto' })
  @IsOptional()
  @MaxLength(100, { message: 'El pedimento no puede exceder 100 caracteres' })
  pedimento?: string;

  @IsString({ message: 'Las observaciones deben ser una cadena de texto' })
  @IsOptional()
  observaciones?: string;

  @IsString({ message: 'El tipo de almacén debe ser una cadena de texto' })
  @IsIn(['GENERAL', 'ROTATIVO'], { message: 'El tipo de almacén debe ser GENERAL o ROTATIVO' })
  @IsOptional()
  tipoAlmacen?: 'GENERAL' | 'ROTATIVO';

  @IsInt({ message: 'El stock disponible debe ser un número entero' })
  @Min(0, { message: 'El stock disponible no puede ser negativo' })
  @IsOptional()
  @Type(() => Number)
  stockDisponible?: number;

  @IsInt({ message: 'El stock comprometido debe ser un número entero' })
  @Min(0, { message: 'El stock comprometido no puede ser negativo' })
  @IsOptional()
  @Type(() => Number)
  stockComprometido?: number;

  @IsInt({ message: 'El stock mínimo debe ser un número entero' })
  @Min(1, { message: 'El stock mínimo debe ser al menos 1' })
  @IsOptional()
  @Type(() => Number)
  stockMinimo?: number;

  @IsString({ message: 'El estado debe ser una cadena de texto' })
  @IsIn(['DISPONIBLE', 'AGOTADO', 'DESCONTINUADO'], { 
    message: 'El estado debe ser DISPONIBLE, AGOTADO o DESCONTINUADO' 
  })
  @IsOptional()
  estado?: 'DISPONIBLE' | 'AGOTADO' | 'DESCONTINUADO';

  @IsString({ message: 'La ubicación debe ser una cadena de texto' })
  @IsOptional()
  @MaxLength(100, { message: 'La ubicación no puede exceder 100 caracteres' })
  ubicacion?: string;
}

export class ProductResponseDto {
  @Expose()
  id: number;

  @Expose()
  codigoItem: string;

  @Expose()
  nombre: string;

  @Expose()
  descripcion?: string;

  @Expose()
  marca?: string;

  @Expose()
  modelo?: string;

  @Expose()
  numeroSerie?: string;

  @Expose()
  pedimento?: string;

  @Expose()
  observaciones?: string;

  @Expose()
  tipoAlmacen: 'GENERAL' | 'ROTATIVO';

  @Expose()
  stockDisponible: number;

  @Expose()
  stockComprometido: number;

  @Expose()
  stockMinimo: number;

  @Expose()
  estado: 'DISPONIBLE' | 'AGOTADO' | 'DESCONTINUADO';

  @Expose()
  ubicacion?: string;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;

  @Expose()
  createdBy?: number;

  @Expose()
  updatedBy?: number;

  // Campos calculados
  @Expose()
  stockReal?: number;

  @Expose()
  nivelStock?: 'CRITICO' | 'BAJO' | 'NORMAL' | 'ALTO';

  @Expose()
  colorIndicador?: 'red' | 'yellow' | 'green' | 'blue';

  @Expose()
  disponibleParaVenta?: boolean;
}

export class StockMovementDto {
  @IsString({ message: 'El tipo de movimiento debe ser una cadena de texto' })
  @IsIn(['ENTRADA', 'SALIDA'], { message: 'El tipo debe ser ENTRADA o SALIDA' })
  tipo: 'ENTRADA' | 'SALIDA';

  @IsInt({ message: 'La cantidad debe ser un número entero' })
  @Min(1, { message: 'La cantidad debe ser mayor a 0' })
  @Type(() => Number)
  cantidad: number;

  @IsString({ message: 'El motivo debe ser una cadena de texto' })
  @IsOptional()
  motivo?: string;
}

export class ProductFilterDto {
  @IsString({ message: 'El término de búsqueda debe ser una cadena de texto' })
  @IsOptional()
  search?: string;

  @IsString({ message: 'El tipo de almacén debe ser una cadena de texto' })
  @IsIn(['GENERAL', 'ROTATIVO'], { message: 'El tipo de almacén debe ser GENERAL o ROTATIVO' })
  @IsOptional()
  tipoAlmacen?: 'GENERAL' | 'ROTATIVO';

  @IsString({ message: 'El estado debe ser una cadena de texto' })
  @IsIn(['DISPONIBLE', 'AGOTADO', 'DESCONTINUADO'], { 
    message: 'El estado debe ser DISPONIBLE, AGOTADO o DESCONTINUADO' 
  })
  @IsOptional()
  estado?: 'DISPONIBLE' | 'AGOTADO' | 'DESCONTINUADO';

  @IsString({ message: 'La marca debe ser una cadena de texto' })
  @IsOptional()
  marca?: string;

  @IsString({ message: 'El modelo debe ser una cadena de texto' })
  @IsOptional()
  modelo?: string;

  @IsString({ message: 'La ubicación debe ser una cadena de texto' })
  @IsOptional()
  ubicacion?: string;

  @IsInt({ message: 'La página debe ser un número entero' })
  @Min(1, { message: 'La página debe ser mayor a 0' })
  @IsOptional()
  @Type(() => Number)
  page?: number = 1;

  @IsInt({ message: 'El límite debe ser un número entero' })
  @Min(1, { message: 'El límite debe ser mayor a 0' })
  @IsOptional()
  @Type(() => Number)
  limit?: number = 20;

  @IsString({ message: 'El campo de ordenamiento debe ser una cadena de texto' })
  @IsIn(['codigoItem', 'nombre', 'marca', 'modelo', 'stockDisponible', 'createdAt'], { 
    message: 'Campo de ordenamiento no válido' 
  })
  @IsOptional()
  sortBy?: string = 'codigoItem';

  @IsString({ message: 'La dirección de ordenamiento debe ser una cadena de texto' })
  @IsIn(['ASC', 'DESC'], { message: 'La dirección debe ser ASC o DESC' })
  @IsOptional()
  sortOrder?: 'ASC' | 'DESC' = 'ASC';
} 