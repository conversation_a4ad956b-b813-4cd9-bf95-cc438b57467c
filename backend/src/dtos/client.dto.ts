// /backend/src/dtos/client.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEmail,
  Max<PERSON>ength,
  IsBoolean,
  Matches,
  IsInt,
  Min,
  Max,
  IsUrl,
  IsEnum,
} from 'class-validator';

// Enums para validación
export enum ClientType {
  PROSPECT = 'Prospecto',
  CLIENT = 'Cliente',
  FREQUENT = 'Frecuente',
}

export enum ClientStatus {
  ACTIVE = 'Activo',
  INACTIVE = 'Inactivo',
  SUSPENDED = 'Suspendido',
  TEMPORARY = 'Temporal',
}

/**
 * @class QuickClientRegistrationDto
 * @description DTO para el registro rápido de un cliente.
 *
 * Define la estructura y las validaciones para los datos mínimos
 * necesarios para registrar un cliente en modo rápido.
 */
export class QuickClientRegistrationDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  commercialName: string;

  @IsString()
  @IsNotEmpty()
  @Matches(/^[A-Z&Ñ]{3,4}\d{6}[A-Z\d]{3}$/, {
    message: 'El RFC no tiene un formato válido.',
  })
  @MaxLength(13)
  rfc: string;

  @IsInt()
  @IsOptional()
  assignedSalespersonId?: number;
}

/**
 * @class CreateClientDto
 * @description DTO para la creación de un nuevo cliente.
 *
 * Define la estructura y las validaciones para los datos necesarios
 * al registrar un cliente. Se utiliza en el endpoint de creación.
 */
export class CreateClientDto {
  // Datos Generales
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  commercialName: string;

  @IsString()
  @IsOptional()
  @MaxLength(255)
  legalName?: string;

  @IsString()
  @IsNotEmpty()
  @Matches(/^[A-Z&Ñ]{3,4}\d{6}[A-Z\d]{3}$/, {
    message: 'El RFC no tiene un formato válido.',
  })
  @MaxLength(13)
  rfc: string;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  taxRegime?: string;

  // Dirección Fiscal
  @IsString()
  @IsOptional()
  @MaxLength(100)
  street?: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  neighborhood?: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  city?: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  state?: string;

  @IsString()
  @IsOptional()
  @MaxLength(10)
  zipCode?: string;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  country?: string;

  // Contacto Principal
  @IsString()
  @IsOptional()
  @MaxLength(150)
  contactName?: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  contactPosition?: string;

  @IsEmail()
  @IsOptional()
  @MaxLength(100)
  contactEmail?: string;

  @IsString({ each: true })
  @IsOptional()
  @MaxLength(20, { each: true })
  contactPhones?: string[];

  @IsEmail({}, { each: true })
  @IsOptional()
  @MaxLength(100, { each: true })
  contactEmails?: string[];

  // Datos Comerciales
  @IsString()
  @IsOptional()
  @MaxLength(100)
  industry?: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  companyArea?: string;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  companySize?: string;

  @IsEnum(ClientType)
  @IsOptional()
  clientType?: ClientType;

  @IsInt()
  @Min(0)
  @IsOptional()
  creditLimit?: number;

  @IsInt()
  @Min(0)
  @Max(180)
  @IsOptional()
  creditDays?: number;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  preferredPayment?: string;

  // Información Adicional
  @IsUrl()
  @IsOptional()
  @MaxLength(255)
  website?: string;

  @IsString()
  @IsOptional()
  observations?: string;

  @IsEnum(ClientStatus)
  @IsOptional()
  status?: ClientStatus;

  @IsInt()
  @IsOptional()
  assignedSalespersonId?: number;

  @IsBoolean()
  @IsOptional()
  isQuickRegistration?: boolean;
}

/**
 * @class UpdateClientDto
 * @description DTO para la actualización de un cliente existente.
 *
 * Define la estructura y las validaciones para los datos que se pueden
 * modificar de un cliente. Todos los campos son opcionales.
 */
export class UpdateClientDto {
  // Datos Generales
  @IsString()
  @IsOptional()
  @MaxLength(255)
  commercialName?: string;

  @IsString()
  @IsOptional()
  @MaxLength(255)
  legalName?: string;

  @IsString()
  @IsOptional()
  @Matches(/^[A-Z&Ñ]{3,4}\d{6}[A-Z\d]{3}$/, {
    message: 'El RFC no tiene un formato válido.',
  })
  @MaxLength(13)
  rfc?: string;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  taxRegime?: string;

  // Dirección Fiscal
  @IsString()
  @IsOptional()
  @MaxLength(100)
  street?: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  neighborhood?: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  city?: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  state?: string;

  @IsString()
  @IsOptional()
  @MaxLength(10)
  zipCode?: string;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  country?: string;

  // Contacto Principal
  @IsString()
  @IsOptional()
  @MaxLength(150)
  contactName?: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  contactPosition?: string;

  @IsEmail()
  @IsOptional()
  @MaxLength(100)
  contactEmail?: string;

  @IsString({ each: true })
  @IsOptional()
  @MaxLength(20, { each: true })
  contactPhones?: string[];

  @IsEmail({}, { each: true })
  @IsOptional()
  @MaxLength(100, { each: true })
  contactEmails?: string[];

  // Datos Comerciales
  @IsString()
  @IsOptional()
  @MaxLength(100)
  industry?: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  companyArea?: string;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  companySize?: string;

  @IsEnum(ClientType)
  @IsOptional()
  clientType?: ClientType;

  @IsInt()
  @Min(0)
  @IsOptional()
  creditLimit?: number;

  @IsInt()
  @Min(0)
  @Max(180)
  @IsOptional()
  creditDays?: number;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  preferredPayment?: string;

  // Información Adicional
  @IsUrl()
  @IsOptional()
  @MaxLength(255)
  website?: string;

  @IsString()
  @IsOptional()
  observations?: string;

  @IsEnum(ClientStatus)
  @IsOptional()
  status?: ClientStatus;

  @IsInt()
  @IsOptional()
  assignedSalespersonId?: number;

  @IsBoolean()
  @IsOptional()
  active?: boolean;

  @IsBoolean()
  @IsOptional()
  rfcValidated?: boolean;
}