import {
  IsString,
  <PERSON>NotEmpty,
  IsOptional,
  IsInt,
  Min,
  IsDateString,
  IsEnum,
  IsNumber,
  IsPositive,
  MaxLength,
} from 'class-validator';
import { Type } from 'class-transformer';
import { NotificationPriority } from '../entities/notification.entity';

// --- Solicitud de Paquetería ---
export class CreatePackageRequestDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  title: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsInt()
  @IsOptional()
  requesterId?: number;

  @IsInt()
  @IsOptional()
  departmentId?: number;

  @IsString()
  @IsOptional()
  @MaxLength(20)
  priority?: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  destinationAddress: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  shippingCompany?: string;

  @IsNumber()
  @IsOptional()
  @IsPositive()
  packageWeight?: number;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  packageDimensions?: string;
}

export class UpdatePackageRequestDto {
  @IsString()
  @IsOptional()
  @MaxLength(255)
  title?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  @MaxLength(20)
  priority?: string;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  status?: string;

  @IsInt()
  @IsOptional()
  assignedTo?: number;

  @IsString()
  @IsOptional()
  @MaxLength(255)
  destinationAddress?: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  shippingCompany?: string;

  @IsNumber()
  @IsOptional()
  @IsPositive()
  packageWeight?: number;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  packageDimensions?: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  trackingNumber?: string;
}

// --- Asigna Número Fiscal ---
export enum FiscalEntityTypeDto {
  INVOICE = 'invoice',
  CUSTOMER = 'customer',
}

export class AssignFiscalNumberDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  assignedNumber: string;

  @IsEnum(FiscalEntityTypeDto)
  @IsNotEmpty()
  entityType: FiscalEntityTypeDto;

  @IsInt()
  @IsNotEmpty()
  entityId: number;

  @IsString()
  @IsOptional()
  notes?: string;
}

// --- Contratos Repse ---
export class CreateContractRepseDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  contractNumber: string;

  @IsInt()
  @IsNotEmpty()
  customerId: number;

  @IsInt()
  @IsOptional()
  projectId?: number;

  @IsDateString()
  @IsNotEmpty()
  startDate: string;

  @IsDateString()
  @IsNotEmpty()
  endDate: string;

  @IsNumber()
  @Type(() => Number)
  @IsPositive()
  @IsOptional()
  totalAmount?: number;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  status?: string;

  @IsString()
  @IsOptional()
  termsConditions?: string;
}

export class UpdateContractRepseDto {
  @IsString()
  @IsOptional()
  @MaxLength(100)
  contractNumber?: string;

  @IsInt()
  @IsOptional()
  customerId?: number;

  @IsInt()
  @IsOptional()
  projectId?: number;

  @IsDateString()
  @IsOptional()
  startDate?: string;

  @IsDateString()
  @IsOptional()
  endDate?: string;

  @IsNumber()
  @Type(() => Number)
  @IsPositive()
  @IsOptional()
  totalAmount?: number;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  status?: string;

  @IsString()
  @IsOptional()
  termsConditions?: string;
}

// --- Facturas ---
export class CreateInvoiceDto {
  @IsInt()
  @IsOptional()
  quotationId?: number;

  @IsInt()
  @IsNotEmpty()
  customerId: number;

  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  invoiceNumber: string;

  @IsNumber()
  @Type(() => Number)
  @IsPositive()
  totalAmount: number;

  @IsNumber()
  @Type(() => Number)
  @Min(0)
  @IsOptional()
  taxAmount?: number;

  @IsDateString()
  @IsNotEmpty()
  dueDate: string;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  status?: string;

  @IsString()
  @IsOptional()
  notes?: string;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  paymentTerms?: string;
}

export class UploadInvoiceFilesDto {
  @IsInt()
  @IsNotEmpty()
  invoiceId: number;
}


export class UpdateInvoiceDto {
  @IsInt()
  @IsOptional()
  quotationId?: number;

  @IsInt()
  @IsOptional()
  customerId?: number;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  invoiceNumber?: string;

  @IsNumber()
  @Type(() => Number)
  @IsPositive()
  @IsOptional()
  totalAmount?: number;

  @IsNumber()
  @Type(() => Number)
  @Min(0)
  @IsOptional()
  taxAmount?: number;

  @IsDateString()
  @IsOptional()
  dueDate?: string;

  @IsDateString()
  @IsOptional()
  paymentDate?: string;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  status?: string;

  @IsString()
  @IsOptional()
  notes?: string;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  paymentTerms?: string;
}

// --- Solicitud de Crédito ---
export class CreateCreditRequestDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  requestNumber: string;

  @IsInt()
  @IsNotEmpty()
  customerId: number;

  @IsNumber()
  @Type(() => Number)
  @IsPositive()
  amount: number;

  @IsString()
  @IsOptional()
  purpose?: string;
}

export class UpdateCreditRequestDto {
  @IsString()
  @IsOptional()
  @MaxLength(50)
  status?: string;

  @IsInt()
  @IsOptional()
  approvedBy?: number;

  @IsDateString()
  @IsOptional()
  approvalDate?: string;
}

// --- Morosos ---
export class UpdateInvoicePaymentStatusDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  status: string;

  @IsDateString()
  @IsOptional()
  paymentDate?: string;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  paymentTerms?: string;
}

// --- Gráficos ---
export class GetAdminDashboardGraphDto {
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @IsDateString()
  @IsOptional()
  endDate?: string;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  groupBy?: string;

  @IsInt()
  @IsOptional()
  salespersonId?: number;

  @IsInt()
  @IsOptional()
  customerId?: number;
}

// --- DTO Genérico para Paginación ---
export class AdminPaginationQueryDto {
  @IsInt()
  @Type(() => Number)
  @Min(1)
  @IsOptional()
  page?: number = 1;

  @IsInt()
  @Type(() => Number)
  @Min(1)
  @IsOptional()
  limit?: number = 10;

  @IsString()
  @IsOptional()
  sortBy?: string;

  @IsEnum(['ASC', 'DESC'])
  @IsOptional()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';

  @IsString()
  @IsOptional()
  search?: string;
}

// --- Solicitud de Viáticos ---
export class CreateTravelRequestDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  title: string; // Motivo del viaje o título de la solicitud

  @IsString()
  @IsOptional()
  description?: string; // Desglose de gastos, detalles adicionales

  @IsInt()
  @IsOptional()
  requesterId?: number;

  @IsInt()
  @IsOptional()
  departmentId?: number;

  @IsEnum(NotificationPriority)
  @IsOptional()
  priority?: NotificationPriority;

  @IsDateString()
  @IsNotEmpty()
  travelStartDate: string;

  @IsDateString()
  @IsNotEmpty()
  travelEndDate: string;

  @IsString()
  @IsNotEmpty()
  destination: string;

  @IsNumber()
  @IsPositive()
  @IsNotEmpty()
  requestedAmount: number;

  @IsString()
  @IsOptional()
  clientOrProject?: string;
}

export class UpdateTravelRequestDto {
  @IsString()
  @IsOptional()
  @MaxLength(255)
  title?: string; // Motivo del viaje o título de la solicitud

  @IsString()
  @IsOptional()
  description?: string; // Desglose de gastos, detalles adicionales

  @IsEnum(NotificationPriority)
  @IsOptional()
  priority?: NotificationPriority;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  status?: string;

  @IsInt()
  @IsOptional()
  assignedTo?: number;

  @IsString()
  @IsOptional()
  @MaxLength(255)
  destination?: string;

  @IsNumber()
  @IsOptional()
  @IsPositive()
  requestedAmount?: number;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  travelStartDate?: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  travelEndDate?: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  clientOrProject?: string;
}

// --- Solicitud de Tiempo Extra ---
export class CreateOvertimeRequestDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  title: string; // Motivo o título de la solicitud

  @IsString()
  @IsOptional()
  description?: string; // Actividades realizadas, justificación

  @IsInt()
  @IsOptional()
  requesterId?: number;

  @IsInt()
  @IsOptional()
  departmentId?: number;

  @IsEnum(NotificationPriority)
  @IsOptional()
  priority?: NotificationPriority;

  @IsDateString()
  @IsNotEmpty()
  overtimeDate: string;

  @IsString()
  @IsNotEmpty()
  startTime: string;

  @IsString()
  @IsNotEmpty()
  endTime: string;

  @IsString()
  @IsOptional()
  clientOrProject?: string;
}

export class UpdateOvertimeRequestDto {
  @IsString()
  @IsOptional()
  @MaxLength(255)
  title?: string; // Motivo o título de la solicitud

  @IsString()
  @IsOptional()
  description?: string; // Actividades realizadas, justificación

  @IsEnum(NotificationPriority)
  @IsOptional()
  priority?: NotificationPriority;

  @IsString()
  @IsOptional()
  @MaxLength(50)
  status?: string;

  @IsInt()
  @IsOptional()
  assignedTo?: number;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  startTime?: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  endTime?: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  clientOrProject?: string;
}

// --- Actualización de estado para Solicitud de Viáticos ---
export class UpdateTravelRequestStatusDto {
  @IsString()
  @IsNotEmpty()
  @IsEnum(['APPROVED', 'REJECTED'])
  status: 'APPROVED' | 'REJECTED';

  @IsString()
  @IsOptional()
  @MaxLength(255)
  approverComment?: string;

  @IsDateString()
  @IsOptional()
  approvalDate?: string;
}

// --- Actualización de estado para Solicitud de Tiempo Extra ---
export class UpdateOvertimeRequestStatusDto {
  @IsString()
  @IsNotEmpty()
  @IsEnum(['APPROVED', 'REJECTED'])
  status: 'APPROVED' | 'REJECTED';

  @IsString()
  @IsOptional()
  @MaxLength(255)
  approverComment?: string;

  @IsDateString()
  @IsOptional()
  approvalDate?: string;
}
