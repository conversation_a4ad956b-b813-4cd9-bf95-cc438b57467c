import { IsString, IsEmail, IsOptional, <PERSON><PERSON>eng<PERSON>, <PERSON><PERSON>ength } from 'class-validator';
import { UserRole } from '../interfaces/user.interface';

export class UpdateProfileDto {
  @IsString({ message: 'El nombre debe ser una cadena de texto' })
  @IsOptional()
  @MinLength(2, { message: 'El nombre debe tener al menos 2 caracteres' })
  @MaxLength(50, { message: 'El nombre no puede tener más de 50 caracteres' })
  name?: string;

  @IsEmail({}, { message: 'El correo electrónico no es válido' })
  @IsOptional()
  @MaxLength(100, { message: 'El correo electrónico no puede tener más de 100 caracteres' })
  email?: string;

  @IsString({ message: 'La contraseña debe ser una cadena de texto' })
  @IsOptional()
  @MinLength(8, { message: 'La contraseña debe tener al menos 8 caracteres' })
  @MaxLength(100, { message: 'La contraseña no puede tener más de 100 caracteres' })
  password?: string;
}

export class UpdatePasswordDto {
  @IsString({ message: 'La contraseña actual es requerida' })
  currentPassword!: string;

  @IsString({ message: 'La nueva contraseña es requerida' })
  @MinLength(8, { message: 'La nueva contraseña debe tener al menos 8 caracteres' })
  @MaxLength(100, { message: 'La nueva contraseña no puede tener más de 100 caracteres' })
  newPassword!: string;
}

export class ProfileResponseDto {
  id!: number;
  name!: string;
  email!: string;
  role!: UserRole;
  createdAt!: Date;
  updatedAt!: Date;
}
