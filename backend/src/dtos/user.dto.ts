import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>al, IsString, IsIn, <PERSON><PERSON><PERSON>ber, IsArray } from 'class-validator';
import { IsMexicanPhone } from '../validators/mexican-phone.validator';
import { IsStrongPassword } from '../validators/password-policy.validator';

export class CreateUserDto {
  @IsString({ message: 'El nombre debe ser una cadena de texto' })
  @IsNotEmpty({ message: 'El nombre es requerido' })
  name!: string;

  @IsEmail({}, { message: 'Correo electrónico inválido' })
  @IsNotEmpty({ message: 'El correo electrónico es requerido' })
  email!: string;

  @IsString({ message: 'La contraseña debe ser una cadena de texto' })
  @IsNotEmpty({ message: 'La contraseña es requerida' })
  @IsStrongPassword({
    minLength: 8,
    maxLength: 128,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    maxConsecutiveChars: 2,
    minUniqueChars: 6,
  }, {
    message: 'La contraseña no cumple con las políticas de seguridad requeridas'
  })
  password!: string;

  @IsString({ message: 'El teléfono debe ser una cadena de texto' })
  @IsOptional()
  @IsMexicanPhone({
    message: 'El teléfono debe tener un formato mexicano válido con código de área reconocido (ej: 33 1234 5678, +52 33 1234 5678)'
  })
  phone?: string;

  @IsString({ message: 'El área debe ser una cadena de texto' })
  @IsOptional()
  area?: string;

  @IsString({ message: 'El status debe ser una cadena de texto' })
  @IsOptional()
  @IsIn(['activo', 'inactivo', 'bloqueado'], {
    message: 'El status debe ser: activo, inactivo o bloqueado'
  })
  status?: string;

  @IsNumber({}, { message: 'created_by debe ser un número' })
  @IsOptional()
  created_by?: number;

  @IsArray({ message: 'roleIds debe ser un array' })
  @IsOptional()
  @IsNumber({}, { each: true, message: 'Cada roleId debe ser un número' })
  roleIds?: number[];
}

export class UpdateUserDto {
  @IsString({ message: 'El nombre debe ser una cadena de texto' })
  @IsOptional()
  name?: string;

  @IsEmail({}, { message: 'Correo electrónico inválido' })
  @IsOptional()
  email?: string;

  @IsString({ message: 'La contraseña debe ser una cadena de texto' })
  @IsOptional()
  @IsStrongPassword({
    minLength: 8,
    maxLength: 128,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    maxConsecutiveChars: 2,
    minUniqueChars: 6,
  }, {
    message: 'La contraseña no cumple con las políticas de seguridad requeridas'
  })
  password?: string;

  @IsString({ message: 'El teléfono debe ser una cadena de texto' })
  @IsOptional()
  @IsMexicanPhone({
    message: 'El teléfono debe tener un formato mexicano válido con código de área reconocido (ej: 33 1234 5678, +52 33 1234 5678)'
  })
  phone?: string;

  @IsString({ message: 'El área debe ser una cadena de texto' })
  @IsOptional()
  area?: string;

  @IsString({ message: 'El status debe ser una cadena de texto' })
  @IsOptional()
  @IsIn(['activo', 'inactivo', 'bloqueado'], {
    message: 'El status debe ser: activo, inactivo o bloqueado'
  })
  status?: string;

  @IsNumber({}, { message: 'updated_by debe ser un número' })
  @IsOptional()
  updated_by?: number;

  @IsArray({ message: 'roleIds debe ser un array' })
  @IsOptional()
  @IsNumber({}, { each: true, message: 'Cada roleId debe ser un número' })
  roleIds?: number[];
}

export class ChangePasswordDto {
  @IsString({ message: 'La contraseña actual debe ser una cadena de texto' })
  @IsNotEmpty({ message: 'La contraseña actual es requerida' })
  currentPassword!: string;

  @IsString({ message: 'La nueva contraseña debe ser una cadena de texto' })
  @IsNotEmpty({ message: 'La nueva contraseña es requerida' })
  @IsStrongPassword({
    minLength: 8,
    maxLength: 128,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    maxConsecutiveChars: 2,
    minUniqueChars: 6,
  }, {
    message: 'La nueva contraseña no cumple con las políticas de seguridad requeridas'
  })
  newPassword!: string;

  @IsString({ message: 'La confirmación debe ser una cadena de texto' })
  @IsNotEmpty({ message: 'La confirmación de contraseña es requerida' })
  confirmPassword!: string;
}

export class UserResponseDto {
  id!: number;
  name!: string;
  email!: string;
  phone?: string;
  area?: string;
  status!: string;
  active!: boolean;
  created_at!: Date;
  updated_at!: Date;
  created_by?: number;
  updated_by?: number;
}
