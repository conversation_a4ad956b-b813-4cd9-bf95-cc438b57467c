import { IsNumber, IsOptional, IsString, IsDateString, IsPositive } from 'class-validator';

export class QuotationDto {
  id: number;
  clientId: number;
  projectId?: number;
  createdBy: number;
  quotationNumber?: string;
  totalAmount?: number;
  status: string;
  validUntil?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export class CreateQuotationDto {
  @IsNumber()
  clientId: number;

  @IsOptional()
  @IsNumber()
  projectId?: number;

  @IsNumber()
  createdBy: number;

  @IsOptional()
  @IsString()
  quotationNumber?: string;

  @IsOptional()
  @IsNumber()
  @IsPositive()
  totalAmount?: number;

  @IsOptional()
  @IsString()
  status?: string;

  @IsOptional()
  @IsDateString()
  validUntil?: string;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class UpdateQuotationDto {
  @IsOptional()
  @IsNumber()
  projectId?: number;

  @IsOptional()
  @IsString()
  quotationNumber?: string;

  @IsOptional()
  @IsNumber()
  @IsPositive()
  totalAmount?: number;

  @IsOptional()
  @IsString()
  status?: string;

  @IsOptional()
  @IsDateString()
  validUntil?: string;

  @IsOptional()
  @IsString()
  notes?: string;
} 