// /backend/src/app.ts
import 'reflect-metadata';
import express, { Application, Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { DataSource } from 'typeorm';
import { config } from './config/config';
import { errorHandler } from './middlewares/error.middleware';
import { notFoundHandler } from './middlewares/not-found.middleware';
import { createRouter } from './routes'; // Usar createRouter en lugar de router
import { NotificationService } from './services/notification.service';
import healthRoutes from './routes/health.routes';
// Inicialización de cronjobs
import './schedulers/cleanup-audit-logs';

/**
 * @class App
 * @description Clase principal para configurar y encapsular la aplicación Express.
 *
 * Responsabilidades:
 * - Inicializar middlewares esenciales (CORS, Helmet, Morgan, etc.).
 * - Configurar las rutas de la API.
 * - Establecer manejadores de errores y rutas no encontradas.
 * - Exponer la instancia de la aplicación Express (`app`).
 */
export class App {
  public app: Application;
  private dataSource?: DataSource;
  private services?: { notificationService: NotificationService };

  constructor(dataSource?: DataSource, services?: { notificationService: NotificationService }) {
    this.app = express();
    this.dataSource = dataSource;
    this.services = services;
    this.setupMiddlewares();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  /**
   * @private
   * @description Configura los middlewares de la aplicación.
   */
  private setupMiddlewares(): void {
    // --- Middlewares de Seguridad ---
    // Helmet: Ayuda a proteger la aplicación de vulnerabilidades web conocidas
    // al establecer cabeceras HTTP de forma adecuada.
    this.app.use(helmet());

    // CORS: Habilita el Cross-Origin Resource Sharing para permitir solicitudes
    // desde dominios específicos, mejorando la seguridad en el navegador.
    const corsOptions: cors.CorsOptions = {
      origin: (origin, callback) => {
        // En desarrollo, permitir todas las conexiones
        if (config.server.env === 'development') {
          callback(null, true);
          return;
        }
        
        // En producción, usar whitelist
        const whitelist = process.env.CORS_WHITELIST?.split(',') || [
          'http://localhost:3000',
          'http://localhost:3001',
          'http://localhost:3002', 
          'https://comintec-app.vercel.app',
          'https://app.comintec.com'
        ];
        if (!origin || whitelist.includes(origin)) {
          callback(null, true);
        } else {
          callback(new Error('Not allowed by CORS'));
        }
      },
      credentials: true, // Permite el envío de cookies y cabeceras de autorización.
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization'],
    };
    this.app.use(cors(corsOptions));

    // --- Middlewares de Procesamiento de Solicitudes ---
    // express.json(): Parsea las solicitudes entrantes con payloads en formato JSON.
    this.app.use(express.json());

    // express.urlencoded(): Parsea las solicitudes entrantes con payloads en formato URL-encoded.
    this.app.use(express.urlencoded({ extended: true }));

    // --- Middleware de Logging ---
    // Morgan: Registra las solicitudes HTTP en la consola, útil para depuración.
    // Se activa solo en el entorno de desarrollo para no sobrecargar los logs en producción.
    if (config.server.env === 'development') {
      this.app.use(morgan('dev'));
    }
  }

  /**
   * @private
   * @description Configura las rutas de la aplicación.
   */
  private setupRoutes(): void {
    // Ruta de bienvenida para verificar el estado del servidor.
    this.app.get('/', (_req: Request, _res: Response) => {
      _res.status(200).json({
        message: 'Bienvenido a la API de Comintec',
        version: '1.0.0',
        environment: config.server.env,
        timestamp: new Date().toISOString(),
      });
    });

    // Agregar rutas de health check
    this.app.use('/api', healthRoutes);

    // Crear el router con el dataSource apropiado (test o producción)
    if (this.dataSource && this.services) {
      const apiRouter = createRouter(this.dataSource, this.services);
      // Integra el enrutador principal de la API bajo el prefijo `/api`.
      this.app.use('/api', apiRouter);
    } else {
      console.warn('⚠️ DataSource o Services no están disponibles, rutas limitadas');
    }
  }

  /**
   * @private
   * @description Configura los manejadores de errores.
   */
  private setupErrorHandling(): void {
    // Middleware para manejar rutas no encontradas (404).
    // Se ejecuta si ninguna de las rutas anteriores coincide.
    this.app.use(notFoundHandler);

    // Middleware de manejo de errores global.
    // Captura cualquier error que ocurra en la aplicación.
    this.app.use(errorHandler);
  }

  /**
   * @description Inicia el servidor Express.
   * @param port El puerto en el que el servidor escuchará.
   * @param callback Una función a ejecutar cuando el servidor se inicie.
   */
  public listen(port: number, callback: () => void) {
    return this.app.listen(port, callback);
  }
}

/**
 * Factory function to create a new Express app instance for testing or server startup.
 */
export function createApp(dataSource?: DataSource, services?: { notificationService: NotificationService }) {
  const appInstance = new App(dataSource, services);
  return appInstance.app;
}
