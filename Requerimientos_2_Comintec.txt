Requerimientos Específicos para la herramienta Web de COMINTEC
Página: Panel de Control - Comintec  
Sección: Menú de Navegación Superior (Pestañas)
Contexto:
Actualmente, el menú de navegación superior de la página principal (main.html) contiene varias pestañas y submenús con opciones de ejemplo. Muchas de estas opciones no tienen funcionalidades implementadas ni formularios asociados, y se desconoce el alcance real que deben tener.
Objetivo
Solicitar a los usuarios y responsables del sistema que definan los requerimientos funcionales y de contenido para cada pestaña y submenú que aún no tiene una función clara o formulario asignado.
Debemos aclarar que nos estamos basando en la herramienta ya existente para darles las mismas funcionalidades, si algunas de ellas no son requeridas o deben ser diferente a lo que se nos presentó en sesiones anteriores favor de especificarlo
Información Solicitada:
Instrucciones: Por favor complete cada sección para cada pestaña o submenú. Si no aplica o no se requiere, especifíquelo. Si alguna opción debe reemplazarse por otra diferente, indique el nuevo nombre o funcionalidad.
Para cada pestaña y submenú, por favor indicar:
1. ¿Debe existir esta opción en el sistema?
2. ¿Qué funcionalidad debe tener?
   - ¿Debe mostrar un formulario, una tabla, un dashboard, un reporte, etc.?
   - ¿Qué campos o datos debe manejar?
   - ¿Qué acciones debe permitir (crear, editar, eliminar, consultar, exportar, etc.)?
3. ¿Quiénes deben tener acceso a esta opción?
4. ¿Hay algún flujo de trabajo o proceso asociado?
5. ¿Existen ejemplos de cómo debería verse o funcionar? (Mockups, sistemas similares, etc.)

Pestañas y Submenús a Definir:
A continuación se listan las pestañas que requieren una definición de lo que hacen o muestra cada una de ellas y la función que debe de hacer cada opción
1. Administración
- Solicitud de Viáticos / solo ponerla en pestaña en almacén, logística, ventas, servicio, metrología.
- Solicitud de Tiempo ponerlo en la pestaña de almacén y servicio
- Solicitud de Paquetería  
- Asigna Número Fiscal/ ese se queda en administración
- Mis Solicitudes
- Contratos Repse/ de este necesito que llegue una notificación al área de ventas que su contrato está por vencer. 
- Facturas / que se puedan subir el XML O PDF de la factura a la  app para ligar con el proyecto que se cotizo, de igual manera que le pueda llegar una notificación al área de ventas de que su proyecto ya está facturado. 
Solicitud de crédito / que en esta pestaña se pueda subir la documentación del socio para la solicitud de crédito y que pueda la app pueda generar una constancia de crédito con la información del socio y  la línea de crédito otorgado, de igual manera que le  llegue la notificación a ventas y al área de facturación.  
Morosos/ de acuerdo a las facturas que subida a la APP, que haya una casilla para   poner como se facturo si con crédito, anticipado o contado contra entrega, que una vez dándole check a dichas casillas le pueda llegar una notificación a informes para la liberación de los mismos, también que se notifique a cada vendedor los socios que no estén pagados.  
Gráficos/ que pueda ver una descarga en EXCEL con toda la información cotizada, facturada y pagada para no realizar papel de trabajo.




2. Calidad
Pestaña: Calidad
Funcionalidad 1:  Opción de visualización de procedimientos de calibración y procedimientos generales de cada área (ventas, administración, almacén, logistica, calidad) para consulta 
- Debe permitir registrar visualización de los archivos en PDF.
- Acciones: cargar o eliminar archivos (solo calidad y sistemas)
Acceso: Solo calidad y sistemas.
Flujo:  
1. Usuario entra al apartado de Calidad


2. Se visualizan apartados de procedimientos técnicos (de calibración) y procedimientos generales.
3. Se muestran los archivos en PDF, y opciones de cargar nuevo o eliminar(habilitadas solo para calidad y sistemas).
Pestaña: Calidad
Funcionalidad 2:  Acceso a links de QuestionPro para encuestas de satisfacción y supervisiones. 
- Debe permitir acceder a los links de QuestionPro (encuestas de satisfacción y supervisiones de personal)
- Acciones: Visualizar, modificar.
Acceso: visualizar (todas las áreas), modificar los links (calidad, sistemas)
Flujo:  
1. Usuario entra al apartado de Calidad
2. Se visualiza apartado de QuestiónPro
3. Se muestran los links de acceso
4. Genera una notificación a Calidad cuando un link fue utilizado 
Pestaña: Calidad
Funcionalidad 3:  Agregar apartado de seguimiento a minutas, quejas, trabajos no conforme, llamados de atención, acciones correctivas. 
- Debe Permitir visualizar y modificar todos los formatos arriba mencionados, para dar seguimiento a su resolución.
- Acciones: Visualizar, modificar.
Acceso: Solo calidad y sistemas.
Flujo:  
1. Usuario entra al apartado de Calidad

2. Se visualiza apartado de Seguimiento
3. Se muestran los formatos mencionados, con una barra indicadora de si se encuentra en proceso de ejecución o si ya se cerró.
4. Genera un recordatorio de revisión para su seguimiento oportuno.
3. Compras
- (Sin submenús definidos, ¿qué funcionalidades debe tener?)
4. Clientes
5. Informes
Pestaña: Informes/ Submenu: Asigna folio/ Submenu: Planta / Submenu: Laboratorio   
2. ¿Qué funcionalidad debe tener? Asignación de folios de manera automatica o rápida para los servicios de laboratorio y planta. 
   - ¿Debe mostrar un formulario, una tabla, un dashboard, un reporte, etc.? Debe mostrar un formulario para cada submenu.
   - ¿Qué campos o datos debe manejar? En cada no. de folio debe aparecer la cotización y orden de compra, quien realizó el servicio, datos del equipo, fechas de ingreso y calibración.
   - ¿Qué acciones debe permitir (crear, editar, eliminar, consultar, exportar, etc.)? Crear, editar, eliminar y consultar.
3. ¿Quiénes deben tener acceso a esta opción? Informes y sistemas.
4. ¿Hay algún flujo de trabajo o proceso asociado?
User ingresa a Informes
Abre la pestaña Asigna folio
Seleccionar los Submenus Laboratorio/Planta
Asignación de folios

Pestaña en la barra de inicio: Status de informes   
2. ¿Qué funcionalidad debe tener? En este apartado debe mostrar el status de los informes con respectivo no. de folio y que aparezca el tiempo restante de envio. Son 10 días hábiles después del servicio de calibración. El área de informes debe seleccionar el submenu dependiendo el status del informe o si hay manera visualizarlo de manera automática. 
   - ¿Debe mostrar un formulario, una tabla, un dashboard, un reporte, etc.? Debe mostrar una tabla, parecido a un calendario con fechas y varios submenus.
   - ¿Qué campos o datos debe manejar? Las submenus serían: Informe enviado, En proceso de emisión, Template(hoja de cálculo) no recibido, Informe moroso(no pagado), Template listo pero equipo de laboratorio no entregado y dias de tiempo restante para envio de informes.
  - ¿Qué acciones debe permitir (crear, editar, eliminar, consultar, exportar, etc.)? Crear, editar, eliminar y consultar.
3. ¿Quiénes deben tener acceso a esta opción? Informes y sistemas.
4. ¿Hay algún flujo de trabajo o proceso asociado?
User ingresa a Informes
Abre la pestaña Status de informes
Selecciona los Submenus Informe enviado/En proceso de emisión/Template no recibido/ Informe moroso/ Template listo pero equipo de laboratorio no entregado.
Todas las áreas deben visualizar este apartado.
Pestaña: Historial de folios  
2. ¿Qué funcionalidad debe tener? Existe este apartado en la app pero aparecen los folios de todas las sedes, que solo se pueden ver los folios asignados de gdl, de laboratorio y planta. Que se pueda buscar con no. folio o con algun dato del equipo.
   - ¿Debe mostrar un formulario, una tabla, un dashboard, un reporte, etc.? Debe mostrar un reporte.
   - ¿Qué campos o datos debe manejar? Debe manejar no. de folio, cotización, orden de compra, servicio en planta/laboratorio, datos del equipo, fechas de ingreso y calibración a laboratorio y planta.
 - ¿Qué acciones debe permitir (crear, editar, eliminar, consultar, exportar, etc.)? Crear, editar, eliminar y consultar.
3. ¿Quiénes deben tener acceso a esta opción? Informes y sistemas.
4. ¿Hay algún flujo de trabajo o proceso asociado?
User ingresa a Informes
Abre la pestaña de historial de folios
Busca el no. de folio que necesita visualizar.
Pestaña: Estadisticos
2. ¿Qué funcionalidad debe tener? Exista una base de datos en donde se puedan visualizar la cantidad de folios/servicios que se asignaron al mes con gráficas, al igual los datos de los equipos/fechas/servicios pagados o en morosos para  planta y laboratorio y que se pueda guardar esta información en un excel.
   - ¿Debe mostrar un formulario, una tabla, un dashboard, un reporte, etc.? Debe mostrar un reporte, tablas y graficas..
   - ¿Qué campos o datos debe manejar? Debe manejar no. de folio, cotización, orden de compra, servicio en planta/laboratorio, datos del equipo, fechas de ingreso y calibración a laboratorio y planta.
 - ¿Qué acciones debe permitir (crear, editar, eliminar, consultar, exportar, etc.)? Crear, editar, eliminar y consultar.
3. ¿Quiénes deben tener acceso a esta opción? Informes y sistemas.
4. ¿Hay algún flujo de trabajo o proceso asociado?
User ingresa a Informes
Abre la pestaña de estadisticos
Se obtienen todos los folios registrados por mes con graficas y datos solicitados.
Se guarda información en excel.
6. Almacén
Ejemplos:
- Stock Actual/ que se pueda visualizar el inventario en tiempo real para el área de ventas.
- Entradas de Stock / que solo se pueda ingresar la información por medio de scanner o de QR.
- Salidas de Stock, que se realice a través de un vale de salida. 
- Préstamo de equipo/ que el área de ventas pueda enviar una alerta al área de almacén notificando el préstamo del equipo una vez señalado, a su vez que se genere un folio y vale del préstamos para su descarga.
- Reporte de Inventario/  que podamos tener un gráfico con las ventas de marcas y modelos de los equipos.
7. Sistemas
Opciones sugeridas:
- Gestión de Usuarios
- Permisos y Roles
- Configuración del Sistema
- Integraciones
- Auditoría del Sistema
- Backup y Restauración
8. Marketing

9. Metrología
¿Debe existir esta opción en el sistema? Si, metrología 
¿Qué funcionalidad debe tener? Consulta y edición para responsables, 
¿Debe mostrar un formulario, una tabla, un dashboard, un reporte, etc.? Tablas, reportes, rendimiento de cada personal (serviría mucho un dashboard para la métrica de cada personal)
 ¿Qué campos o datos debe manejar? 
Entregas de informes (visualizar tiempos de entrega), asignación de tareas (Calibraciones, verificaciones o asignacion de actividades por parte de su responsable), calendario de capacitaciones.
¿Qué acciones debe permitir (crear, editar, eliminar, consultar, exportar, etc.)? Consulta para tecnicos
Consulta, edición, exportar, crear y eliminar  para responsable de calibraciones.
3. ¿Quiénes deben tener acceso a esta opción? Todo el personal de Metrología, pero con algunas ediciones restringidas.
4. ¿Hay algún flujo de trabajo o proceso asociado?
Metrología-Patrones-Calendario con programación de calibración de patrones
Metrología-Retroalimentación-Técnicos/Empresa
Metrología -Autorizaciones-Técnico
Metrología -Solicitud de Viáticos (Llenado automático con información de cada perfil).
-Llenado automático de templates con información del socio.
-Alertas de tiempos establecidos para calibraciones.

10. Servicio

11. Ventas
- Agregar Cliente// necesito que se pueda agregar clientes de dos formas una colocando todos los generales ya existentes y otra de una forma rapida, unicamente con el nombre comercial o RFC de la empresa
- Mis Clientes // Necesito que la informacion se pueda filtrar por ubicación, nombre, estado, area de empresa
- Borrador de Cotización// que se pueda modificar una cotizacion sin necesidad de que sea le asigne un digito diferente como M1, M2, M3
- Existencias de Productos // poder observar lo que almacen tiene en su stock y poder filtrar la informaicon por marca, codigo ITEM, Nombre dle producto
- Asignación de Clientes// que se pueda jalar clientes a las cuentas de los vendedores sin necesidad de meterse en una cuenta unica.
- Fichas Técnicas// que se puedan subir las fichas tecnicas de las diferentes marcas que tenemos y se pueda descargar

Ejemplo de Respuesta:
Pestaña: Ventas
Funcionalidad:  
-Cotizar de manera facil y sencilla, arrastrando los items que se tienen cargados en esta aplicacion
-Ver todo el proceso de avance de un proyecto hasta que se culmina en la entrega de un equipo nuevo o culminado los servicios de calibración
-Ver el proceso de cierre de ventas, desde que se cotiza, hasta que se cierra por porcentajes como un CRM
-Ver el stock que tiene almacen y que se vea el numero de equipos que se tiene en almacen y si el equipo esta asignado en una cotizacion o no.
-Que se pueda colocar imagenes a las cotizaciones con fines de que se vea mas presentable el formato de cotizacion
-Que el formato pdf de la cotizacion tenga link que te redireccionen a paginas web de la empres o links importantes de nuestros proveedores de equipos.
-Que se pueda ver estadisticos por meses de lo que se ha cotizado y vendido, filtrado por vendedor y item de equipos, fecha
           - Permitir adjuntar facturas y documentos.
- Acciones: Crear, consultar, editar, elimina Acceso: Solo al responsable
Flujo:  
que al abrir la ventana de ventas me aparezca mis clientes y dandole click ahi aparezca los clientes que tiene cada vendedor, en caso de ver todo el concentrado de clientes, que permita tener acceso solo a ver informacion pero no modificar y en caso de querar modificar que se haga por medio del usuario responsable.
Que se pueda ver clientes que han comprado frecuentemente o que tengan una compra mas continuas en la empresa
Al abrir la venta de ventas que haya un apartado de estadisticos y de ventas realizadas por mes y que se pueda filtrar por clientes, por equipos vendidos y por vendedor.
Que al abrir la venta de ventas haya una pestaña o apartado donde salga proyectos cotizacion y pueda verse el progreso del 0% al 100% (CRM)de cada proyecto y que esta informacion se vea de todos los proyectos para el responsable de ventas y que sea visible para cada vendedor unicamente de sus proyectos.
Que al abir la ventana de ventas que en el apartado aparezca dos formas de cotizar, una por un socio ya dado de alta y otra colocando datos de un prospecto, que se permita cotizar si colocar tanta informacion general del prospecto
      Que al abrir la venta de ventas, permita ver resultados de ventas por usuario o por vendedor, de forma actualizada y en tiempo real y que cada vendedor pueda ver el resultado de ventas por mes, sin poder editar o corregir solo ver

12. RH
-Capacitación RFP (registro de formación personal)
¿Qué funcionalidad debe tener? Automatizar el procedimiento de capacitación.
   - ¿Debe mostrar un formulario, una tabla, un dashboard, un reporte, etc.? formulario para las evaluaciones y tablas con porcentaje de avance (las esvaluaciones deben tener al menos 80 para ser aprobables)
   - ¿Qué campos o datos debe manejar? Poder almacenar las evaluaciones con calificación para futuras revisiones
   - ¿Qué acciones debe permitir (crear, editar, eliminar, consultar, exportar, etc.)? los usuarios podrán consultar su avance, contestar evaluaciones de las capacitaciones recibidas, solo RH podrá agregar o eliminar evaluaciones reprobadas y capacitaciones asignadas así como que el usuario vea que capacitaciones tiene en su perfil de puesto 
 ¿Quiénes deben tener acceso a esta opción? Para visualizar todos los usuarios podrán ver como va su progreso y solo RH podrá hacer modificaciones
 ¿Hay algún flujo de trabajo o proceso asociado?Cada magnitud tiene un proceso de autorización, en el cuál está involucrado RH y Calidad.
¿Existen ejemplos de cómo debería verse o funcionar? (Mockups, sistemas similares, etc.) LMS
-Agenda de capacitación (complemento de punto anterior)
¿Qué funcionalidad debe tener? que el usuario tenga acceso a su plan de capacitación semanal y sea mas facil apartar de la agenda de servicio para que esté disponible para capacitación en sede
   - ¿Debe mostrar un formulario, una tabla, un dashboard, un reporte, etc.? Calendario
   - ¿Qué campos o datos debe manejar? cargar capacitación (para RH), Tomar capacitación (usuario), contestar evaluación (usuario), visualizar capacitaciones tomadas, aprobadas y reprobadas (Calidad, Rh y usuario)
   - ¿Qué acciones debe permitir (crear, editar, eliminar, consultar, exportar, etc.)? 
 ¿Quiénes deben tener acceso a esta opción? RH, Calidad y usuario
 ¿Hay algún flujo de trabajo o proceso asociado? RH agenda capacitación, usuario toma capacitación, RH abre link de evaluación, usuario responde evaluación con calificación minimo 80, en caso de ser menor solo RH puede eliminar la evaluación tomada y se reinicia el procedimiento.
¿Existen ejemplos de cómo debería verse o funcionar? (Mockups, sistemas similares, etc.) LMS

-RSP (resgistro de seguimiento personal)
¿Qué funcionalidad debe tener? Evidenciar el progreso y servir de guia para el crecimiento profesional de los compañeros
   - ¿Debe mostrar un formulario, una tabla, un dashboard, un reporte, etc.? un registro de reuniones de seguimiento donde se planteen objetivos y quizás ligarlo al calendario para las fechas compromiso.
   - ¿Qué campos o datos debe manejar? Datos generales, nombre, fecha de renunión de seguimiento, puesto, departamento. 
   - ¿Qué acciones debe permitir (crear, editar, eliminar, consultar, exportar, etc.)? visualizar para el usuario y editar oslo RH
 ¿Quiénes deben tener acceso a esta opción? Todos los usuarios, ya que el seguimiento personal es parte de todos los perfiles 
 ¿Hay algún flujo de trabajo o proceso asociado? los objetivos se plantean entre los usuarios, RH y sus responsables, en la agenda se notifica acercandose la fecha establecida
¿Existen ejemplos de cómo debería verse o funcionar? (Mockups, sistemas similares, etc.) N/A
-Expediente Contractual
¿Qué funcionalidad debe tener? almacenar de manera digital para su consulta, solo RH
   - ¿Debe mostrar un formulario, una tabla, un dashboard, un reporte, etc.? N/A
   - ¿Qué campos o datos debe manejar?  Un apartado donde subir los documentos escaneados
   - ¿Qué acciones debe permitir (crear, editar, eliminar, consultar, exportar, etc.)? solo para RH y Calidad editar y visualizar
 ¿Quiénes deben tener acceso a esta opción? solo para RH y Calidad
 ¿Hay algún flujo de trabajo o proceso asociado? RH carga los documentos el ingreso del nuevo compañero/a
¿Existen ejemplos de cómo debería verse o funcionar? (Mockups, sistemas similares, etc.) N/A
-Expediente Laboral
¿Qué funcionalidad debe tener? almacenar de manera digital para su consulta, solo RH
   - ¿Debe mostrar un formulario, una tabla, un dashboard, un reporte, etc.? N/A
   - ¿Qué campos o datos debe manejar?  Un apartado donde subir los documentos escaneados
   - ¿Qué acciones debe permitir (crear, editar, eliminar, consultar, exportar, etc.)? solo para RH y Calidad editar y visualizar
 ¿Quiénes deben tener acceso a esta opción? solo para RH y Calidad
 ¿Hay algún flujo de trabajo o proceso asociado?  RH carga los documentos el ingreso del nuevo compañero/a
¿Existen ejemplos de cómo debería verse o funcionar? (Mockups, sistemas similares, etc.) N/A

Logistica 
¿Debe existir esta opción en el sistema?
1.- Logística pestaña principal
¿Qué funcionalidad debe tener?
Mostrar calendario de programación de servicio
Poder ingresar a cada día y que se muestre los servicios de ese día, el horario y personal asignado
¿Debe mostrar un formulario, una tabla, un dashboard, un reporte, etc.?
Hoja de servicio en Planta
 ¿Qué campos o datos debe manejar?
Datos para el informe: Dirección contacto teléfono correo electrónico
Datos del servicio: Atencion a clientes, fecha del servicio domicilio del servicio contacto y teléfono
Datos del personal asignado al servicio: responsable del servicio, fecha de recepción de papelería
Datos de equipos: folio instrumentos marca modelo serie identificación ubicación vigencia y observaciones
 ¿Qué acciones debe permitir (crear, editar, eliminar, consultar, exportar, etc.)?
Crear editar para personal de logística; Solo consulta por ventas metrología calidad Rh administración Almacen
¿Quiénes deben tener acceso a esta opción?
Todo el personal
¿Hay algún flujo de trabajo o proceso asociado?
Solo consulta
¿Existen ejemplos de cómo debería verse o funcionar? (Mockups, sistemas similares, etc.)
Formato COM-FT-SP Servicios en planta
 
Subpestañas  
1.- Proyectos
¿Qué funcionalidad debe tener?
Mostrar el historial de proyectos subidos por ventas y autorizados por administración y visualización de estatus y/o comentarios
¿Debe mostrar un formulario, una tabla, un dashboard, un reporte, etc.?
Visualizar y descargar cotizaciones y OC
¿Qué campos o datos debe manejar?
Estatus de proyectos: En proceso Finalizado, pendiente de Almacen, mantenimientos 
¿Qué acciones debe permitir (crear, editar, eliminar, consultar, exportar, etc.)?
Modificar y consultar el estatus de los proyectos y enviar notificaciones de actualización de estatus o comentarios
¿Quiénes deben tener acceso a esta opción?
Personal de logística
¿Hay algún flujo de trabajo o proceso asociado?
Procedimiento de tratamiento de solicitudes
¿Existen ejemplos de cómo debería verse o funcionar? (Mockups, sistemas similares, etc.)
 
2.- Historial de ingresos
¿Qué funcionalidad debe tener?
Consulta de ingreso de equipos
Ingresos de equipos para servicio, laboratorio o cualquier otro servicio dentro de Comintec
¿Debe mostrar un formulario, una tabla, un
dashboard, un reporte, etc.?
Si, Hoja de recepción de equipos
¿Qué campos o datos debe manejar?
Datos del proyecto: No. De cotización, orden de compra
Datos para informes: Nombre de la empresa domicilio, contacto, teléfono correo electrónico
Datos del equipo: instrumento; folio Marca modelo serie identificación ubicación vigencia, accesorios, observaciones
Datos de seguimientos internos: quien entrego quien recibió, fechas de entregas, ingresos y finalización del servicio
 ¿Qué acciones debe permitir (crear,
editar, eliminar, consultar, exportar, etc.)?
Crear editar consultar explorar
¿Quiénes deben tener acceso a esta opción?
Crear editar y consultar: personal autorizado de logística, consultar: metrología y ventas
¿Hay algún flujo de trabajo o proceso
asociado?
Procedimientos de recolección recepción
¿Existen ejemplos de cómo debería verse o
funcionar? (Mockups, sistemas similares, etc.)
COM-FT-RE Recepción de equipos y/o instrumentos
3.- Pestaña de estadísticos
¿Qué funcionalidad debe tener?
Base de datos de todos los proyectos para descargar
   ¿Debe mostrar un
formulario, una tabla, un dashboard, un reporte, etc.?
Si, base de datos con información necesaria
¿Qué campos o datos debe manejar?
Datos de la empresa, datos del equipo, fechas de cotizaciones, OC, tiempos de programación tiempos de servicios, personal asignado
¿Qué acciones debe permitir (crear,
editar, eliminar, consultar, exportar, etc.)?
Consultar porcentajes de cumplimientos de objetivos
Editar fechas
¿Quiénes deben tener acceso a esta opción?
Logística
¿Hay algún flujo de trabajo o proceso asociado?
Que la información se comience a cargar al realizar cada quien sus actividades, posiblemente solo alimentar fechas
5. ¿Existen ejemplos de cómo debería verse
o funcionar? (Mockups, sistemas similares, etc.)
Subpestañas o submenús necesarios para logística
- Solicitud de Viáticos
Crear solicitud y enviar notificación para cuando haya una solicitud a procesar
- Solicitud de Paquetería
Crear y enviar solicitud de paquetería, notificar cuando haya nueva solicitud a procesar 

Por favor completar este documento y devolverlo al MIND para poder avanzar con la implementación de las funcionalidades reales del sistema.