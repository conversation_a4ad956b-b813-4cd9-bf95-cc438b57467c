# ========================
# Backend Configuration
# ========================

# Server
PORT=3000
NODE_ENV=development

# Database
DB_HOST=db
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_secure_password
DB_DATABASE=comintec_db

# JWT
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=1h

# CORS (comma-separated list of allowed origins)
CORS_WHITELIST=http://localhost:3001,http://localhost:3000,https://comintec-app.vercel.app,https://app.comintec.com

# ========================
# Frontend Configuration
# ========================

NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# ========================
# Docker Compose
# ========================
# Database
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=comintec_db

# PGAdmin
PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=admin

# ========================
# Development
# ========================
# Set to 'true' to enable debug logging
DEBUG=false

# ========================
# Production Overrides
# ========================
# Uncomment and update these for production
# NODE_ENV=production
# DB_HOST=your_prod_db_host
# DB_PASSWORD=your_prod_db_password
# JWT_SECRET=your_prod_jwt_secret
# NEXT_PUBLIC_API_URL=https://api.yourdomain.com
