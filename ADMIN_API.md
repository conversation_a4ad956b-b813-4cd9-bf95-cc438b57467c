# API de Administración (/api/v1/admin)

Todas las rutas bajo `/api/v1/admin` requieren autenticación (<PERSON><PERSON>).
Los roles específicos requeridos se indican para cada endpoint.

## Autenticación

Todas las solicitudes deben incluir un encabezado `Authorization` con un Bearer Token JWT:
`Authorization: Bearer <tu_token_jwt>`

## Solicitudes de Paquetería (`/package-requests`)

### 1. Crear <PERSON> de Paquetería
*   **Endpoint:** `POST /package-requests`
*   **Descripción:** Crea una nueva solicitud de paquetería.
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER`, `ROLE_USER`, `ROLE_ALMACEN` (o según configuración)
*   **Cuerpo de la Solicitud (JSON):** `CreatePackageRequestDto`
    ```json
    {
      "title": "Envío de componentes",
      "description": "Componentes para reparación urgente.",
      "destinationAddress": "Calle Destino 456, Ciudad Ejemplo",
      "departmentId": 5,
      "priority": "HIGH",
      "shippingCompany": "DHL",
      "packageWeight": 2.5,
      "packageDimensions": "30x20x10 cm"
    }
    ```
*   **Respuesta Exitosa (201 Created):**
    ```json
    {
      "message": "Solicitud de paquetería creada",
      "data": { /* Objeto de la solicitud creada */ }
    }
    ```
*   **Errores Comunes:** `400`, `401`, `403`, `500`.

### 2. Obtener Todas las Solicitudes de Paquetería
*   **Endpoint:** `GET /package-requests`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER`, `ROLE_ALMACEN`
*   **Query Parameters:** `AdminPaginationQueryDto` (page, limit, sortBy, sortOrder, search)
*   **Respuesta Exitosa (200 OK):** Lista paginada.

### 3. Obtener Solicitud de Paquetería por ID
*   **Endpoint:** `GET /package-requests/:id`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER`, `ROLE_USER` (si es creador), `ROLE_ALMACEN`
*   **Respuesta Exitosa (200 OK):** Objeto de la solicitud.

### 4. Actualizar Solicitud de Paquetería
*   **Endpoint:** `PUT /package-requests/:id`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER`, `ROLE_ALMACEN`
*   **Cuerpo (JSON):** `UpdatePackageRequestDto`
*   **Respuesta Exitosa (200 OK):** Solicitud actualizada.

### 5. Eliminar Solicitud de Paquetería
*   **Endpoint:** `DELETE /package-requests/:id`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER`, `ROLE_ALMACEN`
*   **Respuesta Exitosa (204 No Content)`

## Asignación de Números Fiscales (`/fiscal-numbers`)

### 1. Asignar Número Fiscal
*   **Endpoint:** `POST /fiscal-numbers`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER_ADMINISTRACION`, `ROLE_FINANZAS`
*   **Cuerpo (JSON):** `AssignFiscalNumberDto`
    ```json
    {
      "assignedNumber": "NF-2024-001",
      "entityType": "invoice",
      "entityId": 123,
      "notes": "Número fiscal para factura X."
    }
    ```
*   **Respuesta Exitosa (201 Created):** Número fiscal asignado.

### 2. Obtener Todos los Números Fiscales
*   **Endpoint:** `GET /fiscal-numbers`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER_ADMINISTRACION`, `ROLE_FINANZAS`
*   **Query:** `AdminPaginationQueryDto`
*   **Respuesta Exitosa (200 OK):** Lista paginada.

### 3. Obtener Número Fiscal por ID
*   **Endpoint:** `GET /fiscal-numbers/:id`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER_ADMINISTRACION`, `ROLE_FINANZAS`
*   **Respuesta Exitosa (200 OK):** Objeto del número fiscal.

### 4. Eliminar Número Fiscal
*   **Endpoint:** `DELETE /fiscal-numbers/:id`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER_ADMINISTRACION`, `ROLE_FINANZAS`
*   **Respuesta Exitosa (204 No Content)`

## Contratos Repse (`/contracts-repse`)

### 1. Crear Contrato Repse
*   **Endpoint:** `POST /contracts-repse`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER_ADMINISTRACION`, `ROLE_VENTAS`
*   **Cuerpo (JSON):** `CreateContractRepseDto`
*   **Respuesta Exitosa (201 Created):** Contrato creado.

### 2. Obtener Todos los Contratos Repse
*   **Endpoint:** `GET /contracts-repse`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER_ADMINISTRACION`, `ROLE_VENTAS`
*   **Query:** `AdminPaginationQueryDto`
*   **Respuesta Exitosa (200 OK):** Lista paginada.

### 3. Obtener Contrato Repse por ID
*   **Endpoint:** `GET /contracts-repse/:id`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER_ADMINISTRACION`, `ROLE_VENTAS`
*   **Respuesta Exitosa (200 OK):** Objeto del contrato.

### 4. Actualizar Contrato Repse
*   **Endpoint:** `PUT /contracts-repse/:id`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER_ADMINISTRACION`, `ROLE_VENTAS`
*   **Cuerpo (JSON):** `UpdateContractRepseDto`
*   **Respuesta Exitosa (200 OK):** Contrato actualizado.

### 5. Eliminar Contrato Repse
*   **Endpoint:** `DELETE /contracts-repse/:id`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER_ADMINISTRACION`
*   **Respuesta Exitosa (204 No Content)`

## Facturas (`/invoices`)

### 1. Crear Factura
*   **Endpoint:** `POST /invoices`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER_ADMINISTRACION`, `ROLE_FINANZAS`
*   **Cuerpo (JSON):** `CreateInvoiceDto`
*   **Respuesta Exitosa (201 Created):** Factura creada.

### 2. Subir Documento de Factura (XML/PDF)
*   **Endpoint:** `POST /invoices/:invoiceId/documents`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER_ADMINISTRACION`, `ROLE_FINANZAS`
*   **Tipo de Contenido:** `multipart/form-data`
*   **Form Data:** `invoiceFile` (archivo), `documentType` ('XML'/'PDF'/'OTHER'), `description`
*   **Respuesta Exitosa (201 Created):** Documento subido.

### 3. Obtener Todas las Facturas
*   **Endpoint:** `GET /invoices`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER`, `ROLE_FINANZAS`, `ROLE_VENTAS`
*   **Query:** `AdminPaginationQueryDto`
*   **Respuesta Exitosa (200 OK):** Lista paginada.

### 4. Obtener Factura por ID
*   **Endpoint:** `GET /invoices/:id`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER`, `ROLE_FINANZAS`, `ROLE_VENTAS`
*   **Respuesta Exitosa (200 OK):** Objeto de la factura con `attached_documents`.

### 5. Actualizar Factura
*   **Endpoint:** `PUT /invoices/:id`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER_ADMINISTRACION`, `ROLE_FINANZAS`
*   **Cuerpo (JSON):** `UpdateInvoiceDto`
*   **Respuesta Exitosa (200 OK):** Factura actualizada.

### 6. Actualizar Estado de Pago de Factura
*   **Endpoint:** `PATCH /invoices/:id/payment-status`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER_ADMINISTRACION`, `ROLE_FINANZAS`
*   **Cuerpo (JSON):** `UpdateInvoicePaymentStatusDto`
*   **Respuesta Exitosa (200 OK):** Factura actualizada.

### 7. Eliminar Documento de Factura
*   **Endpoint:** `DELETE /invoices/:invoiceId/documents/:documentId`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER_ADMINISTRACION`, `ROLE_FINANZAS`
*   **Respuesta Exitosa (204 No Content)`

### 8. Eliminar Factura
*   **Endpoint:** `DELETE /invoices/:id`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER_ADMINISTRACION`
*   **Respuesta Exitosa (204 No Content)`

## Solicitudes de Crédito (`/credit-requests`)

### 1. Crear Solicitud de Crédito
*   **Endpoint:** `POST /credit-requests`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER`, `ROLE_VENTAS`, `ROLE_FINANZAS`
*   **Cuerpo (JSON):** `CreateCreditRequestDto`
*   **Respuesta Exitosa (201 Created):** Solicitud creada.

### 2. Subir Documento de Solicitud de Crédito
*   **Endpoint:** `POST /credit-requests/:creditRequestId/documents`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER`, `ROLE_VENTAS`, `ROLE_FINANZAS`
*   **Tipo de Contenido:** `multipart/form-data`
*   **Form Data:** `creditRequestFile` (archivo), `documentType`, `description`
*   **Respuesta Exitosa (201 Created):** Documento subido.

### 3. Obtener Todas las Solicitudes de Crédito
*   **Endpoint:** `GET /credit-requests`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER`, `ROLE_VENTAS`, `ROLE_FINANZAS`
*   **Query:** `AdminPaginationQueryDto`
*   **Respuesta Exitosa (200 OK):** Lista paginada.

### 4. Obtener Solicitud de Crédito por ID
*   **Endpoint:** `GET /credit-requests/:id`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER`, `ROLE_VENTAS`, `ROLE_FINANZAS`
*   **Respuesta Exitosa (200 OK):** Objeto de la solicitud con `attached_documents`.

### 5. Actualizar Estado de Solicitud de Crédito
*   **Endpoint:** `PATCH /credit-requests/:id/status`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER`, `ROLE_FINANZAS`
*   **Cuerpo (JSON):** `UpdateCreditRequestDto` (`status`: 'APPROVED'/'REJECTED')
*   **Respuesta Exitosa (200 OK):** Solicitud actualizada.

### 6. Generar Constancia de Crédito (PDF Simulado)
*   **Endpoint:** `GET /credit-requests/:creditRequestId/certificate`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER`, `ROLE_FINANZAS`
*   **Respuesta Exitosa (200 OK):** Archivo para descargar.

### 7. Eliminar Solicitud de Crédito
*   **Endpoint:** `DELETE /credit-requests/:id`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER`
*   **Respuesta Exitosa (204 No Content)`

## Mis Solicitudes (`/me/requests`)

### 1. Obtener Mis Solicitudes
*   **Endpoint:** `GET /me/requests`
*   **Roles Requeridos:** Cualquier usuario autenticado.
*   **Query:** `AdminPaginationQueryDto`
*   **Respuesta Exitosa (200 OK):** Lista de solicitudes del usuario (implementación actual es placeholder).

## Dashboard / Gráficos (`/admin/dashboard`)
*(Nota: La ruta base en admin.routes.ts es /dashboard, por lo que sería /admin/dashboard)*

### 1. Obtener Datos para Gráficos
*   **Endpoint:** `GET /dashboard/graphs`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER`
*   **Query:** `GetAdminDashboardGraphDto` (startDate, endDate, groupBy, etc.)
*   **Respuesta Exitosa (200 OK):** Array de datos agregados.

### 2. Descargar Reporte en Excel (CSV Simulado)
*   **Endpoint:** `GET /dashboard/graphs/download`
*   **Roles Requeridos:** `ROLE_ADMIN`, `ROLE_MANAGER`
*   **Query:** `GetAdminDashboardGraphDto`
*   **Respuesta Exitosa (200 OK):** Archivo para descargar.

```
