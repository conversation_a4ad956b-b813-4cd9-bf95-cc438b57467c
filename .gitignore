# =====================================
# Archivos generados (se reconstruyen)
# =====================================

# Build y compilación
dist/
build/
.next/
frontend/.next/
frontend/out/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# =====================================
# Configuración de entornos locales
# =====================================


# Archivos de IDE
.idea/
# Archivos temporales
*.swp
*.swo
.DS_Store
Thumbs.db

# =====================================
# Dependencias de Node.js (se instalan con npm install)
# =====================================
node_modules/
frontend/node_modules/

# =====================================
# Logs
# =====================================
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# =====================================
# Directorios de build
# =====================================
/dist
/build
/out
.next/
.vercel/

# =====================================
# Archivos del sistema
# =====================================
.DS_Store
Thumbs.db

# =====================================
# Archivos de Java
# =====================================
*.class
*.jar
*.war
*.ear
*.iml

# =====================================
# Directorios de build de backend
# =====================================
/target/

# =====================================
# Archivos de IDE
# =====================================
.idea/
.vscode/

# =====================================
# Archivos temporales
# =====================================
*.swp
*.swo
*.swn

# Next.js specific
.next/
.nuxt/
.nitro/
.cache/
.temp/
.tmp/
.vercel/
.netlify/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*


# Large files
*.node
*.zip
*.tar.gz
*.tar
*.tgz
*.7z
*.dmg
*.pkg
*.exe
*.dll
*.so
*.dylib
*.o
*.obj
*.pyc
*.pyo
*.pyd

# Ignore all files in node_modules by default
node_modules/

# But track .gitkeep files if any
!.gitkeep

# Frontend build artifacts
frontend/.next/
frontend/out/
frontend/node_modules/

# Logs and environment files
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*


# IDE specific files
.idea/
.vscode/
*.swp
*.swo
*.swn
*.swo

# System Files
.DS_Store
Thumbs.db

# Node
node_modules
npm-debug.log
yarn-error.log

# Build
.next
out
build
coverage

# Editor
.vscode
.idea
.DS_Store

dev-debug.log
# Dependency directories
# Environment variables - COMENTADA para permitir versionado
# Editor directories and files
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

#
# Task files
# tasks.json
# tasks/ database_structure.html
comintec_schema_app_only.sql
