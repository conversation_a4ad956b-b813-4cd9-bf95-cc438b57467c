# 🎨 Frontend Styling Rules - AppComintec2.0

## 📍 ARCHIVO PRINCIPAL DEL DASHBOARD

### **Ruta Correcta para Modificaciones:**
```
frontend/app/dashboard/page.tsx
```

**⚠️ CRÍTICO:** Este es el ÚNICO archivo que se está usando para el dashboard principal. 

### **❌ Archivos que NO se deben modificar (no se usan):**
- `frontend/comintec-dashboard.tsx`
- `frontend/app/dashboard/dashboard-cards.tsx` 
- `frontend/sales-dashboard.tsx`
- Cualquier archivo en `_importados/`

---

## 🎨 GRADIENTES DE FONDO

### **Ubicación Principal:**
**Layout Global (Header/Sidebar/Contenido)**: Línea 67 en `frontend/components/layout/dashboard-shell.tsx`

### **⚠️ IMPORTANTE:**
El fondo se controla ÚNICAMENTE desde `dashboard-shell.tsx`. NO agregar fondos en `page.tsx`.

### **<PERSON><PERSON> (Paleta Café Moka Personalizada - Versión Más Clara):**
```css
bg-gradient-to-br from-moka-peach/80 via-moka-lion/70 to-moka-brown/60
```
- **Esquina superior izquierda**: `moka-peach/80` (#ffe6a7 con 80% opacidad) - Crema suave
- **Centro**: `moka-lion/70` (#cc9f69 con 70% opacidad) - Café con leche
- **Esquina inferior derecha**: `moka-brown/60` (#a66030 con 60% opacidad) - Café medio

### **Paleta Completa:**
- `moka-peach` (#ffe6a7) - Fondos claros, highlights
- `moka-lion` (#bb9457) - Elementos principales, botones
- `moka-brown` (#99582a) - Acentos, bordes
- `moka-bistre` (#432818) - Textos, detalles oscuros
- `moka-falu` (#6f1d1b) - Acentos especiales, hover states

### **Modo Oscuro (Rojo a Azul):**
```css
dark:bg-gradient-to-br dark:from-slate-900 dark:via-blue-950 dark:to-red-950
```
- **Esquina superior izquierda**: `slate-900` (azul-gris muy oscuro)
- **Centro**: `blue-950` (azul muy oscuro)
- **Esquina inferior derecha**: `red-950` (rojo muy oscuro)

### **🎯 CONTROL CENTRALIZADO:**
El gradiente se aplica desde `dashboard-shell.tsx` y afecta automáticamente a:
- ✅ Header
- ✅ Sidebar
- ✅ Contenido principal
- ✅ Todas las páginas del dashboard

---

## � ELEMENTOS ESPECÍFICOS

### **Texto del Sidebar:**
- **Sombra de texto**: `[text-shadow:_0_1px_2px_rgb(0_0_0_/_40%)]` para mejor legibilidad sobre gradiente

### **Barras de Progreso:**
- **Color principal**: `from-moka-falu to-red-600` (rojo falu a rojo más intenso)
- **Ubicaciones**: Dashboard principal, modales de proyectos, tarjetas de proyectos

### **Vista de Ventas:**
- **Fondo principal**: `bg-moka-peach/30` (peach con transparencia)
- **Acentos**: Color `moka-falu` para elementos destacados
- **Tabs activos**: `bg-moka-falu` con texto blanco

---

## �🃏 ESTRUCTURA DE CARDS DE PROYECTOS

### **Ubicación:** Líneas 1699-1731 en `frontend/app/dashboard/page.tsx`

### **Jerarquía Visual:**
1. **Título Principal**: `{sale.customer}` (nombre de la empresa)
   - Clase: `text-xl font-light tracking-wide text-gray-900 dark:text-slate-100`
2. **Subtítulo**: `{sale.project}` (nombre del proyecto)
   - Clase: `text-sm font-light text-gray-600 dark:text-slate-400`

### **Colores de Cards:**
```css
bg-gray-200/30 dark:bg-slate-800/30 
backdrop-blur-xl rounded-3xl 
border border-gray-300/30 dark:border-slate-700/30
hover:bg-gray-300/40 dark:hover:bg-slate-700/40
```

---

## 🎯 PALETA DE COLORES CONSISTENTE

### **Modo Claro:**
- **Fondo principal**: `gray-200/30`
- **Bordes**: `gray-300/30`
- **Texto primario**: `gray-900`
- **Texto secundario**: `gray-600`
- **Hover**: `gray-300/40`

### **Modo Oscuro:**
- **Fondo principal**: `slate-800/30`
- **Bordes**: `slate-700/30`
- **Texto primario**: `slate-100`
- **Texto secundario**: `slate-400`
- **Hover**: `slate-700/40`

---

## 📋 UBICACIONES CLAVE PARA MODIFICACIONES

### **Cards de Proyectos:**
- **Líneas 1699-1731**: Estructura principal de cards
- **Línea 1705**: Colores de departamento
- **Línea 1706**: Badge de estado
- **Líneas 1710-1711**: Títulos (empresa/proyecto)

### **Modales:**
- **Líneas 970+**: Cards internas de modales
- **Patrón**: Buscar `bg-white/25 dark:bg-white/10` y reemplazar por colores consistentes

### **Calendario:**
- **Línea 1743**: Card principal del calendario
- **Líneas 1877+**: Panel de eventos

---

## 🔧 REGLAS DE MODIFICACIÓN

### **1. Siempre usar el archivo correcto:**
```
frontend/app/dashboard/page.tsx
```

### **2. Mantener consistencia de colores:**
- Usar `gray-*` para modo claro
- Usar `slate-*` para modo oscuro
- Mantener niveles de transparencia (`/30`, `/40`)

### **3. Estructura de títulos:**
- **Empresa** = Título principal (grande)
- **Proyecto** = Subtítulo (pequeño)

### **4. Efectos visuales:**
- `backdrop-blur-xl` para cards principales
- `backdrop-blur-md` para cards internas
- `rounded-3xl` para cards principales
- `border` siempre visible con transparencia

---

## 🚨 NOTAS IMPORTANTES

1. **NO modificar archivos en `_importados/`** - son referencias históricas
2. **Verificar cambios en navegador** después de cada modificación
3. **Mantener gradientes cálidos** para evitar fatiga visual
4. **Usar TypeScript casting** para evitar errores: `as keyof typeof`
5. **Probar en modo claro Y oscuro** siempre

---

## 🎨 APLICACIÓN DE COLORES MOKA EN FORMULARIOS

### **Componentes Actualizados:**

#### **1. Form Styles (form-styles.tsx):**
- **CheckboxField**: Fondos `moka-peach/10` y `moka-peach/20`, bordes `moka-brown/40`
- **FormSection**: Títulos con `text-moka-bistre`, peso `font-medium`
- **FormCard**: Fondo `moka-peach/10`, bordes `moka-brown/30`
- **SelectableCard**: Estados seleccionados con `moka-falu/50` y `moka-peach/20`
- **PermissionStats**: Barra de progreso con gradiente `from-moka-falu to-red-600`

#### **2. Todos los Tabs Actualizados con Estilo Profesional Blanco/Gris:**

**Patrón Estándar Aplicado a Todos los Módulos:**
- **Header Section**: Título h1 con `text-3xl font-medium tracking-tight text-gray-900 dark:text-slate-100`
- **Descripción**: `text-gray-600 dark:text-slate-400` con información contextual del módulo
- **Cards Principales**: `bg-white/60 dark:bg-slate-800/60 backdrop-blur-md border border-white/20 dark:border-slate-700/20`
- **Cards Internas**: `bg-white/30 dark:bg-slate-800/30` con bordes `border-gray-200 dark:border-slate-700`
- **Texto Principal**: `text-gray-900 dark:text-slate-100` para títulos y contenido principal
- **Texto Secundario**: `text-gray-600 dark:text-slate-400` para descripciones y metadatos
- **Badges**: Colores específicos con variantes dark mode apropiadas

**Módulos Actualizados:**
- ✅ **Sistemas**: Gestión de Usuarios, Roles y Permisos, Auditoría, Backup
- ✅ **RH**: Capacitaciones, Seguimiento, Expedientes
- ✅ **Logística**: Servicios, Proyectos, Estadísticas
- ✅ **Almacén**: Stock, Préstamos, Solicitudes (ya tenía estilo actualizado)
- ✅ **Metrología**: Dashboard, Tareas, Capacitaciones, Reportes
- ✅ **Ventas**: Dashboard, Clientes, Cotizaciones, Productos
- ✅ **Administración**: Proyectos, Inventario, Solicitudes
- ✅ **Clientes**: Listado, Alta, Historial, Categorización
- ✅ **Reportes**: Generador, Historial, Estadísticas

#### **5. Backup (BackupView.tsx):**
- **Fondo principal**: Gradiente moka consistente
- **Cards de estadísticas**: Fondo `moka-peach/20`, bordes `moka-brown/40`
- **Modales**: Fondo `moka-peach/30` con `backdrop-blur-md`
- **Tabla**: Colores moka con hover effects
- **Inputs y selects**: Estilo consistente con otros formularios

#### **6. Sidebar (sidebar.tsx):**
- **Texto en modo claro**: Cambiado de blanco a negro (`text-black`)
- **Links activos**: Fondo `moka-falu/20` con texto `moka-bistre`
- **Hover states**: Mantienen consistencia con colores negros

#### **7. Pestañas con Tablas:**

**Calidad Tab (calidad-tab.tsx):**
- **Card principal**: Fondo `moka-peach/20`, bordes `moka-brown/40`
- **Tablas**: Fondo `moka-peach/10`, bordes `moka-brown/40`
- **Headers**: Texto `moka-bistre`, peso `font-normal`
- **Filas**: Hover effects con `moka-peach/10`
- **Input de búsqueda**: Colores moka consistentes

#### **8. Componentes de Almacén:**

**Stock Actual (StockActualView.tsx):**
- **Card principal**: Fondo `moka-peach/20`, bordes `moka-brown/40`
- **Tabla**: Fondo `moka-peach/10` con hover effects
- **Headers y celdas**: Colores moka aplicados

**Préstamo Equipo (PrestamoEquipoView.tsx):**
- **Card**: Fondo `moka-peach/20`, bordes `moka-brown/40`
- **Tabla**: Estilo consistente con otros componentes

**Entradas Stock (EntradasStockView.tsx):**
- **Card**: Fondo `moka-peach/20`, bordes `moka-brown/40`
- **Tabla**: Colores moka con hover effects

**Salidas Stock (SalidasStockView.tsx):**
- **Tabla**: Fondo `moka-peach/10`, bordes `moka-brown/40`
- **Inputs**: Colores moka aplicados

### **Paleta de Colores Aplicada:**
- **moka-peach** (#ffe6a7): Fondos claros, highlights
- **moka-lion** (#cc9f69): Elementos principales (no usado directamente en forms)
- **moka-brown** (#a66030): Bordes, texto secundario
- **moka-bistre** (#432818): Texto principal, títulos
- **moka-falu** (#6f1d1b): Botones primarios, acentos especiales

### **Pesos de Fuente Actualizados:**
- **Títulos principales**: `font-medium` (era `font-bold`)
- **Subtítulos**: `font-normal` (era `font-semibold`)
- **Labels**: `font-normal` (era `font-medium`)
- **Texto de contenido**: `font-normal` (consistente)

---

## 📝 HISTORIAL DE CAMBIOS

- **2024-01**: Identificación del archivo correcto (`page.tsx`)
- **2024-01**: Restauración de estructura empresa/proyecto
- **2024-01**: Actualización de gradientes cálidos
- **2024-01**: Implementación de colores consistentes gray/slate
- **2024-01**: Aplicación completa de paleta moka en formularios y componentes
- **2024-01**: Reducción de pesos de fuente para mejor legibilidad
- **2024-01**: Actualización de checkboxes, modales y elementos interactivos
- **2024-01**: Actualización de componente Backup con colores moka
- **2024-01**: Cambio de texto del sidebar a negro en modo claro para mejor legibilidad
- **2024-01**: Actualización de pestañas con tablas (Calidad) con colores moka
- **2024-01**: Actualización completa de componentes de Almacén con colores moka
- **2024-01**: Aplicación consistente de hover effects y transparencias en todas las tablas
- **2024-01**: CORRECCIÓN: Gestión de Usuarios, Roles y Permisos, y Auditoría cambiados a estilo blanco/gris limpio como BackupView
- **2024-01**: Unificación de estilo: componentes de sistemas usan bg-white/60 y colores gray en lugar de moka
- **2024-01**: ACTUALIZACIÓN MASIVA: Todos los tabs actualizados con estilo profesional blanco/gris consistente
- **2024-01**: Aplicación de headers estandarizados con títulos h1 y descripciones en todos los módulos
- **2024-01**: Implementación de cards con backdrop-blur-md y transparencias profesionales en toda la aplicación
