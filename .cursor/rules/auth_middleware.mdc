---
description: 
globs: 
alwaysApply: true
---
# Authentication Middleware Rules

## **🔐 CRITICAL: Auth Middleware Factory Pattern (MANDATORY)**

### **Problem Solved**
- **NEVER instantiate auth middleware before DataSource is ready**
- **NEVER use global middleware instances** - causes database connection errors
- **ALWAYS use factory function pattern** for DataSource injection

### **Correct Implementation Pattern**
```typescript
// ✅ CORRECT: Factory function in auth.middleware.ts
import { Request, Response, NextFunction } from 'express';
import { DataSource } from 'typeorm';
import jwt from 'jsonwebtoken';
import { User } from '../entities/user.entity';

export function createAuthMiddleware(dataSource: DataSource) {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({ message: 'Token de acceso requerido' });
        return;
      }

      const token = authHeader.substring(7);
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
      
      // Use injected DataSource for user lookup
      const userRepository = dataSource.getRepository(User);
      
      // Use SQL direct query to avoid schema issues
      const userQuery = `
        SELECT id, email, name, status, active 
        FROM users 
        WHERE id = $1 AND active = true
      `;
      
      const userResult = await userRepository.query(userQuery, [decoded.sub]);
      
      if (!userResult || userResult.length === 0) {
        res.status(401).json({ message: 'Usuario no encontrado o inactivo' });
        return;
      }

      req.user = userResult[0];
      next();
    } catch (error: any) {
      console.error('[AuthMiddleware] Error:', error.message);
      res.status(401).json({ message: 'Token inválido' });
    }
  };
}

// ❌ WRONG: Global middleware instance
export const authMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  // This will fail because DataSource might not be initialized
};
```

### **Route Configuration Pattern**
```typescript
// ✅ CORRECT: Route setup with DataSource injection
import { Router } from 'express';
import { DataSource } from 'typeorm';
import { createAuthMiddleware } from '../middlewares/auth.middleware';

export function createProtectedRoutes(dataSource: DataSource): Router {
  const router = Router();
  const authMiddleware = createAuthMiddleware(dataSource);
  const controller = new SomeController(dataSource);
  
  // Apply auth middleware to all routes in this router
  router.use(authMiddleware);
  
  // Protected routes
  router.get('/users', controller.getUsers.bind(controller));
  router.post('/users', controller.createUser.bind(controller));
  
  return router;
}

// ❌ WRONG: Using global middleware
import { authMiddleware } from '../middlewares/auth.middleware'; // Don't do this
router.use(authMiddleware); // Will cause database errors
```

### **Main Routes Index Pattern**
```typescript
// ✅ CORRECT: Main routes/index.ts setup
import { Express } from 'express';
import { DataSource } from 'typeorm';
import { createAuthMiddleware } from '../middlewares/auth.middleware';

export function setupRoutes(app: Express, dataSource: DataSource): void {
  // Public routes (no auth required)
  app.use('/api/auth', createAuthRoutes());
  app.use('/api/health', createHealthRoutes());
  
  // Protected routes (auth required)
  const authMiddleware = createAuthMiddleware(dataSource);
  
  app.use('/api/systems', authMiddleware, createSystemsRoutes(dataSource));
  app.use('/api/users', authMiddleware, createUserRoutes(dataSource));
  app.use('/api/dashboard', authMiddleware, createDashboardRoutes(dataSource));
  
  console.log('✅ All routes loaded successfully');
}

// ❌ WRONG: Global middleware application
app.use(authMiddleware); // Don't apply globally without DataSource
```

## **🔧 DataSource Injection Pattern**

### **Controller Pattern with DataSource**
```typescript
// ✅ CORRECT: Controller with DataSource injection
export class UserController {
  private userService: UserService;
  private auditService: AuditService;

  constructor(dataSource: DataSource) {
    this.userService = new UserService(dataSource);
    this.auditService = new AuditService(dataSource);
  }

  async getUsers(req: Request, res: Response): Promise<void> {
    try {
      // req.user is available from auth middleware
      const userId = req.user?.id;
      const users = await this.userService.getUsers();
      
      res.json({ data: users });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }
}

// ❌ WRONG: Controller without proper DataSource
export class BadController {
  constructor() {
    // No DataSource injection - will cause issues
  }
}
```

### **Service Pattern with DataSource**
```typescript
// ✅ CORRECT: Service with DataSource injection
export class UserService {
  private dataSource: DataSource;

  constructor(dataSource: DataSource) {
    this.dataSource = dataSource;
  }

  async findUserById(id: number): Promise<User | null> {
    const userRepository = this.dataSource.getRepository(User);
    
    // Use SQL queries to avoid schema issues
    const userQuery = `
      SELECT id, name, email, status, active
      FROM users 
      WHERE id = $1
    `;
    
    const result = await userRepository.query(userQuery, [id]);
    return result.length > 0 ? result[0] : null;
  }
}

// ❌ WRONG: Service with AppDataSource import
import { AppDataSource } from '../data-source';

export class BadService {
  async findUser(id: number) {
    // Don't import AppDataSource directly - use injection
    const repository = AppDataSource.getRepository(User);
  }
}
```

## **🚨 Error Prevention Patterns**

### **Common Auth Middleware Errors to AVOID**
```typescript
// ❌ WRONG: Middleware instantiated at module level
const middleware = createAuthMiddleware(AppDataSource); // DataSource not ready yet
export { middleware };

// ❌ WRONG: Global middleware without DataSource
export const authMiddleware = (req, res, next) => {
  // No access to DataSource for user lookup
};

// ❌ WRONG: Using TypeORM relations in middleware
const user = await userRepository.findOne({
  where: { id: decoded.sub },
  relations: ['roles', 'permissions'] // Causes schema errors
});

// ❌ WRONG: Not handling database errors
try {
  const user = await findUser(decoded.sub);
  req.user = user;
} catch (error) {
  // No error handling - middleware crashes
}
```

### **Correct Error Handling Pattern**
```typescript
// ✅ CORRECT: Comprehensive error handling
export function createAuthMiddleware(dataSource: DataSource) {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      // Validate authorization header
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ 
          message: 'Token de acceso requerido' 
        });
      }

      // Validate JWT token
      const token = authHeader.substring(7);
      let decoded: any;
      
      try {
        decoded = jwt.verify(token, process.env.JWT_SECRET!);
      } catch (jwtError) {
        return res.status(401).json({ 
          message: 'Token inválido o expirado' 
        });
      }

      // Validate user exists and is active
      try {
        const userRepository = dataSource.getRepository(User);
        const userQuery = `
          SELECT id, email, name, status, active 
          FROM users 
          WHERE id = $1 AND active = true
        `;
        
        const userResult = await userRepository.query(userQuery, [decoded.sub]);
        
        if (!userResult || userResult.length === 0) {
          return res.status(401).json({ 
            message: 'Usuario no encontrado o inactivo' 
          });
        }

        req.user = userResult[0];
        next();
      } catch (dbError: any) {
        console.error('[AuthMiddleware] Database error:', dbError);
        return res.status(500).json({ 
          message: 'Error interno del servidor' 
        });
      }
    } catch (error: any) {
      console.error('[AuthMiddleware] Unexpected error:', error);
      return res.status(500).json({ 
        message: 'Error interno del servidor' 
      });
    }
  };
}
```

## **🔄 Migration from Global to Factory Pattern**

### **Step-by-Step Migration**
1. **Create factory function** in auth.middleware.ts
2. **Update route files** to use createAuthMiddleware(dataSource)
3. **Update main routes index** to pass DataSource
4. **Remove global middleware imports**
5. **Test all protected endpoints**

### **Before (Global Pattern)**
```typescript
// ❌ OLD: Global middleware
export const authMiddleware = async (req, res, next) => {
  // Implementation without DataSource injection
};

// ❌ OLD: Route usage
import { authMiddleware } from '../middlewares/auth.middleware';
router.use(authMiddleware);
```

### **After (Factory Pattern)**
```typescript
// ✅ NEW: Factory function
export function createAuthMiddleware(dataSource: DataSource) {
  return async (req, res, next) => {
    // Implementation with DataSource injection
  };
}

// ✅ NEW: Route usage
import { createAuthMiddleware } from '../middlewares/auth.middleware';
const authMiddleware = createAuthMiddleware(dataSource);
router.use(authMiddleware);
```

## **📋 Testing Auth Middleware**

### **Unit Test Pattern**
```typescript
// ✅ Test with mocked DataSource
describe('createAuthMiddleware', () => {
  let mockDataSource: Partial<DataSource>;
  let mockRepository: any;
  
  beforeEach(() => {
    mockRepository = {
      query: jest.fn()
    };
    
    mockDataSource = {
      getRepository: jest.fn().mockReturnValue(mockRepository)
    };
  });
  
  it('should authenticate valid user', async () => {
    mockRepository.query.mockResolvedValue([
      { id: 1, email: '<EMAIL>', active: true }
    ]);
    
    const middleware = createAuthMiddleware(mockDataSource as DataSource);
    const req = { headers: { authorization: 'Bearer valid-token' } };
    const res = { status: jest.fn().mockReturnThis(), json: jest.fn() };
    const next = jest.fn();
    
    await middleware(req as any, res as any, next);
    
    expect(next).toHaveBeenCalled();
    expect(req.user).toBeDefined();
  });
});
```

This pattern ensures reliable, testable, and maintainable authentication middleware that properly handles DataSource initialization and database errors.


