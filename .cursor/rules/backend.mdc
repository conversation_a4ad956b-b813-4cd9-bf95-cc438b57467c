---
description: 
globs: 
alwaysApply: true
---
# Reglas y Comandos Backend

## Comandos principales
- `npm run dev` — Desarrollo
- `npm run build` — Compilar
- `npm start` — Producción
- `npm run test` — Tests
- `npm run migration:generate` — Generar migración
- `npm run migration:run` — Ejecutar migraciones

## Convenciones
- Código en `src/`
- Tests en `__tests__/` y `src/tests/`
- Scripts en `scripts/`
- Usa TypeORM para acceso a base de datos
- Sigue las reglas de linting y formateo

# Backend Development Rules

## **🔄 CRITICAL: Frontend-Backend ID Type Conversion**

### **API Response Format**
- **All IDs in API responses are STRINGS**: `{"id": "78", "name": "ROLE_CALIDAD"}`
- **Database stores IDs as integers** but TypeORM/queries return them as strings
- **This is consistent across ALL endpoints**: users, roles, permissions, etc.

### **Frontend-Backend Data Flow**
```typescript
// ✅ CORRECT: Backend expects numbers for operations
// Create user with role IDs
POST /api/systems/users
{
  "name": "User Name",
  "roles": [78, 31]  // NUMBERS for operations
}

// ✅ CORRECT: Backend returns strings in responses  
Response: {
  "data": {
    "id": "111",     // STRING in response
    "roles": [
      {"id": "78", "name": "ROLE_CALIDAD"},    // STRING
      {"id": "31", "name": "ROLE_CALIDAD_USER"} // STRING
    ]
  }
}
```

### **Frontend Conversion Pattern**
```typescript
// ✅ ALWAYS convert API string IDs to numbers for form validation
const roleIdNum = parseInt(role.id.toString(), 10);

// ✅ Form validation expects numbers
roleIds: z.array(z.number()).min(1, 'Required')

// ✅ Send numbers to backend for operations
const requestData = {
  ...otherData,
  roles: roleIds  // Array of numbers [78, 31]
}
```

### **Backend Service Patterns**
```typescript
// ✅ Create methods that accept number IDs for operations
async createUserWithRoleIds(userData: Partial<User>, roleIds: number[]): Promise<User>

// ✅ Use SQL direct queries to avoid TypeORM schema issues
const roleCheckQuery = `
  SELECT id, name FROM roles WHERE id = ANY($1)
`;
const existingRoles = await repository.query(roleCheckQuery, [roleIds]);
```

## **Database Query Patterns**

### **Use SQL Direct Queries (MANDATORY)**
- **NEVER use TypeORM relations** for entities with problematic columns
- **ALWAYS use raw SQL** for queries involving roles, permissions, users
- **Avoid `findOne({ relations: ['roles'] })`** - causes column errors

```typescript
// ❌ AVOID: TypeORM relations cause schema errors
const user = await repository.findOne({ 
  where: { id }, 
  relations: ['roles', 'roles.permissions'] 
});

// ✅ USE: SQL direct queries
const userQuery = `
  SELECT id, name, email, phone, status, area, active,
         created_at, updated_at, created_by, updated_by
  FROM users WHERE id = $1
`;
const userResult = await repository.query(userQuery, [id]);

const rolesQuery = `
  SELECT r.id, r.name
  FROM user_roles ur
  JOIN roles r ON ur.role_id = r.id  
  WHERE ur.user_id = $1
`;
const rolesResult = await repository.query(rolesQuery, [id]);
```

### **Many-to-Many Relationship Handling**
```typescript
// ✅ Handle user-roles via direct SQL insertion
for (const roleId of roleIds) {
  await repository.query(
    'INSERT INTO user_roles (user_id, role_id) VALUES ($1, $2)',
    [userId, roleId]
  );
}

// ✅ Query many-to-many with explicit JOINs
const query = `
  SELECT u.*, r.id as role_id, r.name as role_name
  FROM users u
  LEFT JOIN user_roles ur ON u.id = ur.user_id
  LEFT JOIN roles r ON ur.role_id = r.id
  WHERE u.id = $1
`;
```

## **Service Layer Conventions**

### **Dual Methods Pattern**
- **Create methods accepting both string names AND number IDs**
- **Use descriptive method names** to indicate expected input type

```typescript
// Legacy method (string names)
async createUser(userData: Partial<User>, roleNames: string[]): Promise<User>

// New method (number IDs) - PREFERRED
async createUserWithRoleIds(userData: Partial<User>, roleIds: number[]): Promise<User>
```

### **Error Handling**
```typescript
// ✅ Validate role existence before operations
if (existingRoles.length !== roleIds.length) {
  throw new Error('One or more roles not found');
}

// ✅ Use meaningful error messages
throw new Error(`Invalid status. Allowed values: ${allowedStatuses.join(', ')}`);
```

## **API Response Consistency**

### **Standard Response Format**
```typescript
// ✅ Consistent API response structure
{
  "data": {}, // or array for lists
  "message"?: string,
  "total"?: number,     // for paginated responses
  "page"?: number,      // for paginated responses  
  "limit"?: number,     // for paginated responses
  "totalPages"?: number // for paginated responses
}
```

### **ID Handling in Responses**
- **All IDs returned as strings** from database queries
- **Maintain this format** across all endpoints
- **Frontend handles conversion** when needed for operations

## **Migration and Schema Considerations**

### **Column Existence Checks**
- **Comment out problematic columns** in entities if they don't exist in Supabase
- **Use SQL queries instead** of entity relations for reliability
- **Document schema differences** between local and Supabase

```typescript
// ✅ Comment out non-existent columns
export class User {
  @PrimaryGeneratedColumn()
  id!: number;
  
  // @Column({ nullable: true })
  // deleted_at?: Date;  // COMMENTED: doesn't exist in Supabase
}
```

## **Performance Patterns**

### **Batch Operations**
```typescript
// ✅ Use Promise.all for parallel queries
const [usersResult, countResult] = await Promise.all([
  repository.query(usersQuery, queryParams),
  repository.query(countQuery, countParams)
]);
```

### **Query Optimization**
- **Select only needed columns** in SQL queries
- **Use prepared statements** with parameterized queries
- **Implement pagination** for large datasets

This ensures consistent data handling across the entire application and prevents type conversion errors between frontend and backend.
