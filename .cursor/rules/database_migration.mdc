---
description: 
globs: 
alwaysApply: true
---
# Database Migration & Schema Management Rules

## **🗄️ CRITICAL: Supabase vs Local Development Pattern (MANDATORY)**

### **Problem Solved**
- **Schema differences** between Supabase production and local development
- **Column existence variations** causing TypeORM errors
- **Migration management** with proper indexing and optimization
- **Entity definition** that works across environments

### **Project Structure for Database Management**
```
AppComintec2.0/
├── backend/
│   ├── src/
│   │   ├── entities/           # TypeORM entities with conditional fields
│   │   ├── migrations/         # TypeORM migrations for development
│   │   └── scripts/            # Database setup and utility scripts
│   ├── comintec_schema_core.sql          # Core schema (production-ready)
│   ├── comintec_schema_data_indexes.sql  # Indexes and optimizations
│   ├── setup-database.sh                # Database initialization script
│   ├── setup-backup-table.sql           # Backup system setup
│   ├── setup-system-config.sql          # System configuration
│   └── setup-test-schema.sql            # Test environment schema
```

## **🔧 Entity Pattern for Cross-Environment Compatibility**

### **Entity Definition with Conditional Columns**
```typescript
// ✅ CORRECT: Entity with commented problematic columns
import { Entity, PrimaryGeneratedColumn, Column, ManyToMany, JoinTable } from 'typeorm';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ length: 100 })
  name!: string;

  @Column({ unique: true, length: 100 })
  email!: string;

  @Column({ length: 255 })
  password!: string;

  @Column({ length: 15, nullable: true })
  phone?: string;

  @Column({ type: 'enum', enum: ['active', 'inactive'], default: 'active' })
  status!: string;

  @Column({ length: 100, nullable: true })
  area?: string;

  @Column({ type: 'boolean', default: true })
  active!: boolean;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at!: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
  updated_at!: Date;

  @Column({ nullable: true })
  created_by?: number;

  @Column({ nullable: true })
  updated_by?: number;

  // ❌ COMMENTED: Column doesn't exist in Supabase
  // @Column({ nullable: true })
  // deleted_at?: Date;

  // Many-to-many relationship (use SQL queries instead)
  // @ManyToMany(() => Role, role => role.users)
  // @JoinTable({
  //   name: 'user_roles',
  //   joinColumn: { name: 'user_id', referencedColumnName: 'id' },
  //   inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' }
  // })
  // roles?: Role[];
}

@Entity('roles')
export class Role {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ unique: true, length: 50 })
  name!: string;

  // ❌ COMMENTED: Column doesn't exist in Supabase
  // @Column({ nullable: true })
  // description?: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at!: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
  updated_at!: Date;

  // ❌ COMMENTED: Column doesn't exist in Supabase
  // @Column({ nullable: true })
  // deleted_at?: Date;

  // Use SQL queries instead of TypeORM relations
  // @ManyToMany(() => User, user => user.roles)
  // users?: User[];

  // @ManyToMany(() => Permission, permission => permission.roles)
  // @JoinTable({
  //   name: 'role_permissions',
  //   joinColumn: { name: 'role_id', referencedColumnName: 'id' },
  //   inverseJoinColumn: { name: 'permission_id', referencedColumnName: 'id' }
  // })
  // permissions?: Permission[];
}
```

### **Service Pattern with SQL Direct Queries**
```typescript
// ✅ CORRECT: Service using SQL instead of TypeORM relations
export class UserService {
  constructor(private dataSource: DataSource) {}

  async findUserById(id: number): Promise<User | null> {
    const repository = this.dataSource.getRepository(User);
    
    // Use direct SQL to avoid column issues
    const userQuery = `
      SELECT 
        id, name, email, phone, status, area, active,
        created_at, updated_at, created_by, updated_by
      FROM users 
      WHERE id = $1 AND active = true
    `;
    
    const result = await repository.query(userQuery, [id]);
    return result.length > 0 ? result[0] : null;
  }

  async getUserWithRoles(id: number): Promise<any> {
    const repository = this.dataSource.getRepository(User);
    
    // Separate queries to avoid relation issues
    const userQuery = `
      SELECT 
        id, name, email, phone, status, area, active,
        created_at, updated_at, created_by, updated_by
      FROM users 
      WHERE id = $1 AND active = true
    `;
    
    const rolesQuery = `
      SELECT r.id, r.name
      FROM user_roles ur
      JOIN roles r ON ur.role_id = r.id  
      WHERE ur.user_id = $1
    `;
    
    const [userResult, rolesResult] = await Promise.all([
      repository.query(userQuery, [id]),
      repository.query(rolesQuery, [id])
    ]);
    
    if (userResult.length === 0) return null;
    
    return {
      ...userResult[0],
      roles: rolesResult
    };
  }

  async createUserWithRoleIds(userData: Partial<User>, roleIds: number[]): Promise<User> {
    const repository = this.dataSource.getRepository(User);
    
    // Validate role IDs exist
    const roleCheckQuery = `SELECT id, name FROM roles WHERE id = ANY($1)`;
    const existingRoles = await repository.query(roleCheckQuery, [roleIds]);
    
    if (existingRoles.length !== roleIds.length) {
      throw new Error('One or more roles not found');
    }
    
    // Insert user with direct SQL
    const insertUserQuery = `
      INSERT INTO users (name, email, password, phone, status, area, created_by)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING id, name, email, phone, status, area, active, created_at, updated_at
    `;
    
    const userResult = await repository.query(insertUserQuery, [
      userData.name,
      userData.email,
      userData.password,
      userData.phone,
      userData.status || 'active',
      userData.area,
      userData.created_by
    ]);
    
    const newUser = userResult[0];
    
    // Insert user-role relationships
    for (const roleId of roleIds) {
      await repository.query(
        'INSERT INTO user_roles (user_id, role_id) VALUES ($1, $2)',
        [newUser.id, roleId]
      );
    }
    
    // Return user with roles
    return await this.getUserWithRoles(newUser.id);
  }
}
```

## **📋 Migration Patterns**

### **TypeORM Migration Structure**
```typescript
// ✅ CORRECT: Migration with proper indexing and constraints
import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateNotificationsTable1751315767062 implements MigrationInterface {
  name = 'CreateNotificationsTable1751315767062';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create notifications table
    await queryRunner.query(`
      CREATE TABLE "notifications" (
        "id" SERIAL NOT NULL,
        "title" character varying(255) NOT NULL,
        "message" text NOT NULL,
        "type" character varying(20) NOT NULL DEFAULT 'info',
        "status" character varying(20) NOT NULL DEFAULT 'unread',
        "target_user_id" integer,
        "target_role" character varying(50),
        "module" character varying(50),
        "metadata" jsonb,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "read_at" TIMESTAMP,
        CONSTRAINT "PK_notifications" PRIMARY KEY ("id")
      )
    `);

    // Create indexes for performance
    await queryRunner.query(`
      CREATE INDEX "IDX_notifications_target_user" ON "notifications" ("target_user_id") 
    `);
    
    await queryRunner.query(`
      CREATE INDEX "IDX_notifications_target_role" ON "notifications" ("target_role") 
    `);
    
    await queryRunner.query(`
      CREATE INDEX "IDX_notifications_status" ON "notifications" ("status") 
    `);
    
    await queryRunner.query(`
      CREATE INDEX "IDX_notifications_module" ON "notifications" ("module") 
    `);
    
    await queryRunner.query(`
      CREATE INDEX "IDX_notifications_created_at" ON "notifications" ("created_at" DESC) 
    `);

    // Add check constraints
    await queryRunner.query(`
      ALTER TABLE "notifications" 
      ADD CONSTRAINT "CHK_notification_type" 
      CHECK ("type" IN ('info', 'warning', 'error', 'success'))
    `);
    
    await queryRunner.query(`
      ALTER TABLE "notifications" 
      ADD CONSTRAINT "CHK_notification_status" 
      CHECK ("status" IN ('unread', 'read'))
    `);

    // Add foreign key constraints (if users table exists)
    await queryRunner.query(`
      ALTER TABLE "notifications" 
      ADD CONSTRAINT "FK_notifications_target_user" 
      FOREIGN KEY ("target_user_id") REFERENCES "users"("id") ON DELETE CASCADE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "notifications"`);
  }
}
```

### **Schema Optimization Migration**
```typescript
// ✅ CORRECT: Performance optimization migration
export class OptimizeTablesAndIndexes1751316200000 implements MigrationInterface {
  name = 'OptimizeTablesAndIndexes1751316200000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add missing indexes for better performance
    await queryRunner.query(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS "IDX_users_email_active" 
      ON "users" ("email", "active") WHERE "active" = true
    `);
    
    await queryRunner.query(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS "IDX_users_status_active" 
      ON "users" ("status", "active") WHERE "active" = true
    `);
    
    await queryRunner.query(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS "IDX_user_roles_user_id" 
      ON "user_roles" ("user_id")
    `);
    
    await queryRunner.query(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS "IDX_user_roles_role_id" 
      ON "user_roles" ("role_id")
    `);
    
    await queryRunner.query(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS "IDX_role_permissions_role_id" 
      ON "role_permissions" ("role_id")
    `);
    
    await queryRunner.query(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS "IDX_audit_logs_user_target" 
      ON "audit_logs" ("user_id", "target_entity", "action")
    `);
    
    await queryRunner.query(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS "IDX_audit_logs_created_at" 
      ON "audit_logs" ("created_at" DESC)
    `);

    // Update table statistics for better query planning
    await queryRunner.query(`ANALYZE "users"`);
    await queryRunner.query(`ANALYZE "roles"`);
    await queryRunner.query(`ANALYZE "user_roles"`);
    await queryRunner.query(`ANALYZE "role_permissions"`);
    await queryRunner.query(`ANALYZE "notifications"`);
    await queryRunner.query(`ANALYZE "audit_logs"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the indexes (Note: CONCURRENTLY can't be used in transactions)
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_users_email_active"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_users_status_active"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_user_roles_user_id"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_user_roles_role_id"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_role_permissions_role_id"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_audit_logs_user_target"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_audit_logs_created_at"`);
  }
}
```

## **🗂️ Schema Management Files**

### **Core Schema (comintec_schema_core.sql)**
```sql
-- ✅ CORRECT: Production-ready schema with all constraints
-- Core Tables for Comintec Application

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(15),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    area VARCHAR(100),
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

-- Roles table
CREATE TABLE IF NOT EXISTS roles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Permissions table
CREATE TABLE IF NOT EXISTS permissions (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    module VARCHAR(50) NOT NULL,
    resource VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Junction tables
CREATE TABLE IF NOT EXISTS user_roles (
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    role_id INTEGER REFERENCES roles(id) ON DELETE CASCADE,
    PRIMARY KEY (user_id, role_id)
);

CREATE TABLE IF NOT EXISTS role_permissions (
    role_id INTEGER REFERENCES roles(id) ON DELETE CASCADE,
    permission_id INTEGER REFERENCES permissions(id) ON DELETE CASCADE,
    PRIMARY KEY (role_id, permission_id)
);

-- Notifications table (real-time system)
CREATE TABLE IF NOT EXISTS notifications (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(20) DEFAULT 'info' CHECK (type IN ('info', 'warning', 'error', 'success')),
    status VARCHAR(20) DEFAULT 'unread' CHECK (status IN ('unread', 'read')),
    target_user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    target_role VARCHAR(50),
    module VARCHAR(50),
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP
);

-- Audit logs table
CREATE TABLE IF NOT EXISTS audit_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(50) NOT NULL,
    target_entity VARCHAR(50) NOT NULL,
    target_id INTEGER,
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### **Indexes and Optimizations (comintec_schema_data_indexes.sql)**
```sql
-- ✅ CORRECT: Comprehensive indexing strategy
-- Performance Indexes for Comintec Application

-- Users table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_active ON users (email, active) WHERE active = true;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_status_active ON users (status, active) WHERE active = true;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_area ON users (area) WHERE area IS NOT NULL;

-- Junction table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_roles_user_id ON user_roles (user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_roles_role_id ON user_roles (role_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_role_permissions_role_id ON role_permissions (role_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_role_permissions_permission_id ON role_permissions (permission_id);

-- Notifications indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_target_user ON notifications (target_user_id) WHERE target_user_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_target_role ON notifications (target_role) WHERE target_role IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_status ON notifications (status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_module ON notifications (module) WHERE module IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_created_at ON notifications (created_at DESC);

-- Audit logs indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_user_id ON audit_logs (user_id) WHERE user_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_target_entity ON audit_logs (target_entity, target_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_action ON audit_logs (action);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_created_at ON audit_logs (created_at DESC);

-- Permissions indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_permissions_module_resource ON permissions (module, resource);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_permissions_name_unique ON permissions (name);

-- Update statistics for query planner
ANALYZE users;
ANALYZE roles;
ANALYZE permissions;
ANALYZE user_roles;
ANALYZE role_permissions;
ANALYZE notifications;
ANALYZE audit_logs;
```

## **🚀 Database Setup Patterns**

### **Automated Setup Script (setup-database.sh)**
```bash
#!/bin/bash
# ✅ CORRECT: Complete database setup automation

set -e

echo "🗄️ Setting up Comintec database..."

# Database configuration
DB_NAME="${DB_NAME:-comintec_app}"
DB_USER="${DB_USER:-postgres}"
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"

# Check if database exists
if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
    echo "✅ Database $DB_NAME already exists"
else
    echo "📋 Creating database $DB_NAME..."
    createdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$DB_NAME"
fi

# Apply core schema
echo "📋 Applying core schema..."
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f comintec_schema_core.sql

# Apply indexes and optimizations
echo "🚀 Applying indexes and optimizations..."
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f comintec_schema_data_indexes.sql

# Setup backup table
echo "💾 Setting up backup system..."
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f setup-backup-table.sql

# Apply system configuration
echo "⚙️ Applying system configuration..."
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f setup-system-config.sql

echo "✅ Database setup completed successfully!"
```

## **⚠️ Common Pitfalls and Solutions**

### **Schema Differences Between Environments**
```typescript
// ❌ WRONG: Assuming all columns exist
const user = await userRepository.findOne({
  where: { id },
  relations: ['roles'] // May fail if relation columns don't exist
});

// ✅ CORRECT: Use SQL queries for reliability
const userQuery = `
  SELECT id, name, email, phone, status, area, active
  FROM users WHERE id = $1
`;
const user = await userRepository.query(userQuery, [id]);
```

### **Migration Rollback Safety**
```typescript
// ✅ CORRECT: Always provide rollback logic
export class SafeMigration implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if column already exists
    const hasColumn = await queryRunner.hasColumn('users', 'new_column');
    
    if (!hasColumn) {
      await queryRunner.addColumn('users', new TableColumn({
        name: 'new_column',
        type: 'varchar',
        length: '100',
        isNullable: true
      }));
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const hasColumn = await queryRunner.hasColumn('users', 'new_column');
    
    if (hasColumn) {
      await queryRunner.dropColumn('users', 'new_column');
    }
  }
}
```

## **📊 Performance Monitoring**

### **Query Performance Tracking**
```sql
-- ✅ Monitor slow queries
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements
WHERE mean_time > 100  -- Queries taking more than 100ms
ORDER BY mean_time DESC
LIMIT 10;

-- ✅ Check index usage
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
WHERE idx_scan = 0  -- Unused indexes
ORDER BY relname;
```

This database management approach ensures compatibility across development and production environments while maintaining optimal performance through proper indexing and query patterns.


