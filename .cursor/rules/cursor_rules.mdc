---
description: Guidelines for creating and maintaining Cursor rules to ensure consistency and effectiveness.
globs: .cursor/rules/*.mdc
alwaysApply: true
---
# Core Development Rules

## **🔄 CRITICAL: ID Type Conversion Between Frontend-Backend**

### **Golden Rule: API IDs are ALWAYS strings, Operations need numbers**
- **API responses return ALL IDs as strings**: `{"id": "78"}`
- **Frontend operations/validations need numbers**: `roleIds: [78, 31]`
- **ALWAYS convert before using in forms/validation**

### **Frontend Form Patterns**
```typescript
// ✅ MANDATORY: Convert API string IDs to numbers for form validation
const roleIdNum = parseInt(role.id.toString(), 10);

// ✅ Form schema expects numbers
const schema = z.object({
  roleIds: z.array(z.number()).min(1, 'Required')
});

// ✅ Checkbox/Select handling
checked={field.value.includes(roleIdNum)}
onCheckedChange={() => handleToggle(roleIdNum)}

// ✅ When sending to backend for operations
const requestData = {
  ...otherData,
  roles: roleIds  // Array of numbers [78, 31]
}
```

### **Frontend Service Patterns**
```typescript
// ✅ Handle API response structure correctly
export interface Role {
  id: number  // Will be string from API, convert when needed
  name: string
}

// ✅ Service method conversion
async createUser(data: CreateUserData): Promise<User> {
  const { roleIds, ...otherData } = data
  const requestData = {
    ...otherData,
    roles: roleIds  // Send as numbers for backend operations
  }
  // Backend expects: { roles: [78, 31] }
}
```

### **React Hook Form + Zod Integration**
```typescript
// ✅ Initialize form with proper number conversion
useEffect(() => {
  if (isEditing && user) {
    form.reset({
      roleIds: user.roles?.map(role => parseInt(role.id.toString(), 10)) || [],
    })
  }
}, [user, isEditing, form])

// ✅ Render with proper conversion
{roles.map((role: any) => {
  const roleIdNum = parseInt(role.id.toString(), 10);
  return (
    <Checkbox
      checked={field.value.includes(roleIdNum)}
      onCheckedChange={() => handleRoleToggle(roleIdNum)}
    />
  );
})}
```

## **Error Prevention Patterns**

### **Common ID Type Errors to AVOID**
```typescript
// ❌ WRONG: Using string IDs in number array validation
roleIds: user.roles?.map(role => role.id) || []  // role.id is string!

// ❌ WRONG: Direct comparison without conversion  
checked={field.value.includes(role.id)}  // string vs number mismatch

// ❌ WRONG: Sending strings when backend expects numbers
roles: user.roles?.map(role => role.id)  // Backend gets ["78"] instead of [78]
```

### **Validation Patterns**
```typescript
// ✅ Always validate ID conversion
const roleId = parseInt(role.id.toString(), 10);
if (isNaN(roleId)) {
  throw new Error(`Invalid role ID: ${role.id}`);
}

// ✅ Type-safe form handling
const handleRoleToggle = (roleId: number) => {
  const currentRoles = form.getValues('roleIds');
  if (currentRoles.includes(roleId)) {
    form.setValue('roleIds', currentRoles.filter(id => id !== roleId));
  } else {
    form.setValue('roleIds', [...currentRoles, roleId]);
  }
};
```

## **Backend Service Integration**

### **Controller-Service Pattern**
```typescript
// ✅ Backend: Accept numbers for operations
async createUser(req: Request, res: Response): Promise<void> {
  const { roles, ...userData } = req.body;
  // roles = [78, 31] (numbers from frontend)
  const newUser = await this.userService.createUserWithRoleIds(userData, roles);
}

// ✅ Service: Use SQL direct queries for reliability
async createUserWithRoleIds(userData: Partial<User>, roleIds: number[]): Promise<User> {
  const roleCheckQuery = `SELECT id, name FROM roles WHERE id = ANY($1)`;
  const existingRoles = await repository.query(roleCheckQuery, [roleIds]);
  // ... rest of implementation
}
```

### **Response Format Consistency**
```typescript
// ✅ Backend: Always return IDs as strings (natural from SQL queries)
{
  "data": {
    "id": "111",  // String
    "roles": [
      {"id": "78", "name": "ROLE_CALIDAD"}  // String IDs
    ]
  }
}

// ✅ Frontend: Convert when needed for operations
const roles = response.data.roles.map(role => ({
  ...role,
  numericId: parseInt(role.id, 10)  // For form validation
}));
```

## **Development Workflow**

### **Before Creating Forms with IDs**
1. **Verify API response format** - check if IDs are strings or numbers
2. **Plan conversion points** - where will you convert string→number?  
3. **Set up proper validation** - Zod schema with correct types
4. **Test with real data** - ensure no "undefined" in form fields

### **Debugging ID Issues**
```typescript
// ✅ Add logging for ID type debugging
console.log('[DEBUG] Role ID type:', typeof role.id, 'Value:', role.id);
console.log('[DEBUG] Form values:', form.getValues());
console.log('[DEBUG] Validation errors:', form.formState.errors);
```

### **Code Review Checklist**
- [ ] **API response IDs handled as strings**
- [ ] **Form validation uses numbers** 
- [ ] **Conversion points clearly marked**
- [ ] **Backend methods accept appropriate types**
- [ ] **No mixed string/number comparisons**
- [ ] **Error handling for invalid conversions**

This prevents the "undefined" role selection bug and ensures consistent data flow across the entire stack.

## **File References:**
- Use `[filename](mdc:path/to/file)` ([filename](mdc:filename)) to reference files
- Example: [backend.mdc](mdc:.cursor/rules/backend.mdc) for backend-specific rules
- Example: [complex_features.mdc](mdc:.cursor/rules/complex_features.mdc) for advanced patterns

## **📋 Complete Rules Index:**

### **🎯 Core Development Rules**
- **[cursor_rules.mdc](mdc:.cursor/rules/cursor_rules.mdc)** - General development guidelines and meta-rules
- **[dev_workflow.mdc](mdc:.cursor/rules/dev_workflow.mdc)** - Backend-frontend workflow patterns
- **[project_conventions.mdc](mdc:.cursor/rules/project_conventions.mdc)** - Project structure and naming

### **🔧 Technical Implementation Rules** 
- **[id_management.mdc](mdc:.cursor/rules/id_management.mdc)** - ⭐ **MOST CRITICAL** - ID conversion between frontend/backend
- **[api_patterns.mdc](mdc:.cursor/rules/api_patterns.mdc)** - API call patterns, React Query, error handling
- **[auth_middleware.mdc](mdc:.cursor/rules/auth_middleware.mdc)** - Authentication middleware factory patterns
- **[backend.mdc](mdc:.cursor/rules/backend.mdc)** - Backend services, SQL queries, TypeORM patterns
- **[frontend.mdc](mdc:.cursor/rules/frontend.mdc)** - Frontend components, forms, validation

### **🏗️ System Architecture Rules**
- **[notifications_system.mdc](mdc:.cursor/rules/notifications_system.mdc)** - Real-time notifications with WebSocket
- **[database_migration.mdc](mdc:.cursor/rules/database_migration.mdc)** - Schema management and Supabase compatibility
- **[permissions.mdc](mdc:.cursor/rules/permissions.mdc)** - RBAC patterns and security
- **[complex_features.mdc](mdc:.cursor/rules/complex_features.mdc)** - Advanced features (QR, PDF, backups)

### **🤖 AI Assistant Integration**
- **[taskmaster.mdc](mdc:.cursor/rules/taskmaster.mdc)** - Taskmaster usage patterns
- **[taskmaster/dev_workflow.mdc](mdc:.cursor/rules/taskmaster/dev_workflow.mdc)** - Development workflow with Taskmaster
- **[taskmaster/taskmaster.mdc](mdc:.cursor/rules/taskmaster/taskmaster.mdc)** - Command reference
- **[self_improve.mdc](mdc:.cursor/rules/self_improve.mdc)** - Rule improvement patterns

### **📁 Project File Structure Reference**
Based on the complete project tree, the key directories and their purposes:

```
AppComintec2.0/
├── .cursor/rules/          # All development rules (THIS DIRECTORY)
├── .taskmaster/           # Taskmaster configuration and tasks
├── backend/
│   ├── src/
│   │   ├── entities/      # TypeORM entities with conditional fields  
│   │   ├── services/      # Business logic with SQL direct queries
│   │   ├── controllers/   # API endpoints with error handling
│   │   ├── middlewares/   # Auth middleware factory pattern
│   │   ├── routes/        # Route configuration with DataSource injection
│   │   └── migrations/    # Database schema migrations
│   ├── scripts/           # Database setup and utility scripts
│   ├── __tests__/         # Backend tests with proper setup
│   └── *.sql              # Schema files for Supabase compatibility
├── frontend/
│   ├── app/               # Next.js 15 app directory
│   ├── components/        # React components with Radix UI
│   ├── hooks/             # Custom hooks with React Query
│   ├── lib/               # Utility libraries and services
│   └── providers/         # Context providers (Auth, WebSocket, Query)
├── scripts/               # Project-wide automation scripts
└── Requerimientos/        # Project requirements documentation
```

### **🎯 Rule Priority by Criticality**
1. **🚨 CRITICAL**: [id_management.mdc](mdc:.cursor/rules/id_management.mdc) - Prevents "undefined" form errors
2. **🔐 CRITICAL**: [auth_middleware.mdc](mdc:.cursor/rules/auth_middleware.mdc) - Prevents middleware deadlocks  
3. **🔧 HIGH**: [api_patterns.mdc](mdc:.cursor/rules/api_patterns.mdc) - Consistent API structure
4. **🗄️ HIGH**: [database_migration.mdc](mdc:.cursor/rules/database_migration.mdc) - Schema compatibility
5. **📢 MEDIUM**: [notifications_system.mdc](mdc:.cursor/rules/notifications_system.mdc) - Real-time features
6. **🏗️ MEDIUM**: [backend.mdc](mdc:.cursor/rules/backend.mdc) - Backend patterns

## **Required Rule Structure:**
```markdown
---
description: Clear, one-line description of what the rule enforces
globs: path/to/files/*.ext, other/path/**/*
alwaysApply: boolean
---

- **Main Points in Bold**
  - Sub-points with details
  - Examples and explanations
```

## **Code Examples:**
- Use language-specific code blocks
```typescript
// ✅ DO: Show good examples
const goodExample = true;

// ❌ DON'T: Show anti-patterns
const badExample = false;
```

## **Rule Content Guidelines:**
- Start with high-level overview
- Include specific, actionable requirements
- Show examples of correct implementation
- Reference existing code when possible
- Keep rules DRY by referencing other rules

## **Rule Maintenance:**
- Update rules when new patterns emerge
- Add examples from actual codebase
- Remove outdated patterns
- Cross-reference related rules

## **Best Practices:**
- Use bullet points for clarity
- Keep descriptions concise
- Include both DO and DON'T examples
- Reference actual code over theoretical examples
- Use consistent formatting across rules 