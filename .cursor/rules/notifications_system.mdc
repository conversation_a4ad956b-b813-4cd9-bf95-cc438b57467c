---
description: 
globs: 
alwaysApply: true
---
# Real-Time Notifications System Rules

## **🔔 CRITICAL: Notification System Architecture (MANDATORY)**

### **Problem Solved**
- **Real-time notifications** across all modules (sistemas, almacén, calidad, etc.)
- **WebSocket connection management** with automatic reconnection
- **Notification persistence** and status tracking
- **Cross-module notification delivery** with proper targeting

### **Backend Service Architecture**

#### **1. Notification Entity Structure**
```typescript
// ✅ CORRECT: Notification entity with proper fields
@Entity('notifications')
export class Notification {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  title: string;

  @Column()
  message: string;

  @Column({ type: 'enum', enum: ['info', 'warning', 'error', 'success'] })
  type: 'info' | 'warning' | 'error' | 'success';

  @Column({ type: 'enum', enum: ['unread', 'read'] })
  status: 'unread' | 'read';

  @Column({ nullable: true })
  targetUserId?: number;

  @Column({ nullable: true })
  targetRole?: string;

  @Column({ nullable: true })
  module?: string; // sistemas, almacen, calidad, etc.

  @Column({ type: 'json', nullable: true })
  metadata?: any;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ nullable: true })
  readAt?: Date;
}
```

#### **2. NotificationService Pattern**
```typescript
// ✅ CORRECT: Notification service with targeting capabilities
export class NotificationService {
  constructor(private dataSource: DataSource) {}

  // Create and broadcast notification
  async createNotification(data: {
    title: string;
    message: string;
    type: 'info' | 'warning' | 'error' | 'success';
    targetUserId?: number;
    targetRole?: string;
    module?: string;
    metadata?: any;
  }): Promise<Notification> {
    const repository = this.dataSource.getRepository(Notification);
    
    const notification = repository.create({
      ...data,
      status: 'unread',
      createdAt: new Date(),
    });
    
    const savedNotification = await repository.save(notification);
    
    // Broadcast via WebSocket
    this.websocketService.broadcastNotification(savedNotification);
    
    return savedNotification;
  }

  // Get notifications with filtering
  async getNotifications(userId: number, filters: {
    status?: 'unread' | 'read';
    module?: string;
    page?: number;
    limit?: number;
  } = {}): Promise<{
    notifications: Notification[];
    total: number;
    unreadCount: number;
  }> {
    const repository = this.dataSource.getRepository(Notification);
    
    // Build query with user targeting
    const baseQuery = `
      SELECT * FROM notifications 
      WHERE (target_user_id = $1 OR target_user_id IS NULL)
      AND (target_role IS NULL OR target_role = ANY($2))
    `;
    
    // Get user roles for targeting
    const userRoles = await this.getUserRoles(userId);
    
    // Add filters
    const conditions = [];
    const params = [userId, userRoles];
    
    if (filters.status) {
      conditions.push(`AND status = $${params.length + 1}`);
      params.push(filters.status);
    }
    
    if (filters.module) {
      conditions.push(`AND module = $${params.length + 1}`);
      params.push(filters.module);
    }
    
    const finalQuery = baseQuery + conditions.join(' ') + ' ORDER BY created_at DESC';
    
    // Execute with pagination
    const limit = filters.limit || 20;
    const offset = ((filters.page || 1) - 1) * limit;
    
    const notifications = await repository.query(
      finalQuery + ` LIMIT $${params.length + 1} OFFSET $${params.length + 2}`,
      [...params, limit, offset]
    );
    
    const countResult = await repository.query(
      `SELECT COUNT(*) FROM (${finalQuery}) as filtered`,
      params
    );
    
    const unreadResult = await repository.query(
      baseQuery + ` AND status = 'unread'`,
      [userId, userRoles]
    );
    
    return {
      notifications,
      total: parseInt(countResult[0].count),
      unreadCount: unreadResult.length,
    };
  }

  // Mark notifications as read
  async markAsRead(notificationIds: number[], userId: number): Promise<void> {
    const repository = this.dataSource.getRepository(Notification);
    
    await repository.query(
      `UPDATE notifications 
       SET status = 'read', read_at = CURRENT_TIMESTAMP 
       WHERE id = ANY($1) 
       AND (target_user_id = $2 OR target_user_id IS NULL)`,
      [notificationIds, userId]
    );
  }

  // Get notification stats
  async getNotificationStats(userId: number): Promise<{
    total: number;
    unread: number;
    read: number;
  }> {
    const userRoles = await this.getUserRoles(userId);
    const repository = this.dataSource.getRepository(Notification);
    
    const statsQuery = `
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'unread' THEN 1 END) as unread,
        COUNT(CASE WHEN status = 'read' THEN 1 END) as read
      FROM notifications 
      WHERE (target_user_id = $1 OR target_user_id IS NULL)
      AND (target_role IS NULL OR target_role = ANY($2))
    `;
    
    const result = await repository.query(statsQuery, [userId, userRoles]);
    
    return {
      total: parseInt(result[0].total),
      unread: parseInt(result[0].unread),
      read: parseInt(result[0].read),
    };
  }
}
```

#### **3. WebSocket Service Pattern**
```typescript
// ✅ CORRECT: WebSocket service with room management
export class WebSocketService {
  private io: Server;
  private userSockets = new Map<number, Set<string>>(); // userId -> Set<socketId>

  initialize(server: any): void {
    this.io = new Server(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3001",
        methods: ["GET", "POST"],
        credentials: true
      }
    });

    this.io.on('connection', (socket) => {
      console.log('🔌 Cliente conectado:', socket.id);

      // Handle user authentication
      socket.on('authenticate', (data: { userId: number, token: string }) => {
        try {
          // Verify JWT token
          const decoded = jwt.verify(data.token, process.env.JWT_SECRET!) as any;
          
          if (decoded.sub === data.userId) {
            // Add to user socket mapping
            if (!this.userSockets.has(data.userId)) {
              this.userSockets.set(data.userId, new Set());
            }
            this.userSockets.get(data.userId)!.add(socket.id);
            
            // Join user-specific room
            socket.join(`user_${data.userId}`);
            
            console.log(`✅ Usuario ${data.userId} autenticado en socket ${socket.id}`);
            socket.emit('authenticated', { success: true });
          }
        } catch (error) {
          console.error('❌ Error autenticando socket:', error);
          socket.emit('authentication_error', { message: 'Token inválido' });
        }
      });

      // Handle disconnect
      socket.on('disconnect', () => {
        // Remove from user socket mapping
        for (const [userId, sockets] of this.userSockets.entries()) {
          if (sockets.has(socket.id)) {
            sockets.delete(socket.id);
            if (sockets.size === 0) {
              this.userSockets.delete(userId);
            }
            break;
          }
        }
        console.log('🔌 Cliente desconectado:', socket.id);
      });
    });
  }

  // Broadcast notification to specific user
  broadcastToUser(userId: number, notification: Notification): void {
    this.io.to(`user_${userId}`).emit('notification', notification);
  }

  // Broadcast notification to role
  broadcastToRole(role: string, notification: Notification): void {
    this.io.emit('role_notification', { role, notification });
  }

  // Broadcast notification based on targeting
  broadcastNotification(notification: Notification): void {
    if (notification.targetUserId) {
      // Send to specific user
      this.broadcastToUser(notification.targetUserId, notification);
    } else if (notification.targetRole) {
      // Send to users with specific role
      this.broadcastToRole(notification.targetRole, notification);
    } else {
      // Broadcast to all connected users
      this.io.emit('notification', notification);
    }
    
    console.log(`📢 Notificación enviada: ${notification.title}`);
  }

  // Send real-time updates
  broadcastUpdate(module: string, data: any): void {
    this.io.emit('module_update', { module, data });
  }
}
```

## **🔧 Frontend WebSocket Integration**

### **1. WebSocket Client Service**
```typescript
// ✅ CORRECT: Frontend WebSocket service with reconnection
export class WebSocketClient {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 5000;

  connect(): void {
    if (this.socket?.connected) return;

    const token = localStorage.getItem('access_token');
    if (!token) {
      console.warn('⚠️ No token available for WebSocket connection');
      return;
    }

    this.socket = io(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000', {
      autoConnect: true,
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionAttempts: this.maxReconnectAttempts,
    });

    this.socket.on('connect', () => {
      console.log('🔌 Conectado al servidor WebSocket');
      this.reconnectAttempts = 0;
      
      // Authenticate with server
      const userData = JSON.parse(localStorage.getItem('user') || '{}');
      if (userData.id) {
        this.socket!.emit('authenticate', {
          userId: userData.id,
          token: token
        });
      }
    });

    this.socket.on('authenticated', () => {
      console.log('✅ WebSocket autenticado correctamente');
    });

    this.socket.on('notification', (notification: Notification) => {
      console.log('📢 Nueva notificación:', notification);
      
      // Show toast notification
      toast.success(notification.title, {
        description: notification.message,
        duration: 5000,
      });
      
      // Trigger notification refetch
      this.notifyListeners('new_notification', notification);
    });

    this.socket.on('disconnect', () => {
      console.log('🔌 Desconectado del servidor WebSocket');
    });

    this.socket.on('connect_error', (error) => {
      console.error('❌ Error de conexión WebSocket:', error);
      this.handleReconnect();
    });
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`🔄 Reintentando conexión... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect();
      }, this.reconnectInterval);
    }
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  // Event listeners for React components
  private listeners = new Map<string, Set<Function>>();

  subscribe(event: string, callback: Function): () => void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(callback);

    // Return unsubscribe function
    return () => {
      this.listeners.get(event)?.delete(callback);
    };
  }

  private notifyListeners(event: string, data: any): void {
    this.listeners.get(event)?.forEach(callback => callback(data));
  }
}

// Export singleton instance
export const websocketClient = new WebSocketClient();
```

### **2. React WebSocket Provider**
```typescript
// ✅ CORRECT: WebSocket provider with context
interface WebSocketContextType {
  isConnected: boolean;
  notifications: Notification[];
  unreadCount: number;
  markAsRead: (ids: number[]) => void;
  refetchNotifications: () => void;
}

export const WebSocketProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    // Connect WebSocket
    websocketClient.connect();

    // Subscribe to new notifications
    const unsubscribe = websocketClient.subscribe('new_notification', (notification: Notification) => {
      setNotifications(prev => [notification, ...prev]);
      if (notification.status === 'unread') {
        setUnreadCount(prev => prev + 1);
      }
    });

    return () => {
      unsubscribe();
      websocketClient.disconnect();
    };
  }, []);

  // Fetch initial notifications
  const { data: notificationsData, refetch } = useQuery({
    queryKey: ['notifications'],
    queryFn: () => notificationService.getNotifications(),
    refetchInterval: 30000, // Refetch every 30 seconds as fallback
  });

  useEffect(() => {
    if (notificationsData) {
      setNotifications(notificationsData.notifications);
      setUnreadCount(notificationsData.unreadCount);
    }
  }, [notificationsData]);

  const markAsRead = useCallback(async (ids: number[]) => {
    try {
      await notificationService.markAsRead(ids);
      setNotifications(prev => 
        prev.map(notif => 
          ids.includes(notif.id) 
            ? { ...notif, status: 'read' as const, readAt: new Date() }
            : notif
        )
      );
      setUnreadCount(prev => Math.max(0, prev - ids.length));
    } catch (error) {
      console.error('Error marking notifications as read:', error);
    }
  }, []);

  return (
    <WebSocketContext.Provider value={{
      isConnected,
      notifications,
      unreadCount,
      markAsRead,
      refetchNotifications: refetch,
    }}>
      {children}
    </WebSocketContext.Provider>
  );
};
```

### **3. Real-Time Notifications Component**
```typescript
// ✅ CORRECT: Notifications component with real-time updates
export const RealTimeNotifications: React.FC = () => {
  const { notifications, unreadCount, markAsRead } = useWebSocket();
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center text-xs"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      
      <PopoverContent className="w-80 p-0" align="end">
        <div className="flex items-center justify-between p-4 border-b">
          <h4 className="font-semibold">Notificaciones</h4>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                const unreadIds = notifications
                  .filter(n => n.status === 'unread')
                  .map(n => n.id);
                markAsRead(unreadIds);
              }}
            >
              Marcar todas como leídas
            </Button>
          )}
        </div>
        
        <ScrollArea className="h-96">
          {notifications.length === 0 ? (
            <div className="p-4 text-center text-sm text-muted-foreground">
              No hay notificaciones
            </div>
          ) : (
            <div className="space-y-1">
              {notifications.map((notification) => (
                <NotificationItem
                  key={notification.id}
                  notification={notification}
                  onMarkAsRead={(id) => markAsRead([id])}
                />
              ))}
            </div>
          )}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
};
```

## **📋 Module Integration Patterns**

### **Notification Creation from Any Module**
```typescript
// ✅ CORRECT: How to create notifications from any service
export class AlmacenService {
  constructor(
    private dataSource: DataSource,
    private notificationService: NotificationService
  ) {}

  async updateStock(productId: number, newQuantity: number): Promise<void> {
    // Update stock logic...
    
    // Create notification for low stock
    if (newQuantity < minStockLevel) {
      await this.notificationService.createNotification({
        title: 'Stock Bajo',
        message: `El producto ${productName} tiene stock bajo (${newQuantity} unidades)`,
        type: 'warning',
        module: 'almacen',
        targetRole: 'ROLE_ALMACEN',
        metadata: {
          productId,
          currentStock: newQuantity,
          minStock: minStockLevel,
        },
      });
    }
  }
}

// ✅ CORRECT: User-specific notifications
export class SistemasService {
  async assignRole(userId: number, roleId: number): Promise<void> {
    // Assignment logic...
    
    // Notify specific user
    await this.notificationService.createNotification({
      title: 'Nuevo Rol Asignado',
      message: `Se te ha asignado un nuevo rol: ${roleName}`,
      type: 'success',
      module: 'sistemas',
      targetUserId: userId,
      metadata: { roleId, roleName },
    });
  }
}
```

## **🔄 Error Handling & Fallbacks**

### **WebSocket Connection Management**
```typescript
// ✅ CORRECT: Robust error handling
export const useNotificationSystem = () => {
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting');
  const [notifications, setNotifications] = useState<Notification[]>([]);

  useEffect(() => {
    let pollInterval: NodeJS.Timeout;

    // WebSocket connection with fallback to polling
    try {
      websocketClient.connect();
      setConnectionStatus('connected');
    } catch (error) {
      console.warn('WebSocket failed, falling back to polling');
      setConnectionStatus('disconnected');
      
      // Fallback to polling every 30 seconds
      pollInterval = setInterval(async () => {
        try {
          const data = await notificationService.getNotifications();
          setNotifications(data.notifications);
        } catch (pollError) {
          console.error('Polling failed:', pollError);
        }
      }, 30000);
    }

    return () => {
      if (pollInterval) clearInterval(pollInterval);
      websocketClient.disconnect();
    };
  }, []);

  return { connectionStatus, notifications };
};
```

## **🎯 Key Implementation Points**

### **1. Notification Targeting Strategy**
- **User-specific**: `targetUserId` for individual notifications
- **Role-based**: `targetRole` for department-wide notifications  
- **Module-based**: `module` field for filtering by area
- **Global**: No targeting for system-wide announcements

### **2. Real-Time Delivery**
- **Primary**: WebSocket for instant delivery
- **Fallback**: HTTP polling every 30 seconds
- **Persistence**: All notifications stored in database

### **3. Module Integration**
- **Any service** can create notifications via `NotificationService`
- **Consistent patterns** across all modules (almacén, sistemas, calidad, etc.)
- **Metadata support** for module-specific data

### **4. Frontend Integration**
- **Global provider** for WebSocket management
- **Hook-based** access to notifications
- **Toast integration** for immediate feedback
- **Persistent UI** with badge counts and history

This notification system provides real-time, reliable, and scalable communication across all modules of the application.

---
## 📝 Historial de decisiones y patrones de UX multiusuario (Junio 2024)

### Resumen de problemas y soluciones implementadas

1. **Problemas iniciales**
   - Notificaciones duplicadas, problemas al marcar como leídas y desincronización frontend-backend.
   - El backend generaba notificaciones de stock bajo/crítico para cada usuario relevante.
   - El frontend mostraba duplicados y no sincronizaba correctamente el estado leído/no leído.

2. **Soluciones aplicadas**
   - Backend: notificaciones individuales por usuario, endpoint para marcar duplicados como leídos, filtrado estricto por usuario.
   - Frontend: deduplicación visual, actualización masiva de duplicados al marcar como leído, polling HTTP cada 20s, solo notificaciones del usuario autenticado.

3. **Ajustes adicionales**
   - Corrección de filtrado en endpoint `/api/notifications`.
   - Visualización de todas las notificaciones (leídas/no leídas) y distinción visual clara.
   - Mejoras de UI: alineación, truncado, colores de prioridad.
   - Mensaje de alerta de stock más claro.

4. **Eliminación de notificaciones**
   - Implementación de botón "Eliminar" en frontend.
   - Ruta DELETE `/api/notifications/:id` en backend, validando usuario dueño.
   - Actualización inmediata del estado local tras borrar.

5. **Sincronización y UX**
   - Estado local actualizado tras DELETE exitoso usando `setNotifications`.

6. **Token y autenticación**
   - Corrección para obtener el token desde el contexto de autenticación (`useAuth().token`).

7. **Estado final**
   - Solo notificaciones del usuario autenticado.
   - Marcar como leídas y eliminar de forma segura.
   - Sincronización en tiempo real y por polling.
   - Historial completo y UI clara.

---
**Regla de referencia:**
> Toda nueva funcionalidad o ajuste en el sistema de notificaciones debe respetar estos patrones para asegurar una experiencia multiusuario robusta, sin duplicados, con sincronización eficiente y control de acceso seguro. Documentar cualquier excepción o cambio relevante en esta sección para mantener el historial de decisiones accesible a todo el equipo.


