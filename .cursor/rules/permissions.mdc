---
description: 
globs: 
alwaysApply: true
---
# Patrón `module:resource:action`

**Objetivo**: estandarizar todos los identificadores de permisos para que sean legibles, predecibles y fáciles de mapear a UI y documentación.

- `module`  → nombre del módulo funcional (almacen, compras, calidad, rh, logistica, ventas…)
- `resource` → entidad o funcionalidad concreta (stock, qr, solicitud, link, incidente, file, training, etc.)
- `action`   → verbo en infinitivo en inglés (`create`, `read`, `update`, `delete`, `list`, `approve`, `schedule`, `generate_pdf` …)

## Ejemplos
| Caso | Permiso |
|------|---------|
| Crear pedido de compra | `compras:request:create` |
| Ver stock de almacén | `almacen:stock:read` |
| Generar hoja de servicio (PDF) | `logistica:service:generate_pdf` |
| Aprobar incidente | `quality:incidencia:approve` |

## Reglas
- Siempre minúsculas y sin espacios.
- Múltiples acciones → se crean permisos independientes.
- Wildcard `*` permitido solo para roles administrativos (`compras:*`).
- Las comprobaciones RBAC aceptan coincidencia exacta o wildcard.

## Implementación
1. Definir los permisos en un archivo YAML central (`scripts/permissions.yaml`) y generar enums automáticamente.
2. Backend: middleware lee scopes del JWT y compara contra permisos requeridos por endpoint.
3. Frontend: PageGuard consulta `/api/auth/me` y oculta UI según array de permisos.

## Migración de permisos existentes
- Mapear permisos previos (`SISTEMAS_READ`, `SISTEMAS_WRITE`, etc.) al nuevo formato.
- Mantener alias antiguos por 1 versión para compatibilidad.

# RBAC Permission Management Rules

## **🔐 CRITICAL: Role Naming Convention (MANDATORY)**

### **Consistent Naming Pattern**
- **ALL roles MUST use prefix `ROLE_`**: `ROLE_ADMIN`, `ROLE_CALIDAD`, etc.
- **NO roles without prefix**: Avoid `ADMIN`, `USER`, etc.
- **Backend expects**: `ROLE_ADMIN` format for all permission checks
- **Frontend sends**: Role IDs as numbers for operations

### **Permission Pattern: `module:resource:action`**
```typescript
// ✅ CORRECT: Standard permission format
"sistemas:users:read"
"almacen:stock:update"  
"calidad:incidents:approve"

// ❌ AVOID: Non-standard formats
"users:read"
"SISTEMAS_READ"
```

## **🔧 Backend Service Patterns**

### **Role Assignment with SQL Direct Queries**
```typescript
// ✅ MANDATORY: Use SQL direct queries to avoid TypeORM schema issues
async assignPermissionsToRole(req: Request, res: Response): Promise<void> {
  // Verify role exists
  const roleQuery = `SELECT id, name FROM roles WHERE id = $1`;
  const roleResult = await repository.query(roleQuery, [roleId]);
  
  // Verify permissions exist
  const permissionQuery = `
    SELECT id, name, module, action
    FROM permissions WHERE name = ANY($1)
  `;
  const permissions = await repository.query(permissionQuery, [permissionNames]);
  
  // Clear existing permissions
  await repository.query('DELETE FROM role_permissions WHERE role_id = $1', [roleId]);
  
  // Assign new permissions
  for (const permission of permissions) {
    await repository.query(
      'INSERT INTO role_permissions (role_id, permission_id) VALUES ($1, $2)',
      [roleId, permission.id]
    );
  }
}
```

### **NEVER Use TypeORM Relations for Roles/Permissions**
```typescript
// ❌ AVOID: TypeORM relations cause "column does not exist" errors
const role = await repository.findOne({ 
  where: { id }, 
  relations: ['permissions'] 
});

// ✅ USE: SQL direct queries
const roleQuery = `SELECT id, name FROM roles WHERE id = $1`;
const permissionsQuery = `
  SELECT p.* FROM role_permissions rp 
  JOIN permissions p ON rp.permission_id = p.id 
  WHERE rp.role_id = $1
`;
```

## **🎯 Frontend Service Patterns**

### **Role Assignment API Calls**
```typescript
// ✅ Service method for assigning permissions
async assignPermissionsToRole(roleId: number, permissionNames: string[]): Promise<Role> {
  const response = await apiClient.post('/api/systems/roles/assign-permissions', {
    roleId: parseInt(roleId.toString(), 10),  // Ensure number type
    permissionNames: permissionNames          // Array of permission strings
  });
  
  if (!response.success) {
    throw new Error(response.message || 'Error assigning permissions');
  }
  return response.data!;
}
```

### **Permission Selection UI Pattern**
```typescript
// ✅ Group permissions by module for better UX
const permissionsByModule = permissions.reduce((acc, permission) => {
  const module = permission.module || 'other';
  if (!acc[module]) acc[module] = [];
  acc[module].push(permission);
  return acc;
}, {} as Record<string, Permission[]>);

// ✅ Handle permission selection with proper ID conversion
const handlePermissionToggle = (permissionName: string) => {
  const currentPermissions = selectedPermissions;
  if (currentPermissions.includes(permissionName)) {
    setSelectedPermissions(current => current.filter(p => p !== permissionName));
  } else {
    setSelectedPermissions(current => [...current, permissionName]);
  }
};
```

## **🗄️ Database Schema Requirements**

### **Required Tables**
- **`roles`**: `id`, `name` (NO description, created_at, updated_at in Supabase)
- **`permissions`**: `id`, `name`, `module`, `action`
- **`role_permissions`**: `role_id`, `permission_id` (many-to-many junction)
- **`user_roles`**: `user_id`, `role_id` (many-to-many junction)

### **Migration Patterns**
```sql
-- ✅ Clean role permissions before reassignment
DELETE FROM role_permissions WHERE role_id = ?;

-- ✅ Insert new permissions in batch
INSERT INTO role_permissions (role_id, permission_id) 
VALUES (?, ?), (?, ?), ...;
```

## **⚠️ Common Issues & Solutions**

### **Problem: "column roles.created_at does not exist"**
**Solution**: Use SQL direct queries instead of TypeORM relations

### **Problem: Role assignment returns 404**
**Solution**: Verify endpoint exists in routes: `POST /roles/assign-permissions`

### **Problem: Frontend shows "undefined" permissions**
**Solution**: Check ID type conversion (strings from API → numbers for operations)

### **Problem: Inconsistent role names (ADMIN vs ROLE_ADMIN)**
**Solution**: Standardize all roles to use `ROLE_` prefix and migrate user assignments

## **🔄 Role Migration Pattern**

### **When updating role structure:**
1. **Create new standardized role** (`ROLE_ADMIN`)
2. **Copy all permissions** from old role (`ADMIN`)
3. **Update user assignments** to use new role ID
4. **Verify functionality** before removing old role

```typescript
// ✅ Migration script pattern
async function migrateRoles() {
  // 1. Get permissions from old role
  const oldRolePermissions = await getPermissions(oldRoleId);
  
  // 2. Assign to new role
  await assignPermissionsToRole(newRoleId, oldRolePermissions);
  
  // 3. Update user assignments
  await updateUserRole(userId, newRoleId);
  
  // 4. Verify before cleanup
  const verification = await verifyUserPermissions(userId);
}
```

## **📋 Required Routes**

### **Backend routes that MUST exist:**
- `GET /api/systems/roles` - List roles with permissions
- `POST /api/systems/roles/assign-permissions` - Assign permissions to role
- `GET /api/systems/permissions` - List all available permissions
- `PUT /api/systems/users/:id` - Update user roles (roleIds array)

### **Permission Middleware Pattern:**
```typescript
// ✅ Route protection with specific permissions
router.post('/roles/assign-permissions', 
  authMiddleware, 
  permissionMiddleware(['sistemas:roles:update']), 
  roleController.assignPermissionsToRole
);
```

This ensures consistent, secure role and permission management across the entire application.
