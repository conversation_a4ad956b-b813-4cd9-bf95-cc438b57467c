---
description: 
globs: 
alwaysApply: true
---
## FileService central
- API: `upload(buffer, path, opts)`, `download(path)`, `delete(path)`.
- Drivers soportados: LocalFS (desarrollo), S3 compatible (prod).
- Ubicación: `backend/src/services/file.service.ts` (factory)

## Storybook
- Ejecutar `npm run storybook` en raíz.
- Todo componente en `frontend/components/ui` o `frontend/components/shared` debe tener su `.stories.tsx`.
- CI falla si `npm run build-storybook` produce warnings críticos.

## Playwright + Lighthouse en CI
- `npm run e2e` ejecuta Playwright tests.
- Workflow `.github/workflows/ci.yml` corre build, unit tests, luego `playwright test` y `lighthouse-ci autorun` sobre preview.

## Cronjobs
- Scripts en carpeta `/schedulers`.
- Se versionan junto con código.
- Jobs sensibles (backups) deben reportar estado vía notificación (Task 108).
