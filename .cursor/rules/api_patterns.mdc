---
description: 
globs: 
alwaysApply: true
---
# API Call Patterns & Service Layer Rules

## **🔄 CRITICAL: API Response Structure (MANDATORY)**

### **Standard API Response Format**
All API endpoints MUST return this consistent structure:

```typescript
// ✅ Single Entity Response
{
  "data": {
    "id": "111",
    "name": "User Name",
    "roles": [{"id": "78", "name": "ROLE_CALIDAD"}]
  },
  "message"?: "Optional success message"
}

// ✅ List/Paginated Response  
{
  "data": [
    {"id": "111", "name": "User 1"},
    {"id": "112", "name": "User 2"}
  ],
  "total": 150,
  "page": 1,
  "limit": 10,
  "totalPages": 15,
  "message"?: "Optional message"
}

// ✅ Error Response
{
  "message": "Descriptive error message",
  "error"?: "Additional error details"
}
```

### **Frontend Service Pattern (MANDATORY)**
```typescript
// ✅ Service class structure
export class EntityService {
  private baseUrl = '/api/module/entities'

  // GET single entity
  async getEntity(id: number): Promise<Entity> {
    const response = await apiClient.get<Entity>(`${this.baseUrl}/${id}`)
    if (!response.success) {
      throw new Error(response.message || 'Error fetching entity')
    }
    return response.data!  // data is guaranteed by apiClient
  }

  // GET paginated list
  async getEntities(filters: EntityFilters = {}): Promise<EntitiesResponse> {
    const params = new URLSearchParams()
    
    if (filters.search) params.append('search', filters.search)
    if (filters.status) params.append('status', filters.status)
    if (filters.page) params.append('page', filters.page.toString())
    if (filters.limit) params.append('limit', filters.limit.toString())

    const response = await apiClient.get<any>(`${this.baseUrl}?${params}`)
    if (!response.success) {
      throw new Error(response.message || 'Error fetching entities')
    }
    
    // Handle nested response structure
    const backendResponse = response.data!
    return {
      entities: backendResponse.data || backendResponse,
      total: backendResponse.total || 0,
      page: backendResponse.page || 1,
      limit: backendResponse.limit || 10,
      totalPages: backendResponse.totalPages || 1
    }
  }

  // POST create with ID conversion
  async createEntity(data: CreateEntityData): Promise<Entity> {
    // Convert roleIds to roles for backend
    const { roleIds, ...otherData } = data
    const requestData = {
      ...otherData,
      ...(roleIds && { roles: roleIds })  // Send numbers for operations
    }
    
    const response = await apiClient.post<Entity>(this.baseUrl, requestData)
    if (!response.success) {
      throw new Error(response.message || 'Error creating entity')
    }
    return response.data!
  }

  // PUT update with ID conversion
  async updateEntity(id: number, data: UpdateEntityData): Promise<Entity> {
    const { roleIds, ...otherData } = data
    const requestData = {
      ...otherData,
      ...(roleIds && { roles: roleIds })  // Send numbers for operations
    }
    
    const response = await apiClient.put<Entity>(`${this.baseUrl}/${id}`, requestData)
    if (!response.success) {
      throw new Error(response.message || 'Error updating entity')
    }
    return response.data!
  }

  // DELETE
  async deleteEntity(id: number): Promise<void> {
    const response = await apiClient.delete<void>(`${this.baseUrl}/${id}`)
    if (!response.success) {
      throw new Error(response.message || 'Error deleting entity')
    }
  }
}

// Export singleton instance
export const entityService = new EntityService()
```

## **🔧 React Query Integration Patterns**

### **Custom Hooks Pattern (MANDATORY)**
```typescript
// ✅ GET query hook
export function useEntities(filters: EntityFilters = {}) {
  return useQuery({
    queryKey: ['entities', filters],
    queryFn: () => entityService.getEntities(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// ✅ GET single entity hook
export function useEntity(id: number) {
  return useQuery({
    queryKey: ['entity', id],
    queryFn: () => entityService.getEntity(id),
    enabled: !!id,
  })
}

// ✅ CREATE mutation hook
export function useCreateEntity() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: entityService.createEntity,
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ['entities'] })
      toast.success('Entity created successfully')
    },
    onError: (error: Error) => {
      console.error('Create entity error:', error)
      toast.error(error.message || 'Failed to create entity')
    },
  })
}

// ✅ UPDATE mutation hook
export function useUpdateEntity() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateEntityData }) =>
      entityService.updateEntity(id, data),
    onSuccess: (updatedEntity) => {
      // Update specific entity in cache
      queryClient.setQueryData(['entity', updatedEntity.id], updatedEntity)
      // Invalidate list queries
      queryClient.invalidateQueries({ queryKey: ['entities'] })
      toast.success('Entity updated successfully')
    },
    onError: (error: Error) => {
      console.error('Update entity error:', error)
      toast.error(error.message || 'Failed to update entity')
    },
  })
}

// ✅ DELETE mutation hook
export function useDeleteEntity() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: entityService.deleteEntity,
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: ['entity', deletedId] })
      // Invalidate list queries
      queryClient.invalidateQueries({ queryKey: ['entities'] })
      toast.success('Entity deleted successfully')
    },
    onError: (error: Error) => {
      console.error('Delete entity error:', error)
      toast.error(error.message || 'Failed to delete entity')
    },
  })
}
```

## **🛡️ Error Handling Patterns**

### **Backend Error Handling (MANDATORY)**
```typescript
// ✅ Controller error handling pattern
export class EntityController {
  async createEntity(req: Request, res: Response): Promise<void> {
    try {
      const { roles, ...entityData } = req.body
      const newEntity = await this.entityService.createEntityWithIds(entityData, roles)
      
      await this.auditService.log({
        userId: req.user?.id,
        action: 'create_entity',
        targetEntity: 'entities',
        targetId: newEntity.id,
        details: newEntity,
        ipAddress: req.ip,
      })
      
      res.status(201).json({ data: newEntity })
    } catch (error: any) {
      console.error(`[EntityController.createEntity] Error:`, {
        error: error.message,
        stack: error.stack,
        body: req.body,
        userId: req.user?.id
      })
      
      // Handle specific error types
      if (error.message.includes('duplicate key')) {
        return res.status(409).json({ 
          message: 'Ya existe una entidad con esos datos' 
        })
      }
      
      if (error.message.includes('not found')) {
        return res.status(404).json({ 
          message: 'Algunos recursos seleccionados no existen' 
        })
      }
      
      if (error.message.includes('validation')) {
        return res.status(400).json({ 
          message: 'Datos de entrada inválidos' 
        })
      }
      
      res.status(500).json({ 
        message: 'Error interno del servidor' 
      })
    }
  }
}
```

### **Frontend Error Handling (MANDATORY)**
```typescript
// ✅ Form submission with error handling
const onSubmit = async (data: FormData) => {
  try {
    if (isEditing && entity) {
      await updateEntityMutation.mutateAsync({ id: entity.id, data })
    } else {
      await createEntityMutation.mutateAsync(data)
    }
    onClose()
  } catch (error: any) {
    console.error('[EntityForm] Submission error:', {
      error: error.message,
      data,
      isEditing,
      entityId: entity?.id
    })
    
    // Error already handled by mutation hooks with toast
    // Additional specific handling if needed
    if (error.message.includes('permisos')) {
      // Redirect to login or show specific permission error
    }
  }
}

// ✅ Loading states in components
if (isLoadingEntities) {
  return (
    <div className="flex items-center justify-center p-8">
      <Loader2 className="w-6 h-6 animate-spin" />
      <span className="ml-2">Cargando entidades...</span>
    </div>
  )
}

if (entitiesError) {
  return (
    <div className="flex flex-col items-center justify-center p-8">
      <AlertCircle className="w-8 h-8 text-red-500 mb-2" />
      <p className="text-red-600">Error al cargar entidades</p>
      <Button onClick={() => refetch()} className="mt-2">
        Reintentar
      </Button>
    </div>
  )
}
```

## **🔐 Authentication & Authorization Patterns**

### **Request Headers (MANDATORY)**
```typescript
// ✅ API client with automatic auth headers
export class ApiClient {
  private baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'
  
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('access_token')
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    }
  }
  
  async get<T>(url: string): Promise<ApiResponse<T>> {
    const response = await fetch(`${this.baseURL}${url}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    })
    
    if (response.status === 401) {
      // Handle token expiration
      localStorage.removeItem('access_token')
      window.location.href = '/login'
      throw new Error('Session expired')
    }
    
    const data = await response.json()
    return {
      success: response.ok,
      data: response.ok ? data.data : null,
      message: data.message || null
    }
  }
}
```

### **Backend Auth Middleware Usage**
```typescript
// ✅ Route protection pattern
import { createAuthMiddleware } from '../middlewares/auth.middleware'

export function createEntityRoutes(dataSource: DataSource): Router {
  const router = Router()
  const authMiddleware = createAuthMiddleware(dataSource)
  const controller = new EntityController(dataSource)
  
  // Apply auth to all routes
  router.use(authMiddleware)
  
  // CRUD operations
  router.get('/', controller.getEntities.bind(controller))
  router.get('/:id', controller.getEntity.bind(controller))
  router.post('/', controller.createEntity.bind(controller))
  router.put('/:id', controller.updateEntity.bind(controller))
  router.delete('/:id', controller.deleteEntity.bind(controller))
  
  return router
}
```

## **📋 Validation Patterns**

### **Backend Validation (MANDATORY)**
```typescript
// ✅ Service-level validation before operations
async createEntityWithIds(entityData: Partial<Entity>, roleIds: number[]): Promise<Entity> {
  const repository = this.dataSource.getRepository(Entity)
  
  // Validate required fields
  if (!entityData.name || entityData.name.trim().length < 2) {
    throw new Error('Name is required and must be at least 2 characters')
  }
  
  // Validate role IDs exist
  if (roleIds?.length) {
    const roleCheckQuery = `SELECT id, name FROM roles WHERE id = ANY($1)`
    const existingRoles = await repository.query(roleCheckQuery, [roleIds])
    
    if (existingRoles.length !== roleIds.length) {
      const missingIds = roleIds.filter(id => 
        !existingRoles.some(role => parseInt(role.id) === id)
      )
      throw new Error(`Roles not found: ${missingIds.join(', ')}`)
    }
  }
  
  // Validate business rules
  if (entityData.status && !['active', 'inactive'].includes(entityData.status)) {
    throw new Error('Status must be active or inactive')
  }
  
  // Continue with creation...
}
```

### **Frontend Validation (MANDATORY)**
```typescript
// ✅ Zod schema with proper types
const entitySchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email format'),
  status: z.enum(['active', 'inactive']),
  roleIds: z.array(z.number()).min(1, 'Select at least one role'),
}).refine((data) => {
  // Custom validation logic
  if (data.status === 'active' && data.roleIds.length === 0) {
    return false
  }
  return true
}, {
  message: 'Active entities must have at least one role',
  path: ['roleIds'],
})
```

## **🔄 Cache Management Patterns**

### **React Query Cache Strategies**
```typescript
// ✅ Cache invalidation patterns
export function useEntityActions() {
  const queryClient = useQueryClient()
  
  const invalidateEntityQueries = useCallback(() => {
    // Invalidate all entity-related queries
    queryClient.invalidateQueries({ queryKey: ['entities'] })
    queryClient.invalidateQueries({ queryKey: ['entity'] })
  }, [queryClient])
  
  const updateEntityInCache = useCallback((updatedEntity: Entity) => {
    // Update specific entity in cache
    queryClient.setQueryData(['entity', updatedEntity.id], updatedEntity)
    
    // Update entity in list caches
    queryClient.setQueriesData(
      { queryKey: ['entities'] },
      (oldData: EntitiesResponse | undefined) => {
        if (!oldData) return oldData
        
        return {
          ...oldData,
          entities: oldData.entities.map(entity =>
            entity.id === updatedEntity.id ? updatedEntity : entity
          )
        }
      }
    )
  }, [queryClient])
  
  return { invalidateEntityQueries, updateEntityInCache }
}
```

## **📊 Performance Patterns**

### **Pagination & Filtering**
```typescript
// ✅ Efficient pagination with React Query
export function useEntitiesWithPagination(filters: EntityFilters) {
  return useQuery({
    queryKey: ['entities', filters],
    queryFn: () => entityService.getEntities(filters),
    keepPreviousData: true,  // Keep previous data while loading new page
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

// ✅ Debounced search
export function useEntitySearch() {
  const [search, setSearch] = useState('')
  const [debouncedSearch, setDebouncedSearch] = useState('')
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search)
    }, 300)
    
    return () => clearTimeout(timer)
  }, [search])
  
  const { data, isLoading } = useEntitiesWithPagination({ 
    search: debouncedSearch 
  })
  
  return { search, setSearch, data, isLoading }
}
```

These patterns ensure consistent, reliable, and maintainable API interactions across the entire application.


