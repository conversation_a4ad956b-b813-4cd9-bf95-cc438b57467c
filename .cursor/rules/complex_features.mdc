---
description: 
globs: 
alwaysApply: true
---
- **Escaneo QR en Navegador / Dispositivos Móviles**
  - Usar librería OSS `html5-qrcode` o `zxing-js/browser`.
  - Para flujos intensivos se evaluará **Expo React Native** + `expo-barcode-scanner`.
  - Evitar dependencias comerciales como Dynamsoft o Scandit.

- **Generación masiva de PDF (Cotizaciones, Hojas de Servicio, Vales)**
  - Evitar jsPDF en frontend para documentos >5 MB.
  - Implementar micro-servicio Node con **puppeteer** (Chromium headless) renderizando plantilla HTML.
  - Para DOCX→PDF usar **LibreOffice headless** vía contenedor.

- **Validación RFC SAT**
  - El webservice oficial requiere certificado y costo.
  - Se usará primero regex + consulta diferida mensual vía job.
  - Alternativa freemium: `https://api.rfc-sat.com`.

- **Integración Contpaq / Outlook**
  - Plan A: exportación CSV + PowerAutomate / Zapier.
  - Plan B: APIs oficiales (COM+ / Graph) con throttling ⇒ Require Azure registro.

- **Backups Automáticos**
  - Cron en contenedor `backup` ejecuta `pg_dump` diario, compresión zstd y subida a **Backblaze B2** (low cost).
  - Script restore documentado.

- **Motor de Notificaciones**
  - WebSocket (socket.io) + fallback REST polling.
  - Para e-mail usar **nodemailer** con SMTP corporativo.

- **RBAC Naming Convention**
  - `module:resource:action` (ej. `almacen:stock:create`).
  - Generar enum automáticamente desde archivo YAML fuente usando script.

- **Cron-Jobs / Schedulers**
  - Colocar en `backend/src/schedulers/` usando `node-cron` o `@nestjs/schedule`.
  - Registrar backups, expiración folios, recordatorios de notificaciones.

- **FileService Central**
  - Soporta almacenamiento local y S3 compatible.
  - Se usa en TODOS los módulos para carga/descarga de archivos.

# Complex Features Implementation Patterns

## **🔄 CRITICAL: ID Type Management Across Full Stack**

### **Universal ID Conversion Pattern (MANDATORY)**
This pattern MUST be applied to ALL forms that handle IDs: users, roles, permissions, products, clients, etc.

```typescript
// ✅ TEMPLATE: Form with ID conversion (copy this pattern)
export function EntityFormModal({ entity, open, onClose }: Props) {
  const form = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      entityIds: [], // Always array of numbers in form
    },
  });

  // ✅ CRITICAL: Convert API string IDs to numbers on load
  useEffect(() => {
    if (isEditing && entity) {
      form.reset({
        entityIds: entity.relatedEntities?.map(item => 
          parseInt(item.id.toString(), 10)
        ) || [],
      });
    }
  }, [entity, isEditing, form]);

  // ✅ CRITICAL: Convert for checkbox/select rendering
  {entities.map((entity: any) => {
    const entityIdNum = parseInt(entity.id.toString(), 10);
    return (
      <Checkbox
        key={entity.id}
        checked={field.value.includes(entityIdNum)}
        onCheckedChange={() => handleToggle(entityIdNum)}
      />
    );
  })}

  // ✅ CRITICAL: Send numbers to backend
  const onSubmit = async (data: FormData) => {
    const requestData = {
      ...data,
      relatedEntityIds: data.entityIds  // Array of numbers [78, 31]
    };
    await apiService.createEntity(requestData);
  };
}
```

### **Backend Service Dual Method Pattern**
Always create both string and number ID methods for backward compatibility:

```typescript
// ✅ TEMPLATE: Service with dual ID methods
export class EntityService {
  // Legacy method (for backwards compatibility)
  async createEntity(data: EntityData, relatedNames: string[]): Promise<Entity> {
    const nameRepository = this.dataSource.getRepository(RelatedEntity);
    const entities = await nameRepository.find({
      where: relatedNames.map(name => ({ name })),
    });
    // ... implementation
  }

  // ✅ PREFERRED: New method with number IDs
  async createEntityWithIds(data: EntityData, relatedIds: number[]): Promise<Entity> {
    // Validate existence with SQL
    const checkQuery = `SELECT id, name FROM related_entities WHERE id = ANY($1)`;
    const existingEntities = await repository.query(checkQuery, [relatedIds]);
    
    if (existingEntities.length !== relatedIds.length) {
      throw new Error('One or more related entities not found');
    }

    // Create main entity
    const newEntity = repository.create({ ...data, relatedEntities: [] });
    const savedEntity = await repository.save(newEntity);

    // Link relationships with SQL
    for (const relatedId of relatedIds) {
      await repository.query(
        'INSERT INTO entity_relations (entity_id, related_id) VALUES ($1, $2)',
        [savedEntity.id, relatedId]
      );
    }

    return this.findEntityById(savedEntity.id) as Promise<Entity>;
  }

  // ✅ Use SQL queries for reliable data fetching
  async findEntityById(id: number): Promise<Entity | null> {
    const entityQuery = `
      SELECT id, name, email, status, created_at, updated_at
      FROM entities WHERE id = $1
    `;
    const entityResult = await repository.query(entityQuery, [id]);
    
    if (!entityResult || entityResult.length === 0) return null;
    
    const entity = entityResult[0];
    
    // Get related entities with SQL
    const relatedQuery = `
      SELECT r.id, r.name
      FROM entity_relations er
      JOIN related_entities r ON er.related_id = r.id
      WHERE er.entity_id = $1
    `;
    const relatedResult = await repository.query(relatedQuery, [id]);
    entity.relatedEntities = relatedResult;
    
    return entity;
  }
}
```

## **Multi-Entity Form Patterns**

### **Complex Forms with Multiple ID Arrays**
For forms managing multiple entity types (e.g., user with roles AND departments):

```typescript
// ✅ Schema with multiple ID arrays
const schema = z.object({
  name: z.string().min(2),
  roleIds: z.array(z.number()).min(1, 'Select at least one role'),
  departmentIds: z.array(z.number()).min(1, 'Select at least one department'),
  projectIds: z.array(z.number()).optional(),
});

// ✅ Form initialization with multiple conversions
useEffect(() => {
  if (isEditing && user) {
    form.reset({
      name: user.name,
      roleIds: user.roles?.map(role => parseInt(role.id.toString(), 10)) || [],
      departmentIds: user.departments?.map(dept => parseInt(dept.id.toString(), 10)) || [],
      projectIds: user.projects?.map(proj => parseInt(proj.id.toString(), 10)) || [],
    });
  }
}, [user, isEditing, form]);

// ✅ Multiple toggle handlers with proper types
const handleRoleToggle = (roleId: number) => {
  const currentRoles = form.getValues('roleIds');
  form.setValue('roleIds', 
    currentRoles.includes(roleId) 
      ? currentRoles.filter(id => id !== roleId)
      : [...currentRoles, roleId]
  );
};

const handleDepartmentToggle = (deptId: number) => {
  const currentDepts = form.getValues('departmentIds');
  form.setValue('departmentIds',
    currentDepts.includes(deptId)
      ? currentDepts.filter(id => id !== deptId) 
      : [...currentDepts, deptId]
  );
};

// ✅ Backend payload with proper ID arrays
const onSubmit = async (data: FormData) => {
  const payload = {
    name: data.name,
    roles: data.roleIds,        // [78, 31]
    departments: data.departmentIds, // [5, 12]
    projects: data.projectIds,  // [22, 45, 67]
  };
  await userService.createUser(payload);
};
```

### **Nested Entity Loading Pattern**
For entities with deep relationships (user → roles → permissions):

```typescript
// ✅ Service method with nested loading
async getUserWithFullDetails(id: number): Promise<UserWithDetails> {
  const userQuery = `
    SELECT id, name, email, status, area
    FROM users WHERE id = $1
  `;
  const userResult = await repository.query(userQuery, [id]);
  if (!userResult.length) return null;
  
  const user = userResult[0];
  
  // Get roles
  const rolesQuery = `
    SELECT r.id, r.name, r.description
    FROM user_roles ur
    JOIN roles r ON ur.role_id = r.id
    WHERE ur.user_id = $1
  `;
  const roles = await repository.query(rolesQuery, [id]);
  
  // Get permissions for each role
  for (const role of roles) {
    const permissionsQuery = `
      SELECT p.id, p.name, p.module, p.action
      FROM role_permissions rp
      JOIN permissions p ON rp.permission_id = p.id
      WHERE rp.role_id = $1
    `;
    role.permissions = await repository.query(permissionsQuery, [role.id]);
  }
  
  user.roles = roles;
  return user;
}
```

## **RBAC Implementation Patterns**

### **Permission-Based Component Guard**
```typescript
// ✅ Component guard with proper permission checking
interface PermissionGuardProps {
  permission: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function PermissionGuard({ permission, children, fallback = null }: PermissionGuardProps) {
  const { user } = useAuth();
  
  const hasPermission = useMemo(() => {
    if (!user?.roles) return false;
    
    // Check if user has ADMIN role (full access)
    if (user.roles.some(role => role.name === 'ADMIN')) return true;
    
    // Check specific permission using module:resource:action pattern
    return user.roles.some(role =>
      role.permissions?.some(perm => 
        perm.name === permission || 
        perm.name === `${permission.split(':')[0]}:*` // Wildcard support
      )
    );
  }, [user, permission]);
  
  return hasPermission ? <>{children}</> : <>{fallback}</>;
}

// ✅ Usage with proper permission names
<PermissionGuard permission="sistemas:users:create">
  <Button onClick={openCreateModal}>Create User</Button>
</PermissionGuard>

<PermissionGuard permission="almacen:stock:read">
  <StockTable />
</PermissionGuard>
```

### **Page-Level Permission Check**
```typescript
// ✅ Page guard with redirect
export function usePagePermissions(requiredPermission: string) {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  
  useEffect(() => {
    if (isLoading) return;
    
    if (!user) {
      router.push('/login');
      return;
    }
    
    const hasPermission = user.roles?.some(role =>
      role.name === 'ADMIN' || 
      role.permissions?.some(perm => 
        perm.name === requiredPermission ||
        perm.name === `${requiredPermission.split(':')[0]}:*`
      )
    );
    
    if (!hasPermission) {
      router.push('/dashboard');  // Redirect to allowed page
      toast.error('No tienes permisos para acceder a esta página');
    }
  }, [user, isLoading, requiredPermission, router]);
  
  return { hasPermission: user?.roles?.some(/* same logic */) };
}

// ✅ Usage in page components
export default function UsersPage() {
  const { hasPermission } = usePagePermissions('sistemas:users:read');
  
  if (!hasPermission) {
    return <div>Cargando...</div>;
  }
  
  return <UsersManagement />;
}
```

## **Error Handling Patterns**

### **Form Validation with ID Conversion Errors**
```typescript
// ✅ Validate ID conversions with proper error handling
const validateAndConvertIds = (entities: any[], fieldName: string): number[] => {
  if (!Array.isArray(entities)) {
    throw new Error(`${fieldName} must be an array`);
  }
  
  return entities.map((entity, index) => {
    const id = parseInt(entity.id?.toString(), 10);
    if (isNaN(id)) {
      throw new Error(`Invalid ${fieldName} ID at index ${index}: ${entity.id}`);
    }
    return id;
  });
};

// ✅ Use in form initialization with error boundary
try {
  const roleIds = validateAndConvertIds(user.roles || [], 'role');
  const deptIds = validateAndConvertIds(user.departments || [], 'department');
  
  form.reset({ roleIds, departmentIds: deptIds });
} catch (error) {
  console.error('Error converting entity IDs:', error);
  toast.error('Error loading form data');
}
```

### **API Error Handling with Type Safety**
```typescript
// ✅ Service with proper error handling
async createUserWithValidation(userData: CreateUserData): Promise<User> {
  try {
    // Validate role IDs exist before creation
    if (userData.roleIds?.length) {
      const roleCheckQuery = `SELECT id FROM roles WHERE id = ANY($1)`;
      const existingRoles = await repository.query(roleCheckQuery, [userData.roleIds]);
      
      if (existingRoles.length !== userData.roleIds.length) {
        const missingIds = userData.roleIds.filter(id => 
          !existingRoles.some(role => parseInt(role.id) === id)
        );
        throw new Error(`Roles not found: ${missingIds.join(', ')}`);
      }
    }
    
    return await this.createUserWithRoleIds(userData, userData.roleIds || []);
  } catch (error: any) {
    // Log specific error for debugging
    console.error('User creation error:', {
      userData,
      error: error.message,
      stack: error.stack
    });
    
    // Re-throw with user-friendly message
    if (error.message.includes('duplicate key')) {
      throw new Error('Ya existe un usuario con ese email');
    }
    
    if (error.message.includes('not found')) {
      throw new Error('Algunos roles seleccionados no existen');
    }
    
    throw new Error('Error al crear usuario. Intenta de nuevo.');
  }
}
```

This pattern ensures reliable, type-safe handling of complex multi-entity forms and prevents the ID conversion bugs we encountered.
