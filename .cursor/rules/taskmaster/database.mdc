---
description: 
globs: 
alwaysApply: true
---
# Base de Datos COMINTEC

## Estructura de Archivos SQL

La base de datos se mantiene a través de tres archivos SQL principales:

1. `comintec_schema_core.sql` - ESTRUCTURA BASE
   - Definiciones de tablas
   - Índices
   - Restricciones
   - Tipos y enums
   - Secuencias

2. `permissions_data.sql` - PERMISOS Y ROLES
   - Datos de permisos
   - Datos de roles
   - Relaciones entre roles y permisos

3. `users_data.sql` - USUARIOS
   - Datos de usuarios iniciales
   - Configuraciones de usuario

## ⚠️ REGLA CRÍTICA: MANTENIMIENTO DE ARCHIVOS SQL

**CUALQUIER CAMBIO EN LA BASE DE DATOS DEBE REFLEJARSE EN ESTOS ARCHIVOS:**

| Tipo de Cambio | Archivo a Actualizar |
|----------------|---------------------|
| Estructural (tablas, columnas, índices) | `comintec_schema_core.sql` |
| Permisos o roles | `permissions_data.sql` |
| Usuarios | `users_data.sql` |

### ¿Por qué es importante?

- Permite recrear la BD en cualquier momento
- Mantiene historial de cambios en Git
- Evita pérdida de información en backups
- Facilita despliegue en diferentes ambientes
- Asegura consistencia entre entornos

### Proceso de Actualización

1. NUNCA hacer cambios directamente en la BD sin actualizar estos archivos
2. Documentar cada cambio en el archivo correspondiente
3. Mantener el formato SQL consistente
4. Actualizar las secuencias si es necesario
5. Hacer commit de los cambios en Git

## Orden de Ejecución

Los archivos deben ejecutarse en este orden:

1. `comintec_schema_core.sql` - PRIMERO (estructura base)
2. `permissions_data.sql` o `users_data.sql` - SEGUNDO/TERCERO (intercambiables)

## Notas Importantes

- Mantener respaldos antes de cualquier cambio
- Probar los scripts en ambiente de desarrollo primero
- Documentar cualquier dependencia entre cambios
- Seguir las convenciones de nombrado existentes 