---
description: 
globs: 
alwaysApply: true
---
# Reglas y Comandos Frontend

## Comandos principales
- `npm run dev` — Desarrollo
- `npm run build` — Compilar
- `npm start` — Producción
- `npm run test` — Tests

## Convenciones
- Código en `app/` y `components/`
- Usa Next.js y TypeScript
- Usa TanStack React Query, React Hook Form, Zod, Radix UI, Tailwind CSS
- Sigue las reglas de linting y formateo
