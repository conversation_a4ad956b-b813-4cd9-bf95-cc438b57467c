# Schedulers

Todos los cron-jobs o programadores recurrentes del proyecto deben declararse aquí.

Convención:
- Cada script se nombra `YYYYMMDD-nombre-tarea.sh` o `.ts` si es Node.
- Los timers systemd o crontabs hacen referencia a rutas dentro de este directorio.
- Ejemplos:
  - `20250701-backup-monthly.sh` → llama a `scripts/backup-monthly.sh`
  - `20250715-cleanup-temp.sh` → ejecuta `npm run cleanup -- --force`

Registrar nuevos jobs en el documento `.cursor/rules/cronjobs.mdc` (por crear). 