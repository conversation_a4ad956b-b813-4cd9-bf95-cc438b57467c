name: CI Pipeline

on:
  push:
    branches: [ main, develop, feat/* ]
  pull_request:
    branches: [ main, develop ]

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: testpass
          POSTGRES_DB: comintec_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json
    
    - name: Install backend dependencies
      working-directory: ./backend
      run: npm ci
    
    - name: Run backend linting
      working-directory: ./backend
      run: npm run lint
    
    - name: Run backend tests
      working-directory: ./backend
      env:
        NODE_ENV: test
        DATABASE_URL: postgresql://postgres:testpass@localhost:5432/comintec_test
      run: npm test
    
    - name: Generate coverage report
      working-directory: ./backend
      run: npm run test:cov
    
    - name: Upload backend coverage
      uses: actions/upload-artifact@v3
      with:
        name: backend-coverage
        path: backend/coverage

  frontend-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install frontend dependencies
      working-directory: ./frontend
      run: npm ci
    
    - name: Run frontend linting
      working-directory: ./frontend
      run: npm run lint
    
    - name: Build frontend
      working-directory: ./frontend
      run: npm run build
    
    - name: Run Storybook build
      working-directory: ./frontend
      run: npm run build-storybook
    
    - name: Upload Storybook build
      uses: actions/upload-artifact@v3
      with:
        name: storybook-static
        path: frontend/storybook-static

  e2e-tests:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
    
    - name: Install Playwright browsers
      run: npx playwright install --with-deps chromium
    
    - name: Install dependencies
      run: |
        cd backend && npm ci
        cd ../frontend && npm ci
    
    - name: Start services
      run: |
        cd backend && npm run build && npm start &
        cd frontend && npm run build && npm start &
        npx wait-on http://localhost:3000/api/health http://localhost:3001 -t 60000
    
    - name: Run Playwright tests
      run: npx playwright test
    
    - name: Upload Playwright report
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: playwright-report
        path: playwright-report

  lighthouse:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
    
    - name: Install dependencies
      run: |
        cd backend && npm ci
        cd ../frontend && npm ci
        npm install -g @lhci/cli
    
    - name: Build and start services
      run: |
        cd backend && npm run build && npm start &
        cd frontend && npm run build && npm start &
        npx wait-on http://localhost:3000/api/health http://localhost:3001 -t 60000
    
    - name: Run Lighthouse CI
      run: |
        lhci autorun --config=.lighthouserc.js
    
    - name: Upload Lighthouse reports
      uses: actions/upload-artifact@v3
      with:
        name: lighthouse-report
        path: .lighthouseci 