
import { test, expect } from '@playwright/test';

test.describe('Si<PERSON>mas Module - Full E2E Test', () => {
  // Login as a systems user before each test
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.waitForSelector('#credential', { timeout: 10000 });
    await page.fill('#credential', '<EMAIL>');
    await page.fill('#password', 'password');
    await Promise.all([
      page.waitForResponse(response => response.url().includes('/api/auth/login')),
      page.click('button[type="submit"]')
    ]);
    await expect(page).toHaveURL('/dashboard', { timeout: 10000 });
  });

  test.describe('Submodule Navigation', () => {
    const navigationTimeout = { timeout: 10000 };

    test('should navigate to Usuarios and verify title', async ({ page }) => {
      await page.goto('/dashboard/sistemas/usuarios');
      await expect(page.getByRole('heading', { name: 'Gestión de Usuarios' })).toBeVisible(navigationTimeout);
    });

    test('should navigate to Roles and verify title', async ({ page }) => {
      await page.goto('/dashboard/sistemas/roles');
      await expect(page.getByRole('heading', { name: 'Roles y Permisos' })).toBeVisible(navigationTimeout);
    });

    test('should navigate to Backup and verify title', async ({ page }) => {
      await page.goto('/dashboard/sistemas/backup');
      await expect(page.getByRole('heading', { name: 'Backup y Restauración' })).toBeVisible(navigationTimeout);
    });

    test('should navigate to Auditoria and verify title', async ({ page }) => {
      await page.goto('/dashboard/sistemas/auditoria');
      await expect(page.getByRole('heading', { name: 'Auditoría' })).toBeVisible(navigationTimeout);
    });
  });

  test.describe('Users Management', () => {
    test('should create, update, and delete a user', async ({ page }) => {
      await page.goto('/dashboard/sistemas/usuarios');
      
      // --- CREATE A NEW USER ---
      await page.getByRole('button', { name: 'Crear Usuario' }).click();
      
      // Fill out the form
      await page.getByLabel('Nombre *').fill('Test User');
      await page.getByLabel('Email *').fill('<EMAIL>');
      await page.getByLabel('Contraseña *').fill('aSecurePassword123!');
      await page.getByLabel('Confirmar Contraseña *').fill('aSecurePassword123!');
      
      // Handle the custom combobox for 'Área'
      await page.getByRole('combobox', { name: 'Área' }).click();
      await page.getByRole('option', { name: 'Sistemas' }).click();
      
      // Select a role
      await page.getByLabel('ROLE_SISTEMAS').check();
      
      // Submit the form
      await page.getByRole('button', { name: 'Crear Usuario' }).click();
      
      // Verify that the user was created successfully
      await expect(page.getByText('Usuario creado con éxito')).toBeVisible({ timeout: 10000 });
      const userRow = page.getByRole('row', { name: /Test User/i });
      await expect(userRow).toBeVisible();

      // --- UPDATE THE USER ---
      // Open the actions menu for the new user
      await userRow.getByRole('button').last().click();
      await page.getByRole('menuitem', { name: /editar/i }).click();
      
      // Update the user's name
      await page.getByLabel('Nombre *').fill('Test User Updated');
      await page.getByRole('button', { name: 'Guardar Cambios' }).click();
      await expect(page.getByText('Usuario actualizado con éxito')).toBeVisible();

      // Verify that the user's name was updated in the table
      const updatedUserRow = page.getByRole('row', { name: /Test User Updated/i });
      await expect(updatedUserRow).toBeVisible();

      // --- DELETE THE USER ---
      // Open the actions menu for the updated user
      await updatedUserRow.getByRole('button').last().click();
      await page.getByRole('menuitem', { name: /eliminar/i }).click();
      
      // Confirm the deletion
      await expect(page.getByRole('heading', { name: '¿Estás seguro?' })).toBeVisible();
      await page.getByRole('button', { name: 'Eliminar' }).click();
      
      // Verify that the user was deleted
      await expect(page.getByText('Usuario eliminado con éxito')).toBeVisible();
      await expect(updatedUserRow).not.toBeVisible();
    });
  });

  test.describe('Roles Management', () => {
    test('should create, read, update, and delete a role', async ({ page }) => {
      await page.goto('/dashboard/sistemas/roles');

      // Create a new role
      await page.click('button:has-text("Nuevo Rol")');
      await page.fill('[name="name"]', 'ROLE_E2E_TEST');
      await page.fill('[name="description"]', 'Role for E2E testing');
      await page.click('text=sistemas:users:read');
      await page.click('button[type="submit"]');
      await expect(page.locator('text=Rol creado')).toBeVisible();
      await expect(page.locator('text=ROLE_E2E_TEST')).toBeVisible();

      // Read the role
      await page.click('button[aria-label="Ver permisos"]');
      await expect(page.locator('text=Permisos del Rol')).toBeVisible();
      await expect(page.locator('text=sistemas:users:read')).toBeVisible();
      await page.click('button:has-text("Cerrar")');

      // Update the role
      await page.click('button[aria-label="Editar permisos"]');
      await page.click('text=sistemas:users:create');
      await page.click('button[type="submit"]');
      await expect(page.locator('text=Permisos actualizados')).toBeVisible();

      // Delete the role
      await page.click('button[aria-label="Eliminar rol"]');
      await expect(page.locator('text=¿Estás seguro?')).toBeVisible();
      await page.click('button:has-text("Eliminar")');
      await expect(page.locator('text=Rol eliminado')).toBeVisible();
      await expect(page.locator('text=ROLE_E2E_TEST')).not.toBeVisible();
    });
  });

  test.describe('Backup Management', () => {
    test('should create and download a backup', async ({ page }) => {
      await page.goto('/dashboard/sistemas/backup');
      await expect(page.locator('table')).toBeVisible();
      
      await page.click('button:has-text("Nuevo Backup")');
      await expect(page.locator('text=Backup iniciado')).toBeVisible({ timeout: 15000 });
      await expect(page.locator('text=Backup completo').first()).toBeVisible({ timeout: 30000 });

      const [ download ] = await Promise.all([
        page.waitForEvent('download'),
        page.click('button[aria-label="Descargar backup"]:first-of-type')
      ]);
      expect(download.suggestedFilename()).toContain('.sql');
    });
  });

  test.describe('Audit Log', () => {
    test('should filter and export audit logs', async ({ page }) => {
      await page.goto('/dashboard/sistemas/auditoria');
      await expect(page.locator('table')).toBeVisible();

      await page.fill('[name="startDate"]', '2025-01-01');
      await page.fill('[name="endDate"]', '2025-12-31');
      await page.click('button:has-text("Filtrar")');
      
      // We can't guarantee the number of rows, but we can check that the table is still there
      await expect(page.locator('table')).toBeVisible();

      const [ download ] = await Promise.all([
        page.waitForEvent('download'),
        page.click('button:has-text("Exportar")')
      ]);
      expect(download.suggestedFilename()).toContain('.csv');
    });
  });
  
  
});
