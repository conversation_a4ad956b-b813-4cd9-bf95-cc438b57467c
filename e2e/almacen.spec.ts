import { test, expect } from '@playwright/test';

test.describe('<PERSON>cé<PERSON>', () => {
  // Login as almacén user before each test
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.waitForSelector('#credential', { timeout: 10000 });
    await page.fill('#credential', '<EMAIL>');
    await page.fill('#password', 'password');
    await Promise.all([
      page.waitForResponse(response => response.url().includes('/api/auth/login')),
      page.click('button[type="submit"]')
    ]);
    await expect(page).toHaveURL('/dashboard', { timeout: 10000 });
  });

  test('should show almacén dashboard', async ({ page }) => {
    await page.goto('/dashboard/almacen');
    await expect(page.locator('h1')).toContainText('Almacén');
    await expect(page.locator('[data-testid="almacen-stats"]')).toBeVisible();
  });

  test('should show stock actual page', async ({ page }) => {
    await page.goto('/dashboard/almacen/stock');
    await expect(page.locator('h1')).toContainText('Stock Actual');
  });

  test('should show entradas page', async ({ page }) => {
    await page.goto('/dashboard/almacen/entradas');
    await expect(page.locator('h1')).toContainText('Entradas de Stock');
  });

  test('should show salidas page', async ({ page }) => {
    await page.goto('/dashboard/almacen/salidas');
    await expect(page.locator('h1')).toContainText('Vale de Salida de Stock');
  });

  test('should show reporte page', async ({ page }) => {
    await page.goto('/dashboard/almacen/reporte');
    await expect(page.locator('h1')).toContainText('Reporte de Inventario');
  });

  test('should create new product', async ({ page }) => {
    await page.goto('/dashboard/almacen/stock');
    
    // Click new product button
    await page.click('button:has-text("Nuevo Producto")');
    
    // Fill product form
    await page.fill('[name="name"]', 'Producto de Prueba');
    await page.fill('[name="description"]', 'Descripción del producto de prueba');
    await page.fill('[name="sku"]', 'TEST-001');
    await page.fill('[name="minStock"]', '10');
    await page.fill('[name="maxStock"]', '100');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Should show success message
    await expect(page.locator('text=Producto creado')).toBeVisible();
    
    // Should see new product in table
    await expect(page.locator('text=Producto de Prueba')).toBeVisible();
  });

  test('should edit existing product', async ({ page }) => {
    await page.goto('/dashboard/almacen/stock');
    
    // Click edit button on first product
    await page.click('button[aria-label="Editar producto"]');
    
    // Update product name
    await page.fill('[name="name"]', 'Producto Actualizado');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Should show success message
    await expect(page.locator('text=Producto actualizado')).toBeVisible();
    
    // Should see updated product name
    await expect(page.locator('text=Producto Actualizado')).toBeVisible();
  });

  test('should delete product', async ({ page }) => {
    await page.goto('/dashboard/almacen/stock');
    
    // Get initial product count
    const initialCount = await page.locator('tr').count();
    
    // Click delete button on first product
    await page.click('button[aria-label="Eliminar producto"]');
    
    // Confirm deletion
    await page.click('button:has-text("Eliminar")');
    
    // Should show success message
    await expect(page.locator('text=Producto eliminado')).toBeVisible();
    
    // Should have one less product
    await expect(page.locator('tr')).toHaveCount(initialCount - 1);
  });

  test('should register product entry', async ({ page }) => {
    await page.goto('/dashboard/almacen/entradas');
    
    // Click new entry button
    await page.click('button:has-text("Nueva Entrada")');
    
    // Fill entry form
    await page.selectOption('select[name="productId"]', { label: 'Producto de Prueba' });
    await page.fill('[name="quantity"]', '50');
    await page.fill('[name="notes"]', 'Entrada de prueba');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Should show success message
    await expect(page.locator('text=Entrada registrada')).toBeVisible();
    
    // Should see new entry in table
    await expect(page.locator('text=Producto de Prueba')).toBeVisible();
    await expect(page.locator('text=50')).toBeVisible();
  });

  test('should register product exit', async ({ page }) => {
    await page.goto('/dashboard/almacen/salidas');
    
    // Click new exit button
    await page.click('button:has-text("Nueva Salida")');
    
    // Fill exit form
    await page.selectOption('select[name="productId"]', { label: 'Producto de Prueba' });
    await page.fill('[name="quantity"]', '20');
    await page.fill('[name="notes"]', 'Salida de prueba');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Should show success message
    await expect(page.locator('text=Salida registrada')).toBeVisible();
    
    // Should see new exit in table
    await expect(page.locator('text=Producto de Prueba')).toBeVisible();
    await expect(page.locator('text=20')).toBeVisible();
  });

  test('should show inventory report', async ({ page }) => {
    await page.goto('/dashboard/almacen/reporte');
    
    // Should see report filters
    await expect(page.locator('[data-testid="report-filters"]')).toBeVisible();
    
    // Should see report table
    await expect(page.locator('table')).toBeVisible();
    
    // Should see product movements
    await expect(page.locator('text=Producto de Prueba')).toBeVisible();
    await expect(page.locator('text=Entrada')).toBeVisible();
    await expect(page.locator('text=Salida')).toBeVisible();
  });
}); 