import { test, expect } from '@playwright/test';

test.describe('Calidad Module', () => {
  // <PERSON><PERSON> as calidad user before each test
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.fill('#credential', '<EMAIL>');
    await page.fill('#password', 'password');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('should show calidad dashboard', async ({ page }) => {
    await page.goto('/dashboard/calidad');
    await expect(page.locator('h1')).toContainText('Calidad');
    await expect(page.locator('[data-testid="calidad-stats"]')).toBeVisible();
  });

  test('should create new incidencia', async ({ page }) => {
    await page.goto('/dashboard/calidad/incidencias');
    
    // Click new incidencia button
    await page.click('button:has-text("Nueva Incidencia")');
    
    // Fill incidencia form
    await page.fill('[name="title"]', 'Incidencia de Prueba');
    await page.fill('[name="description"]', 'Descripción de la incidencia de prueba');
    await page.selectOption('select[name="priority"]', 'alta');
    await page.selectOption('select[name="area"]', 'Producción');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Should show success message
    await expect(page.locator('text=Incidencia creada')).toBeVisible();
    
    // Should see new incidencia in table
    await expect(page.locator('text=Incidencia de Prueba')).toBeVisible();
  });

  test('should update incidencia status', async ({ page }) => {
    await page.goto('/dashboard/calidad/incidencias');
    
    // Click status button on first incidencia
    await page.click('button[aria-label="Cambiar estado"]');
    
    // Select new status
    await page.click('button:has-text("En Proceso")');
    
    // Should show success message
    await expect(page.locator('text=Estado actualizado')).toBeVisible();
    
    // Should see updated status
    await expect(page.locator('text=En Proceso')).toBeVisible();
  });

  test('should show procedures list', async ({ page }) => {
    await page.goto('/dashboard/calidad/procedimientos');
    
    // Should see procedures table
    await expect(page.locator('table')).toBeVisible();
    
    // Should see procedure details
    await expect(page.locator('text=Procedimientos de Calidad')).toBeVisible();
  });

  test('should create new procedure', async ({ page }) => {
    await page.goto('/dashboard/calidad/procedimientos');
    
    // Click new procedure button
    await page.click('button:has-text("Nuevo Procedimiento")');
    
    // Fill procedure form
    await page.fill('[name="title"]', 'Procedimiento de Prueba');
    await page.fill('[name="description"]', 'Descripción del procedimiento de prueba');
    await page.fill('[name="version"]', '1.0');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Should show success message
    await expect(page.locator('text=Procedimiento creado')).toBeVisible();
    
    // Should see new procedure in table
    await expect(page.locator('text=Procedimiento de Prueba')).toBeVisible();
  });

  test('should show QuestionPro surveys', async ({ page }) => {
    await page.goto('/dashboard/calidad/questionpro');
    
    // Should see surveys list
    await expect(page.locator('[data-testid="surveys-list"]')).toBeVisible();
    
    // Should see survey details
    await expect(page.locator('text=Encuestas de Satisfacción')).toBeVisible();
  });

  test('should create new survey', async ({ page }) => {
    await page.goto('/dashboard/calidad/questionpro');
    
    // Click new survey button
    await page.click('button:has-text("Nueva Encuesta")');
    
    // Fill survey form
    await page.fill('[name="title"]', 'Encuesta de Prueba');
    await page.fill('[name="description"]', 'Descripción de la encuesta de prueba');
    await page.fill('[name="questions[0].text"]', '¿Pregunta de prueba 1?');
    await page.fill('[name="questions[1].text"]', '¿Pregunta de prueba 2?');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Should show success message
    await expect(page.locator('text=Encuesta creada')).toBeVisible();
    
    // Should see new survey in list
    await expect(page.locator('text=Encuesta de Prueba')).toBeVisible();
  });

  test('should show audit logs', async ({ page }) => {
    await page.goto('/dashboard/calidad/auditoria');
    
    // Should see audit table
    await expect(page.locator('table')).toBeVisible();
    
    // Should see audit details
    await expect(page.locator('text=Registros de Auditoría')).toBeVisible();
    await expect(page.locator('text=Usuario')).toBeVisible();
    await expect(page.locator('text=Acción')).toBeVisible();
    await expect(page.locator('text=Fecha')).toBeVisible();
  });
}); 