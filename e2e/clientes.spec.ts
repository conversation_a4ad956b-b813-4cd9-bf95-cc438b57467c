import { test, expect } from '@playwright/test';

// Test de acceso protegido y CRUD básico para módulo Clientes

// JWT generado con secret 'supersecreto123' y permisos de clientes
const JWT_VALIDO = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************.jwfmODwN775XpRpbqqOD2TwbtoYa5phDf_y61YwQqkU';

const USER_ADMIN = {
  id: 1,
  name: 'Test User',
  email: '<EMAIL>',
  active: true,
  roles: ['ROLE_ADMIN'],
  permissions: ['clientes:read', 'clientes:create', 'clientes:update']
};

const USER_SIN_PERMISOS = {
  id: 2,
  name: 'Sin Permisos',
  email: '<EMAIL>',
  active: true,
  roles: ['ROLE_USER'],
  permissions: []
};

test.describe('Clientes - Acceso y CRUD', () => {
  test('Debe requerir sesión para acceder a la página de clientes', async ({ page }) => {
    await page.goto('/dashboard/clientes');
    await expect(page.locator('text=Sesión Requerida')).toBeVisible();
  });

  test('Debe requerir permisos para acceder a la página de clientes', async ({ page }) => {
    await page.goto('/login');
    await page.evaluate(({ token, user }) => {
      localStorage.setItem('auth_token', token);
      localStorage.setItem('user_data', JSON.stringify(user));
    }, { token: JWT_VALIDO, user: USER_SIN_PERMISOS });
    // Debug: imprime el localStorage
    const ls = await page.evaluate(() => ({ token: localStorage.getItem('auth_token'), user: localStorage.getItem('user_data') }));
    console.log('DEBUG localStorage (sin permisos):', ls);
    await page.reload();
    await page.goto('/dashboard/clientes');
    // Debug: imprime el HTML de la página
    const html = await page.content();
    console.log('DEBUG HTML (sin permisos):', html);
    await expect(page.locator('text=Acceso Restringido')).toBeVisible();
    await expect(page.locator('text=permisos de gestión de clientes')).toBeVisible();
  });

  test('Debe mostrar la tabla de clientes si tiene permisos (token real)', async ({ page }) => {
    await page.goto('/login');
    await page.evaluate(({ token, user }) => {
      localStorage.setItem('auth_token', token);
      localStorage.setItem('user_data', JSON.stringify(user));
    }, { token: JWT_VALIDO, user: USER_ADMIN });
    // Debug: imprime el localStorage
    const ls = await page.evaluate(() => ({ token: localStorage.getItem('auth_token'), user: localStorage.getItem('user_data') }));
    console.log('DEBUG localStorage (admin):', ls);
    await page.reload();
    await page.goto('/dashboard/clientes');
    // Debug: imprime el HTML de la página
    const html = await page.content();
    console.log('DEBUG HTML (admin):', html);
    await expect(page.locator('h1', { hasText: 'Mis Clientes' })).toBeVisible();
    await expect(page.locator('text=Nuevo cliente')).toBeVisible();
  });

  test('Debe poder crear un cliente nuevo', async ({ page }) => {
    await page.goto('/dashboard/clientes?mockPerm=clientes:create');
    await page.click('text=Nuevo cliente');
    await page.fill('input[name="commercialName"]', 'Test Cliente');
    await page.fill('input[name="legalName"]', 'Test Legal');
    await page.fill('input[name="rfc"]', 'ABC123456T12');
    await page.click('button[type="submit"]');
    await expect(page.locator('text=Test Cliente')).toBeVisible();
  });

  test('Debe poder editar un cliente', async ({ page }) => {
    await page.goto('/dashboard/clientes?mockPerm=clientes:update');
    await page.click('button[aria-label="Editar"]');
    await page.fill('input[name="legalName"]', 'Legal Editado');
    await page.click('button[type="submit"]');
    await expect(page.locator('text=Legal Editado')).toBeVisible();
  });
});
