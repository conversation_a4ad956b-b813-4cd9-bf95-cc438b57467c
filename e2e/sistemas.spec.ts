import { test, expect } from '@playwright/test';

test.describe('Sistemas Module', () => {
  // <PERSON><PERSON> as sistemas user before each test
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.waitForSelector('#credential', { timeout: 10000 });
    await page.fill('#credential', '<EMAIL>');
    await page.fill('#password', 'password');
    await Promise.all([
      page.waitForResponse(response => response.url().includes('/api/auth/login')),
      page.click('button[type="submit"]')
    ]);
    await expect(page).toHaveURL('/dashboard', { timeout: 10000 });
  });

  test('should show sistemas dashboard', async ({ page }) => {
    await page.goto('/dashboard/sistemas');
    await expect(page.locator('h1')).toContainText('Sistemas');
    await expect(page.locator('[data-testid="sistemas-stats"]')).toBeVisible();
  });

  test('should manage users', async ({ page }) => {
    await page.goto('/dashboard/sistemas/usuarios');
    
    // Should see users table
    await expect(page.locator('table')).toBeVisible();
    
    // Create new user
    await page.click('button:has-text("Nuevo Usuario")');
    await page.fill('[name="name"]', 'Usuario de Prueba');
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'password123');
    await page.selectOption('select[name="area"]', 'Ventas');
    await page.click('text=ROLE_VENTAS_VENDEDOR'); // Select role checkbox
    await page.click('button[type="submit"]');
    
    // Should show success message
    await expect(page.locator('text=Usuario creado')).toBeVisible();
    
    // Should see new user in table
    await expect(page.locator('text=Usuario de Prueba')).toBeVisible();
    
    // Edit user
    await page.click('button[aria-label="Editar usuario"]');
    await page.fill('[name="name"]', 'Usuario Actualizado');
    await page.click('button[type="submit"]');
    
    // Should show success message
    await expect(page.locator('text=Usuario actualizado')).toBeVisible();
    
    // Should see updated user name
    await expect(page.locator('text=Usuario Actualizado')).toBeVisible();
  });

  test('should manage roles', async ({ page }) => {
    await page.goto('/dashboard/sistemas/roles');
    
    // Should see roles table
    await expect(page.locator('table')).toBeVisible();
    
    // Create new role
    await page.click('button:has-text("Nuevo Rol")');
    await page.fill('[name="name"]', 'ROLE_TEST');
    await page.fill('[name="description"]', 'Rol de prueba');
    await page.click('text=sistemas:users:read'); // Select permission checkbox
    await page.click('button[type="submit"]');
    
    // Should show success message
    await expect(page.locator('text=Rol creado')).toBeVisible();
    
    // Should see new role in table
    await expect(page.locator('text=ROLE_TEST')).toBeVisible();
    
    // Edit role permissions
    await page.click('button[aria-label="Editar permisos"]');
    await page.click('text=sistemas:users:create'); // Select additional permission
    await page.click('button[type="submit"]');
    
    // Should show success message
    await expect(page.locator('text=Permisos actualizados')).toBeVisible();
  });

  test('should manage backups', async ({ page }) => {
    await page.goto('/dashboard/sistemas/backups');
    
    // Should see backups table
    await expect(page.locator('table')).toBeVisible();
    
    // Create new backup
    await page.click('button:has-text("Nuevo Backup")');
    
    // Should show success message
    await expect(page.locator('text=Backup iniciado')).toBeVisible();
    
    // Should see new backup in table after completion
    await expect(page.locator('text=Backup completo')).toBeVisible();
    
    // Download backup
    await page.click('button[aria-label="Descargar backup"]');
    
    // Should trigger download
    const download = await page.waitForEvent('download');
    expect(download.suggestedFilename()).toContain('.sql');
  });

  test('should show audit logs', async ({ page }) => {
    await page.goto('/dashboard/sistemas/auditoria');
    
    // Should see audit table
    await expect(page.locator('table')).toBeVisible();
    
    // Apply date filter
    await page.fill('[name="startDate"]', '2025-01-01');
    await page.fill('[name="endDate"]', '2025-12-31');
    await page.click('button:has-text("Filtrar")');
    
    // Should see filtered results
    await expect(page.locator('tr')).toHaveCount(10);
    
    // Export logs
    await page.click('button:has-text("Exportar")');
    
    // Should trigger download
    const download = await page.waitForEvent('download');
    expect(download.suggestedFilename()).toContain('.csv');
  });

  test('should manage notifications', async ({ page }) => {
    await page.goto('/dashboard/sistemas');
    
    // Open notifications panel
    await page.click('button[aria-label="Abrir notificaciones"]');
    
    // Should see notifications list
    await expect(page.locator('[data-testid="notifications-list"]')).toBeVisible();
    
    // Create new notification
    await page.click('button:has-text("Nueva Notificación")');
    await page.fill('[name="title"]', 'Notificación de Prueba');
    await page.fill('[name="message"]', 'Mensaje de prueba');
    await page.selectOption('select[name="type"]', 'info');
    await page.click('text=ROLE_VENTAS_VENDEDOR'); // Select target role
    await page.click('button[type="submit"]');
    
    // Should show success message
    await expect(page.locator('text=Notificación enviada')).toBeVisible();
    
    // Should see new notification in list
    await expect(page.locator('text=Notificación de Prueba')).toBeVisible();
  });

  test('should show roles page', async ({ page }) => {
    await page.goto('/dashboard/sistemas/roles');
    await expect(page.locator('h1')).toContainText('Roles');
    // Puedes agregar más validaciones específicas aquí
  });

  test('should show users page', async ({ page }) => {
    await page.goto('/dashboard/sistemas/usuarios');
    await expect(page.locator('h1')).toContainText('Usuarios');
    // Puedes agregar más validaciones específicas aquí
  });

  test('should show audit logs page', async ({ page }) => {
    await page.goto('/dashboard/sistemas/auditoria');
    await expect(page.locator('h1')).toContainText('Auditoría');
    // Puedes agregar más validaciones específicas aquí
  });

  test('should show backups page', async ({ page }) => {
    await page.goto('/dashboard/sistemas/backup');
    await expect(page.locator('h1')).toContainText('Backups');
    // Puedes agregar más validaciones específicas aquí
  });
}); 