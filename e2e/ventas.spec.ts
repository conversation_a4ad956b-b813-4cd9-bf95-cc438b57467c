import { test, expect } from '@playwright/test';

test.describe('Ventas Module', () => {
  test.beforeEach(async ({ page }) => {
    // Ir directamente a la página de login
    await page.goto('/login');

    // Esperar a que el formulario de login esté visible y el servicio de autenticación esté listo
    await page.waitForSelector('#credential', { timeout: 10000 });
    await page.waitForFunction(() => {
      const authInitialized = !document.querySelector('.loading-auth');
      return authInitialized;
    }, { timeout: 10000 });

    // Intentar login con email de prueba
    await page.fill('#credential', '<EMAIL>');
    await page.fill('#password', 'password');
    
    // Click en el botón de login y esperar la respuesta
    await Promise.all([
      page.waitForResponse(response => response.url().includes('/api/auth/login')),
      page.click('button[type="submit"]')
    ]);

    // Esperar a que se redirija al dashboard y verificar que estamos autenticados
    await expect(page).toHaveURL('/dashboard', { timeout: 10000 });
    await expect(page.locator('text=Cerrar Sesión')).toBeVisible({ timeout: 10000 });
  });

  test('should show ventas dashboard', async ({ page }) => {
    // Navegar al módulo de ventas
    await page.goto('/dashboard/ventas');
    await expect(page.locator('h1')).toContainText('Módulo de Ventas', { timeout: 10000 });
    
    // Verificar que los componentes principales estén presentes
    await expect(page.locator('text=Ventas Totales')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=Nuevos Clientes')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=Cotizaciones Enviadas')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=Tasa de Conversión')).toBeVisible({ timeout: 10000 });
    
    // Verificar gráficas
    await expect(page.locator('text=Visión General de Ventas')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=Productos Más Vendidos')).toBeVisible({ timeout: 10000 });
  });

  test('should navigate to clients section', async ({ page }) => {
    await page.goto('/dashboard/ventas/mis-clientes');
    await expect(page.locator('table')).toBeVisible({ timeout: 10000 });
    
    // Verificar funcionalidad de búsqueda
    const searchInput = page.locator('[placeholder="Buscar cliente..."]');
    await expect(searchInput).toBeVisible({ timeout: 10000 });
    await searchInput.fill('Cliente');
    
    // Verificar filtros
    const filterSelect = page.locator('select[name="type"]');
    await expect(filterSelect).toBeVisible({ timeout: 10000 });
  });

  test('should navigate to quotations section', async ({ page }) => {
    await page.goto('/dashboard/ventas/cotizar');
    
    // Verificar botón de nueva cotización
    const newQuoteButton = page.locator('button:has-text("Nueva Cotización")');
    await expect(newQuoteButton).toBeVisible({ timeout: 10000 });
    
    // Verificar tabla de cotizaciones
    await expect(page.locator('table')).toBeVisible({ timeout: 10000 });
  });

  test('should check product stock', async ({ page }) => {
    await page.goto('/dashboard/ventas/existencias');
    
    // Verificar tabla de stock
    await expect(page.locator('table')).toBeVisible({ timeout: 10000 });
    
    // Verificar búsqueda de productos
    const searchInput = page.locator('[placeholder="Buscar producto..."]');
    await expect(searchInput).toBeVisible({ timeout: 10000 });
    
    // Verificar filtros
    const categoryFilter = page.locator('select[name="category"]');
    await expect(categoryFilter).toBeVisible({ timeout: 10000 });
  });

  test('should navigate to CRM projects', async ({ page }) => {
    await page.goto('/dashboard/ventas/crm-proyectos');
    
    // Verificar botón de nuevo proyecto
    const newProjectButton = page.locator('button:has-text("Nuevo Proyecto")');
    await expect(newProjectButton).toBeVisible({ timeout: 10000 });
    
    // Verificar tabla de proyectos
    await expect(page.locator('table')).toBeVisible({ timeout: 10000 });
  });

  test('should navigate to technical sheets', async ({ page }) => {
    await page.goto('/dashboard/ventas/fichas-tecnicas');
    
    // Verificar botón de nueva ficha técnica
    const newSheetButton = page.locator('button:has-text("Nueva Ficha Técnica")');
    await expect(newSheetButton).toBeVisible({ timeout: 10000 });
    
    // Verificar lista de fichas técnicas
    await expect(page.locator('[data-testid="technical-sheets-list"]')).toBeVisible({ timeout: 10000 });
  });

  test('should show sales statistics', async ({ page }) => {
    await page.goto('/dashboard/ventas/estadisticos');
    
    // Verificar componentes de estadísticas
    await expect(page.locator('text=Estadísticos de Ventas')).toBeVisible({ timeout: 10000 });
    
    // Verificar filtros de fecha
    await expect(page.locator('[name="startDate"]')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('[name="endDate"]')).toBeVisible({ timeout: 10000 });
    
    // Verificar botón de exportar
    await expect(page.locator('button:has-text("Exportar")')).toBeVisible({ timeout: 10000 });
  });
}); 