import { test, expect } from '@playwright/test';

test.describe('Authentication', () => {
  test('should redirect to login when not authenticated', async ({ page }) => {
    await page.goto('/dashboard');
    await expect(page).toHaveURL('/login');
  });

  test('should login with valid credentials', async ({ page }) => {
    await page.goto('/login');
    
    // Wait for form to be loaded
    await page.waitForSelector('#credential');
    
    // Fill login form using IDs
    await page.fill('#credential', 'admin1');
    await page.fill('#password', 'password');
    
    // Click login button
    await page.click('button[type="submit"]');
    
    // Should redirect to dashboard
    await expect(page).toHaveURL('/dashboard');
    
    // Should see dashboard content
    await expect(page.locator('h1, h2, [data-testid="dashboard"]')).toBeVisible();
  });

  test('should show error with invalid credentials', async ({ page }) => {
    await page.goto('/login');
    
    await page.fill('#credential', '<EMAIL>');
    await page.fill('#password', 'wrongpassword');
    await page.click('button[type="submit"]');
    
    // Should show error message
    await expect(page.locator('text=Credenciales incorrectas')).toBeVisible();
  });

  test('should logout successfully', async ({ page }) => {
    // First login
    await page.goto('/login');
    await page.fill('#credential', 'admin1');
    await page.fill('#password', 'password');
    await page.click('button[type="submit"]');
    
    // Wait for successful login
    await expect(page).toHaveURL('/dashboard');
    
    // Find logout button (adjust selector based on your implementation)
    await page.click('button:has-text("Cerrar Sesión")');
    
    // Should redirect back to login
    await expect(page).toHaveURL('/login');
  });
}); 