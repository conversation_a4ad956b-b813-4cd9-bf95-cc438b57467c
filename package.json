{"devDependencies": {"@playwright/test": "^1.53.1", "@storybook/test": "^8.6.14", "@storybook/testing-react": "^2.0.0", "@supabase/supabase-js": "^2.50.3", "@types/js-yaml": "^4.0.9", "@types/pg": "^8.15.4", "concurrently": "^9.2.0", "js-yaml": "^4.1.0", "pg": "^8.16.3", "supabase": "^2.30.4", "task-master-ai": "^0.18.0", "ts-node": "^10.9.2"}, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev -- -p 3001", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "test": "npm run test:backend && npm run test:frontend && npm run test:e2e", "test:backend": "cd backend && npm run test", "test:frontend": "cd frontend && npm run test", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "cleanup": "bash scripts/cleanup-obsolete-files.sh", "cleanup:temp": "bash scripts/cleanup-temp.sh", "extract:schema": "bash scripts/extract-app-schema.sh", "update:schema": "bash scripts/update-db-schema.sh", "e2e": "cd e2e && npx playwright test", "storybook": "cd frontend && npm run storybook", "build-storybook": "cd frontend && npm run build-storybook", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:full": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm start", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "verify": "npm run build && npm test", "quick-check": "npm run build && npm test -- --passWithNoTests", "test:systems": "npm test -- --testPathPattern=\"systems\" --verbose", "test:crud": "npm test -- --testPathPattern=\"crud|users|roles|permissions\" --verbose", "verify:systems": "npm run build && npm run test:systems", "dev:full": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\""}, "dependencies": {"bcryptjs": "^3.0.2", "react-dropzone": "^14.3.8"}}