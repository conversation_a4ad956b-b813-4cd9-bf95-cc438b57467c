import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3001',
    trace: 'on-first-retry',
    video: 'on-first-retry',
  },

  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],

  webServer: [
    {
      command: 'cd backend && npm run dev',
      url: 'http://localhost:3000/api/health',
      timeout: 120000,
      reuseExistingServer: !process.env.CI,
      env: {
        NODE_ENV: 'test',
        PORT: '3000',
        DB_HOST: 'awlxzhrubqkryrenunun.supabase.co',
        DB_PORT: '5432',
        DB_USERNAME: 'postgres',
        DB_PASSWORD: 'appcomintecpasw0rd',
        DB_DATABASE: 'postgres',
        JWT_SECRET: 'test-secret-key',
        JWT_EXPIRES_IN: '1d',
        CORS_ORIGIN: 'http://localhost:3001',
      },
    },
    {
      command: 'cd frontend && npm run dev',
      url: 'http://localhost:3001',
      timeout: 120000,
      reuseExistingServer: !process.env.CI,
      env: {
        NEXT_PUBLIC_SUPABASE_URL: 'https://awlxzhrubqkryrenunun.supabase.co',
        NEXT_PUBLIC_SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.zcl_3UQ1s9zOBgwvUYXQPYHiPXlz1z1DaKxKEGEuH-c',
      },
    },
  ],

  globalSetup: './e2e/setup/global-setup.ts',
}); 